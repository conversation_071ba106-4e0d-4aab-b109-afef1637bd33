{"version": "0.2.0", "configurations": [{"name": "Chrome", "type": "chrome", "request": "launch", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}/src", "sourceMapPathOverrides": {"webpack:///src/*": "${webRoot}/*"}}, {"name": "Debug CRA Tests", "type": "node", "request": "launch", "runtimeExecutable": "${workspaceRoot}/node_modules/.bin/react-scripts", "args": ["test", "--runInBand", "--no-cache", "--no-watch"], "cwd": "${workspaceRoot}", "protocol": "inspector", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}]}