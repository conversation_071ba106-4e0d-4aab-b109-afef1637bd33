export const mockAdminToken = `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0rEkLU7ULykTLj2pBSqKafV8coxTH7chVgnUJaXgktY`;
export const mockReviewerToken = `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************.Yo2hZ1bUJ9C9-m_S2NbBMXYJlPxN0GkEgkGDelYbw9Y`;
export const mockPublisherToken = `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************.cOEyvw2uOsHV75YvZXhJ-eAkPSuMjsVEUZbk1IPAb5c`;
export const mockBasicToken = `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************.6qLE2bf9H8G7T6I0gBaYLr_K29rS7Kd49xsWn1QPNIw`;

export const CONSTANTS = {
  defaultOffer: "Default All"
};

export const postImageApi = process.env.REACT_APP_POST_IMAGE_API;

export const postSecurityApi = process.env.REACT_APP_POST_SECURITY_API;

export const offerFacetsApi = process.env.REACT_APP_OFFER_FACETS_API;

export const offerPromotionsApi = process.env.REACT_APP_OFFER_PROMOTIONS_API;

export const offerSubmissionApi = process.env.REACT_APP_OFFER_SUBMISSION_API;

export const pingLoginUrl = process.env.REACT_APP_PING_LOGIN_URL;

export const offerIssuerApi = process.env.REACT_APP_OFFER_ISSUER_API;

export const urls = {
  groupsController: {
    groups: `${postSecurityApi}/groups`
  },
  imagesController: {
    processImages: `${postImageApi}/v1/process-images`,
    generateS3Url: `${postImageApi}/v1/generate-s3-url`
  },
  login: "/login",
  logout: "/logout",
  offersController: {
    getBulkJobs: `${postSecurityApi}/v1/offers/jobs`,
    offers: `${postSecurityApi}/v1/offers`,
    bulkPublishOffers: `${postSecurityApi}/v1/offers/bulk/publish`,
    publishOffer: `${postSecurityApi}/v1/offers/publish`,
    uploadOffers: `${postSecurityApi}/v1/offers/bulk`,
    updateBulkOffers: `${postSecurityApi}/v1/offers/bulk`,
    generateContent: `${postSecurityApi}/v1/offers/generate-content`,
    postOffer: `${postSecurityApi}/v1/offers`,
    getCategories: `${offerFacetsApi}/v1/categories`,
    getPromotions: `${offerPromotionsApi}/v1/promotions`,
    getOffersCounts: `${postSecurityApi}/v1/offers/counts`,
    deleteOffers: `${postSecurityApi}/v1/offers/delete-multiple`,
    disableOffer: `${postSecurityApi}/v1/offers/disable`,
    targetedOffers: `${postSecurityApi}/v1/offers/targeted`,
    enableOffer: `${postSecurityApi}/v1/offers/enable`
  },
  partnersController: {
    partners: `${postSecurityApi}/partners`
  },
  permissionController: {
    permissionConfig: "/config/permissions.json",
    permissions: `${offerSubmissionApi}/permissions`
  },

  pingAuthorizer: pingLoginUrl,
  rolesController: {
    roles: `${offerSubmissionApi}/roles`
  },

  userController: {
    groups: (email: string) => `${postSecurityApi}/users/${email}/groups`,
    partners: (email: string) => `${postSecurityApi}/users/${email}/partners`,
    users: `${offerSubmissionApi}/users`
  },

  offersIssuer: {
    activeSponsorCodes: `${offerIssuerApi}/issuers`,
    issuanceOfferCodes: `${offerIssuerApi}/issuer-offers`,
  }
};

export const BARCODE_MECHANISM_TYPES = [
  "barcodeUPC",
  "barcodeCODE128",
  "barcodeEAN",
  "barcodeCODE39"
];