import { urls } from "../constants";

import apiService, { IApiService } from "./api.service";
import { get } from "lodash";

export const MAX_IMG_WIDTH = 720;
export const MAX_IMG_HEIGHT = 720;
export const MIN_IMG_WIDTH = 320;
export const MIN_IMG_HEIGHT = 320;
export const IMG_CROP_BOUNDARY = 0.8; // '1' - no gray area on crop field

export class ImageService {
  private apiService: IApiService;
  constructor(apiServiceDep?: any) {
    this.apiService = apiServiceDep || apiService;
  }

  public processImages(
    requestBody: IProcessImageRequestBody
  ): Promise<IProcessImagesResponseBody> {
    const request: Request = new Request(urls.imagesController.processImages, {
      ...this.apiService.getDefaultRequestInit(),
      body: JSON.stringify(requestBody),
      method: "POST"
    });

    return this.apiService.ajax<IProcessImagesResponseBody>(request);
  }

  public getProcessInfo(batchID: string): Promise<IProcessImageBatchResult> {
    return this.apiService.ajax<IProcessImageBatchResult>(
      `${urls.imagesController.processImages}/${batchID}`
    );
  }

  public generateS3Url(
    requestBody: IGenerateS3UrlRequestBody
  ): Promise<IGenerateS3UrlResponseBody> {
    const request: Request = new Request(urls.imagesController.generateS3Url, {
      ...this.apiService.getDefaultRequestInit(),
      body: JSON.stringify(requestBody),
      method: "POST"
    });

    return this.apiService.ajax<IGenerateS3UrlResponseBody>(request);
  }

  public uploadToS3(
    requestUrl: string,
    requestBody: Blob,
    mimeType: string
  ): Promise<string> {
    const request: Request = new Request(requestUrl, {
      body: requestBody,
      method: "PUT",
      headers: {
        "Content-Type": mimeType
      }
    });

    return this.apiService.ajax<string>(request);
  }

  public isValid(blob: Blob): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.src = URL.createObjectURL(blob);
      img.onload = () => {
        resolve(this.isValidImageSize(img));
      };
      img.onerror = () => reject();
    });
  }

  /* This function will check if the image size is valid before and after resizing
  Mocking the aspect ratio resize based on the current width and height
  */
  public isValidImageSize(img: any) {
    let newWidth = img.width;
    let newHeight = img.height;

    if (img.width > MAX_IMG_WIDTH || img.height > MAX_IMG_HEIGHT) {
      // resize as it would be in the image service based on the aspect ratio
      if (img.width > img.height) {
        newWidth = MAX_IMG_WIDTH - 0.01;
        newHeight = newWidth * (img.height / img.width); // The new height is the resized width multiplied by the aspect ratio
      } else {
        newHeight = MAX_IMG_HEIGHT - 0.01;
        newWidth = newHeight * (img.width / img.height); // The new width is the resized height multiplied by the aspect ratio
      }
    }
    if (newWidth < MIN_IMG_WIDTH && newHeight < MIN_IMG_HEIGHT) {
      return false;
    }
    return true;
  }
  /* HELPERS */
  public getImageWidth(originalImage: HTMLImageElement) {
    if (
      originalImage.naturalWidth >= MAX_IMG_WIDTH ||
      originalImage.naturalHeight >= MAX_IMG_HEIGHT
    ) {
      return MAX_IMG_WIDTH;
    } else {
      if (originalImage.naturalWidth > originalImage.naturalHeight) {
        return originalImage.naturalWidth;
      } else {
        return originalImage.naturalHeight;
      }
    }
  }
  public getImageHeight(originalImage: HTMLImageElement) {
    if (
      originalImage.naturalWidth >= MAX_IMG_WIDTH ||
      originalImage.naturalHeight >= MAX_IMG_HEIGHT
    ) {
      return MAX_IMG_HEIGHT;
    } else {
      if (originalImage.naturalHeight > originalImage.naturalWidth) {
        return originalImage.naturalHeight;
      } else {
        return originalImage.naturalWidth;
      }
    }
  }
  public getCropperImageZoom(
    imgUploadEl: HTMLImageElement,
    imgEl: HTMLImageElement
  ) {
    let zoom = 1,
      dimensionToZoom =
        imgEl.naturalWidth >= imgEl.naturalHeight
          ? imgEl.naturalWidth
          : imgEl.naturalHeight;

    zoom = (imgUploadEl.offsetWidth * IMG_CROP_BOUNDARY) / dimensionToZoom;

    return zoom;
  }
}

export default new ImageService();

export interface IProcessImagesResponseBody {
  id: string;
}

export interface IProcessImageBatchResult {
  [key: string]: {
    raw_s3_location?: string;
    processed_s3_location: string;
    success: boolean;
    error_code?: string;
    message?: string;
  };
}

export interface IGenerateS3UrlRequestBody {
  fileLocationPathForUpload: string;
  mimeType: string;
}

export interface IGenerateS3UrlResponseBody {
  signedURLForUpload: string;
}

export interface IOptimizations {
  w?: number;
  h?: number;
  ar?: string;
  c?: string;
  cm?: string;
  fo?: number;
  dpr?: number;
}

export interface IProcessImageRequestBody {
  zip_file?: IFileProcessingZipData;
  external_images?: IFileProcessingData[];
}

export interface IFileProcessingData {
  location: string;
  optimizations?: IOptimizations;
}

export interface IFileProcessingZipData extends IFileProcessingData {
  files_to_process: string[];
}
