import JsBarcode from "jsbarcode";
import { defaults, get, isEmpty } from "lodash";
import { Validator, MechanismType } from "../../validation/validator";

export interface IBarcodeTypeType {
  [key: string]: { [key: string]: string };
}

const barcodeTypeOverrides: IBarcodeTypeType = {};

const optionsDefault = {
  displayValue: false,
  background: "#ffffff",
  width: 1.5,
  height: 20,
  format: "UPC"
};

export class BarcodeService {
  public generateBarcode(
    elementId: SVGSVGElement | null,
    barcodeType: string,
    barcodeMechanismType: MechanismType,
    barcodeValue: string,
    options?: any
  ) {
    let type = barcodeType;
    const val = new Validator();
    if (
      isEmpty(
        val.getBarcodeErrors([
          {
            mechanismType: barcodeMechanismType,
            mechanismValue: { "en-US": barcodeValue }
          }
        ])
      )
    ) {
      if (barcodeTypeOverrides[barcodeType]) {
        type = get(
          barcodeTypeOverrides[barcodeType],
          barcodeValue.length,
          "code128"
        );
      }

      try {
        // If JSBarcode runs with an invalid barcode, it'll crash the whole app
        return JsBarcode(
          elementId,
          barcodeValue,
          defaults({ format: type.toUpperCase() }, options, optionsDefault)
        );
      } catch (error) {
        console.error(error);
      }
    } else {
      console.error("Unable to render barcode due to invalid barcode");
    }
  }
}

export default new BarcodeService();
