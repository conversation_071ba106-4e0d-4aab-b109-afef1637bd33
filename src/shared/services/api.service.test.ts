import { ApiService } from "./api.service";

describe("API service", () => {
  const fakeData = { data: "test" };
  const fakeFetch = () =>
    Promise.resolve<Response>(new Response(JSON.stringify(fakeData)));
  const apiService = new ApiService(fakeFetch);

  it("instanciate", () => {
    expect(apiService).toBeDefined();
  });

  xit("can make ajax requests", async () => {
    expect.assertions(1);
    const data = await apiService.ajax("./test");
    expect(data).toBeDefined();
  });
});
