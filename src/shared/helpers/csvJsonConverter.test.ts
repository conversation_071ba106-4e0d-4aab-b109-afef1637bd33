import { convertCsvStringToJson, convertJsonToCsv, csvToSchema } from "./csvJsonConverter";

describe("Papa parse", () => {
  describe("convertCsvStringToJson", () => {
    const parseObjectMeta = {
      aborted: false,
      cursor: expect.any(Number),
      delimiter: ",",
      fields: expect.any(Array),
      linebreak: "\n",
      truncated: false
    };
    it("converts basic CSV String to JSON parse object", () => {
      const csvString =
        "Column 1,Column 2,Column 3,Column 4\n1-1,1-2,1-3,1-4 \n2-1,2-2,2-3,2-4 \n3-1,3-2,3-3,3-4 \n4,5,6,7";

      expect(convertCsvStringToJson(csvString)).toEqual({
        data: expect.any(Array),
        errors: [],
        meta: parseObjectMeta
      });
    });
    it("accepts a config that will override the mapping of the JSON parse object", () => {
      const csvString =
        "Column 1,Column 2,Column 3,Column 4\n1-1,1-2,1-3,1-4 \n2-1,2-2,2-3,2-4 \n3-1,3-2,3-3,3-4 \n4,5,6,7";
      let overrideConfig = {
        dynamicTyping: false,
        header: false
      };

      expect(convertCsvStringToJson(csvString, overrideConfig)).not.toEqual(
        convertCsvStringToJson(csvString)
      );

      overrideConfig = {
        dynamicTyping: true,
        header: true
      };
      expect(convertCsvStringToJson(csvString, overrideConfig)).toEqual(
        convertCsvStringToJson(csvString)
      );
    });
    it("converts uneven column CSV String to JSON parse object", () => {
      const csvString =
        "Column 1,Column 2,Column 3,Column 4\n1-1,1-2,,1-4 \n2-1,2-2,,2-4 \n3-1,3-2,,3-4 \n4,,6,7";

      expect(convertCsvStringToJson(csvString)).toEqual({
        data: expect.any(Array),
        errors: [],
        meta: parseObjectMeta
      });
    });
    it("converts space delimited CSV String to JSON parse object", () => {
      const csvString =
        "Column 1 Column 2 Column 3 Column 4\n1-1 1-2 1-3 1-4 \n2-1 2-2 2-3 2-4 \n3-1 3-2 3-3 3-4 \n4 5 6 7";

      expect(convertCsvStringToJson(csvString)).toEqual({
        data: expect.any(Array),
        errors: [
          {
            code: "UndetectableDelimiter",
            message:
              "Unable to auto-detect delimiting character; defaulted to ','",
            row: undefined,
            type: "Delimiter"
          }
        ],
        meta: parseObjectMeta
      });
    });
    it("converts larger CSV String to JSON parse object", () => {
      const csvString =
        '"LatD","LatM","LatS","NS","LonD","LonM","LonS","EW","City","State"\n41,    5,   59, "N",     80,   39,    0, "W", "Youngstown", OH\n  42,   52,   48, "N",     97,   23,   23, "W", "Yankton", SD\n   46,   35,   59, "N",    120,   30,   36, "W", "Yakima", WA\n   42,   16,   12, "N",     71,   48,    0, "W", "Worcester", MA\n   43,   37,   48, "N",     89,   46,   11, "W", "Wisconsin Dells", WI\n   36,    5,   59, "N",     80,   15,    0, "W", "Winston-Salem", NC\n   49,   52,   48, "N",     97,    9,    0, "W", "Winnipeg", MB\n   39,   11,   23, "N",     78,    9,   36, "W", "Winchester", VA\n   34,   14,   24, "N",     77,   55,   11, "W", "Wilmington", NC\n   39,   45,    0, "N",     75,   33,    0, "W", "Wilmington", DE\n   48,    9,    0, "N",    103,   37,   12, "W", "Williston", ND\n   41,   15,    0, "N",     77,    0,    0, "W", "Williamsport", PA\n   37,   40,   48, "N",     82,   16,   47, "W", "Williamson", WV\n   33,   54,    0, "N",     98,   29,   23, "W", "Wichita Falls", TX\n   37,   41,   23, "N",     97,   20,   23, "W", "Wichita", KS\n   40,    4,   11, "N",     80,   43,   12, "W", "Wheeling", WV\n   26,   43,   11, "N",     80,    3,    0, "W", "West Palm Beach", FL\n   47,   25,   11, "N",    120,   19,   11, "W", "Wenatchee", WA\n   41,   25,   11, "N",    122,   23,   23, "W", "Weed", CA\n   31,   13,   11, "N",     82,   20,   59, "W", "Waycross", GA\n   44,   57,   35, "N",     89,   38,   23, "W", "Wausau", WI\n   42,   21,   36, "N",     87,   49,   48, "W", "Waukegan", IL\n   44,   54,    0, "N",     97,    6,   36, "W", "Watertown", SD\n   43,   58,   47, "N",     75,   55,   11, "W", "Watertown", NY\n   42,   30,    0, "N",     92,   20,   23, "W", "Waterloo", IA\n   41,   32,   59, "N",     73,    3,    0, "W", "Waterbury", CT\n   38,   53,   23, "N",     77,    1,   47, "W", "Washington", DC\n   41,   50,   59, "N",     79,    8,   23, "W", "Warren", PA\n   46,    4,   11, "N",    118,   19,   48, "W", "Walla Walla", WA\n   31,   32,   59, "N",     97,    8,   23, "W", "Waco", TX\n   38,   40,   48, "N",     87,   31,   47, "W", "Vincennes", IN\n   28,   48,   35, "N",     97,    0,   36, "W", "Victoria", TX\n   32,   20,   59, "N",     90,   52,   47, "W", "Vicksburg", MS\n   49,   16,   12, "N",    123,    7,   12, "W", "Vancouver", BC\n   46,   55,   11, "N",     98,    0,   36, "W", "Valley City", ND\n   30,   49,   47, "N",     83,   16,   47, "W", "Valdosta", GA\n   43,    6,   36, "N",     75,   13,   48, "W", "Utica", NY\n   39,   54,    0, "N",     79,   43,   48, "W", "Uniontown", PA\n   32,   20,   59, "N",     95,   18,    0, "W", "Tyler", TX\n   42,   33,   36, "N",    114,   28,   12, "W", "Twin Falls", ID\n   33,   12,   35, "N",     87,   34,   11, "W", "Tuscaloosa", AL\n   34,   15,   35, "N",     88,   42,   35, "W", "Tupelo", MS\n   36,    9,   35, "N",     95,   54,   36, "W", "Tulsa", OK\n   32,   13,   12, "N",    110,   58,   12, "W", "Tucson", AZ\n   37,   10,   11, "N",    104,   30,   36, "W", "Trinidad", CO\n   40,   13,   47, "N",     74,   46,   11, "W", "Trenton", NJ\n   44,   45,   35, "N",     85,   37,   47, "W", "Traverse City", MI\n   43,   39,    0, "N",     79,   22,   47, "W", "Toronto", ON\n   39,    2,   59, "N",     95,   40,   11, "W", "Topeka", KS\n   41,   39,    0, "N",     83,   32,   24, "W", "Toledo", OH\n   33,   25,   48, "N",     94,    3,    0, "W", "Texarkana", TX\n   39,   28,   12, "N",     87,   24,   36, "W", "Terre Haute", IN\n   27,   57,    0, "N",     82,   26,   59, "W", "Tampa", FL\n   30,   27,    0, "N",     84,   16,   47, "W", "Tallahassee", FL\n   47,   14,   24, "N",    122,   25,   48, "W", "Tacoma", WA\n   43,    2,   59, "N",     76,    9,    0, "W", "Syracuse", NY\n   32,   35,   59, "N",     82,   20,   23, "W", "Swainsboro", GA\n   33,   55,   11, "N",     80,   20,   59, "W", "Sumter", SC\n   40,   59,   24, "N",     75,   11,   24, "W", "Stroudsburg", PA\n   37,   57,   35, "N",    121,   17,   24, "W", "Stockton", CA\n   44,   31,   12, "N",     89,   34,   11, "W", "Stevens Point", WI\n   40,   21,   36, "N",     80,   37,   12, "W", "Steubenville", OH\n   40,   37,   11, "N",    103,   13,   12, "W", "Sterling", CO\n   38,    9,    0, "N",     79,    4,   11, "W", "Staunton", VA\n   39,   55,   11, "N",     83,   48,   35, "W", "Springfield", OH\n   37,   13,   12, "N",     93,   17,   24, "W", "Springfield", MO\n   42,    5,   59, "N",     72,   35,   23, "W", "Springfield", MA\n   39,   47,   59, "N",     89,   39,    0, "W", "Springfield", IL\n   47,   40,   11, "N",    117,   24,   36, "W", "Spokane", WA\n   41,   40,   48, "N",     86,   15,    0, "W", "South Bend", IN\n   43,   32,   24, "N",     96,   43,   48, "W", "Sioux Falls", SD\n   42,   29,   24, "N",     96,   23,   23, "W", "Sioux City", IA\n   32,   30,   35, "N",     93,   45,    0, "W", "Shreveport", LA\n   33,   38,   23, "N",     96,   36,   36, "W", "Sherman", TX\n   44,   47,   59, "N",    106,   57,   35, "W", "Sheridan", WY\n   35,   13,   47, "N",     96,   40,   48, "W", "Seminole", OK\n   32,   25,   11, "N",     87,    1,   11, "W", "Selma", AL\n   38,   42,   35, "N",     93,   13,   48, "W", "Sedalia", MO\n   47,   35,   59, "N",    122,   19,   48, "W", "Seattle", WA\n   41,   24,   35, "N",     75,   40,   11, "W", "Scranton", PA\n   41,   52,   11, "N",    103,   39,   36, "W", "Scottsbluff", NB\n   42,   49,   11, "N",     73,   56,   59, "W", "Schenectady", NY\n   32,    4,   48, "N",     81,    5,   23, "W", "Savannah", GA\n   46,   29,   24, "N",     84,   20,   59, "W", "Sault Sainte Marie", MI\n   27,   20,   24, "N",     82,   31,   47, "W", "Sarasota", FL\n   38,   26,   23, "N",    122,   43,   12, "W", "Santa Rosa", CA\n   35,   40,   48, "N",    105,   56,   59, "W", "Santa Fe", NM\n   34,   25,   11, "N",    119,   41,   59, "W", "Santa Barbara", CA\n   33,   45,   35, "N",    117,   52,   12, "W", "Santa Ana", CA\n   37,   20,   24, "N",    121,   52,   47, "W", "San Jose", CA\n   37,   46,   47, "N",    122,   25,   11, "W", "San Francisco", CA\n   41,   27,    0, "N",     82,   42,   35, "W", "Sandusky", OH\n   32,   42,   35, "N",    117,    9,    0, "W", "San Diego", CA\n   34,    6,   36, "N",    117,   18,   35, "W", "San Bernardino", CA\n   29,   25,   12, "N",     98,   30,    0, "W", "San Antonio", TX\n   31,   27,   35, "N",    100,   26,   24, "W", "San Angelo", TX\n   40,   45,   35, "N",    111,   52,   47, "W", "Salt Lake City", UT\n   38,   22,   11, "N",     75,   35,   59, "W", "Salisbury", MD\n   36,   40,   11, "N",    121,   39,    0, "W", "Salinas", CA\n   38,   50,   24, "N",     97,   36,   36, "W", "Salina", KS\n   38,   31,   47, "N",    106,    0,    0, "W", "Salida", CO\n   44,   56,   23, "N",    123,    1,   47, "W", "Salem", OR\n   44,   57,    0, "N",     93,    5,   59, "W", "Saint Paul", MN\n   38,   37,   11, "N",     90,   11,   24, "W", "Saint Louis", MO\n   39,   46,   12, "N",     94,   50,   23, "W", "Saint Joseph", MO\n   42,    5,   59, "N",     86,   28,   48, "W", "Saint Joseph", MI\n   44,   25,   11, "N",     72,    1,   11, "W", "Saint Johnsbury", VT\n   45,   34,   11, "N",     94,   10,   11, "W", "Saint Cloud", MN\n   29,   53,   23, "N",     81,   19,   11, "W", "Saint Augustine", FL\n   43,   25,   48, "N",     83,   56,   24, "W", "Saginaw", MI\n   38,   35,   24, "N",    121,   29,   23, "W", "Sacramento", CA\n   43,   36,   36, "N",     72,   58,   12, "W", "Rutland", VT\n   33,   24,    0, "N",    104,   31,   47, "W", "Roswell", NM\n   35,   56,   23, "N",     77,   48,    0, "W", "Rocky Mount", NC\n   41,   35,   24, "N",    109,   13,   48, "W", "Rock Springs", WY\n   42,   16,   12, "N",     89,    5,   59, "W", "Rockford", IL\n   43,    9,   35, "N",     77,   36,   36, "W", "Rochester", NY\n   44,    1,   12, "N",     92,   27,   35, "W", "Rochester", MN\n   37,   16,   12, "N",     79,   56,   24, "W", "Roanoke", VA\n   37,   32,   24, "N",     77,   26,   59, "W", "Richmond", VA\n   39,   49,   48, "N",     84,   53,   23, "W", "Richmond", IN\n   38,   46,   12, "N",    112,    5,   23, "W", "Richfield", UT\n   45,   38,   23, "N",     89,   25,   11, "W", "Rhinelander", WI\n   39,   31,   12, "N",    119,   48,   35, "W", "Reno", NV\n   50,   25,   11, "N",    104,   39,    0, "W", "Regina", SA\n   40,   10,   48, "N",    122,   14,   23, "W", "Red Bluff", CA\n   40,   19,   48, "N",     75,   55,   48, "W", "Reading", PA\n   41,    9,   35, "N",     81,   14,   23, "W", "Ravenna", OH';

      expect(convertCsvStringToJson(csvString)).toEqual({
        data: expect.any(Array),
        errors: [],
        meta: parseObjectMeta
      });
    });
    it("converts CSV String to JSON parse object with listed errors", () => {
      const csvErrors =
        '"LatD","LatM","LatS","NS","LonD","LonM","LonS","EW","City","State"\n41, 59, "N",     80,   39,    0, "W", "Youngstown", OH\n  42,   52,   48, "N",     97,   23,   23, "W", "Yankton", SD\n   46,   35,   59, "N",    120,   30,   36, "W", "Yakima", WA\n   42,   16,   12, "N",     71,   48,    0, "W", "Worcester", MA\n   43,   37,   48, "N",     89,   46,   11, "W", "Wisconsin Dells", WI\n   36,    5,   59, "N",     80,   15,    0, "W", "Winston-Salem", NC\n   49,   52,   48, "N",     97,    9,    0, "W", "Winnipeg", MB\n   39,   11,   23, "N",     78,    9,   36, "W", "Winchester", VA\n   34,   14,   24, "N",     77,   55,   11, "W", "Wilmington", NC\n   39,   45,    0, "N",     75,   33,    0, "W", "Wilmington", DE\n   48,    9,    0, "N",    103,   37,   12, "W", "Williston", ND\n   41,   15,    0, "N",     77,    0,    0, "W", "Williamsport", PA\n   37,   40,   48, "N",     82,   16,   47, "W", "Williamson", WV\n   33,   54,    0, "N",     98,   29,   23, "W", "Wichita Falls", TX\n   37,   41,   23, "N",     97,   20,   23, "W", "Wichita", KS\n   40,    4,   11, "N",     80,   43,   12, "W", "Wheeling", WV\n   26,   43,   11, "N",     80,    3,    0, "W", "West Palm Beach", FL\n   47,   25,   11, "N",    120,   19,   11, "W", "Wenatchee", WA\n   41,   25,   11, "N",    122,   23,   23, "W", "Weed", CA\n   31,   13,   11, "N",     82,   20,   59, "W", "Waycross", GA\n   44,   57,   35, "N",     89,   38,   23, "W", "Wausau", WI\n   42,   21,   36, "N",     87,   49,   48, "W", "Waukegan", IL\n   44,   54,    0, "N",     97,    6,   36, "W", "Watertown", SD\n   43,   58,   47, "N",     75,   55,   11, "W", "Watertown", NY\n   42,   30,    0, "N",     92,   20,   23, "W", "Waterloo", IA\n   41,   32,   59, "N",     73,    3,    0, "W", "Waterbury", CT\n   38,   53,   23, "N",     77,    1,   47, "W", "Washington", DC\n   41,   50,   59, "N",     79,    8,   23, "W", "Warren", PA\n   46,    4,   11, "N",    118,   19,   48, "W", "Walla Walla", WA\n   31,   32,   59, "N",     97,    8,   23, "W", "Waco", TX\n   38,   40,   48, "N",     87,   31,   47, "W", "Vincennes", IN\n   28,   48,   35, "N",     97,    0,   36, "W", "Victoria", TX\n   32,   20,   59, "N",     90,   52,   47, "W", "Vicksburg", MS\n   49,   16,   12, "N",    123,    7,   12, "W", "Vancouver", BC\n   46,   55,   11, "N",     98,    0,   36, "W", "Valley City", ND\n   30,   49,   47, "N",     83,   16,   47, "W", "Valdosta", GA\n   43,    6,   36, "N",     75,   13,   48, "W", "Utica", NY\n   39,   54,    0, "N",     79,   43,   48, "W", "Uniontown", PA\n   32,   20,   59, "N",     95,   18,    0, "W", "Tyler", TX\n   42,   33,   36, "N",    114,   28,   12, "W", "Twin Falls", ID\n   33,   12,   35, "N",     87,   34,   11, "W", "Tuscaloosa", AL\n   34,   15,   35, "N",     88,   42,   35, "W", "Tupelo", MS\n   36,    9,   35, "N",     95,   54,   36, "W", "Tulsa", OK\n   32,   13,   12, "N",    110,   58,   12, "W", "Tucson", AZ\n   37,   10,   11, "N",    104,   30,   36, "W", "Trinidad", CO\n   40,   13,   47, "N",     74,   46,   11, "W", "Trenton", NJ\n   44,   45,   35, "N",     85,   37,   47, "W", "Traverse City", MI\n   43,   39,    0, "N",     79,   22,   47, "W", "Toronto", ON\n   39,    2,   59, "N",     95,   40,   11, "W", "Topeka", KS\n   41,   39,    0, "N",     83,   32,   24, "W", "Toledo", OH\n   33,   25,   48, "N",     94,    3,    0, "W", "Texarkana", TX\n   39,   28,   12, "N",     87,   24,   36, "W", "Terre Haute", IN\n   27,   57,    0, "N",     82,   26,   59, "W", "Tampa", FL\n   30,   27,    0, "N",     84,   16,   47, "W", "Tallahassee", FL\n   47,   14,   24, "N",    122,   25,   48, "W", "Tacoma", WA\n   43,    2,   59, "N",     76,    9,    0, "W", "Syracuse", NY\n   32,   35,   59, "N",     82,   20,   23, "W", "Swainsboro", GA\n   33,   55,   11, "N",     80,   20,   59, "W", "Sumter", SC\n   40,   59,   24, "N",     75,   11,   24, "W", "Stroudsburg", PA\n   37,   57,   35, "N",    121,   17,   24, "W", "Stockton", CA\n   44,   31,   12, "N",     89,   34,   11, "W", "Stevens Point", WI\n   40,   21,   36, "N",     80,   37,   12, "W", "Steubenville", OH\n   40,   37,   11, "N",    103,   13,   12, "W", "Sterling", CO\n   38,    9,    0, "N",     79,    4,   11, "W", "Staunton", VA\n   39,   55,   11, "N",     83,   48,   35, "W", "Springfield", OH\n   37,   13,   12, "N",     93,   17,   24, "W", "Springfield", MO\n   42,    5,   59, "N",     72,   35,   23, "W", "Springfield", MA\n   39,   47,   59, "N",     89,   39,    0, "W", "Springfield", IL\n   47,   40,   11, "N",    117,   24,   36, "W", "Spokane", WA\n   41,   40,   48, "N",     86,   15,    0, "W", "South Bend", IN\n   43,   32,   24, "N",     96,   43,   48, "W", "Sioux Falls", SD\n   42,   29,   24, "N",     96,   23,   23, "W", "Sioux City", IA\n   32,   30,   35, "N",     93,   45,    0, "W", "Shreveport", LA\n   33,   38,   23, "N",     96,   36,   36, "W", "Sherman", TX\n   44,   47,   59, "N",    106,   57,   35, "W", "Sheridan", WY\n   35,   13,   47, "N",     96,   40,   48, "W", "Seminole", OK\n   32,   25,   11, "N",     87,    1,   11, "W", "Selma", AL\n   38,   42,   35, "N",     93,   13,   48, "W", "Sedalia", MO\n   47,   35,   59, "N",    122,   19,   48, "W", "Seattle", WA\n   41,   24,   35, "N",     75,   40,   11, "W", "Scranton", PA\n   41,   52,   11, "N",    103,   39,   36, "W", "Scottsbluff", NB\n   42,   49,   11, "N",     73,   56,   59, "W", "Schenectady", NY\n   32,    4,   48, "N",     81,    5,   23, "W", "Savannah", GA\n   46,   29,   24, "N",     84,   20,   59, "W", "Sault Sainte Marie", MI\n   27,   20,   24, "N",     82,   31,   47, "W", "Sarasota", FL\n   38,   26,   23, "N",    122,   43,   12, "W", "Santa Rosa", CA\n   35,   40,   48, "N",    105,   56,   59, "W", "Santa Fe", NM\n   34,   25,   11, "N",    119,   41,   59, "W", "Santa Barbara", CA\n   33,   45,   35, "N",    117,   52,   12, "W", "Santa Ana", CA\n   37,   20,   24, "N",    121,   52,   47, "W", "San Jose", CA\n   37,   46,   47, "N",    122,   25,   11, "W", "San Francisco", CA\n   41,   27,    0, "N",     82,   42,   35, "W", "Sandusky", OH\n   32,   42,   35, "N",    117,    9,    0, "W", "San Diego", CA\n   34,    6,   36, "N",    117,   18,   35, "W", "San Bernardino", CA\n   29,   25,   12, "N",     98,   30,    0, "W", "San Antonio", TX\n   31,   27,   35, "N",    100,   26,   24, "W", "San Angelo", TX\n   40,   45,   35, "N",    111,   52,   47, "W", "Salt Lake City", UT\n   38,   22,   11, "N",     75,   35,   59, "W", "Salisbury", MD\n   36,   40,   11, "N",    121,   39,    0, "W", "Salinas", CA\n   38,   50,   24, "N",     97,   36,   36, "W", "Salina", KS\n   38,   31,   47, "N",    106,    0,    0, "W", "Salida", CO\n   44,   56,   23, "N",    123,    1,   47, "W", "Salem", OR\n   44,   57,    0, "N",     93,    5,   59, "W", "Saint Paul", MN\n   38,   37,   11, "N",     90,   11,   24, "W", "Saint Louis", MO\n   39,   46,   12, "N",     94,   50,   23, "W", "Saint Joseph", MO\n   42,    5,   59, "N",     86,   28,   48, "W", "Saint Joseph", MI\n   44,   25,   11, "N",     72,    1,   11, "W", "Saint Johnsbury", VT\n   45,   34,   11, "N",     94,   10,   11, "W", "Saint Cloud", MN\n   29,   53,   23, "N",     81,   19,   11, "W", "Saint Augustine", FL\n   43,   25,   48, "N",     83,   56,   24, "W", "Saginaw", MI\n   38,   35,   24, "N",    121,   29,   23, "W", "Sacramento", CA\n   43,   36,   36, "N",     72,   58,   12, "W", "Rutland", VT\n   33,   24,    0, "N",    104,   31,   47, "W", "Roswell", NM\n   35,   56,   23, "N",     77,   48,    0, "W", "Rocky Mount", NC\n   41,   35,   24, "N",    109,   13,   48, "W", "Rock Springs", WY\n   42,   16,   12, "N",     89,    5,   59, "W", "Rockford", IL\n   43,    9,   35, "N",     77,   36,   36, "W", "Rochester", NY\n   44,    1,   12, "N",     92,   27,   35, "W", "Rochester", MN\n   37,   16,   12, "N",     79,   56,   24, "W", "Roanoke", VA\n   37,   32,   24, "N",     77,   26,   59, "W", "Richmond", VA\n   39,   49,   48, "N",     84,   53,   23, "W", "Richmond", IN\n   38,   46,   12, "N",    112,    5,   23, "W", "Richfield", UT\n   45,   38,   23, "N",     89,   25,   11, "W", "Rhinelander", WI\n   39,   31,   12, "N",    119,   48,   35, "W", "Reno", NV\n   50,   25,   11, "N",    104,   39,    0, "W", "Regina", SA\n   40,   10,   48, "N",    122,   14,   23, "W", "Red Bluff", CA\n   40,   19,   48, "N",     75,   55,   48, "W", "Reading", PA\n   41,    9,   35, "N",     81,   14,   23, "W", "Ravenna", OH';

      expect(convertCsvStringToJson(csvErrors)).toEqual({
        data: expect.any(Array),
        errors: [
          {
            code: "TooFewFields",
            message: "Too few fields: expected 10 fields but parsed 9",
            row: 0,
            type: "FieldMismatch"
          }
        ],
        meta: parseObjectMeta
      });
    });
    it("handles for missing header", () => {
      const csvErrors =
        '"LonS","EW","City","State"\n41, 59, "N",     80,   39,    0, "W", "Youngstown", OH\n  42,   52,   48, "N",     97,   23,   23, "W", "Yankton", SD\n   46,   35,   59, "N",    120,   30,   36, "W", "Yakima", WA\n   42,   16,   12, "N",     71,   48,    0, "W", "Worcester", MA\n   43,   37,   48, "N",     89,   46,   11, "W", "Wisconsin Dells", WI\n   36,    5,   59, "N",     80,   15,    0, "W", "Winston-Salem", NC\n   49,   52,   48, "N",     97,    9,    0, "W", "Winnipeg", MB\n   39,   11,   23, "N",     78,    9,   36, "W", "Winchester", VA\n   34,   14,   24, "N",     77,   55,   11, "W", "Wilmington", NC\n   39,   45,    0, "N",     75,   33,    0, "W", "Wilmington", DE\n   48,    9,    0, "N",    103,   37,   12, "W", "Williston", ND\n   41,   15,    0, "N",     77,    0,    0, "W", "Williamsport", PA\n   37,   40,   48, "N",     82,   16,   47, "W", "Williamson", WV\n   33,   54,    0, "N",     98,   29,   23, "W", "Wichita Falls", TX\n   37,   41,   23, "N",     97,   20,   23, "W", "Wichita", KS\n   40,    4,   11, "N",     80,   43,   12, "W", "Wheeling", WV\n   26,   43,   11, "N",     80,    3,    0, "W", "West Palm Beach", FL\n   47,   25,   11, "N",    120,   19,   11, "W", "Wenatchee", WA\n   41,   25,   11, "N",    122,   23,   23, "W", "Weed", CA\n   31,   13,   11, "N",     82,   20,   59, "W", "Waycross", GA\n   44,   57,   35, "N",     89,   38,   23, "W", "Wausau", WI\n   42,   21,   36, "N",     87,   49,   48, "W", "Waukegan", IL\n   44,   54,    0, "N",     97,    6,   36, "W", "Watertown", SD\n   43,   58,   47, "N",     75,   55,   11, "W", "Watertown", NY\n   42,   30,    0, "N",     92,   20,   23, "W", "Waterloo", IA\n   41,   32,   59, "N",     73,    3,    0, "W", "Waterbury", CT\n   38,   53,   23, "N",     77,    1,   47, "W", "Washington", DC\n   41,   50,   59, "N",     79,    8,   23, "W", "Warren", PA\n   46,    4,   11, "N",    118,   19,   48, "W", "Walla Walla", WA\n   31,   32,   59, "N",     97,    8,   23, "W", "Waco", TX\n   38,   40,   48, "N",     87,   31,   47, "W", "Vincennes", IN\n   28,   48,   35, "N",     97,    0,   36, "W", "Victoria", TX\n   32,   20,   59, "N",     90,   52,   47, "W", "Vicksburg", MS\n   49,   16,   12, "N",    123,    7,   12, "W", "Vancouver", BC\n   46,   55,   11, "N",     98,    0,   36, "W", "Valley City", ND\n   30,   49,   47, "N",     83,   16,   47, "W", "Valdosta", GA\n   43,    6,   36, "N",     75,   13,   48, "W", "Utica", NY\n   39,   54,    0, "N",     79,   43,   48, "W", "Uniontown", PA\n   32,   20,   59, "N",     95,   18,    0, "W", "Tyler", TX\n   42,   33,   36, "N",    114,   28,   12, "W", "Twin Falls", ID\n   33,   12,   35, "N",     87,   34,   11, "W", "Tuscaloosa", AL\n   34,   15,   35, "N",     88,   42,   35, "W", "Tupelo", MS\n   36,    9,   35, "N",     95,   54,   36, "W", "Tulsa", OK\n   32,   13,   12, "N",    110,   58,   12, "W", "Tucson", AZ\n   37,   10,   11, "N",    104,   30,   36, "W", "Trinidad", CO\n   40,   13,   47, "N",     74,   46,   11, "W", "Trenton", NJ\n   44,   45,   35, "N",     85,   37,   47, "W", "Traverse City", MI\n   43,   39,    0, "N",     79,   22,   47, "W", "Toronto", ON\n   39,    2,   59, "N",     95,   40,   11, "W", "Topeka", KS\n   41,   39,    0, "N",     83,   32,   24, "W", "Toledo", OH\n   33,   25,   48, "N",     94,    3,    0, "W", "Texarkana", TX\n   39,   28,   12, "N",     87,   24,   36, "W", "Terre Haute", IN\n   27,   57,    0, "N",     82,   26,   59, "W", "Tampa", FL\n   30,   27,    0, "N",     84,   16,   47, "W", "Tallahassee", FL\n   47,   14,   24, "N",    122,   25,   48, "W", "Tacoma", WA\n   43,    2,   59, "N",     76,    9,    0, "W", "Syracuse", NY\n   32,   35,   59, "N",     82,   20,   23, "W", "Swainsboro", GA\n   33,   55,   11, "N",     80,   20,   59, "W", "Sumter", SC\n   40,   59,   24, "N",     75,   11,   24, "W", "Stroudsburg", PA\n   37,   57,   35, "N",    121,   17,   24, "W", "Stockton", CA\n   44,   31,   12, "N",     89,   34,   11, "W", "Stevens Point", WI\n   40,   21,   36, "N",     80,   37,   12, "W", "Steubenville", OH\n   40,   37,   11, "N",    103,   13,   12, "W", "Sterling", CO\n   38,    9,    0, "N",     79,    4,   11, "W", "Staunton", VA\n   39,   55,   11, "N",     83,   48,   35, "W", "Springfield", OH\n   37,   13,   12, "N",     93,   17,   24, "W", "Springfield", MO\n   42,    5,   59, "N",     72,   35,   23, "W", "Springfield", MA\n   39,   47,   59, "N",     89,   39,    0, "W", "Springfield", IL\n   47,   40,   11, "N",    117,   24,   36, "W", "Spokane", WA\n   41,   40,   48, "N",     86,   15,    0, "W", "South Bend", IN\n   43,   32,   24, "N",     96,   43,   48, "W", "Sioux Falls", SD\n   42,   29,   24, "N",     96,   23,   23, "W", "Sioux City", IA\n   32,   30,   35, "N",     93,   45,    0, "W", "Shreveport", LA\n   33,   38,   23, "N",     96,   36,   36, "W", "Sherman", TX\n   44,   47,   59, "N",    106,   57,   35, "W", "Sheridan", WY\n   35,   13,   47, "N",     96,   40,   48, "W", "Seminole", OK\n   32,   25,   11, "N",     87,    1,   11, "W", "Selma", AL\n   38,   42,   35, "N",     93,   13,   48, "W", "Sedalia", MO\n   47,   35,   59, "N",    122,   19,   48, "W", "Seattle", WA\n   41,   24,   35, "N",     75,   40,   11, "W", "Scranton", PA\n   41,   52,   11, "N",    103,   39,   36, "W", "Scottsbluff", NB\n   42,   49,   11, "N",     73,   56,   59, "W", "Schenectady", NY\n   32,    4,   48, "N",     81,    5,   23, "W", "Savannah", GA\n   46,   29,   24, "N",     84,   20,   59, "W", "Sault Sainte Marie", MI\n   27,   20,   24, "N",     82,   31,   47, "W", "Sarasota", FL\n   38,   26,   23, "N",    122,   43,   12, "W", "Santa Rosa", CA\n   35,   40,   48, "N",    105,   56,   59, "W", "Santa Fe", NM\n   34,   25,   11, "N",    119,   41,   59, "W", "Santa Barbara", CA\n   33,   45,   35, "N",    117,   52,   12, "W", "Santa Ana", CA\n   37,   20,   24, "N",    121,   52,   47, "W", "San Jose", CA\n   37,   46,   47, "N",    122,   25,   11, "W", "San Francisco", CA\n   41,   27,    0, "N",     82,   42,   35, "W", "Sandusky", OH\n   32,   42,   35, "N",    117,    9,    0, "W", "San Diego", CA\n   34,    6,   36, "N",    117,   18,   35, "W", "San Bernardino", CA\n   29,   25,   12, "N",     98,   30,    0, "W", "San Antonio", TX\n   31,   27,   35, "N",    100,   26,   24, "W", "San Angelo", TX\n   40,   45,   35, "N",    111,   52,   47, "W", "Salt Lake City", UT\n   38,   22,   11, "N",     75,   35,   59, "W", "Salisbury", MD\n   36,   40,   11, "N",    121,   39,    0, "W", "Salinas", CA\n   38,   50,   24, "N",     97,   36,   36, "W", "Salina", KS\n   38,   31,   47, "N",    106,    0,    0, "W", "Salida", CO\n   44,   56,   23, "N",    123,    1,   47, "W", "Salem", OR\n   44,   57,    0, "N",     93,    5,   59, "W", "Saint Paul", MN\n   38,   37,   11, "N",     90,   11,   24, "W", "Saint Louis", MO\n   39,   46,   12, "N",     94,   50,   23, "W", "Saint Joseph", MO\n   42,    5,   59, "N",     86,   28,   48, "W", "Saint Joseph", MI\n   44,   25,   11, "N",     72,    1,   11, "W", "Saint Johnsbury", VT\n   45,   34,   11, "N",     94,   10,   11, "W", "Saint Cloud", MN\n   29,   53,   23, "N",     81,   19,   11, "W", "Saint Augustine", FL\n   43,   25,   48, "N",     83,   56,   24, "W", "Saginaw", MI\n   38,   35,   24, "N",    121,   29,   23, "W", "Sacramento", CA\n   43,   36,   36, "N",     72,   58,   12, "W", "Rutland", VT\n   33,   24,    0, "N",    104,   31,   47, "W", "Roswell", NM\n   35,   56,   23, "N",     77,   48,    0, "W", "Rocky Mount", NC\n   41,   35,   24, "N",    109,   13,   48, "W", "Rock Springs", WY\n   42,   16,   12, "N",     89,    5,   59, "W", "Rockford", IL\n   43,    9,   35, "N",     77,   36,   36, "W", "Rochester", NY\n   44,    1,   12, "N",     92,   27,   35, "W", "Rochester", MN\n   37,   16,   12, "N",     79,   56,   24, "W", "Roanoke", VA\n   37,   32,   24, "N",     77,   26,   59, "W", "Richmond", VA\n   39,   49,   48, "N",     84,   53,   23, "W", "Richmond", IN\n   38,   46,   12, "N",    112,    5,   23, "W", "Richfield", UT\n   45,   38,   23, "N",     89,   25,   11, "W", "Rhinelander", WI\n   39,   31,   12, "N",    119,   48,   35, "W", "Reno", NV\n   50,   25,   11, "N",    104,   39,    0, "W", "Regina", SA\n   40,   10,   48, "N",    122,   14,   23, "W", "Red Bluff", CA\n   40,   19,   48, "N",     75,   55,   48, "W", "Reading", PA\n   41,    9,   35, "N",     81,   14,   23, "W", "Ravenna", OH';

      expect(convertCsvStringToJson(csvErrors)).toEqual({
        data: expect.any(Array),
        errors: expect.any(Array),
        meta: parseObjectMeta
      });
    });
    it("handles for empty string", () => {
      expect(convertCsvStringToJson("")).toEqual({
        data: [],
        errors: [
          {
            code: "UndetectableDelimiter",
            message:
              "Unable to auto-detect delimiting character; defaulted to ','",
            row: undefined,
            type: "Delimiter"
          }
        ],
        meta: parseObjectMeta
      });
    });
  });
  describe("convertJsonToCsv", () => {
    it("handles for empty array and object", () => {
      expect(convertJsonToCsv([])).toEqual("");
      expect(convertJsonToCsv({})).toEqual("");
      expect(convertJsonToCsv([{}])).toEqual("");
    });
    it("handles for unexpected input", () => {
      expect(convertJsonToCsv("test")).toEqual("");
      expect(convertJsonToCsv("{}")).toEqual("");
      expect(convertJsonToCsv("[{}]")).toEqual("");
      expect(convertJsonToCsv("[]")).toEqual("");
    });

    it("convert Json string to CSV string in a format identical to the parse operation", () => {
      const jsonData = [
        {
          "Column 1": "1-1",
          "Column 2": "1-2",
          "Column 3": "1-3",
          "Column 4": "1-4"
        },
        {
          "Column 1": "2-1",
          "Column 2": "2-2",
          "Column 3": "2-3",
          "Column 4": "2-4"
        },
        {
          "Column 1": "3-1",
          "Column 2": "3-2",
          "Column 3": "3-3",
          "Column 4": "3-4"
        },
        {
          "Column 1": 4,
          "Column 2": 5,
          "Column 3": 6,
          "Column 4": 7
        }
      ];

      const csvResult = convertJsonToCsv(jsonData);

      expect(typeof csvResult).toBe("string");

      // String to match original json when parsed
      const jsonResult = convertCsvStringToJson(csvResult);
      expect((jsonResult as any).data).toEqual(jsonData);
    });
  });

  describe("csvToSchema", () => {
    const parseObjectMeta = {
      aborted: false,
      cursor: expect.any(Number),
      delimiter: ";",
      fields: expect.any(Array),
      linebreak: "\n",
      truncated: false
    };
    const csvString =
      "partnerId;partnerName;partnerBaseEarnRate;baseCashRedemption;displayDate;startDate;endDate;offerType;qualifier;awardType;awardShort.en-US;awardShort.fr-CA;qualifierShort.en-US;qualifierShort.fr-CA;issuanceCode;image.en-US.path;image.fr-CA.path;cashierInstruction.en-US;cashierInstruction.fr-CA;mechanisms[0].mechanismType;mechanisms[0].mechanismLabel.en-US;mechanisms[0].mechanismLabel.fr-CA;mechanisms[0].mechanismValue;mechanisms[0].mechanismText.en-US;mechanisms[0].mechanismText.fr-CA;mechanisms[1].mechanismType;mechanisms[1].mechanismLabel.en-US;mechanisms[1].mechanismLabel.fr-CA;mechanisms[1].mechanismValue;mechanisms[1].mechanismText.en-US;mechanisms[1].mechanismText.fr-CA;mechanisms[2].mechanismType;mechanisms[2].mechanismLabel.en-US;mechanisms[2].mechanismLabel.fr-CA;mechanisms[2].mechanismValue;mechanisms[2].mechanismText.en-US;mechanisms[2].mechanismText.fr-CA;tiers[0].awardLong.en-US;tiers[0].awardLong.fr-CA;tiers[1].awardValue;tiers[1].qualifierValue;tiers[1].content.en-US;tiers[1].content.fr-CA;tiers[1].qualifierLong.en-US;tiers[1].qualifierLong.fr-CA;tiers[1].awardLong.en-US;tiers[1].awardLong.fr-CA;tiers[2].awardValue;tiers[2].qualifierValue;tiers[2].content.en-US;tiers[2].content.fr-CA;tiers[2].qualifierLong.en-US;tiers[2].qualifierLong.fr-CA;tiers[2].awardLong.en-US;tiers[2].awardLong.fr-CA;displayPriority;promoPriority;regions;offerLimitation;offerLimitationText.en-US;offerLimitationText.fr-CA;includedLocations.en-US;includedLocations.fr-CA;excludedLocations.en-US;excludedLocations.fr-CA;includedBanners.en-US;includedBanners.fr-CA;excludedBanners.en-US;excludedBanners.fr-CA;canBeCombined;combinationsText.en-US;combinationsText.fr-CA;exclusions.en-US;exclusions.fr-CA;availability;partnerUrl.en-US;partnerUrl.fr-CA;daysToApply;trademarkInfo.en-US;trademarkInfo.fr-CA;description.en-US;description.fr-CA;tags;labels.en-US;labels.fr-CA;hasCustomLegal;legalText.en-US;legalText.fr-CA;partnerLegalName.en-US;partnerLegalName.fr-CA\nf0f6e127-e5c1-44e2-9991-d105b482e663 ;Air Miles Partner;50;50;2019-02-14T00:00:00Z;2019-02-14T00:00:00Z;2019-02-14T23:59:00Z;buy;product;flatMiles;non-empty;can be empty;non-empty;can be empty;1u2u3u4u5u6;https://sandbox-post-public.s3.amazonaws.com/images/partner/f0f6e127-e5c1-44e2-9991-d105b482e663/offer/1550155871525134992699cea28bb.jpg;;Cashier instruction;Cashier instruction FR;button;my button label;french button label;mechanism value string;english mechanism text;french mechanism text;button;my button label;french button label;mechanism value string;english mechanism text;french mechanism text;button;my button label;french button label;mechanism value string;english mechanism text;french mechanism text;2x Miles;2x les milles;60;20;Tier content|Tier content 2;Tier content FR|Tier Content 2;Buy 2 of foafjaiooaof or afmiaf or afaif or afaf or ewewe products in-store or online*;Achetez 2 d’achats ajdioajsd ou asdasd ou asdasd ou asdasd ou asdasd en magasin ou en ligne*;2x Miles;2x les milles;60;20;Tier content;Tier content FR;Buy 2 of foafjaiooaof or afmiaf or afaif or afaf or ewewe products in-store or online*;Achetez 2 d’achats ajdioajsd ou asdasd ou asdasd ou asdasd ou asdasd en magasin ou en ligne*;2x Miles;2x les milles;250;250;ab|bc;perCollectorPerTransaction;English offer Limitation Text;French offer Limitation Text;Location 1|Location 2;Location 1 FR|Location 2 FR ;Excluded Location 1|Ex Loc 2;Excluded Location 1;Banner 1|Banner 2;|Banner 2;Excluded Banner English;Excluded Banner Fr;TRUE;offers, and AIR MILES offers;offers, and AIR MILES offers FR;English Exclusions;French Exclusions;online|inStore;https://www.google.ca;https://www.google.ca;50;Trademark Info;Trademark Info FR;My offer description;My offer description FR;ShopTheBlock|Tag 2|Tag 3;Shop the Block|Label 2 ;Shop the Block FR |;FALSE;* Offer valid on February 14, 2019. Valid at participating AIR MILES Travel Hub, adiajsdoa and doadioa locations (excluding asdjuaidhuihasi and asdnaisdhi locations) in Alberta, British Columbia, New Brunswick, Nova Scotia, Nunavut, Ontario, Saskatchewan, Thunder Bay, Yukon and asdasjuidaih (excluding asdjiaosjdoasod and asdnsadji) or on https://www.google.ca. Minimum eligible purchase must be spent in a single transaction, excluding adasdjasidasuid. While supplies last. Product availability may vary by store. We reserve the right to limit quantities. Multiplier offer applies to standard base offer of 1 Mile for every $40.00 purchase. Limit of one per Collector Number per transaction. Please allow up to 50 days from the offer end date for Bonus Miles to be posted to your Collector Account. AIR MILES Card must be presented at the time of the purchase. Can be combined with other offers, and AIR MILES offers. ®™ Trademarks of AM Royalties Limited Partnership used under license by LoyaltyOne, Co.;* Offre en vigueur le 14 février 2019. Valable dans toutes les succursales AIR MILES Travel Hub, wjeiqjoweqw et aaa participantes (sauf les succursales ahsuidhiasd et aushdia) dans les régions suivantes : Alberta, Colombie-Britannique, Nouveau-Brunswick, Nouvelle-Écosse, Nunavut, Ontario, Saskatchewan, Thunder Bay, Yukon et ahsudhiuashd (sauf ajsoidjioajoisd et asjdioasd) ou à https://www.google.ca. L’achat minimum admissible doit être effectué en une seule transaction, excluant les hasdhiuhasuid. Jusqu’à épuisement des stocks. La disponibilité des produits peut varier selon le magasin. Nous nous réservons le droit d’appliquer une limite aux quantités. L’offre multiplicatrice s’applique à l’offre de base d’un (1) mille par tranche de 40.00 $ d’achats. Limite de une par numéro d’adhérent et par transaction. Veuillez prévoir jusqu’à 50 jours après la fin de l’offre pour le versement des milles en prime dans votre compte d’adhérent. La carte AIR MILES doit être présentée au moment de l’achat. Peut se combiner avec d’autres offres et offres AIR MILES. md/mc Marque déposée/de commerce d'AM Royalties Limited Partnership, employée en vertu d'une licence par LoyaltyOne, Co.;Partner legal name;Partner legal name";
    it("converts basic CSV String to JSON schema object", () => {
      const [json] = csvToSchema(csvString);

      expect(json.partnerId).toEqual("f0f6e127-e5c1-44e2-9991-d105b482e663 ");
    });
    it("converts delimited values into arrays of strings", () => {
      const [json] = csvToSchema(csvString);

      expect(json.availability).toEqual(["online", "inStore"]);
      expect(json.regions).toEqual(["ab", "bc"]);
      expect(json.tags).toEqual(["ShopTheBlock", "Tag 2", "Tag 3"]);
    });
    it("converts to localized object arrays", () => {
      const [json] = csvToSchema(csvString);
      expect(json.includedBanners).toEqual([
        { "en-US": "Banner 1", "fr-CA": "" },
        { "en-US": "Banner 2", "fr-CA": "Banner 2" }
      ]);
    });
  });
});
