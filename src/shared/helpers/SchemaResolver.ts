import {
  JSONSchema7,
  JSONSchema7<PERSON>rray,
  JSONSchema7Definition
} from "json-schema";
import { isEmpty } from "lodash";

export function evaluateIf(
  ifSchema: JSONSchema7Definition,
  propertyName: string,
  value: any
): boolean {
  if (!ifSchema || isEmpty(ifSchema)) {
    throw new Error("Empty schema");
  }

  if (typeof ifSchema === "object" && ifSchema.properties) {
    if (!(propertyName in ifSchema.properties)) {
      throw new Error("Invalid property name");
    }

    const schemaExpected = ifSchema.properties[propertyName];
    if (typeof schemaExpected === "object" && !Array.isArray(schemaExpected)) {
      if ("enum" in schemaExpected) {
        return Array.prototype.indexOf.call(schemaExpected.enum, value) >= 0;
      }
      return schemaExpected === value;
    }
  }

  return false;
}
