import { isEmpty } from ".";

// TODO: this should be moved to the validator class under /validation
export class Validator {
  public static isRequired(
    value: object | number | string | undefined | null
  ): boolean {
    return isEmpty(value);
  }
  public static isNumeric(value: any): boolean {
    if (value === undefined || value === null) {
      return false;
    }
    const reg = new RegExp(/^\d+$/g);
    return reg.test(value.toString().trim());
  }
  public static minLength(value: string, len: number): boolean {
    return !((value || "").length >= len);
  }
  public static maxLength(value: string, len: number): boolean {
    return !((value || "").length <= len);
  }
}
