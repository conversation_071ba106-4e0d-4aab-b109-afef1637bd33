import { countDistinctBy, distinct, groupByValueIn<PERSON>ey, isEmpty, objectToQueryString, queryStringToObject } from "./";

describe("helpers", () => {
  describe("isEmpty", () => {
    it("returns true for empty string", () => {
      expect(isEmpty("")).toBeTruthy();
    });

    it("returns false for non-empty string", () => {
      expect(isEmpty("test")).toBeFalsy();
    });

    it("returns true for undefined", () => {
      expect(isEmpty(undefined)).toBeTruthy();
    });

    it("returns true for null", () => {
      expect(isEmpty(null)).toBeTruthy();
    });

    it("returns true for empty object", () => {
      expect(isEmpty({})).toBeTruthy();
    });

    it("returns false for non-empty object", () => {
      expect(isEmpty({ test: "test" })).toBeFalsy();
    });

    it("returns true for empty array", () => {
      expect(isEmpty([])).toBeTruthy();
    });

    it("returns false for array", () => {
      expect(isEmpty([1, 2])).toBeFalsy();
    });

    it("returns true for array of nulls", () => {
      expect(isEmpty([null, null])).toBeTruthy();
    });

    it("returns false for collection", () => {
      expect(isEmpty([{ a: 1 }, { b: 2 }])).toBeFalsy();
    });

    it("returns true for non-empty object with empty attributes", () => {
      expect(isEmpty([{ a: "" }, { b: [] }])).toBeTruthy();
    });

    it("returns false for non-empty object with partially empty attributes", () => {
      expect(isEmpty([{ a: "test", b: "", c: null }, { b: [] }])).toBeFalsy();
    });

    it("returns true for nested objects", () => {
      expect(isEmpty({ a: "", b: { c: [] } })).toBeTruthy();
    });
  });

  describe("queryStringToObject", () => {
    it("converts query string to object", () => {
      const qs = "?key1=1&key2=true";
      const expectValue = { key1: "1", key2: "true" };
      const result = queryStringToObject(qs);

      expect(result).toEqual(expectValue);
    });
  });

  describe("objectToQueryString", () => {
    it("can convert empty object to query string", () => {
      const params = {};
      const str = objectToQueryString(params);
      const expectedValue = "";
      expect(str).toBe(expectedValue);
    });

    it("can convert object to query params string", () => {
      const date = new Date();
      const params = { test: 1, ok: false, date, false: "" };
      const str = objectToQueryString(params);
      const expectedValue = `test=1&ok=false&date=${ date.toString() }`;
      expect(str).toBe(expectedValue);
    });
  });

  describe("groupBy", () => {
    it("can group array of objects by key", () => {
      const arr = [
        { id: 1, text: "item 1" },
        { id: 1, text: "item 2" },
        { id: 1, text: "item 3" }
      ];

      const result = groupByValueInKey("id", arr);
      expect(result).toBeDefined();
      expect(result).toEqual({ "1": arr });
    });
  });

  describe("countDistinctBy", () => {
    it("can group array of objects by key", () => {
      const arr = [
        { id: 1, text: "item 1" },
        { id: 1, text: "item 2" },
        { id: 1, text: "item 3" }
      ];

      const result = countDistinctBy(arr, "id");
      expect(result).toBeDefined();
      expect(result).toEqual(1);
    });
  });

  describe("distinct", () => {
    it("disctincts empty array", () => {
      const arr: any[] = [];
      const result = distinct([]);
      expect(result).toEqual(arr);
    });

    it("disctinct array of objects", () => {
      const arr: any[] = [
        { a: 1, b: 1 },
        { a: 1, b: 1 },
        { a: 1, b: 1 },
        { a: 1, b: 1 }
      ];
      const result = distinct(arr, (x, y) => x.a === y.a && x.b === y.b);
      expect(result.length).toBe(1);
    });
  });
});
