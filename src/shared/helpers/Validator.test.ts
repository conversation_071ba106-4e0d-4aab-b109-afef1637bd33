import { Validator } from "./Validator";

describe("Validator", () => {
  describe("required", () => {
    it("returns false for non empty string", () => {
      expect(Validator.isRequired("test")).toBeFalsy();
    });
    it("returns true for spaces string", () => {
      expect(Validator.isRequired("   ")).toBeTruthy();
    });
    it("returns true for empty string", () => {
      expect(Validator.isRequired("")).toBeTruthy();
    });
    it("returns true for undefined", () => {
      expect(Validator.isRequired(undefined)).toBeTruthy();
    });
    it("returns true for null", () => {
      expect(Validator.isRequired(null)).toBeTruthy();
    });
    it("returns false for number", () => {
      expect(Validator.isRequired(42)).toBeFalsy();
    });
  });

  describe("numeric", () => {
    it("returns true for number", () => {
      expect(Validator.isNumeric("10")).toBeTruthy();
    });
    it("returns false for boolean", () => {
      expect(Validator.isNumeric(true)).toBeFalsy();
    });
    it("returns false for object", () => {
      expect(Validator.isNumeric({})).toBeFalsy();
    });
    it("returns false for empty string", () => {
      expect(Validator.isNumeric("")).toBeFalsy();
    });
    it("returns false for undefined string", () => {
      expect(Validator.isNumeric(undefined)).toBeFalsy();
    });
  });
});
