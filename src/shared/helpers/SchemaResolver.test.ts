import schema from "../../validation/schemas/PostOfferFormObject.json";
import { evaluateIf } from "./SchemaResolver";

describe("SchemaResolver", () => {
  describe("evaluteIf", () => {
    it("returns true for if schema", () => {
      const test: any = schema.dependencies.offerType.allOf[0].if;
      const result = evaluateIf(test, "offerType", "buy");
      expect(result).toBe(true);
    });
  });
});
