import { cloneDeep, entries, get, includes, intersection, set } from "lodash";
import <PERSON> from "papaparse";
import { IOfferFormModel, IValidationErrors } from "../../validation/validator";
import { IProcessImageBatchResult } from "../services/image.service";

/**
 * Converts CSV string to JSON
 * @param {string} csvString is a string of delimited text to be parsed.
 * @returns a parse results object (if not streaming or using worker).
 */
export function convertCsvStringToJson(
  csvString: string,
  configOverride?: object
): object {
  if (configOverride) {
    return Papa.parse(csvString, configOverride);
  }

  const config = {
    dynamicTyping: true,
    header: true
  };
  return Papa.parse(csvString, config);
}

/**
 * Converts JSON to CSV
 * @param {object} jsonData json object
 * @returns a parse results object (if not streaming or using worker).
 */
export function convertJsonToCsv<T = object>(jsonData: T[]): string {
  if (typeof jsonData === "string") {
    return "";
  }
  return Papa.unparse(jsonData);
}

/**
 * Converts JSON parse results object to JSON offer form json schema by nesting fields
 * @param {object} jsonData parse results object from papa parse
 * @returns a json object that has nested fields
 */
export function createNestedJson(jsonData: Papa.ParseResult): any[] {
  return jsonData.data.map((flatData: any) => {
    const nestedData = entries(flatData).reduce(
      (obj: any, [key, value]: any) => {
        // if there is no value then do not set the path
        if (value === null) {
          return obj;
        }

        const nestedKey = key.split(".");

        // if the value contains an array delimiter handle for more than one value
        if (includes(value, "|")) {
          const nestedValue: string[] = value
            .split("|")
            .map((val: string) => val.trim());

          // if the last element in the key is a 'en-US' or 'fr-CA' insert a position based on the nestedValue needed
          if (
            nestedKey[nestedKey.length - 1] === "en-US" ||
            nestedKey[nestedKey.length - 1] === "fr-CA"
          ) {
            // only handle here positions that are above zero.
            for (let i = 0; i < nestedValue.length; i++) {
              let newPath = cloneDeep(nestedKey);

              newPath.splice(newPath.length - 1, 0, "[" + i + "]");
              newPath = newPath.join(".");

              set(obj, newPath, nestedValue[i]);
            }
          } else {
            set(obj, key, nestedValue);
          }
        } else if (
          intersection(nestedKey, [
            "content",
            "includedLocations",
            "excludedLocations",
            "includedBanners",
            "excludedBanners"
          ]).length > 0
        ) {
          // if nestedKey contains anny of the known special cases, add a zero'th position and continue as usual.
          let newPath = cloneDeep(nestedKey);
          newPath.splice(newPath.length - 1, 0, "[0]");
          newPath = newPath.join(".");

          set(obj, newPath, value);
        } else if (
          intersection(nestedKey, ["availability", "regions", "tags", "skus"]).length >
          0
        ) {
          let newPath = cloneDeep(nestedKey);
          newPath.push("[0]");
          newPath = newPath.join(".");

          set(obj, newPath, value);
        }
        // We force this mechanismValue to be a string, since it could be a number, and the dynamic parsing will type it as a number
        // causing problems for the validation
        else if (
          key.includes(".mechanismValue.en-US") ||
          key.includes(".mechanismValue.fr-CA")
        ) {
          set(obj, key, "" + value);
        } else {
          set(obj, key, value);
        }
        return obj;
      },
      {}
    );

    return nestedData;
  });
}

/**
 * Converts CSV string to JSON offer form json schema by nesting fields
 * @param {string} csvString csv string
 * @returns a json object that has nested fields
 */
export function csvToSchema<T>(
  csvString: string,
  configOverride?: Papa.ParseConfig
): T[] {
  if (configOverride) {
    return Papa.parse(csvString, configOverride).data;
  }

  const jsonObject = Papa.parse(csvString, {
    dynamicTyping: true,
    header: true,
    skipEmptyLines: "greedy",
    encoding: "ISO-8859-1"
  });
  return createNestedJson(jsonObject);
}

/**
 * Transforms the error object into a new structure that will be suitably parsed into csv.
 * @param {string} errorObject errors in the format as returned by ajv
 * @returns a json object that has fields as expected in the downloable errors csv
 */
export function mapErrorObject(
  errorObject: Array<{
    data: IOfferFormModel;
    validationResults: IValidationErrors[];
  }>
) {
  const mappedObject = errorObject.map((data, index: number) => {
    // Loop over the data validatoin results and return the correct fields
    return data.validationResults.map(obj => {
      let allowedValues: string = "";
      // If the datapath is empty return the missing property to the user for a better user experience
      const dataPathValue = obj.dataPath
        ? obj.dataPath
        : obj.params.missingProperty;

      if (obj.keyword === "additionalProperties") {
        obj.data = obj.params;
      }
      if (obj.keyword === "enum") {
        allowedValues = JSON.stringify(obj.schema);
      }
      if (obj.keyword === "regions") {
        allowedValues = obj.params.allowedValues;
      }
      return {
        offer: `Row ${index + 2} - Offer Entry ${index + 1}`,
        // tslint:disable-next-line:object-literal-sort-keys
        path: dataPathValue,
        message: obj.message,
        data: JSON.stringify(obj.data),
        allowedValues,
        // tslint:disable-next-line:object-literal-sort-keys
        keyword: obj.keyword
      };
    });
  });
  return mappedObject;
}

/**
 * Maps the errors from the image processing result to the exact rows in CSV
 * @param {IOfferFormModel[]} csv containing offers
 * @param {IProcessImageBatchResult} imageProcessingResult containing metadata of the processing result
 * @returns a json object that has fields as expected in the downloable errors csv
 */
export function mapImageProcessingErrors(
  csv: IOfferFormModel[],
  imageProcessingResult: IProcessImageBatchResult
) {
  // Retrieve all failed processed images and map them to the corresponding offer in the csv
  return Object.entries(imageProcessingResult)
    .filter(([imageKey, metadata]) => !metadata.success)
    .map(([imageKey, metadata]) => {
      // If imageKey path exists in the CSV return CSV error messaging
      if (pathExistsInCsv(imageKey, csv)) {
        // We have to map the errors to the offers that have corresponding images because multiple offers may use the same error, so the imageProcessResult will only show unique values
        return csv
          .filter(
            data =>
              get(data, "image.en-US.path") === imageKey ||
              get(data, "image.fr-CA.path") === imageKey
          )
          .map((data, index) => {
            // Because we've filtered the CSV, we have to find the index by comparing the offer with the error to overall CSV list of offers and matching the row there
            const readableCsvRow = csv.indexOf(data) + 1;
            return {
              offer: "Offer Entry " + readableCsvRow,
              path:
                get(data, "image.en-US.path") === imageKey
                  ? "image.en-US.path"
                  : "image.fr-CA.path",
              message: metadata.message,
              keyword: metadata.error_code
            };
          });
      } else {
        // If the path does not exist in the CSV generate zip file error message
        return {
          offer: "Zip File Error",
          path: imageKey,
          message: metadata.message,
          keyword: metadata.error_code
        };
      }
    });
}

function pathExistsInCsv(imageKey: string, csv: IOfferFormModel[]) {
  return csv.some(
    data =>
      get(data, "image.en-US.path") === imageKey ||
      get(data, "image.fr-CA.path") === imageKey
  );
}
