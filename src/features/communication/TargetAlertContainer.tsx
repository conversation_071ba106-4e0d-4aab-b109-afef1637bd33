import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import { Action } from "redux";
import { ThunkDispatch } from "redux-thunk";
import { BB8CustomAlert, IBB8AlertProps } from "../../BB8";
import { IRootState, withCommunicationDispatchToProps, } from "../../store";
import * as communicationActions from "../communication/actions";
import TargetAlertMessage from "./TargetAlertMessage";

export type AlertContentType = Pick<IBB8AlertProps, "variant"> & {
  message?: string | React.ReactNode;
};
export interface SnackbarProps {
  alertContent?: Pick<IBB8AlertProps, "message" | "variant">;
  displayAlert: (content: AlertContentType) => void;
  hideAlert: () => void;
}

export const SnackbarContext = React.createContext<SnackbarProps>({
  displayAlert: () => {},
  hideAlert: () => {}
});

interface AlertContainerProps {
  showTargetAlertState?: boolean;
  hideTargetAlertAction: () => void;
}

const TargetAlertContainer: React.FunctionComponent<AlertContainerProps> = ({
  showTargetAlertState,
  hideTargetAlertAction
}) => {
  const [state, setState] = useState<{
    showTargetAlertLocal: boolean;
  }>({showTargetAlertLocal: false});

  useEffect(() => {
    setState(({ showTargetAlertLocal: showTargetAlertState || false}))
  }, [showTargetAlertState]);

  const handleTargetAlertClose = (e: any, reason: string) => {
    if(reason !== 'clickaway') {
      hideTargetAlertAction();
    }
  }
  return (
    <BB8CustomAlert message="" variant="warning" show={state.showTargetAlertLocal} onClose={handleTargetAlertClose} classes={ {} }>
      <TargetAlertMessage/>
    </BB8CustomAlert>
  );
};

const mapStateToProps = (state: IRootState) => ({
  showTargetAlertState: state.communication.showTargetAlert
});

const mapDispatchToProps = (
  dispatch: ThunkDispatch<IRootState, void, Action>
) =>
  withCommunicationDispatchToProps(dispatch, {
    hideTargetAlertAction: () => dispatch(communicationActions.hideTargetAlert()),
  });

export default connect(mapStateToProps, mapDispatchToProps)(TargetAlertContainer);
