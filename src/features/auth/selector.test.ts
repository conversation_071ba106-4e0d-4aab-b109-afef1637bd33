import { IRoleMatrix } from "../../models/Role";
import { IRootState } from "../../store";
import { getRoleMatrix } from "./selector";

describe("Auth Selectors", () => {
  it("returns empty RoleMatrix given empty state", () => {
    const expected: IRoleMatrix = {
      availableActions: [],
      roles: []
    };

    const mockState: IRootState = {
      administration: {
        roles: []
      },
      auth: {},
      communication: { pendingRequests: 0 }
    };
    const result = getRoleMatrix(mockState);

    expect(result).toEqual(expected);
  });

  it("returns RoleMatrix with actions given state", () => {
    const actions = ["MANAGE_RESOURCE"];

    const rolePermission = {
      permission: {
        id: 0,
        name: "MANAGE_RESOURCE"
      },
      permissionId: 0,
      roleId: 0
    };

    const role = {
      actions,
      id: 0,
      name: "USER",
      rolePermissions: [rolePermission]
    };

    const roles = [role];

    const expected: IRoleMatrix = {
      availableActions: ["MANAGE_RESOURCE"],
      roles: [{ ...roles[0], actions: ["VIEW", "CREATE", "EDIT", "DELETE"] }]
    };

    const mockState: IRootState = {
      administration: {
        roles
      },
      auth: {
        permissionConfig: {
          actions: ["MANAGE_RESOURCE"],
          mappings: {
            MANAGE_RESOURCE: ["VIEW", "CREATE", "EDIT", "DELETE"]
          }
        }
      },
      communication: { pendingRequests: 0 }
    };

    const result = getRoleMatrix(mockState);

    expect(result).toEqual(expected);
  });
});
