import { IUser } from "../../../models/User";
import { Authorizer } from "./Authorizer";

const mockPermissionConfig = {
  actions: [
    "CREATE_OFFER",
    "EDIT_OFFER",
    "PUBLISH_OFFER",
    "VIEW_OFFER",
    "DIS<PERSON>LE_OFFER",
    "EDIT_USER",
    "DISABLE_USER",
    "CREATE_ROLE",
    "EDIT_ROL<PERSON>",
    "DELETE_ROLE",
    "CREATE_GROUP",
    "EDIT_GROUP",
    "DELETE_GROUP"
  ],
  mappings: {
    VIEW_OFFER: ["VIEW_OFFER"],
    CREATE_OFFER: ["CREATE_OFFER"],
    EDIT_OFFER: ["EDIT_OFFER"],
    PUBLISH_OFFER: ["PUBLISH_OFFER"],
    DISABLE_OFFER: ["DISABLE_OFFER"],
    EDIT_USER: ["EDIT_USER"],
    DISABLE_USER: ["DISABLE_USER"],
    CREATE_ROLE: ["CREATE_ROLE"],
    EDIT_ROLE: ["EDIT_ROLE"],
    DELETE_ROLE: ["DELETE_ROLE"],
    CREATE_GROUP: ["CREATE_GROUP"],
    EDIT_GROUP: ["EDIT_GROUP"],
    DELETE_GROUP: ["DELETE_GROUP"]
  }
};

describe("Authorization", () => {
  it("checkPermissions returns false if empty authorization", () => {
    const authorizer = Authorizer.Instance;
    authorizer.init();
    const isAuthorized = Authorizer.Instance.checkPermissions([]);
    expect(isAuthorized).toBe(false);
  });

  it("checkPermissions returns false if permission not found", () => {
    const authorizer = Authorizer.Instance;
    authorizer.init(mockPermissionConfig);
    const isAuthorized = Authorizer.Instance.checkPermissions([
      "EAT_ICE_CREAM"
    ]);
    expect(isAuthorized).toBe(false);
  });

  it("checkPermissions returns false if permission is not found", () => {
    const user: IUser = {
      name: "test",
      exp: 1111,
      email: "",
      role: "Admin",
      permissions: ["MANAGE_ROLES"]
    };
    const authorizer = Authorizer.Instance;
    authorizer.init(mockPermissionConfig);
    const isAuthorized = Authorizer.Instance.checkPermissions(
      ["MANAGE_GROUPS"],
      user
    );
    expect(isAuthorized).toBe(false);
  });

  it("checkPermissions returns true if permission is found", () => {
    const user: IUser = {
      name: "test",
      exp: 1111,
      email: "",
      role: "Admin",
      permissions: ["EDIT_GROUP"]
    };
    const authorizer = Authorizer.Instance;
    authorizer.init(mockPermissionConfig);
    const isAuthorized = Authorizer.Instance.checkPermissions(
      ["EDIT_GROUP"],
      user
    );
    expect(isAuthorized).toBe(true);
  });

  it("checkPermissions returns false if not all permissions are found and strict check is requested", () => {
    const user: IUser = {
      name: "test",
      exp: 1111,
      email: "",
      role: "Admin",
      permissions: ["EDIT_GROUP"]
    };
    const authorizer = Authorizer.Instance;
    authorizer.init(mockPermissionConfig);
    const isAuthorized = Authorizer.Instance.checkPermissions(
      ["EDIT_GROUP", "EDIT_USER"],
      user,
      true
    );
    expect(isAuthorized).toBe(false);
  });

  it("checkPermissions returns true if all permissions are found and strict check is requested", () => {
    const user: IUser = {
      name: "test",
      exp: 1111,
      email: "",
      role: "Admin",
      permissions: ["EDIT_GROUP", "EDIT_USER"]
    };
    const authorizer = Authorizer.Instance;
    authorizer.init(mockPermissionConfig);
    const isAuthorized = Authorizer.Instance.checkPermissions(
      ["EDIT_GROUP", "EDIT_USER"],
      user,
      true
    );
    expect(isAuthorized).toBe(true);
  });
});
