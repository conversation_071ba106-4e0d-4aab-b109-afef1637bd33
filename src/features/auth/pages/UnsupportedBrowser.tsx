import React from "react";
import { BB8Button } from "../../../BB8";
import clouds from "./NotFound.jpg";

const UnsupportedBrowser = (props: any) => {
  return (
    <main
      style={{
        position: "fixed",
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
        alignItems: "center",
        backgroundImage: `url(${clouds}`,
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
        display: "flex",
        flexFlow: "column wrap",
        justifyContent: "center",
        overflow: "hidden",
        width: "100%"
      }}
    >
      <h1 style={{ color: "#fff", fontSize: "120px", marginBottom: "2rem" }}>
        Uh oh...
      </h1>
      <p style={{ color: "#fff", fontSize: "50px", marginBottom: "2rem" }}>
        This browser is not supported.
      </p>
      <p style={{ color: "#fff", fontSize: "50px", marginBottom: "2rem" }}>
        You can access this page <br />
        using Google Chrome.
      </p>
      <BB8Button
        color="primary"
        variant="contained"
        size="large"
        style={{ fontSize: "2em" }}
        onClick={() => window.open("https://www.google.com/chrome/", "_blank")}
      >
        Learn more about Google Chrome
      </BB8Button>
    </main>
  );
};

export default UnsupportedBrowser;
