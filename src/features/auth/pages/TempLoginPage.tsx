import { Grid, Paper, Theme, Typography, withStyles, WithStyles } from "@material-ui/core";
import React, { Component } from "react";
import { connect } from "react-redux";
import { RouteComponentProps } from "react-router-dom";
import { BB8Button } from "../../../BB8";
import { IRootState, withAuthStateToProps } from "../../../store";
import * as loginActions from "../actions";
import { IAuthState } from "../types";
import { UserRoles } from "./UserRoles";

interface ITempLoginPageProps
  extends WithStyles,
    RouteComponentProps,
    IAuthState {
  login: (useRole: UserRoles) => Promise<any>;
}

const styles = (theme: Theme) => ({
  button: {
    margin: theme.spacing(2)
  },
  paper: {
    padding: theme.spacing(4)
  },
  root: {
    height: "calc(100vh - 80px)"
  }
});

export class TempLoginPage extends Component<ITempLoginPageProps> {
  public render() {
    return (
      <Grid
        container={true}
        direction="column"
        alignItems="center"
        justify="space-around"
        className={this.props.classes.root}
      >
        <Paper elevation={2} className={this.props.classes.paper}>
          <Grid
            container={true}
            direction="column"
            alignItems="center"
            justify="space-around"
          >
            <Typography variant="h4" gutterBottom={true} align="center">
              Temporary Authentication Page
            </Typography>
            <BB8Button
              variant="contained"
              color="primary"
              className={this.props.classes.button}
              onClick={this.handleLoginClick(UserRoles.Reviewer)}
            >
              Login as USER
            </BB8Button>

            <BB8Button
              variant="contained"
              color="primary"
              className={this.props.classes.button}
              onClick={this.handleLoginClick(UserRoles.Publisher)}
            >
              Login as PUBLISHER
            </BB8Button>

            <BB8Button
              variant="contained"
              color="primary"
              className={this.props.classes.button}
              onClick={this.handleLoginClick(UserRoles.Administrator)}
            >
              Login as ADMINISTRATOR
            </BB8Button>
          </Grid>
        </Paper>
      </Grid>
    );
  }
  private handleLoginClick = (userRole: UserRoles) => () => {
    // pass the type of user signing in
    this.props.login(userRole).then(() => this.props.history.push("/"));
  };
}

const mapStateToProps = (state: IRootState) => withAuthStateToProps(state);

const mapDispatchToProps = {
  login: loginActions.asyncLogin
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(styles)(TempLoginPage));
