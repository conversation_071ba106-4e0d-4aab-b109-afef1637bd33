import { Grid } from "@material-ui/core";
import React from "react";
import { connect } from "react-redux";
import { Dispatch } from "redux";
import { BB8MainPage } from "../../../BB8";
import { withAuthDispatchToProps } from "../../../store";

class LoggedOut extends React.PureComponent<any> {
  public componentWillMount() {
    this.props.logout();
  }

  public render() {
    const loginUrl = process.env.REACT_APP_PING_LOGIN_URL;
    return (
      <BB8MainPage.Padded>
        <Grid
          container={true}
          direction="column"
          alignContent="center"
          justify="space-around"
          spacing={8}
          style={{ paddingTop: "25px" }}
        >
          <Grid item={true}>
            <h2>You have successfully logged out.</h2>
          </Grid>
          <Grid item={true}>
            <p className="text-center">
              Didn't mean to logout? <a href={loginUrl}>Login again.</a>
            </p>
          </Grid>
        </Grid>
      </BB8MainPage.Padded>
    );
  }
}

export default connect<any, any, any, any>(
  () => {},
  (dispatch: Dispatch) => withAuthDispatchToProps(dispatch),
  undefined
)(LoggedOut);
