import { Grid } from "@material-ui/core";
import React from "react";
import { connect } from "react-redux";
import { BB8MainPage } from "../../../BB8";
import { IRootState, withAuthStateToProps } from "../../../store";
import AccessDeniedAnimation from "./AccessDeniedAnimation.gif";

const AccessDenied = (props: any) => {
  return (
    <BB8MainPage.Padded>
      <Grid container={true} direction="column" alignContent="center">
        <h2>Sorry, you haven't been granted access yet.</h2>
        <img
          src={AccessDeniedAnimation}
          style={{
            width: "fit-content",
            maxWidth: "250px",
            margin: "4em auto 4em"
          }}
        />
        <p className="text-center">
          If you require access please email us at{" "}
          <a href="<EMAIL>">
            <EMAIL>
          </a>
        </p>
      </Grid>
    </BB8MainPage.Padded>
  );
};

const mapStateToProps = (state: IRootState) => withAuthStateToProps(state);

export default connect(
  mapStateToProps,
  null
)(AccessDenied);
