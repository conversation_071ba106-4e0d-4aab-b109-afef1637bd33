import { createStyles, FormHelperText, Grid, Theme, Tooltip, withStyles, WithStyles } from "@material-ui/core";
import Uppy from "@uppy/core";
import { Formik, FormikProps } from "formik";
import JSZip from "jszip";
import _, { chain, find, get, includes, isEmpty, toPairs, unset } from "lodash";
import React, { useEffect } from "react";
import { connect } from "react-redux";
import { Action } from "redux";
import { ThunkDispatch } from "redux-thunk";
import { BB8Form, BB8FormField } from "../../../BB8";
import FileUploader from "../../../components/FileUploader";
import { PartnerLookup } from "../../../components/form/fields/PartnerLookup";
import { IBulkJob } from "../../../models/BulkJob";
import { IOfferCategory } from "../../../models/Category";
import { IPartner } from "../../../models/Partner";
import { IOfferPromotion } from "../../../models/Promotion";
import { REGIONS } from "../../../models/Region";
import { onlyNonEmptyValidURL } from "../../../shared/helpers";
import { csvToSchema } from "../../../shared/helpers/csvJsonConverter";
import { IRootState } from "../../../store";
import {
  IMechanismObject,
  IOfferFormModel,
  IValidationErrors,
  Region,
  ValidationSchemas,
  Validator
} from "../../../validation/validator";
import { fetchCategoriesAsync, fetchPromotionsAsync } from "../actions";
import { getImages } from "../pages/Bulk";
import { getCSVFile, getZIPFile } from "../utils";
import { ReactComponent as Help } from "./baseline-help_outline-24px.svg";
import { FEATURE_FLAG, featureFlagCompare } from "./FeatureFlag";

type ValidationError = { [k in keyof IBulkFormValues]?: string | undefined };

export interface IBulkFormValues {
  name: string;
  partner: IPartner;
  files: Uppy.UppyFile[];
}

type onValidationErrorType = (
  values: IBulkFormValues,
  errorObjects?: Array<{
    data: IOfferFormModel;
    validationResults: IValidationErrors[];
  }>,
  errorCount?: number
) => any;

type onValidationSuccessType = (
  values: IBulkFormValues,
  csv?: IOfferFormModel[]
) => any;
export interface IBulkFormProps {
  partners: IPartner[];
  preselectedPartner?: IPartner;
  initialValues?: Partial<IBulkFormValues>;
  onValidationError: onValidationErrorType;
  onValidationSuccess: onValidationSuccessType;
  existingBulkJob?: IBulkJob;
}

export interface IAllowedValues {
  categories: IOfferCategory[];
  promotions: IOfferPromotion[];
}

const defaultValidationError: ValidationError = {};
function validatePartner(values: IBulkFormValues): string | undefined {
  if (isEmpty(values.partner)) {
    return "Please select a partner";
  }
}

function validateBulkName(values: IBulkFormValues): string | undefined {
  if (isEmpty(values.name)) {
    return "Please name your upload";
  }
}

function assignAccurateDateType(offerKeyDate: string | Date): string | Date {
  return offerKeyDate instanceof Date
    ? (offerKeyDate as Date).toISOString()
    : offerKeyDate;
}

function validateCSVFile(
  values: IBulkFormValues,
  csvFile: Uppy.UppyFile<{}>,
  onValidationSuccess: onValidationSuccessType,
  onValidationError: onValidationErrorType,
  errors = defaultValidationError,
  allowedValues: IAllowedValues,
  zipContent?: JSZip | PromiseLike<JSZip>,
  existingBulkJob?: IBulkJob
) {
  return new Promise((resolve, reject) => {
    const fr = new FileReader();
    fr.onloadend = evt => {
      if (evt && evt.target && fr.readyState === FileReader.DONE) {
        // DONE == 2
        if (fr.result) {
          const schemaData: IOfferFormModel[] = csvToSchema<IOfferFormModel>(
            fr.result.toString()
          );

          // check whether all images are URL's or mixed or images names and URL's
          const imagePaths = chain(schemaData)
            .map(getImages)
            .flatten()
            .compact()
            .value();

          const containsUrlOnly: boolean = chain(imagePaths)
            .every(onlyNonEmptyValidURL)
            .value();

          const schemaDataPartner = schemaData.map(offer => {
            let hasValidRegions: boolean = true;
            let missingEnglishImagePath: boolean = false;
            let missingFrenchImagePath: boolean = false;
            let bulkJobMissingOfferId: boolean = false;

            if (existingBulkJob && existingBulkJob.offers) {
              const result = find(existingBulkJob.offers, { id: offer.id });
              if (result === undefined) {
                bulkJobMissingOfferId = true;
              }
            }

            // If they dont pass in regions, we will manually go and set the regions that are tied to the partner
            const partnerRegions: string[] = values.partner.regions
              ? values.partner.regions.map(region => region.toUpperCase())
              : [];
            if (_.isEmpty(offer.regions) && !_.isEmpty(partnerRegions)) {
              offer.regions =
                partnerRegions.indexOf("ALL") >= 0
                  ? REGIONS.map(({ value }) => value as Region)
                  : partnerRegions.map((region: string) => region as Region);
            }
            // If they did pass in regions, we have to make sure the regions they selected are a subset of the partnerRegions
            else if (!_.isEmpty(offer.regions) && !_.isEmpty(partnerRegions)) {
              hasValidRegions =
                partnerRegions.indexOf("ALL") >= 0 ||
                offer.regions.every((region: string) => {
                  return partnerRegions.includes(region);
                });
            }
            // All partners SHOULD have regions attached to them, but in case, we need to check against that
            else if (_.isEmpty(partnerRegions)) {
              hasValidRegions = false;
              console.error(
                "This partner does not have any regions. This should not occur. Contact Support to inquire."
              );
            }
            if (!isEmpty(zipContent) && zipContent instanceof JSZip) {
              const imageNamesInZipFile: string[] = Object.keys(
                zipContent.files
              );
              const imagesLowerCaseNamesToZipNames: {
                [key: string]: string;
              } = {};

              imageNamesInZipFile.forEach(
                imageName =>
                  (imagesLowerCaseNamesToZipNames[
                    imageName.toLowerCase()
                  ] = imageName)
              );
              let offerImageNameEnglish = get(offer, "image.en-US.path");
              let offerImageNameFrench = get(offer, "image.fr-CA.path");

              if (
                offerImageNameEnglish &&
                Object.keys(imagesLowerCaseNamesToZipNames).includes(
                  offerImageNameEnglish.toLowerCase()
                ) &&
                offer.image &&
                offer.image["en-US"] &&
                offer.image["en-US"].path
              ) {
                offer.image["en-US"].path =
                  imagesLowerCaseNamesToZipNames[
                    offerImageNameEnglish.toLowerCase()
                  ];
                offerImageNameEnglish = offer.image["en-US"].path;
              }

              if (
                offerImageNameFrench &&
                Object.keys(imagesLowerCaseNamesToZipNames).includes(
                  offerImageNameFrench.toLowerCase()
                ) &&
                offer.image &&
                offer.image["fr-CA"] &&
                offer.image["fr-CA"].path
              ) {
                offer.image["fr-CA"].path =
                  imagesLowerCaseNamesToZipNames[
                    offerImageNameFrench.toLowerCase()
                  ];
                offerImageNameFrench = offer.image["fr-CA"].path;
              }

              const enImageIsAURL = onlyNonEmptyValidURL(offerImageNameEnglish);
              const frImageIsAURL = onlyNonEmptyValidURL(offerImageNameFrench);

              if (offerImageNameEnglish) {
                if (
                  !enImageIsAURL &&
                  !includes(imageNamesInZipFile, offerImageNameEnglish)
                ) {
                  missingEnglishImagePath = true;
                }
              }
              if (offerImageNameFrench) {
                if (
                  !frImageIsAURL &&
                  !includes(imageNamesInZipFile, offerImageNameFrench)
                ) {
                  missingFrenchImagePath = true;
                }
              }
            }
            return {
              partnerId: values.partner.id,
              partnerName: values.partner.name,
              ...offer,
              startDate: assignAccurateDateType(offer.startDate),
              endDate: assignAccurateDateType(offer.endDate),
              displayDate: assignAccurateDateType(offer.displayDate),
              // We add the below props, which are outside of the OfferForm object, to do custom validation checking for partner regions
              hasValidRegions,
              acceptableRegions: hasValidRegions
                ? undefined
                : partnerRegions.map(
                    (region: string) => region.toUpperCase() as Region
                  ),
              missingEnglishImagePath,
              missingFrenchImagePath,
              bulkJobMissingOfferId
            };
          });

          const { errorCount, errorObjects } = performSchemaValidation(
            schemaDataPartner,
            allowedValues.promotions,
            allowedValues.categories,
            existingBulkJob
          );

          const handleValidationErrors = () => {
            if (errorCount) {
              errors.files = `We encountered some errors in ${errorCount} rows adding your offers to ${get(
                values,
                "partner.name"
              )}`;
              onValidationError(values, errorObjects, errorCount);
              return reject(errors);
            } else {
              return resolve(onValidationSuccess(values, schemaDataPartner));
            }
          };

          // go ahead and process offers
          if (containsUrlOnly) {
            return handleValidationErrors();
          } else {
            // check if there's a zip file
            if (!isEmpty(zipContent)) {
              if (zipContent instanceof JSZip) {
                const imagesInZipArray: string[] = Object.keys(
                  zipContent.files
                );
                const imagesNamesInZipLowerCase: string[] = imagesInZipArray.map(
                  name => name.toLowerCase()
                );

                const imagesNamesPlusLowerCases: string[] = [
                  ...imagesInZipArray,
                  ...imagesNamesInZipLowerCase
                ];
                const containsPhysicalImages = chain(imagePaths)
                  .filter(im => !onlyNonEmptyValidURL(im))
                  .every(image =>
                    imagesNamesPlusLowerCases.includes(image.toLowerCase())
                  )
                  .value();
                if (containsPhysicalImages) {
                  return handleValidationErrors();
                } else {
                  errors.files = `We analyzed your csv and some images are missing from the zip file. Please re-upload a zip containing the missing images`;
                  onValidationError(values, errorObjects, errorCount);
                  return reject(errors);
                }
              } else {
                errors.files = `Something went wrong while trying to read your zip file`;
                return reject(errors);
              }
            } else {
              errors.files = `We analyzed your csv and encountered some physical file paths. Please upload a .zip file with images to resume processing.`;
              return reject(errors);
            }
          }
        }
      }
    };
    fr.onerror = evt => {
      if (evt && evt.target && fr.readyState === FileReader.DONE) {
        reject(fr.error);
      }
    };
    fr.readAsText(csvFile.data);
  });
}

function performSchemaValidation(
  schemaDataPartner: any[],
  promotions: IOfferPromotion[],
  categories: IOfferCategory[],
  existingBulkJob?: IBulkJob
) {
  const validator = new Validator();
  let errorCount = 0;
  const errorObjects = schemaDataPartner.map(data => {
    const originalData = Object.assign({}, data);
    // Remove the extra custom validations we need to for checking partner regions
    unset(data, "hasValidRegions");
    unset(data, "acceptableRegions");
    unset(data, "missingEnglishImagePath");
    unset(data, "missingFrenchImagePath");
    unset(data, "bulkJobMissingOfferId");

    const schemaToUse = existingBulkJob
      ? ValidationSchemas.PutOfferFormObject
      : ValidationSchemas.PostOfferFormObject;

     // Transform cardType on array before validation
     if (data.cardType && typeof data.cardType === "string") {
            data.cardType = data.cardType.split("|");
     } else if (!Array.isArray(data.cardType)) {
            data.cardType = [];
     }

    const validationResults = validator.validate(data, schemaToUse);

    // If we are using the post offer form object but the data has an id in it then push an error
    if (
      originalData.id &&
      schemaToUse === ValidationSchemas.PostOfferFormObject
    ) {
      validationResults.push(
        validator.generateCustomValidationError(
          "additionalProperty",
          "id",
          "Offer id field is not allowed when creating new offers.",
          get(data, "id")
        )
      );
    }

    const promotionErrors = validator.getPromotionErrors(data, promotions);
    if (promotionErrors) {
      validationResults.push(...promotionErrors);
    }

    if (process.env.REACT_APP_CATEGORY === "true") {
      const categoryErrors = validator.getCategoryErrors(data, categories);
      if (categoryErrors) {
        validationResults.push(...categoryErrors);
      }
    }

    const mechanisms: IMechanismObject[] = get(data, "mechanisms");
    const programType : string = data.programType;
    const programTypeError = validator.getProgramTypeErrors(mechanisms, programType);
    if(programTypeError){
        validationResults.push(...programTypeError);
    }
    if(programType === "cardlinked"){
        const cardTypeEmptyError = validator.getCardTypeEmptyErrors(data.cardType);
        if(cardTypeEmptyError){
            validationResults.push(...cardTypeEmptyError);
        }
        const cardTypeValidValuesError = validator.getCardTypeValidValuesErrors(data.cardType,programType);
        if(cardTypeValidValuesError){
            validationResults.push(...cardTypeValidValuesError);
        }
    }
    const barcodeErrors = validator.getBarcodeErrors(mechanisms);
    if (barcodeErrors) {
      validationResults.push(...barcodeErrors);
    }
    if (originalData.bulkJobMissingOfferId) {
      validationResults.push(
        validator.generateCustomValidationError(
          "offerId",
          ".id",
          "The offer id was not found for the bulk job.",
          get(data, "id")
        )
      );
    }

    const dateErrors = validator.getDateErrors(data.startDate, data.endDate, data.displayDate)
    if (dateErrors) {
      validationResults.push(...dateErrors);
    }

    const productSKUErrors = validator.getProductSKUErrors(data.tiers)
    if (productSKUErrors) {
      validationResults.push(...productSKUErrors);
    }

    if (!originalData.hasValidRegions) {
      validationResults.push(
        validator.generateCustomValidationError(
          "regions",
          ".regions",
          "selected regions should be a subset of allowed partner regions",
          data.regions,
          JSON.stringify(originalData.acceptableRegions)
        )
      );
    }
    if (originalData.missingEnglishImagePath) {
      validationResults.push(
        validator.generateCustomValidationError(
          "image",
          ".image.en-US.path",
          "An image is missing from the zip file. Please re-upload a zip containing the missing image.",
          get(data, "image.en-US.path")
        )
      );
    }
    if (originalData.missingFrenchImagePath) {
      validationResults.push(
        validator.generateCustomValidationError(
          "image",
          ".image.fr-CA.path",
          "An image is missing from the zip file. Please re-upload a zip containing the missing image.",
          get(data, "image.fr-CA.path")
        )
      );
    }
    if(featureFlagCompare(FEATURE_FLAG.OFFER_ISSUER_DROPDOWN, "true")){
      const issuanceSponsorErrors = validator.getIssuanceSponsorErrors(data,true)
      if (issuanceSponsorErrors) {
        validationResults.push(...issuanceSponsorErrors);
      }
    }
    if (validationResults && validationResults.length) {
      errorCount++;
    }
    return {
      data,
      validationResults
    };
  });
  return { errorCount, errorObjects };
}

function readZIPFile(
  zipFile: Uppy.UppyFile<{}>
): Promise<JSZip | PromiseLike<JSZip> | undefined> {
  // open csv, check for images, store in state
  return new Promise((resolve, reject) => {
    const fr = new FileReader();
    fr.onloadend = evt => {
      if (evt && evt.target && fr.readyState === FileReader.DONE) {
        // DONE == 2
        if (fr.result) {
          JSZip.loadAsync(fr.result).then(value => {
            resolve(value);
          });
        }
      }
    };
    fr.onerror = evt => {
      if (evt && evt.target && fr.readyState === FileReader.DONE) {
        reject(fr.error);
      }
    };
    fr.readAsArrayBuffer(zipFile.data);
  });
}

const validateFiles = (
  values: IBulkFormValues,
  onValidationSuccess: onValidationSuccessType,
  onValidationError: onValidationErrorType,
  errors = defaultValidationError,
  allowedValues: IAllowedValues,
  existingBulkJob?: IBulkJob
): Promise<any> | ValidationError => {
  if (values.files && values.files.length) {
    // check for csv
    const csvFile = getCSVFile(values);

    // check for zip
    const zipFile = getZIPFile(values);

    if (csvFile && zipFile) {
      return readZIPFile(zipFile).then(zipContent =>
        validateCSVFile(
          values,
          csvFile,
          onValidationSuccess,
          onValidationError,
          errors,
          allowedValues,
          zipContent,
          existingBulkJob
        )
      );
    } else if (csvFile) {
      return validateCSVFile(
        values,
        csvFile,
        onValidationSuccess,
        onValidationError,
        errors,
        allowedValues,
        undefined,
        existingBulkJob
      );
    } else if (zipFile) {
      errors.files = "Please drop the .csv file containing your offers";
    } else {
      errors.files = "At least one file must be .csv";
    }
  }
  return errors;
};

type validateFormType = (
  allowedValues: IAllowedValues,
  onValidationSuccess: onValidationSuccessType,
  onValidationError: onValidationErrorType,
  existingBulkJob?: IBulkJob
) => (values: IBulkFormValues) => any;

const validateForm: validateFormType = (
  allowedValues,
  onValidationSuccess,
  onValidationError,
  existingBulkJob?
) => {
  return (values: IBulkFormValues) => {
    const errors = {
      ...defaultValidationError,
      name: validateBulkName(values),
      partner: validatePartner(values)
    };

    if (!isEmpty(errors.name) || !isEmpty(errors.partner)) {
      onValidationError(values);
      return errors;
    }

    return validateFiles(
      values,
      onValidationSuccess,
      onValidationError,
      errors,
      allowedValues,
      existingBulkJob
    );
  };
};

function validateFileBeforeAdd(
  currentFile: Uppy.UppyFile,
  files: { [key: string]: Uppy.UppyFile }
): Uppy.UppyFile | boolean | undefined {
  const addedFiles = toPairs(files);

  if (addedFiles.length === 0) {
    return currentFile;
  }

  for (const kv of addedFiles) {
    const [, addedFile] = kv;
    if (addedFile.extension.toLowerCase() === "csv") {
      return (
        currentFile.data.type !== "text/csv" &&
        currentFile.extension !== "application/csv" &&
        currentFile.name.indexOf(".csv") < 0
      );
    }
    if (addedFile.extension.toLowerCase() === "zip") {
      return currentFile.data.type !== "application/zip";
    }
  }

  return currentFile;
}

export type BulkUploadFormProps = IBulkFormProps;

type BulkUploadFormProps2 = {
  partners: IPartner[];
  categories: IOfferCategory[];
  promotions: IOfferPromotion[];
  fetchCategories: () => void;
  fetchPromotions: () => void;
} & WithStyles &
  BulkUploadFormProps;

export const BulkUploadForm: React.FunctionComponent<
  BulkUploadFormProps2
> = React.forwardRef<FileUploader, BulkUploadFormProps2>((props, ref) => {
  useEffect(() => {
    props.fetchCategories();
    props.fetchPromotions();
  }, []);
  const render = (renderProps: FormikProps<IBulkFormValues>) => (
    <BB8Form className={props.classes.form}>
      <PartnerLookup
        name="partnerId"
        partners={props.partners}
        placeholder="Select Partner"
        value={!_.isEmpty(renderProps.values.partner) ? renderProps.values.partner.id : undefined}
        onSelect={(selectedPartner?: IPartner) =>
          renderProps.setFieldValue("partner", selectedPartner)
        }
        disabled={
          Boolean(props.existingBulkJob) ||
          (Boolean(renderProps.values.partner) &&
            Boolean(renderProps.values.name))
        }
      />
      <BB8FormField
        id="name"
        name="name"
        placeholder="e.g: ACBD"
        label="Name your upload file"
        value={
          props.existingBulkJob
            ? props.existingBulkJob.name
            : renderProps.values.name
        }
        error={renderProps.touched.name && renderProps.errors.name}
        variant="filled"
        fullWidth={true}
        disabled={
          Boolean(
            renderProps.values.files && renderProps.values.files.length
          ) ||
          isEmpty(renderProps.values.partner) ||
          !isEmpty(props.existingBulkJob)
        }
      />
      {((!props.existingBulkJob &&
        renderProps.values.partner &&
        renderProps.values.name) ||
        props.existingBulkJob) && (
        <Grid
          container={true}
          direction="row"
          alignItems="flex-start"
          justify="center"
          className={props.classes.fileUpload}
        >
          <Grid container={true} item={true} justify="center">
            <FileUploader
              onBeforeFileAdded={validateFileBeforeAdd}
              onFileAdded={files => renderProps.setFieldValue("files", files)}
              onFileRemoved={files => renderProps.setFieldValue("files", files)}
              onCancelAll={() =>
                renderProps.setFieldValue("files", null, false)
              }
              hideUploadButton={true}
              uppyConfig={{
                autoProceed: true,
                restrictions: {
                  allowedFileTypes: [".zip", ".csv"],
                  maxFileSize: 10e9,
                  maxNumberOfFiles: 2,
                  minNumberOfFiles: 1
                }
              }}
              width={250}
              height={182}
              inline={true}
              locale={{
                strings: {
                  dropPaste:
                    "Drop one .csv file and optionally a .zip file containing images to start uploading"
                }
              }}
              ref={ref}
            />
            <Tooltip
              className={props.classes.tooltip}
              title={
                <ul>
                  <li>A .csv file with offers to be submitted is required</li>
                  <li>
                    Optionally, a .zip file containing images referenced using
                    relative path in your csv file can be paired with the .csv
                    file
                  </li>
                </ul>
              }
            >
              <Help />
            </Tooltip>
          </Grid>
          {renderProps.errors && renderProps.errors.files && (
            <FormHelperText error={true}>
              {renderProps.errors.files}
            </FormHelperText>
          )}
        </Grid>
      )}
    </BB8Form>
  );

  if (!props.partners || props.partners.length === 0) {
    return <div className={props.classes.loading}>Loading Partners...</div>;
  }

  const initValues: { partner?: IPartner; name?: string } = {};

  initValues.partner = props.preselectedPartner;
  initValues.name = get(props, "existingBulkJob.name");

  return (
    <Formik
      initialValues={initValues as IBulkFormValues}
      onSubmit={() => {}}
      validate={validateForm(
        {
          categories: props.categories,
          promotions: props.promotions
        },
        props.onValidationSuccess,
        props.onValidationError,
        props.existingBulkJob
      )}
      validateOnBlur={false}
      render={render}
    />
  );
});

const formStyles = createStyles((theme: Theme) => ({
  fileUpload: {
    margin: `${theme.spacing(6)}px 0`
  },
  form: {
    marginTop: theme.spacing(2)
  },
  tooltip: {
    transform: "translate(25%,-50%)"
  },
  loading: {
    opacity: ".5",
    fontSize: ".7em",
    marginTop: theme.spacing(6),
    animation: "fadeIn 1s infinite alternate"
  }
}));

const mapStateToProps = (state: IRootState) => ({
  categories: state.offers.categories || [],
  promotions: state.offers.promotions || []
});

const mapDispatchToProps = (
  dispatch: ThunkDispatch<IRootState, void, Action>
) => ({
  fetchCategories: () => dispatch(fetchCategoriesAsync()),
  fetchPromotions: () => dispatch(fetchPromotionsAsync())
});

export const StyledBulkUploadForm = withStyles(formStyles)(
  connect(
    mapStateToProps,
    mapDispatchToProps,
    null,
    { forwardRef: true }
  )(BulkUploadForm)
);

export default StyledBulkUploadForm;
