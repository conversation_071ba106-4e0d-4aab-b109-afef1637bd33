import { createStyles, Fab, Grid, Paper, Typography, withStyles, WithStyles } from "@material-ui/core";

import AddIcon from "@material-ui/icons/Add";
import RemoveIcon from "@material-ui/icons/Remove";
import { FieldArray, FieldArrayRenderProps, FormikProps } from "formik";
import { get } from "lodash";
import React, { useContext, useEffect, useState } from "react";
import { FIELDS_CONFIG } from "../../../components/form/configs/fields.config";
import CustomLocalizationFieldArray from "../../../components/form/fields/CustomLocalizationFieldArray";
import CustomLocalizationTextField from "../../../components/form/fields/CustomLocalizationTextField";
import CustomOptionalLocalizationField from "../../../components/form/fields/CustomOptionalLocalizationField";
import CustomRegionComponent from "../../../components/form/fields/CustomRegionComponent";
import { CustomStyledDateTimeWidget } from "../../../components/form/widgets/CustomDateTimeWidget";
import CustomLookupWidget from "../../../components/form/widgets/CustomLookupWidget";
import CustomMechanismWidget from "../../../components/form/widgets/CustomMechanismWidget";
import { CustomSelectWidget } from "../../../components/form/widgets/CustomSelectWidget";
import CustomTextWidget from "../../../components/form/widgets/CustomTextWidget";
import { DISPLAY_PRIORITIES } from "../../../models/DisplayPriority";
import { IIssuance } from "../../../models/Issuance";
import { IPartner } from "../../../models/Partner";
import { ISponsor } from "../../../models/Sponsor";

import { generateRandomGUIDForCacheInvalidation } from "../../../shared/helpers";
import { IOfferFormModel, MechanismType, OfferStatus } from "../../../validation/validator";
import { UserService } from "../../administration/services";
import { SnackbarContext } from "../../communication/AlertContainer";
import Concealable from "./Concealable";
import { FEATURE_FLAG, FeatureFlag } from "./FeatureFlag";
import OfferSubmissionFormPanel from "./OfferSubmissionFormPanel";
import OfferSubmissionPromotions from "./OfferSubmissionPromotions";
import { OfferSubmissionSubSectionTitle } from "./OfferSubmissionSubSectionTitle";

const styles = createStyles(() => ({
    subsectionHolder: {
        paddingTop: "1rem"
    },
    buttonTextPadding: {
        paddingLeft: "1rem",
        paddingRight: "1rem"
    }
}));

interface IOfferSubmissionOfferSettingsSectionProps extends WithStyles {
    disabled?: boolean;
    schema: any;
    formikProps: FormikProps<IOfferFormModel>;
    partners?: IPartner[];
}

const OfferSubmissionOfferSettingsSection: React.FunctionComponent<IOfferSubmissionOfferSettingsSectionProps> = (props) => {
    const { classes,
        disabled,
        schema,
        formikProps,
        partners } = props;

    const sponsorCodes = formikProps.values.activeSponsorCodesForSelectedPartnerId;
    const selectedSponsorCode = formikProps.values.sponsorCode;

    const programType = formikProps.values.programType;
    const selectedTags = formikProps.values.tags || [];
    const isBoostOfferTagSelected = selectedTags.includes("5d407839-7138-48d7-811f-557feccbe5b3")

    const [audience, setAudience] = useState(formikProps.values.audience);
    const [key, setKeyS] = useState(generateRandomGUIDForCacheInvalidation());
    const [issuanceCodeOptions, setIssuanceCodeOptions] = useState<IIssuance[]>([]);

    const alertContext = useContext(SnackbarContext);

    useEffect(() => {
        setAudience(formikProps.values.audience);
    }, [formikProps.values.audience]);

    useEffect(() => {
        if (isMechanismNoActionOptIn()) {
            formikProps.setFieldValue("ctaLabel", formikProps.initialValues.ctaLabel || undefined);
            formikProps.setFieldValue("ctaUrl", formikProps.initialValues.ctaUrl || undefined);
        } else {
            formikProps.setFieldValue("ctaLabel", undefined);
            formikProps.setFieldValue("ctaUrl", undefined);
        }
    }, [formikProps.values.mechanisms]);

    useEffect(() => {
        const {ctaLabel, ctaUrl} = formikProps.values;

        const hasCtaLabel = ctaLabel && (ctaLabel["en-US"] || ctaLabel["fr-CA"]);
        const hasCtaUrl = ctaUrl && (ctaUrl["en-US"] || ctaUrl["fr-CA"]);

        if (!hasCtaLabel && !hasCtaUrl) {
            formikProps.setFieldValue("ctaLabel", undefined);
            formikProps.setFieldValue("ctaUrl", undefined);
        } else {
            formikProps.setFieldValue("ctaLabel", ctaLabel ? ctaLabel : {});
            formikProps.setFieldValue("ctaUrl", ctaUrl ? ctaUrl : {});
        }
    }, [formikProps.values.ctaLabel, formikProps.values.ctaUrl]);

    const handleIssuanceCodeChange = (setFieldValue: any, selectedIssuanceOfferCode?: IIssuance) => {
        setFieldValue("issuanceCode", selectedIssuanceOfferCode ? selectedIssuanceOfferCode.offerCode : undefined, true);
    }

    const isPublishedDisabledOrUpdatedOffer = (status: OfferStatus | undefined) => {
        return status && status !== OfferStatus.Draft.toUpperCase();
    }

    const arrayItems = (arrayHelpers: FieldArrayRenderProps) => {
        const maxMechanisms: number = get(schema, "properties.mechanisms.maxItems");

        const items = arrayHelpers.form.values[arrayHelpers.name];

        if (items && items.length > 0) {
            return items.map((item: any, index: number) => {
                const mechanismTypeError =
                    get(formikProps.errors, `mechanisms[${index}].mechanismType`) ||
                    get(formikProps.errors, `mechanisms[${index}].mechanismValue.en-US`);

                return (
                    <Grid
                        id={`mechanisms[${index}]`}
                        item={true}
                        xs={12}
                        key={key + index}
                    >
                        <Grid item={true} xs={12}>
                            <CustomMechanismWidget
                                formikProps={formikProps}
                                mechanismIndex={index}
                                key={key + index}
                                partners={partners}
                                disabled={isPublishedDisabledOrUpdatedOffer(formikProps.values.status)}
                            />
                        </Grid>
                        <FeatureFlag featureName={FEATURE_FLAG.MULTI_MECHANIC}>
                            <Grid
                                item={true}
                                container={true}
                                xs={12}
                                justify="flex-end"
                                alignItems="center"
                            >
                                {get(item, `mechanismType`) !== MechanismType.NoAction &&
                                    get(item, `mechanismType`) !== MechanismType.OptIn &&
                                    !mechanismTypeError &&
                                    items.length < maxMechanisms &&
                                    items.length - 1 === index && (
                                        <>
                                            <Fab
                                                onClick={() =>
                                                    arrayHelpers.push({
                                                        mechanismType: MechanismType.NoAction
                                                    })
                                                }
                                                size="small"
                                                color="primary"
                                                type="button"
                                            >
                                                <AddIcon/>
                                            </Fab>
                                            <Typography
                                                className={classes.buttonTextPadding}
                                                variant="button"
                                                color="primary"
                                            >
                                                Add Mechanism
                                            </Typography>
                                        </>
                                    )}
                                {items.length > 1 && (
                                    <>
                                        <Fab
                                            size="small"
                                            color="primary"
                                            type="button"
                                            onClick={() => {
                                                arrayHelpers.remove(index);
                                                setKey();
                                            }}
                                        >
                                            <RemoveIcon/>
                                        </Fab>

                                        <Typography
                                            className={classes.buttonTextPadding}
                                            variant="button"
                                            color="primary"
                                        >
                                            Remove Mechanism
                                        </Typography>
                                    </>
                                )}
                            </Grid>
                        </FeatureFlag>
                    </Grid>
                );
            });
        }
    };

    const setKey = () => {
        setKeyS(generateRandomGUIDForCacheInvalidation())
    };

    const isEventBasedOffer = (audience: String) => {
        return (audience && audience === "event") || formikProps.values.eventBasedOffer;
    }

    const isAMReceiptsOffer = (programtype: String) => {
        if(programtype === "amreceipts"){
            return true;
        }
        return false;
    }

    const areSponsorAndIssuanceCodeFieldsShown = (mechanism: String, awardType: String, offerType: String, audience: String, internalAirmilesCalculated: Boolean) => {
        if (offerType === "amCashDiscount" || awardType === "flatDiscount" || awardType === "percentDiscount") {
            return false;
        } else if (awardType === "custom") {
            return true;
        }

        return true;
    }

    const toOptions = (values: any[]) => {
        return values.map(
            ({name, value}: { name: string; value: string | number }) => ({
                label: name,
                value
            })
        );
    };

    const handleDisplayPriorityChange = (
        event: React.ChangeEvent<HTMLSelectElement>
    ) => {
        formikProps.setFieldValue(
            "displayPriority",
            parseInt(event.target.value),
            true
        );
    };

    const handleSponsorCodeChange = (
        setFieldValue: any,
        selectedSponsorCode?: ISponsor
    ) => {
        const sponsorCode = selectedSponsorCode ? selectedSponsorCode.issuerCode : undefined;
        setFieldValue("sponsorCode", sponsorCode, true);

        // Since issuance code depends on sponsor code, on sponsor code change clear issuance code field
        setIssuanceCodeOptions([])

        //Retrieve Issuance-Offer-Codes for the selected Sponsor-Code
        if (sponsorCode) {
            const reponseIssuanceOfferCodes = UserService.getIssuanceOfferCodes(sponsorCode).catch(error => {
                console.error(error);
                alertContext.displayAlert({
                    message: "Error while trying to fetch issuance offer codes",
                    variant: "error"
                });
            })

            Promise.all([reponseIssuanceOfferCodes])
                .then(values => {
                    const [issuanceCodeOptions] = values;

                    let issuanceCode: IIssuance[] = [];
                    if (issuanceCodeOptions && issuanceCodeOptions.length > 0) {
                        issuanceCode = issuanceCodeOptions.filter((data: IIssuance) => data.offerCode.length <= 8);
                        setIssuanceCodeOptions(issuanceCode)
                    }
                }).catch(error => {
                    console.error(error);
                    alertContext.displayAlert({
                        message: "Error while trying to fetch issuance offer codes",
                        variant: "error"
                    });
                })
        } else {
            setIssuanceCodeOptions([])
        }
    };

    const isMechanismNoActionOptIn = () => {
        const selectedMechanism = get(
            formikProps.values,
            `mechanisms[0].mechanismType`
        );

        if (selectedMechanism) {
            return selectedMechanism.includes(MechanismType.NoAction) || selectedMechanism.includes(MechanismType.OptIn);
        }

        return false;
    };

    return (
        <Grid
            item={true}
            xs={12}
            style={{marginTop: "1rem", marginBottom: "1rem"}}
        >
            <Paper elevation={ 2 } style={ { padding: "1rem", width: "100%" } }>
                <Grid container={true}>
                    <Grid item={true} xs="auto">
                        <Typography
                            variant="h4"
                            component="h4"
                            paragraph={true}
                            gutterBottom={true}
                        >
                            Offer Settings
                        </Typography>
                    </Grid>
                    <Grid
                        container={true}
                        item={true}
                        xs={12}
                        direction="row"
                        style={{paddingBottom: "1rem"}}
                    >
                        <FeatureFlag featureName={FEATURE_FLAG.OFFER_ISSUER_DROPDOWN}>
                            <Grid container={true} spacing={3}>
                                {areSponsorAndIssuanceCodeFieldsShown(
                                        formikProps.values.mechanisms[0].mechanismType,
                                        formikProps.values.awardType,
                                        formikProps.values.offerType,
                                        formikProps.values.audience,
                                        formikProps.values.partnerInternalAirmilesCalculated ?
                                            formikProps.values.partnerInternalAirmilesCalculated : false
                                    ) && (
                                        <Grid item={true} xs={3}>
                                            <CustomLookupWidget
                                                name="sponsorCode"
                                                title={FIELDS_CONFIG.sponsorCode.title}
                                                placeholder="Select a sponsor code"
                                                options={sponsorCodes || []}
                                                onSelect={handleSponsorCodeChange}
                                                formikProps={formikProps}
                                                hasTooltip={true}
                                                disabled={isPublishedDisabledOrUpdatedOffer(formikProps.values.status)}
                                            />
                                        </Grid>
                                    )}
                                {areSponsorAndIssuanceCodeFieldsShown(
                                    formikProps.values.mechanisms[0].mechanismType,
                                    formikProps.values.awardType,
                                    formikProps.values.offerType,
                                    formikProps.values.audience,
                                    formikProps.values.partnerInternalAirmilesCalculated ?
                                        formikProps.values.partnerInternalAirmilesCalculated : false
                                    ) && (
                                        <Grid item={true} xs={3}>
                                            <CustomLookupWidget
                                                name="issuanceCode"
                                                title={FIELDS_CONFIG.issuanceCode.title}
                                                placeholder="Select an issuance code"
                                                options={issuanceCodeOptions || []}
                                                onSelect={handleIssuanceCodeChange}
                                                formikProps={formikProps}
                                                hasTooltip={true}
                                                disabled={isPublishedDisabledOrUpdatedOffer(formikProps.values.status)}
                                            />
                                        </Grid>
                                    )}
                            </Grid>
                        </FeatureFlag>
                        {process.env.REACT_APP_OFFER_ISSUER_DROPDOWN !== 'true' &&
                            areSponsorAndIssuanceCodeFieldsShown(
                                formikProps.values.mechanisms[0].mechanismType,
                                formikProps.values.awardType,
                                formikProps.values.offerType,
                                formikProps.values.audience,
                                formikProps.values.partnerInternalAirmilesCalculated ?
                                    formikProps.values.partnerInternalAirmilesCalculated : false
                            ) && (
                                <Grid item={true} xs={12}>
                                    <CustomTextWidget
                                        name="issuanceCode"
                                        label={FIELDS_CONFIG.issuanceCode.title}
                                        tooltip={FIELDS_CONFIG.issuanceCode.toolTip}
                                        disabled={isPublishedDisabledOrUpdatedOffer(formikProps.values.status)}
                                    />
                                </Grid>
                            )}
                        <Grid
                            item={true}
                            xs={12}
                            className={classes.subsectionHolder}
                            id="mechanisms"
                            style={{paddingBottom: "1rem"}}
                        >
                            <FieldArray name="mechanisms" render={arrayItems}/>
                        </Grid>
                        <Concealable displayCondition={ isMechanismNoActionOptIn() }>
                            <FeatureFlag featureName={FEATURE_FLAG.CTA_FIELDS}>
                                <OfferSubmissionFormPanel
                                    title={FIELDS_CONFIG.ctaLabel.title}
                                    tooltipText={FIELDS_CONFIG.ctaLabel.toolTip}
                                >
                                    <CustomLocalizationTextField
                                        name="ctaLabel"
                                        label={FIELDS_CONFIG.ctaLabel.title}
                                    />
                                </OfferSubmissionFormPanel>
                                <OfferSubmissionFormPanel
                                    title={FIELDS_CONFIG.ctaUrl.title}
                                    tooltipText={FIELDS_CONFIG.ctaUrl.toolTip}
                                >
                                    <CustomLocalizationTextField
                                        name="ctaUrl"
                                        label={FIELDS_CONFIG.ctaUrl.title}
                                    />
                                </OfferSubmissionFormPanel>
                            </FeatureFlag>
                        </Concealable>
                        <Concealable displayCondition={ !isEventBasedOffer(audience) }>
                            <Grid
                                container={true}
                                item={true}
                                xs={6}
                                spacing={1}
                                direction="column"
                            >
                                <Grid
                                    container={true}
                                    item={true}
                                    xs={7}
                                    alignItems="flex-start"
                                    alignContent="flex-start"
                                >
                                    <Typography
                                        variant="h5"
                                        component="h5"
                                        paragraph={true}
                                        gutterBottom={true}
                                    >
                                        Offer Date and Priority
                                    </Typography>
                                    <CustomStyledDateTimeWidget name="displayDate"/>
                                    <CustomStyledDateTimeWidget name="startDate"/>
                                    <CustomStyledDateTimeWidget name="endDate"/>
                                </Grid>
                            </Grid>
                        </Concealable>
                        <Concealable displayCondition={ isEventBasedOffer(audience) }>
                            <Grid
                                container={true}
                                item={true}
                                xs={6}
                                spacing={1}
                                direction="column"
                            >
                                <Grid
                                    container={true}
                                    item={true}
                                    xs={7}
                                    alignItems="flex-start"
                                    alignContent="flex-start"
                                >
                                    <Typography
                                        variant="h5"
                                        component="h5"
                                        paragraph={true}
                                        gutterBottom={true}
                                    >
                                        Offer Qualification Date
                                    </Typography>
                                    <CustomStyledDateTimeWidget name="firstQualificationDate"/>
                                    <CustomStyledDateTimeWidget name="lastQualificationDate"/>
                                </Grid>
                            </Grid>
                            <Grid item={true} xs={3}>
	                            <Grid item={true} xs={10}>
		                            <CustomTextWidget
			                            label="Collector's eligibility duration (days)"
			                            type="number"
			                            name="eligibilityDuration"
			                            tooltip={FIELDS_CONFIG.eligibilityDuration.toolTip}
		                            />
	                            </Grid>
                            </Grid>
                        </Concealable>
	                    <Concealable displayCondition={isAMReceiptsOffer(programType) && !isBoostOfferTagSelected}>
		                    <FeatureFlag featureName={FEATURE_FLAG.USAGE_LIMIT}>
			                    <Grid item={true} xs={3} spacing={4}>
				                    <Grid item={true} xs={10}>
					                    <CustomTextWidget
						                    label="Usage Limit Per Collector"
						                    type="number"
						                    name="usageLimit"
						                    tooltip={FIELDS_CONFIG.usageLimit.toolTip}
					                    />
				                    </Grid>
			                    </Grid>
		                    </FeatureFlag>
	                    </Concealable>
                        <OfferSubmissionPromotions formikProps={formikProps}/>
                        <Grid container={true} item={true} xs={12} direction="row">
                            <Grid item={true} xs={5}>
                                <CustomSelectWidget
                                    type="displayPriority"
                                    name="displayPriority"
                                    title="Display Priority"
                                    options={toOptions(DISPLAY_PRIORITIES)}
                                    onChange={handleDisplayPriorityChange}
                                    hasTooltip={true}
                                    optional={true}
                                />
                            </Grid>
                        </Grid>
                    </Grid>
                    <FeatureFlag featureName={FEATURE_FLAG.CASHIER_INSTRUCTIONS}>
                        <Grid
                            container={true}
                            item={true}
                            direction="column"
                            spacing={0}
                            className={classes.subsectionHolder}
                        >
                            <OfferSubmissionSubSectionTitle title="Cashier Instructions"/>
                            <Grid item={true}>
                                <CustomOptionalLocalizationField
                                    formikProps={formikProps}
                                    fieldPath="cashierInstruction"
                                    label="Cashier Instructions"
                                />
                            </Grid>
                        </Grid>
                    </FeatureFlag>
                    <Grid item={true} xs={12} className={classes.subsectionHolder}>
                        <CustomRegionComponent/>
                    </Grid>
                    <Grid
                        container={true}
                        item={true}
                        direction="column"
                        spacing={0}
                        className={classes.subsectionHolder}
                    >
                        <OfferSubmissionSubSectionTitle title="Location / Region Inclusions and Exclusions"/>
                        <Grid item={true}>
                            <CustomLocalizationFieldArray
                                name="excludedLocations"
                                label="Excluded Location"
                                optional={true}
                            />
                        </Grid>
                        <Grid item={true}>
                            <CustomLocalizationFieldArray
                                name="includedLocations"
                                label="Included Location"
                                optional={true}
                            />
                        </Grid>
                    </Grid>
                    <Grid
                        container={true}
                        item={true}
                        direction="column"
                        spacing={0}
                        className={classes.subsectionHolder}
                    >
                        <OfferSubmissionSubSectionTitle title="Banner Inclusions and Exclusions"/>
                        <Grid item={true}>
                            <CustomLocalizationFieldArray
                                name="excludedBanners"
                                label="Excluded Banner"
                                optional={true}
                            />
                        </Grid>
                        <Grid item={true}>
                            <CustomLocalizationFieldArray
                                name="includedBanners"
                                label="Included Banner"
                                optional={true}
                            />
                        </Grid>
                    </Grid>
                </Grid>
            </Paper>
        </Grid>
    );
}

export default withStyles(styles)(OfferSubmissionOfferSettingsSection);

