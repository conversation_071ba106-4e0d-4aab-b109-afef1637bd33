import React, { memo } from "react";

// FEATURE_FLAG names are first defined in .env
export enum FEATURE_FLAG {
  CASHIER_INSTRUCTIONS = "CASHIER_INSTRUCTIONS",
  MULTI_MECHANIC = "MULTI_MECHANIC",
  CATEGORY = "CATEGORY",
  DELETE_OFFERS = "DELETE_OFFERS",
  DISABLE_OFFER = "DISABLE_OFFER",
  MECHANISM_TITLE_TEXT = "MECHANISM_TITLE_TEXT",
  OFFER_ISSUER_DROPDOWN = "OFFER_ISSUER_DROPDOWN",
  EVENT_BASED_OFFER = "EVENT_BASED_OFFER",
  CARD_TYPE = "CARD_TYPE",
  CTA_FIELDS = "CTA_FIELDS",
  AM_RECEIPTS = "AM_RECEIPTS",
  RETAILER_GROUP = "RETAILER_GROUP",
  USAGE_LIMIT = "USAGE_LIMIT",
  NEW_OFFER_TEMPLATE = "NEW_OFFER_TEMPLATE"
}

interface IFeatureFlagProps {
  featureName: FEATURE_FLAG;
  children: any;
}

export const FeatureFlag: React.FunctionComponent<IFeatureFlagProps> = memo((props) => {
  const { featureName, children } = props;

  if (process.env["REACT_APP_" + featureName] === "false") {
    return <></>;
  }

  return children;
});

export const featureFlagCompare = (flag: FEATURE_FLAG, compareValue: String) => {
  if (process.env["REACT_APP_" + flag] === compareValue) {
    return true
  }
  return false
}
