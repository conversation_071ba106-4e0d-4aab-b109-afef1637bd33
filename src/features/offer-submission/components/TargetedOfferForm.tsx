import { createStyles, FormControl, FormLabel, Grid, TextField, withStyles } from "@material-ui/core";
import classNames from "classnames";
import { push } from "connected-react-router";
import { InlineDatePicker } from "material-ui-pickers";
import moment from "moment";
import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import { BB8Button } from "../../../BB8";
import { PartnerLookup } from "../../../components/form/fields/PartnerLookup";
import { ITargetedOfferFilter, TargetedOfferFilter } from "../../../models/TargetedOfferParams";
import { DATE_TIME_FORMAT } from "../utils";

const TargetedOfferForm = ({
  partners,
  classes,
  submitForm,
  initialState
}: {
  partners: any[];
  classes: any;
  submitForm: any;
  initialState?: ITargetedOfferFilter;
}) => {
  useEffect(() => {
    if (initialState) {
      setFilters(initialState);
    }
  }, [initialState]);
  const [filters, setFilters] = useState({
    partnerId: "",
    collectorNumber: "",
    date: moment().format(DATE_TIME_FORMAT)
  });

  return (
    <Grid container={true} className={classes.root} spacing={2}>
      <Grid item={true} xs={4}>
        <FormControl fullWidth={true}>
          <FormLabel component="label">Partner: </FormLabel>
          <PartnerLookup
            name="partnerId"
            partners={partners}
            placeholder="Select Partner"
            value={filters.partnerId}
            onSelect={partner => {
              setFilters({ ...filters, partnerId: partner ? partner.id : "" });
            }}
          />
        </FormControl>
      </Grid>
      <Grid item={true} xs={3}>
        <FormControl fullWidth={true}>
          <FormLabel component="label">Collector Number: </FormLabel>
          <TextField
            className={classNames(classes.textField)}
            variant="outlined"
            placeholder="Enter Collector Number"
            value={filters.collectorNumber}
            onChange={e =>
              setFilters({ ...filters, collectorNumber: e.target.value })
            }
          />
        </FormControl>
      </Grid>
      <Grid item={true} xs={3}>
        <FormControl fullWidth={true}>
          <FormLabel component="label">Date: </FormLabel>
          <InlineDatePicker
            name="date"
            onChange={date =>
              setFilters({
                ...filters,
                date: moment(date).format(DATE_TIME_FORMAT)
              })
            }
            value={filters.date}
            className={classNames(classes.textField)}
            clearable={true}
            keyboard={true}
            variant="outlined"
            format={DATE_TIME_FORMAT}
          />
        </FormControl>
      </Grid>
      <Grid item={true} xs={2} container={true}>
        <BB8Button
          className={classes.submitButton}
          onClick={() => submitForm(filters)}
          variant="contained"
          color="primary"
          disabled={!(filters.partnerId && filters.collectorNumber && filters.date)}
        >
          Get Targets
        </BB8Button>
      </Grid>
    </Grid>
  );
};

export const formFieldStyles = createStyles({
  root: {
    margin: "1em 0"
  },
  submitButton: {
    marginBottom: "9px",
    alignSelf: "flex-end"
  },
  datePickerError: {
    outline: "#D30E8B solid 3px",
    borderColor: "#f7e0ed",
    outlineOffset: "-3px",
    backgroundColor: "#f7e0ed"
  },
  textField: {
    "& > div": {
      width: "100%",
      margin: "0",
      background: "#f1f2f2",
      border: "0",
      borderRadius: "3px",
      color: "#231f20",
      "& fieldset": {
        border: 0
      }
    }
  }
});

const mapDispatchToProps = (dispatch: any) => ({
  submitForm: (filters: ITargetedOfferFilter) => {
    const request =
      filters instanceof TargetedOfferFilter
        ? filters
        : new TargetedOfferFilter(filters);

    dispatch(
      push({
        search: "?" + request.toQueryString()
      })
    );
  }
});

export default connect(
  undefined,
  mapDispatchToProps
)(withStyles(formFieldStyles)(TargetedOfferForm));
