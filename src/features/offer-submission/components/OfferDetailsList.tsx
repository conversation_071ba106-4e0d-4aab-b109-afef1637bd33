import {
  createStyles,
  List,
  ListItem,
  ListItemText,
  Theme,
  withStyles,
  WithStyles
} from "@material-ui/core";
import { get } from "lodash";
import moment from "moment-timezone";
import React, { PureComponent } from "react";
import { IOfferFormModelWithNames } from "../../../validation/validator";

interface IOfferPreviewState extends WithStyles {
  offer: IOfferFormModelWithNames;
}

class OfferDetailsTable extends PureComponent<IOfferPreviewState> {
  public render() {
    const { classes } = this.props;
    return (
      <List className={"offer-details-list-row"}>
        {this.getOfferData().map(data => (
            (data.value!=="delete")?
          <ListItem
            classes={{
              root: classes.listItem
            }}
          >
            <ListItemText
              classes={{
                root: classes.listItemTextRoot,
                primary: classes.listItemText,
                secondary: classes.listItemText
              }}
              primary={data.header}
              secondary={data.value}
            />
          </ListItem>:<html style={{ display: "block" }}></html>
        ))}
      </List>
    );
  }
  public getOfferData = () => {
    const  firstQualificationDate = this.getValueFormat(get(this.props.offer, "firstQualificationDate"));
    return [
      {
        header: "Partner Name",
        value: get(this.props.offer, "partnerName")
      },
      {
        header: "Category",
        value: get(this.props.offer, "offerCategory1Name", "N/A")
      },
      {
        header: "Sub Category",
        value: get(this.props.offer, "offerCategory2Name", "N/A")
      },
      {
        header: "Issuance Code",
        value: this.getValueFormat(get(this.props.offer, "issuanceCode"))
      },
      {
        header: "Campaign Code",
        value: get(this.props.offer, "campaignCode")
          ? get(this.props.offer, "campaignCode")
          : "N/A"
      },
      {
        header: "First Qualification Date",
        value: ((get(this.props.offer, "firstQualificationDate"))!== undefined)?(this.getValueFormat(get(this.props.offer, "firstQualificationDate"))):"delete"
      },
      {
        header: "Last Qualification Date",
        value: ((get(this.props.offer, "lastQualificationDate"))!== undefined)?this.getValueFormat(get(this.props.offer, "lastQualificationDate")):"delete"
      },
      {
        header: "Eligibility Duration (days)",
        value: ((get(this.props.offer, "eligibilityDuration"))!== undefined)?this.getValueFormat(get(this.props.offer, "eligibilityDuration")):"delete"
      },
      {
        header: "Display Date",
        value: (firstQualificationDate==="N/A")?this.getValueFormat(get(this.props.offer, "displayDate")):"delete"
      },
      {
        header: "Start Date",
        value: (firstQualificationDate==="N/A")?this.getValueFormat(get(this.props.offer, "startDate")):"delete"
      },
      {
        header: "End Date",
        value: (firstQualificationDate==="N/A")?this.getValueFormat(get(this.props.offer, "endDate")):"delete"
      },
      {
        header: "Priority",
        value: get(this.props.offer, "displayPriority")
      },
      {
        header: "Mechanism Type",
        value: get(this.props.offer, "mechanisms[0].mechanismType")

      },
      {
        header: "Promotional Tags",
        value: get(this.props.offer, "tagNames[0.en-US]", "N/A")
      },
      {
        header: "Regions",
        value: this.getValueFormat(get(this.props.offer, "regions"))
      }
    ];
  };

  public getValueFormat = (value: any) => {
    if (value === undefined) {
      return "N/A";
    } else if (typeof value === "string") {
      if (value.includes(":")) {
        return moment
          .utc(value)
          .locale("en")
          .format("ddd, MMM Do, YYYY hh:mm A");
      }
    } else if (typeof value === "number") {
      return value.toString();
    } else if (typeof value === "object") {
      return value.join(", ");
    }
    return value;
  };
}

const styles = createStyles((theme: Theme) => ({
  listItem: {
    alignItems: "flex-start",
    padding: "0.2em 0em 0.3em 0.3em",
    flex: "1 0 20%"
  },
  listItemText: {
    fontSize: "0.8125rem !important"
  },
  listItemTextRoot: {
    paddingRight: "0em",
    marginTop: 0,
    marginBottom: 0
  }
}));

export default withStyles(styles)(OfferDetailsTable);
