import {
  createStyles,
  Grid,
  Theme,
  Typography,
  withStyles,
  WithStyles
} from "@material-ui/core";
import React, { ReactNode } from "react";
import HelperTooltip from "../../../components/form/widgets/HelperTooltip";

const styles = createStyles((theme: Theme) => ({
  root: {
    border: "1px solid #1790CC",
    borderRadius: "3px",
    borderTop: "none",
    margin: `${theme.spacing(1)}px 0`
  },
  title: {
    backgroundColor: "#2faae7",
    borderBottom: "1px solid #1790CC",
    padding: "0.5em"
  },
  titleText: {
    color: theme.palette.common.white,
    width: "100%"
  }
}));

interface IOfferSubmissionFormPanelProps extends WithStyles {
  title: ReactNode;
  tooltipText?: string;
}

const OfferSubmissionFormPanel: React.FunctionComponent<
  IOfferSubmissionFormPanelProps
> = ({ children, title, classes, tooltipText }) => {
  return (
    <Grid container={true} className={classes.root}>
      <Grid
        direction="row"
        container={true}
        item={true}
        className={classes.title}
      >
        <Typography variant="h5" className={classes.titleText}>
          {title}
          {tooltipText && (
            <HelperTooltip text={tooltipText} hasInvertedColours={true} />
          )}
        </Typography>
      </Grid>
      {children}
    </Grid>
  );
};

export default withStyles(styles)(OfferSubmissionFormPanel);
