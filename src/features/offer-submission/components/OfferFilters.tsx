import {
  Chip,
  createStyles,
  ExpansionPanel,
  ExpansionPanelDetails,
  ExpansionPanelSummary,
  FormControl,
  FormLabel,
  Grid,
  ListItem,
  ListItemText,
  MenuItem,
  Theme,
  Typography,
  WithStyles,
  withStyles
} from "@material-ui/core";
import _ from "lodash";
import React from "react";
import { BB8Lookup, BB8MainPage, BB8SystemType, IRenderOptionProps } from "../../../BB8";
import { PartnerLookup } from "../../../components/form/fields/PartnerLookup";
import { AWARD_TYPES } from "../../../models/AwardType";
import { IBulkJob } from "../../../models/BulkJob";
import { IChooseUser, USER_OPTIONS } from "../../../models/ChooseUser";
import { IStaticFilterOption } from "../../../models/IStaticFilterOption";
import { MECHANISM_TYPES } from "../../../models/MechanismType";
import { AdvancedSearchFilterTypes, IOfferFilter, StaticFilterTypes } from "../../../models/OffersRequestParams";
import { OFFER_TYPES } from "../../../models/OfferType";
import { IPartner } from "../../../models/Partner";
import { PROGRAM_TYPES } from "../../../models/ProgramType";
import { QUALIFIER_TYPES } from "../../../models/QualifierType";
import { REGIONS } from "../../../models/Region";
import { ITarget, TARGETS } from "../../../models/Target";
import { IRbacUser } from "../../../models/User";
import { DATE_TIME_SECONDS_FORMAT, getLocalTime } from "../utils";
import { ReactComponent as ExpandMore } from "./baseline-expand_more-24px.svg";
import { CategoryFilterField, IssuanceCodeFilterField, PromotionFilterField } from "./FilterFields";
import CampaignCodeFilterField from "./FilterFields/CampaignCodeFilterField";
import OfferIdFilterField from "./FilterFields/OfferIdFilterField";

interface IRenderFilterChipsProps {
  options: IStaticFilterOption[] | undefined;
  selectedOptions: Set<string>;
  filterType: string;
}

const filterToObjectsMapping: Map<string, IStaticFilterOption[]> = new Map([
  [StaticFilterTypes.offerType, OFFER_TYPES],
  [StaticFilterTypes.qualifier, QUALIFIER_TYPES],
  [StaticFilterTypes.awardType, AWARD_TYPES],
  [StaticFilterTypes.programType, PROGRAM_TYPES],
  [StaticFilterTypes.mechanismType, MECHANISM_TYPES],
  [StaticFilterTypes.regions, REGIONS]
]);

const filterToNameMapping: Map<string, string> = new Map([
  [StaticFilterTypes.offerType, "Offer Types"],
  [StaticFilterTypes.qualifier, "Qualifier Types"],
  [StaticFilterTypes.awardType, "Award Types"],
  [StaticFilterTypes.programType, "Program Types"],
  [StaticFilterTypes.mechanismType, "Mechanism Types"],
  [StaticFilterTypes.regions, "Regions"]
]);

const styles = createStyles((theme: Theme) => ({
  bulkJobCreatedAt: {
    fontStyle: "italic"
  },
  bulkJobName: {
    display: "inline"
  },
  expansion: {
    backgroundColor: "transparent",
    width: "100%"
  },
  expansionDetails: {
    padding: `${theme.spacing(2)}px`,
    border: `1px solid ${theme.palette.secondary.light}`,
    margin: `0 0 ${theme.spacing(2)}px 0`
  },
  expansionSummary: {
    padding: `0 ${theme.spacing(1)}px 0 ${theme.spacing(2)}px `,
    width: `fit-content`
  },
  formControl: {
    padding: `${theme.spacing(2)}px ${theme.spacing(2)}px 0`,
    margin: `0`
  },
  formLabel: {
    padding: `0 0 ${theme.spacing(1)}px 0`
  },
  group: {
    margin: `0`
  },
  heading: {
    flexGrow: 1
  },
  root: {
    display: "flex"
  },
  staticFilter: {
    padding: `${theme.spacing(1)}px 0 ${theme.spacing(1)}px 0`
  },
  chipRow: {
    padding: `${theme.spacing(0.5)}px 0 0`
  },
  partnerSelection: {
    margin: `${theme.spacing(1)}px 0`
  }
}));

interface IOfferFilterProps extends WithStyles {
  bulkJobs?: IBulkJob[];
  partners?: IPartner[];
  filters?: IOfferFilter;
  onFilterChange: (updatedFilterValue: IOfferFilter) => void;
  currentUser?: string;
  isAdmin: boolean;
  users?: IRbacUser[];
}
const OfferFilters: React.FunctionComponent<IOfferFilterProps> = ({
  bulkJobs,
  classes,
  partners,
  filters,
  onFilterChange,
  currentUser,
  isAdmin = false,
  users
}) => {

  function determineInitialCreatedByValue() {
    if (filters && filters.user === currentUser) {
      return "me";
    } else if ((filters && filters.user !== currentUser) || isAdmin) {
      return "anyone";
    }
    return "me";
  }

  const [value, setValue] = React.useState<IOfferFilter>({
    user: determineInitialCreatedByValue()
  });
  const [bulkJobSearchString, setBulkJobSearch] = React.useState<string>("");
  const [userSearchString, setSearchString] = React.useState<string>("");

  function handleChange(event: IChooseUser) {

    if (event && event.value !== "anyone") {
      if (event.value === "me") {
        if (currentUser && userHasSelectedBulkJob(currentUser)) {
          updateFilter({ user: currentUser });
        } else {
          updateFilter({ user: currentUser, bulkId: null });
        }
      } else if (event.value !== undefined) {
        if (event.value && userHasSelectedBulkJob(event.value)) {
          updateFilter({ user: event.value });
        } else {
          updateFilter({ user: event.value, bulkId: null });
        }
      } else {
        updateFilter({ user: undefined });
      }
      setValue(prevState => ({
        ...prevState,
        [event.name]: event.value
      }));
    } else {
      updateFilter({ user: undefined });
    }
  }

  function handleTargetChange(selectedTarget: any) {
    updateFilter({ massOffer: selectedTarget ? selectedTarget.value : null });
  }

  function userHasSelectedBulkJob(user: string) {
    if (!filters) {
      return false;
    }
    const foundBulkJob = _.find(
      getFilteredBulkJobs(user),
      (i: IBulkJob) => i.id === filters.bulkId
    );
    if (foundBulkJob) {
      return true;
    }
    return false;
  }

  function updateFilter(updatedFilter: any) {
    const updatedFilters = {
      ...filters,
      ...updatedFilter
    };

    if (!_.isEqual(filters, updatedFilters)) {
      onFilterChange(updatedFilters);
    }
  }

  function getTargetingValue(targetted: ITarget | string): string {
    if (typeof targetted === "string") {
      return targetted;
    } else {
      return targetted ? targetted.name : "";
    }
  }
  const renderDropDownOption: React.FunctionComponent<IRenderOptionProps<
    ITarget
  >> = props => {
    const isHighlighted = props.highlightedIndex === props.index;
    return (
      <MenuItem
        {...props.itemProps}
        key={props.option.value}
        selected={isHighlighted}
      >
        {props.option.name}
      </MenuItem>
    );
  };

  // Bulk Job selection:
  function getBulkJob(bulkJob: IBulkJob | string): string {
    if (typeof bulkJob === "string") {
      return bulkJob;
    } else {
      return bulkJob ? bulkJob.name : "";
    }
  }

  const renderBulkJobOption: React.FunctionComponent<IRenderOptionProps<
    IBulkJob
  >> = props => {
    const createdAtStr = props.option.createdAt
      ? getLocalTime(props.option.createdAt, DATE_TIME_SECONDS_FORMAT)
      : "";

    return (
      <ListItem button={true} {...props.itemProps} key={props.option.id}>
        <ListItemText
          primary={props.option.partnerName}
          secondary={
            <React.Fragment>
              <Typography variant="body2" className={classes.bulkJobName}>
                {props.option.name}
              </Typography>{" "}
              - <span className={classes.bulkJobCreatedAt}>{createdAtStr}</span>
            </React.Fragment>
          }
        />
      </ListItem>
    );
  };

  function getTargetFromValue(massOfferValue: any) {
    const found = _.find(TARGETS, tag => tag.value === massOfferValue);
    return found ? found.name : null;
  }

  // Filter Chips

  const FiltersChip: React.FunctionComponent<IRenderFilterChipsProps> = props => {
    return (
      <div>
        {props.options &&
          props.options.map(option => (
            <Chip
              onClick={() =>
                handleStaticFilterClick(option.value, props.filterType)
              }
              label={option.name}
              color={
                props.selectedOptions.has(option.value)
                  ? "primary"
                  : "secondary"
              }
              className="chip"
              key={props.filterType + "_" + option.value}
            />
          ))}
      </div>
    );
  };

  function handleStaticFilterClick(value: string, filterName: string): void {
    const filterSet =
      filters && filters[filterName] ? filters[filterName] : new Set();

    if (filterSet && filterSet instanceof Set) {
      filterSet.has(value) ? filterSet.delete(value) : filterSet.add(value);

      const newObj: { [key: string]: Set<string> } = {};
      newObj[filterName] = filterSet;
      updateFilter(newObj);
    }

    // Do nothing if the type of the filter is not set, as this method handles only the
    // static filters.
  }
  function getBulkJobNameFromId(bulkId: any) {
    const found = _.find(bulkJobs, job => job.id === bulkId);
    return found ? found.name : "";
  }

  function getFilteredBulkJobs(userFilterOverride?: any) {
    const searchStr = bulkJobSearchString.trim().toLowerCase();
    const filterByUser = userFilterOverride
      ? userFilterOverride
      : filters && filters.user != null
      ? filters.user
      : null;
    const filterByPartner =
      filters && filters.partnerId != null ? filters.partnerId : null;
    return bulkJobs
      ? _.chain(bulkJobs)
          .filter(job => (filterByUser ? job.createdBy === filterByUser : true))
          .filter(job =>
            filterByPartner ? job.partnerId === filterByPartner : true
          )
          .filter(job => job.name && job.name.toLowerCase().includes(searchStr))
          .sortBy("createdAt")
          .reverse()
          .value()
      : [];
  }

  function handleBulkJobChange(bulkJobSelected: any) {
    updateFilter({ bulkId: bulkJobSelected ? bulkJobSelected.id : null });
  }
  function handlePartnerChange(selectedPartner?: IPartner) {
    const partnerId: string = selectedPartner ? selectedPartner.id : "";

    let cleanedFilters: IOfferFilter = {};
    if (filters) {
      Object.keys(filters).forEach(key => {
        if (
          Object.values(AdvancedSearchFilterTypes).includes(key) ||
          Object.values(StaticFilterTypes).includes(key)
        ) {
          cleanedFilters[key] = undefined;
        } else {
          cleanedFilters[key] = filters[key];
        }
      });
    }
    cleanedFilters = { ...cleanedFilters, partnerId };

    updateFilter(cleanedFilters);
  }

  const getUserOption = () => {
    const searchStr = userSearchString.trim().toLowerCase();
    const otherUsers: IChooseUser[] = [];

    if (isAdmin && users) {
      users.forEach((user: IRbacUser) => {
        const currUser: IChooseUser = { name: user.fullName, value: user.id };
        otherUsers.push(currUser);
      });
    }

    const filteredUsers = _.chain(otherUsers)
      .filter(user => user.name && user.name.toLowerCase().includes(searchStr))
      .sortBy("name")
      .value();

    const allUsers = [...USER_OPTIONS, ...filteredUsers];

    return allUsers ? allUsers : [];
  };

  const renderUserOption: React.FunctionComponent<IRenderOptionProps<
    IBulkJob
  >> = props => {
    return (
      <ListItem button={true} {...props.itemProps}>
        <ListItemText
          primary={
            <Typography variant="body2" className={classes.userName}>
              {props.option.name}
            </Typography>
          }
        />
      </ListItem>
    );
  };

  const getUserNameFromEmail = (userEmail: any) => {
    if (userEmail === currentUser) {
      return "Me";
    }

    const found = _.find(users, user => user.id === userEmail);

    return found ? found.fullName : "Anyone";
  };

  function getUser(user: IChooseUser | string): string {
    if (typeof user === "string") {
      return user;
    } else {
      return user ? user.name : "";
    }
  }

  return (
    <BB8MainPage type={BB8SystemType.Billing}>
      <BB8MainPage.Padded>
        <Grid>
          <Grid
            container={true}
            item={true}
            component="section"
            className="basic-filters"
          >
            <Grid item={true} xs={6} container={true}>
              <FormControl
                className={classes.formControl}
                fullWidth={true}
                margin="dense"
              >
                <FormLabel className={classes.formLabel} component="label">
                  Created by user:
                </FormLabel>
                <Grid item={true} xs={6}>
                  <BB8Lookup
                    name="userOption"
                    options={getUserOption()}
                    onInputChange={e => {
                      if (
                        e.target.value === "" &&
                        filters &&
                        filters.user !== undefined
                      ) {
                        updateFilter({ user: undefined });
                      }
                      setSearchString(e.target.value);
                    }}
                    onInputBlur={() => setSearchString("")}
                    onSelect={handleChange}
                    renderOption={renderUserOption}
                    itemToString={getUser}
                    value={getUserNameFromEmail(
                      filters && filters.user ? filters.user : null
                    )}
                    shouldOpenOnFocus={true}
                    placeholder="-- Choose User --"
                    classes={ {} }
                  />
                </Grid>
              </FormControl>
            </Grid>
            <Grid item={true} xs={6} container={true} direction="column">
              <FormControl className={classes.formControl} margin="dense">
                <FormLabel className={classes.formLabel} component="label">
                  Partner:{" "}
                </FormLabel>
                {partners && partners.length > 0 && (
                  <PartnerLookup
                    name="partnerId"
                    partners={partners}
                    placeholder="Select Partner"
                    value={filters ? filters.partnerId : ""}
                    onSelect={handlePartnerChange}
                  />
                )}
              </FormControl>
            </Grid>
          </Grid>
          <Grid
            container={true}
            item={true}
            component="section"
            className="advanced-filters"
          >
            <ExpansionPanel
              elevation={0}
              className={classes.expansion}
              color="primary"
            >
              <ExpansionPanelSummary
                expandIcon={<ExpandMore />}
                classes={{ root: classes.expansionSummary }}
              >
                <Typography variant="body2" className={classes.heading} color="textSecondary">
                  Advanced Search
                </Typography>
              </ExpansionPanelSummary>
              <ExpansionPanelDetails className={classes.expansionDetails}>
                <Grid container={true} spacing={2}>
                  <Grid
                    item={true}
                    container={true}
                    alignItems="center"
                    direction="row"
                  >
                    <Grid item={true} xs={1}>
                      <FormLabel component="label">Bulk Name: </FormLabel>
                    </Grid>
                    <Grid item={true} xs={6}>
                      <BB8Lookup
                        name="bulkJob"
                        options={getFilteredBulkJobs()}
                        onInputChange={e => {
                          setBulkJobSearch(e.target.value);
                          if (e.target.value.length === 0) {
                            handleBulkJobChange(null); // select no bulk job
                          }
                        }}
                        onInputBlur={() => setBulkJobSearch("")}
                        onSelect={handleBulkJobChange}
                        renderOption={renderBulkJobOption}
                        itemToString={getBulkJob}
                        value={getBulkJobNameFromId(
                          filters ? filters.bulkId : null
                        )}
                        shouldOpenOnFocus={true}
                        placeholder="Select"
                        classes={ {} }
                      />
                    </Grid>
                  </Grid>
                  <Grid
                    item={true}
                    container={true}
                    alignItems="center"
                    direction="row"
                  >
                    <PromotionFilterField
                      updateFilter={updateFilter}
                      filters={filters}
                    />
                  </Grid>
                  <Grid
                    item={true}
                    container={true}
                    alignItems="center"
                    direction="row"
                  >
                    <Grid item={true} xs={1}>
                      <FormLabel component="label">Targeting: </FormLabel>
                    </Grid>
                    <Grid item={true} xs={6}>
                      <BB8Lookup
                        name="massOffer"
                        options={TARGETS}
                        onSelect={handleTargetChange}
                        renderOption={renderDropDownOption}
                        itemToString={getTargetingValue}
                        value={getTargetFromValue(
                          filters ? filters.massOffer : null
                        )}
                        shouldOpenOnFocus={true}
                        placeholder="Select"
                        classes={ {} }
                      />
                    </Grid>
                  </Grid>
                  <CategoryFilterField
                    updateFilter={updateFilter}
                    filters={filters}
                  />
                  <IssuanceCodeFilterField
                    updateFilter={updateFilter}
                    filters={filters}
                  />
                  <CampaignCodeFilterField
                    updateFilter={updateFilter}
                    filters={filters}
                  />
                  <OfferIdFilterField
                    updateFilter={updateFilter}
                    filters={filters}
                  />
                  <Grid
                    container={true}
                    item={true}
                    xs={12}
                    className={classes.staticFilterHolder}
                  >
                    {Object.keys(StaticFilterTypes).map((filter, index) => (
                      <Grid
                        container={true}
                        spacing={0}
                        className={classes.staticFilter}
                        alignItems="center"
                        key={index}
                      >
                        <Grid item={true} xs={1}>
                          <FormLabel component="label">
                            {filterToNameMapping.get(filter)}:
                          </FormLabel>
                        </Grid>
                        <Grid item={true} xs={8} className={classes.chipRow}>
                          <FiltersChip
                            filterType={filter}
                            options={filterToObjectsMapping.get(filter)}
                            selectedOptions={
                              filters && filters[filter]
                                ? filters[filter]
                                : new Set()
                            }
                          />
                        </Grid>
                      </Grid>
                    ))}
                  </Grid>
                </Grid>
              </ExpansionPanelDetails>
            </ExpansionPanel>
          </Grid>
        </Grid>
      </BB8MainPage.Padded>
    </BB8MainPage>
  );
};

export default React.memo(withStyles(styles)(OfferFilters));
