import React, { useEffect } from "react";
import { connect } from "react-redux";
import BB8Lookup, {
  IRenderOptionProps
} from "../../../../BB8/components/BB8Lookup";
import { IOfferCategory } from "../../../../models/Category";
import { FeatureFlag, FEATURE_FLAG } from "../FeatureFlag";
import { fetchCategoriesAsync } from "../../actions";
import _ from "lodash";
import { MenuItem, Grid, FormLabel } from "@material-ui/core";
import { ThunkDispatch } from "redux-thunk";
import { IRootState } from "../../../../store";
import { Action } from "redux";
import { IOfferFilter } from "../../../../models/OffersRequestParams";
import { FIELDS_CONFIG } from "../../../../components/form/configs/fields.config";
import {
  formatCategoryLabel,
  getCategoryNameFromValue,
  getChildCategoriesFromCategoryId,
  hasChildCategories
} from "../../utils/categories";

const CategoryFilterField: React.FunctionComponent<{
  categories: IOfferCategory[];
  updateFilter: (prop: any) => void;
  filters: IOfferFilter;
  fetchCategories: () => void;
}> = ({ updateFilter, filters, fetchCategories, categories }) => {
  useEffect(() => fetchCategories(), []);

  const [categorySearchString, setCategorySearchString] = React.useState<
    string
  >("");
  const [subCategorySearchString, setSubCategorySearchString] = React.useState<
    string
  >("");

  function handleCategoryChange(selectedCategory?: IOfferCategory) {
    if (selectedCategory && selectedCategory.id === "none") {
      updateFilter({
        onlyEmptyCategory: true,
        offerCategory1: null,
        offerCategory2: null
      });
    } else {
      updateFilter({
        offerCategory1: selectedCategory ? selectedCategory.id : null,
        offerCategory2: null,
        onlyEmptyCategory: null
      });
    }
  }

  function handleSubCategoryChange(selectedCategory?: IOfferCategory) {
    updateFilter({
      offerCategory2: selectedCategory ? selectedCategory.id : null
    });
  }

  function getCategoryValue(category: IOfferCategory | string): string {
    if (typeof category === "string") {
      return category;
    } else {
      return category ? formatCategoryLabel(category) || "" : "";
    }
  }

  function getFilteredCategories(
    categoryList: IOfferCategory[],
    searchString: string
  ) {
    const cleanedSearchString = searchString.trim().toLowerCase();
    return categoryList.filter(category =>
      formatCategoryLabel(category)
        .toLowerCase()
        .includes(cleanedSearchString)
    );
  }

  const renderCategoryOption: React.FunctionComponent<
    IRenderOptionProps<IOfferCategory>
  > = props => {
    const isHighlighted = props.highlightedIndex === props.index;
    return (
      <MenuItem
        {...props.itemProps}
        key={props.option.id}
        selected={isHighlighted}
      >
        {formatCategoryLabel(props.option)}
      </MenuItem>
    );
  };

  return (
    <FeatureFlag featureName={FEATURE_FLAG.CATEGORY}>
      <Grid item={true} container={true} alignItems="center" direction="row">
        <Grid item={true} xs={1}>
          <FormLabel component="label">
            {FIELDS_CONFIG.offerCategory1.title}:{" "}
          </FormLabel>
        </Grid>
        <Grid item={true} xs={6}>
          <BB8Lookup
            name="offerCategory1"
            options={getFilteredCategories(categories, categorySearchString)}
            onSelect={handleCategoryChange}
            onInputChange={e => {
              if (e.target.value.length === 0) {
                handleCategoryChange(); // select no category
              } else {
                setCategorySearchString(e.target.value);
              }
            }}
            onInputBlur={() => setCategorySearchString("")}
            renderOption={renderCategoryOption}
            itemToString={getCategoryValue}
            value={getCategoryNameFromValue(
              categories,
              filters && filters.onlyEmptyCategory
                ? "none"
                : filters.offerCategory1
                ? filters.offerCategory1
                : ""
            )}
            shouldOpenOnFocus={true}
            placeholder="Select"
            classes={ {} }
          />
        </Grid>

        {filters &&
          filters.offerCategory1 &&
          hasChildCategories(filters.offerCategory1, categories) && (
            <React.Fragment>
              <Grid item={true} xs={1}>
                <FormLabel component="label" style={{ paddingLeft: "8px" }}>
                  {FIELDS_CONFIG.offerCategory2.title}:{" "}
                </FormLabel>
              </Grid>
              <Grid item={true} xs={4}>
                <BB8Lookup
                  name="offerCategory2"
                  options={getFilteredCategories(
                    getChildCategoriesFromCategoryId(
                      categories,
                      filters.offerCategory1
                    ),
                    subCategorySearchString
                  )}
                  onInputChange={e => {
                    if (e.target.value.length === 0) {
                      handleSubCategoryChange(); // select no child category
                    } else {
                      setSubCategorySearchString(e.target.value);
                    }
                  }}
                  onSelect={handleSubCategoryChange}
                  onInputBlur={() => setSubCategorySearchString("")}
                  renderOption={renderCategoryOption}
                  itemToString={getCategoryValue}
                  value={getCategoryNameFromValue(
                    categories,
                    filters.offerCategory2 || ""
                  )}
                  shouldOpenOnFocus={true}
                  placeholder="Select"
                  classes={ {} }
                />
              </Grid>
            </React.Fragment>
          )}
      </Grid>
    </FeatureFlag>
  );
};

const mapStateToProps = (state: IRootState) => ({
  categories:
    [
      ...[
        {
          id: "none",
          translations: { "en-US": "N/A", "fr-CA": "No Categories" }
        }
      ],
      ...state.offers.categories
    ] || []
});

const mapDispatchToProps = (
  dispatch: ThunkDispatch<IRootState, void, Action>
) => ({
  fetchCategories: () => {
    dispatch(fetchCategoriesAsync());
  }
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(CategoryFilterField);
