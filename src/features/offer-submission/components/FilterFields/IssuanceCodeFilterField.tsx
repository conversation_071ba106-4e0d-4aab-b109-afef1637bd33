import React from "react";
import _ from "lodash";
import {
  <PERSON>rid,
  FormLabel,
  withSty<PERSON>,
  WithStyles,
  createStyles,
  MenuItem
} from "@material-ui/core";
import { IOfferFilter } from "../../../../models/OffersRequestParams";
import { FIELDS_CONFIG } from "../../../../components/form/configs/fields.config";
import BB8Lookup, {
  IRenderOptionProps
} from "../../../../BB8/components/BB8Lookup";

const options = [
  {
    label: "Set",
    value: true
  },
  {
    label: "Not Set",
    value: false
  }
];

const renderOption: React.FunctionComponent<
  IRenderOptionProps<{ label: string; value: string }>
> = props => {
  const isHighlighted = props.highlightedIndex === props.index;
  return (
    <MenuItem
      {...props.itemProps}
      key={props.option.label}
      selected={isHighlighted}
    >
      {props.option.label}
    </MenuItem>
  );
};

const valueToString = (value?: string) => {
  if (!value) {
    return "";
  }

  const foundOption = options.find(i => {
    return i.value === (value === "true");
  });
  return foundOption && foundOption.label || "";
};

const itemToString = (item: string | { label: string; value: boolean }) => {
  if(typeof item === "string") {
    return item;
  }

  return item.label;
};

const IssuanceCodeFilterField: React.FunctionComponent<{
  updateFilter: (prop: any) => void;
  filters?: IOfferFilter;
}> = ({ updateFilter, filters }) => {
  const onToggle = (selectedOption?: { value: string; label: string }) => {
    updateFilter({
      hasIssuanceCode: selectedOption ? selectedOption.value : null
    });
  };

  return (
    <Grid item={true} container={true} alignItems="center" direction="row">
      <Grid item={true} xs={1}>
        <FormLabel component="label">
          {FIELDS_CONFIG.issuanceCode.title}:{" "}
        </FormLabel>
      </Grid>
      <Grid item={true} xs={6}>
        <BB8Lookup
          name="hasIssuanceCode"
          options={options}
          onSelect={onToggle}
          renderOption={renderOption}
          itemToString={itemToString}
          value={filters ? valueToString(filters.hasIssuanceCode) : ""}
          onInputChange={e => {
            if (e.target.value.length === 0) {
              onToggle(); // select empty option
            }
          }}
          shouldOpenOnFocus={true}
          placeholder="Select"
          classes={ {} }
        />
      </Grid>
    </Grid>
  );
};

export default IssuanceCodeFilterField;
