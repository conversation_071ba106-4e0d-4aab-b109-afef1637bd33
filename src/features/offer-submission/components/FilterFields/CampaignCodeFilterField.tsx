import { FormLabel, Grid } from "@material-ui/core";
import React, { useState } from "react";
import { BB8Lookup } from "../../../../BB8";
import { FIELDS_CONFIG } from "../../../../components/form/configs/fields.config";
import { IOfferFilter } from "../../../../models/OffersRequestParams";

const CampaignCodeFilterField: React.FunctionComponent<{
  updateFilter: (prop: any) => void;
  filters?: IOfferFilter;
}> = ({ updateFilter, filters }) => {
  const handleCampaignCodeChange = (selectedOption?: string) => {
    updateFilter({
      campaignCode: selectedOption ? selectedOption : null
    });
  };

  const [campaignCodeList, setCampaignCodeList] = useState<string[]>(
    filters && filters.campaignCode ? [filters.campaignCode] : []
  );

  return (
    <Grid item={true} container={true} alignItems="center" direction="row">
      <Grid item={true} xs={1}>
        <FormLabel component="label">
          {FIELDS_CONFIG.campaignCode.title}:{" "}
        </FormLabel>
      </Grid>
      <Grid item={true} xs={6}>
        <BB8Lookup
          name="campaignCode"
          options={campaignCodeList}
          onSelect={handleCampaignCodeChange}
          value={filters && filters.campaignCode ? filters.campaignCode : ""}
          onInputChange={e => {
            if (e.target.value.length === 0) {
              handleCampaignCodeChange();
            }
            setCampaignCodeList([e.target.value]);
          }}
          onInputBlur={() => setCampaignCodeList([])}
          shouldOpenOnFocus={true}
          placeholder="Select"
          classes={ {} }
        />
      </Grid>
    </Grid>
  );
};

export default CampaignCodeFilterField;
