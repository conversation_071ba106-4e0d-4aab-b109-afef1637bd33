import { <PERSON><PERSON>abel, Grid, MenuItem } from "@material-ui/core";
import _ from "lodash";
import React, { useEffect } from "react";
import { connect } from "react-redux";
import { Action } from "redux";
import { ThunkDispatch } from "redux-thunk";
import { BB8Lookup, IRenderOptionProps } from "../../../../BB8";
import { FIELDS_CONFIG } from "../../../../components/form/configs/fields.config";
import { IOfferFilter } from "../../../../models/OffersRequestParams";
import { IOfferPromotion } from "../../../../models/Promotion";
import { IRootState } from "../../../../store";
import { fetchPromotionsAsync } from "../../actions";

const PromotionFilterField: React.FunctionComponent<{
  promotions: IOfferPromotion[];
  updateFilter: (prop: any) => void;
  filters: IOfferFilter;
  fetchPromotions: () => void;
}> = ({ updateFilter, filters, fetchPromotions, promotions }) => {
  useEffect(() => fetchPromotions(), []);

  const [promotionSearchString, setPromotionSearchString] = React.useState<
    string
  >("");

  function getPromotionNameFromValue(promotionId: string) {
    const found = _.find(promotions, tag => tag.id === promotionId);
    return found ? formatPromotionLabel(found) : null;
  }

  function formatPromotionLabel(promotion: IOfferPromotion) {
    return `${promotion.translations["en-US"]} / ${
      promotion.translations["fr-CA"]
    }`;
  }
  function handlePromotionChange(selectedPromotion?: IOfferPromotion) {
    updateFilter({
      tags: selectedPromotion ? selectedPromotion.id : null
    });
  }

  function getPromotionValue(promotion: IOfferPromotion | string): string {
    if (typeof promotion === "string") {
      return promotion;
    } else {
      return promotion ? formatPromotionLabel(promotion) || "" : "";
    }
  }

  function getFilteredPromotions(
    promotionList: IOfferPromotion[],
    searchString: string
  ) {
    const cleanedSearchString = searchString.trim().toLowerCase();
    return promotionList.filter(promotion =>
      formatPromotionLabel(promotion)
        .toLowerCase()
        .includes(cleanedSearchString)
    );
  }

  const renderPromotionOption: React.FunctionComponent<
    IRenderOptionProps<IOfferPromotion>
  > = props => {
    const isHighlighted = props.highlightedIndex === props.index;
    return (
      <MenuItem
        {...props.itemProps}
        key={props.option.id}
        selected={isHighlighted}
      >
        {formatPromotionLabel(props.option)}
      </MenuItem>
    );
  };

  return (
    <Grid item={true} container={true} alignItems="center" direction="row">
      <Grid item={true} xs={1}>
        <FormLabel component="label">{FIELDS_CONFIG.tags.title}: </FormLabel>
      </Grid>
      <Grid item={true} xs={6}>
        <BB8Lookup
          name="tags"
          options={getFilteredPromotions(promotions, promotionSearchString)}
          onSelect={handlePromotionChange}
          onInputChange={e => {
            if (e.target.value.length === 0) {
              handlePromotionChange(); // select no Promotion
            } else {
              setPromotionSearchString(e.target.value);
            }
          }}
          onInputBlur={() => setPromotionSearchString("")}
          renderOption={renderPromotionOption}
          itemToString={getPromotionValue}
          value={filters.tags ? getPromotionNameFromValue(filters.tags) : ""}
          shouldOpenOnFocus={true}
          placeholder="Select"
          classes={ {} }
        />
      </Grid>
    </Grid>
  );
};

const mapStateToProps = (state: IRootState) => ({
  promotions: state.offers.promotions || []
});

const mapDispatchToProps = (
  dispatch: ThunkDispatch<IRootState, void, Action>
) => ({
  fetchPromotions: () => {
    dispatch(fetchPromotionsAsync());
  }
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(PromotionFilterField);
