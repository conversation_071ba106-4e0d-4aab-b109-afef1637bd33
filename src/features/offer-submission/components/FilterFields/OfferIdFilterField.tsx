import { FormLabel, Grid } from "@material-ui/core";
import React, { useState } from "react";
import { BB8Lookup } from "../../../../BB8";
import { FIELDS_CONFIG } from "../../../../components/form/configs/fields.config";
import { IOfferFilter } from "../../../../models/OffersRequestParams";

const OfferIdFilterField: React.FunctionComponent<{
  updateFilter: (prop: any) => void;
  filters?: IOfferFilter;
}> = ({ updateFilter, filters }) => {
  const handleOfferIdChange = (selectedOption?: string) => {
    updateFilter({
      id: selectedOption ? selectedOption : null
    });
  };

  const [offerIdList, setOfferIdList] = useState<string[]>(
    filters && filters.id ? [filters.id] : []
  );

  return (
    <Grid item={true} container={true} alignItems="center" direction="row">
      <Grid item={true} xs={1}>
        <FormLabel component="label">
          {FIELDS_CONFIG.id.title}:{" "}
        </FormLabel>
      </Grid>
      <Grid item={true} xs={6}>
        <BB8Lookup
          name="offerId"
          options={offerIdList}
          onSelect={handleOfferIdChange}
          value={filters && filters.id ? filters.id : ""}
          onInputChange={e => {
            if (e.target.value.length === 0) {
              handleOfferIdChange();
            }
            setOfferIdList([e.target.value]);
          }}
          onInputBlur={() => setOfferIdList([])}
          shouldOpenOnFocus={true}
          placeholder="Select"
          classes={ {} }
        />
      </Grid>
    </Grid>
  );
};

export default OfferIdFilterField;
