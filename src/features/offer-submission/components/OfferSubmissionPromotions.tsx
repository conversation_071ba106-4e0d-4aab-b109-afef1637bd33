import { Grid, Typography } from "@material-ui/core";
import { FormikProps } from "formik";
import React, { PureComponent } from "react";
import { connect } from "react-redux";
import { Action } from "redux";
import { ThunkDispatch } from "redux-thunk";
import { FIELDS_CONFIG } from "../../../components/form/configs/fields.config";
import { CustomSelectWidget } from "../../../components/form/widgets/CustomSelectWidget";
import { IOfferPromotion } from "../../../models/Promotion";
import { IRootState } from "../../../store";
import { IOfferFormModel } from "../../../validation/validator";
import { fetchPromotionsAsync } from "../actions";

interface IOfferSubmissionPromotionProps {
  disabled?: boolean;
  formikProps: FormikProps<Partial<IOfferFormModel>>;
  promotions: IOfferPromotion[];
  fetchPromotions: () => void;
}

class OfferSubmissionPromotions extends PureComponent<
  IOfferSubmissionPromotionProps
> {
  public state = {
    initialOfferTagId: null
  }

  public componentDidMount = () => {
    this.props.fetchPromotions();

    const { tags } = this.props.formikProps.values;
    const { values } = this.props.formikProps;

    if (tags) {
      const tagsLabels = [this.findPromotionTranslation(tags[0], this.props.promotions)];
      this.props.formikProps.setValues({
        ...values,
        tagsLabels
      });
    }
  };

  public render() {
    const { promotions } = this.props;
    const offer = this.props.formikProps.values;
    const offerTagId = offer && offer.tags ? offer.tags[0] : null;

    if(!this.state.initialOfferTagId) {
      this.setState({ initialOfferTagId: offerTagId})
    }

    const promotionOptions: IOfferPromotion[] = promotions.filter(
      ({ active, id }) => active === true || id === this.state.initialOfferTagId
    );
    const mappedPromotions = promotionOptions.map(this.offerDropDownFormat);

    return (
      <Grid
        container={true}
        item={true}
        xs={6}
        alignItems="flex-start"
        alignContent="flex-start"
        justify="flex-start"
      >
        <Typography
          variant="h5"
          component="h5"
          paragraph={true}
          gutterBottom={true}
        >
          Offer Tags
        </Typography>
        <CustomSelectWidget
          type="select"
          name="tags"
          title={FIELDS_CONFIG.tags.title}
          options={mappedPromotions}
          onChange={(e) => this.handleOfferPromotionChange(e, mappedPromotions)}
          hasTooltip={true}
          optional={true}
          canSelectEmptyValue={true}
        />
      </Grid>
    );
  }

  private offerDropDownFormat(promotion: IOfferPromotion) {
    return {
      value: promotion.id,
      label: `${promotion.translations["en-US"]} / ${
        promotion.translations["fr-CA"]
      }`,
      disabled: !promotion.active
    };
  }

  private handleOfferPromotionChange = (e: any, promotions: any) => {
    const { formikProps } = this.props;
    const values = formikProps.values;

    let tags = values.tags;
    let tagsLabels = [];
    if (e.target.name === "tags") {
      tags = e.target.value === undefined ? undefined : [e.target.value];
      if (tags) {
        tagsLabels = [promotions.find((promotion) => promotion.value === tags[0]).label];
      }
    }

    formikProps.setValues({
      ...values,
      tags,
      tagsLabels
    });
  };

  private findPromotionTranslation = (promotionId: string, promotions: any) => {
    if (promotionId) {
      return promotions.find((promotion) => promotion.id === promotionId).translations["en-US"];
    }
    return "";
  }
}

const mapStateToProps = (state: IRootState) => ({
  promotions: state.offers.promotions || []
});

const mapDispatchToProps = (
  dispatch: ThunkDispatch<IRootState, void, Action>
) => ({
  fetchPromotions: () => {
    dispatch(fetchPromotionsAsync());
  }
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(OfferSubmissionPromotions);
