import { Grid, Paper, Typography } from "@material-ui/core";
import { FormikProps } from "formik";
import { get, isArray } from "lodash";
import React, { PureComponent } from "react";
import CustomCheckboxField from "../../../components/form/fields/CustomCheckboxField";
import CustomLocalizationTextField from "../../../components/form/fields/CustomLocalizationTextField";
import CustomOptionalLocalizationField from "../../../components/form/fields/CustomOptionalLocalizationField";
import DaysToApply from "../../../components/form/fields/DaysToApply";
import OfferCombinations from "../../../components/form/fields/OfferCombinations";
import { CustomSelectWidget } from "../../../components/form/widgets/CustomSelectWidget";
import CustomTextWidget from "../../../components/form/widgets/CustomTextWidget";
import { getOfferLimitationTypeName } from "../../../models/OfferLimitation";
import {
  IOfferFormModel,
  OfferLimitation,
  OfferType,
  Availability,
} from "../../../validation/validator";
import FieldTitleForLocale from "./FieldTitleForLocale";
import OfferSubmissionFormPanel from "./OfferSubmissionFormPanel";
import { OfferSubmissionSubSectionTitle } from "./OfferSubmissionSubSectionTitle";
import HelperTooltip from "../../../components/form/widgets/HelperTooltip";
import { FIELDS_CONFIG } from "../../../components/form/configs/fields.config";
interface IOfferSubmissionOfferSettingsSectionProps {
  disabled?: boolean;
  schema: any;
  formikProps: FormikProps<IOfferFormModel>;
}
export default class OfferSubmissionOfferSettingsSection extends PureComponent<
  IOfferSubmissionOfferSettingsSectionProps
> {
  public state = {
    canBeCombined: true,
  };

  public render() {
    const { formikProps, schema } = this.props;
    const formData = formikProps.values;
    const offerLimitationOptions: string[] =
      schema.properties.offerLimitation.enum;

    return (
      <Grid
        item={true}
        xs={12}
        style={{ marginTop: "1rem", marginBottom: "1rem" }}
      >
        <Paper elevation={2} style={{ padding: "1rem", width: "100%" }}>
          <Typography
            variant="h4"
            component="h4"
            paragraph={true}
            gutterBottom={true}
          >
            Terms and Conditions
          </Typography>
          <Grid
            container={true}
            item={true}
            xs={12}
            spacing={2}
            direction="column"
          >
            <Grid container={true} item={true} xs={12}>
              <OfferCombinations formikProps={formikProps} />
            </Grid>
            <Grid container={true} item={true} xs={12}>
              <CustomOptionalLocalizationField
                formikProps={formikProps}
                fieldPath="exclusions"
                label={
                  formikProps.values.offerType.includes("am")
                    ? "AIR MILES Cash Exclusions"
                    : "Exclusions"
                }
              />
            </Grid>
            {formData.availability &&
              formData.availability.includes(Availability.Online) && (
                <Grid container={true} item={true} xs={12}>
                  <OfferSubmissionFormPanel
                    title="Partner Web URL Translations"
                    tooltipText="Website URL - must start with https:// or http:// e.g. http://www.airmiles.ca"
                  >
                    <CustomLocalizationTextField
                      name="partnerUrl"
                      label="Partner Web URL"
                    />
                  </OfferSubmissionFormPanel>
                </Grid>
              )}
            <Grid container={true} item={true} xs={12}>
              <DaysToApply
                formikProps={formikProps}
                min={get(schema, "properties.daysToApply.minimum", 1)}
                max={get(schema, "properties.daysToApply.maximum", 365)}
              />
            </Grid>
            <Grid container={true} item={true} xs={12}>
              <CustomSelectWidget
                type="offerLimitation"
                name="offerLimitation"
                title="Offer Limitation(s)"
                options={offerLimitationOptions.map((value: string) => ({
                  value,
                  label: getOfferLimitationTypeName(value),
                }))}
              />
              <Typography variant="caption">
                The offer limitation type.
              </Typography>
            </Grid>
            {formData.offerLimitation === OfferLimitation.Custom && (
              <Grid container={true} item={true} xs={12} spacing={1}>
                <OfferSubmissionFormPanel title="Offer Limitations Text Translations">
                  <CustomLocalizationTextField
                    name="offerLimitationText"
                    label="Offer Limitation Text"
                  />
                </OfferSubmissionFormPanel>
              </Grid>
            )}
            <Grid container={true} item={true} xs={12}>
              <CustomOptionalLocalizationField
                formikProps={formikProps}
                fieldPath="partnerLegalName"
                label="Partner Legal Name"
              />
            </Grid>
            <Grid container={true} item={true} xs={12}>
              <CustomOptionalLocalizationField
                formikProps={formikProps}
                fieldPath="trademarkInfo"
                label="Additional Trademark Information"
              />
            </Grid>
            <Grid container={true} item={true} xs={12}>
              <OfferSubmissionSubSectionTitle title="Legal Preview" />
              <Grid item={true} xs={12}>
                <CustomCheckboxField
                  name="hasCustomLegal"
                  label="Need to edit the legal content?"
                  optional={true}
                />
              </Grid>
              <Grid
                item={true}
                xs={12}
                container={true}
                alignItems="flex-start"
                direction="row"
              >
                {this.renderLegalPreviewForLocale("en")}
                {this.renderLegalPreviewForLocale("fr")}
              </Grid>
            </Grid>
          </Grid>
        </Paper>
      </Grid>
    );
  }

  private renderLegalPreviewForLocale = (locale: "en" | "fr") => {
    const { formikProps } = this.props;
    const styles =
      locale === "fr" ? { paddingLeft: "20px" } : { paddingRight: "20px" };

    return (
      <Grid item={true} container={true} sm={6} style={styles}>
        <FieldTitleForLocale title="Legal" locale={locale}>
          {formikProps.values.eventBasedOffer &&
          <HelperTooltip text={FIELDS_CONFIG["eventBasedOfferLegal"].toolTip} />}
        </FieldTitleForLocale>
        <CustomTextWidget
          name={"legalText." + (locale === "fr" ? "fr-CA" : "en-US")}
          multiline={true}
          disabled={!formikProps.values.hasCustomLegal}
          shouldUpdateFromProps={!formikProps.values.hasCustomLegal}
          key={formikProps.values.offerType + "-legal-" + locale}
        />
      </Grid>
    );
  };
}
