import { Grid, Paper, Typography } from "@material-ui/core";
import { FormikProps } from "formik";
import { get, isEmpty } from "lodash";
import React, { useEffect } from "react";
import { connect } from "react-redux";
import { FIELDS_CONFIG } from "../../../components/form/configs/fields.config";
import { CustomSelectWidget } from "../../../components/form/widgets/CustomSelectWidget";
import { IOfferFormModel } from "../../../validation/validator";
import { IOfferCategory } from "../../../models/Category";
import { IRootState } from "../../../store";
import { ThunkDispatch } from "redux-thunk";
import { Action } from "redux";
import { fetchCategoriesAsync } from "../actions";
import {
  formatCategoryLabel,
  getChildCategoriesFromCategoryId
} from "../utils/categories";

interface IOfferSubmissionOfferCategorySectionProps {
  disabled?: boolean;
  formikProps: FormikProps<Partial<IOfferFormModel>>;
  categories: IOfferCategory[];
  fetchCategories: () => void;
}

const OfferSubmissionOfferCategorySection: React.FunctionComponent<IOfferSubmissionOfferCategorySectionProps> = (props) => {
  useEffect(() => {
    props.fetchCategories();
  }, []);

  const offerDropDownFormat = (category: any) => {
    return {
      value: category.id,
      label: formatCategoryLabel(category)
    };
  }

  const handleOfferCategoryChange = (e: any) => {
    const { formikProps } = props;
    const values = formikProps.values;

    let offerCategory1 = values.offerCategory1;

    let offerCategory2 =
        e.target.name === "offerCategory2"
            ? e.target.value
            : values.offerCategory2;

    if (e.target.name === "offerCategory1") {
      offerCategory2 = undefined;
      offerCategory1 = e.target.value;
    }

    formikProps.setValues({
      ...values,
      offerCategory1,
      offerCategory2
    });
  };

  const { formikProps } = props;
  const { categories } = props;
  const mappedCategories = categories.map(offerDropDownFormat);

  const categoryValue = get(formikProps.values, "offerCategory1");
  const categoryFilters = categoryValue
      ? getChildCategoriesFromCategoryId(categories, categoryValue).map(
          offerDropDownFormat
      )
      : [];

  const qualifier = get(formikProps.values, "qualifier");
  const allowCategoryFilter = qualifier
      ? ["product", "custom", "category", "fuel"].includes(qualifier)
      : false;

  return (
      <Grid
          item={true}
          xs={12}
          style={{ marginTop: "1rem", marginBottom: "1rem" }}
      >
        <Paper elevation={2} style={{ padding: "1rem", width: "100%" }}>
          <Typography
              variant="h4"
              component="h4"
              paragraph={true}
              gutterBottom={true}
          >
            Offer Category
          </Typography>
          <Grid container={true} spacing={3}>
            <Grid item={true} xs={3}>
              <CustomSelectWidget
                  type="select"
                  name="offerCategory1"
                  title={FIELDS_CONFIG.offerCategory1.title}
                  options={mappedCategories}
                  onChange={handleOfferCategoryChange}
              />
            </Grid>
            {!isEmpty(categoryFilters) && allowCategoryFilter && (
                <Grid item={true} xs={3}>
                  <CustomSelectWidget
                      type="select"
                      name="offerCategory2"
                      title={FIELDS_CONFIG.offerCategory2.title}
                      options={categoryFilters}
                      onChange={handleOfferCategoryChange}
                      optional={true}
                      canSelectEmptyValue={true}
                  />
                </Grid>
            )}
            <Grid item={true} xs={3} />
          </Grid>
        </Paper>
      </Grid>
  );
}

const mapStateToProps = (state: IRootState) => ({
  categories: state.offers.categories || []
});

const mapDispatchToProps = (
    dispatch: ThunkDispatch<IRootState, void, Action>
) => ({
  fetchCategories: () => {
    dispatch(fetchCategoriesAsync());
  }
});

export default connect(
    mapStateToProps,
    mapDispatchToProps
)(OfferSubmissionOfferCategorySection);