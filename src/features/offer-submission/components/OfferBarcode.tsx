import { createStyles, Theme, withStyles, WithStyles } from "@material-ui/core";
import React, { PureComponent } from "react";
import barcodeService from "../../../shared/services/barcode.service";
import { MechanismType } from "../../../validation/validator";
import { BARCODE_MECHANISM_TYPES } from "../../../shared/constants";

interface IOfferBarcodeProps extends WithStyles {
  mechType?: string | undefined;
  mechValue?: string | undefined;
}

class OfferBarcode extends PureComponent<IOfferBarcodeProps> {
  public barcodeDivRef = React.createRef<SVGSVGElement>();

  public componentDidMount() {
    // Note: There will only be one mechanism in V1 Post for MVP
    if (
      this.props.mechType &&
      BARCODE_MECHANISM_TYPES.includes(this.props.mechType) &&
      this.props.mechValue
    ) {
      const barcodeType = this.props.mechType.substr("barcode".length);
      barcodeService.generateBarcode(
        this.barcodeDivRef.current,
        barcodeType,
        this.props.mechType as MechanismType,
        this.props.mechValue
      );
    }
  }

  public render() {
    const { mechType } = this.props;

    if (!mechType || !BARCODE_MECHANISM_TYPES.includes(mechType)) {
      return null;
    }

    return <svg ref={this.barcodeDivRef} />
  }
}
const styles = createStyles(() => ({}));
export default withStyles(styles)(OfferBarcode);
