import { createSty<PERSON>, Grid, IconB<PERSON>on, Theme, Tooltip, Typography, withStyles, WithStyles } from "@material-ui/core";
import CopyIcon from "@material-ui/icons/FileCopy";
import classNames from "classnames";
import copy from "clipboard-copy";
import { get } from "lodash";
import moment from "moment-timezone";
import React, { PureComponent, Ref } from "react";
import { BB8Button } from "../../../BB8";
import { IPartner } from "../../../models/Partner";
import { IUser } from "../../../models/User";
import { IOfferFormModel, OfferStatus, OfferType } from "../../../validation/validator";
import { Authorizer } from "../../auth/components/Authorizer";
import ConfirmationModal from "./ConfirmationModal";
import DetailsViewCard from "./DetailsViewCard";
import "./OfferBlock.scss";
import OfferDetailsList from "./OfferDetailsList";
import { OfferActionType } from "./OffersTable";
import PartnerSummaryCard from "./PartnerSummaryCard";

interface IOfferBlockProps extends WithStyles {
  offer: IOfferFormModel;
  onExportToPdfClick: (ref: any) => void;
  onMenuAction: (action: OfferActionType, data: any) => void;
  user: IUser;
  partner: IPartner;
  shouldHideButtons: boolean;
  editOnly: boolean;
  wasPublished: boolean;
}

interface IOfferPreviewState {
  isDeleteRequested: boolean;
  isInProgressDelection: boolean;
  isActiveChangeRequested: boolean;
  showConfirmationModalForPublish: boolean;
}

const initialState: IOfferPreviewState = {
  isDeleteRequested: false,
  isInProgressDelection: false,
  isActiveChangeRequested: false,
  showConfirmationModalForPublish: false
};

class OfferBlock extends PureComponent<IOfferBlockProps, IOfferPreviewState> {
  private ref: Ref<HTMLDivElement>;
  public state = initialState;

  constructor(props: any) {
    super(props);
    this.ref = React.createRef();
  }
  public hasPermission = (permission: string): boolean => {
    return Authorizer.Instance.checkPermissions([permission], this.props.user);
  };

  private toggleIsDeleteRequested = () => {
    this.setState({ isDeleteRequested: !this.state.isDeleteRequested });
  };

  private toggleIsActiveChangeRequested = () => {
    this.setState({ isActiveChangeRequested: !this.state.isActiveChangeRequested });
  };

  public render() {
    const {
      classes,
      offer,
      shouldHideButtons,
      editOnly,
      partner,
      wasPublished
    } = this.props;
    const { isDeleteRequested, isActiveChangeRequested } = this.state;

    // Note: There will only be one mechanism in V1 Post for MVP
    const mechType = get(offer, "mechanisms[0].mechanismType");

    const canCreateOffer = this.hasPermission("CREATE_OFFER");
    const canEditOffer = this.hasPermission("EDIT_OFFER");
    const canPublishOffer = this.hasPermission("PUBLISH_OFFER");
    const offerId = get(offer, "id");
    const agilityID: string = get(offer, "integrations[0].systemCode");
    const headerColour: string = this.getHeaderColor(offer, classes);
    const bulkName = get(offer, "bulkName");
    const partnerId = get(offer, "partnerId")
    const isOfferCustom = get(offer, "offerType") === OfferType.Custom;
    const offerStatus = get(offer, "status") || '';
    const isOfferStatusUpdated = offerStatus.toLowerCase() === OfferStatus.Updated.toLowerCase();
    const isOfferExpired = (): boolean => {
      if (new String(offer.status).toLowerCase() != OfferStatus.Published.toLowerCase()) {
        return false;
      } else {
        let currentDate = moment.utc();
        let endDate = moment(offer.endDate);
        return endDate.isBefore(currentDate)
      }
    }

    const getOfferStatus = () => {
      if(offer.active === false) return "DISABLED";

      if(!wasPublished) {
        const status = get(offer, "status");
        return isOfferStatusUpdated ? <span className={classes.textOfferStatusUpdated}>CHANGES PENDING</span>: status;
      }
    }

    return (
      <div className="offer-block-container" ref={this.ref}>
        <div className={`offer-header ` + headerColour}>
          <div>
            <Typography
              className={classes.textPrimary}
              variant="h6"
              gutterBottom={true}
            >
              Offer status: {wasPublished && "PUBLISHED"}{" "}
              {getOfferStatus()}
            </Typography>
            <Typography
              className={classes.textPrimary}
              align="left"
              variant="subtitle1"
            >
              {get(offer, "awardShort.en-US")}
            </Typography>
            <Typography
              className={classes.textPrimary}
              variant="subtitle1"
              align="left"
            >
              {get(offer, "qualifierShort.en-US")}
            </Typography>
            <Typography
              className={classes.textPrimary}
              variant="h6"
              align="left"
            >
              Targeting: {get(offer, "massOffer", true) ? "Mass" : get(offer, "eventBasedOffer", true)? "Targeted (EBO)" : "Targeted" }
            </Typography>
          </div>
          <div className="offer-summary">
            <Typography
              className={classes.textPrimary}
              variant="subtitle2"
              align="left"
            >
              Created by: {get(offer, "createdBy")}
            </Typography>
            <Typography
              className={classes.textPrimary}
              variant="subtitle2"
              align="left"
            >
              {get(offer, "publishedBy") &&
                "Published by: " + get(offer, "publishedBy")}
            </Typography>
            <Typography
              className={classes.textPrimary}
              variant="subtitle2"
              align="left"
            >
              {get(offer, "updatedBy") &&
                "Updated by: " + get(offer, "updatedBy")}
            </Typography>
            <Grid
              direction="row"
              container={true}
              justify="flex-start"
              alignItems="center"
            >
              <Typography className={classes.textPrimary} variant="subtitle2">
                Internal Offer ID: {offerId}
              </Typography>
              {!shouldHideButtons && (
                  <Tooltip title="Copy ID">
                    <IconButton
                        aria-label="Copy"
                        className={classes.copyIcon}
                        onClick={() => copy(offerId)}
                    >
                      <CopyIcon />
                    </IconButton>
                  </Tooltip>
              )}
            </Grid>
            {agilityID && (get(offer, "status") || OfferStatus.Draft).toLowerCase() != OfferStatus.Draft && (
              <Grid
                direction="row"
                container={true}
                justify="flex-start"
                alignItems="center"
              >
                <Typography className={classes.textPrimary} variant="subtitle2">
                  Agility ID: {agilityID}
                </Typography>
                {!shouldHideButtons && (
                  <Tooltip title="Copy ID">
                    <IconButton
                      aria-label="Copy"
                      className={classes.copyIcon}
                      onClick={() => copy(agilityID)}
                    >
                      <CopyIcon />
                    </IconButton>
                  </Tooltip>
                )}
              </Grid>
            )}
            <Grid
              direction="row"
              container={true}
              justify="flex-start"
              alignItems="center"
            >
              <Typography className={classes.textPrimary} variant="subtitle2">
                Partner ID: {partnerId}
              </Typography>
            </Grid>
            {bulkName && (<Grid
              direction="row"
              container={true}
              justify="flex-start"
              alignItems="center"
            >
              <Typography className={classes.textPrimary} variant="subtitle2">
                {`Bulk Name: ${bulkName}`}
              </Typography>
            </Grid>)}
          </div>
          {(
            <div
              className={classNames({
                "offer-button-1": !editOnly,
                "offer-button-2": editOnly,
                hidden: shouldHideButtons
              })}
            >
              {!editOnly && !isDeleteRequested && !isActiveChangeRequested && (
                <BB8Button
                  color="primary"
                  variant="contained"
                  fullWidth={true}
                  className={classes.button}
                  onClick={() => this.handleMenuItemClick(OfferActionType.Export)}
                >
                  Export to PDF
                </BB8Button>
              )}
              {canEditOffer && !isOfferExpired() && !isDeleteRequested && !isActiveChangeRequested && (
                <BB8Button
                  color="primary"
                  variant="contained"
                  fullWidth={true}
                  className={classes.button}
                  onClick={() => this.handleMenuItemClick(OfferActionType.Edit)}
                >
                  Edit
                </BB8Button>
              )}
              {!isDeleteRequested && !isActiveChangeRequested &&
                (get(offer, "status") || '').toLowerCase() === OfferStatus.Draft && (
                  <BB8Button
                    variant="contained"
                    color="primary"
                    fullWidth={true}
                    onClick={this.toggleIsDeleteRequested}
                  >
                    Delete
                  </BB8Button>
                )}
              {isDeleteRequested &&
                (get(offer, "status") || '').toLowerCase() === OfferStatus.Draft && (
                  <BB8Button
                    variant="contained"
                    color="primary"
                    fullWidth={true}
                    onClick={this.toggleIsDeleteRequested}
                    className={classes.cancelButton}
                  >
                    Cancel Delete
                  </BB8Button>
                )}
              {isActiveChangeRequested && offer.active &&
                (new String(offer.status).toLowerCase() !==
                  OfferStatus.Draft.toLowerCase()) &&
                canCreateOffer && (
                  <BB8Button
                    variant="contained"
                    color="primary"
                    fullWidth={true}
                    onClick={this.toggleIsActiveChangeRequested}
                    className={classes.cancelButton}
                  >
                    Cancel Disable
                  </BB8Button>
                )}
              {isActiveChangeRequested && !offer.active &&
                (new String(offer.status).toLowerCase() !==
                  OfferStatus.Draft.toLowerCase()) &&
                canCreateOffer && (
                  <BB8Button
                    variant="contained"
                    color="primary"
                    fullWidth={true}
                    onClick={this.toggleIsActiveChangeRequested}
                    className={classes.cancelButton}
                  >
                    Cancel Enable
                  </BB8Button>
                )}
            </div>
          )}
          {!editOnly && (
            <div
              className={`offer-button-2 ${shouldHideButtons ? "hidden" : ""}`}
            >
              {offer.active &&
                new String(offer.status).toLowerCase() !==
                OfferStatus.Published.toLowerCase() &&
                canPublishOffer &&
                !wasPublished &&
                !isDeleteRequested &&
                !isActiveChangeRequested && (
                  <BB8Button
                    color="primary"
                    variant="contained"
                    fullWidth={true}
                    className={isOfferStatusUpdated ? classes.buttonOfferStatusUpdated :classes.button}
                    onClick={() => {
                      if (isOfferCustom) {
                        this.setState({ showConfirmationModalForPublish: true })
                      }
                      else {
                        this.handleMenuItemClick(OfferActionType.Publish)
                      }
                    }
                    }
                  >
                    Publish
                  </BB8Button>
                )}
              {
                offer.active &&
                new String(offer.status).toLowerCase() !==
                OfferStatus.Published.toLowerCase() &&
                canPublishOffer &&
                !wasPublished && this.state.showConfirmationModalForPublish && <ConfirmationModal isOpen={this.state.showConfirmationModalForPublish} onConfirmationAction={() => this.handleMenuItemClick(OfferActionType.Publish)} onToggleConfirmation={this.closeModal} />
              }
              {offer.active &&
                (new String(offer.status).toLowerCase() !==
                  OfferStatus.Updated.toLowerCase() ||
                  wasPublished) &&
                canCreateOffer &&
                !isDeleteRequested &&
                !isActiveChangeRequested && (
                  <BB8Button
                    color="primary"
                    variant="contained"
                    fullWidth={true}
                    className={classes.button}
                    onClick={() =>
                      this.handleMenuItemClick(OfferActionType.Duplicate)
                    }
                  >
                    Duplicate
                  </BB8Button>
                )}
              {!offer.active && !isActiveChangeRequested &&
                (new String(offer.status).toLowerCase() !==
                  OfferStatus.Draft.toLowerCase()) && !isOfferExpired() &&
                canCreateOffer && (
                  <BB8Button
                    color="primary"
                    variant="contained"
                    fullWidth={true}
                    className={classes.button}
                    onClick={this.toggleIsActiveChangeRequested}
                  >
                    Enable
                  </BB8Button>
                )}
              {offer.active && !isActiveChangeRequested &&
                (new String(offer.status).toLowerCase() !==
                  OfferStatus.Draft.toLowerCase()) && !isOfferExpired() &&
                canCreateOffer && (
                  <BB8Button
                    color="primary"
                    variant="contained"
                    fullWidth={true}
                    className={classes.button}
                    onClick={this.toggleIsActiveChangeRequested}
                  >
                    Disable
                  </BB8Button>
                )}
              {isDeleteRequested &&
                (get(offer, "status") || '').toLowerCase() === OfferStatus.Draft && (
                  <BB8Button
                    variant="contained"
                    color="primary"
                    fullWidth={true}
                    onClick={() => this.handleMenuItemClick(OfferActionType.Delete)}
                    className={classes.confirmButton}
                  >
                    Confirm Delete
                  </BB8Button>
                )}
              {isActiveChangeRequested && offer.active &&
                (new String(offer.status).toLowerCase() !==
                  OfferStatus.Draft.toLowerCase()) &&
                canCreateOffer && (
                  <BB8Button
                    variant="contained"
                    color="primary"
                    fullWidth={true}
                    onClick={() => this.handleMenuItemClick(OfferActionType.Disable)}
                    className={classes.confirmButton}
                  >
                    Confirm Disable
                  </BB8Button>
                )}
              {isActiveChangeRequested && !offer.active &&
                (new String(offer.status).toLowerCase() !==
                  OfferStatus.Draft.toLowerCase()) &&
                canCreateOffer && (
                  <BB8Button
                    variant="contained"
                    color="primary"
                    fullWidth={true}
                    onClick={() => this.handleMenuItemClick(OfferActionType.Enable)}
                    className={classes.confirmButton}
                  >
                    Confirm Enable
                  </BB8Button>
                )}
            </div>
          )}
        </div>
        <div className="offer-details">
          {" "}
          <OfferDetailsList offer={offer} />
        </div>

        <div className="details-card-en">
          <Typography variant="subtitle1"> Detailed view (EN)</Typography>
          <DetailsViewCard
            partner={partner}
            offer={offer}
            lang="en"
            mechType={mechType}
          />
        </div>
        <div className="details-card-fr">
          <Typography variant="subtitle1"> Detailed view (FR)</Typography>
          <DetailsViewCard
            partner={partner}
            offer={offer}
            lang="fr"
            mechType={mechType}
          />
        </div>
        <div className="partner-cards">
          <Typography variant="subtitle1">
            {" "}
            Feed / Partner page view (EN)
          </Typography>
          <PartnerSummaryCard offer={offer} lang="en" mechType={mechType} />
          <Typography variant="subtitle1"> Card view (EN)</Typography>
          <PartnerSummaryCard
            offer={offer}
            lang="en"
            mechType={mechType}
            pageView={true}
            partner={partner}
          />
          <Typography variant="subtitle1">
            {" "}
            Feed / Partner page view (FR)
          </Typography>
          <PartnerSummaryCard offer={offer} lang="fr" mechType={mechType} />
          <Typography variant="subtitle1"> Card view (FR)</Typography>
          <PartnerSummaryCard
            offer={offer}
            lang="fr"
            mechType={mechType}
            pageView={true}
            partner={partner}
          />
        </div>
      </div>
    );
  }
  private getHeaderColor(offer: IOfferFormModel, classes: any): string {
    const color: string =
      offer.active === false
        ? classes.disabledHeader
        : offer.massOffer
          ? `offer-header-blue`
          : `offer-header-green`;

    return color;
  }

  private handleMenuItemClick = (actionType: OfferActionType) => {
    const { onMenuAction, offer } = this.props;
    onMenuAction(actionType, offer);
  }

  private closeModal = () => {
    this.setState({ showConfirmationModalForPublish: false });
  }
}

const styles = createStyles((theme: Theme) => ({
  grid: {
    marginTop: theme.spacing(4),
    minWidth: 250
  },
  button: {
    marginBottom: ".5em"
  },
  gridColor: {
    backgroundColor: "blue",
    marginTop: theme.spacing(4),
    minWidth: 250
  },
  textPrimary: {
    color: "white",
    margin: 0
  },
  textOfferStatusUpdated: {
    color: "#683E68",
  },
  buttonOfferStatusUpdated: {
    backgroundColor: "#683E68",
    marginBottom: ".5em"
  },
  copyIcon: {
    marginRight: "0.2em",
    marginLeft: "0.2em",
    color: "white",
    padding: "0.2em"
  },
  disabledHeader: {
    backgroundColor: "#939393"
  },
  cancelButton: {
    backgroundColor: "#D30E8B",
    "&:hover": {
      backgroundColor: "#DC3EA2"
    }
  },
  confirmButton: {
    backgroundColor: "#44A648",
    "&:hover": {
      backgroundColor: "#69B86D"
    },
    marginRight: "2em"
  }
}));
export default withStyles(styles)(OfferBlock);
