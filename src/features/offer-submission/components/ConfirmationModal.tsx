import { createStyles, IconButton, Modal, Theme, Typography, withStyles, WithStyles, } from "@material-ui/core";
import React, { PureComponent } from "react";
import { BB8Button } from "../../../BB8";

interface IConfirmationModalProps extends WithStyles {
  onConfirmationAction: () => void;
  onToggleConfirmation: () => void;
  isOpen: boolean;
}

interface IConfirmationModalState {
  isOpen: boolean;
}

const initialState: IConfirmationModalState = {
  isOpen: false
};

class ConfirmationModal extends PureComponent<
  IConfirmationModalProps,
  IConfirmationModalState
> {
  public state = initialState;
  public render() {
    const {
      classes,
      onConfirmationAction,
      isOpen = false,
    } = this.props;

    this.setState({ isOpen: this.props.isOpen });

    return (
      <Modal
        open={this.state.isOpen}
        onClose={this.handleClose}
        aria-labelledby="simple-modal-title"
        aria-describedby="simple-modal-description"
        className={classes.modal}
        BackdropProps={{ style: { backgroundColor: "rgba(0, 0, 0, 0.5)", zIndex: -1 }, }}
      >
        <div className={classes.modalContent} >
          <IconButton onClick={this.handleClose} className={classes.closeButton}>
            x
          </IconButton>
          <Typography variant="subtitle1">
            We noticed that you are using "Custom" offer type.
          </Typography>
          <br />
          <Typography variant="subtitle1">
            By clicking "I agree, Publish my offer(s)", you confirm that you have reviewed and you adhere to the guidelines: <a href="https://loyaltyone.box.com/s/********************************" target="_blank"> Accessibility and Usability Guide</a>
          </Typography>
          <div className={classes.buttons}>
            <BB8Button
              variant="contained"
              color="primary"
              onClick={() => {
                this.handleClose();
              }}
              className={classes.cancelButton}
            >
              Go back to Draft
          </BB8Button>
            <BB8Button
              variant="contained"
              color="primary"
              onClick={() => {
                onConfirmationAction();
                this.handleClose();
              }}
              className={classes.confirmButton}
            >
              I agree, Publish my offer(s)
        </BB8Button>
          </div>
        </div>
      </ Modal>
    );
  }

  private handleClose = () => {
    this.setState(initialState);
    this.props.onToggleConfirmation();
  }
}

const styles = createStyles((theme: Theme) => ({
  cancelButton: {
    backgroundColor: "#FF9800",
    "&:hover": {
      backgroundColor: "#FFBE77"
    },
    marginRight: "1em"
  },
  confirmButton: {
    backgroundColor: "#44A648",
    "&:hover": {
      backgroundColor: "#69B86D"
    },
  },
  modal: {
    display: 'block',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: "white",
    margin: '225px auto',
    width: "50%",
    height: "max-content",
    border: "1px solid black",
  },
  modalContent: {
    position: "absolute",
    alignItems: 'center',
    backgroundColor: "white",
    padding: "25px"
  },
  buttons: {
    marginTop: "10px",
    textAlign: "right",
    marginRight: "2em"
  },
  closeButton: {
    position: "absolute",
    right: 0,
    marginRight: "2em",
    height: "2em",
    width: "2em",
    fontSize: "1em"
  }
}));

export default withStyles(styles)(ConfirmationModal);
