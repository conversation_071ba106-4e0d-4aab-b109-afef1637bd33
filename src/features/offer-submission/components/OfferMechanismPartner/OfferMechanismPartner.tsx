import { Avatar, CardActions, createStyles, Theme, Typography, WithStyles, withStyles, } from "@material-ui/core";
import { get } from "lodash";
import React, { PureComponent } from "react";
import { IOfferFormModel } from "../../../../validation/validator";
import loadedNoAction from "../../../../shared/assets/images/loaded-noAction.png";
import masterCard from "../../../../shared/assets/images/masterCard.png";
import globe from "../../../../shared/assets/images/offer-tag-online.svg";
import person from "../../../../shared/assets/images/person.svg";
import heartCyan from "../../../../shared/assets/images/heart-outlined-cyan.svg";
import barcode from "../../../../shared/assets/images/offer-tag-barcode.svg";
import { BARCODE_MECHANISM_TYPES } from "../../../../shared/constants";
import { getCardTypeLabelPartnerMech } from "../../helpers/getCardTypeLabel"

interface IOfferMechanismDetailedProps extends WithStyles {
  lang: string;
  offer: IOfferFormModel;
  mechType?: string;
  partnerLogo?: string;
}

class OfferMechanismPartner extends PureComponent<IOfferMechanismDetailedProps> {
  renderMechanismContent = () => {
    const { classes, offer, mechType, lang } = this.props;

    if (!mechType) {
      return null
    }

    const localization = lang === "fr" ? "fr-CA" : "en-US";
    const offerCardType = offer.cardType ? new Set(offer.cardType) : null;

    if (offer.programType === "cardlinked" && mechType === "noAction") {
      const cardDynamicTextLabel = getCardTypeLabelPartnerMech(offerCardType);

      return <CardLinkedNoAction classes={classes} localization={localization} cardDynamicTextLabel={cardDynamicTextLabel} />;
    }

    if (offer.programType === "cardlinked" && mechType === "optIn") {
      const cardDynamicTextLabel = getCardTypeLabelPartnerMech(offerCardType, true);

      return <CardLinkedOptIn classes={classes} localization={localization} cardDynamicTextLabel={cardDynamicTextLabel} />;
    }

    if (mechType === "optIn") {
      return <OptIn classes={classes} localization={localization} />;
    }

    if (mechType === "noAction" || mechType === "load+go" || mechType === "scanReceipt") {
      return <NoActionOrLoadGo classes={classes} localization={localization} />;
    }

    if (mechType === "button") {
      return <ButtonMechanism classes={classes} localization={localization} offer={offer} />;
    }

    if (mechType === "plu" || mechType === "couponCode") {
      return <PluCouponCode classes={classes} localization={localization} mechType={mechType} offer={offer} />;
    }

    if (BARCODE_MECHANISM_TYPES.includes(mechType)) {
      return <BarcodeMechanism classes={classes} localization={localization} mechType={mechType} offer={offer} />;
    }
  };

  render() {
    return this.renderMechanismContent()
  }
}

const CardLinkedNoAction = ({ classes, localization, cardDynamicTextLabel }: any) => {
  const dynamicTextLabel = get(cardDynamicTextLabel, localization);

  return (
    <CardActions disableSpacing={true} className={classes.actions}>
      <div className={classes.buttonMechanic}>
        <div className={classes.barcode}>
          <Avatar
            aria-label={dynamicTextLabel}
            src={masterCard}
            className={classes.optInIcon}
          />
          <Typography variant="caption" className={classes.customCaption}>
            {dynamicTextLabel}
          </Typography>
        </div>
      </div>
    </CardActions>
  )
};

const CardLinkedOptIn = ({ classes, localization, cardDynamicTextLabel }: any) => {
  const dynamicTextLabel = get(cardDynamicTextLabel, localization);

  return (
    <CardActions disableSpacing={true} className={classes.actions}>
      <div className={classes.buttonMechanic}>
        <div className={classes.barcode}>
          <Avatar
            aria-label={dynamicTextLabel}
            src={masterCard}
            className={classes.optInIcon}
          />
          <Typography variant="caption" className={classes.customCaption}>
            {dynamicTextLabel}
          </Typography>
        </div>
      </div>
    </CardActions>
  )
};

const OptIn = ({ classes, localization }: any) => {
  const optInLabels = {
    "en-US": "Opt in",
    "fr-CA": "Activer",
  };

  const optInLabel18n = get(optInLabels, localization);

  return (
    <CardActions disableSpacing={true} className={classes.actions}>
      <div className={classes.buttonMechanic}>
        <div className={classes.barcode}>
          <Avatar
            aria-label={optInLabel18n}
            src={heartCyan}
            className={classes.optInIcon}
          />
          <Typography variant="caption" className={classes.customCaption}>
            {optInLabel18n}
          </Typography>
        </div>
      </div>
    </CardActions>
  );
}

const NoActionOrLoadGo = ({ classes, localization }: any) => {
  const noAction = {
    "en-US": "Ready to use",
    "fr-CA": "Prêt à l'emploi"
  };

  return (
    <CardActions disableSpacing={true} className={classes.actions}>
      <Avatar
        src={loadedNoAction}
        aria-label="Check mark"
        className={classes.icon}
      />

      <Typography variant="body2" className={classes.customCaption}>
        {get(noAction, localization)}
      </Typography>
    </CardActions>
  );
}

const ButtonMechanism = ({ classes, localization, offer }: any) => {
  const buttonLabels = {
    "en-US": "Online",
    "fr-CA": "En ligne"
  };

  const buttonLabel = get(buttonLabels, localization);

  return (
    <CardActions className={classes.actions}>
      <div className={classes.buttonMechanic}>
        <div className={classes.barcode}>
          <Avatar src={globe} className={classes.icon} />
          <Typography variant="caption" className={classes.customCaption}>
            {buttonLabel}
          </Typography>
        </div>
      </div>
    </CardActions>
  );
};

const PluCouponCode = ({ classes, localization, mechType }: any) => {
  const useCodeValues = {
    "en-US": "Use Code",
    "fr-CA": "Utiliser le code"
  };

  const barcodeLabel = get(useCodeValues, localization);

  return (
    <CardActions disableSpacing={true} className={classes.actions}>
      <Avatar
        src={person}
        aria-label="Check mark"
        className={classes.icon}
      />

      <Typography className={classes.customCaption}>
        {barcodeLabel}
      </Typography>
    </CardActions>
  )
};

const BarcodeMechanism = ({ classes, localization, mechType, offer }: any) => {
  const barcodeValues = {
    "en-US": "Scan barcode",
    "fr-CA": "Scanner le code"
  }

  const barcodeLabel = get(barcodeValues, localization);

  return (
    <CardActions disableSpacing={true} className={classes.actions}>
      <Avatar
        src={barcode}
        aria-label="Check mark"
        className={classes.icon}
      />

      <Typography className={classes.customCaption}>
        {barcodeLabel}
      </Typography>
    </CardActions>
  )
};

const styles = createStyles((theme: Theme) => ({
  actions: {
    padding: '3px 5px',
    border: "1px solid #C7D2DC",
    alignItems: "center",
    borderRadius: '16px',
    justifyContent: "center",
  },
  barcode: {
    alignItems: "center",
    justifyContent: "center",
    display: "flex"
  },
  icon: {
    width: "0.9em",
    marginRight: "0.3em",
    border: 0,
    fontSize: "1rem",
    height: "auto",
  },
  optInIcon: {
    width: "1em",
    marginRight: "0.3em",
    border: 0,
    fontSize: "1rem",
    height: "auto",
  },
  mechanism: {
    padding: "16px !important",
    borderTop: `1px solid ${theme.palette.grey[300]}`,
    borderBottom: `1px solid ${theme.palette.grey[300]}`,
  },
  button: {
    padding: ".4em",
    backgroundColor: "#0A6FB3",
    borderRadius: "0.2em",
    color: "#f1f2f2",
    boxShadow: "none",
    fontSize: "18px",
    textTransform: "none",
    marginTop: "0.5em",
    "&:hover": {
      backgroundColor: "#0A6FB3",
      boxShadow: "none"
    },
    "&:focus": {
      boxShadow: "none"
    }
  },
  buttonMechanic: {
    display: "block"
  },
  tabArrowIcon: {
    width: "15px",
    height: "15px",
    borderRadius: "0",
    marginLeft: "5px"
  },
  cardLinkedCustomHeaderSection: {
    alignItems: "center",
    justifyContent: "center",
    display: "flex",
    borderBottom: "0.5px solid #E5E5E5",
    paddingBottom: "0.7rem"
  },
  cardLinkedCustomCaptionInfo: {
    lineHeight: "16px",
    fontSize: "0.6rem"
  },
  cardLinkedSection: {
    alignItems: "center",
    justifyContent: "center",
    display: "flex",
    padding: "0.075rem"
  },
}));

export default withStyles(styles)(OfferMechanismPartner);