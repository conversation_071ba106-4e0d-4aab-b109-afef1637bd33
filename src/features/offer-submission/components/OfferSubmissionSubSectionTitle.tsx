import { Grid, Typography } from "@material-ui/core";
import { TypographyProps } from "@material-ui/core/Typography";
import React, { memo } from "react";

export const OfferSubmissionSubSectionTitle: React.FunctionComponent<{
  title: string;
  optional?: boolean;
}> = memo(({ title, optional }) => {
  const typographyProps = {
    variant: "h5",
    component: "h5",
    paragraph: true
  } as TypographyProps;
  return (
    <Grid item={true} container={true} direction="row">
      <Typography {...typographyProps}>{title}</Typography>
      {optional && (
        <Typography {...typographyProps} color="primary">
          &nbsp;(Optional)
        </Typography>
      )}
    </Grid>
  );
});
