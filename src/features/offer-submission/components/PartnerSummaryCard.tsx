import {
  <PERSON><PERSON>,
  Card,
  createStyles,
  Grid,
  Theme,
  Typography,
  withStyles,
  WithStyles
} from "@material-ui/core";
import { get } from "lodash";
import React, { PureComponent } from "react";
import amCash from "../../../shared/assets/images/amCash.png";
import spend from "../../../shared/assets/images/spend.png";
import heart from "../../../shared/assets/images/heart-outlined-red.png";
import { generateRandomGUIDForCacheInvalidation } from "../../../shared/helpers";
import { IOfferFormModel, OfferType } from "../../../validation/validator";
import { OfferMechanismPartner } from "./OfferMechanismPartner";
import { IPartner } from "../../../models/Partner";

interface IOfferPreviewProps extends WithStyles {
  lang: string;
  offer: IOfferFormModel;
  mechType?: string | undefined;
  pageView?: boolean;
  partner?: IPartner;
}

class PartnerSummaryCard extends PureComponent<IOfferPreviewProps> {
  private imageFallback(e: any) {
    const { offer } = this.props;
    const fallbackImage = (get(offer, "offerType") === ("amCash" as OfferType) ? amCash : spend);
    e.target.src = fallbackImage + generateRandomGUIDForCacheInvalidation();
  }

  public render() {
    const { classes, offer, lang, partner } = this.props;
    const localization = lang === "fr" ? "fr-CA" : "en-US";

    const cardMedia =
      (get(offer, "image." + localization + ".path")
        ? get(offer, "image." + localization + ".path")
        : get(offer, "image.en-US.path")) +
      generateRandomGUIDForCacheInvalidation();

    const promoTagName = get(offer, `tagNames[0].${localization}`);

    const partnerLogo = get(partner, "fullLogo[1].file.url", undefined) === undefined
      ? "fullLogo[0].file.url"
      : "fullLogo[1].file.url";

    return (
      <Card className={classes.cardWrapper}>
        <div className={classes.card}>
          <Grid container alignItems='center'>
            <img
              src={partnerLogo}
              onError={this.imageFallback.bind(this)}
              className={classes.image}
              crossOrigin="anonymous"
              title={`${offer.partnerName} product logo`}
            />

            <Typography variant="body2" classes={{ root: classes.partnerText }}>
              {offer.partnerName}
            </Typography>
          </Grid>

          <Grid container spacing={1} wrap='nowrap' justify="space-between" alignItems="center">
            <Grid item>
              <Typography variant='body1' style={{ marginTop: 5, fontWeight: 700 }}>
                {get(offer, "awardShort." + localization)}
              </Typography>

              <Typography variant="body2" gutterBottom className={classes.qualifierShort}>
                {get(offer, "qualifierShort." + localization)}
              </Typography>
            </Grid>

            <Grid item style={{ paddingRight: 15 }}>
              <img
                src={cardMedia}
                width='60px'
                onError={this.imageFallback.bind(this)}
                className={classes.offerImage}
                crossOrigin="anonymous"
                title={get(offer, "awardShort." + localization)}
              />
            </Grid>
          </Grid>

          <Grid
            container
            wrap="nowrap"
            spacing={1}
            justify='space-between'
            alignItems='center'
            style={{ marginTop: '1rem' }}
          >
            <Grid item>
              <OfferMechanismPartner
                lang={this.props.lang}
                offer={this.props.offer}
                mechType={this.props.mechType}
              />
            </Grid>

            <Grid item>
              <Avatar
                src={heart}
                aria-label="Save offer"
                classes={{
                  root: classes.heart,
                  img: classes.heartImage
                }}
              />
            </Grid>
          </Grid>
        </div>

        {promoTagName && (
          <div style={{ padding: '5px 15px', backgroundColor: '#FFEBF4', borderRadius: '0 0 8px 8px' }}>
            <Typography variant="body2" style={{ fontWeight: 700 }}>
              {promoTagName}
            </Typography>
          </div>
        )}
      </Card>
    );
  }
}

const styles = createStyles((theme: Theme) => ({
  cardWrapper: {
    marginTop: ".5em",
    boxShadow: "none",
    marginBottom: ".5em",
    borderRadius: '8px',
  },
  card: {
    padding: "15px"
  },
  image: {
    width: "32px",
    height: "32px",
    alignSelf: "center",
    objectFit: "scale-down"
  },
  mechanism: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "center",
    borderTop: `1px solid ${theme.palette.grey[200]}`,
    padding: 0,
    paddingTop: "5px",
    paddingBottom: "15px !important"
  },
  mechanismCard: {
    padding: "0"
  },
  couponValue: {
    fontWeight: "1000",
    fontSize: "1.5em",
    padding: "0"
  },
  chip: {
    marginTop: 0,
    marginBottom: ".5em",
    borderRadius: "1em",
    height: "1.5em",
    width: "fit-content",
    fontSize: `${theme.typography.caption.fontSize}`,
    backgroundColor: `${theme.palette.grey[200]}`,
    fontWeight: `${theme.typography.fontWeightMedium}`,
    transform: "translateZ(1px)",
    color: "#5E5E5E",
  },
  chipLabel: {
    paddingLeft: "6px",
    paddingRight: "6px"
  },
  bookmarkImg: {
    marginTop: 0,
    width: "14px",
    height: "12px",
    padding: "0",
    marginLeft: "6px",
    backgroundColor: `${theme.palette.grey[200]}`
  },
  bookmarkProps: {
    width: "auto",
  },
  qualifierShort: {
    display: "-webkit-box",
    '-webkitBoxOrient': "vertical",
    '-webkitLineClamp': 3,
    overflow: "hidden",
  },
  partnerText: {
    color: '#384A5B',
    fontSize: '12px',
    fontWeight: 700,
    marginLeft: 10,
  },
  heart: {
    width: "20px",
    height: "20px",
    borderRadius: 0,
  },
  heartImage: {
    objectFit: "contain"
  },
}));

export default withStyles(styles)(PartnerSummaryCard);
