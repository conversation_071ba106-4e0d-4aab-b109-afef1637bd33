import { <PERSON>ton, Checkbox, Grid, Menu, MenuItem } from "@material-ui/core";
import { get } from "lodash";
import moment from "moment-timezone";
import React from "react";
import { connect } from "react-redux";
import { BB8CustomTable } from "../../../BB8";
import { IColProps } from "../../../BB8/components/BB8CustomTable";
import { ITemplateCellProps } from "../../../BB8/components/BB8Table";
import { IOfferCategory } from "../../../models/Category";
import { IOfferSort, OfferSortField, SortDirection } from "../../../models/OffersRequestParams";
import { getOfferTypeName } from "../../../models/OfferType";
import { IOfferPromotion } from "../../../models/Promotion";
import { withAuthStateToProps } from "../../../store";
import { IOfferFormModel, IOfferFormModelWithNames, OfferStatus } from "../../../validation/validator";
import { Authorizer } from "../../auth/components/Authorizer";
import { IAuthState } from "../../auth/types";
import { OffersTabs } from "../actions";
import { canBeEnabled, isExpiredTab, isPublishableState } from "../pages/MyOffers";
import { DATE_FULL_YEAR_TIME_SECONDS_FORMAT, DATE_TIME_FORMAT, getOffersWithReadableNames, } from "../utils";

export enum OfferActionType {
  Publish = "publish",
  Edit = "edit",
  Preview = "preview",
  Duplicate = "duplicate",
  Export = "export",
  Enable="enable",
  Disable="disable",
  Delete = "delete"
}

const MENU_CONFIG = {
  duplicate: {
    label: "Duplicate",
    permission: "CREATE_OFFER"
  },
  edit: {
    label: "Edit",
    permission: "EDIT_OFFER"
  },
  preview: {
    label: "Preview",
    permission: "VIEW_OFFER"
  },
  publish: {
    label: "Publish",
    permission: "PUBLISH_OFFER"
  },
  enable: {
    label: "Enable",
    permission: "ENABLE_OFFER"
  },
  disable: {
    label: "Disable",
    permission: "DISABLE_OFFER"
  },
  delete: {
    label: "Delete",
    permission: "DELETE_OFFER"
  }
};

interface IOffersTableProps extends IAuthState {
  offers: IOfferFormModel[];
  promotions: IOfferPromotion[];
  categories: IOfferCategory[];
  onOfferSelected: (offer: IOfferFormModel) => void;
  onOfferUnSelected: (offer: IOfferFormModel) => void;
  onOfferSelectAll: () => void;
  onOfferUnSelectAll: () => void;
  selectedOffers: Set<string>;
  sorting: IOfferSort;
  onSortChanged: (
    field: keyof IOfferFormModel,
    direction: SortDirection
  ) => void;
  onMenuAction: (action: OfferActionType, data: any) => void;
  tab: OffersTabs;
  rowsPerPage: number;
}

const DateFormatElement = () => (
  <small style={{ fontWeight: "lighter", fontStyle: "italic" }}>
    {DATE_TIME_FORMAT}
  </small>
);

const dateCellTemplate: React.FunctionComponent<ITemplateCellProps> = ({
  id,
  row
}) => {
  const val = get(row, id);
  const firstqualificationDate = get(row,"firstQualificationDate");
  const lastqualificationDate = get(row,"lastQualificationDate");
  if(val && firstqualificationDate && id === "startDate"){
    return <>
      <div style={{ lineHeight: '1.2'}}>
        <div>First Qualification Date:</div>
        <div>{moment.utc(val).format(DATE_FULL_YEAR_TIME_SECONDS_FORMAT)}</div>
      </div>
    </>;
  }
  if (val && lastqualificationDate && id === "endDate") {
    return <>
      <div style={{ lineHeight: '1.2'}}>
        <div>Offer live through:</div>
        <div>{moment.utc(val).format(DATE_FULL_YEAR_TIME_SECONDS_FORMAT)}</div>
      </div>
    </>;
  }
  if (val) {
    return <>{moment.utc(val).format(DATE_FULL_YEAR_TIME_SECONDS_FORMAT)}</>;
  }
  return null;
};

const getCategoryNamesAsString = (data: IOfferFormModelWithNames) => {
  let categoryNames: string = "";

  if (data.offerCategory1Name) {
    categoryNames += data.offerCategory1Name;
    if (data.offerCategory2Name) {
      categoryNames += ", " + data.offerCategory2Name;
      if (data.offerCategory3Name) {
        categoryNames += ", " + data.offerCategory3Name;
      }
    }
  }

  return categoryNames ? categoryNames : "N/A";
};

const detailsRowComponent = (data: IOfferFormModelWithNames) => (
  <Grid container={true}>
    <Grid item={true} xs={4}>
      <p>
        <strong>Bulk Name:</strong>&nbsp;
        {get(data, "bulkName", "N/A")}
      </p>
      <p>
        <strong>Promo Tag:</strong>&nbsp;
        {get(data, "tagNames[0].en-US", "N/A")}
      </p>
      <p>
        <strong>Targeting:</strong>&nbsp;{" "}
        {get(data, "massOffer", true) ? "Mass" : get(data, "eventBasedOffer", true)? "Targeted (EBO)" : "Targeted" }
      </p>
      <p>
        <strong>Offer Type:</strong>&nbsp;{" "}
        {getOfferTypeName(get(data, "offerType"))}
      </p>
      <p>
        <strong>Mechanism Type:</strong>&nbsp;
        {get(data, "mechanisms[0].mechanismType")}
      </p>
    </Grid>
    <Grid item={true} xs={4}>
      <p>
        <strong>Award + Qualifier Short:</strong>
      </p>
      <p>{get(data, "awardShort.en-US")}</p>
      <p>{get(data, "qualifierShort.en-US")}</p>
      <p>
        <strong>Categories:</strong>&nbsp;
        {getCategoryNamesAsString(data)}
      </p>
      <p>
        <strong>Region:</strong>&nbsp;
        {Array.prototype.join.call(get(data, "regions", []), ",")}
      </p>
    </Grid>
    {get(data, "eventBasedOffer",true)?
    <Grid item={true} xs={4}>
      <p>
        <strong>Event-based Offer</strong>
      </p>
      <p>
        <strong>Last Qualification Date: </strong>&nbsp;
        {get(data, "lastQualificationDate")}
      </p>
    </Grid>:("")}
  </Grid>
);

const OffersTable: React.FunctionComponent<IOffersTableProps> = ({
  offers,
  onOfferSelected,
  onOfferUnSelected,
  onOfferSelectAll,
  onOfferUnSelectAll,
  selectedOffers,
  onMenuAction,
  onSortChanged,
  sorting,
  tab,
  user,
  rowsPerPage,
  promotions,
  categories
}) => {
  const totalCount = offers.length;
  const checkedCount = selectedOffers.size;
  const anyChecked = checkedCount > 0;
  const allChecked = totalCount === checkedCount;
  const someChecked = !allChecked && anyChecked;

  function sortChanged(field: OfferSortField) {
    onSortChanged(field, sorting.direction === "asc" ? "desc" : "asc");
  }

  function hasPermission(permission: string): boolean {
    return Authorizer.Instance.checkPermissions([permission], user);
  }

  function handleMenuItemClick(actionType: OfferActionType, row: any) {
    return () => {
      onMenuAction(actionType, row);
    };
  }

  function renderMenuItem(actionType: Exclude<OfferActionType, OfferActionType.Export>, row: any) {
    const showMenuItem = hasPermission(MENU_CONFIG[actionType].permission);
    return (
      showMenuItem && (
        <MenuItem onClick={handleMenuItemClick(actionType, row)}>
          {MENU_CONFIG[actionType].label}
        </MenuItem>
      )
    );
  }

  const actionCellTemplate: React.FunctionComponent<ITemplateCellProps> = ({
    row
  }) => {
    const [anchorEl, setAnchorEl] = React.useState<HTMLElement | null>(null);
    function handleClick(event: React.MouseEvent<HTMLElement, MouseEvent>) {
      setAnchorEl(event.currentTarget);
    }

    function handleClose() {
      setAnchorEl(null);
    }

    return (
      <div key={row.id}>
        <Button
          size="large"
          color="inherit"
          variant="text"
          aria-owns={anchorEl ? "simple-menu" : undefined}
          aria-haspopup="true"
          onClick={handleClick}
        >
          ...
        </Button>
        <Menu
          id="simple-menu"
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleClose}
          elevation={2}
        >
          {isPublishableState(tab) &&
            renderMenuItem(OfferActionType.Publish, row)}
          {renderMenuItem(OfferActionType.Preview, row)}
          {/* Show edit for all tabs except for disabled tab */}
          {!isExpiredTab(tab) && renderMenuItem(OfferActionType.Edit, row)}
          {new String(row.status).toLowerCase() !==
            OfferStatus.Updated.toLowerCase() &&
            !canBeEnabled(tab) &&
            renderMenuItem(OfferActionType.Duplicate, row)}
        </Menu>
      </div>
    );
  };

  const columns: IColProps[] = [
    {
      id: "checkbox",
      tableCellTemplate: cellProps => (
        <Checkbox
          checked={selectedOffers.has(cellProps.row.id)}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            e.target.checked
              ? onOfferSelected(cellProps.row)
              : onOfferUnSelected(cellProps.row);
          }}
        />
      ),
      title: (
        <Checkbox
          color="primary"
          checked={allChecked}
          indeterminate={someChecked}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            e.target.checked ? onOfferSelectAll() : onOfferUnSelectAll();
          }}
        />
      )
    },
    {
      id: "partnerName",
      title: "Partner Name",
      sort: true
    },
    {
      id: "createdBy",
      title: "Created By",
      sort: true
    },
    ...
    (tab === "draft"
        ? [{
            id: "createdAt",
            title: "Creation Date",
            sort: true,
            tableCellTemplate: dateCellTemplate
          }]
        : (tab === "staged" || tab === "live")
        ? [{
            id: "publishedAt",
            title: "Published Date",
            sort: true,
            tableCellTemplate: dateCellTemplate
          }]
        : tab === "changesPending"
        ? [{
            id: "updatedAt",
            title: "Updated Date",
            sort: true,
            tableCellTemplate: dateCellTemplate
          }]
        : []
    ),

    {
      id: "startDate",
      title: (
        <>
          <span>Start Date</span>
          <br />
          <DateFormatElement />
        </>
      ),
      tableCellTemplate: dateCellTemplate,
      sort: true
    },

    {
      id: "endDate",
      title: (
        <>
          <span>End Date</span>
          <br />
          <DateFormatElement />
        </>
      ),
      tableCellTemplate: dateCellTemplate,
      sort: true
    },

    {
      id: "displayPriority",
      title: "Display Priority",
      sort: true
    },
    {
      id: "actions",
      title: "Actions",
      tableCellTemplate: actionCellTemplate
    }
  ];

  return (
    <BB8CustomTable
      cols={columns}
      data={offers}
      rowsPerPage={rowsPerPage}
      key={tab}
      detailsComponent={data =>
        detailsRowComponent(
          getOffersWithReadableNames([data], promotions, categories)[0]
        )
      }
      sorting={sorting}
      sortChange={sortChanged}
      classes={ {} }
    />
  );
};

export default connect(withAuthStateToProps)(OffersTable);
