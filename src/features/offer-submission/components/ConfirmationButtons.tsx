import { createStyles, Grid, Theme, WithStyles, withStyles } from "@material-ui/core";
import React, { PureComponent } from "react";
import { BB8Button } from "../../../BB8";
import ConfirmationModal from "./ConfirmationModal";

interface IConfirmationButtonProps extends WithStyles {
  onConfirmationAction: () => void;
  onToggleConfirmation?: () => void;
  isDefaultDisabled: boolean;
  defaultText: string;
  confirmText: string;
  cancelText: string;
  resetRequest: boolean;
  hasCustomOfferType?: boolean;
}

interface IConfirmationButtonState {
  actionRequested: boolean;
}

const initialState: IConfirmationButtonState = {
  actionRequested: false
};

class ConfirmationButton extends PureComponent<
  IConfirmationButtonProps,
  IConfirmationButtonState
> {
  public state = initialState;
  public render() {
    const {
      classes,
      defaultText,
      cancelText,
      confirmText,
      isDefaultDisabled,
      onConfirmationAction,
      resetRequest,
      hasCustomOfferType = false,
    } = this.props;

    const { actionRequested } = this.state;

    // Show the action button when an action has not yet been requested and when the reset is
    const showActionButton = !actionRequested || resetRequest;

    // Show the confirmation button when the action has been requested and the reset has not been set
    const showConfirmationButtons = actionRequested && !resetRequest;

    const showModal = showConfirmationButtons && hasCustomOfferType;

    return (
      <Grid>
        {showActionButton && (
          <BB8Button
            variant="contained"
            color="primary"
            disabled={isDefaultDisabled}
            onClick={this.toggleRequestedState}
          >
            {defaultText}
          </BB8Button>
        )}

        {
          showModal && <ConfirmationModal isOpen={showConfirmationButtons} onConfirmationAction={this.props.onConfirmationAction} onToggleConfirmation={this.toggleRequestedState} />
        }

        {showConfirmationButtons && !showModal && (
          <Grid
            direction="row"
            item={true}
            container={true}
            justify="flex-end"
            alignItems="center"
          >
            <BB8Button
              variant="contained"
              color="primary"
              onClick={() => {
                onConfirmationAction();
                this.toggleRequestedState();
              }}
              className={classes.confirmButton}
            >
              {confirmText}
            </BB8Button>
            <BB8Button
              variant="contained"
              color="primary"
              onClick={this.toggleRequestedState}
              className={classes.cancelButton}
            >
              {cancelText}
            </BB8Button>
          </Grid>
        )}
      </Grid>
    );
  }

  private toggleRequestedState = () => {
    this.setState({ actionRequested: !this.state.actionRequested }, () => {
      if (this.props.onToggleConfirmation) {
        this.props.onToggleConfirmation();
      }
    });
  };
}

const styles = createStyles((theme: Theme) => ({
  cancelButton: {
    backgroundColor: "#D30E8B",
    "&:hover": {
      backgroundColor: "#DC3EA2"
    }
  },
  confirmButton: {
    backgroundColor: "#44A648",
    "&:hover": {
      backgroundColor: "#69B86D"
    },
    marginRight: "2em"
  }
}));

export default withStyles(styles)(ConfirmationButton);
