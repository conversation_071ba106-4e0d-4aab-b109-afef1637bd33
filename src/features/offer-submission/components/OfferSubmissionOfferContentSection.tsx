import { createStyles, Fab, Grid, Paper, Theme, Typography, WithStyles, withStyles } from "@material-ui/core";
import AddIcon from "@material-ui/icons/Add";
import RemoveIcon from "@material-ui/icons/Remove";
import { Field, FieldArray, FieldArrayRenderProps, FieldProps, FormikProps } from "formik";
import { cloneDeep, get } from "lodash";
import React, { useEffect, useState } from "react";
import { DYNAMIC_FIELDS_CONFIG, FIELDS_CONFIG } from "../../../components/form/configs/fields.config";
import CustomLocalizationFieldArray from "../../../components/form/fields/CustomLocalizationFieldArray";
import CustomLocalizationTextField from "../../../components/form/fields/CustomLocalizationTextField";
import CustomOptionalLocalizationField from "../../../components/form/fields/CustomOptionalLocalizationField";
import OfferContentPreview from "../../../components/form/fields/OfferContentPreview";
import CustomFileWidget from "../../../components/form/widgets/CustomFileWidget";
import CustomTextWidget from "../../../components/form/widgets/CustomTextWidget";
import HelperTooltip from "../../../components/form/widgets/HelperTooltip";
import { generateRandomGUIDForCacheInvalidation } from "../../../shared/helpers";
import { AwardType, IOfferFormModel, IValidationErrors, OfferType, Qualifier } from "../../../validation/validator";
import { getOfferDefaultContentTier } from "../helpers/OfferDefaults";
import { IOfferGeneratedContent } from "../pages/OfferSubmission";
import Concealable from "./Concealable";
import OfferSubmissionFormPanel from "./OfferSubmissionFormPanel";

interface IOfferSubmissionOfferContentSectionProps {
  disabled?: boolean;
  schema: any;
  formikProps: FormikProps<IOfferFormModel>;
  generatedContent: IOfferGeneratedContent;
}

interface IOfferSubmissionOfferContentSectionStates {
  key?: any;
}

const initialState: IOfferSubmissionOfferContentSectionStates = {
  key: generateRandomGUIDForCacheInvalidation(),
}

const OfferSubmissionOfferContentSection: React.FunctionComponent<
    IOfferSubmissionOfferContentSectionProps & WithStyles> = (props) => {
  const {
    formikProps,
    schema,
    classes,
    generatedContent
  } = props;

  const [state, setState] = useState(() => cloneDeep(initialState));

  const [programType, setProgramType] = useState(formikProps.values.programType);
  const [promoTags, setPromoTags] = useState(formikProps.values.tagsLabels);

  useEffect(() => {
    setProgramType(formikProps.values.programType);
    setPromoTags(formikProps.values.tagsLabels);
  }, [formikProps.values]);

  useEffect(() => {
    setProgramType(formikProps.values.programType);
    setPromoTags(formikProps.values.tagsLabels);
  }, []);

  const fields = [
    "baseCashRedemption",
    "partnerBaseEarnRate",
    "tiers",
    "image"
  ];

  const properties = fields
  .map(field => ({ [field]: get(schema, `properties.${ field }`) }))
  .reduce((actual: any, acc: any) => ({ ...acc, ...actual }), {});

  const awardShortEn =
      get(generatedContent, `awardShort.en-US`) ||
      get(formikProps.values, `awardShort.en-US`);

  const awardShortFr =
      get(generatedContent, `awardShort.fr-CA`) ||
      get(formikProps.values, `awardShort.fr-CA`);

  const qualifierShortEn =
      get(generatedContent, `qualifierShort.en-US`) ||
      get(formikProps.values, `qualifierShort.en-US`);

  const qualifierShortFr =
      get(generatedContent, `qualifierShort.fr-CA`) ||
      get(formikProps.values, `qualifierShort.fr-CA`);

  const awardShortValue = { "en-US": awardShortEn, "fr-CA": awardShortFr };

  const qualifierShortValue = {
    "en-US": qualifierShortEn,
    "fr-CA": qualifierShortFr
  };

  const setKey = () => {
    setState({ key: generateRandomGUIDForCacheInvalidation() });
  };

  const imageChangeHandler = (fieldProps: FieldProps, fieldPath: string) => (value: string | undefined) => {
    fieldPath =
        value === undefined && fieldPath.includes("fr-CA")
            ? "image.fr-CA" // Remove the .path from string
            : fieldPath;

    fieldProps.form.setFieldValue(fieldPath, value);
  };

  const getQualifierLabel = (formValues: any) => {
    const offerType = formValues.offerType;
    const qualifier = formValues.qualifier;
    return DYNAMIC_FIELDS_CONFIG.qualifier.title(offerType, qualifier);
  };

  const getCustomContent = (formValues: any) => {
    const qualifier = formValues.qualifier;
    const awardType = formValues.awardType;

    const contentObj: {
      qualifierLong?: string;
      awardLong?: string;
    } = {};

    if (awardType === "custom") {
      contentObj.awardLong = "Custom Award";
    }
    if (qualifier === "custom") {
      contentObj.qualifierLong = "Custom Qualifier";
    }
    return contentObj;
  };

  const showQualifierFrequency = () => {
    if (programType && promoTags) {
      return programType === "amreceipts" && promoTags[0].includes(("Boost offer"));
    }
    return false;
  }

  const arrayItems = (arrayHelpers: FieldArrayRenderProps) => {
    const maxTiers: number = get(schema, "properties.tiers").maxItems;
    const qualifier: Qualifier = formikProps.values.qualifier;
    const awardType: AwardType = formikProps.values.awardType;
    const offerType: OfferType = formikProps.values.offerType;

    // TODO: is there a better way to do this? Currently the only way to grab the maxItems is from the dependencies section and because we know the hardcoded order of these restrictions
    // const maxTierContentItems = get(schema, "dependencies.tierContent.allOf");
    const maxProductContentItems = 5; // get(maxTierContentItems[0],"then.properties.tiers.items.properties.content.maxItems");
    const maxCategoryContentItems = 3; // get(maxTierContentItems[1],"then.properties.tiers.items.properties.content.maxItems");
    const items = arrayHelpers.form.values[arrayHelpers.name];
    const qualifierLabel = getQualifierLabel(arrayHelpers.form.values);
    const customContent = getCustomContent(arrayHelpers.form.values);

    return items && items.length > 0 ? (
        items.map((item: any, index: number) => {
          return (
              <Grid
                  id={ `tiers[${ index }]` }
                  item={ true }
                  xs={ 12 }
                  key={ state.key + index }
              >
                <OfferSubmissionFormPanel
                    title={
                      <Grid
                          container={ true }
                          item={ true }
                          xs={ 12 }
                          justify="space-between"
                          alignContent="center"
                      >
                        <Grid item={ true } id={ `tiers[${ index }].content` }>
                          Offer Content { items.length > 1 && ` - Tier ${ index + 1 }` }{ " " }
                        </Grid>
                        <Grid item={ true }>
                          { items.length > 1 && (
                              <Fab
                                  size="small"
                                  color="primary"
                                  type="button"
                                  onClick={ () => {
                                    arrayHelpers.remove(index);
                                    setKey();
                                  } }
                              >
                                <RemoveIcon/>
                              </Fab>
                          ) }
                        </Grid>
                      </Grid>
                    }
                >
                  { renderContentFields(index) }
                  { qualifierLabel && (
                      <Grid
                          item={ true }
                          xs={ 12 }
                          container={ true }
                          className={ classes.nestedContentWrapper }
                      >
                        <CustomLocalizationFieldArray
                            name={ `tiers[${ index }].content` }
                            label={ qualifierLabel }
                            maxItems={
                              qualifier === Qualifier.Product
                                  ? maxProductContentItems
                                  : qualifier === Qualifier.Category
                                      ? maxCategoryContentItems
                                      : qualifier === Qualifier.Fuel
                                          ? maxCategoryContentItems
                                          : 5
                            }
                        />
                      </Grid>
                  ) }
                  { customContent &&
                      renderCustomContentFields(customContent, index) }
                  <Grid item={ true } xs={ 12 }>
                    <OfferContentPreview
                        awardText={ get(
                            generatedContent,
                            `tiersGeneratedContent[${ index }].awardLong`
                        ) }
                        qualifierText={ get(
                            generatedContent,
                            `tiersGeneratedContent[${ index }].qualifierLong`
                        ) }
                        previewTitle={ "Offer Preview" }
                    />
                  </Grid>
                </OfferSubmissionFormPanel>
                <Concealable displayCondition={ items.length < maxTiers
                    && items.length - 1 === index
                    && offerType !== OfferType.AmCashDiscount
                    && programType !== "amreceipts" }>
                  <Grid
                      item={ true }
                      container={ true }
                      xs={ 12 }
                      justify="flex-end"
                      alignItems="center"
                      onClick={ () =>
                          arrayHelpers.push(getOfferDefaultContentTier(qualifier, awardType, offerType))
                      }
                  >
                    <Fab size="small" color="primary" type="button">
                      <AddIcon/>
                    </Fab>
                    { "  " }
                    <Typography variant="button" color="primary">
                      &nbsp;&nbsp;Add Tier
                    </Typography>
                  </Grid>
                </Concealable>
                { items.length > 1 && // when there is more than one tier
                    items.length - 1 === index && // when the item is the last item to be displayed
                    ((formikProps.values.awardType && // when the item contains custom awardType
                            formikProps.values.awardType === AwardType.Custom) ||
                        (formikProps.values.qualifier && // or when the item contains custom qualifier
                            formikProps.values.qualifier === Qualifier.Custom)) && (
                        <OfferSubmissionFormPanel
                            title={ "Please add the short versions of your custom offer" }
                            tooltipText={ `Using a custom offer with multiple tiers, please specify the values below to be used when displaying this offer.` }
                        >
                          { formikProps.values.awardType &&
                              formikProps.values.awardType === AwardType.Custom && (
                                  <CustomLocalizationTextField
                                      name="awardShort"
                                      label={ "Award Short" }
                                      required={ true }
                                  />
                              ) }
                          { formikProps.values.qualifier &&
                              formikProps.values.qualifier === Qualifier.Custom && (
                                  <CustomLocalizationTextField
                                      name="qualifierShort"
                                      label={ "Qualifier Short" }
                                      required={ true }
                                  />
                              ) }
                        </OfferSubmissionFormPanel>
                    ) }
              </Grid>
          );
        })
    ) : (
        <Grid item={ true } xs={ 12 }>
          <Fab color="primary" size="small" onClick={ () => arrayHelpers.push({}) }>
            <AddIcon/>
          </Fab>
        </Grid>
    );
  };

  const renderCustomContentFields = (customObject: any, index: number) => {
    const resultObj = [];
    for (const key in customObject) {
      if (customObject.hasOwnProperty(key)) {
        resultObj.push(
            <Grid
                item={ true }
                xs={ 12 }
                container={ true }
                className={ classes.nestedContentWrapper }
            >
              <OfferSubmissionFormPanel
                  title={ customObject[key] + " Translations" }
              >
                <CustomLocalizationTextField
                    name={ `tiers[${ index }].` + key }
                    label={ customObject[key] }
                />
              </OfferSubmissionFormPanel>
            </Grid>
        );
      }
    }
    return resultObj;
  }

  const renderContentFields = (tierIndex: number) => {
    const { values } = formikProps;

    if (tierIndex === undefined) {
      return null;
    }

    return (
        <Grid
            item={ true }
            container={ true }
            xs={ 12 }
            className={ classes.contentFieldPadding }
            direction="row"
            spacing={ 3 }
        >
          { ["flatMiles"].includes(values.awardType) && (
              <Grid item={ true } xs={ 6 } md={ 6 }>
                <CustomTextWidget
                    label="Bonus Miles (Award Value)"
                    type="number"
                    inputProps={ {
                      min: 1,
                      max: 99999,
                      step: 1,
                      pattern: "/^([1-9][0-9]{0,4})$/"
                    } }
                    name={ `tiers[${ tierIndex }].awardValue` }
                    required={ true }
                />
              </Grid>
          ) }

          { ["flatDiscount"].includes(values.awardType) && (
              <Grid item={ true } xs={ 6 } md={ 6 }>
                <CustomTextWidget
                    label="Dollars Off (Award Value)"
                    type="number"
                    inputProps={ {
                      min: 0.01,
                      max: 99999.99,
                      step: 1,
                      pattern: "/^d{0,5}(.d{1,2})?$/"
                    } }
                    name={ `tiers[${ tierIndex }].awardValue` }
                    required={ true }
                />
              </Grid>
          ) }

          { ["multiplierMiles"].includes(values.awardType) && (
              <Grid item={ true } xs={ 6 } md={ 6 }>
                <CustomTextWidget
                    label="Multiplier (Award Value)"
                    type="number"
                    inputProps={ {
                      min: 1,
                      max: 99,
                      step: 1
                    } }
                    name={ `tiers[${ tierIndex }].awardValue` }
                    required={ true }
                />
              </Grid>
          ) }

          { ["percentDiscount"].includes(values.awardType) && (
              <Grid item={ true } xs={ 6 } md={ 6 }>
                <CustomTextWidget
                    label="Percentage Off (Award Value)"
                    type="number"
                    inputProps={ {
                      min: 1,
                      max: 99,
                      step: 1
                    } }
                    name={ `tiers[${ tierIndex }].awardValue` }
                    required={ true }
                />
              </Grid>
          ) }

          { ["perProduct"].includes(values.qualifier) && ["spend"].includes(values.offerType) && (
              <Grid item={ true } xs={ 6 } md={ 6 }>
                <CustomTextWidget
                    label="Spend (Qualifier Value)"
                    type="number"
                    name={ `tiers[${ tierIndex }].qualifierValue` }
                    inputProps={ {
                      min: 0.01,
                      max: 99999.99,
                      pattern: "/^d{0,5}(.d{1,2})?$/"
                    } }
                    required={ true }
                />
              </Grid>
          ) }

          { ["perUnit"].includes(values.qualifier) && ["buy"].includes(values.offerType) && (
              <Grid item={ true } xs={ 6 } md={ 6 }>
                <CustomTextWidget
                    label="Quantity (Qualifier Value)"
                    type="number"
                    inputProps={ {
                      min: 0.01,
                      max: 99999.99,
                      pattern: "/^d{0,5}(.d{1,2})?$/"
                    } }
                    name={ `tiers[${ tierIndex }].qualifierValue` }
                    required={ true }
                />
              </Grid>
          ) }

          { ["frequency"].includes(values.qualifier) && (
              <Grid item={ true } xs={ 6 } md={ 6 }>
                <CustomTextWidget
                    label="Quantity (Qualifier Value)"
                    type="number"
                    inputProps={ {
                      min: 0.01,
                      max: 99999.99,
                      pattern: "/^d{0,5}(.d{1,2})?$/"
                    } }
                    name={ `tiers[${ tierIndex }].qualifierValue` }
                    required={ true }
                />
              </Grid>
          ) }

          { ["storewide", "category" , "perDollar"].includes(values.qualifier) && (
              <Grid item={ true } xs={ 6 } md={ 6 }>
                <CustomTextWidget
                    label="Spend (Qualifier Value)"
                    type="number"
                    name={ `tiers[${ tierIndex }].qualifierValue` }
                    inputProps={ {
                      min: 0.01,
                      max: 99999.99,
                      pattern: "/^d{0,5}(.d{1,2})?$/"
                    } }
                    required={ true }
                />
              </Grid>
          ) }
          { ["storewide","custom"].includes(values.qualifier) && ["spend","custom"].includes(values.offerType) && (
              <Grid item={true} sm={12} className={classes.fieldHolder}>
                <Typography variant="subtitle1" color="initial">
                  {"SKUs"}
                  <HelperTooltip text={"this is an optional field for putting in single or multiple sku, if mulitple,please use ',' to separate the sku."} />
                </Typography>
                <CustomTextWidget
                    name={`tiers[${ tierIndex }].content[0].skus`}
                    placeholder="e.g: 1000000 or 1000000,10000002,1000003"
                    type="string"
                    isArray={true}
                />
                <Field name = {`tiers[${tierIndex}].content[0].en-US`}>
                  {({form}) => {
                    const skuList = form.values?.tiers?.[tierIndex]?.content?.[0]?.skus;
                    const hasSKUs = Array.isArray(skuList) && skuList.length>0;
                    const currentenUS = form.values?.tiers?.[tierIndex]?.content?.[0]?.["en-US"];

                    if(hasSKUs && currentenUS !== ""){
                      //If SKUs are present, set en-US to ""
                      form.setFieldValue(`tiers[${tierIndex}].content[0].en-US`, "");
                    }
                    if(!hasSKUs){
                      //No SKUs, clear both values
                      if(skuList !== undefined && currentenUS !== undefined) {
                        form.setFieldValue(`tiers[${tierIndex}].content[0].skus`, undefined);
                        form.setFieldValue(`tiers[${tierIndex}].content[0].en-US`, undefined);
                      }
                    }
                    return <input type="hidden"/>;
                  }}
                </Field>
              </Grid>
          ) }
          { ["product", "volume", "continuity", "fuel"].includes(values.qualifier) && (
              <Grid item={ true } xs={ 6 } md={ 6 }>
                <CustomTextWidget
                    label={
                      ["volume", "continuity"].includes(values.qualifier)
                          ? "Minimum (Qualifier Value)"
                          : ["fuel"].includes(values.qualifier)
                              ? "Volume in Liters (Qualifier Value)"
                              : "Quantity (Qualifier Value)"
                    }
                    type="number"
                    inputProps={ {
                      min: 1,
                      max: 999,
                      step: 1
                    } }
                    name={ `tiers[${ tierIndex }].qualifierValue` }
                    required={ true }
                />
              </Grid>
          ) }

          <Concealable displayCondition={ ["fuel"].includes(values.qualifier) || showQualifierFrequency() }>
            <Grid item={ true } xs={ 6 } md={ 6 }>
              <CustomTextWidget
                  label="Number of Visits (Qualifier Frequency)"
                  type="number"
                  tooltip={ FIELDS_CONFIG.qualifierFrequency.toolTip }
                  inputProps={ {
                    min: 1,
                    max: 10,
                    step: 1
                  } }
                  name={ `tiers[${ tierIndex }].qualifierFrequency` }
                  required={ true }
              />
            </Grid>
          </Concealable>

          { ["amCashDiscount"].includes(values.offerType) && (
              <Grid item={ true } xs={ 6 } md={ 6 }>
                <CustomTextWidget
                    label="Cash Miles Discount (Award Value)"
                    type="number"
                    inputProps={ {
                      min: 1,
                      max: values.baseCashRedemption
                    } }
                    name={ `tiers[${ tierIndex }].awardValue` }
                    required={ true }
                />
              </Grid>
          ) }
        </Grid>
    );
  }

  const onFileError = (fieldProps: FieldProps) => (error: IValidationErrors[]) => {
    // there is a problem with this method described here
    // https://github.com/jaredpalmer/formik/issues/1309
    // fieldProps.form.setFieldError(fieldProps.field.name, error as any);
  };

  return (
      <Grid item={ true } xs={ 12 }>
        <Paper className={ classes.paperContainer }>
          <Typography
              variant="h4"
              component="h4"
              paragraph={ true }
              gutterBottom={ true }
          >
            Offer Display Content
          </Typography>
          <Grid container={ true } item={ true } xs={ 12 } spacing={ 1 }>
            { formikProps.values.qualifier &&
                (formikProps.values.qualifier === Qualifier.CashDiscount ||
                    formikProps.values.qualifier === Qualifier.CashRedemption) && (
                    <Grid item={ true } xs={ 12 }>
                      <CustomTextWidget
                          name="baseCashRedemption"
                          label={ FIELDS_CONFIG.baseCashRedemption.title }
                          type={ properties.baseCashRedemption.type }
                      />
                    </Grid>
                ) }
            { ["multiplierMiles"].includes(formikProps.values.awardType) && (
                <Grid item={ true } xs={ 12 }>
                  <CustomTextWidget
                      label={ FIELDS_CONFIG.partnerBaseEarnRate.title }
                      tooltip={ FIELDS_CONFIG.partnerBaseEarnRate.toolTip }
                      type={ properties.partnerBaseEarnRate.type }
                      inputProps={ {
                        max: properties.baseCashRedemption.maximum,
                        step: 1
                      } }
                      name="partnerBaseEarnRate"
                      required={ true }
                  />
                </Grid>
            ) }
            <FieldArray name="tiers" render={ arrayItems }/>
            { (formikProps.values.tiers.length > 1 ||
                (formikProps.values.qualifier === Qualifier.Product &&
                    formikProps.values.tiers[0].content &&
                    formikProps.values.tiers[0].content.length > 1)) && (
                <Grid item={ true } xs={ 12 }>
                  <OfferContentPreview
                      awardText={ awardShortValue }
                      qualifierText={ qualifierShortValue }
                      previewTitle={ "Thumbnail Content" }
                      showBorder={ true }
                  />
                </Grid>
            ) }
            <Grid item={ true } xs={ 12 }>
              <CustomOptionalLocalizationField
                  formikProps={ formikProps }
                  fieldPath="description"
              />
            </Grid>
            <Grid item={ true } xs={ 12 }>
              <Typography
                  variant="h5"
                  component="h5"
                  paragraph={ true }
                  gutterBottom={ true }
              >
                { FIELDS_CONFIG.image.title }
                <HelperTooltip text={ FIELDS_CONFIG.image.toolTip }/>
              </Typography>

              <Grid container={ true }>
                <Grid item={ true } xs={ 6 } id="image.en-US">
                  <Typography>
                    English Image (Must be .jpg, .jpeg, .png or .gif){ " " }
                  </Typography>
                  <Field name="image.en-US.path">
                    { (fieldProps: FieldProps) => {
                      return (
                          <CustomFileWidget
                              name="image.en-US.path"
                              value={ fieldProps.field.value }
                              onChange={ imageChangeHandler(
                                  fieldProps,
                                  "image.en-US.path"
                              ) }
                              onError={ onFileError(fieldProps) }
                              error={
                                get(
                                    fieldProps.form.errors,
                                    fieldProps.field.name
                                ) as any
                              }
                          />
                      );
                    } }
                  </Field>
                </Grid>
                <Grid item={ true } xs={ 6 } id="image.fr-CA">
                  <Typography>
                    French Image (Must be .jpg, .jpeg, .png, or .gif)
                    <Typography
                        variant="subtitle2"
                        color="primary"
                        inline={ true }
                    >
                      &nbsp;(Optional)
                    </Typography>
                  </Typography>
                  <Field name="image.fr-CA.path">
                    { (fieldProps: FieldProps) => {
                      return (
                          <CustomFileWidget
                              name="image.fr-CA.path"
                              value={ fieldProps.field.value }
                              onChange={ imageChangeHandler(
                                  fieldProps,
                                  "image.fr-CA.path"
                              ) }
                              onError={ onFileError(fieldProps) }
                              error={
                                get(
                                    fieldProps.form.errors,
                                    fieldProps.field.name
                                ) as any
                              }
                          />
                      );
                    } }
                  </Field>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Paper>
      </Grid>
  );
}

const styles = createStyles((theme: Theme) => ({
  contentFieldPadding: { padding: "1em" },
  nestedContentWrapper: { padding: "1rem", width: "100%" },
  paperContainer: { padding: "1rem", width: "100%" }
}));

export default withStyles(styles)(OfferSubmissionOfferContentSection);