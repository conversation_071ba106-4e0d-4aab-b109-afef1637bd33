import {
  createStyles,
  FormHelperText,
  Grid,
  Paper,
  Typography,
  WithStyles,
  withStyles
} from "@material-ui/core";
import { Field, FieldProps, FormikProps } from "formik";
import { get } from "lodash";
import React, { PureComponent } from "react";
import { PartnerLookup } from "../../../components/form/fields/PartnerLookup";
import { IPartner } from "../../../models/Partner";
import imageUnavailable from "../../../shared/assets/images/imageUnavailable.png";
import { IOfferFormModel, MechanismType, OfferStatus } from "../../../validation/validator";
import { buildErrorMessage } from "../pages/ErrorSummary";
import _ from "lodash";
import { string } from "prop-types";
import { REGIONS } from "../../../models/Region";
interface IOfferSubmissionPartnerSectionProps {
  schema: any;
  partners?: IPartner[];
  formikProps: FormikProps<IOfferFormModel>;
  setSponsorCodesOnPartnerChange: any;
}
class OfferSubmissionPartnerSection extends PureComponent<
  IOfferSubmissionPartnerSectionProps & WithStyles
> {
  public render() {
    const { partners, classes, formikProps } = this.props;

    return (
      <Grid
        item={true}
        xs={12}
        style={{ marginTop: "1rem", marginBottom: "1rem" }}
      >
        <Paper elevation={2} style={{ padding: "1rem", width: "100%" }}>
          <Typography
            variant="h4"
            component="h4"
            paragraph={true}
            gutterBottom={true}
          >
            Partner Information
          </Typography>

          <Field name="partnerId">
            {(fieldProps: FieldProps) => {
              const errors: any[] | undefined = fieldProps.form.errors[
                fieldProps.field.name
              ] as any;
              const partnerInfo = this.getPartnerInfo(fieldProps.field.value);

              const issuerCodes = formikProps.values.activeSponsorCodesForSelectedPartnerId
              ? formikProps.values.activeSponsorCodesForSelectedPartnerId.map(sponsorCode => sponsorCode.issuerCode)
              : [];

              const featureFlagSponsorCode = process.env.REACT_APP_OFFER_ISSUER_DROPDOWN === 'true';
              const sponsorCodes = featureFlagSponsorCode? issuerCodes.join(", ") : get(partnerInfo,'sponsorCodes',[]).join(", ");
              return (
                <Grid container={true} direction="column">
                  <Grid
                    item={true}
                    container={true}
                    direction="row"
                    alignItems="flex-start"
                    justify="space-between"
                  >
                    <Grid
                      container={true}
                      item={true}
                      sm={8}
                      direction="column"
                    >
                      <Grid item={true}>
                        <Typography variant="subtitle1" color="initial">
                          Partner Name
                        </Typography>
                        {partners && partners.length > 0 && (
                          <div className="partnerLookup">
                            <PartnerLookup
                              name="partnerId"
                              partners={partners}
                              disabled={formikProps.values.status === OfferStatus.Published.toUpperCase() || formikProps.values.status === OfferStatus.Updated.toUpperCase()}
                              placeholder="Select Partner"
                              value={fieldProps.field.value}
                              onSelect={(selectedPartner?: IPartner) =>
                                this.handlePartnerChange(
                                  fieldProps.form.setFieldValue,
                                  selectedPartner
                                )
                              }

                              />
                            {(errors || []).map(error => (
                              <FormHelperText error={true}>
                                {buildErrorMessage("partnerId")(error)}
                              </FormHelperText>
                            ))}
                          </div>
                        )}

                        {sponsorCodes && (
                          <Grid
                            item={true}
                            className={classes.sponsorCodesHolder}
                          >
                            <Typography variant="subtitle1" color="initial">
                              Sponsor Codes:
                            </Typography>
                            <Typography variant="body2">{sponsorCodes}</Typography>
                          </Grid>
                        )}
                      </Grid>
                    </Grid>
                    <Grid
                      container={true}
                      sm={4}
                      item={true}
                      direction="row"
                      justify="space-around"
                    >
                      {this.renderLogo(partnerInfo, "en")}
                      {this.renderLogo(partnerInfo, "fr")}
                    </Grid>
                  </Grid>
                </Grid>
              );
            }}
          </Field>
        </Paper>
      </Grid>
    );
  }

  private getPartnerNameFromId = (partnerId: any) => {
    const found = _.find(
      this.props.partners,
      partner => partner.id === partnerId
    );
    return found ? found.name : "";
  };

  private handlePartnerChange = (
    setFieldValue: any,
    selectedPartner?: IPartner
  ) => {
    const partnerId = selectedPartner ? selectedPartner.id : undefined;
    setFieldValue("partnerId", partnerId, true);
    setFieldValue("partnerName", this.getPartnerNameFromId(partnerId), true);
    setFieldValue("mechanisms", [{ mechanismType: MechanismType.NoAction }], true);

    if (process.env.REACT_APP_OFFER_ISSUER_DROPDOWN === 'true') {
      const partnerInfo = this.getPartnerInfo(partnerId);
      const sponsorCodesForSelectedPartnerId = get(partnerInfo, "sponsorCodes", []);
      this.props.setSponsorCodesOnPartnerChange(sponsorCodesForSelectedPartnerId);

      const partnerType = get(partnerInfo, "type", []) || [];
      const internalAirmilesCalculated = partnerType.includes("internal-airmiles-calculated");
      setFieldValue("partnerInternalAirmilesCalculated", internalAirmilesCalculated, true);
    }

    if (selectedPartner && selectedPartner.regions) {
      const regionsToSet =
        selectedPartner.regions.indexOf("all") >= 0
          ? REGIONS.map(({ value }) => value)
          : selectedPartner.regions.map(region => region.toUpperCase());

      setFieldValue("regions", regionsToSet, true);
    } else {
      setFieldValue("regions", undefined, true);
    }
  };

  private getPartnerInfo = (partnerId: string | undefined) => {
    const { partners } = this.props;
    return partners && partnerId
      ? partners.find((partnerObj: IPartner) => partnerObj.id === partnerId)
      : null;
  };

  private renderLogo = (
    partnerInfo: IPartner | undefined | null,
    locale: "en" | "fr"
  ) => {
    // Fall back to english logo if french logo doesn't exist
    let imagePath: string;
    if (locale === "fr") {
      imagePath =
        get(partnerInfo, "fullLogo[1].file.url", undefined) === undefined
          ? "fullLogo[0].file.url"
          : "fullLogo[1].file.url";
    } else {
      imagePath = "fullLogo[0].file.url";
    }
    const partnerLogo = get(partnerInfo, imagePath, imageUnavailable);
    const partnerName = get(partnerInfo, "name", "");
    const { classes } = this.props;
    return (
      <div className={classes.partnerLogoContainer}>
        <Typography variant="subtitle1" color="initial">
          Logo ({locale})
        </Typography>
        <img
          className={classes.partnerLogo}
          src={partnerLogo}
          title={`${partnerName} product logo`}
        />
      </div>
    );
  };
}

const styles = createStyles(() => ({
  partnerLogo: {
    backgroundColor: "#f1f2f2", // TODO: create a well class
    border: "10px solid #f1f2f2",
    height: "138px",
    padding: "10px",
    textAlign: "center",
    width: "138px"
  },
  partnerLogoContainer: {
    margin: "0 0",
    textAlign: "center"
  },
  sponsorCodesHolder: {
    padding: "1em 0"
  }
}));

export default withStyles(styles)(OfferSubmissionPartnerSection);
