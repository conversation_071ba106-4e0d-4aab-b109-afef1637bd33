.offer-block-container {
  display: grid;
  grid-template-columns: 33% 33% 33%;
  grid-template-rows: repeat(6, auto);
  grid-template-areas:
    "offer-header offer-header offer-header"
    "offer-details offer-details offer-details"
    "details-card-en details-card-fr partner-cards"
    "details-card-en details-card-fr partner-cards"
    "details-card-en details-card-fr partner-cards"
    "details-card-en details-card-fr partner-cards";
  grid-gap: 0.5em;
  background-color: #f1f2f2;
  margin-bottom: 2em;
  margin-top: 1em;
  padding-bottom: 1em;
  border-radius: 0.5em;
  border: 2px solid lightgray;
  border-top: none;
  flex-grow: 1;
}
.offer-header {
  display: grid;
  grid-template-columns: 30% 40% 15% 15%;
  grid-template-rows: repeat(2, auto);
  grid-template-areas:
    ". offer-summary offer-button-1 offer-button-2"
    ". offer-summary offer-button-1 offer-button-2";
  grid-area: offer-header;
  flex-direction: row;
  padding: 1em;
  margin: 0 0.25em 0 -0.1em;
  border-radius: 0.5em 0.5em 0em 0em;

  &-blue {
    background-color: #45a6d6;
    border-bottom: 1px solid #45a6d6;
  }
  &-green {
    background-color: #69b86d;
    border-bottom: 1px solid #69b86d;
  }
}

.offer-button-1 {
  grid-area: offer-button-1;
  flex-direction: column;
  margin-right: 1em;
}
.offer-button-2 {
  grid-area: offer-button-2;
  flex-direction: column;
  margin-right: 1em;
}

.offer-button-1.hidden,
.offer-button-2.hidden {
  display: none;
}

.offer-summary {
  grid-area: offer-summary;
}
.offer-details {
  grid-area: offer-details;
  padding-left: 1em;
  padding-right: 1em;
}
.details-card-en {
  grid-area: details-card-en;
  padding-left: 1em;
  padding-right: 1em;
}
.details-card-fr {
  grid-area: details-card-fr;
  padding-left: 1em;
  padding-right: 1em;
}
.partner-cards {
  grid-area: partner-cards;
  padding-left: 1em;
  padding-right: 1em;
}
.offer-details-list-row {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  padding: 0;
  background-color: white;
}
.offer-details-list-row {
  border: 0.02em solid darkgray;
}

.offer-details-list-row > li {
  border-left: 0.02em solid darkgray;
  border-top: 0.02em solid darkgray;
}
.offer-button-1-margin {
  margin-left: 1em;
  margin-right: 1em;
}
.hidden {
  display: none;
}
