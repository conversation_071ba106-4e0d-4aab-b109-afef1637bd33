import { Grid, Paper, Typography } from "@material-ui/core";
import { FormikProps } from "formik";
import { chain, get, pick, cloneDeep } from "lodash";
import React, { PureComponent } from "react";
import { FIELDS_CONFIG } from "../../../components/form/configs/fields.config";
import CustomCheckboxGroup from "../../../components/form/widgets/CustomCheckboxGroup";
import { CustomSelectWidget } from "../../../components/form/widgets/CustomSelectWidget";
import { getStaticFilterName } from "../../../models/IStaticFilterOption";
import { getProgramTypeName} from "../../../models/ProgramType";
import {
  featureFlagOfferTemplateOptions,
  OFFER_TEMPLATE_GROUPS_DIVIDERS,
  OfferTemplateOptions
} from "../../../models/OfferTemplateType";
import { AWARD_GROUPS_DIVIDERS } from "../../../models/AwardType";
import { evaluateIf } from "../../../shared/helpers/SchemaResolver";
import {
  IOfferFormModel,
  Qualifier,
  AwardType,
  Availability,
  OfferType,
  ProgramType,
  ITierObject
} from "../../../validation/validator";
import amCash from "../../../shared/assets/images/amCash.png";
import spend from "../../../shared/assets/images/spend.png";
import {
  getOfferDefaultContentTier,
  getDefaultLocalizedObject
} from "../helpers/OfferDefaults";
import CustomCardTypeWidget from "../../../components/form/widgets/CustomCardTypeWidget";
import CustomAudienceWidget from "../../../components/form/widgets/CustomAudienceWidget";
import CustomTextWidget from "../../../components/form/widgets/CustomTextWidget";
import { IPartner } from "../../../models/Partner";
import {FEATURE_FLAG, featureFlagCompare} from "./FeatureFlag";
import CustomRetailerGroup from "../../../components/form/widgets/CustomRetailerGroup";

interface IOfferSubmissionOfferTypeSectionProps {
  disabled?: boolean;
  schema: any;
  formikProps: FormikProps<Partial<IOfferFormModel>>;
  partners?: IPartner[];
}

export default class OfferSubmissionOfferTypeSection extends PureComponent<
  IOfferSubmissionOfferTypeSectionProps
> {
  public getSectionConfig = (offerType: OfferType | undefined) => {
    const { schema, formikProps, partners } = this.props;
    const fields = ["offerTemplate","programType", "offerType", "qualifier", "awardType", "availability", "audience", "campaignCode"];
    const featureFlagRetailerGroup = featureFlagCompare(FEATURE_FLAG.RETAILER_GROUP, "true");

    let selectedPartner: IPartner | undefined;
    if (partners) {
      selectedPartner = this.getSelectedPartner(partners, formikProps.values.partnerId);
    }
    // Filtering programTypes based on the selected partner
    const programTypeOptions = get(schema, "properties.programType.enum", []);
    let filteredProgramTypes = [...programTypeOptions];
    if(featureFlagCompare(FEATURE_FLAG.AM_RECEIPTS, "false")){
      filteredProgramTypes = filteredProgramTypes.filter((pt: string) => pt !== "amreceipts");
    }
    if (!selectedPartner || selectedPartner.id !== process.env.REACT_APP_PARTNER_BMO_BANK_MONTREAL) {
        filteredProgramTypes = filteredProgramTypes.filter((pt: string) => pt !== "bmopreapp"); // Filter "BMO Pre-approval"
    }
    // calculate qualifier and awardType options based on offer type selection
    // grab allOf schemas from PostOfferFormObject.json
    const allOfSchemas = get(schema, "dependencies.offerType.allOf");
    // this will tell us if any of the allOf subschemas evaluate to true, given
    // offer type value
    let thenSchema: {
      [key: string]: { enum: string[] };
    } = {
      awardType: { enum: [] },
      qualifier: { enum: [] }
    };

    try {
      for (const allOfSchema of allOfSchemas) {
        // this is a naive approach. Not trying to determine the type of subschema
        // just infering it's a if schema and evaluating the tree
        const isTrue = evaluateIf(allOfSchema.if, "offerType", offerType);

        // if evaluates to true, merge the subschema to thenSchema
        if (isTrue) {
          thenSchema = {
            ...thenSchema,
            ...get(allOfSchema, "then.properties")
          };
          break;
        }
      }
      // tslint:disable-next-line: no-empty
    } catch { }

    const subSchema = pick(schema.properties, fields);

    /** here we use lodash pipe to
     *  1. break {} into [[key,value]]  |  {offerType: "buy"} => [["offerType", "buy"]]
     *  2. merge each value with a possible thenSchema
     *  3. reduce the array of [[key,value]] to {} again, inverse of step 1
     * */

    const properties = chain(subSchema)
      .toPairs()
      .map(([key, value]) => {
        return [key, { ...value, ...thenSchema[key] }];
      })
      .fromPairs()
      .value();

    const sectionConfig = {
      schema: {
        $id: "form-section-offer-type",
        $schema: "http://json-schema.org/draft-07/schema#",
        title: "Offer Type",
        type: "object",
        properties,
        dependencies: schema.dependencies
      },
      filteredProgramTypes, // add to the filtered list of program types
    };
    return sectionConfig;
  };

  public render() {
    const {formikProps } = this.props;

    //#region offerType
    const sectionConfig = this.getSectionConfig(
      get(formikProps.values, "offerType")
    );

    // Using filteredProgramTypes instead of programTypeOptions
    let programTypeOptions = [];
    if (sectionConfig.filteredProgramTypes) {
        programTypeOptions = sectionConfig.filteredProgramTypes;
    }

    //#endregion
    const awardTypeOptions = sectionConfig.schema.properties.awardType.enum;
    const fieldConfig = FIELDS_CONFIG.availability;
    const showFlaggedTemplates = featureFlagCompare(FEATURE_FLAG.NEW_OFFER_TEMPLATE, "true");
    const filteredOfferTemplateOptions = showFlaggedTemplates? OfferTemplateOptions :
        OfferTemplateOptions.filter((value) => !featureFlagOfferTemplateOptions.includes(value));
    return (
      <Grid
        item={true}
        xs={12}
        style={{ marginTop: "1rem", marginBottom: "1rem" }}
      >
        <Paper elevation={2} style={{ padding: "1rem", width: "100%" }}>
          <Typography
            variant="h4"
            component="h4"
            paragraph={true}
            gutterBottom={true}
          >
            Offer Type
          </Typography>
          <Grid container={true} spacing={3}>
            <Grid item={true} xs={3}>
              <CustomSelectWidget
                  type="select"
                  name="programType"
                  title={FIELDS_CONFIG.programType.title}
                  options={programTypeOptions.map((value: string) => ({
                    value,
                    label: getProgramTypeName(value)
                  }))}
                  onChange={this.handleCardTypeChange}
              />
            </Grid>
            <Grid item={true} xs={3}>
              <CustomSelectWidget
                type="select"
                name="offerTemplate"
                title={FIELDS_CONFIG.offerTemplate.title}
                options={filteredOfferTemplateOptions.map((value: string) => ({
                  value,
                  label: getStaticFilterName("offerTemplate", value),
                  hasDivider: OFFER_TEMPLATE_GROUPS_DIVIDERS.includes(value)
                }))}
                onChange={this.handleOfferTypeChange}
              />
            </Grid>
            <Grid item={true} xs={3}>
              <CustomSelectWidget
                type="select"
                name="awardType"
                title={FIELDS_CONFIG.awardType.title}
                options={awardTypeOptions.map((value: string) => ({
                  value,
                  label: getStaticFilterName("awardType", value),
                  hasDivider: AWARD_GROUPS_DIVIDERS.includes(value)
                }))}
                onChange={this.handleOfferTypeChange}
              />
            </Grid>
            <Grid item={true} xs={3}>
              <CustomCheckboxGroup
                values={sectionConfig.schema.properties.availability.items.enum.map(
                  (item: string) => {
                    return {
                      name: item
                        .replace(/([a-z](?=[A-Z]))/g, "$1 ") // Change from camelCase to separate words
                        .split(" ")
                        .map(
                          (s: string) =>
                            s[0].toUpperCase() + s.substring(1, s.length)
                        )
                        .join(" "),
                      value: item
                    };
                  }
                )}
                title={fieldConfig.title}
                isColumn={true}
                name="availability"
                tooltipText={fieldConfig.toolTip}
                onChange={this.handleAvailabilityChange}
              />
            </Grid>
          </Grid>
            <CustomCardTypeWidget
                formikProps={formikProps}
                key="key"
            />
            <CustomRetailerGroup/>
          <Grid item={true} xs={12} >
            <CustomAudienceWidget
              formikProps={formikProps}
              key="key"
            />
          </Grid>

          <Grid item={true} xs={12}>
            <CustomTextWidget
              name="campaignCode"
              type="string"
              label={FIELDS_CONFIG.campaignCode.title}
              placeholder={FIELDS_CONFIG.campaignCode.title}
              tooltip={FIELDS_CONFIG.campaignCode.toolTip}
            />
          </Grid>

        </Paper>
      </Grid>
    );
  }
  private handleCardTypeChange = (e: any) => {
    const { formikProps } = this.props;
    if (e.target.value !== ProgramType.CardLinkedOffers) {
      formikProps.setFieldValue(
          "cardType", undefined
      );
    }
  }

  // TODO: this logic should sit at the offer submission level, not the section level and should be passed in as a prop function
  private handleOfferTypeChange = (e: any) => {
    console.log("HANDLE OFFER TYPE CHANGE")
    const { formikProps } = this.props;
    const values = formikProps.values;
    const offerTemplate =
      e.target.name === "offerTemplate" ? e.target.value : values.offerTemplate;
    const offerTemplateParts = offerTemplate.split('-');
    let offerType = offerTemplateParts ? offerTemplateParts[0] : undefined;
    const qualifier = offerTemplateParts ? offerTemplateParts[1] : undefined;
    const sectionConfig = this.getSectionConfig(offerType);
    const awardTypeOptions = sectionConfig.schema.properties.awardType.enum;
    const awardType =
      e.target.name === "awardType"
        ? e.target.value
        : awardTypeOptions.includes(values.awardType)
          ? values.awardType
          : undefined

    const hasCustomLegal = values.hasCustomLegal;
    const tiers = [getOfferDefaultContentTier(qualifier, awardType,offerType)];
    console.log("Tiers", tiers)

    const programTypeOptions = sectionConfig.schema.properties.programType.enum;
    const programType =
        e.target.name === "programType"
            ? e.target.value
            : programTypeOptions.includes(values.programType)
                ? values.programType
                : undefined
    formikProps.setValues({
      ...values,
      offerTemplate,
      programType,
      offerType,
      qualifier,
      awardType,
      tiers,
      baseCashRedemption: qualifier === Qualifier.CashDiscount ? 95 : undefined, // TODO: get default value from schema
      issuanceCode: formikProps.initialValues.issuanceCode ? formikProps.initialValues.issuanceCode : undefined,
      sponsorCode: formikProps.initialValues.sponsorCode ? formikProps.initialValues.sponsorCode : undefined,
      partnerBaseEarnRate:
        awardType === AwardType.MultiplierMiles ? 20 : undefined,
      hasCustomLegal,
      legalText: hasCustomLegal
        ? values.legalText
        : getDefaultLocalizedObject(),
      image: this.getImageDefaults(offerType, qualifier),
      awardShort: undefined,
      ctaLabel: formikProps.initialValues.ctaLabel ? formikProps.initialValues.ctaLabel : undefined,
      ctaUrl: formikProps.initialValues.ctaUrl ? formikProps.initialValues.ctaUrl : undefined,
      qualifierShort: undefined,
      offerCategory1: undefined,
      offerCategory2: undefined
    });

    console.log(formikProps.values);
  };

  private handleAvailabilityChange = (e: any, checked: boolean) => {
    const { formikProps } = this.props;
    if (e.target.value === Availability.Online) {
      formikProps.setFieldValue(
        "partnerUrl",
        checked ? getDefaultLocalizedObject() : undefined
      );
    }
  };

  private getImageDefaults = (offerType: OfferType, qualifier: Qualifier) => {
    let image;
    if (
      offerType === OfferType.AmCashEarn ||
      offerType === OfferType.AmCashDiscount
    ) {
      image = {
        "en-US": {
          path: amCash
        },
        "fr-CA": undefined
      };
    } else if (
      (qualifier === Qualifier.Storewide || qualifier === Qualifier.Category) &&
      (offerType === OfferType.Spend || offerType === OfferType.Custom)
    ) {
      image = {
        "en-US": {
          path: spend
        },
        "fr-CA": undefined
      };
    } else {
      // image is require, en-US is required, but fr-CA is not required
      // do not change next line, it will break validation
      image = {
        "en-US": { path: undefined },
        "fr-CA": undefined
      };
    }

    return image;
  };

   private getSelectedPartner(partners: IPartner[], partnerId?: string): IPartner | undefined {
      return partners.find((partner) => partner.id === partnerId);
   }
}