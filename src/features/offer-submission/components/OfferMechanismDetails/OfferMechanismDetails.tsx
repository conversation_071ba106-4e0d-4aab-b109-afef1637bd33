import { Avatar, CardActions, createStyles, Grid, Theme, Typography, WithStyles, withStyles, } from "@material-ui/core";
import { get } from "lodash";
import React, { PureComponent } from "react";
import { BB8Button } from "../../../../BB8";
import infoIcon from '../../../../shared/assets/icons/info.svg'
import heartCyan from "../../../../shared/assets/images/heart-outlined-cyan.svg";
import loadedNoAction from "../../../../shared/assets/images/loaded-noAction.png";
import masterCard from "../../../../shared/assets/images/masterCard.png";
import barcode from "../../../../shared/assets/images/offer-tag-barcode.svg";
import globe from "../../../../shared/assets/images/offer-tag-online.svg";
import person from "../../../../shared/assets/images/person.svg";
import rightArrow from "../../../../shared/assets/images/right-arrow.png";
import tabArrow from "../../../../shared/assets/images/tab-arrow.png";

import { BARCODE_MECHANISM_TYPES } from "../../../../shared/constants";
import { IOfferFormModel } from "../../../../validation/validator";
import { getCardTypeLabelPartnerDetails } from "../../helpers/getCardTypeLabel";
import OfferBarcode from "../OfferBarcode";

interface IOfferMechanismDetailedProps extends WithStyles {
  lang: string;
  offer: IOfferFormModel;
  mechType?: string;
}


class OfferMechanismDetails extends PureComponent<IOfferMechanismDetailedProps> {
  renderMechanismContent = () => {
    const { classes, offer, mechType, lang } = this.props;

    if (!mechType) {
      return null;
    }

    const localization = lang === "fr" ? "fr-CA" : "en-US";
    const offerCardType = offer.cardType ? new Set(offer.cardType) : null;
    const { ctaLabel, ctaUrl, programType } = offer;

    if (programType === "cardlinked" && mechType === "noAction") {
      const cardDynamicTextLabel = getCardTypeLabelPartnerDetails(offerCardType);
      return <CardLinkedNoAction classes={classes} localization={localization} cardDynamicTextLabel={cardDynamicTextLabel} ctaLabel={ctaLabel} ctaUrl={ctaUrl} />;
    }

    if (programType === "cardlinked" && mechType === "optIn") {
      const cardDynamicTextLabel = getCardTypeLabelPartnerDetails(offerCardType);
      if (ctaLabel && ctaUrl) {
        return (
          <div style={{ display: "flex", flexDirection: "column", gap: "0.4em" }}>
            <CardLinkedOptIn classes={classes} localization={localization} cardDynamicTextLabel={cardDynamicTextLabel} />
            <CardLinkedNoAction classes={classes} localization={localization} cardDynamicTextLabel={cardDynamicTextLabel} ctaLabel={ctaLabel} ctaUrl={ctaUrl} />
          </div>
        )
      }else{
        return <CardLinkedOptIn classes={classes} localization={localization} cardDynamicTextLabel={cardDynamicTextLabel} />;
      }
    }

    // Scenario: OptIn without CTA (current state)
    if (mechType === "optIn" && (!ctaLabel || !ctaUrl)) {
      return <OptIn classes={classes} localization={localization} />;
    }

    // Scenario: OptIn with CTA (OptIn + CTA button)
    if (mechType === "optIn" && ctaLabel && ctaUrl) {
      return (
       <div style={{ display: "flex", flexDirection: "column", gap: "0.4em" }}>
          <OptIn classes={classes} localization={localization} />
          <CtaPreview classes={classes} localization={localization} ctaUrl={ctaUrl} ctaLabel={ctaLabel} />
        </div>
      );
    }

    // Scenario: NoAction witout CTA (current state)
    if (mechType === "noAction" && (!ctaLabel || !ctaUrl)) {
      return <NoActionOrLoadGo classes={classes} localization={localization} />;
    }

    // Scenario: NoAction with CTA (NoAction + CTA button)
    if (mechType === "noAction" && ctaLabel && ctaUrl) {
      return <CtaPreview classes={classes} localization={localization} ctaLabel={ctaLabel} ctaUrl={ctaUrl} />;
    }

    if (mechType === "load+go" || mechType === "scanReceipt") {
      return <NoActionOrLoadGo classes={classes} localization={localization} />;
    }

    if (mechType === "button") {
      return <ButtonMechanism classes={classes} localization={localization} offer={offer} />;
    }

    if (mechType === "plu" || mechType === "couponCode") {
      return <PluCouponCode classes={classes} localization={localization} mechType={mechType} offer={offer} />;
    }

    if (BARCODE_MECHANISM_TYPES.includes(mechType)) {
      return <BarcodeMechanism classes={classes} localization={localization} mechType={mechType} offer={offer} />;
    }
  };

  render() {
    return this.renderMechanismContent();
  }
}

const CtaPreview = ({ classes, localization, ctaLabel, ctaUrl }: any) => {
     const buttonLink = ctaUrl[localization] ?? "#";
     const buttonLabel = ctaLabel[localization] ?? "Defaul Label";

      const readyToUseLabel = {
         "en-US": "Ready to use",
         "fr-CA": "Prêt à l'emploi"
       };

  return (
    <CardActions className={classes.actions}>
      <div className={classes.buttonMechanic}>
        <div className={classes.barcode}>
         <Avatar
            aria-label="Check mark"
            src={loadedNoAction}
            className={classes.icon}
         />
          <Typography variant="caption">
                     {get(readyToUseLabel, localization)}
          </Typography>
         </div>
        <BB8Button
          fullWidth
          color="primary"
          variant="contained"
          classes={{ root: classes.button }}
          onClick={() => {
            window.open(buttonLink, "_blank");
          }}
        >
           {buttonLabel}
          <Avatar src={tabArrow} className={classes.tabArrowIcon} />
        </BB8Button>
      </div>
    </CardActions>
  );
};

const CardLinkedNoAction = ({
  classes,
  localization,
  cardDynamicTextLabel,
  ctaLabel,
  ctaUrl,
}: any) => {
  const labels = {
    cardlinked: {
      "en-US": "Use your linked card",
      "fr-CA": "Utiliser votre carte liée",
    },
    learnMore: {
      "en-US": "LEARN MORE",
      "fr-CA": "En savoir plus",
    },
  };

  const cardDynamicTextLabel18n = get(cardDynamicTextLabel, localization);

  //CTA
  const buttonLink = get(ctaUrl, localization, undefined);
  const buttonLabel = get(ctaLabel, localization, undefined);

  return (
    <CardActions className={classes.actions}>
      <div className={classes.buttonMechanic}>
        <div className={classes.cardLinkedCustomHeaderSection}>
          <Avatar src={masterCard} className={classes.icon} />
          <Typography variant="caption" >
            {get(labels.cardlinked, localization)}
          </Typography>
        </div>
        {buttonLabel && buttonLink && (
          <BB8Button
            fullWidth
            color="primary"
            variant="contained"
            classes={{ root: classes.button }}
            onClick={() => {
              window.open(buttonLink, "_blank");
            }}
          >
            {buttonLabel}
            <Avatar src={tabArrow} className={classes.tabArrowIcon} />
          </BB8Button>
        )}
        {cardDynamicTextLabel18n && (
          <div className={classes.cardLinkedSection}>
            <Avatar src={infoIcon} className={classes.icon} />
            <Typography
              variant="caption"
              className={classes.cardLinkedCustomCaptionInfo}
            >
              {cardDynamicTextLabel18n}
            </Typography>
          </div>
        )}
      </div>
    </CardActions>
  );
};

const CardLinkedOptIn = ({ classes, localization, cardDynamicTextLabel }: any) => {
  const optInLabels = {
    "en-US": "Opt in to activate Offer",
    "fr-CA": "Activer l’offre pour participer",
  };
  const optInMechanismValues = {
    "en-US": "Opt-in",
    "fr-CA": "Activez l’offre",
  };
  const optInLabelValues = {
    "en-US": "Learn More",
    "fr-CA": "En savoir plus",
  };

  const optInLabel18n = get(optInLabels, localization);
  const optInLabelMsg18n = get(optInLabelValues, localization);
  const optInMechanismType = get(optInMechanismValues, localization)
  const optInMechanismType18n = get(cardDynamicTextLabel, localization)

  return (
    <CardActions disableSpacing={true} className={classes.actions}>
      <div className={classes.buttonMechanic}>
        <div className={classes.cardLinkedCustomHeaderSection}>
          <Avatar
            aria-label={optInLabel18n}
            src={masterCard}
            className={classes.optInIcon}
          />
          <Typography variant="caption">
            {optInLabel18n}
          </Typography>
        </div>

        <div style={{backgroundColor: "#1F68DA", borderRadius: "100px", paddingBottom: "10px", paddingTop: "10px", cursor: "pointer", marginTop: "16px"}}>
          <p style={{color: "#FFFFFF", fontWeight: "bold", fontSize: "14px", lineHeight: "24px", letterSpacing: "2px", textTransform: "uppercase", textAlign: "center"}}>
            {optInMechanismType}
          </p>
        </div>

        {optInMechanismType18n &&
          <div className={classes.cardLinkedSection}>
            <Avatar
              src={infoIcon}
              className={classes.icon}
            />
            <Typography variant="caption" className={classes.cardLinkedCustomCaptionInfo}>
              {optInMechanismType18n}
            </Typography>
          </div>
        }

        <Typography className={classes.learnMoreLabel}>
          {optInLabelMsg18n}
          <Avatar
            src={rightArrow}
            className={classes.icon}
          />
        </Typography>
      </div>
    </CardActions>
  );
}

const OptIn = ({ classes, localization }: any) => {
  const optInLabels = {
    "en-US": "Opt in to activate offer",
    "fr-CA": "Activer l’offre pour participer",
  };
  const optInMechanismValues = {
    "en-US": "OPT IN",
    "fr-CA": "ACTIVER",
  };

  const optInLabel18n = get(optInLabels, localization);
  const optInMechanismType = get(optInMechanismValues, localization);

  return (
    <CardActions disableSpacing={true} className={classes.actions}>
      <div className={classes.buttonMechanic}>
        <div className={classes.barcode}>
          <Avatar aria-label={optInLabel18n} src={heartCyan} className={classes.optInIcon} />
          <Typography variant="caption" className={classes.customCaption}>
            {optInLabel18n}
          </Typography>
        </div>

        <BB8Button
          fullWidth
          classes={{ root: classes.button }}
          color="primary"
          variant="contained"
        >
          {optInMechanismType}
        </BB8Button>
      </div>
    </CardActions>
  );
};

const NoActionOrLoadGo = ({ classes, localization }: any) => {
  const noAction = {
    "en-US": "Ready to use",
    "fr-CA": "Prêt à l'emploi"
  };

  return (
    <CardActions disableSpacing={true} className={classes.actions}>
      <Avatar
        aria-label="Check mark"
        src={loadedNoAction}
        className={classes.icon}
      />
      <Typography className={classes.customCaption}>
        {get(noAction, localization)}
      </Typography>
    </CardActions>
  );
}

const ButtonMechanism = ({ classes, localization, offer }: any) => {
  const buttonLink = get(offer, "mechanisms[0].mechanismValue." + localization, "#");
  const buttonText = {
    "en-US": "Online Offer",
    "fr-CA": "Offre en ligne"
  };

  return (
    <CardActions className={classes.actions}>
      <div className={classes.buttonMechanic}>
        <div className={classes.barcode}>
          <Avatar src={globe} className={classes.icon} />

          <Typography variant="caption" className={classes.customCaption}>
            {get(buttonText, localization)}
          </Typography>
        </div>

        <BB8Button
          fullWidth
          color="primary"
          variant="contained"
          classes={{ root: classes.button }}
          onClick={() => window.open(buttonLink, "_blank")}
        >
          {get(offer, "mechanisms[0].mechanismLabel." + localization, "Default Label")}
          <Avatar src={tabArrow} className={classes.tabArrowIcon} />
        </BB8Button>
      </div>
    </CardActions>
  );
};

const PluCouponCode = ({ classes, localization, mechType, offer }: any) => {
  const useCodeValues = {
    "en-US": "Use Code",
    "fr-CA": "Utiliser le code"
  };

  const barcodeLabel = get(useCodeValues, localization);
  const couponCodeValue = get(offer, `mechanisms[0].mechanismValue.${localization}`);

  return (
    <CardActions className={classes.actions}>
      <Grid container direction="column" alignItems="center" justify="center">
        <div className={classes.barcode}>
          <Avatar aria-label="Barcode" src={person} className={classes.icon} />
          <Typography>{barcodeLabel}</Typography>
        </div>

        <Typography align="center" className={classes.barcodeTextCode}>
          {couponCodeValue}
        </Typography>
      </Grid>
    </CardActions>
  );
};

const BarcodeMechanism = ({ classes, localization, mechType, offer }: any) => {
  const barcodeLabels = {
    "en-US": "Scan Barcode",
    "fr-CA": "Scanner le code à barres"
  };

  const barcodeLabel = get(barcodeLabels, localization);
  const mechanismValue = get(offer, `mechanisms[0].mechanismValue.${mechType === "button" ? localization : "en-US"}`);

  return (
    <CardActions className={classes.actions}>
      <Grid container direction="column" alignItems="center" justify="center">
        <div className={classes.barcode}>
          <Avatar aria-label="Barcode" src={barcode} className={classes.icon} />
          <Typography>{barcodeLabel}</Typography>
        </div>

        <OfferBarcode mechType={mechType} mechValue={mechanismValue} />

        <Typography align="center" className={classes.barcodeTextCode}>
          {mechanismValue}
        </Typography>
      </Grid>
    </CardActions>
  );
};

const styles = createStyles((theme: Theme) => ({
  actions: {
    alignItems: "center",
    justifyContent: "center",
    margin: '0 10px',
    padding: "1em",
    border: `1px solid #E5E5E5`,
    borderRadius: '10px',
    paddingBottom: '1em'
  },
  barcode: {
    width: "100%",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    borderBottom: `1px solid #E5E5E5`,
    paddingBottom: "0.5em",
    marginBottom: "0.5em"
  },
  barcodeTextCode: {
    fontSize: "1.5rem",
    fontWeight: 700
  },
  icon: {
    width: "0.9em",
    marginRight: "0.3em",
    border: 0,
    fontSize: "1rem",
    height: "auto",
  },
  optInIcon: {
    width: "1em",
    marginRight: "0.3em",
    border: 0,
    fontSize: "1rem",
    height: "auto",
  },
  mechanism: {
    borderBottom: `1px solid ${theme.palette.grey[300]}`,
    borderTop: `1px solid ${theme.palette.grey[300]}`,
    padding: "16px !important"
  },
  button: {
    padding: ".4em",
    backgroundColor: "#1F68DA",
    borderRadius: "1.2em",
    color: "#f1f2f2",
    boxShadow: "none",
    fontSize: "18px",
    textTransform: "none",
    marginTop: "0.5em",
    "&:hover": {
      backgroundColor: "#1F68DA",
      boxShadow: "none"
    },
    "&:focus": {
      boxShadow: "none"
    }
  },
  buttonMechanic: {
    width: '100%',
    display: "block"
  },
  tabArrowIcon: {
    width: "15px",
    height: "15px",
    borderRadius: "0",
    marginLeft: "5px"
  },
  learnMoreLabel: {
    height: "16px",
    fontStyle: "normal",
    fontWeight: "bold",
    fontSize: "12px",
    lineHeight: "16px",
    marginTop: "10px",
    justifyContent: "center",
    /* identical to box height, or 133% */
    display: "flex",
    alignItems: "center",
    letterSpacing: "1px",
    textTransform: "uppercase",
    /* App/Interactive/interactive-blue */
    color: "#1F68DA",
  },
  buttonCardLinked: {
    padding: ".4em",
    backgroundColor: "#1F68DA",
    textAlign: "center",
    fontWeight: "bold",
    borderRadius: "56px",
    color: "#f1f2f2",
    boxShadow: "none",
    fontSize: "18px",
    textTransform: "none",
    marginTop: "0.5em",
    width: "311px",
    height: "48px",
    radius: "56px",
    paddingRight: "14px",
    paddingLeft: "28px",
    paddingTop: "14px",
    paddingBottom: "28px",
    gap: "8px",
    "&:hover": {
      backgroundColor: "#1F68DA",
      boxShadow: "none"
    },
    "&:focus": {
      boxShadow: "none"
    }
  },
  cardLinkedCustomHeaderSection: {
    alignItems: "center",
    justifyContent: "center",
    display: "flex",
    borderBottom: "1px solid #E5E5E5",
    paddingBottom: "0.7rem"
  },
  cardLinkedCustomCaptionInfo: {
    lineHeight: "16px",
    fontSize: "0.6rem"
  },
  cardLinkedSection: {
    alignItems: "center",
    justifyContent: "center",
    display: "flex",
    padding: "0.075rem",
    marginTop: '10px',
  },
}));

export default withStyles(styles)(OfferMechanismDetails);