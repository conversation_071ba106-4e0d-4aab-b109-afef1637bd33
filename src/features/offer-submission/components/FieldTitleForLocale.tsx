import {
  createStyles,
  Grid,
  Typography,
  withStyles,
  WithStyles,
} from "@material-ui/core";
import React, { memo, ReactNode } from "react";

interface FieldTitleForLocaleProps extends WithStyles {
  title: string;
  locale?: "en" | "fr";
  children?: ReactNode;
}

const FieldTitleForLocale: React.FunctionComponent<
  FieldTitleForLocaleProps
> = memo(({ title, locale, classes, children }) => {
  return (
    <Grid
      direction="row"
      sm={true}
      className={classes.subHeaderHolder}
      container={true}
      item={true}
    >
      <Typography variant="subtitle1" color="initial">
        {title}{" "}
      </Typography>
      <Typography variant="subtitle1" color="secondary">
        &nbsp; {locale === "fr" ? "(French)" : "(English)"}
      </Typography>
      {children}
    </Grid>
  );
});

const styles = createStyles(() => ({
  subHeaderHolder: {
    paddingBottom: "1em",
  },
}));

export default withStyles(styles)(FieldTitleForLocale);
