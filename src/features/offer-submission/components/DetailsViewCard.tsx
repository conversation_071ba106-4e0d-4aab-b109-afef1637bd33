import {
  Ava<PERSON>,
  Card,
  CardContent,
  Chip,
  createStyles,
  Theme,
  Typography,
  withStyles,
  WithStyles
} from "@material-ui/core";
import { get } from "lodash";
import React, { PureComponent } from "react";
import { IPartner } from "../../../models/Partner";
import amCash from "../../../shared/assets/images/amCash.png";
import heart from "../../../shared/assets/images/heart-outlined.png";
import spend from "../../../shared/assets/images/spend.png";
import { generateRandomGUIDForCacheInvalidation } from "../../../shared/helpers";
import { IOfferFormModel, OfferType } from "../../../validation/validator";
import { getOfferFormattedDate } from "../utils";
import { OfferMechanismDetails } from "./OfferMechanismDetails";

interface IOfferPreviewProps extends WithStyles {
  lang: string;
  offer: IOfferFormModel;
  mechType?: string | undefined;
  partner: IPartner;
}

class DetailsViewCard extends PureComponent<IOfferPreviewProps> {
  public barcodeDivRef = React.createRef<SVGSVGElement>();

  private imageFallback(e: any) {
    const { offer, partner } = this.props;
    const fallbackImage = (get(offer, "offerType") === ("amCash" as OfferType) ? amCash : spend);

    e.target.src = fallbackImage + generateRandomGUIDForCacheInvalidation();
  }

  public render() {
    const { classes, offer, lang } = this.props;
    const localization = lang === "fr" ? "fr-CA" : "en-US";
    const promoTagName = get(offer, `tagNames[0].${localization}`);

    const offerLegalText: string = get(offer, "legalText." + localization);
    const legalText = offerLegalText.includes("\n")
      ? offerLegalText.split("\n").map((text: string) => {
        if (text === "") {
          return <br />;
        }
        return <p>{text}</p>;
      })
      : offerLegalText;

    const cardMedia =
      (get(offer, "image." + localization + ".path")
        ? get(offer, "image." + localization + ".path")
        : get(offer, "image.en-US.path")) +
      generateRandomGUIDForCacheInvalidation();

    const terms = {
      "en-US": "Terms & Conditions",
      "fr-CA": "Modalités de l’offre"
    };

    const tierInfo = offer.tiers.map(tier => {
      return (
        <div style={{ marginBottom: 5 }}>
          <Typography variant="body2" style={{ fontWeight: 700, marginBottom: 2 }}>
            {get(tier, "awardLong." + localization)}
          </Typography>

          <Typography variant="body2" style={{ fontSize: '18px' }}>
            {get(tier, "qualifierLong." + localization)}
          </Typography>
        </div>
      );
    })

    const endDate = get(offer, "endDate");

    return (
      <Card className={classes.card}>
        <CardContent className={classes.imageBlock}>
          <div className={classes.imageWrapper}>
            <img
              src={cardMedia}
              className={classes.image}
              onError={this.imageFallback.bind(this)}
              crossOrigin="anonymous"
              title={`${offer.partnerName} product image`}
            />
          </div>

          <Avatar
            src={heart}
            aria-label="Save offer"
            classes={{
              root: classes.heart,
              img: classes.heartImage
            }}
          />
        </CardContent>
        <CardContent>
          <Typography variant="body2" classes={{ root: classes.partnerText }}>
            {offer.partnerName}
          </Typography>

          {tierInfo}

          {get(offer, "description") && (
            <Typography variant="body2" paragraph>
              {get(offer, "description." + localization)}
            </Typography>
          )}

          {promoTagName && (
            <Chip
              label={promoTagName}
              className={classes.chip}
              classes={{
                label: classes.chipLabel
              }}
            />
          )}

          <Typography
            variant="body2"
            align="left"
            classes={{ root: classes.date }}
          >
            {lang === "en" && endDate ? "Ends " : "Prend fin: "}
            <span dangerouslySetInnerHTML={{ __html: getOfferFormattedDate(endDate, lang) }} />
          </Typography>
        </CardContent>

        <OfferMechanismDetails
          lang={this.props.lang}
          offer={this.props.offer}
          mechType={this.props.mechType}
        />

        <CardContent>
          <Typography
            paragraph
            align="center"
            variant="subtitle1"
            classes={{ root: classes.termsTitle }}
          >
            {get(terms, localization)}
          </Typography>
          <Typography paragraph variant="caption">
            {legalText}
          </Typography>
        </CardContent>
      </Card>
    );
  }
}

const styles = createStyles((theme: Theme) => ({
  card: {
    maxWidth: 350,
    width: "350px",
    minWidth: "auto",
    marginTop: ".5em",
    display: "flex",
    flexDirection: "column",
    borderRadius: 0,
    boxShadow: "none"
  },
  partnerText: {
    color: '#5A6B7B',
    fontSize: '12px',
    fontWeight: 600,
    marginBottom: 5,
  },
  heart: {
    width: "48px",
    height: "48px",
    right: 20,
    bottom: 0,
    position: 'absolute',
    borderRadius: '100%',
    backgroundColor: '#EEF4FC',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  },
  heartImage: {
    width: "20px",
    height: "20px",
    objectFit: "contain"
  },
  avatar: {
    width: 60,
    height: 60
  },
  imageWrapper: {
    display: "flex",
    flexDirection: "column",
    paddingBottom: "0",
    alignItems: "center",
    justifyContent: "center",
    width: "160px",
    height: "160px"
  },
  imageBlock: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    position: "relative",
    width: "100%",
    paddingBottom: "0"
  },
  image: {
    maxHeight: "160px",
    maxWidth: "160px",
    objectFit: "contain"
  },
  button: {
    padding: ".4em",
    backgroundColor: "#0A6FB3",
    borderRadius: "0.2em",
    color: "#f1f2f2",
    boxShadow: "none",
    fontSize: "18px",
    textTransform: "none",
    marginTop: "0.5em",
    "&:hover": {
      backgroundColor: "#0A6FB3",
      boxShadow: "none"
    },
    "&:focus": {
      boxShadow: "none"
    }
  },
  date: {
    paddingTop: "1em",
  },
  chip: {
    display: 'inline-block',
    padding: '5px 2px',
    borderRadius: "6px",
    height: "auto",
    fontSize: `${theme.typography.caption.fontSize}`,
    backgroundColor: '#FDECFF',
    fontWeight: 600,
    transform: "translateZ(1px)",
    color: "#5E5E5E",
  },
  chipLabel: {
    paddingLeft: "6px",
    paddingRight: "6px"
  },
  bookmarkImg: {
    marginTop: 0,
    width: "14px",
    height: "12px",
    padding: "0",
    marginLeft: "6px",
    backgroundColor: `${theme.palette.grey[200]}`,
    color: "#1790CC",
  },
  bookmarkProps: {
    width: "auto",
  },
  description: {
    backgroundColor: `${theme.palette.grey[200]}`,
    paddingBottom: "0"
  },
  termsTitle: {
    color: '#1F68DA',
    fontSize: '12px',
    fontWeight: 600,
    textTransform: 'uppercase'
  }
}));

export default withStyles(styles)(DetailsViewCard);
