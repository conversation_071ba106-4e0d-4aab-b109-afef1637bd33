import { buildErrorMessage, getErrorsBySection, removeIndexDataPath } from "./ErrorSummary";

const errors: any[] = [
  {
    data: {},
    dataPath: ".offerType",
    keyword: "enum",
    message: "should be one of ",
    params: { allowedValues: ["buy", "spend"] },
    parentSchema: "",
    schema: "",
    schemaPath: ""
  },
  {
    data: {},
    dataPath: ".qualifier",
    keyword: "enum",
    message: "should be one of ",
    params: { allowedValues: ["product", "storewide", "category"] },
    parentSchema: "",
    schema: "",
    schemaPath: ""
  },
  {
    data: {},
    dataPath: ".awardType",
    keyword: "enum",
    message: "should be one of ",
    params: { allowedValues: ["milesFlat", "milesMultiplier"] },
    parentSchema: "",
    schema: "",
    schemaPath: ""
  },
  {
    data: {},
    dataPath: ".tiers[0].content[0].en-US",
    keyword: "required",
    message: "this field is required",
    params: {},
    parentSchema: "",
    schema: "",
    schemaPath: ""
  },
  {
    data: {},
    dataPath: ".tiers[0].content[1].en-US",
    keyword: "required",
    message: "this field is required",
    params: {},
    parentSchema: "",
    schema: "",
    schemaPath: ""
  },
  {
    data: {},
    dataPath: ".tiers[1].content[0].en-US",
    keyword: "required",
    message: "this field is required",
    params: {},
    parentSchema: "",
    schema: "",
    schemaPath: ""
  },
  {
    data: {},
    dataPath: ".startDate",
    keyword: "required",
    message: "this field is required",
    params: {},
    parentSchema: "",
    schema: "",
    schemaPath: ""
  },
  {
    data: {},
    dataPath: ".partnerUrl.en-US",
    keyword: "required",
    message: "this field is required",
    params: {},
    parentSchema: "",
    schema: "",
    schemaPath: ""
  }
];

const errorObj = {
  awardType: [errors[2]],
  offerType: [errors[0]],
  qualifier: [errors[1]],
  "tiers[0].content[0].en-US": [errors[3]],
  "tiers[0].content[1].en-US": [errors[4]],
  "tiers[1].content[0].en-US": [errors[5]],
  startDate: [errors[6]],
  "partnerUrl.en-US": [errors[7]]
};
describe("ErrorSummary", () => {
  describe("buildErrorMessage", () => {
    it("builds error message given IValidationErrors[]", () => {
      const builder = buildErrorMessage("offerType");
      const errorMessage = builder(errors[0]);
      expect(errorMessage).toBeDefined();
    });
  });

  describe("removeIndexDataPath", () => {
    it("removes index from inside []", () => {
      const path = removeIndexDataPath("tiers[0].content[1].en-US");
      expect(path).toEqual("tiers[].content[].en-US");
    });
  });
  
  describe("getErrorsBySection", () => {
    it("getErrorsBySection", () => {
      const sortedErrors = getErrorsBySection(errorObj);
      expect(sortedErrors.partnerSectionFields).toBeUndefined();
      expect(
        sortedErrors.offerTypeSectionFields &&
        sortedErrors.offerTypeSectionFields.length
      ).toEqual(3);
      expect(
        sortedErrors.offerTypeSectionFields &&
        sortedErrors.offerTypeSectionFields[0]
      ).toEqual(["offerType", [errors[0]]]);
      expect(
        sortedErrors.offerTypeSectionFields &&
        sortedErrors.offerTypeSectionFields[1]
      ).toEqual(["qualifier", [errors[1]]]);
      expect(
        sortedErrors.offerTypeSectionFields &&
        sortedErrors.offerTypeSectionFields[2]
      ).toEqual(["awardType", [errors[2]]]);
      expect(
        sortedErrors.offerContentSectionFields &&
        sortedErrors.offerContentSectionFields.length
      ).toEqual(3);
      expect(
        sortedErrors.offerContentSectionFields &&
        sortedErrors.offerContentSectionFields[0]
      ).toEqual(["tiers[0].content[0].en-US", [errors[3]]]);
      expect(
        sortedErrors.offerSettingsSectionFields &&
        sortedErrors.offerSettingsSectionFields.length
      ).toEqual(1);
      expect(
        sortedErrors.offerSettingsSectionFields &&
        sortedErrors.offerSettingsSectionFields[0]
      ).toEqual(["startDate", [errors[6]]]);
      expect(
        sortedErrors.offerTermsSectionFields &&
        sortedErrors.offerTermsSectionFields.length
      ).toEqual(1);
      expect(
        sortedErrors.offerTermsSectionFields &&
        sortedErrors.offerTermsSectionFields[0]
      ).toEqual(["partnerUrl.en-US", [errors[7]]]);
    });
  });
});
