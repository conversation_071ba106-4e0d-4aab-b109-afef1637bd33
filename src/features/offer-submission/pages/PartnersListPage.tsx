import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@material-ui/core";
import { chain, get, isEmpty } from "lodash";
import React, { ReactNode } from "react";
import { connect } from "react-redux";
import { RouteComponentProps } from "react-router-dom";
import { Action } from "redux";
import { ThunkDispatch } from "redux-thunk";
import { BB8Button, BB8CustomTable, BB8MainPage, BB8SystemType } from "../../../BB8";
import { IWithSearchProps, withSearch } from "../../../components/WithSearch";
import { IPartner } from "../../../models/Partner";
import { IRootState, withAuthStateToProps } from "../../../store";
import UserService from "../../administration/services/UserService";
import withAuthorization from "../../auth/components/Authorization";
import { Authorizer } from "../../auth/components/Authorizer";
import { IAuthState } from "../../auth/types";
import * as adminActions from "../actions";

interface IPartnersListPageProps
  extends IAuthState,
    IWithSearchProps,
    RouteComponentProps {
  partners: IPartner[];
  fetchPartners: () => void;
}

interface ISorting {
  direction: string;
  field: string;
}

interface IPartnersListPageStates {
  partners: IPartner[];
  sorting: ISorting;
}

const initialState: IPartnersListPageStates = {
  partners: [],
  sorting: {
    direction: "asc",
    field: "name"
  }
};

const tableHeaders: Array<{
  id: keyof IPartner | "action1" | "action2";
  title: ReactNode;
  sort?: boolean;
}> = [
  {
    id: "fullLogo",
    title: "Partner"
  },
  {
    id: "name",
    title: "Partner Name",
    sort: true
  },
  {
    id: "action1",
    title: "Offers Actions"
  },
  {
    id: "action2",
    title: "Batches Actions"
  }
];

export class PartnersListPage extends React.PureComponent<
  IPartnersListPageProps
> {
  public state = initialState;

  public componentDidMount = () => {
    const { user } = this.props;
    if (user) {
      UserService.getUserPartners(user.email, true)
        .then((partners: IPartner[]) => {
          this.setState({ partners });
        })
        .catch(() => {
          this.context.displayAlert({
            message: "Error while trying to fetch partners",
            variant: "error"
          });
        });
    }
  };

  public sortChanged = (field: string) => {
    const sorting = {
      direction: this.state.sorting.direction === "asc" ? "desc" : "asc",
      field
    };
    this.setState({ sorting });
  };

  public render() {
    const { searchString, onSearchChange, onSearchClear } = this.props;
    const { partners, sorting } = this.state;
    const options = this.getSuggestions(searchString, partners);

    return (
      <BB8MainPage type={BB8SystemType.Billing}>
        <Grid container={true} direction="column">
          <BB8MainPage.Padded>
            <Grid container={true} spacing={0} direction="row">
              <h3 style={{ margin: "1em 0" }}>My Partners</h3>
            </Grid>
            <Grid item={true} md={4}>
              <TextField
                id="search-partner-name"
                placeholder="Search by partner name"
                variant="outlined"
                color="default"
                fullWidth={true}
                value={searchString}
                onChange={onSearchChange}
                InputProps={{
                  endAdornment: searchString && (
                    <Button
                      size="small"
                      variant="text"
                      className="btn-clear-search"
                      onClick={onSearchClear}
                    >
                      &times;
                    </Button>
                  )
                }}
              />
            </Grid>
            <Grid>
              <BB8CustomTable
                tableId={"table-user-list"}
                cols={tableHeaders}
                data={this.tableData(options, sorting)}
                noDataComponent={<div>Could not find any partner(s).</div>}
                pagination={true}
                rowsPerPage={25}
                sorting={sorting}
                sortChange={this.sortChanged}
                classes={ {} }
              />
            </Grid>
          </BB8MainPage.Padded>
        </Grid>
      </BB8MainPage>
    );
  }

  /*  This function populates the table data:
   *  with filtered options if they are passed in
   *  with the entire endpoint if no options are passed in
   */
  private tableData = (filteredPartners: IPartner[], sorting: any) => {
    const data = chain(filteredPartners)
      .orderBy(partner => partner.name.toLowerCase(), sorting.direction)
      .map((partner: IPartner) => ({
        action1: (
          <>
            <BB8Button
              onClick={this.handleGoToOffersPreview(partner.id)}
              variant="outlined"
              color="primary"
              size="large"
              className={"btn-edit"}
            >
              View Offers
            </BB8Button>
            {Authorizer.Instance.checkPermissions(
              ["CREATE_OFFER"],
              this.props.user
            ) && (
              <BB8Button
                onClick={this.handleGoToCreateOffer(partner.id)}
                variant="outlined"
                color="primary"
                size="large"
                className={"btn-edit"}
                style={{ marginLeft: ".5em" }}
              >
                Create Offer
              </BB8Button>
            )}
          </>
        ),
        action2: (
          <>
            <BB8Button
              onClick={this.handleGoToBulkListing(partner.id)}
              variant="outlined"
              color="primary"
              size="large"
              className={"btn-edit"}
            >
              See Batches
            </BB8Button>
            {Authorizer.Instance.checkPermissions(
              ["CREATE_OFFER"],
              this.props.user
            ) && (
              <BB8Button
                onClick={this.handleGoToUploadOffers(partner.id)}
                variant="outlined"
                color="primary"
                size="large"
                className={"btn-edit"}
                style={{ marginLeft: ".5em" }}
              >
                Upload Batch
              </BB8Button>
            )}
          </>
        ),
        fullLogo: (
          <img
            src={get(partner, "fullLogo[0].file.url")}
            alt={partner.name + " logo"}
            className="logo-wrapper"
            style={{ height: "auto" }}
          />
        ),
        name: (
          <span>{partner.name}</span>
        ),
        offers: <span>{partner.offers}</span>,
        id: (
          <a href={`mailto:${partner.id}`}>
            <Tooltip title={partner.id}>
              <span>{partner.id}</span>
            </Tooltip>
          </a>
        ),
        batchUploads: <span>{partner.batchUploads}</span>
      }))
      .value();
    return data;
  };

  /* getSuggestions returns options that match input value */
  private getSuggestions = (value: string, options: IPartner[]) => {
    if (!options) {
      return [];
    }
    if (isEmpty(value)) {
      return options;
    }

    return options.filter(
      suggestion =>
        (suggestion.name &&
          suggestion.name.toLowerCase().includes(value.trim().toLowerCase())) ||
        suggestion.id.toLowerCase().includes(value.trim().toLowerCase())
    );
  };

  private handleGoToOffersPreview = (partnerId: string) => () => {
    this.props.history.push(
      `/offer-submission/my-offers?sort=partnerName&direction=asc&partnerId=${encodeURIComponent(
        partnerId
      )}`
    );
  };

  private handleGoToCreateOffer = (partnerId: string) => () => {
    this.props.history.push(
      `/offer-submission/single?partnerId=${encodeURIComponent(partnerId)}`
    );
  };

  private handleGoToBulkListing = (partnerId: string) => () => {
    this.props.history.push(
      `/offer-submission/my-bulk-uploads?partnerId=${encodeURIComponent(
        partnerId
      )}`
    );
  };

  private handleGoToUploadOffers = (partnerId: string) => () => {
    this.props.history.push(
      `/offer-submission/bulk?partnerId=${encodeURIComponent(partnerId)}`
    );
  };
}

const mapStateToProps = (state: IRootState) => ({
  ...withAuthStateToProps(state),
  partners: state.administration.partners
});

const mapDispatchToProps = (
  dispatch: ThunkDispatch<IRootState, void, Action>
) => ({
  fetchPartners: () => {
    // TODO: This is not being used anywhere. If used need to use UserService instead of PartnerService.getPartners
    dispatch(adminActions.fetchPartnersFlow());
  }
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withAuthorization(withSearch(PartnersListPage), ["VIEW_OFFER"]));
