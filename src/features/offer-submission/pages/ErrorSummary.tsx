import {
  createStyles,
  Grid,
  List,
  ListItem,
  Theme,
  WithStyles,
  withStyles
} from "@material-ui/core";
import classNames from "classnames";
import {
  get,
  isEmpty,
  template,
  trimStart,
  toPairs,
  groupBy,
  flatten,
  uniqBy,
  find
} from "lodash";
import React, { ReactNode } from "react";
import {
  FIELDS_CONFIG,
  SECTIONS_WITH_FIELDS
} from "../../../components/form/configs/fields.config";
import { getStaticFilterName } from "../../../models/IStaticFilterOption";
import { IValidationErrors } from "../../../validation/validator";

type ErrorDictionary = [string, IValidationErrors[]];

const friendlyErrorMessages = {
  required: "is required",
  pattern: "is not in the correct format",
  barcode: "is not a valid barcode",
  url: "is not a valid URL",
  minimum: "is lower than the minimum allowed value",
  maximum: "is higher than the maximum allowed value",
  maxLength: "is longer than maximum allowed length",
  minLength: "is shorter than minimum allowed length",
  size:
    "must be greater than 320px in width and height. It must also not exceed an aspect ratio of 2:1 or 1:2.",
  promotionIsInvalid: "selected promotion is inactive"

  // LEGACY VALIDATION MESSAGES
  // contentCategory: "is longer than maximum allowed length",
  // contentProduct: "is longer than maximum allowed length",
  // couponCode: "is longer than maximum allowed length or has more than one word",
  // descriptionCopy: "is longer than maximum allowed length",
  // exclusions: "is longer than maximum allowed length",
  // inclusionExclusions: "is longer than maximum allowed length",
  // partnerLegalName: "is longer than maximum allowed length",
  // pluCode: "is longer than maximum allowed length or has letters in the code",
  // trademarkInfo: "is longer than maximum allowed length",
  // customAward: "is longer than maximum allowed length",
  // customQualifier: "is longer than maximum allowed length"
};

const templateMessage = template(
  'The field "<%= friendlyName %>" <%= friendlyType %>'
);

function dataPathToParts(dataPath: string): string[] {
  return dataPath.split(".");
}

function isNestedPath(pathParts: string[]): boolean {
  return pathParts.length > 1;
}

export function buildErrorMessage(name: string) {
  return (error: IValidationErrors): ReactNode => {
    if (error) {
      const fieldPathParts = dataPathToParts(name);
      const fieldName = fieldPathParts[fieldPathParts.length - 1];
      const friendlyNames = [
        get(FIELDS_CONFIG, `${fieldName}.title`, fieldName)
      ];
      const friendlyName = friendlyNames.join(" ");
      const friendlyType = get(
        friendlyErrorMessages,
        error.keyword,
        error.keyword
      );
      switch (error.keyword) {
        case "enum": {
          const paramValues = get(error, "params.allowedValues", "");
          if (!isEmpty(paramValues) && Array.isArray(paramValues)) {
            // If the error is mechanism type error, create a custom error summary
            if (error.dataPath.includes("mechanismType")) {
              return `${templateMessage({
                friendlyName,
                friendlyType:
                  "is not filled out for all additional mechanisms. Please fill in all the required fields."
              })}`;
            }
            const paramLabels = paramValues.map((p: string) =>
              getStaticFilterName(name as any, p)
            );
            return `${error.message} ${
              paramLabels ? paramLabels.join(", ") : ""
            }`;
          }
        }
        case "type": {
          return `${templateMessage({
            friendlyName,
            friendlyType: "should be a " + error.params.type
          })}`;
        }

        case "maximum":
        case "minimum":
        case "maxLength":
        case "minLength": {
          return `${templateMessage({
            friendlyName,
            friendlyType
          })} (${error.message})`;
        }
        case "required": {
          return fieldName === 'sponsorCode' || fieldName === 'issuanceCode' ? `Please fill in the required ${friendlyName} field` : "Please fill in all the required fields";
        }
        case "barcode": {
          return "The Barcode Value is not a valid barcode.";
        }
        case "format": {
          if (error.params.format === "uri") {
            return "Invalid URL provided. URLs should start with http(s):";
          } else {
            return error.message;
          }
        }
        case "pattern": {
          return "This field is not in a valid format.";
        }
        case "qualifierLong": {
          return `The sum of the qualifier long values exceeds 256. Edit one of these fields to make it less than 256.`;
        }
        case "cardTypeEmpty":{
          return "Card Type is a mandatory field.";
        }
        case "retailerGroupEmpty":{
          return "Retailer Group is a mandatory field.";
        }
        case "startDateGreaterThanEndDate":{
          return "Start date cannot be after end date.";
        }
        case "startDateLessThanDisplayDate":{
          return "Start date cannot be before display date.";
        }
        case "displayDateGreaterThanEndDate":{
          return "Display date cannot be after end date.";
        }
        case "startDateGreaterThanCurrentDate":{
          return "Start date cannot be before current date.";
        }
        case "displayDateGreaterThanCurrentDate":{
          return "Display date cannot be before current date.";
        }
        case "endDateGreaterThanCurrentDate":{
          return "End date cannot be before current date. To disable an Offer, go to My Offers or Preview page, and select Disable.";
        }
        case "firstQualificationDateGreaterThanCurrentDate":{
          return "First qualification date cannot be before current date.";
        }
        case "lastQualificationDateGreaterThanCurrentDate":{
          return "Last qualification date cannot be before current date.";
        }
        case "lastQualificationDateLowerThanFirstQualificationDate":{
          return "Last qualification date cannot be before First qualification date.";
        }
        case "eligibilityDurationInteger":{
          return "Collector's eligibility duration must be an integer value.";
        }
        case "eligibilityDurationEmpty":{
          return "Collector's eligibility duration is mandatory.";
        }
        case "usageLimitInteger":{
          return "Usage limit per collector must be an integer value.";
        }
        case "usageLimitEmpty":{
          return "Usage limit per collector is mandatory.";
        }
        case "ctaField": {
          return "All CTA Label & CTA Url fields are required.";
        }
        default: {
          return `${templateMessage({
            friendlyName,
            friendlyType
          })}`;
        }
      }
    }
    return "";
  };
}

export function removeIndexDataPath(dataPath: string): string {
  let result = "";
  let betweenBrackets = false;
  for (const char of dataPath) {
    if (char === "[") {
      // append and toggle flag
      result = result.concat(char);
      betweenBrackets = true;
    } else if (char === "]") {
      betweenBrackets = false;
    }

    if (betweenBrackets) {
      continue;
    }
    result = result.concat(char);
  }
  return result;
}

export function getErrorsBySection(errors: {
  [key: string]: IValidationErrors[];
}): ISectionWithFields {
  if (isEmpty(errors)) {
    return {};
  }
  const pairs: ErrorDictionary[] = toPairs(errors);
  const noIndexPathMap: { [key: string]: ErrorDictionary[] } = groupBy(
    pairs,
    ([key, value]) => {
      return removeIndexDataPath(key);
    }
  );

  const ret: any = {};

  Object.keys(SECTIONS_WITH_FIELDS).forEach((section: any) => {
    const errorsForSection = getSortedFilteredErrors(
      get(SECTIONS_WITH_FIELDS, section),
      noIndexPathMap
    );
    if (errorsForSection.length) {
      ret[section] = errorsForSection;
    }
  });

  return ret;
}

const getSortedFilteredErrors = (
  fieldsOrder: string[],
  noIndexPathMap: any
) => {
  const tuples = fieldsOrder.map(field => {
    const validationErrorsMap = get(
      noIndexPathMap,
      field,
      []
    ) as ErrorDictionary[];
    return validationErrorsMap.map(([path, validationErrors]) => {
      return [trimStart(path, "."), validationErrors] as ErrorDictionary;
    }) as ErrorDictionary[];
  });

  return flatten(tuples).filter(tuple => {
    return !isEmpty(tuple[0]) && !isEmpty(tuple[1]);
  });
};

export const styles = createStyles((theme: Theme) => ({
  listItem: {
    cursor: "pointer",
    marginBottom: 2,
    "&:hover": {
      boxShadow: theme.shadows[1]
    }
  }
}));

const ErrorSummary: React.FunctionComponent<
  WithStyles & {
    errors: ErrorDictionary[];
  }
> = ({ classes, errors }) => {
  function goto(key: string) {
    return () => {
      if (document) {
        const el = document.getElementById(key);
        if (el && el instanceof Node) {
          // This is for cases where the error belongs to a dropdown. When it's a dropdown, we try to scroll to the the hidden input attached to the select, but because the input is hidden, the scrollIntoView won't trigger.
          // In order to get the scroll effect to work, we have to let the input be seen on the page, scroll to it, and then hide it again.
          const isHiddenElement: boolean = (el as any).type === "hidden";
          if (isHiddenElement) {
            (el as any).type = "";
          }
          el.scrollIntoView({
            behavior: "smooth",
            block: "center",
            inline: "nearest"
          });
          if (isHiddenElement) {
            el.setAttribute("type", "hidden");
          }
        }
      }
    };
  }

  let foundRequired = false;
  let foundQualifierLong = false;

  return (
    <List color="error">
      {errors.map(([key, errorList]) => {
        return (
          errorList &&
          errorList.length &&
          errorList.map((error, index) => {
            if (
              (error.keyword !== "required" || !foundRequired) &&
              (error.keyword !== "qualifierLong" || !foundQualifierLong)
            ) {
              if (error.keyword === "required") {
                foundRequired = true;
              }
              if (error.keyword === "qualifierLong") {
                foundQualifierLong = true;
              }
              return (
                <ListItem
                  title={key}
                  key={key + "-" + index}
                  onClick={goto(key)}
                  className={classNames(
                    "am-alert am-alert--fail",
                    classes.listItem
                  )}
                >
                  <Grid className="am-alert__container">
                    <span className="am-alert__icon am-icon-alert" />
                    {buildErrorMessage(key)(error)}
                  </Grid>
                </ListItem>
              );
            }
          })
        );
      })}
    </List>
  );
};

export interface ISectionWithFields {
  partnerSectionFields?: ErrorDictionary[];
  offerTypeSectionFields?: ErrorDictionary[];
  offerCategorySectionFields?: ErrorDictionary[];
  offerContentSectionFields?: ErrorDictionary[];
  offerSettingsSectionFields?: ErrorDictionary[];
  offerTermsSectionFields?: ErrorDictionary[];
}

export default withStyles(styles)(ErrorSummary);
