import { createStyles, Grid, Theme, Typography, WithStyles, withStyles } from "@material-ui/core";
import { chain, flatten, get, isEmpty, set, sortBy, toPairs, unset } from "lodash";
import React, { PureComponent, ReactNode } from "react";
import { connect } from "react-redux";
import { Link } from "react-router-dom";
import { from, Subscription, timer } from "rxjs";
import { concatMap, filter } from "rxjs/operators";
import { BB8Button, BB8MainPage, BB8Spinner, BB8SystemType } from "../../../BB8";
import FileUploader from "../../../components/FileUploader";
import { IBulkJob } from "../../../models/BulkJob";
import { OffersRequestParams } from "../../../models/OffersRequestParams";
import { IPartner } from "../../../models/Partner";
import { onlyNonEmptyValidURL } from "../../../shared/helpers";
import { convertJsonToCsv, mapErrorObject, mapImageProcessingErrors } from "../../../shared/helpers/csvJsonConverter";
import imageService, {
  IFileProcessingData,
  IProcessImageBatchResult,
  IProcessImagesResponseBody
} from "../../../shared/services/image.service";
import {
  IRootState,
  IWithCommunicationProps,
  withAuthStateToProps,
  withCommunicationDispatchToProps
} from "../../../store";
import { IOfferFormModel, IValidationErrors } from "../../../validation/validator";
import UserService from "../../administration/services/UserService";
import withAuthorization from "../../auth/components/Authorization";
import { IAuthState } from "../../auth/types";
import { SnackbarContext } from "../../communication/AlertContainer";
import BulkForm, { IBulkFormValues } from "../components/BulkFileUploadForm";
import OffersService from "../services/OffersService";
import { getZIPFile } from "../utils";

// the sequence of stages is
//  0. "INIT",
//  1.a. "SCHEMA_VALIDATION_ERROR",
//  1.b. "SCHEMA_VALIDATION_SUCCESS",
//  2.a. "ZIP_FILE_UPLOAD_INIT",
//  2.b. "ZIP_FILE_UPLOAD_SUCCESS",
//  2.c. "ZIP_FILE_UPLOAD_ERROR",
//  3.a. "PROCESS_IMAGE_INIT",
//  3.b. "PROCESS_IMAGE_SUCCESS",
//  3.c. "PROCESS_IMAGE_ERROR",
//  4. "READY_TO_UPLOAD"
enum BulkProcessStage {
  "INIT",
  "SCHEMA_VALIDATION_ERROR",
  "SCHEMA_VALIDATION_SUCCESS",
  "ZIP_FILE_UPLOAD_INIT",
  "ZIP_FILE_UPLOAD_SUCCESS",
  "ZIP_FILE_UPLOAD_ERROR",
  "PROCESS_IMAGE_INIT",
  "PROCESS_IMAGE_SUCCESS",
  "PROCESS_IMAGE_ERROR",
  "READY_TO_UPLOAD",
  "FILE_UPLOAD_IN_PROGRESS",
  "FILE_UPLOAD_SUCCESS",
  "FILE_UPLOAD_ERROR"
}
interface IBulkProps extends WithStyles, IAuthState, IWithCommunicationProps {
  search: string;
}

interface IBulkState {
  stage: BulkProcessStage;
  isLoading: boolean;
  pollingVal: any;
  formValues?: IBulkFormValues;
  offerSchemaArray?: IOfferFormModel[];
  errorObjects?: Array<{
    data: IOfferFormModel;
    validationResults: IValidationErrors[];
  }>;
  imageProcessingResult?: IProcessImageBatchResult;
  errorCount?: number;
  partners: IPartner[];
  preselectedPartner?: IPartner;
  uploadErrors?: string;
  uploadError?: string;
  createdBulkId?: string;
  isLongPolling: boolean;
  existingBulkJob?: IBulkJob;
}

const initialState: IBulkState = {
  isLoading: true,
  pollingVal: {},
  stage: BulkProcessStage.INIT,
  partners: [],
  uploadError: "",
  isLongPolling: false,
  errorObjects: [],
  errorCount: 0
};

const BULK_UPLOAD_MAX_OFFERS_EXCEED_BACKEND_ERROR_MESSAGE = "Number of offers in bulk must be between"
const BULK_UPLOAD_MAX_OFFERS_EXCEED_USER_ERROR_MESSAGE = "The batch file cannot contain more than 800 offers.\nPlease upload smaller batch files with 800 or fewer offers."

class Bulk extends PureComponent<IBulkProps, IBulkState> {
  public static contextType = SnackbarContext;
  public contextType: typeof SnackbarContext;
  public state = initialState;
  private formRef = React.createRef<FileUploader>();
  private polling: Subscription;

  public componentDidMount = () => {
    const { user } = this.props;
    const params = OffersRequestParams.fromQueryString(this.props.search);

    const bulkJobPromise = params.bulkId
      ? OffersService.getBulkJobById(params.bulkId).catch(() => {
          this.context.displayAlert({
            message: "Error while trying to fetch bulkJob",
            variant: "error"
          });
        })
      : Promise.resolve(undefined);

    const userServicePromise = user
      ? UserService.getUserPartners(user.email, true).catch(() => {
          this.context.displayAlert({
            message: "Error while trying to fetch partners",
            variant: "error"
          });
        })
      : Promise.resolve(undefined);

    const bulkOffersQueryString: string = params.bulkId
      ? `?bulkId=${params.bulkId}`
      : "";

    const offersInBulkJobPromise = params.bulkId
      ? OffersService.getPaginatedPreviewOffers(bulkOffersQueryString).catch(
          () => {
            this.context.displayAlert({
              message: "Error while getting all offers with given bulkJob id.",
              variant: "error"
            });
          }
        )
      : Promise.resolve(undefined);

    Promise.all([
      bulkJobPromise,
      userServicePromise,
      offersInBulkJobPromise
    ])
      .then(values => {
        const [bulkJob, partners, offersInBulkJob] = values;
        let preselectedPartner: IPartner | undefined;

        if (bulkJob) {
          bulkJob.offers = offersInBulkJob;
        }

        if (partners && params && !bulkJob) {
          preselectedPartner = partners.find(
            partner => partner.id === params.partnerId
          );
          this.setState({
            partners,
            preselectedPartner,
            isLoading: false
          });
        } else if (partners && bulkJob && !preselectedPartner) {
          preselectedPartner = partners.find(
            partner => partner.id === bulkJob.partnerId
          );
          this.setState({
            partners,
            preselectedPartner,
            isLoading: false,
            existingBulkJob: bulkJob
          });
        }
      })
      .finally(() => {
        this.setState({
          isLoading: false
        });
      });
  };

  public render() {
    const {
      formValues,
      isLoading,
      stage,
      partners,
      preselectedPartner,
      createdBulkId,
      existingBulkJob
    } = this.state;
    const { classes } = this.props;

    return (
      <BB8MainPage type={BB8SystemType.Billing}>
        <BB8MainPage.Padded>
          <Grid container={true} direction="column">
            <Grid item={true} xs={6} className={classes.grid}>
              <Typography variant="h5">
                {existingBulkJob
                  ? "Update Existing Offers"
                  : "Create New Offers"}
              </Typography>
              <BulkForm
                innerRef={this.formRef}
                partners={partners}
                preselectedPartner={preselectedPartner}
                onValidationError={this.handleSyncValidationError}
                onValidationSuccess={this.handleSyncValidationSuccess}
                existingBulkJob={existingBulkJob}
              />

              {(stage === BulkProcessStage.SCHEMA_VALIDATION_ERROR ||
                stage === BulkProcessStage.PROCESS_IMAGE_ERROR) && (
                <Grid
                  container={true}
                  spacing={1}
                  direction="row"
                  justify="center"
                >
                  <Grid item={true} xs={12}>
                    <Typography variant="body2" color="error" align="center">
                      Please download the error log below and correct the issues
                      identified before trying again.
                    </Typography>
                    <Typography variant="body2" color="error" align="center">
                      {this.getValidationErrorMessage()}
                    </Typography>
                  </Grid>
                  <Grid item={true} xs={4}>
                    <BB8Button
                      color="primary"
                      variant="contained"
                      fullWidth={true}
                      onClick={this.handleDownloadErrors}
                    >
                      Download error log
                    </BB8Button>
                  </Grid>
                  <Grid item={true} xs={4}>
                    <BB8Button
                      color="primary"
                      variant="contained"
                      fullWidth={true}
                      onClick={this.retry}
                    >
                      Reset Files
                    </BB8Button>
                  </Grid>
                  <Grid item={true} xs={4}>
                    <BB8Button
                      color="primary"
                      variant="contained"
                      fullWidth={true}
                      onClick={this.reset}
                    >
                      Clear
                    </BB8Button>
                  </Grid>
                </Grid>
              )}
              {stage === BulkProcessStage.READY_TO_UPLOAD && (
                <Grid
                  container={true}
                  spacing={1}
                  direction="row"
                  justify="center"
                >
                  <Grid item={true} xs={6}>
                    <BB8Button
                      color="primary"
                      variant="contained"
                      fullWidth={true}
                      onClick={
                        existingBulkJob ? this.saveOffers : this.uploadOffers
                      }
                    >
                      {existingBulkJob ? "Save Offers" : "Upload offers"}
                    </BB8Button>
                  </Grid>
                  <Grid item={true} xs={6}>
                    <BB8Button
                      color="primary"
                      variant="contained"
                      fullWidth={true}
                      onClick={this.reset}
                    >
                      Cancel
                    </BB8Button>
                  </Grid>
                </Grid>
              )}
              {stage === BulkProcessStage.FILE_UPLOAD_ERROR && (
                <Grid
                  container={true}
                  spacing={1}
                  direction="row"
                  justify="center"
                >
                  <Grid item={true} xs={12}>
                    <Typography variant="body2" align="center">
                      {"Your "}
                      {formValues
                        ? get(formValues, "partner.name")
                        : "Partners"}
                      {" offers with Bulk Name "}
                      {formValues ? get(formValues, "name") : "Default ID"}
                      {" have not been uploaded due to the following error(s)"}
                      <Typography variant="body2" color="error" align="center">
                        {this.state.uploadError}
                      </Typography>
                    </Typography>
                  </Grid>
                  <Grid item={true} xs={6}>
                    <BB8Button
                      color="primary"
                      variant="contained"
                      fullWidth={true}
                      onClick={this.reset}
                    >
                      Retry
                    </BB8Button>
                  </Grid>
                </Grid>
              )}
              {stage === BulkProcessStage.FILE_UPLOAD_SUCCESS && (
                <Grid
                  container={true}
                  spacing={1}
                  direction="row"
                  justify="center"
                >
                  <Grid item={true} xs={12}>
                    <Typography variant="body2" align="center">
                      Your{" "}
                      {formValues
                        ? get(formValues, "partner.name")
                        : "Partners"}{" "}
                      offers have been uploaded successfully with Bulk Name{" "}
                      {formValues ? get(formValues, "name") : "Default Name"}
                    </Typography>
                    <Typography variant="body2" align="center">
                      You can navigate to the{" "}
                      <Link
                        to={`/offer-submission/my-offers?bulkId=${
                          existingBulkJob ? existingBulkJob.id : createdBulkId
                        }${
                          formValues
                            ? "&partnerId=" + get(formValues, "partner.id")
                            : ""
                        }`}
                      >
                        My Offers Page
                      </Link>{" "}
                      to review the offers in the offer bank.
                    </Typography>
                  </Grid>
                  <Grid item={true} xs={6}>
                    <BB8Button
                      color="primary"
                      variant="contained"
                      fullWidth={true}
                      onClick={this.reset}
                    >
                      Done
                    </BB8Button>
                  </Grid>
                </Grid>
              )}
            </Grid>
          </Grid>
        </BB8MainPage.Padded>
        <BB8Spinner show={isLoading} size={200} />
      </BB8MainPage>
    );
  }

  public componentWillUnmount() {
    if (this.polling && this.polling.unsubscribe) {
      this.polling.unsubscribe();
    }
  }

  private handleSyncValidationError = (
    values: IBulkFormValues,
    errorObjects?: Array<{
      data: IOfferFormModel;
      validationResults: IValidationErrors[];
    }>,
    errorCount?: number
  ) => {
    // start from scratch is user unselects / clears partner
    if (isEmpty(values) || isEmpty(values.partner)) {
      this.setState(
        {
          formValues: values,
          stage: BulkProcessStage.INIT
        },
        () => {
          if (this.formRef && this.formRef.current) {
            this.formRef.current.resetFiles();
          }
        }
      );
      return;
    }

    this.setState({
      errorCount,
      errorObjects,
      formValues: values,
      stage:
        errorObjects && errorObjects.length && errorCount
          ? BulkProcessStage.SCHEMA_VALIDATION_ERROR
          : BulkProcessStage.INIT
    });
  };

  private handleDownloadErrors = () => {
    const allErrors = this.getErrorsWithImageResults();
    const csvErrors = convertJsonToCsv(sortBy(allErrors, "row"));
    const element = document.createElement("a");
    const file = new Blob([csvErrors], { type: "text/csv" });
    element.href = URL.createObjectURL(file);
    element.download = "Error Log.csv";
    element.click();
  };

  private getErrorsWithImageResults = () => {
    const errorsJson = [];

    // Go through the image processing result and map the failed images to the
    if (this.state.imageProcessingResult && this.state.offerSchemaArray) {
      errorsJson.push(
        flatten(
          mapImageProcessingErrors(
            this.state.offerSchemaArray,
            this.state.imageProcessingResult
          )
        )
      );
    }

    if (this.state.errorObjects) {
      errorsJson.push(flatten(mapErrorObject(this.state.errorObjects)));
    }
    return flatten(errorsJson);
  };

  private handleSyncValidationSuccess = (
    values: IBulkFormValues,
    offerSchemaArray?: IOfferFormModel[]
  ) => {
    this.setState(
      {
        offerSchemaArray,
        formValues: values
      },
      () => {
        const zipFile = getZIPFile(values);
        if (offerSchemaArray) {
          // If a zip file was added, then upload and include it during the processing time by
          // generating an S3 presigned URL, uploading zip into that URL and submitting the zip into
          // processing
          const external_images = getValidUniqueURLs(offerSchemaArray)

          if (zipFile) {
            // Collect a list of all of the image paths that would require being uploaded from the zip
            const imagePaths = chain(offerSchemaArray)
              .map(getImages)
              .flatten()
              .uniq()
              .compact()
              .value()
              .filter(path => !onlyNonEmptyValidURL(path));

            let presignedUrl: string;
            imageService
              .generateS3Url({
                fileLocationPathForUpload: zipFile.name,
                mimeType: "application/zip"
              })
              .then(response => {
                presignedUrl = response.signedURLForUpload;
                return this.startZipUploading(presignedUrl, zipFile.data);
              })
              .then(() => {
                return imageService.processImages({
                  zip_file: {
                    location: getFileLocationFromPresignedURL(presignedUrl),
                    files_to_process: imagePaths
                  },
                  external_images
                });
              })
              .then(this.startImageProcessingLongPolling);
          } else if (isEmpty(zipFile) && !isEmpty(external_images)) {
            imageService
              .processImages({
                external_images
              })
              .then(this.startImageProcessingLongPolling);
          } else {
            this.setState(actualState => ({
              ...actualState,
              isLoading: false,
              stage: BulkProcessStage.READY_TO_UPLOAD
            }));
          }
        }
      }
    );
  };

  private getValidationErrorMessage: () => ReactNode = () => {
    const { errorObjects, errorCount } = this.state;
    if (errorObjects) {
      const allErrorObjects = this.getErrorsWithImageResults();
      const rowsCount = errorObjects.length;
      const rowsErroredCount =
        errorCount ||
        errorObjects.filter(
          e => e.validationResults && e.validationResults.length
        ).length;

      const isPlural = (errorCount || 0) > 1;
      return `${rowsErroredCount}
       of ${rowsCount}
      offers need to be fixed
      before we can add them to POST.
      ${allErrorObjects.length} ${isPlural ? "errors" : "error"} ${
        isPlural ? "were" : "was"
      }
      detected in total`;
    }
    return "";
  };

  private startZipUploading = (presignedUrl: string, zipData: Blob) => {
    this.setState({
      isLoading: true,
      stage: BulkProcessStage.ZIP_FILE_UPLOAD_INIT
    });

    return imageService.uploadToS3(presignedUrl, zipData, "application/zip");
  };

  private startImageProcessingLongPolling = (
    response: IProcessImagesResponseBody
  ) => {
    if (!this.state.isLongPolling) {
      this.setState(
        {
          isLoading: true,
          stage: BulkProcessStage.PROCESS_IMAGE_INIT,
          isLongPolling: true
        },
        () => {
          // start long polling to fetch image processing batch status
          this.polling = timer(0, 1000)
            .pipe(
              concatMap(_ =>
                from(
                  imageService.getProcessInfo(response.id).catch(error => {
                    if (error.statusCode !== 400) {
                      this.polling.unsubscribe();
                    }
                    return error;
                  })
                )
              ),
              filter(val => val.code !== "PROCESSING_JOB_NOT_DONE")
            )
            .subscribe(this.imageProcessingSubscriptionHandler);
        }
      );
    }
  };

  private imageProcessingSubscriptionHandler = (
    val: IProcessImageBatchResult
  ) => {
    // TODO: ensure all items were in the zip
    const errorPairs = val
      ? toPairs(val).filter(([key, value]) => !value.success)
      : [];
    const isSuccess = errorPairs.length
      ? errorPairs.every(([_, value]) => value.success)
      : true;
    const newState: any = {
      isLoading: false,
      imageProcessingResult: val,
      stage: isSuccess
        ? BulkProcessStage.READY_TO_UPLOAD
        : BulkProcessStage.PROCESS_IMAGE_ERROR,
      isLongPolling: false
    };

    if (
      isSuccess &&
      this.state.offerSchemaArray &&
      newState.imageProcessingResult
    ) {
      newState.offerSchemaArray = replaceImageUrls(
        this.state.offerSchemaArray,
        newState.imageProcessingResult
      );
    }

    this.polling.unsubscribe();

    this.setState(actualState => ({
      ...actualState,
      ...newState,
      errorCount: (actualState.errorCount || 0) + errorPairs.length,
      errorObjects: [
        ...(actualState.errorObjects ? actualState.errorObjects : []),
        ...errorPairs.map(([url, errorObject]) => ({
          data: {} as IOfferFormModel,
          validationResults: [] as IValidationErrors[]
        }))
      ]
    }));
  };

  private saveOffers = () => {
    this.setState({ isLoading: true });
    const bulkId = this.state.existingBulkJob ? this.state.existingBulkJob.id : null;
    const bulkOfferObject = {
      bulkName: this.state.formValues ? this.state.formValues.name : null,
      offers: this.state.offerSchemaArray
    };
    OffersService.callBulkUpdate(bulkId, bulkOfferObject)
      .then(() => {
        this.setState({
          stage: BulkProcessStage.FILE_UPLOAD_SUCCESS,
          isLoading: false
        });
      })
      .catch((error: Error) => {
        console.error("callBulkUpdate() error=", error)
        this.setState({
          stage: BulkProcessStage.FILE_UPLOAD_ERROR,
          uploadError: (error.message.includes(BULK_UPLOAD_MAX_OFFERS_EXCEED_BACKEND_ERROR_MESSAGE))? BULK_UPLOAD_MAX_OFFERS_EXCEED_USER_ERROR_MESSAGE : error.message,
          isLoading: false
        });
      });
  };

  private uploadOffers = () => {
    this.setState({ isLoading: true });
    const bulkOfferObject = {
      bulkName: this.state.formValues ? this.state.formValues.name : null,
      offers: this.state.offerSchemaArray
    };
    OffersService.callUploadOffer(bulkOfferObject)
      .then(response => {
        this.setState({
          stage: BulkProcessStage.FILE_UPLOAD_SUCCESS,
          createdBulkId: response.id,
          isLoading: false
        });
      })
      .catch((error: Error) => {
        console.error("callUploadOffer() error=", error)
        this.setState({
          stage: BulkProcessStage.FILE_UPLOAD_ERROR,
          uploadError: (error.message.includes(BULK_UPLOAD_MAX_OFFERS_EXCEED_BACKEND_ERROR_MESSAGE))? BULK_UPLOAD_MAX_OFFERS_EXCEED_USER_ERROR_MESSAGE : error.message,
          isLoading: false
        });
      });
  };

  private reset = () => {
    this.setState(initialState, () => {
      location.reload();
    });
  };

  private retry = () => {
    const modifiedInitialState: IBulkState = Object.assign({}, initialState);
    unset(modifiedInitialState, "partners");

    this.setState(
      currentState => ({
        ...modifiedInitialState,
        formValues: {
          files: [],
          imageProcessingResult: null,
          name: currentState.formValues ? currentState.formValues.name : "",
          partner: currentState.formValues
            ? currentState.formValues.partner
            : ({} as IPartner)
        },
        errorObjects: [],
        errorCount: 0,
        isLoading: false
      }),
      () => {
        if (this.formRef && this.formRef.current) {
          this.formRef.current.resetFiles();
        }
      }
    );
  };
}

const mapStateToProps = (state: IRootState) => ({
  ...withAuthStateToProps(state),
  partners: state.administration.partners,
  search: state.router!.location.search
});

const styles = createStyles((theme: Theme) => ({
  grid: {
    marginTop: theme.spacing(4),
    minWidth: 250
  }
}));

export default connect(
  mapStateToProps,
  withCommunicationDispatchToProps
)(withStyles(styles)(withAuthorization(Bulk, ["CREATE_OFFER"])));

function replaceImageUrls(
  offerSchemaArray: IOfferFormModel[],
  imageProcessingResult: IProcessImageBatchResult
): IOfferFormModel[] {
  return offerSchemaArray.map(offer => {
    const enImage: string = get(offer, "image.en-US.path");
    const frImage: string = get(offer, "image.fr-CA.path");

    if (enImage) {
      set(
        offer,
        "image.en-US.path",
        get(imageProcessingResult, [enImage, "processed_s3_location"])
      );
    }
    if (frImage) {
      set(
        offer,
        "image.fr-CA.path",
        get(imageProcessingResult, [frImage, "processed_s3_location"])
      );
    }
    return offer;
  });
}

function getValidUniqueURLs(
  offerSchemaArray: IOfferFormModel[]
): IFileProcessingData[] {
  return chain(offerSchemaArray)
    .map(getImages)
    .flatten()
    .compact()
    .filter(onlyNonEmptyValidURL)
    .uniq()
    .value()
    .map(s => {
      return {
        location: s
      };
    });
}

export function getImages(d: IOfferFormModel) {
  return [get(d, "image.en-US.path"), get(d, "image.fr-CA.path")];
}

function getFileLocationFromPresignedURL(presignedUrl: string): string {
  return presignedUrl.substring(0, presignedUrl.indexOf("?"));
}
