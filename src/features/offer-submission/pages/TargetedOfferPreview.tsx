import { createStyles, Grid, Theme, Typography, withStyles, WithStyles } from "@material-ui/core";
import jspdf from "jspdf";
import "moment/locale/en-ca";
import "moment/locale/fr";
import React, { PureComponent } from "react";
import ReactDOM from "react-dom";
import { connect } from "react-redux";
import { RouteComponentProps } from "react-router";
import { Action } from "redux";
import { ThunkDispatch } from "redux-thunk";
import { BB8Button, BB8MainPage, BB8Spinner, BB8SystemType } from "../../../BB8";
import { IWithSearchProps } from "../../../components/WithSearch";
import { IOfferCategory } from "../../../models/Category";
import { IOfferFilter } from "../../../models/OffersRequestParams";
import { IPartner } from "../../../models/Partner";
import { IOfferPromotion } from "../../../models/Promotion";
import { queryStringToObject } from "../../../shared/helpers";
import {
  IRootState,
  IWithCommunicationProps,
  withAuthStateToProps,
  withCommunicationDispatchToProps
} from "../../../store";
import { IOfferFormModel, IOfferFormModelWithNames } from "../../../validation/validator";
import { UserService } from "../../administration/services";
import withAuthorization from "../../auth/components/Authorization";
import { SnackbarContext } from "../../communication/AlertContainer";
import { fetchCategoriesAsync, fetchPromotionsAsync } from "../actions";
import OfferBlock from "../components/OfferBlock";
import { OfferActionType } from "../components/OffersTable";
import TargetedOfferForm from "../components/TargetedOfferForm";
import { exportToPDF, processExportToPDF } from "../helpers/pdfHelper";
import OfferService from "../services/OffersService";
import { getOffersWithReadableNames } from "../utils";

interface IOfferPreviewProps
  extends IWithSearchProps,
    IWithCommunicationProps,
    RouteComponentProps,
    WithStyles {
  user: any;
  filter: IOfferFilter;
  promotions: IOfferPromotion[];
  fetchPromotions: () => void;
  categories: IOfferCategory[];
  fetchCategories: () => void;
}
interface IOfferPreviewState {
  isLoading: boolean;
  loadedNoOffers: boolean;
  offers: IOfferFormModel[];
  partners: IPartner[];
}

const initialState: IOfferPreviewState = {
  isLoading: true,
  loadedNoOffers: false,
  offers: [],
  partners: []
};

class TargetedOfferPreview extends PureComponent<IOfferPreviewProps, IOfferPreviewState> {
  public static contextType = SnackbarContext;
  public contextType: typeof SnackbarContext;
  public state = initialState;
  // For ForwardRefs of the OfferBlock Components
  private OfferComponents: any[] = [];

  public componentDidMount = async () => {
    const partners = await UserService.getUserPartners(
      this.props.user.email,
      true
    );
    this.setState({
      partners
    });

    this.props.fetchPromotions();
    this.props.fetchCategories();

    if (this.props.location.search) {
      await this.fetchOffers();
    }

    this.setState({
      isLoading: false
    });
  };

  public componentDidUpdate = async (prevProps: IOfferPreviewProps) => {
    if (this.props.location.search !== prevProps.location.search) {
      await this.fetchOffers();

      this.setState({ isLoading: false });
    }
  };

  public render() {
    const { isLoading } = this.state;
    const { classes } = this.props;

    return (
      <BB8MainPage type={BB8SystemType.Billing}>
        <BB8MainPage.Padded>
          <Grid container={true}>
            <TargetedOfferForm
              partners={this.state.partners}
              initialState={queryStringToObject(this.props.location.search)}
            />
          </Grid>
          {this.OfferComponents.length > 0 && (
            <Grid container={true} direction="column">
              <Grid
                item={true}
                container={true}
                direction="row"
                spacing={5}
                className={classes.grid}
              >
                {/* Header and buttons  */}
                <Grid item={true} xs={9}>
                  <Typography gutterBottom={true} variant="h5">
                    Preview Targeted Offers
                  </Typography>
                </Grid>
                <Grid item={true} xs={2}>
                  <BB8Button
                    color="primary"
                    variant="contained"
                    fullWidth={true}
                    onClick={async () => {
                      await this.setState({ isLoading: true });
                      await exportToPDF(
                          undefined,
                          this.OfferComponents.map(offerComponent => offerComponent.current),
                          this.processExportToPDF
                      );
                      this.setState({ isLoading: false });
                    }}
                  >
                    Export All Offers to PDF
                  </BB8Button>
                </Grid>
              </Grid>
              {/* Offer Block Component */}
              {this.OfferComponents.map((OfferComponent, index) => (
                <Grid item={true} container={true} md={12} lg={11}>
                  <OfferComponent
                    key={index}
                    ref={this.OfferComponents[index]}
                  />
                </Grid>
              ))}
            </Grid>
          )}
          {this.state.loadedNoOffers && (
            <Grid item={true} xs={9}>
              <Typography gutterBottom={true} variant="h5">
                No Offers Found
              </Typography>
            </Grid>
          )}
        </BB8MainPage.Padded>
        <BB8Spinner show={isLoading} size={200} />
      </BB8MainPage>
    );
  }

  private async fetchOffers() {
    this.setState({ isLoading: true });

    let agilityIdList: string[] = [];
    const { collectorNumber, partnerId, date } = queryStringToObject(
      this.props.location.search
    );

    if (collectorNumber && partnerId && date) {
      const response = await OfferService.getTargetedOffers(
        queryStringToObject(this.props.location.search)
      );
      agilityIdList = response.data ? response.data.map((i: { id: string; }) => i.id): [];
    }

    const offerList: IOfferFormModel[] =
      agilityIdList.length > 0
        ? await OfferService.getOffersByIdList(agilityIdList)
        : [];

    const offers: IOfferFormModelWithNames[] = getOffersWithReadableNames(
      offerList,
      this.props.promotions,
      this.props.categories
    );

    this.setState({ loadedNoOffers: offers.length === 0 });

    this.OfferComponents = offers.map(
      (offer: IOfferFormModel, index: number) => {
        return React.forwardRef((props, ref) => (
          <OfferBlock
            {...props}
            key={index}
            offer={offer}
            ref={ref}
            onMenuAction={this.handleMenuAction}
            shouldHideButtons={this.state.isLoading}
            user={this.props.user}
            partner={this.state.partners.find((partnerObj: IPartner) => {
              return partnerObj.id === offer.partnerId;
            })}
            editOnly={true}
          />
        ));
      }
    );

    return offers;
  }

  private processExportToPDF = async (
    node: any,
    nodeIndex: number,
    totalElementsProcessed: number,
    pdf: jspdf,
    totalNodes: number
  ) => {
    await processExportToPDF(node, nodeIndex, pdf, totalNodes);
    this.context.displayAlert({
      message: `Exported ${totalElementsProcessed + 1} / ${totalNodes} offers`,
      variant: "success"
    });

    return Promise.resolve();
  };

  private handleMenuAction = (action: OfferActionType, data: any) => {
    switch (action) {
      case OfferActionType.Edit:
        this.props.history.push("/offer-submission/single?id=" + data.id);
        break;
    }
  };
}

const mapStateToProps = (state: IRootState) => ({
  ...withAuthStateToProps(state),
  promotions: state.offers.promotions || [],
  categories: state.offers.categories || []
});

const mapDispatchToProps = (
  dispatch: ThunkDispatch<IRootState, void, Action>
) =>
  withCommunicationDispatchToProps(dispatch, {
    fetchPromotions: () => dispatch(fetchPromotionsAsync()),
    fetchCategories: () => dispatch(fetchCategoriesAsync())
  });

const styles = createStyles((theme: Theme) => ({
  grid: {
    marginTop: theme.spacing(4),
    minWidth: 250
  },
  backTextStyle: {
    textDecoration: "none",
    "&:hover": {
      textDecoration: "underline",
      cursor: "default"
    }
  }
}));

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(styles)(withAuthorization(TargetedOfferPreview, ["VIEW_OFFER"])));
