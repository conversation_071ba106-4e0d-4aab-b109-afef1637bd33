import { Grid, Paper, Slide, Typography } from "@material-ui/core";
import Component from "@reach/component-component";
import { Form, Formik, FormikActions, FormikProps } from "formik";
import { JSONSchema6 } from "json-schema";
import _, { chain, cloneDeep, debounce, get, isEmpty, isEqual, omit, pick, set, unset } from "lodash";
import Papa from "papaparse";
import { not } from "rambda";
import React from "react";
import { connect } from "react-redux";
import { RouteComponentProps } from "react-router";
import { Action } from "redux";
import { ThunkDispatch } from "redux-thunk";
import { from, timer } from "rxjs";
import { concatMap, filter, take } from "rxjs/operators";
import * as uuid from "uuid";
import { BB8Button, BB8MainPage, BB8Spinner, BB8SystemType } from "../../../BB8";
import { IUploadImageData } from "../../../components/form/widgets/CustomFileWidget";
import { IOfferCategory, IOfferCategoryResponse } from "../../../models/Category";
import { IGenerateContentResponse } from "../../../models/IGenerateContentResponse";
import { IGetOffersResponse } from "../../../models/IGetOffersResponse";
import { OfferTemplateOptions } from "../../../models/OfferTemplateType";
import { IPartner } from "../../../models/Partner";
import { IOfferPromotion } from "../../../models/Promotion";
import { REGIONS } from "../../../models/Region";
import { ISponsor } from "../../../models/Sponsor";
import { getDiffOfObjects, queryStringToObject, to } from "../../../shared/helpers";
import imageService, {
  IGenerateS3UrlResponseBody,
  IProcessImageBatchResult,
  IProcessImageRequestBody,
  IProcessImagesResponseBody
} from "../../../shared/services/image.service";
import {
  IRootState,
  IWithCommunicationProps,
  withAuthStateToProps,
  withCommunicationDispatchToProps
} from "../../../store";
import mechanismSchema from "../../../validation/schemas/MechanismObject.json";
import offerFormSchema from "../../../validation/schemas/PostOfferFormObject.json";
import tierSchema from "../../../validation/schemas/TierObject.json";
import {
  Availability,
  AwardType,
  DurationUnit,
  ILocalizedObject,
  IMechanismObject,
  IOfferFormModel,
  ITierObject,
  IValidationErrors,
  MechanismType,
  OfferLimitation,
  OfferType,
  ProgramType,
  Qualifier,
  ValidationSchemas,
  Validator
} from "../../../validation/validator";
import { UserService } from "../../administration/services";
import withAuthorization from "../../auth/components/Authorization";
import { Authorizer } from "../../auth/components/Authorizer";
import { IAuthState } from "../../auth/types";
import { SnackbarContext } from "../../communication/AlertContainer";
import { fetchCategoriesAsync, fetchPromotionsAsync } from "../actions";
import { FEATURE_FLAG, FeatureFlag, featureFlagCompare } from "../components/FeatureFlag";
import OfferSubmissionOfferCategorySection from "../components/OfferSubmissionOfferCategorySection";
import OfferSubmissionOfferContentSection from "../components/OfferSubmissionOfferContentSection";
import OfferSubmissionOfferSettingsSection from "../components/OfferSubmissionOfferSettingsSection";
import OfferSubmissionOfferTypeSection from "../components/OfferSubmissionOfferTypeSection";
import OfferSubmissionPartnerSection from "../components/OfferSubmissionPartnerSection";
import OfferSubmissionTermsSection from "../components/OfferSubmissionTermsSection";
import offerService from "../services/OffersService";
import StyledErrorSummary, { getErrorsBySection } from "./ErrorSummary";
import "./OfferSubmission.scss";

export const DEFAULT_TIER_CONTENT = [
  {
    content: [
      {
        "en-US": undefined
      }
    ]
  }
];

// Store the minimum number of tiers before the custom short is required
export const MIN_CUSTOM_SHORT_TIER = 1;

export const OFFER_READ_ONLY_FIELDS: string[] = [
  "id",
  "bulkId",
  "bulkName",
  "duplicatedFrom",
  "contentfulId",
  "detailsId",
  "createdAt",
  "createdBy",
  "updatedAt",
  "updatedBy",
  "publishedAt",
  "status"
];

export const OFFER_COLUMNS_ORDER_IN_CSV: string[] = [
  "id",
  "active",
  "massOffer",
  "issuanceCode",
  "displayDate",
  "startDate",
  "endDate",
  "programType",
  "offerType",
  "qualifier",
  "awardType",
  "availability",
  "partnerUrl.en-US",
  "partnerUrl.fr-CA",
  "regions",
  "mechanisms[0].mechanismType",
  "mechanisms[0].mechanismLabel.en-US",
  "mechanisms[0].mechanismLabel.fr-CA",
  "mechanisms[0].mechanismText.en-US",
  "mechanisms[0].mechanismText.fr-CA",
  "mechanisms[0].mechanismValue.en-US",
  "mechanisms[0].mechanismValue.fr-CA",
  "mechanisms[1].mechanismType",
  "mechanisms[1].mechanismLabel.en-US",
  "mechanisms[1].mechanismLabel.fr-CA",
  "mechanisms[1].mechanismText.en-US",
  "mechanisms[1].mechanismText.fr-CA",
  "mechanisms[1].mechanismValue.en-US",
  "mechanisms[1].mechanismValue.fr-CA",
  "mechanisms[2].mechanismType",
  "mechanisms[2].mechanismLabel.en-US",
  "mechanisms[2].mechanismLabel.fr-CA",
  "mechanisms[2].mechanismText.en-US",
  "mechanisms[2].mechanismText.fr-CA",
  "mechanisms[2].mechanismValue.en-US",
  "mechanisms[2].mechanismValue.fr-CA",
  "tiers[0].content[0].en-US",
  "tiers[0].content[0].fr-CA",
  "tiers[0].content[1].en-US",
  "tiers[0].content[1].fr-CA",
  "tiers[0].content[2].en-US",
  "tiers[0].content[2].fr-CA",
  "tiers[0].awardValue",
  "tiers[0].qualifierValue",
  "tiers[0].qualifierFrequency",
  "tiers[1].content[0].en-US",
  "tiers[1].content[0].fr-CA",
  "tiers[1].content[1].en-US",
  "tiers[1].content[1].fr-CA",
  "tiers[1].content[2].en-US",
  "tiers[1].content[2].fr-CA",
  "tiers[1].awardValue",
  "tiers[1].qualifierValue",
  "tiers[1].qualifierFrequency",
  "tiers[2].content[0].en-US",
  "tiers[2].content[0].fr-CA",
  "tiers[2].content[1].en-US",
  "tiers[2].content[1].fr-CA",
  "tiers[2].content[2].en-US",
  "tiers[2].content[2].fr-CA",
  "tiers[2].awardValue",
  "tiers[2].qualifierValue",
  "tiers[2].qualifierFrequency",
  "partnerBaseEarnRate",
  "baseCashRedemption",
  "image.en-US.path",
  "image.fr-CA.path",
  "awardShort.en-US",
  "awardShort.fr-CA",
  "tiers[0].awardLong.en-US",
  "tiers[0].awardLong.fr-CA",
  "tiers[1].awardLong.en-US",
  "tiers[1].awardLong.fr-CA",
  "tiers[2].awardLong.en-US",
  "tiers[2].awardLong.fr-CA",
  "qualifierShort.en-US",
  "qualifierShort.fr-CA",
  "tiers[0].qualifierLong.en-US",
  "tiers[0].qualifierLong.fr-CA",
  "tiers[1].qualifierLong.en-US",
  "tiers[1].qualifierLong.fr-CA",
  "tiers[2].qualifierLong.en-US",
  "tiers[2].qualifierLong.fr-CA",
  "description.en-US",
  "description.fr-CA",
  "cashierInstruction.en-US",
  "cashierInstruction.fr-CA",
  "displayPriority",
  "tags",
  "offerCategory1",
  "offerCategory2",
  "offerCategory3",
  "productBrand",
  "productName",
  "canBeCombined",
  "combinationsText.en-US",
  "combinationsText.fr-CA",
  "daysToApply",
  "exclusions.en-US",
  "exclusions.fr-CA",
  "includedBanners.en-US",
  "includedBanners.fr-CA",
  "includedLocations.en-US",
  "includedLocations.fr-CA",
  "excludedBanners.en-US",
  "excludedBanners.fr-CA",
  "excludedLocations.en-US",
  "excludedLocations.fr-CA",
  "offerLimitation",
  "offerLimitationText.en-US",
  "offerLimitationText.fr-CA",
  "hasCustomLegal",
  "legalText.en-US",
  "legalText.fr-CA",
  "partnerLegalName.en-US",
  "partnerLegalName.fr-CA",
  "trademarkInfo.en-US",
  "trademarkInfo.fr-CA",
  "publishedBy",
  "programPriority"
];

export const ADDITIONAL_OFFERS_KEYS_TO_PIPE_VALUES: string[] = [
  "tiers",
  "mechanisms"
];

function assignValue(key: any, value: any, fields: any, data: any): any {
  if (value instanceof Object) {
    if (Array.isArray(value)) {
      for (let i = 0; i < value.length; i++) {
        const v = value[i];
        if (v instanceof Object) {
          assignValue(`${key}[${i}]`, v, fields, data);
        } else {
          fields.push(key);
          data.push(value.join("|"));
        }
      }

      return;
    }
    const pairs = _.toPairs(value);
    for (let j = 0; j < pairs.length; j++) {
      const [innerKey, innerValue] = pairs[j];
      assignValue(`${key}.${innerKey}`, innerValue, fields, data);
    }
  } else {
    fields.push(key);
    data.push(value);
    return;
  }
}

function generateParseObject(sampleObject: any, ADDITIONAL_FIELDS_TO_IGNORE: string[]) {

  const offerKeys: string[] = Object.keys(sampleObject);

  [...ADDITIONAL_FIELDS_TO_IGNORE, ...OFFER_READ_ONLY_FIELDS].forEach(field => {
    if (offerKeys.includes(field) && field !== "id") {
      delete sampleObject[field];
    }
  });

  const pairs = _.toPairs(sampleObject);
  const fields: any = [],
      data: any = [];

  for (const [key, value] of pairs) {
    if (
        ADDITIONAL_OFFERS_KEYS_TO_PIPE_VALUES.includes(key) &&
        Array.isArray(value)
    ) {
      for (let i = 0; i < value.length; i++) {
        assignValue(`${key}[${i}]`, value[i], fields, data);
      }
    } else {
      assignValue(key, value, fields, data);
    }
  }

  let offerObj: any = {};

  fields.forEach((item: string, index: number) => {
    offerObj[item] = data[index];
  });

  if(offerObj.massOffer === false){
    offerObj.massOffer = "FALSE";
  }

  if(get(offerObj,"image.en-US.path") !== undefined){
    set(offerObj,"image.en-US.path","dummy.jpg");
  }
  if(!offerObj["image.fr-CA.path"]|| get(offerObj,"image.fr-CA.path") !== undefined){
    offerObj["image.fr-CA.path"] = "dummy.jpg";
  }

  if (offerObj.qualifier !== "custom") {
    offerObj = omit(offerObj, [
      "qualifierShort.en-US",
      "qualifierShort.fr-CA",
      "tiers[0].qualifierLong.en-US",
      "tiers[0].qualifierLong.fr-CA",
      "tiers[1].qualifierLong.en-US",
      "tiers[1].qualifierLong.fr-CA",
      "tiers[2].qualifierLong.en-US",
      "tiers[2].qualifierLong.fr-CA"
    ]);
  }

  if (offerObj.awardType !== "custom") {
    offerObj = omit(offerObj, [
      "awardShort.en-US",
      "awardShort.fr-CA",
      "tiers[0].awardLong.en-US",
      "tiers[0].awardLong.fr-CA",
      "tiers[1].awardLong.en-US",
      "tiers[1].awardLong.fr-CA",
      "tiers[2].awardLong.en-US",
      "tiers[2].awardLong.fr-CA"
    ]);
  }
  return offerObj;
}

function canCallGenerateContent(offer: Partial<IOfferFormModel>): boolean {
  return (
    !isEmpty(offer) &&
    !isEmpty(offer.partnerId) &&
    !isEmpty(offer.programType) &&
    !isEmpty(offer.offerType) &&
    !isEmpty(offer.awardType) &&
    !isEmpty(offer.qualifier)
  );
}
function replaceRefsWithDefinitions(schema: JSONSchema6): JSONSchema6 {
  const cloned = cloneDeep(schema);

  set(cloned, "properties.mechanisms.items.$ref", "#/definitions/mechanism");
  set(cloned, "properties.tiers.items.$ref", "#/definitions/tiers");

  return cloned;
}

function treatSchema(schema: JSONSchema6): JSONSchema6 {
  return replaceRefsWithDefinitions(schema);
}

interface IMyOffersStates {
  partners?: IPartner[];
  initialFormData: Partial<IOfferFormModel>;
  errors: { [key: string]: IValidationErrors[] };
  isLoading: boolean;
  lastUpdatedImageData: ILocalizedObject<{ path: string | undefined }>;
  generatedContentFields: IOfferGeneratedContent;
  categories?: IOfferCategoryResponse;
  activeSponsorCodes?: ISponsor[];
  activeSponsorCodesForSelectedPartnerId?: ISponsor[];
}
export interface IOfferGeneratedContent {
  awardShort?: ILocalizedObject;
  qualifierShort?: ILocalizedObject;
  tiersGeneratedContent?: IOfferTierGeneratedContent[];
}

export interface IOfferTierGeneratedContent {
  awardLong: ILocalizedObject;
  qualifierLong: ILocalizedObject;
}

const initialState: IMyOffersStates = {
  errors: {},
  initialFormData: {
    programType: ProgramType.TraditionalCore,
    availability: [Availability.InStore],
    combinationsText: {
      "en-US": "offers, and AIR MILES offers",
      "fr-CA": "offres et offres AIR MILES"
    },
    canBeCombined: true,
    displayPriority: 0,
    mechanisms: [
      {
        mechanismType: MechanismType.NoAction
      }
    ]
  },
  partners: [],
  isLoading: true,
  lastUpdatedImageData: { "en-US": { path: undefined } },
  generatedContentFields: {},
  activeSponsorCodes: [],
  activeSponsorCodesForSelectedPartnerId: []
};

interface IOfferSubmissionProps
  extends IAuthState,
    IWithCommunicationProps,
    RouteComponentProps {
  search: string;
  categories: IOfferCategory[];
  fetchCategories: () => void;
  promotions: IOfferPromotion[];
  fetchPromotions: () => void;
}
interface IChangeListenerProps
  extends Pick<FormikProps<Partial<IOfferFormModel>>, "setFieldValue"> {
  values: Partial<IOfferFormModel>;
}

class OfferSubmission extends React.Component<IOfferSubmissionProps, IMyOffersStates> {
  public static contextType = SnackbarContext;
  public contextType: typeof SnackbarContext;
  public validator = new Validator();
  public formikRef: any;

  constructor(props: IOfferSubmissionProps) {
    super(props);

    this.state = cloneDeep(initialState);

    // this.handleValidate = _.debounce(this.handleValidate, 280);
    this.handleValueChanges = debounce(this.handleValueChanges, 500);

    this.formikRef = React.createRef();
  }

  private getFilteredActiveSponsorCodes = (partnerSponsorCodes: string[]) => {
    const { activeSponsorCodes } = this.state;
    const filteredSponsorCodes = activeSponsorCodes ? activeSponsorCodes.filter(activeSponsorCode =>
      partnerSponsorCodes.find(sponsorCode => sponsorCode === activeSponsorCode.issuerCode) !== undefined
    )
    : [];

    this.formikRef.current.state.values.activeSponsorCodesForSelectedPartnerId = filteredSponsorCodes || [];
    this.formikRef.current.state.values.issuanceCode = undefined;
    this.formikRef.current.state.values.sponsorCode = undefined;

    this.setState({
      activeSponsorCodesForSelectedPartnerId: filteredSponsorCodes
    });

    return filteredSponsorCodes as ISponsor[];
  }

  public componentDidMount = () => {
    const { user, location, fetchCategories, fetchPromotions } = this.props;
    const { initialFormData } = this.state;
    if (isEmpty(initialFormData)) {
      set(initialFormData, `tiers`, DEFAULT_TIER_CONTENT);
    }

    const queryParams = queryStringToObject(location.search);
    window.scrollTo(0, 0);

    if (get(queryParams, "id")) {
      this.retrieveOfferFromProps(queryParams.id);
    } else if (get(queryParams, "duplicatedFrom")) {
      this.retrieveOfferFromProps(queryParams.duplicatedFrom, true);
    }

    fetchCategories();
    fetchPromotions();

    const userServicePromise = user
      ? UserService.getUserPartners(user.email, true).catch(error => {
          console.error(error);
          this.context.displayAlert({
            message: "Error while trying to fetch partners",
            variant: "error"
          });
        })
      : Promise.resolve(undefined);

    Promise.all([userServicePromise])
      .then(values => {
        const [userPartners] = values;
        const partners = userPartners || [];

        this.setState({
          partners
        });

        if (get(queryParams, "partnerId")) {
          const selectedPartner: IPartner | undefined = partners.find(
            (partner: { id: any }) => partner.id === queryParams.partnerId
          );

          // Use the formik ref to directly update the formik field values when the component renders
          this.formikRef.current.setValues({
            ...this.formikRef.current.state.values,
            partnerId: queryParams.partnerId,
            partnerName: selectedPartner ? selectedPartner.name : null,
            duplicatedFrom: queryParams.partnerId,
            regions:
              selectedPartner && selectedPartner.regions.indexOf("all") >= 0
                ? REGIONS.map(({ value }) => value)
                : selectedPartner
                ? selectedPartner.regions.map(region => region.toUpperCase())
                : []
          });

          this.setState({ isLoading: false }, () => {
            let url = document.location.href,
              urlparts = url.split("?"),
              urlBase = urlparts.shift(),
              queryString = urlparts.join("?"),
              prefix = encodeURIComponent("partnerId") + "=",
              pars = queryString.split(/[&;]/g);

            for (let i = pars.length; i-- > 0; ) {
              if (pars[i].lastIndexOf(prefix, 0) !== -1) {
                pars.splice(i, 1);
              }
            }
            url = urlBase + "?" + pars.join("&");
            history.pushState(null, "", url);
          });
        }
        if (canCallGenerateContent(initialFormData)) {
          this.getOfferContent(initialFormData).then(
            (content: IGenerateContentResponse) => {
              const tiers = get(initialFormData, "tiers");
              if (tiers) {
                const newTiers = tiers.map((tier, index) => {
                  set(tier, `awardLong`, get(content, `awardLong.[${index}]`));
                  set(
                    tier,
                    `qualifierLong`,
                    get(content, `qualifierLong.[${index}]`)
                  );
                  return tier;
                });
                const stateSetter = (currentState: IMyOffersStates) => ({
                  initialFormData: {
                    ...currentState.initialFormData,
                    tiers: newTiers
                  }
                });
                this.setState(stateSetter);
              }
            }
          );
        }

        // Set "flagSponsorCode" for the "PostOfferFormObject" validation rules:
        this.formikRef.current.state.values.flagSponsorCode = process.env.REACT_APP_OFFER_ISSUER_DROPDOWN === 'true';

        if (process.env.REACT_APP_OFFER_ISSUER_DROPDOWN === 'true') {
          const reponseActiveSponsorCodes = UserService.getActiveSponsorCodes().catch(error => {
            console.error(error);
            this.context.displayAlert({
              message: "Error while trying to fetch active sponsor codes",
              variant: "error"
            });
          })

          Promise.all([reponseActiveSponsorCodes])
              .then(values => {
                const [activeSponsorCodes] = values;

                if (activeSponsorCodes && activeSponsorCodes.length > 0){
                  this.setState({ activeSponsorCodes }, () => {
                    const currentPartnerId = this.formikRef.current.state.values.partnerId || '';

                    const selectedPartner: IPartner | undefined = this.state.partners ?
                        this.state.partners.find(
                            (partner: { id: any }) => partner.id === currentPartnerId
                        )
                        : undefined;

                    if (selectedPartner) {
                      const sponsorCodesForSelectedPartnerId = get(selectedPartner, "sponsorCodes", []);
                      this.getFilteredActiveSponsorCodes(sponsorCodesForSelectedPartnerId);

                      const partnerType = get(selectedPartner, "type", []) || [];
                      this.formikRef.current.state.values.partnerInternalAirmilesCalculated = partnerType.includes("internal-airmiles-calculated");
                    }
                  });
                }
              })
        }

      })
      .finally(() => {
        this.setState({
          isLoading: false
        });
      });


  };

  public render() {
    const schema = {
      ...offerFormSchema,
      definitions: {
        mechanism: mechanismSchema,
        tiers: tierSchema
      }
    };
    const {
      errors,
      initialFormData,
      isLoading,
      generatedContentFields
    } = this.state;

    const errorsBySection = getErrorsBySection(errors);
    const errorsToDisplay = [
      ...(errorsBySection.partnerSectionFields || []),
      ...(errorsBySection.offerTypeSectionFields || [])
    ];
    let showOfferSettings = false;
    let showRestOfOffer = false;
    if (!errorsToDisplay.length) {
      showOfferSettings = true;
      errorsToDisplay.push(
        ...(errorsBySection.offerSettingsSectionFields || [])
      );

      if (!errorsToDisplay.length) {
        showRestOfOffer = true;
        errorsToDisplay.push(
          ...(errorsBySection.offerContentSectionFields || []),
          ...(errorsBySection.offerCategorySectionFields || []),
          ...(errorsBySection.offerTermsSectionFields || [])
        );
      }
    }

    return (
      <div id="offer-submission">
        <BB8MainPage type={BB8SystemType.Billing}>
          <BB8MainPage.Padded>
            <Formik
              ref={this.formikRef}
              initialValues={initialFormData as IOfferFormModel}
              validateOnChange={true}
              validateOnBlur={false}
              validate={(values: Partial<IOfferFormModel>) => this.handleValidate(values, initialFormData as IOfferFormModel)}
              onSubmit={this.onSubmit}
            >
              {(formikProps: FormikProps<IOfferFormModel>) => {
                const partnerSelected = Boolean(formikProps.values.partnerId);
                const offerTypeSelected =
                  partnerSelected &&
                  Boolean(
                    formikProps.values.offerType &&
                      formikProps.values.qualifier &&
                      formikProps.values.awardType
                  );
                return (
                  <Form noValidate={true} onKeyPress={this.onKeyPress}>
                    <Component
                      values={formikProps.values}
                      setFieldValue={formikProps.setFieldValue}
                      didUpdate={this.formUpdateHandler}
                    />
                    <OfferSubmissionPartnerSection
                      schema={{ ...offerFormSchema }}
                      partners={this.state.partners}
                      formikProps={formikProps}
                      setSponsorCodesOnPartnerChange={this.getFilteredActiveSponsorCodes}
                    />
                    <Slide direction="right" in={partnerSelected}>
                      <div>
                        <OfferSubmissionOfferTypeSection
                            schema={{ ...offerFormSchema }}
                            formikProps={formikProps}
                            partners={this.state.partners}
                        />
                      </div>
                    </Slide>

                    {offerTypeSelected && showOfferSettings && (
                      <>
                        <Slide direction="right" in={showOfferSettings}>
                          <div>
                            <OfferSubmissionOfferSettingsSection
                                schema={{ ...offerFormSchema }}
                                formikProps={formikProps}
                                partners={this.state.partners}
                            />
                          </div>
                        </Slide>

                        {showRestOfOffer && (
                          <>
                            <Slide direction="right" in={showRestOfOffer}>
                              <div>
                                <OfferSubmissionOfferContentSection
                                    schema={treatSchema(schema as any)}
                                    formikProps={formikProps}
                                    generatedContent={generatedContentFields}
                                    key={
                                        formikProps.values.offerType +
                                        (formikProps.values.qualifier || "qualifier") +
                                        (formikProps.values.awardType || "awardType")
                                    }
                                />
                              </div>
                            </Slide>
                            <FeatureFlag featureName={FEATURE_FLAG.CATEGORY}>
                              <Slide direction="right" in={partnerSelected}>
                                <div>
                                  <OfferSubmissionOfferCategorySection
                                      formikProps={formikProps}
                                  />
                                </div>
                              </Slide>
                            </FeatureFlag>
                            <Slide direction="right" in={showRestOfOffer}>
                              <div>
                                <OfferSubmissionTermsSection
                                    schema={{ ...offerFormSchema }}
                                    formikProps={formikProps}
                                />
                              </div>
                            </Slide>
                            <Slide direction="right" in={showRestOfOffer}>
                              <div>
                                <Paper
                                    elevation={2}
                                    style={{
                                      padding: "2rem",
                                      width: "50%",
                                      display: "flex",
                                      justifyContent: "center",
                                      margin: "0 auto"
                                    }}
                                >
                                  <Grid
                                      item={true}
                                      container={true}
                                      justify="space-between"
                                  >
                                    <BB8Button
                                        id="submit-single-offer"
                                        color="primary"
                                        size="large"
                                        variant="contained"
                                        type="submit"
                                        disabled={!isEmpty(errors)}
                                    >
                                      {initialFormData.id
                                          ? "Update Offer"
                                          : "Save Offer"}
                                    </BB8Button>
                                    <BB8Button
                                        id="submit-single-offer"
                                        color="primary"
                                        size="large"
                                        variant="contained"
                                        disabled={!isEmpty(errors)}
                                        onClick={this.handleDownload(formikProps.values,[
                                          "createdBy",
                                          "partnerName",
                                          "partnerId",
                                          "audience",
                                          "flagSponsorCode",
                                          "offerTemplate",
                                          "activeSponsorCodesForSelectedPartnerId"
                                        ],`Batch_file_template.csv`)}
                                    >
                                      Generate Batch File Template
                                    </BB8Button>
                                    <BB8Button
                                        id="preview-single-offer"
                                        color="primary"
                                        size="large"
                                        variant="contained"
                                        disabled={!initialFormData.id}
                                        onClick={() => {
                                          this.props.history.push(
                                              "/offer-submission/my-offers/offers/preview?id=" +
                                              initialFormData.id
                                          );
                                        }}
                                    >
                                      View Offer
                                    </BB8Button>
                                  </Grid>
                                </Paper>
                              </div>
                            </Slide>
                          </>
                        )}
                        <BB8Spinner show={isLoading} size={200} />
                      </>
                    )}
                    {/* </Slide> */}
                  </Form>
                );
              }}
            </Formik>
            {isEmpty(errorsToDisplay) ? null : (
              <>
                <Typography variant="body2" color="error">Error summary</Typography>
                <StyledErrorSummary errors={errorsToDisplay} />
              </>
            )}
          </BB8MainPage.Padded>
          <BB8Spinner show={isLoading} size={200} />
        </BB8MainPage>
      </div>
    );
  }

  private handleDownload = (offer: IOfferFormModel, fieldsToIgnore: string[], fileName: string) => () => {

    const masterHeaders = new Set();
    const parsedObjects: any = [];
    const formikPropsCopy = {...offer};
    const parsedObj = generateParseObject(formikPropsCopy,fieldsToIgnore);
    const currentOfferKeys = Object.keys(parsedObj);

    for (const key of currentOfferKeys) {
      if (parsedObj[key]) {
        masterHeaders.add(key);
      }
    }

    parsedObjects.push(parsedObj);

    //Ensure "sponsorCode" and "issuanceCode" columns are included in csv
    if(!masterHeaders.has("sponsorCode")){
      masterHeaders.add("sponsorCode");
    }
    if(!masterHeaders.has("issuanceCode")){
      masterHeaders.add("issuanceCode");
    }

    // Normalize every object returned from the API so we can convert into a CSV by
    // adding `null`s to the field names that exist in some offers  but not in the current offer
    parsedObjects.forEach((obj: any, index: any) => {
      masterHeaders.forEach((header: any) => {
        if (!Object.keys(obj).includes(header)) {
          obj[header] = null;
        }
      });
    });

    const offerKeys: string[] = Array.from(masterHeaders);
    const offerkeysInSortedColumnsList: string[] = OFFER_COLUMNS_ORDER_IN_CSV.filter(
        item => {
          if (offerKeys.includes(item)) {
            return item;
          }
        }
    );
    const remainingOfferKeys: string[] = offerKeys.filter(item => {
      if (
          !offerkeysInSortedColumnsList.includes(item) &&
          !OFFER_COLUMNS_ORDER_IN_CSV.includes(item)
      ) {
        return item;
      }
    });

    const unparseJSON: string = Papa.unparse({
      fields: [...offerkeysInSortedColumnsList, ...remainingOfferKeys],
      data: parsedObjects
    });

    const element = document.createElement("a");
    // Added a UTF-8   byte-order-mark "\ufeff" at the start to indicate the encoding is UTF-8.
    //  For more information on byte-order-mark https://en.wikipedia.org/wiki/Byte_order_mark
    const file = new Blob(["\ufeff", unparseJSON], { type: "text/csv" });
    element.href = URL.createObjectURL(file);
    element.download = fileName;
    element.click();
  };

  // TODO: should this be debounced? can it be debounced
  private formUpdateHandler = ({
    prevProps,
    props
  }: {
    prevProps: IChangeListenerProps;
    props: IChangeListenerProps;
  }) => {
    const { setFieldValue } = props;
    if (!isEqual(prevProps.values, props.values)) {
      const diffObj = getDiffOfObjects(prevProps.values, props.values);

      if (!(Object.keys(diffObj).length === 1 && "image" in diffObj)) {
        // if (!("image" in diffObj)) {
        this.handleValueChanges(
          prevProps.values,
          props.values,
          props.setFieldValue
        );
      }
      // }
      if (
        "offerType" in diffObj &&
        not(isEqual(prevProps.values.tiers, DEFAULT_TIER_CONTENT))
      ) {
        // Remove all tiers except for the 0th tier
        setFieldValue(`tiers`, DEFAULT_TIER_CONTENT);
      }
    }
  };

  private async retrieveOfferFromProps(
    offerId: string,
    isDuplicatedFromOffer: boolean = false
  ) {
    const hasPermission: boolean = Authorizer.Instance.checkPermissions(
      ["EDIT_OFFER"],
      this.props.user
    );
    if (!hasPermission) {
      this.props.history.push("/unauthorized");
    }

    const response: IGetOffersResponse = await offerService.getPreviewOffers(
      "?id=" + offerId
    );
    if (response.content.length) {
      const offer: IOfferFormModel = response.content[0];
      if (isDuplicatedFromOffer) {
        offer.duplicatedFrom = offerId;
        // Properties that a POST offer shouldn't have, we have to manually retrieve
        delete offer.id;
        offer.contentfulId = undefined;
        offer.detailsId = undefined;
        offer.createdAt = undefined;
        offer.createdBy = undefined;
        offer.updatedAt = undefined;
        offer.updatedBy = undefined;
        offer.publishedAt = undefined;
        offer.publishedBy = undefined;
        offer.status = undefined;
        offer.sponsorCode = undefined;
        offer.issuanceCode = undefined;
        offer.massOffer = undefined;
        // When duplicating offers, remove french image if its present
        if (offer.image && offer.image["fr-CA"]) {
          offer.image["fr-CA"] = undefined;
        }
      }
      const offerTemplate = `${offer.offerType}-${offer.qualifier}`;
      offer.offerTemplate = offer.offerType && offer.qualifier && OfferTemplateOptions.includes(offerTemplate)
          ? offerTemplate : undefined;
      // Don't grab the images data from the response because we dont want to have to retransform the data again
      this.setState({
        initialFormData: Object.assign(this.state.initialFormData, offer),
        lastUpdatedImageData: offer.image
          ? offer.image
          : { "en-US": { path: undefined } },
        generatedContentFields: {
          tiersGeneratedContent: offer.tiers
            ? offer.tiers.map((tier: ITierObject) => ({
                qualifierLong: tier.qualifierLong || { "en-US": "" },
                awardLong: tier.awardLong || { "en-US": "" }
              }))
            : []
        }
      });
    } else {
      this.props.history.push("not-found");
    }
  }

  /**
   * Handles generating the S3 Signed URLs for uploading, as well as uploading images to S3
   * We want to only upload the new file if there's actually a difference from the original state
   */
  private async handleUploadImageToS3(
    imageData: ILocalizedObject<{ path: string | undefined }> | null
  ): Promise<any> {
    if (imageData && imageData["en-US"] && imageData["en-US"].path) {
      const { lastUpdatedImageData } = this.state;

      const originalEnglishImagePath: string = get(
        lastUpdatedImageData,
        "en-US.path",
        null
      );
      const originalFrenchImagePath: string = get(
        lastUpdatedImageData,
        "fr-CA.path",
        null
      );

      const updatedEnglishImagePath: IUploadImageData = JSON.parse(
        imageData["en-US"].path
      );
      const updatedFrenchImagePath: IUploadImageData =
        imageData["fr-CA"] && imageData["fr-CA"].path
          ? JSON.parse(imageData["fr-CA"].path)
          : null;

      // We check the originalState against the form image paths to determine if which files we want to actually upload if any
      // Upload english and french images separately if any of them had any changes
      if (
        updatedEnglishImagePath &&
        originalEnglishImagePath !== updatedEnglishImagePath.name
      ) {
        try {
          set(
            imageData,
            "en-US.path",
            await this.uploadAndProcessImage(updatedEnglishImagePath)
          );
        } catch (error) {
          return Promise.reject(error);
        }
      } else if (updatedEnglishImagePath) {
        set(imageData, "en-US.path", updatedEnglishImagePath.name);
      }
      if (
        updatedFrenchImagePath &&
        originalFrenchImagePath !== updatedFrenchImagePath.name
      ) {
        try {
          set(
            imageData,
            "fr-CA.path",
            await this.uploadAndProcessImage(updatedFrenchImagePath)
          );
        } catch (error) {
          return Promise.reject(error);
        }
      } else if (updatedFrenchImagePath) {
        set(imageData, "fr-CA.path", updatedFrenchImagePath.name);
      }
      // TODO: have to figure out how to optimize this so that we're not uploading a new image to s3 every time, even with no image changes
      this.setState({
        lastUpdatedImageData: imageData
      });
      return Promise.resolve();
    } else {
      return Promise.reject("Failed to handle upload Image to S3");
    }
  }

  private async uploadAndProcessImage(image: IUploadImageData) {
    const [errGenerateS3, responseGenerateS3]:
      | IGenerateS3UrlResponseBody
      | any = await to(
      imageService.generateS3Url({
        fileLocationPathForUpload: uuid.v4(),
        mimeType: image.mimeType
      })
    );
    if (errGenerateS3) {
      console.error(errGenerateS3);
      return Promise.reject(errGenerateS3);
    }

    const blob = await (await fetch(image.blobData)).blob();

    const processImagesRequest: IProcessImageRequestBody = {
      external_images: [
        {
          location: this.getFileLocationFromPresignedURL(
            responseGenerateS3.signedURLForUpload
          )
        }
      ]
    };
    const [errUpload]: any = await to(
      imageService.uploadToS3(
        responseGenerateS3.signedURLForUpload,
        blob,
        image.mimeType
      )
    );
    if (errUpload) {
      console.error(errUpload);
      return Promise.reject(errUpload);
    }

    const [errProcess, jobBatchUUID]:
      | IProcessImagesResponseBody
      | any = await to(imageService.processImages(processImagesRequest));
    if (errProcess) {
      console.error(errProcess);
      return Promise.reject(errProcess);
    }

    let s3Url;
    let processSuccess: boolean = false;
    await timer(0, 1000)
      .pipe(
        concatMap(() =>
          from(
            imageService.getProcessInfo(jobBatchUUID.id).catch(error => error)
          )
        ),
        filter(val => val.code !== "PROCESSING_JOB_NOT_DONE")
      )
      .pipe(take(1))
      .toPromise()
      .then((val: IProcessImageBatchResult) => {
        const response: {
          raw_s3_location?: string;
          processed_s3_location: string;
          success: boolean;
          error_code?: string;
          message?: string;
        } =
          val[
            responseGenerateS3.signedURLForUpload.split("?")[0] // The key name will be the signedURL without the query params
          ];
        processSuccess = response.success;
        s3Url = response.processed_s3_location;
      });

    if (!processSuccess) {
      return Promise.reject("Failed to upload and process image");
    }
    return s3Url;
  }

  private onSubmit = (
    values: Partial<IOfferFormModel>,
    actions: FormikActions<Partial<IOfferFormModel>>
  ) => {
    this.setState({
      isLoading: true
    });

    if (values.eventBasedOffer === true) {
      values.displayDate = values.firstQualificationDate;
      values.startDate = values.firstQualificationDate;

      let endDateCalculated;
      if (values.lastQualificationDate && values.eligibilityDuration) {
        endDateCalculated = new Date(values.lastQualificationDate);
        endDateCalculated.setDate(endDateCalculated.getDate() + values.eligibilityDuration);
        values.endDate = endDateCalculated.toISOString();
      }
      values.eligibilityDurationUnit = DurationUnit.Days;
    } else {
      delete values.firstQualificationDate;
      delete values.lastQualificationDate;
      delete values.eligibilityDuration;
      delete values.eligibilityDurationUnit;
    }
    if(values.programType !== ProgramType.AMReceipts){
      delete values.retailerGroupId;
      delete values.usageLimit;
    }
    const imageData: ILocalizedObject<{ path: string | undefined }> | null =
      values.image || null;

    this.handleUploadImageToS3(imageData)
      .then(async () => {
        // Make sure we set the qualifier and award Longs from the generated Content
        // We have to manually set this here because we're not setting the qualifier/award long values directly when we get generated content to avoid form update loops
        const { generatedContentFields } = this.state;
        if (values.tiers) {
          values.tiers = values.tiers.map(
            (tier: ITierObject, index: number) => {
              tier.qualifierLong = get(
                generatedContentFields,
                `tiersGeneratedContent[${index}].qualifierLong`
              );
              tier.awardLong = get(
                generatedContentFields,
                `tiersGeneratedContent[${index}].awardLong`
              );
              return tier;
            }
          );
        }

        const reqToMake = (this.state.initialFormData.id
          ? offerService.putOffer
          : offerService.postOffers
        ).bind(offerService);

        const [err, postedOffer]: IOfferFormModel | any = await to(
          reqToMake({
            ...values,
            ...(this.state.initialFormData.id
              ? pick(this.state.initialFormData, OFFER_READ_ONLY_FIELDS)
              : {})
          } as Partial<IOfferFormModel>)
        );
        if (err) {
          const errorMessage = err.error && err.message ? err.message : "Failed to upload offer";
          console.error("Failed to post offer", err);
          this.context.displayAlert({
            message: errorMessage,
            variant: "error"
          });
        } else {
          this.context.displayAlert({
            message: `Successfully ${
              this.state.initialFormData.id ? "updated existing" : "created new"
            } offer`,
            variant: "success"
          });
          unset(postedOffer, "image");
          this.setState({
            initialFormData: {
              ...postedOffer
            }
          });
        }
      })
      .catch(error => {
        console.error(error);
        this.context.displayAlert({
          message: "Error uploading Logo(s) to S3",
          variant: "error"
        });
      })
      .finally(() => {
        this.setState({
          isLoading: false
        });
        actions.setSubmitting(false);
      });
  };

  private getFileLocationFromPresignedURL(presignedUrl: string): string {
    return presignedUrl.substring(0, presignedUrl.indexOf("?"));
  }

  /**
   * Stop enter from submitting the form everytime.
   * @param event Event triggered when the user presses a key.
   */
  private onKeyPress = (event: React.KeyboardEvent<HTMLFormElement>) => {
    if (
      event.key === "Enter" &&
      (event.target as Element).localName !== "button" &&
      (event.target as Element).localName !== "textarea"
    ) {
      event.preventDefault();
    }
  };

  private handleValidate = (values: Partial<IOfferFormModel>, initialFormData: Partial<IOfferFormModel>) => {
    const { generatedContentFields } = this.state;
    const result = this.validator.validate(
      values,
      ValidationSchemas.PostOfferFormObject
    );

    const images = [
      get(values, "image.en-US.path"),
      get(values, "image.fr-CA.path")
    ];
    if (!_.isEmpty(generatedContentFields)) {
      const qualifierLongErrors = this.validator.getQualifierLongErrors(
        values,
        generatedContentFields
      );

      if (qualifierLongErrors) {
        result.push(...qualifierLongErrors);
      }
    }
    if (images) {
      images.forEach(image => {
        if (image) {
          try {
            const imageObj: IUploadImageData = JSON.parse(image);
            if (imageObj && imageObj.error) {
              result.push(imageObj.error);
            }
          } catch {}
        }
      });
    }

    const mechanisms: IMechanismObject[] | undefined = get(
      values,
      "mechanisms"
    );

    const validator = new Validator();

    if (process.env.REACT_APP_CATEGORY === "true") {
      const categoryResults = this.props.categories;
      const categoryErrors = validator.getCategoryErrors(
        values,
        categoryResults
      );
      if (categoryErrors) {
        result.push(...categoryErrors);
      }
    }

    const promoErrors = validator.getPromotionErrors(
      values,
      this.props.promotions
    );
    if (promoErrors) {
      result.push(...promoErrors);
    }

    let barcodeErrors = mechanisms
      ? validator.getBarcodeErrors(mechanisms)
      : [];

    if (barcodeErrors.length) {
      barcodeErrors = barcodeErrors.filter(
        error => error !== undefined
      ) as IValidationErrors[];

      result.push(...barcodeErrors);
    }

    const featureFlagCardType = featureFlagCompare(FEATURE_FLAG.CARD_TYPE, "true");
    if(values.programType === ProgramType.CardLinkedOffers && featureFlagCardType){
      const cardTypeErrors = validator.getCardTypeEmptyErrors(values.cardType);
      if(cardTypeErrors){
        result.push(...cardTypeErrors);
      }
    }

    const featureFlagUsageLimit = featureFlagCompare(FEATURE_FLAG.USAGE_LIMIT, "true");
    if(values.programType === ProgramType.AMReceipts && featureFlagUsageLimit) {
      if (!values.tagsLabels || !values.tagsLabels[0].includes("Boost offer")) {
        const usageLimitErrors = validator.getUsageLimitErrors(values.usageLimit);
        if(usageLimitErrors) {
          result.push(...usageLimitErrors);
        }

        const usageLimitEmptyErrors = validator.getUsageLimitEmptyErrors(values.usageLimit);
        if(usageLimitEmptyErrors) {
          result.push(...usageLimitEmptyErrors);
        }
      }
    }

    const ctaErrors = validator.getCtaFieldsErrors(values);
    if (ctaErrors) {
      result.push(...ctaErrors);
    }

    if (values.eventBasedOffer) {
      // Editing an existing offer
      if (values.status && values.status.toUpperCase() == "PUBLISHED") {
        if (values.firstQualificationDate != initialFormData.firstQualificationDate) {
          const firstQualificationDateEditErrors = validator.getDateGreaterThanCurrentDateError(values.firstQualificationDate, "firstQualificationDate");
          if(firstQualificationDateEditErrors){
            result.push(...firstQualificationDateEditErrors);
          }
        }

        if (values.lastQualificationDate != initialFormData.lastQualificationDate) {
          const lastQualificationDateEditErrors = validator.getDateGreaterThanCurrentDateError(values.lastQualificationDate, "lastQualificationDate");
          if(lastQualificationDateEditErrors){
            result.push(...lastQualificationDateEditErrors);
          }
        }
      }

      const lastQualificationDateErrors = validator.getLastQualificationDateErrors(values.lastQualificationDate, values.firstQualificationDate);
      if(lastQualificationDateErrors) {
        result.push(...lastQualificationDateErrors);
      }

      const eligibilityDurationErrors = validator.getEligibilityErrors(values.eligibilityDuration);
      if(eligibilityDurationErrors) {
        result.push(...eligibilityDurationErrors);
      }

      const eligibilityDurationEmptyErrors = validator.getEligibilityEmptyErrors(values.eligibilityDuration);
      if(eligibilityDurationEmptyErrors) {
        result.push(...eligibilityDurationEmptyErrors);
      }
    } else {
      if (values.status && values.status.toUpperCase() == "PUBLISHED") {
        if (values.startDate != initialFormData.startDate) {
          const startDateEditError = validator.getDateGreaterThanCurrentDateError(values.startDate, "startDate");
          if(startDateEditError){
            result.push(...startDateEditError);
          }
        }

        if (values.displayDate != initialFormData.displayDate) {
          const displayDateEditError = validator.getDateGreaterThanCurrentDateError(values.displayDate, "displayDate");
          if(displayDateEditError){
            result.push(...displayDateEditError);
          }
        }
      }

      const dateErrors = validator.getDateErrors(values.startDate, values.endDate, values.displayDate);
      if(dateErrors){
        result.push(...dateErrors);
      }
    }

    const dynamicRequiredFields = ["offerCategory1", "offerCategory2", "displayDate", "startDate", "endDate", "sponsorCode", "issuanceCode", "campaignCode", "audience"];
    if (values.eventBasedOffer) {
      dynamicRequiredFields.push("eligibilityDuration");
    }

    if(values.programType === ProgramType.CardLinkedOffers && featureFlagCardType){
      dynamicRequiredFields.push("cardType");
    }

    if(values.programType === ProgramType.AMReceipts){
      dynamicRequiredFields.push("usageLimit");
    }

    if(featureFlagCompare(FEATURE_FLAG.OFFER_ISSUER_DROPDOWN, "true")){
      const issuanceSponsorErrors = validator.getIssuanceSponsorErrors(values,false)
      if (issuanceSponsorErrors) {
        result.push(...issuanceSponsorErrors);
      }
    }

    const keysToValidate = Object.keys(values).concat(
      ...offerFormSchema.required,
      ...dynamicRequiredFields
    );

    const errors = chain(result)
      .map(error => {
        if (error && error.keyword === "required") {
          error.dataPath = [
            error.dataPath,
            get(error, "params.missingProperty")
          ].join(".");
        }
        return error;
      })
      .filter(value => {
        return keysToValidate.some(key =>
          Boolean(value && value.dataPath && value.dataPath.indexOf(key) >= 0)
        );
      })
      .uniqWith((errorA, errorB) => {
        return (
          isEqual(errorA.dataPath, errorB.dataPath) &&
          isEqual(errorA.keyword, errorB.keyword)
        );
      })
      .groupBy(value => {
        let path = value.dataPath;
        if (path) {
          if (path.startsWith(".")) {
            path = path.slice(1, path.length);
          }
          return path.replace("['", ".").replace("']", "");
        }
      })
      .value();

    if (!isEmpty(errors)) {
      this.setState({ errors });
      return errors;
    } else {
      this.setState({
        errors: {}
      });
    }
    return {};
  };

  private getOfferContent = (values: Partial<IOfferFormModel>) => {
    return offerService.getOfferContent(values);
  };

  private handleValueChanges = (
    prevValues: Partial<IOfferFormModel>,
    values: Partial<IOfferFormModel>,
    setFieldValue?: any
  ) => {
    this.handleCustomTiers(values, setFieldValue);
    if (
      prevValues.offerLimitation !== values.offerLimitation &&
      values.offerLimitation === OfferLimitation.Custom
    ) {
      setFieldValue("offerLimitationText", {
        "en-US": undefined,
        "fr-CA": undefined
      });
    } else if (prevValues.offerLimitation !== values.offerLimitation) {
      setFieldValue("offerLimitationText", undefined);
    }

    // for amCashEarn: Tiers[].qualifierValue = baseCashRedemption
    // for amCashDiscount: tiers[].qualifierValue = baseCashRedemption - tiers[].awardvalue

    // For every new tier, for the above scenario, set the qualifier value
    // if awardValue changes, change the qualifier value
    // if base cash redemption changes, change the qualifierValue

    if (
      (prevValues.baseCashRedemption !== values.baseCashRedemption ||
        prevValues.tiers !== values.tiers) &&
      values.tiers &&
      values.tiers.length
    ) {
      if (values.offerType === OfferType.AmCashEarn) {
        values.tiers.forEach((tier, index) => {
          // If the base cash redemption is undefined then default the value to 95 as defined in the schema
          const newCashRedemption = values.baseCashRedemption
            ? values.baseCashRedemption * (index + 1)
            : 95; // TODO: get default value from schema
          setFieldValue(`tiers[${index}].qualifierValue`, newCashRedemption);
        });
      } else if (values.offerType === OfferType.AmCashDiscount) {
        values.tiers.forEach((tier, index) => {
          // Make sure they've set the awardValue before setting anything
          const newQualifierValue = values.baseCashRedemption
            ? values.baseCashRedemption -
              get(values, `tiers[${index}].awardValue`)
            : undefined;
          if (newQualifierValue) {
            setFieldValue(`tiers[${index}].qualifierValue`, newQualifierValue);
          }
        });
      }
    }

    this.getOfferContent(values).then((content: IGenerateContentResponse) => {
      // For each tier, we create an object representing the genereated content for that tier, and store it in the component state so we're not constantly modifying the form
      const newGeneratedContentLong: IOfferTierGeneratedContent[] = values.tiers
        ? values.tiers.map((tier, index: number) => {
            return {
              qualifierLong: content.qualifierLong
                ? content.qualifierLong[index]
                : { "en-US": "" },
              awardLong: content.awardLong
                ? content.awardLong[index]
                : { "en-US": "" }
            };
          })
        : [];

      values.tiers
        ? values.tiers.map(tier => ({
            qualifierLong: tier.qualifierLong || { "en-US": "" },
            awardLong: tier.awardLong || { "en-US": "" }
          }))
        : [];

      const newGeneratedContentShorts = {
        awardShort: content.awardShort,
        qualifierShort: content.qualifierShort
      };

      this.setState({
        generatedContentFields: {
          tiersGeneratedContent: newGeneratedContentLong,
          ...newGeneratedContentShorts
        }
      });
      if (setFieldValue && values && !values.hasCustomLegal) {
        setFieldValue("legalText", {
          "en-US": get(content, "legal.en-US"),
          "fr-CA": get(content, "legal.fr-CA")
        });
      }
    });
  };

  private handleCustomTiers = (
    values: Partial<IOfferFormModel>,
    setFieldValue?: any
  ) => {
    // When there is more than one tier and the offer type is custom
    // If the award or qualifier are also custom, set the awardShort/qualifierShort values to default empty.
    // This is for validation purposes as the fields are now required per the schema expectations
    if (values.offerType === OfferType.Custom && values.tiers) {
      if (values.tiers.length > MIN_CUSTOM_SHORT_TIER) {
        if (
          !values.awardShort &&
          values.awardType && // when the item contains custom awardType
          values.awardType === AwardType.Custom
        ) {
          setFieldValue("awardShort", {
            "en-US": undefined,
            "fr-CA": undefined
          });
        }
        if (
          !values.qualifierShort &&
          values.qualifier &&
          values.qualifier === Qualifier.Custom
        ) {
          setFieldValue("qualifierShort", {
            "en-US": undefined,
            "fr-CA": undefined
          });
        }
      }
      // If the custom tier has been removed and the award short is no longer valid set the field value to be empty
      else if (
        values.tiers.length <= MIN_CUSTOM_SHORT_TIER &&
        (values.awardShort || values.qualifierShort)
      ) {
        setFieldValue(`awardShort`, undefined);
        setFieldValue(`qualifierShort`, undefined);
      }
    }
  };
}

const mapStateToProps = (state: IRootState) => ({
  ...withAuthStateToProps(state),
  search: state.router!.location.search,
  categories: state.offers.categories || [],
  promotions: state.offers.promotions || []
});

const mapDispatchToProps = (
  dispatch: ThunkDispatch<IRootState, void, Action>
) => ({
  ...withCommunicationDispatchToProps(dispatch),
  fetchCategories: () => dispatch(fetchCategoriesAsync()),
  fetchPromotions: () => dispatch(fetchPromotionsAsync())
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withAuthorization(OfferSubmission, ["CREATE_OFFER"]));