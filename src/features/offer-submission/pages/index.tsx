import React from "react";
import { connect } from "react-redux";
import { Redirect, RouteComponentProps } from "react-router";
import { NavLink, Route, Switch } from "react-router-dom";
import { Dispatch } from "redux";
import { BB8CustomTopNav, NotFound } from "../../../BB8";
import { IUser } from "../../../models/User";
import {
  IRootState,
  IWithAuthDispatchToProps,
  IWithCommunicationProps,
  withAuthDispatchToProps,
  withAuthStateToProps,
  withCommunicationDispatchToProps,
  withCommunicationStateToProps
} from "../../../store";
import { Authorizer } from "../../auth/components/Authorizer";
import { IAuthState } from "../../auth/types";
import Bulk from "./Bulk";
import MyBulkUploads from "./MyBulkUploads";
import MyOffers from "./MyOffers";
import OfferPreview from "./OfferPreview";
import OfferSubmission from "./OfferSubmission";
import PartnersListPage from "./PartnersListPage";
import TargetedOfferPreview from "./TargetedOfferPreview";

export function IndexPage({
  match,
  isLoggedIn,
  user
}: RouteComponentProps &
  IWithCommunicationProps &
  IAuthState &
  IWithAuthDispatchToProps) {
  return (
    <>
      <BB8CustomTopNav user={{} as IUser} showAuth={false} subHeader={true}>
        {isLoggedIn &&
          Authorizer.Instance.checkPermissions(["VIEW_OFFER"], user) && (
            <NavLink
              id="navlink-offer-submission-mypartners"
              to={`${match.path}/my-partners`}
            >
              My Partners
            </NavLink>
          )}
        {isLoggedIn &&
          Authorizer.Instance.checkPermissions(["VIEW_OFFER"], user) && (
            <NavLink
              id="navlink-offer-submission-preview"
              to={`${match.path}/my-offers`}
              exact={true}
            >
              My Offers
            </NavLink>
          )}

        {isLoggedIn &&
          Authorizer.Instance.checkPermissions(["VIEW_OFFER"], user) && (
            <NavLink
              id="navlink-offer-submission-view-bulk"
              to={`${match.path}/my-bulk-uploads`}
            >
              My Bulk Uploads
            </NavLink>
          )}
        {isLoggedIn &&
          Authorizer.Instance.checkPermissions(["CREATE_OFFER"], user) && (
            <NavLink
              id="navlink-offer-submission-single"
              to={`${match.path}/single`}
            >
              Single Offer
            </NavLink>
          )}
        {isLoggedIn &&
          Authorizer.Instance.checkPermissions(["CREATE_OFFER"], user) && (
            <NavLink
              id="navlink-offer-submission-bulk"
              to={`${match.path}/bulk`}
            >
              Bulk Upload
            </NavLink>
          )}
        {isLoggedIn &&
          Authorizer.Instance.checkPermissions(["VIEW_OFFER"], user) && (
            <NavLink
              id="navlink-offer-preview-targeted"
              to={`${match.path}/targeted-offer-preview`}
            >
              Preview Targeted Offers
            </NavLink>
          )}
      </BB8CustomTopNav>

      <Switch>
        <Route
          key={1}
          path={`${match.path}/my-offers/offers/preview`}
          component={OfferPreview}
        />
        <Route
          path={`${match.path}/targeted-offer-preview`}
          component={TargetedOfferPreview}
        />

        {/*
          Use props.location.key from connected-react-router which is unqiue per navigation, even
          if navigating back to the same page. This allows us to re-mount bulk/offer submission and clear
          their forms when navigating to the page
        */}
        <Route
          path={`${match.path}/bulk`}
          render={props => <Bulk key={props.location.key} {...props} />}
        />
        <Route
          path={`${match.path}/single`}
          render={props => {
            return <OfferSubmission key={props.location.key} {...props} />;
          }}
        />
        <Route key={4} path={`${match.path}/my-offers`} component={MyOffers} />
        <Route
          key={6}
          path={`${match.path}/my-bulk-uploads`}
          component={MyBulkUploads}
        />
        <Route
          key={5}
          path={`${match.path}/my-partners`}
          component={PartnersListPage}
          exact={true}
        />
        <Redirect
          key={3}
          path={`${match.path}`}
          to={`${match.path}/my-partners`}
        />
        <Route key={6} component={NotFound} />
      </Switch>
    </>
  );
}

const mapStateToProps = (state: IRootState) => ({
  ...withAuthStateToProps(state),
  ...withCommunicationStateToProps(state)
});

const mapDispatchToProps = (dispatch: Dispatch) => ({
  ...withAuthDispatchToProps(dispatch),
  ...withCommunicationDispatchToProps(dispatch)
});
export default connect(
  mapStateToProps,
  mapDispatchToProps
)(IndexPage);
