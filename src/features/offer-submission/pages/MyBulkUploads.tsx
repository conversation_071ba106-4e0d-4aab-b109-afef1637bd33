import {
  createStyles,
  FormControl,
  FormLabel,
  Grid,
  MenuItem,
  Theme,
  Typography,
  withStyles,
  WithStyles
} from "@material-ui/core";
import classNames from "classnames";
import { replace } from "connected-react-router";
import _, { chain, omit } from "lodash";
import Papa from "papaparse";
import React, { ReactNode } from "react";
import { connect } from "react-redux";
import { RouteComponentProps } from "react-router";
import { Action } from "redux";
import { ThunkDispatch } from "redux-thunk";
import { BB8Button, BB8CustomTable, BB8MainPage, BB8Spinner, BB8SystemType, IRenderOptionProps } from "../../../BB8";
import { PartnerLookup } from "../../../components/form/fields/PartnerLookup";
import { IBulkJob } from "../../../models/BulkJob";
import { BulkRequestParams, IBulkFilter } from "../../../models/BulkRequestParams";
import { OffersRequestParams } from "../../../models/OffersRequestParams";
import { IPartner } from "../../../models/Partner";
import {
  IRootState,
  IWithCommunicationProps,
  withAuthStateToProps,
  withCommunicationDispatchToProps
} from "../../../store";
import { IOfferFormModel } from "../../../validation/validator";
import UserService from "../../administration/services/UserService";
import withAuthorization from "../../auth/components/Authorization";
import { Authorizer } from "../../auth/components/Authorizer";
import { IAuthState } from "../../auth/types";
import { SnackbarContext } from "../../communication/AlertContainer";
import * as offersActions from "../actions";
import OffersService from "../services/OffersService";
import { DATE_TIME_FORMAT, getLocalTime } from "../utils";
import {
  ADDITIONAL_OFFERS_KEYS_TO_PIPE_VALUES,
  OFFER_COLUMNS_ORDER_IN_CSV,
  OFFER_READ_ONLY_FIELDS
} from "./OfferSubmission";

const styles = createStyles((theme: Theme) => ({
  root: {
    flexGrow: 1
  },
  formControl: {
    width: "100%"
  },
  grid: {
    marginTop: theme.spacing(4),
    minWidth: 250
  },
  btnUpload: {}
}));

const tableHeaders: Array<{
  id: keyof IBulkJob | "action";
  title: ReactNode;
  sort?: boolean;
}> = [
  {
    id: "partnerName",
    title: "Sponsor",
    sort: true
  },
  {
    id: "name",
    title: "Batch Name",
    sort: true
  },
  {
    id: "createdAt",
    title: "Date added",
    sort: true
  },
  {
    id: "totalOffers",
    title: "Offers",
    sort: true
  },
  {
    id: "action",
    title: ""
  }
];

// TODO: Update to 'MyBulkUploads'
interface IMyBulkUploadsProps
  extends IAuthState,
    IWithCommunicationProps,
    RouteComponentProps,
    WithStyles {
  bulkJobs: IBulkJob[];
  fetchBulkJobs: () => void;
  filterChanged: (filters: IBulkFilter) => void;
  search: string;
  offers: offersActions.IOfferByStatus;
  publishOffers: (offers: IOfferFormModel[]) => void;
}

interface IMyBulkUploadsStates {
  partners: IPartner[];
  sorting: {
    direction: string;
    field: string;
  };
  batchOffers: IOfferFormModel[];
  partnerSelectedId: string;
  isLoading: boolean;
}

interface IOffersResponse {
  content: IOfferFormModel[];
  pageNumber: number;
  pageSize: number;
  totalCount: number;
}

const initialState: IMyBulkUploadsStates = {
  partners: [],
  sorting: {
    direction: "asc",
    field: "partnerName"
  },
  batchOffers: [],
  partnerSelectedId: "",
  isLoading: true
};

function assignValue(key: any, value: any, fields: any, data: any): any {
  if (value instanceof Object) {
    if (Array.isArray(value)) {
      for (let i = 0; i < value.length; i++) {
        const v = value[i];
        if (v instanceof Object) {
          assignValue(`${key}[${i}]`, v, fields, data);
        } else {
          fields.push(key);
          data.push(value.join("|"));
        }
      }

      return;
    }
    const pairs = _.toPairs(value);
    for (let j = 0; j < pairs.length; j++) {
      const [innerKey, innerValue] = pairs[j];
      assignValue(`${key}.${innerKey}`, innerValue, fields, data);
    }
  } else {
    fields.push(key);
    data.push(value);
    return;
  }
}

function generateParseObject(sampleObject: any, ADDITIONAL_FIELDS_TO_IGNORE: string[]) {

  const offerKeys: string[] = Object.keys(sampleObject);

  [...ADDITIONAL_FIELDS_TO_IGNORE, ...OFFER_READ_ONLY_FIELDS].forEach(field => {
    if (offerKeys.includes(field) && field !== "id") {
      delete sampleObject[field];
    }
  });

  const pairs = _.toPairs(sampleObject);
  const fields: any = [],
      data: any = [];

  for (const [key, value] of pairs) {
    if (
        ADDITIONAL_OFFERS_KEYS_TO_PIPE_VALUES.includes(key) &&
        Array.isArray(value)
    ) {
      for (let i = 0; i < value.length; i++) {
        assignValue(`${key}[${i}]`, value[i], fields, data);
      }
    } else {
      assignValue(key, value, fields, data);
    }
  }

  let offerObj: any = {};

  fields.forEach((item: string, index: number) => {
    offerObj[item] = data[index];
  });

  if (offerObj.qualifier !== "custom") {
    offerObj = omit(offerObj, [
      "qualifierShort.en-US",
      "qualifierShort.fr-CA",
      "tiers[0].qualifierLong.en-US",
      "tiers[0].qualifierLong.fr-CA",
      "tiers[1].qualifierLong.en-US",
      "tiers[1].qualifierLong.fr-CA",
      "tiers[2].qualifierLong.en-US",
      "tiers[2].qualifierLong.fr-CA"
    ]);
  }

  if (offerObj.awardType !== "custom") {
    offerObj = omit(offerObj, [
      "awardShort.en-US",
      "awardShort.fr-CA",
      "tiers[0].awardLong.en-US",
      "tiers[0].awardLong.fr-CA",
      "tiers[1].awardLong.en-US",
      "tiers[1].awardLong.fr-CA",
      "tiers[2].awardLong.en-US",
      "tiers[2].awardLong.fr-CA"
    ]);
  }

  if (!offerObj.hasCustomLegal) {
    offerObj = omit(offerObj, ["legalText.en-US", "legalText.fr-CA"]);
  }

  return offerObj;
}

class MyBulkUploads extends React.Component<
  IMyBulkUploadsProps,
  IMyBulkUploadsStates
> {
  public static contextType = SnackbarContext;
  public contextType: typeof SnackbarContext;
  public state = initialState;

  public componentDidMount = () => {
    const { user } = this.props;
    if (user) {
      UserService.getUserPartners(user.email, true)
        .then((partners: IPartner[]) => {
          let preselectedPartner;
          if (this.props.search) {
            const params = BulkRequestParams.fromQueryString(this.props.search);
            preselectedPartner = partners.find(
              partner => partner.id === params.partnerId
            );
          }

          this.setState({
            partners,
            partnerSelectedId: preselectedPartner ? preselectedPartner.id : ""
          });
        })
        .catch(() => {
          this.context.displayAlert({
            message: "Error while trying to fetch partners",
            variant: "error"
          });
        })
        .finally(() => this.setState({ isLoading: false }));
    }

    this.props.fetchBulkJobs();
  };

  public sortChanged = (field: string) => {
    const sorting = {
      direction: this.state.sorting.direction === "asc" ? "desc" : "asc",
      field
    };
    this.setState({ sorting });
  };

  public handlePartnerChange = (selectedPartner?: IPartner) => {
    const partnerId: string = selectedPartner ? selectedPartner.id : "";

    this.setState({
      partnerSelectedId: selectedPartner ? selectedPartner.id : ""
    });

    const filters = BulkRequestParams.fromQueryString(this.props.search);

    const updatedFilters = {
      ...filters,
      partnerId
    };

    this.props.filterChanged(updatedFilters);
  };

  public render() {
    const { classes, bulkJobs, offers } = this.props;
    const { sorting, partners, partnerSelectedId, isLoading } = this.state;

    return (
      <BB8MainPage type={BB8SystemType.Billing}>
        <BB8MainPage.Padded>
          <Grid container={true} direction="column" className={classes.root}>
            <Grid item={true} xs={6} className={classes.grid}>
              <Typography variant="h5">My Bulk Uploads</Typography>
            </Grid>

            <Grid container={true} direction="row" className={classes.root}>
              <Grid item={true} md={4}>
                <FormControl className={classes.formControl} margin="dense">
                  <FormLabel component="label">Choose Partner: </FormLabel>
                  {partners && partners.length > 0 && (
                    <PartnerLookup
                      name="partnerId"
                      partners={partners}
                      placeholder="Select Partner"
                      value={partnerSelectedId ? partnerSelectedId : ""}
                      onSelect={this.handlePartnerChange}
                    />
                  )}
                </FormControl>
              </Grid>
              <Grid item={true} md={2}>
                <Grid
                  container={true}
                  className={classes.grid}
                  alignItems={"center"}
                  direction={"row"}
                  justify={"center"}
                >
                  {Authorizer.Instance.checkPermissions(
                    ["CREATE_OFFER"],
                    this.props.user
                  ) && (
                    <BB8Button
                      onClick={() => this.handleGoToBulk()}
                      variant="outlined"
                      color="primary"
                      size="large"
                      className={classes.btnUpload}
                    >
                      Upload batch
                    </BB8Button>
                  )}
                </Grid>
              </Grid>
            </Grid>

            <Grid
              container={true}
              item={true}
              component="section"
              className={classNames(classes.tableSection)}
            >
              <BB8CustomTable
                tableId={"table-user-list"}
                cols={tableHeaders}
                data={this.tableData(
                  bulkJobs,
                  sorting,
                  partnerSelectedId,
                  offers
                )}
                noDataComponent={<div>Could not find any partner(s).</div>}
                pagination={true}
                rowsPerPage={25}
                sorting={sorting}
                sortChange={this.sortChanged}
                classes={ {} }
              />
            </Grid>
          </Grid>
        </BB8MainPage.Padded>
        <BB8Spinner show={isLoading} size={200} />
      </BB8MainPage>
    );
  }

  /*  This function populates the table data:
   */
  private tableData = (
    bulkJobs: IBulkJob[],
    sorting: any,
    partnerSelectedId: string,
    offers: offersActions.IOfferByStatus
  ) => {
    const data = chain(bulkJobs)
      .filter(
        (upload: IBulkJob) =>
          upload.partnerId === partnerSelectedId || _.isEmpty(partnerSelectedId)
      )
      .orderBy(sorting.field, sorting.direction)
      .map((upload: IBulkJob) => {
        const hasBulkJobs = this.ifHasBulk(upload.partnerId, offers);
        return {
          action: (
            <>
              {Authorizer.Instance.checkPermissions(
                ["CREATE_OFFER"],
                this.props.user
              ) && (
                <BB8Button
                  onClick={this.handleViewOrUpdateBulk(
                    "bulk",
                    upload.partnerId,
                    upload.id
                  )}
                  variant="outlined"
                  color="primary"
                  size="large"
                  className={"btn-edit"}
                >
                  Update Bulk
                </BB8Button>
              )}
              <BB8Button
                onClick={this.handleViewOrUpdateBulk(
                  "my-offers",
                  upload.partnerId,
                  upload.id
                )}
                variant="outlined"
                color="primary"
                size="large"
                className={"btn-edit"}
                style={{ marginLeft: ".5em" }}
              >
                View Offers
              </BB8Button>
              <BB8Button
                onClick={this.handleDownloadBulk(upload)}
                variant="outlined"
                color="primary"
                size="large"
                className={"btn-edit"}
                style={{ marginLeft: ".5em" }}
              >
                Download CSV
              </BB8Button>
            </>
          ),
          partnerName: <span>{upload.partnerName}</span>,
          name: <span>{upload.name}</span>,
          createdAt: (
            <span>
              {upload.createdAt
                ? getLocalTime(upload.createdAt, DATE_TIME_FORMAT)
                : ""}
            </span>
          ),
          totalOffers: <span>{upload.totalOffers}</span>
        };
      })
      .value();
    return data;
  };

  private handleViewOrUpdateBulk = (
    pageName: string,
    partnerId: string,
    bulkId: string
  ) => () => {
    this.props.history.push(
      `/offer-submission/${pageName}?bulkId=${encodeURIComponent(
        bulkId
      )}&partnerId=${encodeURIComponent(partnerId)}`
    );
  };

  private handleDownloadBulk = (bulkObj: IBulkJob) => () => {
    const params = new OffersRequestParams({
      bulkId: bulkObj.id,
      partnerId: bulkObj.partnerId
    });

    OffersService.getPaginatedPreviewOffers(`?` + params.toQueryString())
      .then((offers: IOfferFormModel[]) => {
        const bulkOffers = offers;
        const masterHeaders = new Set();
        const parsedObjects: any = [];
        const fieldsToIgnore: string[] = [
          "partnerId",
          "partnerName",
          "integrations"
        ];

        for (const offer of bulkOffers) {
          const parsedObj = generateParseObject(offer,fieldsToIgnore);
          const currentOfferKeys = Object.keys(parsedObj);

          for (const key of currentOfferKeys) {
            if (parsedObj[key]) {
              masterHeaders.add(key);
            }
          }

          parsedObjects.push(parsedObj);
        }

        // Normalize every object returned from the API so we can convert into a CSV by
        // adding `null`s to the field names that exist in some offers  but not in the current offer
        parsedObjects.forEach((obj: any, index: any) => {
          masterHeaders.forEach((header: any) => {
            if (!Object.keys(obj).includes(header)) {
              obj[header] = null;
            }
          });
        });

        const offerKeys: string[] = Array.from(masterHeaders);
        const offerkeysInSortedColumnsList: string[] = OFFER_COLUMNS_ORDER_IN_CSV.filter(
            item => {
              if (offerKeys.includes(item)) {
                return item;
              }
            }
        );
        const remainingOfferKeys: string[] = offerKeys.filter(item => {
          if (
              !offerkeysInSortedColumnsList.includes(item) &&
              !OFFER_COLUMNS_ORDER_IN_CSV.includes(item)
          ) {
            return item;
          }
        });

        const unparseJSON: string = Papa.unparse({
          fields: [...offerkeysInSortedColumnsList, ...remainingOfferKeys],
          data: parsedObjects
        });

        const element = document.createElement("a");
        // Added a UTF-8   byte-order-mark "\ufeff" at the start to indicate the encoding is UTF-8.
        //  For more information on byte-order-mark https://en.wikipedia.org/wiki/Byte_order_mark
        const file = new Blob(["\ufeff", unparseJSON], { type: "text/csv" });
        element.href = URL.createObjectURL(file);
        element.download = `Downloaded copy of ${bulkObj.name}.csv`;
        element.click();
      })
      .catch(() => {
        this.context.displayAlert({
          message: "Error while trying to fetch bulk offers",
          variant: "error"
        });
      });
  };

  private handleGoToBulk = () => {
    this.props.history.push(`/offer-submission/bulk`);
  };

  private ifHasBulk = (
    partnerId: string,
    offers: offersActions.IOfferByStatus
  ) => {
    if (
      offers.fetchedOffers &&
      offers.fetchedOffers.content &&
      offers.fetchedOffers.content.length
    ) {
      return (
        offers.fetchedOffers.content.filter(
          bulk => bulk.partnerId === partnerId
        ).length > 0
      );
    }
    return false;
  };
}

function getPartner(partner: IPartner): string {
  return partner ? `${partner.name}` : "";
}

const renderPartnerOption: React.FunctionComponent<
  IRenderOptionProps<IPartner>
> = props => {
  return (
    <MenuItem {...props.itemProps} key={props.option.id}>
      {props.option.name}
    </MenuItem>
  );
};

const mapStateToProps = (state: IRootState) => ({
  ...withAuthStateToProps(state),
  bulkJobs: state.offers.bulkJobs,
  offers: state.offers.offers,
  partners: state.administration.partners,
  search: state.router!.location.search
});

const mapDispatchToProps = (
  dispatch: ThunkDispatch<IRootState, void, Action>
) =>
  withCommunicationDispatchToProps(dispatch, {
    fetchBulkJobs: () => {
      dispatch(offersActions.fetchBulkJobsFlow());
    },
    filterChanged: (filters: IBulkFilter) => {
      const request =
        filters instanceof BulkRequestParams
          ? filters
          : new BulkRequestParams(filters);
      dispatch(
        replace({
          search: "?" + request.toQueryString()
        })
      );
    },
    publishOffers: (offers: IOfferFormModel[]) => {
      dispatch(offersActions.publishOffersFlow(offers));
    }
  });

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(styles)(withAuthorization(MyBulkUploads, ["VIEW_OFFER"])));
