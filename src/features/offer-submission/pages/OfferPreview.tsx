import { createStyles, Grid, Theme, Typography, withStyles, WithStyles } from "@material-ui/core";
import { get } from "lodash";
import "moment/locale/en-ca";
import "moment/locale/fr";
import React, { PureComponent } from "react";
import { connect } from "react-redux";
import { RouteComponentProps } from "react-router";

import { Link } from "react-router-dom";
import { Action } from "redux";
import { ThunkDispatch } from "redux-thunk";
import { BB8Button, BB8MainPage, BB8Spinner, BB8SystemType } from "../../../BB8";
import { IWithSearchProps } from "../../../components/WithSearch";
import { IOfferCategory } from "../../../models/Category";
import { IOfferFilter, OffersRequestParams } from "../../../models/OffersRequestParams";
import { IPartner } from "../../../models/Partner";
import { IOfferPromotion } from "../../../models/Promotion";
import {
  IRootState,
  IWithCommunicationProps,
  withAuthStateToProps,
  withCommunicationDispatchToProps
} from "../../../store";
import { IOfferFormModel, IOfferFormModelWithNames, OfferStatus } from "../../../validation/validator";
import { UserService } from "../../administration/services";
import withAuthorization from "../../auth/components/Authorization";
import * as communicationActions from "../../communication/actions";
import { SnackbarContext } from "../../communication/AlertContainer";
import * as offersActions from "../actions";
import { fetchCategoriesAsync, fetchPromotionsAsync } from "../actions";
import OfferBlock from "../components/OfferBlock";
import { OfferActionType } from "../components/OffersTable";
import OfferService from "../services/OffersService";
import { getOffersWithReadableNames, removeReadableNamesFromOffers } from "../utils";

interface IOfferPreviewProps
  extends IWithSearchProps,
    IWithCommunicationProps,
    RouteComponentProps,
    WithStyles {
  user: any;
  filter: IOfferFilter;
  publishOffers: (
    offers: IOfferFormModel[],
    postPublishAction: (data: any) => void
  ) => void;
  disableOffers: (
    offerIdsToDisable: string[],
    postDisablingAction: (data: any) => void
  ) => void;
  enableOffers: (
    offerIdsToEnable: string[],
    postEnablingAction: (data: any) => void
  ) => void;
  deleteOffers: (
    offerIdsToDelete: string[],
    postDeletingAction: (offerIds: string[]) => void
  ) => void;
  promotions: IOfferPromotion[];
  fetchPromotions: () => void;
  categories: IOfferCategory[];
  fetchCategories: () => void;
  showTargetAlertAction: () => void;
  showTargetAlertState: boolean;
}
interface IOfferPreviewState {
  isLoading: boolean;
  offers: IOfferFormModel[];
  partners: IPartner[];
  publishedOffersInSession: Set<string>;
  showTargetAlertLocal: boolean;
}

const initialState: IOfferPreviewState = {
  isLoading: true,
  offers: [],
  partners: [],
  publishedOffersInSession: new Set(),
  showTargetAlertLocal: false
};

class OfferPreview extends PureComponent<
  IOfferPreviewProps,
  IOfferPreviewState
> {
  public static contextType = SnackbarContext;
  public contextType: typeof SnackbarContext;
  public state = initialState;
  // For ForwardRefs of the OfferBlock Components
  private OfferComponents: any[] = [];

  public componentDidMount = async () => {
    const partners = await UserService.getUserPartners(
      this.props.user.email,
      true
    );
    this.setState({
      partners,
      publishedOffersInSession: new Set(),
      showTargetAlertLocal: this.props.showTargetAlertState
    });

    this.props.fetchPromotions();
    this.props.fetchCategories();

    const offers: IOfferFormModelWithNames[] = getOffersWithReadableNames(
      await OfferService.getPaginatedPreviewOffers(this.props.location.search),
      this.props.promotions,
      this.props.categories
    );

    this.OfferComponents = offers.map(
      (offer: IOfferFormModel, index: number) => {
        return React.forwardRef((props, ref) => (
          <OfferBlock
            {...props}
            key={index}
            offer={offer}
            ref={ref}
            onMenuAction={this.handleMenuAction}
            shouldHideButtons={this.state.isLoading}
            user={this.props.user}
            partner={partners.find((partnerObj: IPartner) => {
              return partnerObj.id === offer.partnerId;
            })}
            wasPublished={this.state.publishedOffersInSession.has(offer.id)}
          />
        ));
      }
    );

    this.setState({
      offers,
      isLoading: false
    });

    this.handlePostPublish = this.handlePostPublish.bind(this);
    this.handlePostDelete = this.handlePostDelete.bind(this);
    this.handlePostOfferStatusChange = this.handlePostOfferStatusChange.bind(this);
  };

  public componentDidUpdate() {
    if (!this.state.isLoading) {
      this.handlePrintParam();
    }
  }

  public render() {
    const { isLoading, publishedOffersInSession } = this.state;
    const { classes, showTargetAlertState } = this.props;
    const linkToOffers = new OffersRequestParams(
      this.props.filter
    ).toQueryString();

    return (
      <BB8MainPage type={BB8SystemType.Billing}>
        <BB8MainPage.Padded>
          <div className={classes.offerPreviewWapper}>
            <Grid
              item={true}
              container={true}
              direction="row"
              spacing={5}
              className={classes.grid}
              id='offer-details-list-header'
            >
              {/* Header and buttons  */}
              <Grid item={true} xs={9}>
                <Typography gutterBottom={true} variant="h5">
                  Preview Offers
                </Typography>
              </Grid>
              <Grid item={true} xs={2}>
                <BB8Button
                  color="primary"
                  variant="contained"
                  fullWidth={true}
                  onClick={() => window.print()}
                >
                  Export All Offers to PDF
                </BB8Button>
              </Grid>
            </Grid>
            <Grid item={true} xs={12} id="offer-details-list-back">
              <Link to={`/offer-submission/my-offers?${linkToOffers}`}>
                &larr; Back to My Offers
              </Link>
            </Grid>
            {/* Offer Block Component */}
            {this.OfferComponents.map((OfferComponent, index) => (
              <section>
                <Grid item={true} container={true} md={12} lg={11}>
                  <OfferComponent key={index} ref={this.OfferComponents[index]} />
                </Grid>
              </section>
            ))}
          </div>
        </BB8MainPage.Padded>
        <BB8Spinner show={isLoading} size={200} />
      </BB8MainPage>
    );
  }

  private handleMenuAction = (action: OfferActionType, data: any) => {
    switch (action) {
      case OfferActionType.Publish:
        this.props.publishOffers(
          removeReadableNamesFromOffers([data]),
          this.handlePostPublish
        );
        break;
      case OfferActionType.Edit:
        this.props.history.push("/offer-submission/single?id=" + data.id);
        break;
      case OfferActionType.Duplicate:
        this.props.history.push(
          "/offer-submission/single?duplicatedFrom=" + data.id
        );
        break;
      case OfferActionType.Export:
        window.open(
          `/offer-submission/my-offers/offers/preview?id=${data.id}&print`,
          "_blank"
        );
        break;
        case OfferActionType.Enable:
          this.props.enableOffers(
            [data.id],
            this.handlePostOfferStatusChange
          );
          break;
        case OfferActionType.Disable:
          this.props.disableOffers(
            [data.id],
            this.handlePostOfferStatusChange
          );
          break;
      case OfferActionType.Delete:
        this.props.deleteOffers(
          [data.id],
          this.handlePostDelete
        );
        break;
    }
  };

  private rebuildOfferComponents() {
    const { offers, partners } = this.state;
    const offersWithNames: IOfferFormModelWithNames[] = getOffersWithReadableNames(
      offers,
      this.props.promotions,
      this.props.categories
    );
    this.OfferComponents = offersWithNames.map(
      (offer: IOfferFormModel, index: number) => {
        return React.forwardRef((props, ref) => (
          <OfferBlock
            {...props}
            key={index}
            offer={offer}
            ref={ref}
            onMenuAction={this.handleMenuAction}
            shouldHideButtons={this.state.isLoading}
            user={this.props.user}
            partner={partners.find((partnerObj: IPartner) => {
              return partnerObj.id === offer.partnerId;
            })}
            wasPublished={this.state.publishedOffersInSession.has(offer.id)}
          />
        ));
      }
    );
  }

  private handlePostOfferStatusChange(offerId: string) {
    this.setState((currentState) => ({
      offers: currentState.offers.map((offer) => {
        if (offerId === offer.id) {
          offer.active = !offer.active;
        }
        return offer;
      }),
    }));

    const { offers, partners } = this.state;
    this.OfferComponents = offers.map(
      (offer: IOfferFormModel, index: number) => {
        return React.forwardRef((props, ref) => (
          <OfferBlock
            {...props}
            key={index}
            offer={offer}
            ref={ref}
            onMenuAction={this.handleMenuAction}
            shouldHideButtons={this.state.isLoading}
            user={this.props.user}
            partner={partners.find((partnerObj: IPartner) => {
              return partnerObj.id === offer.partnerId;
            })}
            wasPublished={this.state.publishedOffersInSession.has(offer.id)}
          />
        ));
      }
    );
  }

  private handlePostDelete(offerIds: string[]) {
    const deletedOfferId = offerIds[0];

    this.setState(currentState => ({
      offers: currentState.offers.filter(({ id }) => deletedOfferId !== id)
    }));

    if (!this.state.offers || this.state.offers.length < 1) {
      history.back();
    }

    this.OfferComponents = this.OfferComponents.filter(({ current }) => current.props.offer.id !== deletedOfferId);
  }

  private handlePostPublish(offerId: string) {
    const currentOffer = this.state.offers.filter(offer => offer.id === offerId);
    const isCurrentOfferTargeted = get(currentOffer[0], "massOffer", true);
    if(!isCurrentOfferTargeted) {
      this.props.showTargetAlertAction();
    }
    this.setState(currentState => ({
      publishedOffersInSession: currentState.publishedOffersInSession.add(
        offerId
      ),
      offers: currentState.offers
        .reduce((acc: IOfferFormModel[], current): IOfferFormModel[] => {
          if (current.id === offerId) {
            current.status = OfferStatus.Published;
          }
          return [...acc, current];
        }, [])
      }),
      this.rebuildOfferComponents
    );
  }

  private waitForImagesToLoad = () => new Promise<void>(async (resolve) => {
    window.scroll(0, 600); // images don't start loading until scroll
    let complete = false;
    while (!complete) {
      const images = document.querySelectorAll('img');
      complete = Array.from(images).every((image => image.complete));
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    resolve();
  })

  private async handlePrintParam() {
    const searchParams = new URLSearchParams(this.props.location.search)
    if (!searchParams.has('print')) {
      return;
    }

    await this.waitForImagesToLoad();
    window.print();
  }
}

const mapStateToProps = (state: IRootState) => ({
  ...withAuthStateToProps(state),
  filter: state.offers.filter,
  promotions: state.offers.promotions || [],
  categories: state.offers.categories || [],
  showTargetAlertState: state.communication.showTargetAlert
});

const mapDispatchToProps = (
  dispatch: ThunkDispatch<IRootState, void, Action>
) =>
  withCommunicationDispatchToProps(dispatch, {
    publishOffers: (
      offers: IOfferFormModel[],
      postPublishAction: (offerId: string) => void
    ) => {
      dispatch(offersActions.publishOffersFlow(offers, postPublishAction));
    },
    enableOffers: (
      offerIdsToEnable: string[],
      postEnableActions: (offerId: string) => void
    ) => {
      dispatch(
        offersActions.enableOffersFlow(offerIdsToEnable, postEnableActions)
      );
    },
    disableOffers: (
      offerIdsToDisable: string[],
      postDisableActions: (offerId: string) => void
    ) => {
      dispatch(
        offersActions.disableOffersFlow(offerIdsToDisable, postDisableActions)
      );
    },
    deleteOffers: (
      offerIdsToDelete: string[],
      postDeletingActions: (offerIds: string[]) => void
    ) => {
      dispatch(
        offersActions.deleteOffersFlow(offerIdsToDelete, postDeletingActions)
      );
    },
    fetchPromotions: () => dispatch(fetchPromotionsAsync()),
    fetchCategories: () => dispatch(fetchCategoriesAsync()),
    showTargetAlertAction: () => dispatch(communicationActions.showTargetAlert()),
  });

const styles = createStyles((theme: Theme) => ({
  grid: {
    marginTop: theme.spacing(4),
    minWidth: 250
  },
  backTextStyle: {
    textDecoration: "none",
    "&:hover": {
      textDecoration: "underline",
      cursor: "default"
    }
  },
  offerPreviewWapper: {
    width: '100%',
    overflowBlock: 'scroll'
  }
}));

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(styles)(withAuthorization(OfferPreview, ["VIEW_OFFER"])));
