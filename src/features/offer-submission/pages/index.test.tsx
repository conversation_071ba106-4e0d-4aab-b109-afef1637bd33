import React from "react";
import { MemoryRouter } from "react-router";
import { IndexPage } from "./index";
import configureStore from "redux-mock-store";
import { Provider } from "react-redux";
import { render } from '@testing-library/react';
import { getById } from "../../../setupTests";

describe("OfferSubmissionIndex", () => {
  it("renders", () => {
    const mockStore = configureStore();

    const mockedStore = mockStore({
      administration: { partners: [] },
      auth: { user: {} },
      offers: { offers: [] }
    });

    const { container } = render(
      <Provider store={ mockedStore }>
        <MemoryRouter initialEntries={ [""] }>
          <IndexPage match={ { path: "", url: "" } }/>
        </MemoryRouter>
      </Provider>
    );

    expect(container.firstChild).toBeTruthy();
  });

  it("does not render bulk navlink without permissions", () => {
    const mockStore = configureStore();

    const mockedStore = mockStore({
      administration: { partners: [] },
      auth: { user: { email: "<EMAIL>" } },
      offers: { offers: [] },
      router: { location: { search: "" } }
    });

    const { container } = render(
      <Provider store={ mockedStore }>
        <MemoryRouter initialEntries={ [""] }>
          <IndexPage match={ { path: "", url: "" } }/>
        </MemoryRouter>
      </Provider>
    );

    const bulkLinkComponent = getById(container, "navlink-offer-submission-bulk");
    expect(bulkLinkComponent).toBeFalsy();
  });
});
