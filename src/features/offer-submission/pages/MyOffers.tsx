import {
  createStyles,
  Grid,
  Paper,
  Tab,
  TablePagination,
  Tabs,
  Theme,
  withStyles,
  WithStyles
} from "@material-ui/core";
import classNames from "classnames";
import { push } from "connected-react-router";
import { get, map, pickBy, toInteger } from "lodash";
import React from "react";
import { connect } from "react-redux";
import { RouteComponentProps } from "react-router";
import { Action } from "redux";
import { ThunkDispatch } from "redux-thunk";
import { BB8Button, BB8MainPage, BB8Spinner, BB8SystemType } from "../../../BB8";
import { IBulkJob } from "../../../models/BulkJob";
import { IOfferCategory, IOffersCounts } from "../../../models/Category";
import { IOfferFilter, OfferSortField, OffersRequestParams, SortDirection } from "../../../models/OffersRequestParams";
import { IPartner } from "../../../models/Partner";
import { IOfferPromotion } from "../../../models/Promotion";
import { IRbacUser } from "../../../models/User";
import {
  IRootState,
  IWithCommunicationProps,
  withAuthStateToProps,
  withCommunicationDispatchToProps
} from "../../../store";
import { IOfferFormModel, OfferDateContext, OfferStatus, OfferType } from "../../../validation/validator";
import { adminActions } from "../../administration";
import UserService from "../../administration/services/UserService";
import withAuthorization from "../../auth/components/Authorization";
import { Authorizer } from "../../auth/components/Authorizer";
import { UserRoles } from "../../auth/pages/UserRoles";
import { IAuthState } from "../../auth/types";
import * as communicationActions from "../../communication/actions";
import { SnackbarContext } from "../../communication/AlertContainer";
import * as offersActions from "../actions";
import { fetchPromotionsAsync } from "../actions";
import ConfirmationButton from "../components/ConfirmationButtons";
import { FEATURE_FLAG, FeatureFlag } from "../components/FeatureFlag";
import OfferFilters from "../components/OfferFilters";
import OffersTable, { OfferActionType } from "../components/OffersTable";

const OFFERS_PAGE_SIZE = 100;

export const isPublishableState: (
  tab: offersActions.OffersTabs
) => boolean = tab => {
  return tab && (tab === "draft" || tab === "changesPending");
};

export const isDeletableState: (
  tab: offersActions.OffersTabs
) => boolean = tab => {
  return tab && tab === "draft";
};

export const isExpiredTab: (
  tab: offersActions.OffersTabs
) => boolean = tab => {
  return tab && tab === "expired";
};

export const canBeDisabled: (
  tab: offersActions.OffersTabs
) => boolean = tab => {
  return tab && tab !== "disabled" && tab !== "draft" && tab !== "expired";
};

export const canBeEnabled: (tab: offersActions.OffersTabs) => boolean = tab => {
  return tab && tab === "disabled";
};

interface IDefaultValues {
  page: number;
  size: number;
  sort: string;
  direction: string;
  status: string;
  active?: boolean;
  user?: string;
}

const styles = createStyles((theme: Theme) => ({
  root: {
    flexGrow: 1,
    marginBottom: theme.spacing(8)
  },
  section: {
    margin: `0`
  },
  tabSubtitleHolder: {
    padding: `${theme.spacing(1)}px`,
    paddingTop: `${theme.spacing(2)}px`
  },
  tabSubtitle: {
    color: theme.palette.text.disabled,
    fontSize: theme.typography.fontSize * 0.875,
    fontWeight: "lighter"
  },
  tabsRoot: {
    width: "100%"
  },
  tabRoot: {
    "&$tabSelected": {
      color: "#1890ff",
      fontWeight: theme.typography.fontWeightMedium
    },
    "&:focus": {
      color: "#40a9ff"
    },
    "&:hover": {
      color: "#40a9ff",
      opacity: 1
    },
    "& > span": {
      "& > span": {
        paddingLeft: "0px",
        paddingRight: "0px"
      }
    },
    padding: `${theme.spacing(1)}px`,
    paddingTop: `${theme.spacing(1.5)}px`,
    fontWeight: theme.typography.fontWeightRegular,
    marginRight: theme.spacing(1)
  },
  tabSelected: {},
  paginationRoot: {
    border: `0`
  },
  confirmButtons: {
    "& > *": {
      marginLeft: "2em",
      "&:first-child": {
        marginLeft: 0
      }
    }
  }
}));

interface IMyOffersProps
  extends IAuthState,
  IWithCommunicationProps,
  RouteComponentProps,
  WithStyles {
  bulkJobs: IBulkJob[];
  fetchBulkJobs: () => void;
  filterChanged: (filters: IOfferFilter) => void;
  search: string;
  offers: offersActions.IOfferByStatus;
  publishOffers: (
    offers: IOfferFormModel[],
    postPublishingActions: () => void
  ) => void;
  deleteOffers: (
    offerIdsToDelete: string[],
    postDeletingAction: () => void
  ) => void;
  disableOffers: (
    offerIdsToDisable: string[],
    postDisablingAction: () => void
  ) => void;
  enableOffers: (
    offerIdsToEnable: string[],
    postEnablingAction: () => void
  ) => void;
  tabsCounts: IOffersCounts;
  users: IRbacUser[];
  fetchUsers: () => void;
  promotions: IOfferPromotion[];
  categories: IOfferCategory[];
  fetchPromotions: () => void;
  showTargetAlertAction: () => void;
  showTargetAlertState: boolean;
}
interface IMyOffersStates {
  partners?: IPartner[];
  tab: offersActions.OffersTabs;
  selectedOffers: Set<string>;
  page: number;
  isLoading: boolean;
  publishable: boolean;
  deleteable: boolean;
  canBeDisabled: boolean;
  canBeEnabled: boolean;
  users?: IRbacUser[];
  showTargetAlert: boolean;
}

const initialState: IMyOffersStates = {
  partners: [],
  selectedOffers: new Set(),
  tab: offersActions.OffersTabsValues.draft,
  page: 0,
  isLoading: false,
  publishable: true,
  deleteable: true,
  canBeDisabled: true,
  canBeEnabled: true,
  users: [],
  showTargetAlert: false,
};

function findTabFromStatus(
  status: string | undefined,
  dateContext: string | undefined,
  active: boolean | undefined
) {
  if (active && active.toString() === "false") {
    return offersActions.OffersTabsValues.disabled;
  } else if (status === OfferStatus.Updated) {
    return offersActions.OffersTabsValues.changesPending;
  } else if (status === OfferStatus.Published) {
    if (dateContext === OfferDateContext.Staged) {
      return offersActions.OffersTabsValues.staged;
    } else if (dateContext === OfferDateContext.Live) {
      return offersActions.OffersTabsValues.live;
    } else if (dateContext === OfferDateContext.Expired) {
      return offersActions.OffersTabsValues.expired;
    }
  }
  return offersActions.OffersTabsValues.draft;
}

function findStatusAndDateContextFromTab(
  tab: string | undefined
): [OfferStatus | undefined, OfferDateContext | undefined] {
  if (tab === offersActions.OffersTabsValues.changesPending) {
    return [OfferStatus.Updated, OfferDateContext.notExpired];
  } else if (tab === offersActions.OffersTabsValues.staged) {
    return [OfferStatus.Published, OfferDateContext.Staged];
  } else if (tab === offersActions.OffersTabsValues.live) {
    return [OfferStatus.Published, OfferDateContext.Live];
  } else if (tab === offersActions.OffersTabsValues.expired) {
    return [OfferStatus.Published, OfferDateContext.Expired];
  } else if (tab === offersActions.OffersTabsValues.disabled) {
    return [undefined, OfferDateContext.notExpired];
  }
  return [OfferStatus.Draft, undefined];
}

class MyOffers extends React.Component<IMyOffersProps, IMyOffersStates> {
  // https://stackoverflow.com/questions/53575461/react-typescript-context-in-react-component-class
  public static contextType = SnackbarContext;
  public state = initialState;
  public contextType: typeof SnackbarContext;
  public componentDidMount = () => {
    this.props.fetchUsers();

    const { user } = this.props;
    if (user) {
      UserService.getUserPartners(user.email, true)
        .then((partners: IPartner[]) => {
          this.setState({ partners });
        })
        .catch(() => {
          this.context.displayAlert({
            message: "Error while trying to fetch partners",
            variant: "error"
          });
        });
    }

    let params = {};
    // If there are values set in the query string already, store them in params object
    if (this.props.search) {
      // OffersRequestParam adds keys with undefined values, so we remove the falsy values before proceeding
      params = pickBy(OffersRequestParams.fromQueryString(this.props.search));
    }

    let defaultSort = "partnerName";
    let defaultDirection = "asc";
    const defaultValues: IDefaultValues = {
      page: 0,
      size: 100,
      sort: defaultSort,
      direction: defaultDirection,
      status: OfferStatus.Draft,
      active: true
    };

    // We generate a filter object by overriding all of the default initial values with values that are passed in from the params

    const filterObject = Object.assign(defaultValues, params) as IOfferFilter;

    if (filterObject.status === OfferStatus.Draft) {
      defaultSort = "createdAt";
      defaultDirection = "desc";
    } else if (
      filterObject.status === OfferStatus.Published &&
      (filterObject.dateContext === OfferDateContext.Staged ||
       filterObject.dateContext === OfferDateContext.Live)
    ) {
      defaultSort = "publishedAt";
      defaultDirection = "desc";
    } else if (filterObject.status === OfferStatus.Updated) {
      defaultSort = "updatedAt";
      defaultDirection = "desc";
    }

    // If the user is a publisher we want to add the publishers email to the url
    // Only do this on the first time landing on this page by checking that the url does not include the added, required query param "status"
    if (
      user &&
      user.email &&
      user.role === UserRoles.Publisher &&
      !this.props.search.includes("status")
    ) {
      defaultValues.user = user.email;
    }

    this.props.filterChanged(filterObject);
    this.props.fetchBulkJobs();
    this.props.fetchPromotions();
  };

  public handleFilterChange = (
    updatedFilters: IOfferFilter,
    cleanSelectedOffers: boolean = true,
    resetPages: boolean = true
  ) => {
    if (cleanSelectedOffers) {
      this.setState({ selectedOffers: new Set() });
    }
    if (resetPages) {
      this.setState({ page: 0 });
      updatedFilters.page = 0;
    }
    this.props.filterChanged(updatedFilters);
  };

  public render() {
    const { classes, offers, search, user, bulkJobs, users, showTargetAlertState } = this.props;
    const {
      partners,
      selectedOffers,
      isLoading,
      deleteable,
      publishable
    } = this.state;

    const params = OffersRequestParams.fromQueryString(search);
    const { sort, direction, status, dateContext, page, active } = params;
    const sorting = { field: sort, direction };
    const currentTab = findTabFromStatus(status, dateContext, active);
    const canPublishOffers: boolean = Authorizer.Instance.checkPermissions(
      ["PUBLISH_OFFER"],
      user
    );
    const canDeleteOffers: boolean = Authorizer.Instance.checkPermissions(
      ["DELETE_OFFER"],
      user
    );
    const canDisableOffers: boolean = Authorizer.Instance.checkPermissions(
      ["DISABLE_OFFER"],
      user
    );
    const canEnableOffers: boolean = Authorizer.Instance.checkPermissions(
      ["DISABLE_OFFER"],
      user
    );

    const isCustomOfferSelected = this.customOfferSelectionCheck(selectedOffers);

    return (
      <BB8MainPage type={BB8SystemType.Billing}>
        <BB8MainPage.Padded>
          <Grid container={true} direction="column" className={classes.root}>
            <OfferFilters
              partners={partners}
              bulkJobs={bulkJobs}
              filters={params}
              onFilterChange={this.handleFilterChange}
              isAdmin={Boolean(user && user.role === UserRoles.Administrator)}
              currentUser={user ? user.email : undefined}
              key={
                params.user && params.partnerId
                  ? params.user + params.partnerId
                  : params.user
              }
              users={users}
            />
            <Paper elevation={2} square={true} style={{ width: "100%" }}>
              <Grid
                container={true}
                item={true}
                component="section"
                className={classNames("tabs-headers", classes.section)}
              >
                <Grid container={true}>
                  <Grid xs={9} item={true}>
                    <Tabs
                      variant="standard"
                      value={currentTab}
                      onChange={this.handleTabChange}
                      indicatorColor="primary"
                      textColor="primary"
                      classes={{ root: classes.tabsRoot }}
                    >
                      <Tab
                        classes={{
                          root: classes.tabRoot,
                          selected: classes.tabSelected
                        }}
                        label={`draft (${this.props.tabsCounts.draft})`}
                        value="draft"
                      />
                      <Tab
                        classes={{
                          root: classes.tabRoot,
                          selected: classes.tabSelected
                        }}
                        label={`changes pending (${this.props.tabsCounts.changesPending})`}
                        value="changesPending"
                      />
                      <Tab
                        classes={{
                          root: classes.tabRoot,
                          selected: classes.tabSelected
                        }}
                        label={`staged (${this.props.tabsCounts.staged})`}
                        value="staged"
                      />
                      <Tab
                        classes={{
                          root: classes.tabRoot,
                          selected: classes.tabSelected
                        }}
                        label={`live (${this.props.tabsCounts.live})`}
                        value="live"
                      />
                      <Tab
                        classes={{
                          root: classes.tabRoot,
                          selected: classes.tabSelected
                        }}
                        label={`expired (${this.props.tabsCounts.expired})`}
                        value="expired"
                      />
                      {process.env.REACT_APP_DISABLE_OFFER === "true" && (
                        <Tab
                          classes={{
                            root: classes.tabRoot,
                            selected: classes.tabSelected
                          }}
                          label={`Disabled (${this.props.tabsCounts.disabled})`}
                          value="disabled"
                        />
                      )}
                    </Tabs>
                  </Grid>
                  <Grid xs={1} item={true}>
                    <div className={this.props.classes.tabSubtitleHolder}>
                      <span className={this.props.classes.tabSubtitle}>
                        ({selectedOffers.size} offers selected)
                      </span>
                    </div>
                  </Grid>
                  <Grid xs={1} item={true}>
                    <TablePagination
                      count={offers.fetchedOffers.totalCount}
                      rowsPerPage={OFFERS_PAGE_SIZE}
                      page={page ? toInteger(page) : 0}
                      SelectProps={{
                        native: true
                      }}
                      classes={{
                        root: classes.paginationRoot,
                        toolbar: classes.paginationToolbar
                      }}
                      rowsPerPageOptions={[]}
                      onChangePage={this.handlePageChange}
                    />
                  </Grid>
                </Grid>
              </Grid>

              <Grid
                container={true}
                item={true}
                component="section"
                className={classNames("tabs-content")}
              >
                <OffersTable
                  tab={currentTab}
                  offers={offers.fetchedOffers.content}
                  onOfferSelected={this.handleOfferSelected}
                  onOfferUnSelected={this.handleOfferUnSelected}
                  selectedOffers={selectedOffers}
                  onOfferSelectAll={this.handleSelectAll}
                  onOfferUnSelectAll={this.handleUnselectAll}
                  onMenuAction={this.handleMenuAction}
                  sorting={sorting}
                  onSortChanged={this.handleSortChanged}
                  rowsPerPage={OFFERS_PAGE_SIZE}
                  promotions={this.props.promotions}
                  categories={this.props.categories}
                />

                <Grid
                  container={true}
                  item={true}
                  spacing={2}
                  component="section"
                  xs={12}
                  className={classes.section}
                  style={{
                    position: "sticky",
                    bottom: "0",
                    background: "#FFFFFF",
                    borderTop: "1px solid #e0e0e0"
                  }}
                >
                  <Grid item={true} xs={4}>
                    <BB8Button
                      variant="contained"
                      color="primary"
                      disabled={!Boolean(selectedOffers && selectedOffers.size)}
                      onClick={this.handlePreviewOffers}
                    >
                      Preview selected ({selectedOffers.size})
                    </BB8Button>
                  </Grid>

                  <Grid
                    item={true}
                    container={true}
                    justify="flex-end"
                    style={{ width: "auto", flexGrow: 1 }}
                    className={classes.confirmButtons}
                  >
                    {isPublishableState(currentTab) &&
                      publishable &&
                      canPublishOffers && (
                        <ConfirmationButton
                          onConfirmationAction={this.handlePublishOffers}
                          onToggleConfirmation={this.toggleDeleteable}
                          isDefaultDisabled={
                            !Boolean(selectedOffers && selectedOffers.size)
                          }
                          defaultText={`Publish selected (${selectedOffers.size})`}
                          confirmText={`Confirm Publish (${selectedOffers.size})`}
                          cancelText={`Cancel Publish (${selectedOffers.size})`}
                          resetRequest={selectedOffers.size === 0}
                          hasCustomOfferType={isCustomOfferSelected}
                        />
                      )}
                    <FeatureFlag featureName={FEATURE_FLAG.DELETE_OFFERS}>
                      {isDeletableState(currentTab) &&
                        deleteable &&
                        canDeleteOffers && (
                          <ConfirmationButton
                            onConfirmationAction={this.handleDeleteOffers}
                            onToggleConfirmation={this.togglePublishable}
                            isDefaultDisabled={
                              !Boolean(selectedOffers && selectedOffers.size)
                            }
                            defaultText={`Delete selected (${selectedOffers.size})`}
                            confirmText={`Confirm Delete (${selectedOffers.size})`}
                            cancelText={`Cancel Delete (${selectedOffers.size})`}
                            resetRequest={selectedOffers.size === 0}
                          />
                        )}
                    </FeatureFlag>
                    <FeatureFlag featureName={FEATURE_FLAG.DISABLE_OFFER}>
                      {canBeDisabled(currentTab) &&
                        canBeDisabled &&
                        canDisableOffers && (
                          <ConfirmationButton
                            onConfirmationAction={this.handleDisableOffers}
                            onToggleConfirmation={this.toggleDisable}
                            isDefaultDisabled={
                              !Boolean(selectedOffers && selectedOffers.size)
                            }
                            defaultText={`Disable selected (${selectedOffers.size})`}
                            confirmText={`Confirm Disable (${selectedOffers.size})`}
                            cancelText={`Cancel Disable (${selectedOffers.size})`}
                            resetRequest={selectedOffers.size === 0}
                          />
                        )}
                      {canBeEnabled(currentTab) &&
                        canBeEnabled &&
                        canEnableOffers && (
                          <ConfirmationButton
                            onConfirmationAction={this.handleEnableOffers}
                            onToggleConfirmation={this.toggleEnable}
                            isDefaultDisabled={
                              !Boolean(selectedOffers && selectedOffers.size)
                            }
                            defaultText={`Enable selected (${selectedOffers.size})`}
                            confirmText={`Confirm Enable (${selectedOffers.size})`}
                            cancelText={`Cancel Enable (${selectedOffers.size})`}
                            resetRequest={selectedOffers.size === 0}
                          />
                        )}
                    </FeatureFlag>
                  </Grid>
                </Grid>
              </Grid>
            </Paper>
          </Grid>
        </BB8MainPage.Padded>
        <BB8Spinner show={isLoading} size={200} />
      </BB8MainPage>
    );
  }

  private handleTabChange = (
    e: React.ChangeEvent<{}>,
    value: offersActions.OffersTabs
  ) => {
    const params = OffersRequestParams.fromQueryString(this.props.search);
    const [status, dateContext] = findStatusAndDateContextFromTab(value);
    let sort = "partnerName";
    let direction = "asc";

    if (value === "draft") {
      sort = "createdAt";
      direction = "desc";
    } else if (value === "staged" || value === "live") {
      sort = "publishedAt";
      direction = "desc";
    } else if (value === "changesPending") {
      sort = "updatedAt";
      direction = "desc";
    }

    this.handleFilterChange({
      ...params,
      status,
      dateContext,
      page: 0,
      active: value === "disabled" ? false : value === "expired" ? undefined : true,
      sort,
      direction
    });

    this.setState({ tab: value, selectedOffers: new Set() });
  };

  private handlePageChange = (
    event: React.MouseEvent<HTMLButtonElement, MouseEvent> | null,
    page: number
  ) => {
    // This check exists to get around the "initial firing of the event" that Material UI sends in.
    // Without this check, the 0th page will be loaded every time when you open MyOffers page.
    if (event) {
      const params = OffersRequestParams.fromQueryString(this.props.search);

      // We only want to retain selected offers while browsing through the pages, thus passing 'false' flag
      // for cleaning up the offers
      this.handleFilterChange({ ...params, page }, false, false);
      this.setState({ page });
    }
  };

  private handleOfferSelected = (offer: IOfferFormModel) => {
    this.setState(({ selectedOffers }) => ({
      selectedOffers: new Set(selectedOffers).add(offer.id)
    }));
  };

  private handleOfferUnSelected = (offer: IOfferFormModel) => {
    this.setState(({ selectedOffers }) => {
      const newChecked = new Set(selectedOffers);
      newChecked.delete(offer.id);

      return {
        selectedOffers: newChecked
      };
    });
  };

  private handleSelectAll = () => {
    this.setState({
      selectedOffers: new Set(
        map(this.props.offers.fetchedOffers.content, "id")
      )
    });
  };
  private handleUnselectAll = () => {
    this.setState({ selectedOffers: new Set() });
  };

  private handlePublishOffers = () => {
    const { offers } = this.props;
    const { selectedOffers } = this.state;

    this.props.publishOffers(
      offers.fetchedOffers.content.filter(o => selectedOffers.has(o.id)),
      this.postPublishActions
    );
  };

  private handleDeleteOffers = () => {
    const { offers } = this.props;
    const { selectedOffers } = this.state;

    this.props.deleteOffers(
      offers.fetchedOffers.content
        .filter(o => selectedOffers.has(o.id))
        .map(o => o.id),
      this.cleanSelectedOffers
    );
  };

  private handleDisableOffers = () => {
    const { offers } = this.props;
    const { selectedOffers } = this.state;

    this.props.disableOffers(
      offers.fetchedOffers.content
        .filter(o => selectedOffers.has(o.id))
        .map(o => o.id),
      this.cleanSelectedOffers
    );
  };

  private handleEnableOffers = () => {
    const { offers } = this.props;
    const { selectedOffers } = this.state;

    this.props.enableOffers(
      offers.fetchedOffers.content
        .filter(o => selectedOffers.has(o.id))
        .map(o => o.id),
      this.cleanSelectedOffers
    );
  };

  private toggleDeleteable = () => {
    this.setState({ deleteable: !this.state.deleteable });
  };

  private togglePublishable = () => {
    this.setState({ publishable: !this.state.publishable });
  };

  private toggleDisable = () => {
    this.setState({ canBeDisabled: !this.state.canBeDisabled });
  };

  private toggleEnable = () => {
    this.setState({ canBeEnabled: !this.state.canBeEnabled });
  };

  private postPublishActions = () => {
    this.setShowTargetAlert();
    this.cleanSelectedOffers();
  }

  private cleanSelectedOffers = () => {
    this.setState({ selectedOffers: new Set()});
  };

  private setShowTargetAlert = () => {
    const { selectedOffers } = this.state;
    const isTargetedOfferPublished = this.targetedOfferSelectionCheck(selectedOffers);
    if(isTargetedOfferPublished) {
      this.props.showTargetAlertAction();
    }
  };

  private handlePreviewOffers = () => {
    const { selectedOffers } = this.state;
    this.previewOffers(selectedOffers);
  };

  private previewOffers = (selectedOffers: Set<string>) => {
    const params = OffersRequestParams.fromQueryString(this.props.search);

    this.props.history.push(
      this.props.match.path +
      "/offers/preview?id=" +
      Array.from(selectedOffers).join(",") +
      "&" +
      "sort=" +
      params.sort +
      "&" +
      "direction=" +
      params.direction
    );
  };

  private handleMenuAction = (action: OfferActionType, data: any) => {
    switch (action) {
      case OfferActionType.Publish:
        this.props.publishOffers([data], this.postPublishActions);
        break;
      case OfferActionType.Preview:
        this.previewOffers(new Set([data.id]));
        break;
      case OfferActionType.Edit:
        this.props.history.push("/offer-submission/single?id=" + data.id);
        break;
      case OfferActionType.Duplicate:
        this.props.history.push(
          "/offer-submission/single?duplicatedFrom=" + data.id
        );
        break;
    }
  };

  private handleSortChanged = (
    field: OfferSortField,
    direction: SortDirection
  ) => {
    const params = OffersRequestParams.fromQueryString(this.props.search);
    this.handleFilterChange({ ...params, sort: field, direction });
  };

  private customOfferSelectionCheck = (selectedOffers: Set<string>) => {
    let hasCustomOffer = false;
    for (const offerId of selectedOffers) {
      const offerSelected = this.props.offers.fetchedOffers.content.find(offer => offer.id === offerId);
      if (offerSelected && offerSelected.offerType == OfferType.Custom) {
        hasCustomOffer = true;
        break;
      }
    }
    return hasCustomOffer;
  }

  private targetedOfferSelectionCheck = (selectedOffers: Set<string>) => {
    let hasTargetedOffer = false;
    for (const offerId of selectedOffers) {
      const offerSelected = this.props.offers.fetchedOffers.content.find(offer => offer.id === offerId);
      if (offerSelected && get(offerSelected, "massOffer", true) === false ) {
        hasTargetedOffer = true;
        break;
      }
    }
    return hasTargetedOffer;
  }
}
const mapStateToProps = (state: IRootState) => ({
  ...withAuthStateToProps(state),
  bulkJobs: state.offers.bulkJobs,
  offers: state.offers.offers,
  promotions: state.offers.promotions || [],
  categories: state.offers.categories || [],
  partners: state.administration.partners,
  search: state.router!.location.search,
  tabsCounts: state.offers.offers.fetchedOffersTabsCounts,
  users: state.administration.users,
  showTargetAlertState: state.communication.showTargetAlert,
});

const mapDispatchToProps = (
  dispatch: ThunkDispatch<IRootState, void, Action>
) =>
  withCommunicationDispatchToProps(dispatch, {
    fetchBulkJobs: () => {
      dispatch(offersActions.fetchBulkJobsFlow());
    },
    filterChanged: (filters: IOfferFilter) => {
      const request =
        filters instanceof OffersRequestParams
          ? filters
          : new OffersRequestParams(filters);

      dispatch(
        push({
          search: "?" + request.toQueryString()
        })
      );

      dispatch(offersActions.fetchOffersFlow(request));
    },
    publishOffers: (
      offers: IOfferFormModel[],
      postPublishingActions: () => void
    ) => {
      dispatch(offersActions.publishOffersFlow(offers, postPublishingActions));
    },
    fetchPromotions: () => dispatch(fetchPromotionsAsync()),
    deleteOffers: (
      offerIdsToDelete: string[],
      postDeletingActions: () => void
    ) => {
      dispatch(
        offersActions.deleteOffersFlow(offerIdsToDelete, postDeletingActions)
      );
    },
    disableOffers: (
      offerIdsToDisable: string[],
      postDisablingActions: () => void
    ) => {
      dispatch(
        offersActions.disableOffersFlow(offerIdsToDisable, postDisablingActions)
      );
    },
    enableOffers: (
      offerIdsToEnable: string[],
      postEnablingActions: () => void
    ) => {
      dispatch(
        offersActions.enableOffersFlow(offerIdsToEnable, postEnablingActions)
      );
    },
    fetchUsers: () => {
      dispatch(adminActions.fetchUsersFlow(true));
    },
    showTargetAlertAction: () => dispatch(communicationActions.showTargetAlert()),
  });

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(styles)(withAuthorization(MyOffers, ["VIEW_OFFER"])));
