import { Qualifier, AwardType, OfferType } from "../../../validation/validator";

export function getDefaultLocalizedObject() {
  return { "en-US": undefined, "fr-CA": undefined };
}

export function getDefaultFuelLocalizedObject(){
  return {
    "en-US": "any grade fuel",
    "fr-CA": "n’importe quel grade de carburant"
  };
}

export function getOfferDefaultContentTier(
  qualifier: Qualifier,
  awardType: AwardType,
  offerType: OfferType
) {
  return {
    awardValue: undefined,
    qualifierValue:
        offerType === OfferType.Buy && qualifier === Qualifier.PerProduct ? 1 : undefined,
    qualifierFrequency: 
      qualifier === Qualifier.Fuel
      ? undefined
      : 1,
    content:
      qualifier === Qualifier.Product || qualifier === Qualifier.Category
        ? [getDefaultLocalizedObject()]
        : qualifier === Qualifier.Fuel
        ? [getDefaultFuelLocalizedObject()]
        : undefined,
    qualifierLong:
      qualifier === Qualifier.Custom ? getDefaultLocalizedObject() : undefined,
    awardLong:
      awardType === AwardType.Custom ? getDefaultLocalizedObject() : undefined
  };
}
