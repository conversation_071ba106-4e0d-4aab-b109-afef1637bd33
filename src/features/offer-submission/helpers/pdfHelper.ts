import jspdf from "jspdf";
import moment from "moment";
import html2canvas from 'html2canvas';

// tslint:disable-next-line: no-empty
export const exportToPDF = async (
  ref?: any,
  nodes?: any[],
  callback?: any
): Promise<any> => {
  // We use a set Timeout in this callback to give a better UX so that we can make sure the loading spinner comes up right away
  // before we start stealing all the memory needed to do this operation otherwise there will be a delay in the spinner popping up
  const pdf = new jspdf({
    orientation: "portrait",
    unit: "pt",
    compressPdf: true
  });

  pdf.setFontSize(10);

  const nodesToWorkOn: HTMLElement[] = ref ? [ref.current] : nodes ? nodes : [];

  try {
    let index = 0;
    const totalNodes: number = nodesToWorkOn.length;
    // WARNING: Currently Going over 1 nodes at a time leads to diminishing results
    // WARNING: this operation will take a file, min 1sec/offer
    const maxNodesToWorkOnAtATime: number = 1;
    let listOfNodesToWorkOnAtATime: any[] = [];

    for (const node of nodesToWorkOn) {
      // Instead of synchronously working on one offer at a time, we try to group a couple offers at a time
      // to run asynchronously, and then we wait for this group of offers to finish exporting before starting
      // the next batch
      listOfNodesToWorkOnAtATime.push(node);

      if (
        listOfNodesToWorkOnAtATime.length === maxNodesToWorkOnAtATime ||
        index >= totalNodes - 1
      ) {
        await Promise.all(
          listOfNodesToWorkOnAtATime.map(n => {
            return callback(
              n,
              nodesToWorkOn.indexOf(n),
              index,
              pdf,
              totalNodes
            );
          })
        );
        listOfNodesToWorkOnAtATime = [];
      }
      index++;
    }
    const fileName = `offer-exports__${moment().format("YYYY-MM-DD_HHmmss")}`;
    pdf.save(`${fileName}.pdf`);
    // this.setState({
    //   isLoading: false
    // });
  } catch (error) {
    console.error(error);
  }
};

export const processExportToPDF = async (
  node: any,
  nodeIndex: number,
  pdf: jspdf,
  totalNodes: number
) => {
  const marginFromTop = 10;
  const sideMargins = 20;
  const screenShot: HTMLCanvasElement = await html2canvas(node, {
    scale: 0.8,
    useCORS: true
  });
  const scaleWidth = 570 / screenShot.width;
  const name = `imageToExport-${nodeIndex}`;
  screenShot.setAttribute("id", name);
  pdf.addImage(
    screenShot,
    "JPEG", // format
    sideMargins, // x coordinate
    marginFromTop + 25, // y coordinate
    screenShot.width * scaleWidth, // width
    0, // height
    `offer-${nodeIndex}`, // alias
    "FAST", // compression
    null // rotation
  );

  let str = "Page " + (nodeIndex + 1);
  if (typeof pdf.putTotalPages === "function") {
    str = str + "/" + totalNodes;
  }
  pdf.text(sideMargins, marginFromTop + 20, str);
  if (nodeIndex !== totalNodes - 1) {
    pdf.addPage();
  }
};
