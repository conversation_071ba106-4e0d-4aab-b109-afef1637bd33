export const getCardTypeLabelPartnerMech = (offerCards: Set<string> | null, isOptIn: boolean = false) => {
  if (!offerCards) {
    return {
      "en-US": "",
      "fr-CA": ""
    };
  }

  const prefix = isOptIn ? "Opt in + " : "";
  const prefixFr = isOptIn ? "Activer + " : "";

  if (offerCards.size === 1 && offerCards.has("BmoDebit")) {
    return {
      "en-US": `${prefix}BMO Debit Card`,
      "fr-CA": `${prefixFr}Carte de débit BMO`
    };
  }

  if (offerCards.size === 2 && offerCards.has("NonBmoMastercard") && offerCards.has("BmoMastercard")) {
    return {
      "en-US": `${prefix}Mastercard credit card`,
      "fr-CA": `${prefixFr}Carte de crédit Mastercard`
    };
  }

  const validCards = ["NonBmoMastercard", "BmoMastercard", "BmoDebit"];

  if (validCards.every(card => offerCards.has(card))) {
    return {
      "en-US": `${prefix}All linked cards`,
      "fr-CA": `${prefixFr}Toutes les cartes liées`
    };
  }
};

export const getCardTypeLabelPartnerDetails = (offerCards: Set<string> | null) => {
  if (!offerCards) {
    return {
      "en-US": "",
      "fr-CA": ""
    };
  }

  if (offerCards.size === 1 && offerCards.has("BmoDebit")) {
    return {
      "en-US": `Offer valid for any linked BMO debit card`,
      "fr-CA": `Offre valable pour toute carte de débit BMO liée`
    };
  }

  if (offerCards.size === 2 && offerCards.has("NonBmoMastercard") && offerCards.has("BmoMastercard")) {
    return {
      "en-US": `Offer valid for any linked Mastercard credit card`,
      "fr-CA": `Offre valable pour toute carte de crédit Mastercard liée`
    };
  }

  const validCards = ["NonBmoMastercard", "BmoMastercard", "BmoDebit"];

  if (validCards.every(card => offerCards.has(card))) {
    return {
      "en-US": `Offer valid for any linked Mastercard or BMO debit card`,
      "fr-CA": `Offre valable pour toute carte Mastercard ou toute carte de débit BMO liée`
    };
  }
};