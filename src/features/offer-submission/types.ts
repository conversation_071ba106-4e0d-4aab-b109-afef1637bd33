import { IOfferByStatus, OffersActionType } from "./actions";

import { IBulkJob } from "../../models/BulkJob";
import { IOfferFilter } from "../../models/OffersRequestParams";
import { ReactRouterAction } from "../../types";
import { IOfferCategory } from "../../models/Category";
import { IOfferPromotion } from "../../models/Promotion";

export type RootAction = ReactRouterAction | OffersActionType;

export interface IOffersState {
  offers: IOfferByStatus;
  bulkJobs: IBulkJob[];
  filter: IOfferFilter;
  categories: IOfferCategory[];
  promotions: IOfferPromotion[];
}
