import React from "react";
import { noop } from "rxjs";
import { IPermission, IRole, IRoleMatrix } from "../../../models/Role";
import { RolesListPage } from "./RolesListPage";
import { render, screen } from '@testing-library/react';
import { getById } from "../../../setupTests";

const roles: IRole[] = [];
const permissions: IPermission[] = [];

const roleMatrix: IRoleMatrix = {
  availableActions: [],
  roles: []
};

const defaultProps = {
  classes: {},
  fetchPermissions: noop,
  fetchRoles: noop,
  onAddRole: noop,
  onPermissionChanged: noop,
  onSave: noop,
  permissions,
  roleMatrix,
  roles
};

const defaultComponent = <RolesListPage { ...defaultProps } />;

describe("RolesListPage", () => {
  it("renders without crashing", () => {
    const { container } = render(defaultComponent);
    expect(container.firstChild).toBeTruthy();
  });

  it("displays add new role button", () => {
    render(defaultComponent);
    expect(screen.getByRole('button', { name: "Add a new role" })).toBeTruthy();
  });

  it("displays roles table", () => {
    const { container } = render(defaultComponent);
    expect(getById(container, "table-roles")).toBeTruthy();
  });

  it("componentDidMount triggers fetch", () => {
    const fetchRolesMock = jest.fn();
    const fetchPermissionsMock = jest.fn();
    const component = render(
      <RolesListPage
        classes={ {} }
        roles={ roles }
        permissions={ permissions }
        roleMatrix={ roleMatrix }
        fetchRoles={ fetchRolesMock }
        fetchPermissions={ fetchPermissionsMock }
        onAddRole={ noop }
        onPermissionChanged={ noop }
        onSave={ noop }
      />
    );

    expect(fetchRolesMock).toHaveBeenCalled();
    expect(fetchPermissionsMock).toHaveBeenCalled();
  });
});
