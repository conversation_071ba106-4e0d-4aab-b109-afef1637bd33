import React from "react";
import * as Redux from "redux";
import configureS<PERSON> from "redux-mock-store";
import { noop } from "rxjs";
import { IGroup } from "../../../models/Group";
import { StyledGroupsListPage } from "./GroupsListPage";
import { Provider } from "react-redux";
import { render, screen } from '@testing-library/react';
import { MemoryRouter } from "react-router-dom";
import { getById } from "../../../setupTests";

const groups: IGroup[] = [];

const middlewares: Array<Redux.Middleware<{}, any, Redux.Dispatch<Redux.AnyAction>>> = [];

const mockStore = configureStore(middlewares);

const defaultComponent = (
  <MemoryRouter initialEntries={ [""] }>
    <StyledGroupsListPage
      groups={ groups }
      fetchGroups={ noop }
      onAddGroup={ noop }
      searchString=""
      onSearchChange={ noop }
      onSearchClear={ noop }
    />
  </MemoryRouter>
);

describe("StyledGroupsListPage", () => {
  it("renders without crashing", () => {
    const { container } = render(defaultComponent);
    expect(container.firstChild).toBeTruthy();
  });

  it("displays add new Group button", () => {
    render(defaultComponent);
    expect(screen.getByRole('button', { name: "Add this as a new group" })).toBeTruthy();
  });

  it("displays Groups table", () => {
    const { container } = render(defaultComponent);
    expect(getById(container, "table-group-list")).toBeTruthy();
  });

  it("componentDidMount triggers fetch", () => {
    const fetchGroupsAndPartnersMock = jest.fn();
    const component = render(
      <Provider store={ mockStore({ auth: { user: {} }, offers: { offers: [] } }) }>
        <MemoryRouter initialEntries={ [""] }>
          <StyledGroupsListPage
            groups={ groups }
            fetchGroups={ fetchGroupsAndPartnersMock }
            onAddGroup={ noop }
            searchString=""
            onSearchChange={ noop }
            onSearchClear={ noop }
          />
        </MemoryRouter>
      </Provider>
    );

    expect(fetchGroupsAndPartnersMock).toHaveBeenCalled();
  });
});
