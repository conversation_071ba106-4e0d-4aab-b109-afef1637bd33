import { getGroupsAndPartners } from ".";
import { IGroup } from "../../../models/Group";
import { IPartner } from "../../../models/Partner";
import { IPermission, IRole } from "../../../models/Role";
import { IRootState } from "../../../store";

const role: IRole = {
  id: 0,
  name: "USER",
  rolePermissions: []
};

const permission: IPermission = {
  id: 0,
  name: "<PERSON>NA<PERSON>"
};
const roles = [role];
const rolePermission = {
  permission,
  permissionId: permission.id,
  role,
  roleId: role.id
};

role.rolePermissions = [rolePermission];
const partner: IPartner = {
  baseEarnRate: "0",
  fullLogo: [""],
  id: "0",
  maxCashMiles: "",
  name: "Partner 1",
  priority: 1,
  regionOverrides: [],
  regions: [],
  sponsorCodes: [],
  webColourLogo: [],
  webWhiteLogo: []
};
const groups: IGroup[] = [
  {
    id: "0",
    name: "Group 1",
    partners: [partner]
  }
];

const partners: IPartner[] = [partner];
const mockState: IRootState = {
  administration: {
    groups,
    partners,
    permissions: [permission],
    roles
  },
  auth: {
    permissionConfig: {
      actions: ["MANAGE_RESOURCE"],
      mappings: {
        MANAGE_RESOURCE: ["VIEW", "CREATE", "EDIT", "DELETE"]
      }
    }
  },
  communication: { pendingRequests: 0 }
};

describe("GroupsAndPartner Selector", () => {
  it("selects groups and partners", () => {
    const groupsWithPartners = getGroupsAndPartners(mockState);

    expect(groupsWithPartners[0].partners).toBeDefined();
  });
});
