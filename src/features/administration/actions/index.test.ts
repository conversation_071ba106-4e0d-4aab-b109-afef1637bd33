import { IRole } from "../../../models/Role";
import { ApiError } from "../../../shared/services/ApiError";
import * as adminActions from "./index";

const mockedRoles: any[] = [];

jest.mock("../services/RoleService", () => ({
  getRoles() {
    return Promise.resolve(mockedRoles);
  }
}));
describe("User Administration", () => {
  describe("Role Actions", () => {
    it("fetchRoles triggers request", () => {
      const requestResult = adminActions.fetchRoles.request();
      expect(requestResult).toEqual({ type: "ROLES/FETCH" });

      const roles: IRole[] = [];
      const successResult = adminActions.fetchRoles.success(roles);
      expect(successResult).toEqual({
        payload: roles,
        type: "ROLES/FETCH_SUCCESS"
      });

      const error = new ApiError({});
      const failureResult = adminActions.fetchRoles.failure(error);
      expect(failureResult).toEqual({
        payload: error,
        type: "ROLES/FETCH_ERROR"
      });
    });

    it("fetchRolesFlow works", async () => {
      const dispatch = jest.fn();
      const getState = jest.fn();
      getState.mockImplementation(() => ({ administration: { roles: [] } }));
      await adminActions.fetchRolesFlow()(dispatch, getState);
      expect(dispatch).toBeCalledWith({ type: "ROLES/FETCH" });
      expect(dispatch).toBeCalledWith({
        payload: mockedRoles,
        type: "ROLES/FETCH_SUCCESS"
      });
    });

    it("addNewRole", () => {
      const addRole = adminActions.addNewRole();
      expect(addRole).toEqual({ type: "ROLES/ADD" });
    });
  });
});
