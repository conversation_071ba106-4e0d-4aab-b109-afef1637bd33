import { ApiService, IApiService } from "../../../shared/services/api.service";
import { GroupService } from "./GroupService";

describe("GroupService", () => {
  let fakeApiMiddleware: IApiService;
  beforeEach(() => {
    const fakeData = [{ data: "test" }];
    const fakeFetch = () =>
      Promise.resolve<Response>(new Response(JSON.stringify(fakeData)));
    fakeApiMiddleware = new ApiService(fakeFetch);
  });
  it("should getGroups", done => {
    const groupService = new GroupService(fakeApiMiddleware);
    groupService.getGroups().then(groups => {
      expect(groups).toBeDefined();
      done();
    });
  });
});
