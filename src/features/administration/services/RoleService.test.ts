import { ApiService, IApiService } from "../../../shared/services/api.service";
import { RoleService } from "./RoleService";

describe("RoleService", () => {
  let fakeApiMiddleware: IApiService;

  beforeEach(() => {
    const fakeData = [{ data: "test" }];
    const fakeFetch = () =>
      Promise.resolve<Response>(new Response(JSON.stringify(fakeData)));
    fakeApiMiddleware = new ApiService(fakeFetch);
  });

  it("should getRoles", done => {
    const roleService = new RoleService(fakeApiMiddleware);
    roleService.getRoles().then(roles => {
      expect(roles).toBeDefined();
      done();
    });
  });
});
