<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="omnes_lightregular" horiz-adv-x="1095" >
<font-face units-per-em="2048" ascent="1434" descent="-614" />
<missing-glyph horiz-adv-x="360" />
<glyph unicode="&#xfb01;" horiz-adv-x="1038" d="M113 905q0 11 5.5 28.5t12.5 24.5q11 11 37 11h164l26 118q66 316 301 316q109 0 164 -55q25 -25 25 -56q0 -16 -9 -29t-18.5 -18.5t-11.5 -3.5q-19 33 -62 54.5t-94 21.5q-83 0 -130 -63t-72 -181l-25 -104h215q45 0 45 -25q0 -11 -5.5 -27.5t-12.5 -23.5 q-12 -12 -39 -12h-219l-177 -840q-6 -26 -17 -36.5t-30 -10.5h-10q-51 0 -41 47l178 840h-155q-45 0 -45 24zM727 193l156 735q6 26 17 36.5t30 10.5h12q49 0 39 -47l-156 -733q-17 -80 7 -110t92 -30q3 0 0.5 -17t-20 -34.5t-52.5 -17.5q-78 0 -112.5 51.5t-12.5 155.5z M942 1284q0 34 19.5 58t54.5 24q69 0 69 -53q0 -34 -18.5 -58t-52.5 -24q-72 0 -72 53z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1038" d="M113 905q0 11 5.5 28.5t12.5 24.5q11 11 37 11h164l26 118q66 316 301 316q109 0 164 -55q25 -25 25 -56q0 -16 -9 -29t-18.5 -18.5t-11.5 -3.5q-19 33 -62 54.5t-94 21.5q-83 0 -130 -63t-72 -181l-25 -104h215q45 0 45 -25q0 -11 -5.5 -27.5t-12.5 -23.5 q-12 -12 -39 -12h-219l-177 -840q-6 -26 -17 -36.5t-30 -10.5h-10q-51 0 -41 47l178 840h-155q-45 0 -45 24zM727 193l246 1148q6 26 16 37t29 11h12q49 0 41 -48l-246 -1146q-17 -80 7 -110t92 -30q3 0 0.5 -17t-20 -34.5t-52.5 -17.5q-78 0 -112.5 51.5t-12.5 155.5z" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph unicode=" "  horiz-adv-x="360" />
<glyph unicode="&#x09;" horiz-adv-x="360" />
<glyph unicode="&#xa0;" horiz-adv-x="360" />
<glyph unicode="!" horiz-adv-x="475" d="M70 45q0 59 26 82q20 20 58 20q75 0 75 -53q0 -57 -24 -84q-20 -20 -60 -20q-35 0 -55 14.5t-20 40.5zM188 395l156 881q6 33 19 44t38 11q44 0 61.5 -14t6.5 -51l-221 -883q-6 -31 -27 -31q-23 0 -29.5 9t-3.5 34z" />
<glyph unicode="&#x22;" horiz-adv-x="679" d="M252 883l51 372q4 41 19.5 58.5t44.5 17.5q43 0 58 -19.5t1 -68.5l-107 -366q-12 -39 -32 -39q-24 0 -31 9t-4 36zM530 883l50 372q4 41 20 58.5t45 17.5q44 0 59 -19.5t1 -68.5l-107 -366q-12 -39 -33 -39q-24 0 -31 9t-4 36z" />
<glyph unicode="#" horiz-adv-x="1280" d="M37 401q0 12 5.5 29.5t12.5 24.5q10 10 39 10h236l123 395h-222q-45 0 -45 25q0 30 19 53q13 10 39 10h233l117 379q2 7 25.5 4t37.5 -27t-4 -96l-80 -260h359l117 379q2 7 25.5 4t37.5 -27t-4 -96l-80 -260h221q45 0 45 -24q0 -12 -5.5 -29.5t-12.5 -24.5 q-10 -10 -39 -10h-238l-120 -395h221q45 0 45 -25q0 -11 -6 -27.5t-13 -23.5q-12 -12 -39 -12h-235l-115 -379q-2 -7 -25.5 -4t-37.5 27t4 96l80 260h-359l-116 -379q-2 -7 -26 -4t-38 27t4 96l80 260h-221q-45 0 -45 24zM422 463h364l123 399h-364z" />
<glyph unicode="$" horiz-adv-x="1085" d="M31 268q0 23 15.5 38t31 18.5t16.5 -0.5q33 -92 112 -158t204 -88l120 559q-73 30 -121.5 56.5t-97.5 67.5t-73.5 97.5t-24.5 128.5q0 154 120 256.5t316 102.5h29l29 129q6 26 16 36.5t27 10.5h10q47 0 37 -47l-29 -138q165 -28 254 -120q65 -65 65 -127q0 -23 -15 -38 t-30 -18.5t-18 0.5q-30 79 -101 137t-180 76l-114 -540q64 -26 105 -45.5t90.5 -51.5t77.5 -65.5t47.5 -83.5t19.5 -109q0 -169 -119.5 -270.5t-333.5 -101.5h-39l-31 -136q-8 -47 -40 -47h-13q-45 0 -37 47l33 146q-180 32 -283 135q-75 75 -75 143zM317 997q0 -95 60 -152 t178 -108l111 518q-8 2 -23 2q-144 0 -235 -74t-91 -186zM489 70q5 0 16.5 -1t16.5 -1q167 0 255.5 73.5t88.5 200.5q0 50 -19 90t-58 71.5t-81.5 54t-105.5 48.5z" />
<glyph unicode="%" horiz-adv-x="1449" d="M51 27q0 20 11 36t44 48l1330 1237q2 2 11.5 -1.5t18.5 -14.5t9 -25q0 -19 -11.5 -35.5t-44.5 -46.5l-1327 -1239q-3 -2 -13 1.5t-19 14.5t-9 25zM182 1001q0 139 94.5 240.5t233.5 101.5q112 0 188 -74t76 -194q0 -139 -94.5 -240.5t-233.5 -101.5q-111 0 -187.5 74 t-76.5 194zM266 1006q0 -84 55 -139.5t132 -55.5q100 0 168.5 77t68.5 183q0 85 -54 140t-132 55q-100 0 -169 -77t-69 -183zM752 258q0 139 94 240.5t233 101.5q112 0 188 -74t76 -194q0 -139 -93.5 -240.5t-231.5 -101.5q-111 0 -188.5 74t-77.5 194zM836 262 q0 -85 55 -139.5t133 -54.5q99 0 168.5 77.5t69.5 182.5q0 84 -55.5 139t-133.5 55q-99 0 -168 -77.5t-69 -182.5z" />
<glyph unicode="&#x26;" horiz-adv-x="1312" d="M39 338q0 168 113 276t329 154q-75 113 -75 231q0 143 98.5 245t249.5 102q128 0 206 -75.5t78 -195.5q0 -147 -103 -232.5t-304 -121.5l319 -420q88 101 138.5 217t58.5 213q3 3 19.5 4t35.5 -15.5t19 -43.5q0 -80 -57 -205t-160 -238l26 -38q57 -75 102 -102.5 t105 -24.5q2 0 3 -8t-1 -19t-8 -22t-22 -19t-40 -8q-60 0 -102 30.5t-102 106.5l-29 39q-208 -188 -479 -188q-194 0 -306 99.5t-112 258.5zM137 342q0 -119 86 -196.5t246 -77.5q237 0 416 165l-357 467q-194 -36 -292.5 -128t-98.5 -230zM504 1006q0 -108 76 -218 q94 16 157.5 36.5t112.5 53t71.5 80.5t22.5 115q0 84 -55 136.5t-146 52.5q-106 0 -172.5 -77t-66.5 -179z" />
<glyph unicode="'" horiz-adv-x="401" d="M252 883l51 372q4 41 19.5 58.5t44.5 17.5q43 0 58 -19.5t1 -68.5l-107 -366q-12 -39 -32 -39q-24 0 -31 9t-4 36z" />
<glyph unicode="(" horiz-adv-x="718" d="M96 246q0 252 95.5 491.5t259.5 403.5q92 92 191.5 142t178.5 50q32 0 44 -18.5t6.5 -39t-17.5 -26.5q-175 -9 -338 -172q-148 -148 -232.5 -374t-84.5 -457q0 -316 166 -482q69 -69 151 -92q6 -3 9 -21t-8.5 -36t-41.5 -18q-38 0 -88 26t-100 76q-88 88 -139.5 229 t-51.5 318z" />
<glyph unicode=")" horiz-adv-x="718" d="M-157 -343.5q6 20.5 18 26.5q175 9 338 172q147 148 231 374t84 457q0 312 -164 481q-70 70 -151 93q-6 3 -9.5 21t8 36t42.5 18q83 0 188 -102q87 -88 137.5 -229t50.5 -318q0 -252 -95 -491.5t-259 -403.5q-93 -93 -191.5 -142.5t-179.5 -49.5q-31 0 -42.5 18.5 t-5.5 39z" />
<glyph unicode="*" horiz-adv-x="772" d="M197 1106q0 51 51 39l223 -66l6 232q1 24 9 31.5t26 7.5t26 -7.5t9 -31.5l6 -232l223 66q51 12 51 -39q0 -18 -28 -29l-217 -80l131 -190q20 -33 4 -45q-33 -33 -62 4l-143 182l-143 -182q-29 -37 -62 -4q-16 12 4 45l131 190l-219 80q-26 10 -26 29z" />
<glyph unicode="+" horiz-adv-x="1064" d="M100 526q0 31 19 54q13 10 37 10h354l76 352q8 47 43 47h12q18 0 29.5 -11t7.5 -36l-76 -352h346q45 0 45 -25q0 -11 -5.5 -28.5t-12.5 -24.5q-10 -10 -39 -10h-352l-80 -377q-11 -47 -45 -47h-13q-18 0 -28.5 11t-5.5 36l80 377h-347q-45 0 -45 24z" />
<glyph unicode="," horiz-adv-x="391" d="M-117 -236l213 396q5 1 25.5 1t43.5 -14.5t23 -37.5q0 -35 -28 -70l-232 -305q-8 -9 -30 4.5t-15 25.5z" />
<glyph unicode="-" horiz-adv-x="718" d="M90 489q0 11 6 28t13 24q10 12 38 12h447q47 0 47 -25q0 -35 -18 -53q-15 -12 -39 -12h-449q-45 0 -45 26z" />
<glyph unicode="." horiz-adv-x="391" d="M29 45q0 58 24 82q20 20 60 20q35 0 55 -14t20 -39q0 -58 -26 -84q-20 -20 -58 -20q-75 0 -75 55z" />
<glyph unicode="/" horiz-adv-x="649" d="M-150 -346q0 24 25 76l830 1599q4 0 20 1t34.5 -12t18.5 -36q0 -30 -24 -76l-832 -1599q-4 0 -20 -1t-34 12t-18 36z" />
<glyph unicode="0" horiz-adv-x="1273" d="M135 459q0 123 51 334q130 553 557 553q151 0 259 -63.5t160 -170.5t52 -246q0 -125 -51 -336q-29 -123 -73 -219t-109.5 -172.5t-160.5 -117.5t-214 -41q-226 0 -348.5 132t-122.5 347zM238 459q0 -174 98.5 -282.5t271.5 -108.5q99 0 177 36t131.5 103t89 148.5 t59.5 187.5q47 198 47 323q0 174 -99 282.5t-270 108.5q-99 0 -177.5 -36t-132 -103.5t-89 -149.5t-59.5 -188q-47 -198 -47 -321z" />
<glyph unicode="1" horiz-adv-x="688" d="M186 1153q0 19 11 36.5t21.5 25.5t12.5 6q32 -52 121 -52q66 0 121 47t70 115h43q22 0 31.5 -14.5t3.5 -40.5l-263 -1235q-6 -26 -17 -36.5t-34 -10.5h-12q-49 0 -37 53l231 1096q-29 -35 -73.5 -55.5t-89.5 -20.5q-84 0 -119 35q-21 21 -21 51z" />
<glyph unicode="2" horiz-adv-x="1046" d="M14 72q0 205 138 346q70 70 165 119t230 98q95 35 153.5 60t113 57t83 66t45 78.5t16.5 102.5q0 111 -86 182.5t-233 71.5q-135 0 -225 -67t-138 -180q-4 -1 -17.5 0t-28.5 16.5t-15 40.5t22.5 67.5t59.5 79.5q136 136 346 136q194 0 307 -94.5t113 -252.5 q0 -76 -22 -136t-58 -103.5t-101 -84t-131.5 -69.5t-170.5 -67q-132 -47 -214 -89.5t-143 -103.5q-97 -97 -106 -252h725q57 0 57 -39q0 -10 -5 -24t-10.5 -22.5t-6.5 -8.5h-820q-8 0 -25.5 24.5t-17.5 47.5z" />
<glyph unicode="3" horiz-adv-x="1062" d="M-6 281q0 30 17 48.5t34 22.5t18 0q20 -126 123 -205t273 -79q178 0 277.5 94.5t99.5 232.5q0 119 -83 189.5t-239 70.5h-117q-45 0 -45 25q0 11 6 28.5t13 24.5q10 10 39 10h127q108 0 186.5 22t122.5 62t64 88.5t20 108.5q0 101 -86.5 168.5t-218.5 67.5 q-115 0 -213.5 -54t-153.5 -137q-2 -3 -14.5 2t-24.5 18t-12 29q0 39 49 88q64 64 160 102t215 38q177 0 292 -85t115 -233q0 -133 -84 -226t-241 -114q111 -30 170 -107.5t59 -183.5q0 -79 -29 -151.5t-86.5 -133t-154 -96.5t-219.5 -36q-119 0 -216.5 36t-154.5 93 q-45 45 -66.5 92t-21.5 80z" />
<glyph unicode="4" horiz-adv-x="1146" d="M53 467q0 26 11.5 47t41.5 51l728 711q35 34 57.5 46.5t52.5 12.5q47 0 62.5 -19.5t9.5 -55.5l-166 -773h194q48 0 48 -24q0 -13 -6 -30.5t-15 -26.5q-7 -11 -41 -11h-199l-75 -354q-4 -28 -15.5 -37.5t-35.5 -9.5h-11q-46 0 -35 53l72 348h-643q-3 0 -11.5 10.5t-16 28 t-7.5 33.5zM154 487h600l159 750z" />
<glyph unicode="5" horiz-adv-x="1067" d="M29 258q0 22 15.5 37.5t31 20.5t18.5 1q30 -110 133 -178.5t256 -68.5q179 0 285 107.5t106 266.5q0 131 -88 204t-256 73t-299 -72q-63 13 -51 80l111 531q16 65 78 65h716q3 0 8 -11t5 -22q0 -23 -17.5 -41t-50.5 -18h-645l-98 -473q113 51 252 51q204 0 323 -98.5 t119 -261.5q0 -98 -34 -183.5t-97 -149.5t-158.5 -101t-212.5 -37q-123 0 -220.5 36.5t-158.5 98.5q-71 74 -71 143z" />
<glyph unicode="6" horiz-adv-x="1169" d="M135 444q0 70 14 164.5t42.5 203.5t82 215t121.5 173q74 74 158.5 110t210.5 36q196 0 309 -113q31 -31 47.5 -64.5t16.5 -56.5q0 -22 -14.5 -38t-29 -21.5t-18.5 -1.5q-36 96 -120.5 152.5t-200.5 56.5q-382 0 -496 -598q64 66 162 107.5t223 41.5q191 0 306.5 -96 t115.5 -262q0 -126 -61 -234t-172 -173.5t-248 -65.5q-202 0 -325.5 119.5t-123.5 344.5zM231 442q0 -180 94 -276t261 -96q169 0 276 107.5t107 268.5q0 127 -88.5 204t-249.5 77q-239 0 -391 -170q-9 -62 -9 -115z" />
<glyph unicode="7" horiz-adv-x="1024" d="M159.5 64.5q7.5 26.5 33.5 60.5l817 1100h-740q-61 0 -61 41q0 11 5 25.5t11 24t8 9.5h832q4 0 15 -9.5t22.5 -30t11.5 -42.5q0 -32 -37 -82l-858 -1167q-2 -1 -13.5 1t-24.5 9.5t-21 20.5t-0.5 39.5z" />
<glyph unicode="8" horiz-adv-x="1159" d="M61 324q0 140 107.5 244t284.5 132q-102 31 -156.5 98t-54.5 163q0 161 133 273t340 112q186 0 305 -88.5t119 -231.5q0 -128 -90.5 -225t-255.5 -125q123 -35 186 -114.5t63 -180.5q0 -173 -138.5 -287t-364.5 -114q-217 0 -347.5 94.5t-130.5 249.5zM164 330 q0 -119 102 -191.5t277 -72.5q181 0 289 87.5t108 227.5q0 116 -101.5 189t-281.5 73q-171 0 -282 -88t-111 -225zM344 965q0 -107 90 -170.5t242 -63.5q167 0 263.5 79t96.5 208q0 110 -92 176t-244 66q-162 0 -259 -82.5t-97 -212.5z" />
<glyph unicode="9" horiz-adv-x="1169" d="M100 217q0 22 14.5 38t29 21t16.5 0q38 -96 123 -152t204 -56q379 0 496 575q-64 -65 -163.5 -107t-219.5 -42q-197 0 -309.5 96t-112.5 272q0 131 61.5 241.5t173.5 176.5t249 66q202 0 325 -120t123 -345q0 -70 -14 -163.5t-43 -202t-82.5 -214.5t-122.5 -174 q-73 -75 -159 -111t-214 -36q-197 0 -311 114q-32 31 -48 65t-16 58zM274 868q0 -137 85.5 -213.5t252.5 -76.5q122 0 222 47.5t167 122.5q13 80 13 135q0 180 -94 276t-261 96q-170 0 -277.5 -109t-107.5 -278z" />
<glyph unicode=":" horiz-adv-x="440" d="M53 45q0 57 25 82q20 20 59 20q35 0 55.5 -14t20.5 -39q0 -57 -27 -84q-20 -20 -57 -20q-76 0 -76 55zM207 770q0 55 24 82q20 20 60 20q35 0 55.5 -14.5t20.5 -40.5q0 -58 -27 -82q-20 -20 -57 -20q-76 0 -76 55z" />
<glyph unicode=";" horiz-adv-x="440" d="M-92 -236l213 396q5 1 25.5 1t43.5 -14.5t23 -37.5q0 -33 -29 -70l-231 -305q-8 -9 -30 4.5t-15 25.5zM207 770q0 55 24 82q20 20 60 20q35 0 55.5 -14.5t20.5 -40.5q0 -58 -27 -82q-20 -20 -57 -20q-76 0 -76 55z" />
<glyph unicode="&#x3c;" horiz-adv-x="944" d="M104 537q0 55 64 86l727 358q3 1 10.5 -4.5t14.5 -17.5t8 -25q0 -37 -68 -72l-655 -321l504 -312q63 -34 59 -75q-2 -14 -10 -26t-16 -16.5t-11 -2.5l-590 366q-37 19 -37 62z" />
<glyph unicode="=" horiz-adv-x="1064" d="M70 287q0 12 6.5 30t13.5 27q13 10 39 10h743q48 0 48 -24q0 -13 -6.5 -32t-14.5 -26q-8 -10 -39 -10h-743q-47 0 -47 25zM168 739q0 13 5.5 32t12.5 26q10 10 41 10h742q49 0 49 -25q0 -12 -7 -30t-14 -25q-12 -12 -39 -12h-743q-47 0 -47 24z" />
<glyph unicode="&#x3e;" horiz-adv-x="944" d="M43 158q-3 36 66 71l657 322l-506 309q-64 38 -57 76q2 15 9.5 27t15.5 16.5t12 1.5l589 -365q37 -24 37 -61q0 -28 -14 -48t-51 -38l-727 -360q-2 -1 -9.5 5t-14.5 18.5t-7 25.5z" />
<glyph unicode="?" horiz-adv-x="937" d="M188 1055q0 30 25 76.5t66 87.5q127 127 321 127q176 0 282.5 -97t106.5 -248q0 -263 -315 -366q-136 -47 -188 -83.5t-52 -82.5q0 -64 68 -76q3 0 0 -11.5t-16 -22.5t-35 -11q-49 0 -81 33t-32 84q0 81 61.5 134t212.5 106q141 50 209 116t68 180q0 111 -80 182.5 t-215 71.5q-123 0 -211.5 -71t-128.5 -185q-2 -4 -18 0.5t-32 19.5t-16 36zM252 45q0 58 27 82q20 20 57 20t57.5 -14t20.5 -39q0 -54 -27 -84q-20 -20 -57 -20t-57.5 14.5t-20.5 40.5z" />
<glyph unicode="@" horiz-adv-x="1505" d="M131 571q0 156 56.5 298t154 247t235.5 167.5t293 62.5q274 0 439.5 -149t165.5 -396q0 -199 -92.5 -329t-241.5 -130q-90 0 -139.5 40t-57.5 105q-79 -137 -242 -137q-104 0 -171.5 67t-67.5 181q0 91 40.5 174.5t115.5 137t165 53.5q78 0 135 -40t78 -102l11 47 q8 48 27 66t48 18q8 0 16.5 -2.5t13.5 -5.5t5 -4l-76 -352q-17 -82 11 -126t100 -44q108 0 176 109.5t68 269.5q0 212 -148.5 344.5t-386.5 132.5q-180 0 -331 -95.5t-236.5 -255t-85.5 -345.5q0 -230 154 -377.5t407 -147.5q95 0 188.5 28.5t155.5 70.5q27 20 53 20 q18 0 32.5 -22t8.5 -29q-72 -59 -192 -100t-252 -41q-290 0 -461.5 163.5t-171.5 427.5zM559 608q0 -81 48 -130.5t120 -49.5q84 0 148.5 59.5t80.5 141.5l21 96q-13 71 -60.5 114.5t-125.5 43.5q-100 0 -166 -83.5t-66 -191.5z" />
<glyph unicode="A" horiz-adv-x="1222" d="M-38 38q-1 4 0 7q0 12 5 28q7 22 23 50l706 1177q20 35 66 35q36 0 51.5 -11t20.5 -36l260 -1261q1 -10 -23.5 -23.5t-53 -4.5t-36.5 54l-80 406h-596l-272 -463q-1 -1 -8 -1t-16.5 1.5t-19.5 5.5t-17.5 13t-9.5 23zM356 549h527l-131 670z" />
<glyph unicode="B" horiz-adv-x="1214" d="M72 72l252 1190q9 63 75 63h367q197 0 297 -75t100 -218q0 -132 -73.5 -224.5t-233.5 -127.5q124 -26 191 -96.5t67 -177.5q0 -70 -16.5 -129t-54.5 -111t-96.5 -88t-147 -57t-201.5 -21h-469q-34 0 -49 19q-10 13 -10 33q0 9 2 20zM178 92h426q214 0 310 79t96 235 q0 110 -84 168.5t-240 58.5h-393zM311 723h383q176 0 270.5 82.5t94.5 214.5q-6 213 -303 213h-334z" />
<glyph unicode="C" horiz-adv-x="1325" d="M127 545q0 214 94 397.5t262 293.5t371 110q247 0 387 -144q42 -42 65 -91.5t23 -84.5q0 -24 -15 -40.5t-30 -18.5t-22 0q-38 137 -143 213.5t-271 76.5q-129 0 -243.5 -56.5t-195 -151t-127 -222.5t-46.5 -268q0 -224 131 -356.5t346 -132.5q154 0 265 67t173 170 q2 4 14.5 -0.5t24.5 -19t12 -33.5q0 -48 -74 -125q-66 -64 -176.5 -106.5t-238.5 -42.5q-267 0 -426.5 152.5t-159.5 412.5z" />
<glyph unicode="D" horiz-adv-x="1327" d="M74 72l252 1190q9 63 73 63h219q310 0 482 -146t172 -409q0 -163 -52.5 -302t-153 -244t-257.5 -164.5t-354 -59.5h-324q-35 0 -50 19q-10 12 -10 31q0 10 3 22zM180 90h291q170 0 304 52t218 143.5t127 211t43 259.5q0 226 -144 352.5t-407 126.5h-190z" />
<glyph unicode="E" horiz-adv-x="1148" d="M74 72l252 1190q13 63 73 63h781q47 0 47 -25q0 -12 -6.5 -30t-14.5 -27q-8 -10 -41 -10h-743l-109 -508h572q47 0 47 -25q0 -32 -19 -55q-13 -10 -39 -10h-579l-115 -541h760q49 0 49 -26q0 -12 -6.5 -30.5t-13.5 -27.5q-10 -10 -41 -10h-797q-35 0 -50 19 q-10 12 -10 31q0 10 3 22z" />
<glyph unicode="F" horiz-adv-x="1071" d="M68 47l258 1215q9 63 73 63h748q47 0 47 -25q0 -35 -18 -57q-8 -10 -41 -10h-713l-117 -551h526q48 0 48 -25q0 -13 -6 -30.5t-13 -24.5q-10 -10 -41 -10h-534l-117 -551q-5 -25 -16.5 -36t-34.5 -11h-13q-38 0 -38 34q0 8 2 19z" />
<glyph unicode="G" horiz-adv-x="1415" d="M129 549q0 154 54.5 299t149 255t232.5 176.5t295 66.5q136 0 238 -42t166 -106q36 -36 56.5 -78.5t20.5 -72.5t-16.5 -46.5t-33 -19.5t-19.5 0q-31 117 -142.5 195.5t-271.5 78.5q-134 0 -251.5 -57t-198 -152.5t-126.5 -222.5t-46 -264q0 -233 130.5 -362t344.5 -129 q210 0 338 131q69 67 104 142.5t64 217.5h-408q-47 0 -47 25q0 35 18 57q10 10 41 10h434q35 0 50.5 -18.5t7.5 -52.5l-123 -580q0 -6 -23 -6q-30 0 -44 26q-8 14 -8 37q0 19 5 43l21 101q-57 -99 -170 -160t-262 -61q-259 0 -419.5 151.5t-160.5 417.5z" />
<glyph unicode="H" horiz-adv-x="1351" d="M68 47l262 1237q11 47 51 47h14q39 0 39 -36q0 -8 -2 -17l-121 -567h826l120 573q11 47 52 47h14q23 0 33 -14q6 -9 6 -22q0 -8 -2 -17l-264 -1237q-5 -25 -17 -36t-35 -11h-12q-39 0 -39 34q0 8 2 19l121 569h-825l-123 -575q-5 -25 -16.5 -36t-34.5 -11h-13 q-38 0 -38 34q0 8 2 19z" />
<glyph unicode="I" horiz-adv-x="464" d="M90 47l262 1237q6 26 17 36.5t34 10.5h15q22 0 32 -14q6 -9 6 -21q0 -8 -3 -18l-263 -1237q-6 -26 -17 -36.5t-34 -10.5h-14q-37 0 -37 34q0 8 2 19z" />
<glyph unicode="J" horiz-adv-x="892" d="M4 178q0 22 14 38.5t28.5 22t16.5 1.5q30 -76 101 -124t162 -48q125 0 197.5 77.5t109.5 247.5l188 891q11 47 51 47h15q23 0 33 -14q6 -9 6 -22q0 -8 -2 -17l-193 -899q-43 -203 -141.5 -301t-263.5 -98t-267 98q-55 58 -55 100z" />
<glyph unicode="K" horiz-adv-x="1075" d="M68 47l262 1237q6 26 17.5 36.5t33.5 10.5h14q38 0 38 -37q0 -7 -1 -16l-123 -578l801 643h1q4 0 12 -3q10 -4 20 -14q11 -10 15 -24q1 -4 1 -8q0 -12 -10 -28q-13 -22 -45 -49l-688 -543l372 -477q64 -85 100 -110.5t89 -27.5q2 0 2.5 -7t-2.5 -16.5t-10.5 -19.5 t-25 -17t-42.5 -7q-46 0 -89 35.5t-116 132.5l-391 514l-135 -633q-4 -26 -16 -36.5t-35 -10.5h-13q-38 0 -38 34q0 8 2 19z" />
<glyph unicode="L" horiz-adv-x="995" d="M74 72l256 1212q11 47 51 47h14q39 0 39 -36q0 -8 -2 -17l-252 -1184h643q47 0 47 -26q0 -38 -20 -58q-8 -10 -41 -10h-678q-35 0 -50 19q-10 12 -10 31q0 10 3 22z" />
<glyph unicode="M" horiz-adv-x="1505" d="M68 47l258 1215q16 69 84 69h22q36 0 50 -17t24 -59l233 -802l580 802q32 47 51 61.5t53 14.5h27q33 0 48 -21q11 -15 11 -34q0 -8 -2 -16l-258 -1219q-6 -25 -17 -36t-32 -11h-12q-37 0 -37 34q0 9 2 19l248 1167l-578 -796q-24 -33 -45 -46.5t-51 -13.5 q-38 0 -54.5 17.5t-25.5 54.5l-233 784l-250 -1173q-6 -26 -16.5 -36.5t-32.5 -10.5h-13q-36 0 -36 33q0 9 2 20z" />
<glyph unicode="N" horiz-adv-x="1372" d="M68 47l258 1215q16 69 71 69h27q24 0 37.5 -13t27.5 -44l555 -1124l240 1134q6 26 17.5 36.5t33.5 10.5h11q36 0 36 -33q0 -9 -2 -20l-260 -1223q-13 -61 -65 -61h-13q-21 0 -35 13t-30 44l-567 1153l-246 -1163q-11 -47 -51 -47h-11q-36 0 -36 33q0 9 2 20z" />
<glyph unicode="O" horiz-adv-x="1488" d="M127 551q0 167 61 317t162 253.5t233.5 164t272.5 60.5q264 0 423 -161t159 -411q0 -167 -61 -316t-162 -253t-233.5 -164.5t-272.5 -60.5q-174 0 -307.5 75t-204 204t-70.5 292zM236 559q0 -138 56 -248t167 -175.5t260 -65.5q116 0 227 53t195.5 144t136 222.5 t51.5 278.5q0 212 -129.5 350.5t-353.5 138.5q-116 0 -226.5 -53.5t-195.5 -144.5t-136.5 -222.5t-51.5 -277.5z" />
<glyph unicode="P" horiz-adv-x="1114" d="M68 47l258 1215q9 63 73 63h297q216 0 329.5 -88.5t113.5 -253.5q0 -218 -136.5 -338.5t-390.5 -120.5h-342l-102 -483q-5 -25 -16.5 -36t-34.5 -11h-13q-38 0 -38 34q0 8 2 19zM291 614h340q191 0 297 95t106 262q0 129 -86 195.5t-258 66.5h-268z" />
<glyph unicode="Q" horiz-adv-x="1507" d="M127 551q0 135 39.5 258.5t109.5 220t161.5 168.5t201 110t223.5 38q265 0 420.5 -159t155.5 -413q0 -180 -69.5 -336t-190.5 -264l18 -22q43 -55 74.5 -76.5t68.5 -24.5q1 0 2.5 -7t-0.5 -17t-7.5 -20.5t-18.5 -17.5t-33 -7q-43 0 -75 22.5t-81 87.5l-18 23 q-187 -135 -406 -135q-176 0 -307.5 74t-199.5 203t-68 294zM236 559q0 -211 129 -350t348 -139q184 0 338 114l-238 305q-14 22 -14 41q0 11 16 27t29 16q25 0 43 -24l231 -301q98 95 154.5 229.5t56.5 290.5q0 138 -55.5 248t-165 175.5t-256.5 65.5q-118 0 -230 -53.5 t-197 -144.5t-137 -222.5t-52 -277.5z" />
<glyph unicode="R" horiz-adv-x="1144" d="M68 47l258 1215q9 63 73 63h316q214 0 326 -82t112 -239q0 -204 -132.5 -320.5t-369.5 -116.5h-73l243 -368q52 -80 88 -107q35 -26 93 -26h4t2.5 -7.5t-2 -18.5t-10 -22t-25 -18.5t-43.5 -7.5q-51 0 -93.5 34t-103.5 134l-268 407h-182l-113 -526q-5 -25 -16.5 -36 t-34.5 -11h-13q-38 0 -38 34q0 8 2 19zM299 655h354q191 0 292.5 91t101.5 247q0 117 -86.5 179.5t-251.5 62.5h-287z" />
<glyph unicode="S" horiz-adv-x="1085" d="M31 268q0 23 15.5 38t31 18.5t16.5 -0.5q40 -114 148 -185t280 -71q167 0 255.5 73.5t88.5 200.5q0 44 -17 81.5t-41 63t-69.5 52.5t-82 43.5t-99.5 42.5q-44 18 -74 31t-69.5 34t-65.5 40t-54 47t-44 57.5t-26.5 68.5t-10.5 84q0 154 120 256.5t316 102.5 q127 0 221.5 -36t151.5 -93q65 -65 65 -127q0 -23 -15 -38t-30 -18.5t-18 0.5q-36 98 -132.5 160.5t-246.5 62.5q-146 0 -237 -74t-91 -186q0 -47 16.5 -85.5t40 -64.5t69.5 -53.5t83 -44t103 -42.5q46 -19 74 -31.5t68 -34t64.5 -39.5t53 -45.5t44 -56.5t26 -67t10.5 -81 q0 -169 -119.5 -270.5t-333.5 -101.5q-134 0 -240 40.5t-170 104.5q-75 75 -75 143z" />
<glyph unicode="T" horiz-adv-x="1165" d="M213 1257q0 38 20 58q7 10 41 10h969q47 0 47 -25q0 -37 -20 -57q-6 -10 -41 -10h-434l-254 -1192q-6 -25 -17.5 -36t-34.5 -11h-14q-37 0 -37 34q0 9 2 19l252 1186h-432q-47 0 -47 24z" />
<glyph unicode="U" horiz-adv-x="1292" d="M160 573l151 711q6 26 17 36.5t34 10.5h15q22 0 31.5 -14t3.5 -39l-150 -713q-19 -88 -19 -160q0 -129 62 -209q96 -124 320 -124q341 0 428 417l170 795q5 25 16.5 36t34.5 11h12q22 0 32 -14q6 -9 5 -21q0 -8 -2 -18l-170 -809q-106 -489 -533 -489q-270 0 -394 150 q-84 101 -84 266q0 81 20 177z" />
<glyph unicode="V" horiz-adv-x="1210" d="M207 1300q-1 10 22.5 23t51.5 4t36 -53l224 -1165l708 1222q1 1 8 1t16.5 -1.5t19.5 -5.5t17 -13q8 -8 10 -22q1 -4 0 -8q0 -12 -5 -28q-7 -22 -23 -50l-696 -1177q-12 -21 -26 -29t-40 -8q-36 0 -51 11.5t-20 35.5z" />
<glyph unicode="W" horiz-adv-x="1794" d="M281 1311q1 11 27 19q10 3 19 4l31 -9q26 -14 27 -57l33 -1176l522 975q14 30 31.5 41.5t52.5 11.5q40 0 57.5 -17.5t20.5 -51.5l115 -957l548 1233q1 1 13 3q3 0 6 1q8 1 20 -3q14 -4 26 -16q10 -10 10 -33v-6q-2 -28 -21 -69l-535 -1177q-18 -37 -78 -37 q-40 0 -56.5 12.5t-19.5 38.5l-116 977l-537 -989q-12 -23 -27 -31t-47 -8q-41 0 -57 12.5t-16 38.5z" />
<glyph unicode="X" horiz-adv-x="1148" d="M-50 26q-1 3 -1 8q0 24 39 68l561 572l-297 616q0 9 8 19t21 16t27 7t29 -9.5t25 -32.5l263 -559l581 612q3 0 13 -3t23.5 -11.5t20.5 -20.5q3 -6 3 -14q0 -10 -4 -22q-8 -22 -35 -49l-555 -570l211 -438q43 -94 79 -122q32 -25 85 -25h12q2 0 2.5 -9t-2 -21.5t-9.5 -25 t-24.5 -21.5t-42.5 -9q-56 0 -98 42.5t-88 145.5l-203 426l-586 -614q-1 -1 -3 -1q-7 0 -25 9q-24 12 -30 36z" />
<glyph unicode="Y" d="M186 1292q-3 9 17.5 25t48.5 13.5t45 -40.5l289 -631l575 674q2 2 5 2q8 0 25 -7q25 -11 32 -38q1 -3 1 -7q0 -28 -44 -83l-559 -639l-111 -520q-6 -26 -18.5 -36.5t-36.5 -10.5h-15q-22 0 -30 10.5t-2 36.5l112 530z" />
<glyph unicode="Z" horiz-adv-x="1140" d="M-8 72q0 26 11.5 47t41.5 51l1040 1063h-782q-59 0 -59 37q0 10 5 24t10.5 22.5t6.5 8.5h897q7 0 21 -24t14 -48q0 -26 -11.5 -47t-41.5 -51l-1041 -1061h826q59 0 59 -39q0 -11 -5 -24.5t-11 -22t-8 -8.5h-938q-3 0 -11.5 10.5t-16 28t-7.5 33.5z" />
<glyph unicode="[" horiz-adv-x="659" d="M2 -324l336 1586q13 63 74 63h321q45 0 45 -25q0 -11 -5.5 -28.5t-12.5 -24.5q-10 -10 -39 -10h-295l-328 -1542h289q45 0 45 -25q0 -33 -18 -55q-13 -10 -39 -10h-316q-35 0 -50 18.5t-7 52.5z" />
<glyph unicode="\" horiz-adv-x="649" d="M207 1225q-6 60 8 83t43 23q11 0 20 -2t14 -5t5 -5l152 -1610q4 -60 -9.5 -83t-42.5 -23q-10 0 -20 2.5t-14.5 5.5t-4.5 4z" />
<glyph unicode="]" horiz-adv-x="659" d="M-127 -371q0 12 5.5 29.5t12.5 24.5q12 12 39 12h295l328 1542h-289q-45 0 -45 23q0 12 6 30t13 25q10 10 38 10h316q35 0 50 -19t7 -53l-336 -1585q-14 -63 -73 -63h-322q-45 0 -45 24z" />
<glyph unicode="^" horiz-adv-x="1024" d="M369 948q0 24 30 60.5t105 105.5q43 39 83.5 71.5t61.5 47t25 14.5q3 0 17.5 -14t40.5 -46t50 -69q78 -118 78 -153q0 -20 -10.5 -31.5t-26.5 -11.5q-20 0 -40 25.5t-62 103.5l-59 108q-92 -98 -107 -112q-119 -125 -158 -125q-13 0 -20.5 7.5t-7.5 18.5z" />
<glyph unicode="_" horiz-adv-x="925" d="M-88 -68q0 12 5.5 30.5t12.5 25.5q10 12 41 12h770q47 0 47 -27q0 -33 -18 -55q-12 -12 -41 -12h-770q-47 0 -47 26z" />
<glyph unicode="`" horiz-adv-x="1024" d="M453 1362q0 18 14 30.5t33 12.5q31 0 88 -51q59 -50 109.5 -106.5t73 -90.5t17.5 -37q-6 -6 -43.5 8t-101.5 51.5t-125 88.5q-38 32 -51.5 52.5t-13.5 41.5z" />
<glyph unicode="a" horiz-adv-x="1159" d="M76 395q0 150 68 285.5t193.5 222t276.5 86.5q136 0 233.5 -68t133.5 -171l25 122q12 57 30 80t49 23q9 0 18 -2.5t14 -5.5t5 -4l-164 -768q-7 -32 -7 -56q0 -35 14 -53q23 -30 84 -31q1 0 1.5 -7t-2 -17t-9 -20.5t-21.5 -17.5t-37 -7q-75 0 -109 54q-21 33 -21 82 q0 32 9 71v4q-67 -105 -167.5 -161t-215.5 -56q-172 0 -286.5 111t-114.5 304zM176 397q0 -152 88.5 -241.5t224.5 -89.5q153 0 273.5 110t150.5 258l39 176q-28 133 -115 213t-229 80q-95 0 -177.5 -44t-137.5 -116t-86 -162.5t-31 -183.5z" />
<glyph unicode="b" horiz-adv-x="1155" d="M39 6l285 1335q5 26 15 37t30 11h12q42 0 42 -35q0 -6 -1 -13l-121 -559q142 207 373 207q83 0 156 -28.5t129.5 -81.5t89 -135.5t32.5 -184.5q0 -148 -65.5 -280t-187 -215.5t-269.5 -83.5q-140 0 -237.5 71t-137.5 178l-28 -135q-12 -55 -30 -77.5t-48 -22.5 q-9 0 -18.5 2.5t-15 5.5t-5.5 4zM229 446q-6 -30 -6 -60q0 -39 10 -77q17 -68 59 -122t113.5 -87.5t159.5 -33.5q94 0 174 42t132.5 111.5t82 158t29.5 181.5q0 157 -92 250.5t-227 93.5q-127 0 -231 -74t-161 -190z" />
<glyph unicode="c" horiz-adv-x="1048" d="M74 422q0 147 70 277t195 210t273 80q208 0 324 -121q40 -40 61 -87t21 -83q0 -27 -18.5 -43.5t-34.5 -16t-21 2.5q-16 112 -102.5 186t-227.5 74q-125 0 -227.5 -68.5t-157.5 -176.5t-55 -228q0 -164 99 -262t259 -98q113 0 195 53.5t133 138.5q3 3 16.5 -1t27.5 -16 t14 -30q0 -40 -64 -107q-56 -54 -141 -90t-189 -36q-127 0 -227.5 50.5t-161.5 152.5t-61 239z" />
<glyph unicode="d" horiz-adv-x="1159" d="M82 410q0 148 65.5 280t187 215.5t269.5 83.5q137 0 235 -67t140 -174l127 593q6 26 16 37t29 11h12q43 0 43 -34q0 -7 -2 -14l-246 -1146q-8 -34 -8 -59q0 -34 14 -50q24 -30 85 -31q1 0 1.5 -7t-2 -17t-9 -20.5t-21.5 -17.5t-37 -7q-72 0 -107 51q-23 33 -23 85 q0 28 7 62q-140 -204 -371 -204q-110 0 -201 49t-147.5 148.5t-56.5 232.5zM180 410q0 -156 92.5 -250t227.5 -94q150 0 264.5 103.5t144.5 250.5l41 186q-29 134 -120 215.5t-232 81.5q-94 0 -174 -42t-132.5 -111.5t-82 -158t-29.5 -181.5z" />
<glyph unicode="e" horiz-adv-x="1034" d="M70 371q0 126 46.5 240.5t126.5 197t192.5 131.5t238.5 49q156 0 246 -68.5t90 -175.5q0 -69 -26.5 -120t-89 -91.5t-175 -61.5t-273.5 -21q-124 0 -270 14q-6 -43 -6 -88q0 -142 82.5 -228t236.5 -86q220 0 340 168q2 2 14.5 -2.5t25 -16.5t12.5 -28q0 -36 -47 -80 q-121 -124 -349 -124q-197 0 -306 108t-109 283zM197 549q130 -17 241 -17q471 0 471 211q0 65 -63 112.5t-178 47.5q-162 0 -290.5 -99.5t-180.5 -254.5z" />
<glyph unicode="f" horiz-adv-x="636" d="M113 905q0 11 5.5 28.5t12.5 24.5q11 11 37 11h164l26 118q66 316 301 316q109 0 164 -55q25 -25 25 -56q0 -16 -9 -29t-18.5 -18.5t-11.5 -3.5q-19 33 -62 54.5t-94 21.5q-83 0 -130 -63t-72 -181l-25 -104h215q45 0 45 -25q0 -11 -5.5 -27.5t-12.5 -23.5 q-12 -12 -39 -12h-219l-177 -840q-6 -26 -17 -36.5t-30 -10.5h-10q-43 0 -43 33q0 6 2 14l178 840h-155q-45 0 -45 24z" />
<glyph unicode="g" horiz-adv-x="1155" d="M4 -147q0 30 17.5 48t34.5 21.5t20 -0.5q27 -106 115 -170.5t243 -64.5q316 0 389 350l39 178q-138 -205 -377 -205q-80 0 -153 28.5t-129.5 80.5t-90.5 133.5t-34 181.5q0 141 68.5 268.5t192 207t271.5 79.5q137 0 235 -68.5t132 -170.5l27 122q10 55 28 79t49 24 q9 0 18.5 -2.5t15 -5.5t5.5 -4l-198 -934q-45 -210 -169 -321t-327 -111q-230 0 -352 122q-35 35 -52.5 72t-17.5 62zM178 434q0 -153 94 -245.5t226 -92.5q148 0 264.5 97.5t146.5 238.5l39 178q-24 131 -116 212t-228 81q-122 0 -221 -68.5t-152 -175t-53 -225.5z" />
<glyph unicode="h" d="M47 41l277 1300q8 48 45 48h12q42 0 42 -35q0 -6 -1 -13l-123 -567q62 101 154 158t206 57q152 0 242 -88.5t90 -232.5q0 -87 -57 -310q-47 -172 -47 -223q0 -43 22.5 -61t67.5 -21q1 0 1.5 -7t-2.5 -16.5t-9.5 -19.5t-21 -17t-34.5 -7q-55 0 -89 36t-34 99q0 62 50 250 q53 198 53 280q0 115 -65 180.5t-179 65.5q-124 0 -213 -64.5t-162 -191.5l-127 -600q-11 -47 -45 -47h-12q-43 0 -43 33q0 6 2 14z" />
<glyph unicode="i" horiz-adv-x="401" d="M90 193l156 735q6 26 16 36.5t29 10.5h12q43 0 43 -33q0 -6 -2 -14l-156 -733q-7 -33 -7 -58q0 -35 14 -52q24 -30 90 -30q1 0 1 -7t-2.5 -17t-9 -20.5t-22 -17.5t-37.5 -7q-79 0 -113 52q-21 32 -21 83q0 32 9 72zM305 1284q0 34 19 58t53 24q69 0 69 -53 q0 -34 -18.5 -58t-52.5 -24q-70 0 -70 53z" />
<glyph unicode="j" horiz-adv-x="401" d="M-338 -309q0 17 10 32.5t20 23.5t11 5q53 -71 147 -71q65 0 107 43.5t63 142.5l226 1061q6 26 16 36.5t29 10.5h12q43 0 43 -33q0 -6 -2 -14l-229 -1078q-27 -130 -97.5 -194t-171.5 -64q-103 0 -161 54q-23 23 -23 45zM305 1284q0 34 19 58t53 24q69 0 69 -53 q0 -34 -18.5 -58t-52.5 -24q-70 0 -70 53z" />
<glyph unicode="k" horiz-adv-x="874" d="M47 41l277 1300q8 48 45 48h12q42 0 42 -35q0 -6 -1 -13l-174 -815l604 461q3 1 13 -3t19.5 -14t13.5 -24q1 -3 1 -7q1 -12 -9 -29q-12 -22 -44 -46l-492 -364l234 -301q63 -88 97.5 -108.5t100.5 -20.5q1 0 1.5 -8.5t-2 -21t-9.5 -25t-24.5 -21t-42.5 -8.5 q-54 0 -93 33.5t-122 150.5l-250 334l-99 -463q-11 -47 -45 -47h-12q-43 0 -43 33q0 6 2 14z" />
<glyph unicode="l" horiz-adv-x="401" d="M90 193l244 1148q11 48 47 48h10q43 0 43 -34q0 -7 -2 -14l-244 -1146q-7 -33 -7 -58q0 -35 14 -52q24 -30 90 -30q1 0 1 -7t-2.5 -17t-9 -20.5t-22 -17.5t-37.5 -7q-79 0 -113 52q-21 32 -21 83q0 32 9 72z" />
<glyph unicode="m" horiz-adv-x="1753" d="M47 41l199 928q0 6 20 6q58 0 58 -64q0 -19 -5 -43l-14 -71q64 92 152.5 142t191.5 50q116 0 197 -64t100 -171q62 108 161 171.5t230 63.5q139 0 227.5 -88.5t88.5 -228.5q0 -81 -62 -314q-47 -172 -47 -223q0 -43 23.5 -61t68.5 -21q1 0 1 -7t-2.5 -16.5t-8.5 -19.5 t-20.5 -17t-34.5 -7q-56 0 -89.5 35.5t-33.5 99.5q0 66 49 250q53 214 53 284q0 112 -62 177t-163 65q-120 0 -205.5 -60.5t-154.5 -181.5l-129 -614q-10 -47 -56 -47h-10q-21 0 -30 10t-3 37l113 537q10 45 10 84q0 82 -43 144q-63 90 -180 91q-116 0 -201 -57.5 t-160 -177.5l-131 -621q-6 -27 -19 -37t-36 -10h-10q-35 0 -35 31q0 7 2 16z" />
<glyph unicode="n" d="M47 41l199 928q0 6 20 6q34 0 49 -23q9 -13 9 -39q0 -19 -5 -45l-20 -92q64 100 155.5 156.5t204.5 56.5q152 0 242 -88.5t90 -232.5q0 -87 -57 -310q-47 -172 -47 -223q0 -43 22.5 -61t67.5 -21q1 0 1.5 -7t-2.5 -16.5t-9.5 -19.5t-21 -17t-34.5 -7q-55 0 -89 36t-34 99 q0 62 50 250q53 198 53 280q0 115 -65 180.5t-179 65.5q-124 0 -213 -64.5t-162 -191.5l-127 -600q-11 -47 -45 -47h-12q-43 0 -43 33q0 6 2 14z" />
<glyph unicode="o" horiz-adv-x="1132" d="M76 424q0 152 71 281.5t196 206.5t273 77q189 0 316 -125.5t127 -318.5q0 -151 -72 -281t-196.5 -207t-272.5 -77q-190 0 -316 125t-126 319zM174 426q0 -154 99 -257t251 -103q177 0 305.5 140.5t128.5 336.5q0 156 -98 258t-250 102q-116 0 -216.5 -64t-160 -174 t-59.5 -239z" />
<glyph unicode="p" horiz-adv-x="1155" d="M-37 -348l283 1317q0 6 20 6q34 0 49 -23q9 -13 9 -39q0 -19 -5 -45l-18 -86q142 207 373 207q83 0 156 -28.5t129.5 -81.5t89 -135.5t32.5 -184.5q0 -148 -65.5 -280t-187 -215.5t-269.5 -83.5q-142 0 -241.5 73.5t-133.5 175.5l-123 -577q-4 -26 -15 -36.5t-30 -10.5 h-12q-42 0 -42 35q0 5 1 12zM229 446q-6 -30 -6 -60q0 -38 10 -77q17 -68 59 -122t113.5 -87.5t159.5 -33.5q94 0 174 42t132.5 111.5t82 158t29.5 181.5q0 157 -92 250.5t-227 93.5q-127 0 -231 -74t-161 -190z" />
<glyph unicode="q" horiz-adv-x="1159" d="M82 410q0 148 65.5 280t187 215.5t269.5 83.5q136 0 235.5 -66.5t141.5 -168.5l25 118q12 57 30 80t49 23q9 0 18 -2.5t14 -5.5t5 -4l-278 -1311q-6 -26 -16 -36.5t-29 -10.5h-13q-43 0 -43 33q0 6 2 14l115 534q-142 -206 -373 -206q-110 0 -201 49t-147.5 148.5 t-56.5 232.5zM180 410q0 -156 92.5 -250t227.5 -94q150 0 264.5 103.5t144.5 250.5l41 186q-30 138 -116.5 217.5t-235.5 79.5q-94 0 -174 -42t-132.5 -111.5t-82 -158t-29.5 -181.5z" />
<glyph unicode="r" horiz-adv-x="671" d="M47 41l199 928q0 6 20 6q34 0 49 -23q9 -13 9 -39q0 -19 -5 -45l-20 -100q102 221 285 221q59 0 101 -25t42 -59q0 -21 -9.5 -37t-19 -21.5t-12.5 -4.5q-47 55 -129 55q-117 0 -200.5 -124t-129.5 -345l-82 -387q-11 -47 -45 -47h-12q-43 0 -43 33q0 6 2 14z" />
<glyph unicode="s" horiz-adv-x="903" d="M10 193q0 19 13 34t26 20t17 3q41 -80 134.5 -134.5t217.5 -54.5q126 0 197 52t71 137q0 25 -7.5 45.5t-26.5 38t-36.5 30t-52.5 27t-58 23t-69 24.5q-54 18 -88 31.5t-78 38t-69.5 50.5t-43.5 66t-18 89q0 119 98.5 197.5t254.5 78.5q103 0 187.5 -32t135.5 -83 q47 -50 47 -90q0 -20 -13 -33.5t-27 -18t-17 0.5q-34 76 -122.5 125t-199.5 49q-115 0 -181 -54t-66 -128q0 -32 14.5 -59.5t34 -45.5t58.5 -38t68 -31.5t83 -30.5q53 -19 85 -31.5t75 -36t67 -48t41.5 -62t17.5 -82.5q0 -128 -97.5 -204t-270.5 -76q-111 0 -203.5 34.5 t-147.5 89.5q-51 48 -51 89z" />
<glyph unicode="t" horiz-adv-x="671" d="M119 905q0 38 18 53q8 11 37 11h123l29 135q10 56 29 79t51 23q8 0 17 -2.5t14 -5.5t5 -4l-47 -225h271q43 0 43 -25q0 -35 -19 -51q-9 -12 -37 -12h-276l-121 -562q-12 -57 -12 -101q0 -155 151 -155q43 0 81.5 19t58.5 47q2 1 8 -4.5t11 -17.5t5 -25q0 -27 -24 -51 q-51 -51 -154 -51q-135 0 -195 87q-39 56 -38 143q0 49 12 109l119 562h-117q-43 0 -43 24z" />
<glyph unicode="u" d="M121 434l104 494q6 27 16 37t29 10h13q43 0 43 -33q0 -6 -2 -14l-107 -500q-12 -57 -12 -106q0 -92 44 -155q68 -95 210 -95q117 0 203.5 60t158.5 185l129 611q6 27 16 37t29 10h13q42 0 42 -35q0 -6 -1 -12l-156 -733q-8 -35 -8 -60q1 -33 13 -49q22 -30 83 -31 q1 0 1.5 -7t-2 -17t-9 -20.5t-21.5 -17.5t-37 -7q-72 0 -106 50q-21 31 -21 82q0 30 7 66q-59 -97 -150 -150.5t-199 -53.5q-187 0 -277 122q-60 82 -60 203q0 60 14 129z" />
<glyph unicode="v" horiz-adv-x="929" d="M45 910q-1 7 1 15q2 12 8.5 24t22 20t38.5 8q59 0 100 -41q55 -55 80 -242l80 -596l534 875q6 6 30 -0.5t34 -30.5q2 -5 2 -13q0 -25 -27 -73l-504 -807q-21 -34 -39 -45.5t-47 -11.5q-32 0 -48 17.5t-21 56.5l-90 636q-17 113 -51 155q-33 40 -95 40h-6q-2 0 -2 13z" />
<glyph unicode="w" horiz-adv-x="1390" d="M85 913q1 7 3 15q4 11 11 22t24 19t41 8q55 0 92 -37q61 -61 61 -232v-16l-6 -590l357 660q18 34 38 45.5t52 11.5q37 0 55 -18t23 -58l69 -641l418 871q3 5 27.5 0t36.5 -29q4 -7 4 -18q0 -25 -19 -70l-395 -807q-18 -35 -37 -46t-49 -11q-32 0 -48.5 18t-21.5 58 l-80 632l-362 -651q-18 -34 -36.5 -45.5t-49.5 -11.5q-72 0 -72 74l2 632q0 116 -33 158.5t-102 44.5q-3 0 -3 12z" />
<glyph unicode="x" horiz-adv-x="931" d="M-30 32q-1 3 0 6q0 25 44 71l381 374l-241 455q-5 9 15 25q19 15 37 15q19 0 38 -16q20 -18 35 -49l188 -374l440 444q1 1 3 1q7 0 25 -10q21 -12 27 -38q1 -3 1 -5q0 -25 -45 -71l-402 -395l129 -242q49 -93 89 -121q37 -26 101 -26h9q1 0 1.5 -8.5t-2 -21t-9 -25 t-23 -21t-41.5 -8.5q-64 0 -111.5 45t-111.5 176l-103 197l-419 -424q-1 -1 -3 -1q-6 0 -24 10q-22 11 -28 37z" />
<glyph unicode="y" horiz-adv-x="927" d="M-184 -319q0 15 9 28.5t19 19t13 3.5q18 -23 49.5 -37t58.5 -14q60 0 122.5 52.5t160.5 196.5l82 123q-47 5 -54 66l-79 583q-15 106 -51 152q-34 43 -96 43h-5q-1 0 -1.5 8t2 20t9 24t24 20t42.5 8q52 0 86 -37q66 -66 86 -246l74 -588l542 867q4 6 28 -1t33 -31 q3 -5 3 -12q0 -25 -27 -73l-604 -946q-118 -187 -196.5 -253.5t-174.5 -66.5q-92 0 -137 48q-18 18 -18 43z" />
<glyph unicode="z" horiz-adv-x="942" d="M0 57q0 40 45 80l776 748h-592q-53 0 -53 35q0 12 8.5 30.5t12.5 18.5h700q6 0 17.5 -20t11.5 -38q0 -40 -45 -80l-777 -747h615q53 0 53 -35q0 -12 -8 -30.5t-12 -18.5h-723q-6 0 -17.5 19.5t-11.5 37.5z" />
<glyph unicode="{" horiz-adv-x="806" d="M53 487q6 35 20 50t66 36l170 78q78 336 254 512q86 86 174 128t150 42q28 0 39 -18.5t5.5 -39t-18.5 -26.5q-76 -12 -153.5 -52t-136.5 -99q-78 -80 -132 -202.5t-96 -305.5l-258 -111l203 -155q-65 -402 100 -570q62 -59 138 -82q6 -4 9 -22t-8.5 -35.5t-41.5 -17.5 q-74 0 -164 90q-94 96 -130.5 253t-9.5 343l-133 102q-36 27 -45 46.5t-2 55.5z" />
<glyph unicode="|" horiz-adv-x="471" d="M10 -342l346 1626q5 25 17 36t37 11h10q21 0 30 -14.5t3 -38.5l-347 -1626q-4 -26 -16 -36.5t-37 -10.5h-8q-47 0 -35 53z" />
<glyph unicode="}" horiz-adv-x="806" d="M-136 -345.5q5 20.5 17 26.5q77 12 155.5 52t137.5 99q77 80 130.5 202t96.5 306l258 111l-202 155q66 402 -103 568q-57 59 -135 83q-7 2 -10 20.5t9 37t40 18.5q74 0 164 -90q95 -95 131.5 -252t9.5 -344l133 -102q35 -27 44.5 -47t2.5 -56q-6 -35 -20 -50t-66 -36 l-170 -77q-43 -186 -107 -307t-149 -205q-85 -86 -172 -128t-149 -42q-29 0 -40 18.5t-6 39z" />
<glyph unicode="~" horiz-adv-x="1021" d="M219 367q18 95 62.5 148.5t113.5 53.5q46 0 80 -22t68 -62q28 -34 46 -47.5t42 -13.5q52 0 80 86q8 24 18.5 35.5t28.5 11.5q21 0 35 -14q-15 -94 -60 -147.5t-115 -53.5q-46 0 -79.5 22t-67.5 62q-30 33 -48 46t-40 13q-52 0 -80 -86q-15 -45 -49 -45q-16 0 -35 13z" />
<glyph unicode="&#xa1;" horiz-adv-x="475" d="M86 59l219 883q9 31 29 31q22 0 28.5 -9t2.5 -34l-156 -881q-4 -33 -17 -44t-40 -11q-43 0 -59.5 13t-6.5 52zM324 1233q0 56 26 82q20 20 58 20q35 0 55 -14.5t20 -40.5q0 -58 -24 -82q-20 -20 -60 -20q-35 0 -55 14.5t-20 40.5z" />
<glyph unicode="&#xa2;" horiz-adv-x="1077" d="M115 514q0 109 42 212t113 181t172.5 125.5t214.5 46.5l48 219q8 48 40 48h11q47 0 37 -48l-48 -225q145 -22 232 -112q40 -40 61 -87t21 -83q0 -28 -18 -44.5t-34.5 -16t-21.5 2.5q-12 97 -81 168t-179 88l-176 -829q4 0 12 -1t12 -1q213 0 330 192q2 4 15.5 0.5 t26.5 -15.5t13 -30q0 -43 -63 -106q-53 -53 -139.5 -90t-190.5 -37q-21 0 -33 2l-47 -228q-11 -47 -43 -47h-10q-47 0 -37 47q3 15 8.5 39.5t16.5 77t18 87t6 34.5q-147 34 -238 146.5t-91 283.5zM217 520q0 -135 68 -226.5t184 -121.5l174 821q-122 -4 -221.5 -74 t-152 -176.5t-52.5 -222.5z" />
<glyph unicode="&#xa3;" horiz-adv-x="1118" d="M-8 27q0 31 20 55q10 12 41 12h127l131 455h-178q-47 0 -47 26q0 32 18 54q15 12 39 12h195l72 250q69 246 168.5 352.5t261.5 106.5q149 0 243 -90q30 -32 47 -69t17 -63q0 -30 -17.5 -47.5t-33 -18t-19.5 0.5q-22 97 -85 148t-158 51q-116 0 -191 -87.5t-135 -295.5 l-70 -238h377q47 0 47 -27q0 -31 -18 -53q-15 -12 -41 -12h-389l-133 -455h669q58 0 58 -39q0 -11 -5 -24.5t-10.5 -22t-7.5 -8.5h-946q-47 0 -47 27z" />
<glyph unicode="&#xa4;" horiz-adv-x="1273" d="M76 485q0 36 18 54q10 10 39 10h154q10 118 45 237h-142q-45 0 -45 25q0 11 6 27.5t13 23.5q12 12 39 12h157q75 200 187 312q160 160 383 160q187 0 299 -115q33 -32 52 -77t19 -81q0 -27 -17 -44t-32 -18t-18 1q-28 117 -108.5 182.5t-204.5 65.5q-174 0 -304 -130 q-97 -97 -155 -256h475q43 0 43 -24q0 -38 -18 -53q-8 -11 -37 -11h-492q-34 -121 -45 -237h479q43 0 43 -25q0 -36 -18 -51q-7 -10 -37 -10h-469q5 -177 99.5 -286t252.5 -109q217 0 344 204q2 2 14 -2.5t23.5 -17t11.5 -29.5q0 -45 -67 -112q-55 -57 -142.5 -94 t-179.5 -37q-216 0 -335.5 128t-122.5 355h-162q-45 0 -45 22z" />
<glyph unicode="&#xa5;" horiz-adv-x="1132" d="M78 276q0 32 18 54q13 10 37 10h356l41 203h-348q-43 0 -43 22q0 37 19 53q8 11 37 11h325l-289 669q-1 5 8 13t23 13.5t30 6t31.5 -10.5t24.5 -34l254 -620l535 667q5 5 30 -4.5t31.5 -36.5t-43.5 -92l-477 -571h317q43 0 43 -23q0 -38 -18 -53q-8 -10 -37 -10h-356 l-43 -203h350q43 0 43 -25q0 -12 -5.5 -29t-13.5 -24q-8 -10 -36 -10h-357l-45 -211q-11 -47 -55 -47h-6q-44 0 -33 47l45 211h-348q-45 0 -45 24z" />
<glyph unicode="&#xa7;" horiz-adv-x="1026" d="M-47 -68q0 23 15 40.5t30 24t18 3.5q34 -104 131 -168.5t261 -64.5q145 0 226 63t81 164q0 22 -5 41.5t-18 36.5t-25 30.5t-37 27.5t-42 23.5t-51 23t-54 20.5t-61 22q-64 23 -102.5 38.5t-88 42.5t-77 55t-47 69.5t-19.5 91.5q0 116 83 189.5t208 95.5q-148 84 -148 225 q0 138 112 229t296 91q225 0 346 -121q29 -29 45.5 -62.5t16.5 -54.5q0 -23 -15.5 -39.5t-31 -21.5t-19.5 0q-34 95 -123 154t-227 59q-137 0 -218 -64t-81 -158q0 -26 8.5 -49.5t19.5 -40.5t36 -35.5t43.5 -29.5t57 -28t60.5 -25.5t70 -26.5q53 -20 83.5 -32.5t74.5 -33.5 t69 -41t50 -47.5t36.5 -61t11.5 -73.5q0 -121 -81.5 -195t-206.5 -94q151 -77 151 -219q0 -145 -111.5 -236.5t-302.5 -91.5q-132 0 -229 35t-152 90q-67 70 -67 129zM186 537q0 -38 16 -69.5t39.5 -53.5t66 -44t80 -36.5t97.5 -34.5q17 -5 47.5 -17.5t34.5 -13.5 q118 6 202.5 69t84.5 159q0 36 -16 66t-38 50.5t-65.5 42t-77 34.5t-94.5 34q-26 8 -96 35q-120 -13 -200.5 -71t-80.5 -150z" />
<glyph unicode="&#xa8;" horiz-adv-x="1024" d="M422 1176q0 39 20.5 65.5t57.5 26.5q78 0 78 -60q0 -39 -21.5 -65.5t-58.5 -26.5q-76 0 -76 60zM748 1176q0 39 20.5 65.5t58.5 26.5q76 0 76 -60q0 -39 -21 -65.5t-59 -26.5q-75 0 -75 60z" />
<glyph unicode="&#xa9;" horiz-adv-x="1533" d="M137 659q0 145 51 272.5t139 218.5t212.5 143.5t265.5 52.5q145 0 269.5 -50.5t212 -140t137 -216t49.5 -273.5q0 -145 -51 -272.5t-139.5 -218t-213 -143t-266.5 -52.5q-193 0 -345 86.5t-236.5 241.5t-84.5 351zM219 662q0 -264 166 -436.5t422 -172.5 q257 0 420.5 171.5t163.5 439.5q0 130 -44.5 243.5t-122 194t-186.5 126.5t-235 46q-257 0 -420.5 -171t-163.5 -441zM467 659q0 155 100.5 260t251.5 105q74 0 134 -24t94 -60q43 -40 43 -82q0 -15 -12.5 -27.5t-25 -17t-14.5 -2.5q-26 59 -82 94t-137 35 q-110 0 -185 -80.5t-75 -197.5q0 -122 74 -201.5t192 -79.5q87 0 143 40t79 99q2 3 15 -1t25.5 -16.5t12.5 -29.5q0 -40 -53 -96q-34 -34 -92.5 -57t-131.5 -23q-159 0 -257.5 101.5t-98.5 260.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="960" d="M141 870q0 119 54.5 228.5t154.5 179.5t221 70q108 0 185 -54.5t104 -140.5l19 90q10 51 27 71.5t48 20.5q8 0 16.5 -2.5t13.5 -5.5t5 -4l-131 -612q-12 -62 4.5 -86.5t65.5 -26.5q3 0 1 -14t-17 -27.5t-44 -13.5q-64 0 -92 44t-10 126v2q-52 -87 -132 -132.5t-175 -45.5 q-136 0 -227 90t-91 243zM233 874q0 -117 69.5 -187.5t172.5 -70.5q121 0 213.5 85t118.5 200l29 141q-18 101 -87 163.5t-180 62.5q-98 0 -176.5 -58.5t-119 -148t-40.5 -187.5zM295 41q0 49 22 74q15 18 54 18q69 0 69 -49q0 -53 -24 -74q-17 -20 -51 -20 q-33 0 -51.5 13.5t-18.5 37.5z" />
<glyph unicode="&#xab;" horiz-adv-x="999" d="M98 514q0 4 21 25t69 61t109 84q95 70 145 98.5t78 28.5q17 0 26 -10.5t9 -26.5q0 -45 -195 -168q-130 -86 -172 -110q70 -59 138 -121q86 -79 113 -109t28 -53q2 -14 -10 -24.5t-29 -10.5q-29 0 -70.5 33t-113.5 111q-65 72 -105.5 126.5t-40.5 65.5zM483 498 q0 5 26 29.5t77.5 65.5t113.5 87q98 71 152 102t82 31q17 0 26 -10.5t9 -26.5q0 -48 -215 -184q-16 -10 -80 -50.5t-101 -62.5q34 -27 125 -110q80 -75 102.5 -101.5t22.5 -48.5q0 -14 -12 -24.5t-29 -10.5q-28 0 -64.5 29t-103.5 102q-39 44 -70.5 86t-46 66t-14.5 31z" />
<glyph unicode="&#xad;" horiz-adv-x="718" d="M90 489q0 11 6 28t13 24q10 12 38 12h447q47 0 47 -25q0 -35 -18 -53q-15 -12 -39 -12h-449q-45 0 -45 26z" />
<glyph unicode="&#xae;" horiz-adv-x="1533" d="M137 659q0 145 51 272.5t139 218.5t212.5 143.5t265.5 52.5q145 0 269.5 -50.5t212 -140t137 -216t49.5 -273.5q0 -145 -51 -272.5t-139.5 -218t-213 -143t-266.5 -52.5q-193 0 -345 86.5t-236.5 241.5t-84.5 351zM219 662q0 -264 166 -436.5t422 -172.5 q257 0 420.5 171.5t163.5 439.5q0 130 -44.5 243.5t-122 194t-186.5 126.5t-235 46q-257 0 -420.5 -171t-163.5 -441zM565 360v607q0 55 49 55h207q131 0 200 -54t69 -159q0 -102 -68 -153.5t-170 -51.5h-10l239 -252q2 -4 -3 -13.5t-18 -17.5t-28 -8q-37 0 -63 33l-224 250 h-90v-240q0 -43 -39 -43h-12q-39 0 -39 47zM653 672h172q84 0 128 36t44 101q0 135 -178 135h-166v-272z" />
<glyph unicode="&#xaf;" horiz-adv-x="1024" d="M395 1188q0 30 19 53q10 12 41 12h434q49 0 49 -26q0 -34 -18 -56q-10 -10 -41 -10h-437q-47 0 -47 27z" />
<glyph unicode="&#xb0;" horiz-adv-x="753" d="M193 1016q0 137 96 235.5t229 98.5q111 0 183.5 -70t72.5 -184q0 -137 -96 -236.5t-229 -99.5q-110 0 -183 71t-73 185zM279 1022q0 -78 51.5 -130t132.5 -52q93 0 159 74t66 176q0 78 -51.5 130t-132.5 52q-93 0 -159 -74t-66 -176z" />
<glyph unicode="&#xb1;" horiz-adv-x="1228" d="M150 287q0 13 5.5 30.5t12.5 24.5q9 12 39 12h745q47 0 47 -24q0 -12 -6.5 -30.5t-13.5 -25.5q-9 -12 -39 -12h-743q-47 0 -47 25zM301 858q0 11 5.5 27.5t12.5 23.5q10 13 37 13h301l66 299q11 49 43 49h14q18 0 29 -12t6 -37l-63 -299h292q46 0 46 -25q0 -11 -6 -27.5 t-13 -23.5q-12 -12 -37 -12h-301l-63 -302q-11 -47 -45 -47h-13q-18 0 -28.5 11t-5.5 36l63 302h-295q-45 0 -45 24z" />
<glyph unicode="&#xb2;" horiz-adv-x="688" d="M47 643q6 116 80 193q61 61 213 116q122 42 171.5 79t49.5 104q0 54 -45 88t-119 34q-133 0 -188 -131h-15.5t-27 13t-14.5 35q0 16 13 42t34 48q78 75 209 75q113 0 179 -54.5t66 -145.5q0 -98 -63.5 -151t-202.5 -101q-79 -28 -124 -49.5t-77 -53.5q-47 -47 -53 -120 h385q53 0 53 -33q0 -9 -4.5 -20.5t-9.5 -19t-6 -7.5h-465q-17 0 -28.5 17t-10.5 42z" />
<glyph unicode="&#xb3;" horiz-adv-x="688" d="M33 739q0 26 15.5 41.5t30.5 18.5t17 0q8 -66 61.5 -108t143.5 -42q89 0 140 46.5t51 115.5q0 56 -40 90.5t-116 34.5h-57q-37 0 -37 25q0 24 14 38q11 11 39 11h65q98 0 141.5 37t43.5 98q0 51 -43.5 84t-112.5 33q-64 0 -115 -29.5t-77 -75.5q-3 -2 -15 1t-24.5 14 t-12.5 28q0 29 31 57q79 82 219 82q102 0 174 -52.5t72 -129.5t-46.5 -126t-133.5 -64q125 -38 125 -160q0 -97 -72.5 -166.5t-214.5 -69.5q-141 0 -219 74q-47 47 -47 94z" />
<glyph unicode="&#xb4;" horiz-adv-x="1024" d="M508 1120q-3 4 30 38.5t98.5 88.5t135.5 99q54 40 94 40q19 0 31 -11.5t12 -28.5q0 -20 -19 -40t-67 -53q-74 -46 -150.5 -80t-119.5 -46.5t-45 -6.5z" />
<glyph unicode="&#xb6;" horiz-adv-x="1153" d="M154 918q0 87 27.5 159t84.5 128.5t155.5 88t231.5 31.5h250q35 0 50.5 -19t7.5 -53l-256 -1212q-11 -47 -52 -47h-14q-22 0 -31.5 14t-3.5 39l121 576h-135q-214 0 -325 74t-111 221zM813 47l262 1237q6 26 17 36.5t34 10.5h13q23 0 33 -14t4 -39l-263 -1237 q-11 -47 -51 -47h-14q-22 0 -31.5 14t-3.5 39z" />
<glyph unicode="&#xb7;" horiz-adv-x="421" d="M137 485q0 57 27 84q18 21 57 21q35 0 55.5 -14.5t20.5 -40.5q0 -58 -27 -82q-21 -21 -57 -21q-35 0 -55.5 14t-20.5 39z" />
<glyph unicode="&#xb8;" horiz-adv-x="1024" d="M150 -418q14 20 36 54t68.5 105t77.5 118t30 47q3 3 24.5 -1t43 -18.5t21.5 -36.5q0 -33 -35 -71l-221 -228q-9 -10 -30 5t-15 26z" />
<glyph unicode="&#xb9;" horiz-adv-x="688" d="M59 616q0 16 7 27q12 23 47 23h166l114 530q-50 -53 -121 -53q-42 0 -67 19t-27 50q0 18 9 33.5t17.5 22t10.5 4.5q22 -45 84 -45q42 0 81 29.5t54 74.5h39q42 0 33 -47l-137 -618h155q19 0 29.5 -9t7.5 -24q0 -19 -8 -27q-8 -22 -45 -22h-412q-20 0 -30 8.5t-7 23.5z " />
<glyph unicode="&#xba;" horiz-adv-x="899" d="M150 891q0 122 56.5 226.5t157 166.5t220.5 62q156 0 257 -99.5t101 -255.5q0 -123 -57 -227.5t-158 -166.5t-221 -62q-155 0 -255.5 99.5t-100.5 256.5zM242 893q0 -119 76 -199t194 -80q137 0 236.5 110.5t99.5 262.5q0 119 -77 199t-193 80q-138 0 -237 -110.5 t-99 -262.5zM285 41q0 49 22 74q15 18 53 18q70 0 70 -49q0 -53 -24 -74q-17 -20 -52 -20q-33 0 -51 13.5t-18 37.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="999" d="M35 213q0 49 215 184q112 73 180 115q-53 42 -125 111q-78 72 -101.5 99.5t-23.5 49.5q0 14 12 24.5t29 10.5q28 0 64.5 -29t103.5 -102q58 -67 94.5 -119.5t36.5 -64.5q0 -4 -25.5 -28.5t-78 -67.5t-113.5 -87q-100 -70 -152.5 -100.5t-80.5 -30.5q-16 0 -25.5 10.5 t-9.5 24.5zM451 217q0 45 192 168q123 81 172 111q-66 54 -137 120q-86 79 -113 109t-28 51q0 14 10.5 24.5t27.5 10.5q29 0 72 -32.5t113 -108.5q43 -48 77.5 -92t51 -69.5t16.5 -31.5q0 -5 -21 -26.5t-68.5 -61t-108.5 -84.5q-95 -68 -145 -96.5t-77 -28.5q-34 0 -34 37z " />
<glyph unicode="&#xbc;" horiz-adv-x="1622" d="M59 616q0 16 7 27q12 23 47 23h166l114 530q-50 -53 -121 -53q-42 0 -67 19t-27 50q0 18 9 33.5t17.5 22t10.5 4.5q22 -45 84 -45q42 0 81 29.5t54 74.5h39q42 0 33 -47l-137 -618h155q19 0 29.5 -9t7.5 -24q0 -19 -8 -27q-8 -22 -45 -22h-412q-20 0 -30 8.5t-7 23.5z M160 6q3 22 13.5 37t41.5 45q15 17 116 113.5t272 259t194 184.5l690 662q5 -1 12.5 -4.5t18 -16.5t6.5 -29q-6 -29 -56 -79q-14 -15 -127.5 -124t-254 -243.5t-201.5 -194.5q-64 -64 -235.5 -228t-311.5 -296l-139 -133q-3 -3 -13.5 2t-19.5 17.5t-6 27.5zM983 266 q0 28 39 76l360 365q24 26 42 34.5t49 8.5q78 0 63 -70l-84 -393h92q39 0 39 -25q0 -29 -16 -45q-8 -8 -35 -8h-96l-37 -168q-4 -25 -14.5 -35t-28.5 -10h-13q-43 0 -32 47l35 166h-334q-3 0 -9.5 7t-13 21t-6.5 29zM1069 287h299l80 387z" />
<glyph unicode="&#xbd;" horiz-adv-x="1622" d="M59 616q0 16 7 27q12 23 47 23h166l114 530q-50 -53 -121 -53q-42 0 -67 19t-27 50q0 18 9 33.5t17.5 22t10.5 4.5q22 -45 84 -45q42 0 81 29.5t54 74.5h39q42 0 33 -47l-137 -618h155q19 0 29.5 -9t7.5 -24q0 -19 -8 -27q-8 -22 -45 -22h-412q-20 0 -30 8.5t-7 23.5z M119 6q3 22 13.5 37t41.5 45q15 17 116 113.5t272 259t194 184.5l690 662q5 -1 12.5 -4.5t18 -16.5t6.5 -29q-6 -29 -56 -79q-14 -15 -127.5 -124t-254 -243.5t-201.5 -194.5q-64 -64 -235.5 -228t-310.5 -296l-140 -133q-3 -3 -13.5 2t-19.5 17.5t-6 27.5zM981 59 q6 116 80 193q62 62 213 117q123 43 172 79t49 103q0 55 -45 89t-119 34q-133 0 -188 -131q-3 0 -16 -0.5t-27.5 12.5t-14.5 35q0 16 13.5 42t34.5 48q80 76 208 76q113 0 179.5 -55t66.5 -146q0 -98 -63.5 -151t-202.5 -101q-55 -19 -83.5 -30.5t-62 -30.5t-55.5 -41 q-47 -47 -53 -121h385q53 0 53 -33q0 -9 -4.5 -20.5t-9.5 -19t-6 -7.5h-465q-17 0 -28.5 17t-10.5 42z" />
<glyph unicode="&#xbe;" horiz-adv-x="1622" d="M33 739q0 26 15.5 41.5t30.5 18.5t17 0q8 -66 61.5 -108t143.5 -42q89 0 140 46.5t51 115.5q0 56 -40 90.5t-116 34.5h-57q-37 0 -37 25q0 24 14 38q11 11 39 11h65q98 0 141.5 37t43.5 98q0 51 -43.5 84t-112.5 33q-64 0 -115 -29.5t-77 -75.5q-3 -2 -15 1t-24.5 14 t-12.5 28q0 29 31 57q79 82 219 82q102 0 174 -52.5t72 -129.5t-46.5 -126t-133.5 -64q125 -38 125 -160q0 -97 -72.5 -166.5t-214.5 -69.5q-141 0 -219 74q-47 47 -47 94zM160 6q3 22 13.5 37t41.5 45q15 17 116 113.5t272 259t194 184.5l690 662q5 -1 12.5 -4.5t18 -16.5 t6.5 -29q-6 -29 -56 -79q-14 -15 -127.5 -124t-254 -243.5t-201.5 -194.5q-64 -64 -235.5 -228t-311.5 -296l-139 -133q-3 -3 -13.5 2t-19.5 17.5t-6 27.5zM983 266q0 28 39 76l360 365q24 26 42 34.5t49 8.5q78 0 63 -70l-84 -393h92q39 0 39 -25q0 -29 -16 -45 q-8 -8 -35 -8h-96l-37 -168q-4 -25 -14.5 -35t-28.5 -10h-13q-43 0 -32 47l35 166h-334q-3 0 -9.5 7t-13 21t-6.5 29zM1069 287h299l80 387z" />
<glyph unicode="&#xbf;" horiz-adv-x="937" d="M20 322q0 63 18.5 117t47.5 91.5t72 69t84.5 51t91.5 37.5q137 46 189 82.5t52 83.5q0 62 -67 74q-3 1 0 12.5t16 23t35 11.5q48 0 80.5 -33.5t32.5 -85.5q0 -79 -62 -132t-213 -106q-140 -50 -208 -116.5t-68 -179.5q0 -111 80 -182.5t215 -71.5q121 0 210.5 71 t129.5 185q2 4 18 -0.5t31.5 -19.5t15.5 -36q0 -32 -24 -77t-66 -87q-129 -129 -321 -129q-176 0 -283 97.5t-107 249.5zM596 1229q0 58 27 82q20 20 57 20t57.5 -14t20.5 -39q0 -54 -27 -84q-20 -20 -57 -20t-57.5 14.5t-20.5 40.5z" />
<glyph unicode="&#xc0;" horiz-adv-x="1222" d="M-37.5 37.5q-2.5 13.5 4.5 35.5t23 50l706 1177q20 35 66 35q36 0 51.5 -11t20.5 -36l260 -1261q1 -10 -23.5 -23.5t-53 -4.5t-36.5 54l-80 406h-596l-272 -463q-1 -1 -8 -1t-16.5 1.5t-19.5 5.5t-17 13t-9.5 22.5zM356 549h527l-131 670zM518 1718q0 18 14 30.5t33 12.5 q31 0 88 -51q59 -50 109.5 -106t73.5 -90t18 -37q-6 -6 -43.5 7.5t-101.5 51t-125 88.5q-39 33 -52.5 53t-13.5 41z" />
<glyph unicode="&#xc1;" horiz-adv-x="1222" d="M-37.5 37.5q-2.5 13.5 4.5 35.5t23 50l706 1177q20 35 66 35q36 0 51.5 -11t20.5 -36l260 -1261q1 -10 -23.5 -23.5t-53 -4.5t-36.5 54l-80 406h-596l-272 -463q-1 -1 -8 -1t-16.5 1.5t-19.5 5.5t-17 13t-9.5 22.5zM356 549h527l-131 670zM694 1477q-3 4 30 38.5t98.5 88 t135.5 98.5q55 41 95 41q19 0 31 -12t12 -29q0 -20 -18.5 -39.5t-67.5 -52.5q-74 -46 -151 -80t-120 -46.5t-45 -6.5z" />
<glyph unicode="&#xc2;" horiz-adv-x="1222" d="M-37.5 37.5q-2.5 13.5 4.5 35.5t23 50l706 1177q20 35 66 35q36 0 51.5 -11t20.5 -36l260 -1261q1 -10 -23.5 -23.5t-53 -4.5t-36.5 54l-80 406h-596l-272 -463q-1 -1 -8 -1t-16.5 1.5t-19.5 5.5t-17 13t-9.5 22.5zM356 549h527l-131 670zM532 1497q0 24 31 61.5 t105 104.5q43 39 83.5 71.5t61.5 47t25 14.5q3 0 17.5 -14t40.5 -46t50 -69q76 -115 76 -151q0 -21 -10 -32t-25 -11q-20 0 -40 25t-62 101q-40 67 -60 109l-106 -111q-116 -124 -158 -124q-29 0 -29 24z" />
<glyph unicode="&#xc3;" horiz-adv-x="1222" d="M-37.5 37.5q-2.5 13.5 4.5 35.5t23 50l706 1177q20 35 66 35q36 0 51.5 -11t20.5 -36l260 -1261q1 -10 -23.5 -23.5t-53 -4.5t-36.5 54l-80 406h-596l-272 -463q-1 -1 -8 -1t-16.5 1.5t-19.5 5.5t-17 13t-9.5 22.5zM356 549h527l-131 670zM508 1495q18 94 63 148.5 t113 54.5q46 0 79 -22.5t68 -63.5q28 -33 47 -46.5t42 -13.5q28 0 46.5 21.5t32.5 64.5q15 48 48 48q15 0 34 -15q-16 -94 -60 -147.5t-114 -53.5q-47 0 -80.5 22t-66.5 62q-31 34 -48.5 47t-39.5 13q-55 0 -80 -86q-15 -47 -49 -47q-17 0 -35 14z" />
<glyph unicode="&#xc4;" horiz-adv-x="1222" d="M-37.5 37.5q-2.5 13.5 4.5 35.5t23 50l706 1177q20 35 66 35q36 0 51.5 -11t20.5 -36l260 -1261q1 -10 -23.5 -23.5t-53 -4.5t-36.5 54l-80 406h-596l-272 -463q-1 -1 -8 -1t-16.5 1.5t-19.5 5.5t-17 13t-9.5 22.5zM356 549h527l-131 670zM549 1532q0 39 20.5 65.5 t57.5 26.5q78 0 78 -59q0 -39 -21.5 -65.5t-58.5 -26.5q-76 0 -76 59zM874 1532q0 39 21 65.5t59 26.5q76 0 76 -59q0 -39 -21 -65.5t-59 -26.5q-76 0 -76 59z" />
<glyph unicode="&#xc5;" horiz-adv-x="1222" d="M-36 38q-2 13 4.5 35t23.5 50l676 1128l16 23q-54 23 -83.5 69t-29.5 103q0 103 71.5 179.5t174.5 76.5q94 0 150.5 -56t56.5 -137q0 -86 -53 -155.5t-133 -89.5q6 -18 6 -27l250 -1210q1 -10 -23 -23t-52 -4t-36 53l-82 408h-596l-272 -465q-1 -1 -7.5 -1t-16 2 t-19.5 5.5t-17 13t-9 22.5zM358 551h527l-133 668zM649 1452q0 -52 36 -90.5t95 -38.5q70 0 117 54.5t47 125.5q0 52 -35 90.5t-94 38.5q-71 0 -118.5 -54t-47.5 -126z" />
<glyph unicode="&#xc6;" horiz-adv-x="1728" d="M-38 37.5q-2 13.5 4.5 35.5t23.5 50l688 1147q31 55 90 55h975q47 0 47 -25q0 -35 -18 -57q-8 -10 -41 -10h-885l104 -508h529q47 0 47 -25q0 -12 -6 -30t-13 -25q-10 -10 -39 -10h-499l110 -541h443q47 0 47 -26q0 -13 -6 -31t-15 -27q-8 -10 -41 -10h-444q-39 0 -58 15 t-26 53l-78 391h-596l-272 -463q-1 -1 -8 -1t-16.5 1.5t-20 5.5t-17.5 13t-9 22.5zM356 549h527l-131 670z" />
<glyph unicode="&#xc7;" horiz-adv-x="1325" d="M127 545q0 214 94 397.5t262 293.5t371 110q247 0 387 -144q42 -42 65 -91.5t23 -84.5q0 -24 -15 -40.5t-30 -18.5t-22 0q-38 137 -143 213.5t-271 76.5q-129 0 -243.5 -56.5t-195 -151t-127 -222.5t-46.5 -268q0 -224 131 -356.5t346 -132.5q154 0 265 67t173 170 q2 4 14.5 -0.5t24.5 -19t12 -33.5q0 -48 -74 -125q-66 -64 -176.5 -106.5t-238.5 -42.5q-267 0 -426.5 152.5t-159.5 412.5zM424 -418q14 20 36 54t69 105t78 118t30 47q3 3 24 -1t42.5 -18.5t21.5 -36.5q0 -33 -35 -71l-221 -228q-9 -10 -30 5t-15 26z" />
<glyph unicode="&#xc8;" horiz-adv-x="1148" d="M74 72l252 1190q13 63 73 63h781q47 0 47 -25q0 -12 -6.5 -30t-14.5 -27q-8 -10 -41 -10h-743l-109 -508h572q47 0 47 -25q0 -32 -19 -55q-13 -10 -39 -10h-579l-115 -541h760q49 0 49 -26q0 -12 -6.5 -30.5t-13.5 -27.5q-10 -10 -41 -10h-797q-35 0 -50 19t-7 53z M578 1718q0 18 14 30.5t33 12.5q31 0 88 -51q59 -50 109.5 -106t73 -90t17.5 -37q-6 -6 -43.5 7.5t-101.5 51t-125 88.5q-38 32 -51.5 52.5t-13.5 41.5z" />
<glyph unicode="&#xc9;" horiz-adv-x="1148" d="M74 72l252 1190q13 63 73 63h781q47 0 47 -25q0 -12 -6.5 -30t-14.5 -27q-8 -10 -41 -10h-743l-109 -508h572q47 0 47 -25q0 -32 -19 -55q-13 -10 -39 -10h-579l-115 -541h760q49 0 49 -26q0 -12 -6.5 -30.5t-13.5 -27.5q-10 -10 -41 -10h-797q-35 0 -50 19t-7 53z M692 1477q-3 4 30 38.5t98.5 88t135.5 98.5q55 41 95 41q19 0 31 -12t12 -29q0 -20 -18.5 -39.5t-67.5 -52.5q-74 -46 -151 -80t-120 -46.5t-45 -6.5z" />
<glyph unicode="&#xca;" horiz-adv-x="1148" d="M74 72l252 1190q13 63 73 63h781q47 0 47 -25q0 -12 -6.5 -30t-14.5 -27q-8 -10 -41 -10h-743l-109 -508h572q47 0 47 -25q0 -32 -19 -55q-13 -10 -39 -10h-579l-115 -541h760q49 0 49 -26q0 -12 -6.5 -30.5t-13.5 -27.5q-10 -10 -41 -10h-797q-35 0 -50 19t-7 53z M571 1497q0 24 31 61.5t105 104.5q43 39 83.5 71.5t61.5 47t25 14.5q3 0 17.5 -14t40.5 -46t50 -69q76 -115 76 -151q0 -21 -10 -32t-25 -11q-20 0 -40 25t-62 101q-40 67 -60 109l-106 -111q-116 -124 -158 -124q-29 0 -29 24z" />
<glyph unicode="&#xcb;" horiz-adv-x="1148" d="M74 72l252 1190q13 63 73 63h781q47 0 47 -25q0 -12 -6.5 -30t-14.5 -27q-8 -10 -41 -10h-743l-109 -508h572q47 0 47 -25q0 -32 -19 -55q-13 -10 -39 -10h-579l-115 -541h760q49 0 49 -26q0 -12 -6.5 -30.5t-13.5 -27.5q-10 -10 -41 -10h-797q-35 0 -50 19t-7 53z M578 1532q0 39 20 65.5t57 26.5q78 0 78 -59q0 -39 -21.5 -65.5t-58.5 -26.5q-75 0 -75 59zM903 1532q0 39 21 65.5t59 26.5q76 0 76 -59q0 -39 -21 -65.5t-59 -26.5q-76 0 -76 59z" />
<glyph unicode="&#xcc;" horiz-adv-x="464" d="M90 47l262 1237q6 26 17 36.5t34 10.5h15q22 0 31.5 -14t3.5 -39l-263 -1237q-6 -26 -17 -36.5t-34 -10.5h-14q-47 0 -35 53zM174 1718q0 18 14 30.5t33 12.5q31 0 88 -51q59 -50 109.5 -106t73.5 -90t18 -37q-6 -6 -43.5 7.5t-101.5 51t-125 88.5q-39 33 -52.5 53 t-13.5 41z" />
<glyph unicode="&#xcd;" horiz-adv-x="464" d="M90 47l262 1237q6 26 17 36.5t34 10.5h15q22 0 31.5 -14t3.5 -39l-263 -1237q-6 -26 -17 -36.5t-34 -10.5h-14q-47 0 -35 53zM358 1477q-3 4 30.5 38.5t99 88t135.5 98.5q55 41 94 41q19 0 31 -12t12 -29q0 -20 -18.5 -39.5t-67.5 -52.5q-74 -46 -151 -80t-120 -46.5 t-45 -6.5z" />
<glyph unicode="&#xce;" horiz-adv-x="464" d="M90 47l262 1237q6 26 17 36.5t34 10.5h15q22 0 31.5 -14t3.5 -39l-263 -1237q-6 -26 -17 -36.5t-34 -10.5h-14q-47 0 -35 53zM197 1497q0 25 30.5 62.5t104.5 103.5q43 39 83.5 71.5t61.5 47t25 14.5q3 0 17.5 -14t40.5 -46t50 -69q76 -115 76 -151q0 -21 -10 -32 t-25 -11q-20 0 -40 25t-62 101q-40 67 -60 109l-106 -111q-116 -124 -158 -124q-28 0 -28 24z" />
<glyph unicode="&#xcf;" horiz-adv-x="464" d="M90 47l262 1237q6 26 17 36.5t34 10.5h15q22 0 31.5 -14t3.5 -39l-263 -1237q-6 -26 -17 -36.5t-34 -10.5h-14q-47 0 -35 53zM211 1532q0 39 20.5 65.5t57.5 26.5q78 0 78 -59q0 -39 -21.5 -65.5t-58.5 -26.5q-76 0 -76 59zM537 1532q0 39 20.5 65.5t58.5 26.5 q76 0 76 -59q0 -39 -21 -65.5t-59 -26.5q-75 0 -75 59z" />
<glyph unicode="&#xd0;" horiz-adv-x="1417" d="M80 643q0 11 5.5 28.5t12.5 24.5q11 11 39 11h160l119 555q9 63 73 63h220q310 0 481.5 -146t171.5 -409q0 -163 -52.5 -302t-153 -244t-257.5 -164.5t-354 -59.5h-324q-35 0 -50 19t-7 53l115 546h-154q-45 0 -45 25zM268 90h293q170 0 304 52t218 143.5t127 211 t43 259.5q0 226 -144 352.5t-407 126.5h-190l-113 -528h459q45 0 45 -25q0 -31 -18 -53q-15 -11 -37 -11h-467z" />
<glyph unicode="&#xd1;" horiz-adv-x="1372" d="M68 47l258 1215q16 69 71 69h27q24 0 37.5 -13t27.5 -44l555 -1124l240 1134q6 26 17.5 36.5t33.5 10.5h11q46 0 34 -53l-260 -1223q-13 -61 -65 -61h-13q-21 0 -35 13t-30 44l-567 1153l-246 -1163q-11 -47 -51 -47h-11q-46 0 -34 53zM635 1495q18 94 63 148.5t113 54.5 q46 0 79 -22.5t68 -63.5q28 -33 47 -46.5t42 -13.5q28 0 46.5 21.5t32.5 64.5q15 48 48 48q15 0 34 -15q-16 -94 -60 -147.5t-114 -53.5q-47 0 -80.5 22t-66.5 62q-31 34 -48.5 47t-39.5 13q-55 0 -80 -86q-15 -47 -49 -47q-18 0 -35 14z" />
<glyph unicode="&#xd2;" horiz-adv-x="1488" d="M127 551q0 167 61 317t162 253.5t233.5 164t272.5 60.5q264 0 423 -161t159 -411q0 -167 -61 -316t-162 -253t-233.5 -164.5t-272.5 -60.5q-174 0 -307.5 75t-204 204t-70.5 292zM236 559q0 -138 56 -248t167 -175.5t260 -65.5q116 0 227 53t195.5 144t136 222.5 t51.5 278.5q0 212 -129.5 350.5t-353.5 138.5q-116 0 -226.5 -53.5t-195.5 -144.5t-136.5 -222.5t-51.5 -277.5zM672 1718q0 18 14 30.5t33 12.5q31 0 88 -51q59 -50 109.5 -106t73.5 -90t18 -37q-6 -6 -43.5 7.5t-102 51t-125.5 88.5q-38 32 -51.5 52.5t-13.5 41.5z" />
<glyph unicode="&#xd3;" horiz-adv-x="1488" d="M127 551q0 167 61 317t162 253.5t233.5 164t272.5 60.5q264 0 423 -161t159 -411q0 -167 -61 -316t-162 -253t-233.5 -164.5t-272.5 -60.5q-174 0 -307.5 75t-204 204t-70.5 292zM236 559q0 -138 56 -248t167 -175.5t260 -65.5q116 0 227 53t195.5 144t136 222.5 t51.5 278.5q0 212 -129.5 350.5t-353.5 138.5q-116 0 -226.5 -53.5t-195.5 -144.5t-136.5 -222.5t-51.5 -277.5zM758 1477q-3 4 30 38.5t98.5 88t135.5 98.5q55 41 94 41q19 0 31 -12t12 -29q0 -20 -18.5 -39.5t-67.5 -52.5q-74 -46 -150.5 -80t-119.5 -46.5t-45 -6.5z" />
<glyph unicode="&#xd4;" horiz-adv-x="1488" d="M127 551q0 167 61 317t162 253.5t233.5 164t272.5 60.5q264 0 423 -161t159 -411q0 -167 -61 -316t-162 -253t-233.5 -164.5t-272.5 -60.5q-174 0 -307.5 75t-204 204t-70.5 292zM236 559q0 -138 56 -248t167 -175.5t260 -65.5q116 0 227 53t195.5 144t136 222.5 t51.5 278.5q0 212 -129.5 350.5t-353.5 138.5q-116 0 -226.5 -53.5t-195.5 -144.5t-136.5 -222.5t-51.5 -277.5zM668 1497q0 25 30.5 62.5t104.5 103.5q43 39 83.5 71.5t61.5 47t25 14.5q3 0 17.5 -14t40.5 -46t50 -69q76 -115 76 -151q0 -21 -10 -32t-25 -11q-20 0 -40 25 t-62 101q-34 57 -59 109l-107 -111q-116 -124 -158 -124q-28 0 -28 24z" />
<glyph unicode="&#xd5;" horiz-adv-x="1488" d="M127 551q0 167 61 317t162 253.5t233.5 164t272.5 60.5q264 0 423 -161t159 -411q0 -167 -61 -316t-162 -253t-233.5 -164.5t-272.5 -60.5q-174 0 -307.5 75t-204 204t-70.5 292zM236 559q0 -138 56 -248t167 -175.5t260 -65.5q116 0 227 53t195.5 144t136 222.5 t51.5 278.5q0 212 -129.5 350.5t-353.5 138.5q-116 0 -226.5 -53.5t-195.5 -144.5t-136.5 -222.5t-51.5 -277.5zM610 1495q18 94 63 148.5t113 54.5q46 0 79.5 -22.5t68.5 -63.5q29 -34 47 -47t41 -13q52 0 80 86q15 48 47 48q16 0 35 -15q-16 -94 -60 -147.5t-114 -53.5 q-79 0 -148 84q-31 34 -48.5 47t-39.5 13q-55 0 -80 -86q-15 -47 -49 -47q-17 0 -35 14z" />
<glyph unicode="&#xd6;" horiz-adv-x="1488" d="M127 551q0 167 61 317t162 253.5t233.5 164t272.5 60.5q264 0 423 -161t159 -411q0 -167 -61 -316t-162 -253t-233.5 -164.5t-272.5 -60.5q-174 0 -307.5 75t-204 204t-70.5 292zM236 559q0 -138 56 -248t167 -175.5t260 -65.5q116 0 227 53t195.5 144t136 222.5 t51.5 278.5q0 212 -129.5 350.5t-353.5 138.5q-116 0 -226.5 -53.5t-195.5 -144.5t-136.5 -222.5t-51.5 -277.5zM680 1532q0 39 20.5 65.5t57.5 26.5q78 0 78 -59q0 -39 -21.5 -65.5t-58.5 -26.5q-76 0 -76 59zM1006 1532q0 39 20.5 65.5t58.5 26.5q76 0 76 -59 q0 -39 -21 -65.5t-59 -26.5q-75 0 -75 59z" />
<glyph unicode="&#xd7;" horiz-adv-x="1103" d="M159.5 223.5q-3.5 24.5 18.5 48.5l330 283l-209 283q-18 25 -7.5 50t34 26.5t45.5 -23.5l207 -279l323 277q24 22 49 12t28.5 -34.5t-17.5 -47.5l-328 -280l211 -285q19 -25 8.5 -49.5t-34.5 -26t-46 22.5l-209 280l-325 -278q-24 -22 -49.5 -13t-29 33.5z" />
<glyph unicode="&#xd8;" horiz-adv-x="1488" d="M4 -10q0 40 45 82l174 155q-96 145 -96 324q0 167 61 317t162 253.5t233.5 164t272.5 60.5q256 0 418 -162l207 188q3 2 13.5 -4t20 -19.5t9.5 -27.5q0 -38 -45 -80l-144 -131q103 -144 103 -336q0 -167 -61 -316t-162 -253t-233.5 -164.5t-272.5 -60.5q-259 0 -430 174 l-234 -213q-3 -3 -13 3t-19 19t-9 27zM236 559q0 -146 67 -264l897 823q-126 139 -354 139q-116 0 -226.5 -53.5t-195.5 -144.5t-136.5 -222.5t-51.5 -277.5zM354 219q130 -149 365 -149q116 0 227 53t195.5 144t136 222.5t51.5 278.5q0 158 -76 276z" />
<glyph unicode="&#xd9;" horiz-adv-x="1292" d="M160 573l151 711q6 26 17 36.5t34 10.5h15q22 0 31.5 -14t3.5 -39l-150 -713q-53 -246 43 -369.5t320 -123.5q341 0 428 417l170 795q5 25 16.5 36t34.5 11h12q22 0 31.5 -14t3.5 -39l-170 -809q-106 -489 -533 -489q-270 0 -394.5 150.5t-63.5 442.5zM614 1698 q0 18 14.5 30.5t33.5 12.5q31 0 88 -51q59 -50 109.5 -106.5t73 -90.5t17.5 -37q-6 -6 -43.5 8t-101.5 51.5t-125 88.5q-39 33 -52.5 53t-13.5 41z" />
<glyph unicode="&#xda;" horiz-adv-x="1292" d="M160 573l151 711q6 26 17 36.5t34 10.5h15q22 0 31.5 -14t3.5 -39l-150 -713q-53 -246 43 -369.5t320 -123.5q341 0 428 417l170 795q5 25 16.5 36t34.5 11h12q22 0 31.5 -14t3.5 -39l-170 -809q-106 -489 -533 -489q-270 0 -394.5 150.5t-63.5 442.5zM707 1456 q-3 4 30 38.5t98.5 88t135.5 98.5q55 41 94 41q19 0 31 -12t12 -29q0 -20 -18.5 -39.5t-67.5 -52.5q-74 -46 -150.5 -80t-119.5 -46.5t-45 -6.5z" />
<glyph unicode="&#xdb;" horiz-adv-x="1292" d="M160 573l151 711q6 26 17 36.5t34 10.5h15q22 0 31.5 -14t3.5 -39l-150 -713q-53 -246 43 -369.5t320 -123.5q341 0 428 417l170 795q5 25 16.5 36t34.5 11h12q22 0 31.5 -14t3.5 -39l-170 -809q-106 -489 -533 -489q-270 0 -394.5 150.5t-63.5 442.5zM604 1477 q0 25 30.5 61.5t104.5 103.5q43 39 83.5 72t62 47.5t24.5 14.5t17.5 -14t41 -46t50.5 -69q76 -115 76 -152q0 -21 -10 -32t-25 -11q-20 0 -40.5 25.5t-62.5 101.5q-34 57 -59 109l-106 -111q-117 -125 -158 -125q-13 0 -21 7t-8 18z" />
<glyph unicode="&#xdc;" horiz-adv-x="1292" d="M160 573l151 711q6 26 17 36.5t34 10.5h15q22 0 31.5 -14t3.5 -39l-150 -713q-53 -246 43 -369.5t320 -123.5q341 0 428 417l170 795q5 25 16.5 36t34.5 11h12q22 0 31.5 -14t3.5 -39l-170 -809q-106 -489 -533 -489q-270 0 -394.5 150.5t-63.5 442.5zM618 1511 q0 39 20.5 66t57.5 27q78 0 78 -60q0 -39 -21.5 -65.5t-58.5 -26.5q-76 0 -76 59zM944 1511q0 39 21 66t59 27q76 0 76 -60q0 -39 -21 -65.5t-59 -26.5q-76 0 -76 59z" />
<glyph unicode="&#xdd;" d="M186 1292q-3 9 17.5 25t48.5 13.5t45 -40.5l289 -631l575 674q5 5 30 -5.5t32 -37.5t-43 -90l-559 -639l-111 -520q-6 -26 -18.5 -36.5t-36.5 -10.5h-15q-22 0 -30 10.5t-2 36.5l112 530zM637 1450q-3 4 30 38.5t98.5 88t135.5 98.5q55 41 94 41q19 0 31 -12t12 -29 q0 -20 -18.5 -39.5t-67.5 -52.5q-74 -46 -150.5 -80t-119.5 -46.5t-45 -6.5z" />
<glyph unicode="&#xde;" horiz-adv-x="1128" d="M68 47l262 1237q6 26 17.5 36.5t33.5 10.5h14q46 0 37 -53l-47 -211h260q215 0 327.5 -88.5t112.5 -251.5q0 -213 -135 -334t-389 -121h-344l-49 -231q-4 -26 -16 -36.5t-35 -10.5h-13q-48 0 -36 53zM236 362h344q193 0 297 94.5t104 256.5q0 128 -85.5 195t-256.5 67 h-272z" />
<glyph unicode="&#xdf;" horiz-adv-x="1052" d="M49 41l195 915q48 224 160 335.5t270 111.5q167 0 263.5 -82t96.5 -217q0 -157 -103.5 -247t-314.5 -99q156 -40 247 -133.5t91 -221.5q0 -178 -114 -300.5t-283 -122.5q-163 0 -238 75q-45 48 -45 90q0 20 12.5 36.5t25 23t14.5 2.5q65 -141 237 -141q126 0 209.5 94 t83.5 235q0 203 -274 291q-72 24 -72 68q0 56 39 82q199 0 293 63t94 199q0 97 -72.5 157t-191.5 60q-124 0 -209.5 -90t-124.5 -277l-191 -907q-14 -47 -47 -47h-12q-19 0 -31 14.5t-8 32.5z" />
<glyph unicode="&#xe0;" horiz-adv-x="1159" d="M76 395q0 150 68 285.5t193.5 222t276.5 86.5q136 0 233.5 -68t133.5 -171l25 122q12 57 30 80t49 23q9 0 18 -2.5t14 -5.5t5 -4l-164 -768q-16 -79 7 -108.5t84 -31.5q1 0 1.5 -7t-2 -17t-9 -20.5t-21.5 -17.5t-37 -7q-74 0 -108.5 53.5t-12.5 153.5v4 q-67 -105 -167.5 -161t-215.5 -56q-172 0 -286.5 111t-114.5 304zM176 397q0 -152 88.5 -241.5t224.5 -89.5q153 0 273.5 110t150.5 258l39 176q-28 133 -115 213t-229 80q-95 0 -177.5 -44t-137.5 -116t-86 -162.5t-31 -183.5zM457 1362q0 18 14 30.5t33 12.5q31 0 88 -51 q59 -50 109.5 -106t73.5 -90.5t18 -37.5q-6 -6 -43.5 8t-102 51.5t-125.5 88.5q-38 32 -51.5 52.5t-13.5 41.5z" />
<glyph unicode="&#xe1;" horiz-adv-x="1159" d="M76 395q0 150 68 285.5t193.5 222t276.5 86.5q136 0 233.5 -68t133.5 -171l25 122q12 57 30 80t49 23q9 0 18 -2.5t14 -5.5t5 -4l-164 -768q-16 -79 7 -108.5t84 -31.5q1 0 1.5 -7t-2 -17t-9 -20.5t-21.5 -17.5t-37 -7q-74 0 -108.5 53.5t-12.5 153.5v4 q-67 -105 -167.5 -161t-215.5 -56q-172 0 -286.5 111t-114.5 304zM176 397q0 -152 88.5 -241.5t224.5 -89.5q153 0 273.5 110t150.5 258l39 176q-28 133 -115 213t-229 80q-95 0 -177.5 -44t-137.5 -116t-86 -162.5t-31 -183.5zM586 1120q-3 4 30 38.5t98.5 88.5t135.5 99 q54 40 94 40q19 0 31 -11.5t12 -28.5q0 -20 -19 -40t-67 -53q-74 -46 -150.5 -80t-119.5 -46.5t-45 -6.5z" />
<glyph unicode="&#xe2;" horiz-adv-x="1159" d="M76 395q0 150 68 285.5t193.5 222t276.5 86.5q136 0 233.5 -68t133.5 -171l25 122q12 57 30 80t49 23q9 0 18 -2.5t14 -5.5t5 -4l-164 -768q-16 -79 7 -108.5t84 -31.5q1 0 1.5 -7t-2 -17t-9 -20.5t-21.5 -17.5t-37 -7q-74 0 -108.5 53.5t-12.5 153.5v4 q-67 -105 -167.5 -161t-215.5 -56q-172 0 -286.5 111t-114.5 304zM176 397q0 -152 88.5 -241.5t224.5 -89.5q153 0 273.5 110t150.5 258l39 176q-28 133 -115 213t-229 80q-95 0 -177.5 -44t-137.5 -116t-86 -162.5t-31 -183.5zM453 1141q0 25 30.5 62.5t104.5 103.5 q43 39 83.5 71.5t61.5 47t25 14.5q3 0 17.5 -14t40.5 -46t50 -69q76 -115 76 -152q0 -21 -10 -32t-25 -11q-20 0 -40 25.5t-62 101.5q-40 67 -60 109l-106 -111q-117 -125 -158 -125q-28 0 -28 25z" />
<glyph unicode="&#xe3;" horiz-adv-x="1159" d="M76 395q0 150 68 285.5t193.5 222t276.5 86.5q136 0 233.5 -68t133.5 -171l25 122q12 57 30 80t49 23q9 0 18 -2.5t14 -5.5t5 -4l-164 -768q-16 -79 7 -108.5t84 -31.5q1 0 1.5 -7t-2 -17t-9 -20.5t-21.5 -17.5t-37 -7q-74 0 -108.5 53.5t-12.5 153.5v4 q-67 -105 -167.5 -161t-215.5 -56q-172 0 -286.5 111t-114.5 304zM176 397q0 -152 88.5 -241.5t224.5 -89.5q153 0 273.5 110t150.5 258l39 176q-28 133 -115 213t-229 80q-95 0 -177.5 -44t-137.5 -116t-86 -162.5t-31 -183.5zM424 1139q18 94 63 148t113 54 q46 0 79.5 -22.5t68.5 -63.5q28 -33 46.5 -46t41.5 -13q28 0 46.5 21.5t32.5 64.5q15 47 48 47q17 0 34 -14q-16 -94 -60 -147.5t-114 -53.5q-47 0 -80.5 22t-66.5 62q-30 33 -48 46t-40 13q-55 0 -80 -86q-15 -47 -49 -47q-16 0 -35 15z" />
<glyph unicode="&#xe4;" horiz-adv-x="1159" d="M76 395q0 150 68 285.5t193.5 222t276.5 86.5q136 0 233.5 -68t133.5 -171l25 122q12 57 30 80t49 23q9 0 18 -2.5t14 -5.5t5 -4l-164 -768q-16 -79 7 -108.5t84 -31.5q1 0 1.5 -7t-2 -17t-9 -20.5t-21.5 -17.5t-37 -7q-74 0 -108.5 53.5t-12.5 153.5v4 q-67 -105 -167.5 -161t-215.5 -56q-172 0 -286.5 111t-114.5 304zM176 397q0 -152 88.5 -241.5t224.5 -89.5q153 0 273.5 110t150.5 258l39 176q-28 133 -115 213t-229 80q-95 0 -177.5 -44t-137.5 -116t-86 -162.5t-31 -183.5zM465 1176q0 39 20.5 65.5t57.5 26.5 q78 0 78 -60q0 -39 -21.5 -65.5t-58.5 -26.5q-76 0 -76 60zM791 1176q0 39 20.5 65.5t58.5 26.5q76 0 76 -60q0 -39 -21 -65.5t-59 -26.5q-75 0 -75 60z" />
<glyph unicode="&#xe5;" horiz-adv-x="1159" d="M76 395q0 150 68 285.5t193.5 222t276.5 86.5q136 0 233.5 -68t133.5 -171l25 122q12 57 30 80t49 23q9 0 18 -2.5t14 -5.5t5 -4l-164 -768q-16 -79 7 -108.5t84 -31.5q1 0 1.5 -7t-2 -17t-9 -20.5t-21.5 -17.5t-37 -7q-74 0 -108.5 53.5t-12.5 153.5v4 q-67 -105 -167.5 -161t-215.5 -56q-172 0 -286.5 111t-114.5 304zM176 397q0 -152 88.5 -241.5t224.5 -89.5q153 0 273.5 110t150.5 258l39 176q-28 133 -115 213t-229 80q-95 0 -177.5 -44t-137.5 -116t-86 -162.5t-31 -183.5zM510 1307q0 103 71.5 179.5t174.5 76.5 q94 0 150.5 -56t56.5 -137q0 -103 -71.5 -179.5t-174.5 -76.5q-94 0 -150.5 56t-56.5 137zM588 1313q0 -52 36 -90.5t95 -38.5q70 0 118 55t48 127q0 52 -36 90.5t-95 38.5q-70 0 -118 -55t-48 -127z" />
<glyph unicode="&#xe6;" horiz-adv-x="1740" d="M27 221q0 122 71 193q76 78 240.5 113.5t443.5 35.5h31l4 19q31 153 -44 236t-238 83q-217 0 -324 -153q-2 -2 -12 4t-20.5 19t-10.5 30q0 34 45 76q112 112 332 112q158 0 255.5 -71.5t106.5 -206.5q75 131 198 204.5t277 73.5q153 0 242.5 -68.5t89.5 -175.5 q0 -56 -15.5 -98.5t-52 -80t-97.5 -60t-153 -38.5t-218 -15t-291 10q-6 -44 -6 -86q0 -142 82.5 -228t232.5 -86q221 0 338 168q4 2 16.5 -2.5t24.5 -16.5t12 -28q0 -33 -47 -80q-121 -124 -348 -124q-159 0 -256.5 74t-124.5 200q-58 -129 -174 -201.5t-270 -72.5t-247 65 t-93 176zM127 229q0 -75 70.5 -121.5t183.5 -46.5q138 0 248.5 86t142.5 228l25 110h-56q-257 -6 -381.5 -32t-183.5 -84q-49 -52 -49 -140zM907 545q145 -8 253.5 -8.5t187 12t130.5 30t82.5 45t43 56t12.5 63.5q0 65 -63 112.5t-175 47.5q-162 0 -290.5 -101t-180.5 -257z " />
<glyph unicode="&#xe7;" horiz-adv-x="1048" d="M74 422q0 147 70 277t195 210t273 80q208 0 324 -121q40 -40 61 -87t21 -83q0 -27 -18.5 -43.5t-34.5 -16t-21 2.5q-16 112 -102.5 186t-227.5 74q-125 0 -227.5 -68.5t-157.5 -176.5t-55 -228q0 -164 99 -262t259 -98q113 0 195 53.5t133 138.5q3 3 16.5 -1t27.5 -16 t14 -30q0 -40 -64 -107q-56 -54 -141 -90t-189 -36q-127 0 -227.5 50.5t-161.5 152.5t-61 239zM242 -418q14 20 36 54t69 105t78 118t30 47q3 3 24 -1t42.5 -18.5t21.5 -36.5q0 -33 -35 -71l-221 -228q-9 -10 -30 5t-15 26z" />
<glyph unicode="&#xe8;" horiz-adv-x="1034" d="M70 371q0 126 46.5 240.5t126.5 197t192.5 131.5t238.5 49q156 0 246 -68.5t90 -175.5q0 -69 -26.5 -120t-89 -91.5t-175 -61.5t-273.5 -21q-124 0 -270 14q-6 -43 -6 -88q0 -142 82.5 -228t236.5 -86q220 0 340 168q2 2 14.5 -2.5t25 -16.5t12.5 -28q0 -36 -47 -80 q-121 -124 -349 -124q-197 0 -306 108t-109 283zM197 549q130 -17 241 -17q471 0 471 211q0 65 -63 112.5t-178 47.5q-162 0 -290.5 -99.5t-180.5 -254.5zM442 1362q0 18 14 30.5t33 12.5q32 0 89 -51q59 -50 109.5 -106.5t73 -90.5t17.5 -37q-6 -6 -43.5 8t-101.5 51.5 t-125 88.5q-39 33 -52.5 53t-13.5 41z" />
<glyph unicode="&#xe9;" horiz-adv-x="1034" d="M70 371q0 126 46.5 240.5t126.5 197t192.5 131.5t238.5 49q156 0 246 -68.5t90 -175.5q0 -69 -26.5 -120t-89 -91.5t-175 -61.5t-273.5 -21q-124 0 -270 14q-6 -43 -6 -88q0 -142 82.5 -228t236.5 -86q220 0 340 168q2 2 14.5 -2.5t25 -16.5t12.5 -28q0 -36 -47 -80 q-121 -124 -349 -124q-197 0 -306 108t-109 283zM197 549q130 -17 241 -17q471 0 471 211q0 65 -63 112.5t-178 47.5q-162 0 -290.5 -99.5t-180.5 -254.5zM575 1120q-3 4 30.5 38.5t99 88.5t135.5 99q54 40 94 40q19 0 31 -11.5t12 -28.5q0 -20 -19 -40t-67 -53 q-74 -46 -151 -80t-120 -46.5t-45 -6.5z" />
<glyph unicode="&#xea;" horiz-adv-x="1034" d="M70 371q0 126 46.5 240.5t126.5 197t192.5 131.5t238.5 49q156 0 246 -68.5t90 -175.5q0 -69 -26.5 -120t-89 -91.5t-175 -61.5t-273.5 -21q-124 0 -270 14q-6 -43 -6 -88q0 -142 82.5 -228t236.5 -86q220 0 340 168q2 2 14.5 -2.5t25 -16.5t12.5 -28q0 -36 -47 -80 q-121 -124 -349 -124q-197 0 -306 108t-109 283zM197 549q130 -17 241 -17q471 0 471 211q0 65 -63 112.5t-178 47.5q-162 0 -290.5 -99.5t-180.5 -254.5zM463 1141q0 25 30.5 62.5t104.5 103.5q43 39 83.5 71.5t61.5 47t25 14.5q3 0 17.5 -14t41 -46t50.5 -69 q75 -113 75 -152q0 -21 -10 -32t-24 -11q-20 0 -40.5 25.5t-62.5 101.5q-34 57 -59 109l-107 -111q-117 -125 -157 -125q-13 0 -21 7t-8 18z" />
<glyph unicode="&#xeb;" horiz-adv-x="1034" d="M70 371q0 126 46.5 240.5t126.5 197t192.5 131.5t238.5 49q156 0 246 -68.5t90 -175.5q0 -69 -26.5 -120t-89 -91.5t-175 -61.5t-273.5 -21q-124 0 -270 14q-6 -43 -6 -88q0 -142 82.5 -228t236.5 -86q220 0 340 168q2 2 14.5 -2.5t25 -16.5t12.5 -28q0 -36 -47 -80 q-121 -124 -349 -124q-197 0 -306 108t-109 283zM197 549q130 -17 241 -17q471 0 471 211q0 65 -63 112.5t-178 47.5q-162 0 -290.5 -99.5t-180.5 -254.5zM473 1176q0 39 20.5 65.5t57.5 26.5q78 0 78 -60q0 -39 -21.5 -65.5t-58.5 -26.5q-76 0 -76 60zM799 1176 q0 39 21 65.5t59 26.5q75 0 75 -60q0 -39 -21 -65.5t-59 -26.5q-75 0 -75 60z" />
<glyph unicode="&#xec;" horiz-adv-x="401" d="M82 1362q0 18 14 30.5t33 12.5q31 0 88 -51q59 -50 109.5 -106t73.5 -90.5t18 -37.5q-6 -6 -43.5 8t-102 51.5t-125.5 88.5q-38 32 -51.5 52.5t-13.5 41.5zM90 193l156 735q6 26 16 36.5t29 10.5h12q51 0 41 -47l-156 -733q-17 -80 7 -110t90 -30q1 0 1 -7t-2.5 -17 t-9 -20.5t-22 -17.5t-37.5 -7q-78 0 -112.5 52.5t-12.5 154.5z" />
<glyph unicode="&#xed;" horiz-adv-x="401" d="M90 193l156 735q6 26 16 36.5t29 10.5h12q51 0 41 -47l-156 -733q-17 -80 7 -110t90 -30q1 0 1 -7t-2.5 -17t-9 -20.5t-22 -17.5t-37.5 -7q-78 0 -112.5 52.5t-12.5 154.5zM219 1120q-3 4 30 38.5t98.5 88.5t135.5 99q54 40 95 40q19 0 31 -11.5t12 -28.5q0 -20 -19 -40 t-67 -53q-74 -46 -151 -80t-120 -46.5t-45 -6.5z" />
<glyph unicode="&#xee;" horiz-adv-x="401" d="M90 193l156 735q6 26 16 36.5t29 10.5h12q51 0 41 -47l-156 -733q-17 -80 7 -110t90 -30q1 0 1 -7t-2.5 -17t-9 -20.5t-22 -17.5t-37.5 -7q-78 0 -112.5 52.5t-12.5 154.5zM98 1141q0 25 30.5 62.5t104.5 103.5q43 39 83.5 71.5t61.5 47t25 14.5q3 0 17.5 -14t41 -46 t50.5 -69q76 -115 76 -152q0 -21 -10 -32t-25 -11q-20 0 -40 25.5t-62 101.5q-40 67 -60 109l-106 -111q-117 -125 -158 -125q-13 0 -21 7t-8 18z" />
<glyph unicode="&#xef;" horiz-adv-x="401" d="M90 193l156 735q6 26 16 36.5t29 10.5h12q51 0 41 -47l-156 -733q-17 -80 7 -110t90 -30q1 0 1 -7t-2.5 -17t-9 -20.5t-22 -17.5t-37.5 -7q-78 0 -112.5 52.5t-12.5 154.5zM111 1176q0 39 20 65.5t57 26.5q78 0 78 -60q0 -39 -21.5 -65.5t-58.5 -26.5q-75 0 -75 60z M436 1176q0 39 21 65.5t59 26.5q76 0 76 -60q0 -39 -21 -65.5t-59 -26.5q-76 0 -76 60z" />
<glyph unicode="&#xf0;" horiz-adv-x="1101" d="M55 365q0 136 61 249t177.5 180.5t265.5 67.5q137 0 246 -67t151 -154q2 23 2 68q0 276 -200 456l-320 -178q-3 -2 -9.5 2.5t-12.5 17t-6 27.5q0 36 57 66l217 117q-131 90 -326 106q-1 3 -2 21t19.5 38.5t56.5 20.5q67 0 163.5 -34.5t182.5 -102.5l260 141q3 2 10 -2.5 t13 -16.5t6 -26q0 -38 -57 -68l-164 -86q209 -209 209 -508q0 -96 -19.5 -193t-63.5 -192.5t-107.5 -169.5t-159.5 -119.5t-211 -45.5q-194 0 -316.5 102.5t-122.5 282.5zM154 362q0 -138 95 -218.5t243 -80.5q96 0 174.5 38t131.5 105t87 150t51 181q-29 88 -136.5 163.5 t-242.5 75.5q-131 0 -223.5 -56t-136 -148t-43.5 -210z" />
<glyph unicode="&#xf1;" d="M47 41l199 928q0 6 20 6q34 0 49 -23t4 -84l-20 -92q64 100 155.5 156.5t204.5 56.5q152 0 242 -88.5t90 -232.5q0 -87 -57 -310q-47 -172 -47 -223q0 -43 22.5 -61t67.5 -21q1 0 1.5 -7t-2.5 -16.5t-9.5 -19.5t-21 -17t-34.5 -7q-55 0 -89 36t-34 99q0 62 50 250 q53 198 53 280q0 115 -65 180.5t-179 65.5q-124 0 -213 -64.5t-162 -191.5l-127 -600q-11 -47 -45 -47h-12q-51 0 -41 47zM416 1139q18 94 63 148t113 54q46 0 79 -22.5t68 -63.5q28 -33 46.5 -46t41.5 -13q52 0 80 86q8 24 18.5 35.5t28.5 11.5t35 -14q-16 -94 -60 -147.5 t-114 -53.5q-47 0 -80.5 22t-66.5 62q-30 33 -48 46t-40 13q-55 0 -80 -86q-15 -47 -49 -47q-16 0 -35 15z" />
<glyph unicode="&#xf2;" horiz-adv-x="1132" d="M76 424q0 152 71 281.5t196 206.5t273 77q189 0 316 -125.5t127 -318.5q0 -151 -72 -281t-196.5 -207t-272.5 -77q-190 0 -316 125t-126 319zM174 426q0 -154 99 -257t251 -103q177 0 305.5 140.5t128.5 336.5q0 156 -98 258t-250 102q-116 0 -216.5 -64t-160 -174 t-59.5 -239zM416 1362q0 18 14 30.5t33 12.5q31 0 88 -51q59 -50 109.5 -106t73.5 -90.5t18 -37.5q-6 -6 -43.5 8t-102 51.5t-125.5 88.5q-38 32 -51.5 52.5t-13.5 41.5z" />
<glyph unicode="&#xf3;" horiz-adv-x="1132" d="M76 424q0 152 71 281.5t196 206.5t273 77q189 0 316 -125.5t127 -318.5q0 -151 -72 -281t-196.5 -207t-272.5 -77q-190 0 -316 125t-126 319zM174 426q0 -154 99 -257t251 -103q177 0 305.5 140.5t128.5 336.5q0 156 -98 258t-250 102q-116 0 -216.5 -64t-160 -174 t-59.5 -239zM553 1120q-3 4 30 38.5t98.5 88.5t135.5 99q54 40 94 40q19 0 31 -11.5t12 -28.5q0 -20 -19 -40t-67 -53q-74 -46 -150.5 -80t-119.5 -46.5t-45 -6.5z" />
<glyph unicode="&#xf4;" horiz-adv-x="1132" d="M76 424q0 152 71 281.5t196 206.5t273 77q189 0 316 -125.5t127 -318.5q0 -151 -72 -281t-196.5 -207t-272.5 -77q-190 0 -316 125t-126 319zM174 426q0 -154 99 -257t251 -103q177 0 305.5 140.5t128.5 336.5q0 156 -98 258t-250 102q-116 0 -216.5 -64t-160 -174 t-59.5 -239zM428 1141q0 25 30.5 62.5t104.5 103.5q43 39 83.5 71.5t61.5 47t25 14.5q3 0 17.5 -14t41 -46t50.5 -69q76 -115 76 -152q0 -21 -10 -32t-25 -11q-20 0 -40.5 25.5t-62.5 101.5q-34 57 -59 109l-107 -111q-117 -125 -157 -125q-13 0 -21 7t-8 18z" />
<glyph unicode="&#xf5;" horiz-adv-x="1132" d="M76 424q0 152 71 281.5t196 206.5t273 77q189 0 316 -125.5t127 -318.5q0 -151 -72 -281t-196.5 -207t-272.5 -77q-190 0 -316 125t-126 319zM174 426q0 -154 99 -257t251 -103q177 0 305.5 140.5t128.5 336.5q0 156 -98 258t-250 102q-116 0 -216.5 -64t-160 -174 t-59.5 -239zM399 1139q18 94 63 148t113 54q46 0 79.5 -22.5t68.5 -63.5q28 -33 46.5 -46t41.5 -13q52 0 80 86q8 24 18.5 35.5t28.5 11.5t35 -14q-16 -94 -60 -147.5t-114 -53.5q-79 0 -148 84q-30 33 -48 46t-40 13q-55 0 -80 -86q-15 -47 -49 -47q-16 0 -35 15z" />
<glyph unicode="&#xf6;" horiz-adv-x="1132" d="M76 424q0 152 71 281.5t196 206.5t273 77q189 0 316 -125.5t127 -318.5q0 -151 -72 -281t-196.5 -207t-272.5 -77q-190 0 -316 125t-126 319zM174 426q0 -154 99 -257t251 -103q177 0 305.5 140.5t128.5 336.5q0 156 -98 258t-250 102q-116 0 -216.5 -64t-160 -174 t-59.5 -239zM440 1176q0 39 20.5 65.5t57.5 26.5q78 0 78 -60q0 -39 -21.5 -65.5t-58.5 -26.5q-76 0 -76 60zM766 1176q0 39 21 65.5t59 26.5q76 0 76 -60q0 -39 -21 -65.5t-59 -26.5q-76 0 -76 60z" />
<glyph unicode="&#xf7;" horiz-adv-x="1228" d="M195 541l2 10q6 23 18 32t39 9h764q27 0 35.5 -9.5t3.5 -31.5l-4 -10q-5 -21 -17 -31t-39 -10h-764q-50 0 -38 41zM481 221q0 58 27 82q18 21 57 21q35 0 55.5 -14.5t20.5 -39.5q0 -57 -27 -84q-20 -20 -57 -20q-35 0 -55.5 14.5t-20.5 40.5zM608 823q0 55 27 82 q21 21 57 21q37 0 57.5 -15t20.5 -41q0 -55 -27 -82q-20 -20 -57 -20t-57.5 14.5t-20.5 40.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1132" d="M-10 2q0 31 43 66l117 104q-74 110 -74 252q0 152 71 281.5t196 206.5t273 77q186 0 314 -125l162 148q3 2 13 -3.5t19 -18t9 -25.5q0 -30 -41 -68l-109 -96q76 -113 76 -256q0 -151 -72 -281t-196.5 -207t-272.5 -77q-189 0 -315 126l-170 -151q-3 -3 -13.5 2.5t-20 18 t-9.5 26.5zM174 426q0 -103 51 -193l635 566q-96 104 -250 104q-116 0 -216.5 -64t-160 -174t-59.5 -239zM270 172q98 -106 254 -106q177 0 305.5 140.5t128.5 336.5q0 109 -51 194z" />
<glyph unicode="&#xf9;" d="M121 434l104 494q6 27 16 37t29 10h13q51 0 41 -47l-107 -500q-36 -166 32 -261t210 -95q117 0 203.5 60t158.5 185l129 611q6 27 16 37t29 10h13q49 0 41 -47l-156 -733q-18 -79 4.5 -108.5t83.5 -31.5q1 0 1.5 -7t-2 -17t-9 -20.5t-21.5 -17.5t-37 -7q-72 0 -106 50 t-14 148q-59 -97 -150 -150.5t-199 -53.5q-187 0 -277 121.5t-46 332.5zM403 1356q0 18 14.5 30.5t33.5 12.5q31 0 88 -51q59 -50 109.5 -106.5t73 -90.5t17.5 -37q-6 -6 -43.5 8t-101.5 51.5t-125 88.5q-39 33 -52.5 53t-13.5 41z" />
<glyph unicode="&#xfa;" d="M121 434l104 494q6 27 16 37t29 10h13q51 0 41 -47l-107 -500q-36 -166 32 -261t210 -95q117 0 203.5 60t158.5 185l129 611q6 27 16 37t29 10h13q49 0 41 -47l-156 -733q-18 -79 4.5 -108.5t83.5 -31.5q1 0 1.5 -7t-2 -17t-9 -20.5t-21.5 -17.5t-37 -7q-72 0 -106 50 t-14 148q-59 -97 -150 -150.5t-199 -53.5q-187 0 -277 121.5t-46 332.5zM549 1114q-3 4 30 38.5t98.5 88t135.5 98.5q55 41 94 41q19 0 31 -12t12 -29q0 -20 -18.5 -39.5t-67.5 -52.5q-74 -46 -150.5 -80t-119.5 -46.5t-45 -6.5z" />
<glyph unicode="&#xfb;" d="M121 434l104 494q6 27 16 37t29 10h13q51 0 41 -47l-107 -500q-36 -166 32 -261t210 -95q117 0 203.5 60t158.5 185l129 611q6 27 16 37t29 10h13q49 0 41 -47l-156 -733q-18 -79 4.5 -108.5t83.5 -31.5q1 0 1.5 -7t-2 -17t-9 -20.5t-21.5 -17.5t-37 -7q-72 0 -106 50 t-14 148q-59 -97 -150 -150.5t-199 -53.5q-187 0 -277 121.5t-46 332.5zM428 1135q0 25 30.5 61.5t104.5 103.5q43 39 83.5 72t62 47.5t24.5 14.5t17.5 -14t41 -46t50.5 -69q76 -115 76 -152q0 -21 -10 -32t-25 -11q-20 0 -40.5 25.5t-62.5 101.5q-34 57 -59 109l-107 -111 q-117 -125 -157 -125q-13 0 -21 7t-8 18z" />
<glyph unicode="&#xfc;" d="M121 434l104 494q6 27 16 37t29 10h13q51 0 41 -47l-107 -500q-36 -166 32 -261t210 -95q117 0 203.5 60t158.5 185l129 611q6 27 16 37t29 10h13q49 0 41 -47l-156 -733q-18 -79 4.5 -108.5t83.5 -31.5q1 0 1.5 -7t-2 -17t-9 -20.5t-21.5 -17.5t-37 -7q-72 0 -106 50 t-14 148q-59 -97 -150 -150.5t-199 -53.5q-187 0 -277 121.5t-46 332.5zM440 1169q0 39 20.5 66t57.5 27q78 0 78 -60q0 -39 -21.5 -65.5t-58.5 -26.5q-76 0 -76 59zM766 1169q0 39 21 66t59 27q76 0 76 -60q0 -39 -21 -65.5t-59 -26.5q-76 0 -76 59z" />
<glyph unicode="&#xfd;" horiz-adv-x="927" d="M-184 -319q0 15 9 28.5t19 19t13 3.5q18 -23 49.5 -37t58.5 -14q60 0 122.5 52.5t160.5 196.5l82 123q-47 5 -54 66l-79 583q-15 106 -51 151.5t-101 43.5q-1 0 -1.5 8t2 20t9 24t24 20t42.5 8q52 0 86 -37q66 -66 86 -246l74 -588l542 867q4 6 28 -1t33.5 -31t-24.5 -85 l-604 -946q-118 -187 -196.5 -253.5t-174.5 -66.5q-92 0 -137 48q-18 18 -18 43zM487 1094q-3 4 30.5 38.5t99 88t135.5 98.5q55 41 94 41q19 0 31 -12t12 -29q0 -20 -18.5 -39.5t-67.5 -52.5q-74 -46 -151 -80t-120 -46.5t-45 -6.5z" />
<glyph unicode="&#xfe;" horiz-adv-x="1155" d="M-37 -354l361 1695q5 26 15 37t30 11h12q49 0 41 -48l-121 -559q142 207 373 207q83 0 156 -28.5t129.5 -81.5t89 -135.5t32.5 -184.5q0 -148 -65.5 -280t-187 -215.5t-269.5 -83.5q-142 0 -241.5 73.5t-133.5 175.5l-123 -583q-11 -47 -45 -47h-12q-51 0 -41 47z M229 446q-13 -69 4 -137t59 -122t113.5 -87.5t159.5 -33.5q94 0 174 42t132.5 111.5t82 158t29.5 181.5q0 157 -92 250.5t-227 93.5q-127 0 -231 -74t-161 -190z" />
<glyph unicode="&#xff;" horiz-adv-x="927" d="M-184 -319q0 15 9 28.5t19 19t13 3.5q18 -23 49.5 -37t58.5 -14q60 0 122.5 52.5t160.5 196.5l82 123q-47 5 -54 66l-79 583q-15 106 -51 151.5t-101 43.5q-1 0 -1.5 8t2 20t9 24t24 20t42.5 8q52 0 86 -37q66 -66 86 -246l74 -588l542 867q4 6 28 -1t33.5 -31t-24.5 -85 l-604 -946q-118 -187 -196.5 -253.5t-174.5 -66.5q-92 0 -137 48q-18 18 -18 43zM346 1176q0 39 20.5 65.5t57.5 26.5q78 0 78 -60q0 -39 -21.5 -65.5t-58.5 -26.5q-76 0 -76 60zM672 1176q0 39 21 65.5t59 26.5q75 0 75 -60q0 -39 -20.5 -65.5t-58.5 -26.5q-76 0 -76 60z " />
<glyph unicode="&#x152;" horiz-adv-x="2068" d="M127 553q0 167 61 315.5t161 251.5t231.5 163t269.5 60q78 0 149 -18h1100q47 0 47 -25q0 -12 -6.5 -30t-13.5 -27q-8 -10 -41 -10h-901q115 -68 179 -188.5t64 -270.5v-49h400q47 0 47 -25q0 -32 -19 -55q-13 -10 -38 -10h-402q-29 -179 -131.5 -322t-245.5 -219h826 q47 0 47 -26q0 -12 -6.5 -30.5t-14.5 -27.5q-10 -10 -41 -10h-1009q-72 -16 -135 -16q-174 0 -306 74t-202 202.5t-70 292.5zM236 559q0 -137 55.5 -246.5t166 -175t257.5 -65.5q115 0 224.5 53t193.5 144t135 222t51 277q0 211 -128.5 349t-350.5 138q-115 0 -224.5 -53 t-193.5 -144t-135 -222t-51 -277z" />
<glyph unicode="&#x153;" horiz-adv-x="1896" d="M76 420q0 154 69 284.5t192.5 207.5t272.5 77q156 0 269.5 -90.5t144.5 -241.5q70 149 209 240.5t305 91.5q153 0 242.5 -68.5t89.5 -175.5q0 -54 -15.5 -97t-53.5 -80.5t-99 -62.5t-155 -39.5t-218 -14.5q-156 0 -287 12q-6 -44 -6 -86q0 -142 82.5 -228t233.5 -86 q221 0 338 168q2 2 14.5 -2.5t24.5 -16.5t12 -28q0 -35 -45 -80q-121 -124 -348 -124q-155 0 -254.5 77.5t-126.5 208.5q-66 -130 -187.5 -208t-269.5 -78q-186 0 -310 122t-124 318zM174 422q0 -154 98.5 -255t247.5 -101q178 0 304 140.5t126 340.5q0 153 -97.5 254.5 t-244.5 101.5q-181 0 -307.5 -140.5t-126.5 -340.5zM1065 549q134 -14 254 -14q129 0 221 17.5t140.5 48t70 65.5t21.5 77q0 65 -64 112.5t-176 47.5q-159 0 -287 -100.5t-180 -253.5z" />
<glyph unicode="&#x178;" d="M186 1292q-3 9 17.5 25t48.5 13.5t45 -40.5l289 -631l575 674q5 5 30 -5.5t32 -37.5t-43 -90l-559 -639l-111 -520q-6 -26 -18.5 -36.5t-36.5 -10.5h-15q-22 0 -30 10.5t-2 36.5l112 530zM492 1511q0 39 20.5 66t56.5 27q78 0 78 -60q0 -39 -21.5 -65.5t-58.5 -26.5 q-75 0 -75 59zM817 1511q0 39 21 66t59 27q76 0 76 -60q0 -39 -21 -65.5t-59 -26.5q-76 0 -76 59z" />
<glyph unicode="&#x2c6;" horiz-adv-x="1024" d="M410 1141q0 25 30.5 62.5t104.5 103.5q43 39 83.5 71.5t61.5 47t25 14.5q3 0 17.5 -14t40.5 -46t50 -69q76 -115 76 -152q0 -21 -10 -32t-25 -11q-20 0 -40 25.5t-62 101.5q-40 67 -60 109l-106 -111q-117 -125 -158 -125q-28 0 -28 25z" />
<glyph unicode="&#x2dc;" horiz-adv-x="1021" d="M381 1139q18 94 63 148t113 54q46 0 79.5 -22.5t68.5 -63.5q28 -33 46.5 -46t41.5 -13q28 0 46.5 21.5t32.5 64.5q15 47 48 47q16 0 34 -14q-16 -94 -60 -147.5t-114 -53.5q-47 0 -80.5 22t-66.5 62q-30 33 -48 46t-40 13q-55 0 -80 -86q-15 -47 -49 -47q-16 0 -35 15z " />
<glyph unicode="&#x2000;" horiz-adv-x="898" />
<glyph unicode="&#x2001;" horiz-adv-x="1796" />
<glyph unicode="&#x2002;" horiz-adv-x="898" />
<glyph unicode="&#x2003;" horiz-adv-x="1796" />
<glyph unicode="&#x2004;" horiz-adv-x="598" />
<glyph unicode="&#x2005;" horiz-adv-x="449" />
<glyph unicode="&#x2006;" horiz-adv-x="299" />
<glyph unicode="&#x2007;" horiz-adv-x="299" />
<glyph unicode="&#x2008;" horiz-adv-x="224" />
<glyph unicode="&#x2009;" horiz-adv-x="359" />
<glyph unicode="&#x200a;" horiz-adv-x="99" />
<glyph unicode="&#x2010;" horiz-adv-x="718" d="M90 489q0 11 6 28t13 24q10 12 38 12h447q47 0 47 -25q0 -35 -18 -53q-15 -12 -39 -12h-449q-45 0 -45 26z" />
<glyph unicode="&#x2011;" horiz-adv-x="718" d="M90 489q0 11 6 28t13 24q10 12 38 12h447q47 0 47 -25q0 -35 -18 -53q-15 -12 -39 -12h-449q-45 0 -45 26z" />
<glyph unicode="&#x2012;" horiz-adv-x="718" d="M90 489q0 11 6 28t13 24q10 12 38 12h447q47 0 47 -25q0 -35 -18 -53q-15 -12 -39 -12h-449q-45 0 -45 26z" />
<glyph unicode="&#x2013;" horiz-adv-x="1026" d="M90 489q0 11 6 28t13 24q12 12 36 12h758q45 0 45 -25q0 -35 -18 -53q-10 -10 -37 -10h-758q-45 0 -45 24z" />
<glyph unicode="&#x2014;" horiz-adv-x="1361" d="M90 489q0 11 6 28t13 24q12 12 36 12h1094q43 0 43 -25q0 -38 -18 -53q-8 -10 -37 -10h-1092q-45 0 -45 24z" />
<glyph unicode="&#x2018;" horiz-adv-x="393" d="M186 948q0 30 31 72l242 332q8 8 30.5 -6t16.5 -27l-225 -422q-5 -1 -26.5 -1t-45 14.5t-23.5 37.5z" />
<glyph unicode="&#x2019;" horiz-adv-x="393" d="M158 926l225 422q5 1 25.5 0.5t43.5 -15t23 -37.5q0 -32 -29 -71l-241 -332q-8 -8 -31 6t-16 27z" />
<glyph unicode="&#x201a;" horiz-adv-x="391" d="M-129 -262l225 422q5 1 25.5 1t43.5 -14.5t23 -37.5q0 -35 -28 -70l-242 -334q-8 -8 -31 6t-16 27z" />
<glyph unicode="&#x201c;" horiz-adv-x="675" d="M186 950q0 30 31 72l242 332q8 8 30.5 -6t16.5 -27l-225 -422q-5 -1 -26.5 -1t-45 14.5t-23.5 37.5zM471 948q0 30 31 72l241 332q7 8 29.5 -6t15.5 -27l-225 -422q-4 -1 -24.5 -1t-44 14.5t-23.5 37.5z" />
<glyph unicode="&#x201d;" horiz-adv-x="675" d="M158 926l225 422q5 1 25.5 0.5t43.5 -15t23 -37.5q0 -32 -29 -71l-241 -332q-8 -8 -31 6t-16 27zM442 924q14 27 37.5 70.5t73 136t82 154t31.5 61.5q5 1 26 0.5t44.5 -15t23.5 -37.5q0 -29 -31 -71l-242 -332q-8 -8 -30 6t-15 27z" />
<glyph unicode="&#x201e;" horiz-adv-x="675" d="M-129 -262l225 422q5 1 25.5 1t43.5 -14.5t23 -37.5q0 -35 -28 -70l-242 -334q-8 -8 -31 6t-16 27zM156 -264l225 422q4 1 24.5 0.5t44 -15t23.5 -37.5q0 -30 -31 -69l-241 -334q-7 -8 -29.5 6t-15.5 27z" />
<glyph unicode="&#x2022;" horiz-adv-x="614" d="M135 508q0 75 51.5 127.5t126.5 52.5q73 0 124.5 -52.5t51.5 -127.5t-51.5 -127.5t-124.5 -52.5q-75 0 -126.5 52.5t-51.5 127.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="1175" d="M29 45q0 58 24 82q20 20 60 20q35 0 55 -14t20 -39q0 -58 -26 -84q-20 -20 -58 -20q-75 0 -75 55zM420 45q0 59 26 82q20 20 58 20q35 0 55.5 -14t20.5 -39q0 -57 -27 -84q-17 -20 -57 -20q-36 0 -56 14.5t-20 40.5zM811 45q0 58 27 82q20 20 57 20q36 0 56 -14t20 -39 q0 -56 -25 -84q-20 -20 -59 -20q-35 0 -55.5 14.5t-20.5 40.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="359" />
<glyph unicode="&#x2039;" horiz-adv-x="618" d="M94 506q1 5 26.5 29t77 66t113.5 87q98 71 152 102t82 31q17 0 26 -10.5t9 -26.5q0 -49 -215 -184q-106 -69 -181 -113q45 -38 135 -120q85 -79 110.5 -109t25.5 -53q0 -14 -11.5 -24.5t-29.5 -10.5q-27 0 -67.5 32.5t-110.5 110.5q-42 47 -76.5 91t-50.5 70t-15 32z" />
<glyph unicode="&#x203a;" horiz-adv-x="618" d="M47 207q0 47 213 184q26 16 86 54t94 59q-74 65 -133 121q-87 81 -112 110t-25 51q0 14 12 24.5t29 10.5q29 0 69.5 -32.5t108.5 -110.5q65 -72 105 -127.5t38 -67.5q-2 -5 -27.5 -28t-78 -65.5t-113.5 -86.5q-97 -71 -150 -102t-81 -31q-17 0 -26 10.5t-9 26.5z" />
<glyph unicode="&#x205f;" horiz-adv-x="449" />
<glyph unicode="&#x20ac;" horiz-adv-x="1273" d="M76 485q0 36 18 54q10 10 39 10h154q10 118 45 237h-142q-45 0 -45 25q0 11 6 27.5t13 23.5q12 12 39 12h157q75 200 187 312q160 160 383 160q187 0 299 -115q33 -32 52 -77t19 -81q0 -27 -17 -44t-32 -18t-18 1q-28 117 -108.5 182.5t-204.5 65.5q-174 0 -304 -130 q-97 -97 -155 -256h475q43 0 43 -24q0 -38 -18 -53q-8 -11 -37 -11h-492q-34 -121 -45 -237h479q43 0 43 -25q0 -36 -18 -51q-7 -10 -37 -10h-469q5 -177 99.5 -286t252.5 -109q217 0 344 204q2 2 14 -2.5t23.5 -17t11.5 -29.5q0 -45 -67 -112q-55 -57 -142.5 -94 t-179.5 -37q-216 0 -335.5 128t-122.5 355h-162q-45 0 -45 22z" />
<glyph unicode="&#x2122;" horiz-adv-x="1337" d="M156 1286v6q0 35 37 35h450q37 0 37 -35v-6q0 -33 -37 -33h-184v-514q0 -39 -37 -39h-10q-35 0 -35 39v514h-184q-37 0 -37 33zM784 739v547q0 45 41 45h37q19 0 29 -8t18 -27l164 -344l162 344q9 20 18.5 27.5t28.5 7.5h35q41 0 41 -45v-547q0 -39 -35 -39h-10 q-33 0 -33 39v484l-164 -353q-5 -12 -13 -17t-32 -5q-17 0 -25.5 3t-11 6.5t-6.5 12.5l-166 351v-482q0 -39 -31 -39h-14q-33 0 -33 39z" />
<glyph unicode="&#x25fc;" horiz-adv-x="983" d="M0 0v983h983v-983h-983z" />
<hkern u1="&#x26;" u2="&#x178;" k="18" />
<hkern u1="&#x26;" u2="&#xdd;" k="18" />
<hkern u1="&#x26;" u2="&#xc6;" k="-25" />
<hkern u1="&#x26;" u2="&#xc5;" k="-25" />
<hkern u1="&#x26;" u2="&#xc4;" k="-25" />
<hkern u1="&#x26;" u2="&#xc3;" k="-25" />
<hkern u1="&#x26;" u2="&#xc2;" k="-25" />
<hkern u1="&#x26;" u2="&#xc1;" k="-25" />
<hkern u1="&#x26;" u2="&#xc0;" k="-25" />
<hkern u1="&#x26;" u2="Y" k="18" />
<hkern u1="&#x26;" u2="X" k="-12" />
<hkern u1="&#x26;" u2="W" k="12" />
<hkern u1="&#x26;" u2="V" k="12" />
<hkern u1="&#x26;" u2="T" k="25" />
<hkern u1="&#x26;" u2="J" k="-12" />
<hkern u1="&#x26;" u2="A" k="-25" />
<hkern u1="&#x28;" u2="&#x178;" k="-31" />
<hkern u1="&#x28;" u2="&#xdd;" k="-31" />
<hkern u1="&#x28;" u2="j" k="-92" />
<hkern u1="&#x28;" u2="g" k="-12" />
<hkern u1="&#x28;" u2="Y" k="-31" />
<hkern u1="&#x28;" u2="X" k="-25" />
<hkern u1="&#x28;" u2="W" k="-6" />
<hkern u1="&#x28;" u2="V" k="-18" />
<hkern u1="&#x2a;" u2="&#xc6;" k="37" />
<hkern u1="&#x2a;" u2="&#xc5;" k="37" />
<hkern u1="&#x2a;" u2="&#xc4;" k="37" />
<hkern u1="&#x2a;" u2="&#xc3;" k="37" />
<hkern u1="&#x2a;" u2="&#xc2;" k="37" />
<hkern u1="&#x2a;" u2="&#xc1;" k="37" />
<hkern u1="&#x2a;" u2="&#xc0;" k="37" />
<hkern u1="&#x2a;" u2="A" k="37" />
<hkern u1="&#x2c;" u2="&#x178;" k="31" />
<hkern u1="&#x2c;" u2="&#x153;" k="12" />
<hkern u1="&#x2c;" u2="&#x152;" k="25" />
<hkern u1="&#x2c;" u2="&#xe7;" k="12" />
<hkern u1="&#x2c;" u2="&#xe6;" k="12" />
<hkern u1="&#x2c;" u2="&#xdd;" k="31" />
<hkern u1="&#x2c;" u2="&#xdc;" k="6" />
<hkern u1="&#x2c;" u2="&#xdb;" k="6" />
<hkern u1="&#x2c;" u2="&#xda;" k="6" />
<hkern u1="&#x2c;" u2="&#xd9;" k="6" />
<hkern u1="&#x2c;" u2="&#xd8;" k="25" />
<hkern u1="&#x2c;" u2="&#xd6;" k="25" />
<hkern u1="&#x2c;" u2="&#xd5;" k="25" />
<hkern u1="&#x2c;" u2="&#xd4;" k="25" />
<hkern u1="&#x2c;" u2="&#xd3;" k="25" />
<hkern u1="&#x2c;" u2="&#xd2;" k="25" />
<hkern u1="&#x2c;" u2="&#xc6;" k="-18" />
<hkern u1="&#x2c;" u2="&#xc5;" k="-18" />
<hkern u1="&#x2c;" u2="&#xc4;" k="-18" />
<hkern u1="&#x2c;" u2="&#xc3;" k="-18" />
<hkern u1="&#x2c;" u2="&#xc2;" k="-18" />
<hkern u1="&#x2c;" u2="&#xc1;" k="-18" />
<hkern u1="&#x2c;" u2="&#xc0;" k="-18" />
<hkern u1="&#x2c;" u2="y" k="18" />
<hkern u1="&#x2c;" u2="w" k="6" />
<hkern u1="&#x2c;" u2="v" k="18" />
<hkern u1="&#x2c;" u2="u" k="12" />
<hkern u1="&#x2c;" u2="t" k="25" />
<hkern u1="&#x2c;" u2="r" k="12" />
<hkern u1="&#x2c;" u2="q" k="12" />
<hkern u1="&#x2c;" u2="p" k="12" />
<hkern u1="&#x2c;" u2="o" k="12" />
<hkern u1="&#x2c;" u2="n" k="12" />
<hkern u1="&#x2c;" u2="m" k="12" />
<hkern u1="&#x2c;" u2="e" k="12" />
<hkern u1="&#x2c;" u2="d" k="12" />
<hkern u1="&#x2c;" u2="c" k="12" />
<hkern u1="&#x2c;" u2="a" k="12" />
<hkern u1="&#x2c;" u2="Y" k="31" />
<hkern u1="&#x2c;" u2="W" k="12" />
<hkern u1="&#x2c;" u2="V" k="37" />
<hkern u1="&#x2c;" u2="U" k="6" />
<hkern u1="&#x2c;" u2="T" k="37" />
<hkern u1="&#x2c;" u2="Q" k="25" />
<hkern u1="&#x2c;" u2="O" k="25" />
<hkern u1="&#x2c;" u2="G" k="25" />
<hkern u1="&#x2c;" u2="C" k="25" />
<hkern u1="&#x2c;" u2="A" k="-18" />
<hkern u1="&#x2c;" u2="&#x39;" k="6" />
<hkern u1="&#x2c;" u2="&#x38;" k="10" />
<hkern u1="&#x2c;" u2="&#x36;" k="18" />
<hkern u1="&#x2c;" u2="&#x34;" k="31" />
<hkern u1="&#x2c;" u2="&#x31;" k="37" />
<hkern u1="&#x2c;" u2="&#x30;" k="25" />
<hkern u1="&#x2d;" u2="&#x178;" k="31" />
<hkern u1="&#x2d;" u2="&#x153;" k="6" />
<hkern u1="&#x2d;" u2="&#xe7;" k="6" />
<hkern u1="&#x2d;" u2="&#xe6;" k="6" />
<hkern u1="&#x2d;" u2="&#xdd;" k="31" />
<hkern u1="&#x2d;" u2="&#xc6;" k="-6" />
<hkern u1="&#x2d;" u2="&#xc5;" k="-6" />
<hkern u1="&#x2d;" u2="&#xc4;" k="-6" />
<hkern u1="&#x2d;" u2="&#xc3;" k="-6" />
<hkern u1="&#x2d;" u2="&#xc2;" k="-6" />
<hkern u1="&#x2d;" u2="&#xc1;" k="-6" />
<hkern u1="&#x2d;" u2="&#xc0;" k="-6" />
<hkern u1="&#x2d;" u2="z" k="18" />
<hkern u1="&#x2d;" u2="y" k="6" />
<hkern u1="&#x2d;" u2="x" k="18" />
<hkern u1="&#x2d;" u2="v" k="6" />
<hkern u1="&#x2d;" u2="q" k="6" />
<hkern u1="&#x2d;" u2="o" k="6" />
<hkern u1="&#x2d;" u2="e" k="6" />
<hkern u1="&#x2d;" u2="d" k="6" />
<hkern u1="&#x2d;" u2="c" k="6" />
<hkern u1="&#x2d;" u2="a" k="6" />
<hkern u1="&#x2d;" u2="Z" k="6" />
<hkern u1="&#x2d;" u2="Y" k="31" />
<hkern u1="&#x2d;" u2="X" k="18" />
<hkern u1="&#x2d;" u2="W" k="25" />
<hkern u1="&#x2d;" u2="V" k="31" />
<hkern u1="&#x2d;" u2="T" k="43" />
<hkern u1="&#x2d;" u2="A" k="-6" />
<hkern u1="&#x2d;" u2="&#x37;" k="18" />
<hkern u1="&#x2d;" u2="&#x31;" k="12" />
<hkern u1="&#x2e;" u2="&#x178;" k="31" />
<hkern u1="&#x2e;" u2="&#x153;" k="12" />
<hkern u1="&#x2e;" u2="&#x152;" k="25" />
<hkern u1="&#x2e;" u2="&#xe7;" k="12" />
<hkern u1="&#x2e;" u2="&#xe6;" k="12" />
<hkern u1="&#x2e;" u2="&#xdd;" k="31" />
<hkern u1="&#x2e;" u2="&#xdc;" k="6" />
<hkern u1="&#x2e;" u2="&#xdb;" k="6" />
<hkern u1="&#x2e;" u2="&#xda;" k="6" />
<hkern u1="&#x2e;" u2="&#xd9;" k="6" />
<hkern u1="&#x2e;" u2="&#xd8;" k="25" />
<hkern u1="&#x2e;" u2="&#xd6;" k="25" />
<hkern u1="&#x2e;" u2="&#xd5;" k="25" />
<hkern u1="&#x2e;" u2="&#xd4;" k="25" />
<hkern u1="&#x2e;" u2="&#xd3;" k="25" />
<hkern u1="&#x2e;" u2="&#xd2;" k="25" />
<hkern u1="&#x2e;" u2="&#xc6;" k="-18" />
<hkern u1="&#x2e;" u2="&#xc5;" k="-18" />
<hkern u1="&#x2e;" u2="&#xc4;" k="-18" />
<hkern u1="&#x2e;" u2="&#xc3;" k="-18" />
<hkern u1="&#x2e;" u2="&#xc2;" k="-18" />
<hkern u1="&#x2e;" u2="&#xc1;" k="-18" />
<hkern u1="&#x2e;" u2="&#xc0;" k="-18" />
<hkern u1="&#x2e;" u2="y" k="18" />
<hkern u1="&#x2e;" u2="w" k="6" />
<hkern u1="&#x2e;" u2="v" k="18" />
<hkern u1="&#x2e;" u2="u" k="12" />
<hkern u1="&#x2e;" u2="t" k="25" />
<hkern u1="&#x2e;" u2="r" k="12" />
<hkern u1="&#x2e;" u2="q" k="12" />
<hkern u1="&#x2e;" u2="p" k="12" />
<hkern u1="&#x2e;" u2="o" k="12" />
<hkern u1="&#x2e;" u2="n" k="12" />
<hkern u1="&#x2e;" u2="m" k="12" />
<hkern u1="&#x2e;" u2="e" k="12" />
<hkern u1="&#x2e;" u2="d" k="12" />
<hkern u1="&#x2e;" u2="c" k="12" />
<hkern u1="&#x2e;" u2="a" k="12" />
<hkern u1="&#x2e;" u2="Y" k="31" />
<hkern u1="&#x2e;" u2="W" k="12" />
<hkern u1="&#x2e;" u2="V" k="37" />
<hkern u1="&#x2e;" u2="U" k="6" />
<hkern u1="&#x2e;" u2="T" k="37" />
<hkern u1="&#x2e;" u2="Q" k="25" />
<hkern u1="&#x2e;" u2="O" k="25" />
<hkern u1="&#x2e;" u2="G" k="25" />
<hkern u1="&#x2e;" u2="C" k="25" />
<hkern u1="&#x2e;" u2="A" k="-18" />
<hkern u1="&#x2e;" u2="&#x39;" k="6" />
<hkern u1="&#x2e;" u2="&#x38;" k="10" />
<hkern u1="&#x2e;" u2="&#x36;" k="18" />
<hkern u1="&#x2e;" u2="&#x34;" k="31" />
<hkern u1="&#x2e;" u2="&#x31;" k="37" />
<hkern u1="&#x2e;" u2="&#x30;" k="25" />
<hkern u1="&#x2f;" u2="&#x153;" k="6" />
<hkern u1="&#x2f;" u2="&#x152;" k="-6" />
<hkern u1="&#x2f;" u2="&#xe7;" k="6" />
<hkern u1="&#x2f;" u2="&#xe6;" k="6" />
<hkern u1="&#x2f;" u2="&#xd8;" k="-6" />
<hkern u1="&#x2f;" u2="&#xd6;" k="-6" />
<hkern u1="&#x2f;" u2="&#xd5;" k="-6" />
<hkern u1="&#x2f;" u2="&#xd4;" k="-6" />
<hkern u1="&#x2f;" u2="&#xd3;" k="-6" />
<hkern u1="&#x2f;" u2="&#xd2;" k="-6" />
<hkern u1="&#x2f;" u2="q" k="6" />
<hkern u1="&#x2f;" u2="o" k="6" />
<hkern u1="&#x2f;" u2="g" k="6" />
<hkern u1="&#x2f;" u2="e" k="6" />
<hkern u1="&#x2f;" u2="d" k="6" />
<hkern u1="&#x2f;" u2="c" k="6" />
<hkern u1="&#x2f;" u2="a" k="6" />
<hkern u1="&#x2f;" u2="Q" k="-6" />
<hkern u1="&#x2f;" u2="O" k="-6" />
<hkern u1="&#x2f;" u2="G" k="-6" />
<hkern u1="&#x2f;" u2="C" k="-6" />
<hkern u1="&#x2f;" u2="&#x34;" k="12" />
<hkern u1="&#x2f;" u2="&#x31;" k="-12" />
<hkern u1="&#x30;" u2="&#x2026;" k="25" />
<hkern u1="&#x30;" u2="&#x201e;" k="25" />
<hkern u1="&#x30;" u2="&#x201a;" k="25" />
<hkern u1="&#x30;" u2="&#x37;" k="12" />
<hkern u1="&#x30;" u2="&#x2e;" k="25" />
<hkern u1="&#x30;" u2="&#x2c;" k="25" />
<hkern u1="&#x32;" u2="&#x2014;" k="6" />
<hkern u1="&#x32;" u2="&#x2013;" k="6" />
<hkern u1="&#x32;" u2="&#x34;" k="18" />
<hkern u1="&#x32;" u2="&#x2d;" k="6" />
<hkern u1="&#x33;" u2="&#x2026;" k="12" />
<hkern u1="&#x33;" u2="&#x201e;" k="12" />
<hkern u1="&#x33;" u2="&#x201a;" k="12" />
<hkern u1="&#x33;" u2="&#x37;" k="12" />
<hkern u1="&#x33;" u2="&#x2e;" k="12" />
<hkern u1="&#x33;" u2="&#x2c;" k="12" />
<hkern u1="&#x34;" u2="&#x2122;" k="31" />
<hkern u1="&#x34;" u2="&#xb0;" k="25" />
<hkern u1="&#x34;" u2="&#x37;" k="6" />
<hkern u1="&#x35;" u2="&#x2026;" k="6" />
<hkern u1="&#x35;" u2="&#x201e;" k="6" />
<hkern u1="&#x35;" u2="&#x201a;" k="6" />
<hkern u1="&#x35;" u2="&#x37;" k="6" />
<hkern u1="&#x35;" u2="&#x2e;" k="6" />
<hkern u1="&#x35;" u2="&#x2c;" k="6" />
<hkern u1="&#x36;" u2="&#x2026;" k="6" />
<hkern u1="&#x36;" u2="&#x201e;" k="6" />
<hkern u1="&#x36;" u2="&#x201a;" k="6" />
<hkern u1="&#x36;" u2="&#x37;" k="6" />
<hkern u1="&#x36;" u2="&#x2e;" k="6" />
<hkern u1="&#x36;" u2="&#x2c;" k="6" />
<hkern u1="&#x37;" u2="&#x2122;" k="-25" />
<hkern u1="&#x37;" u2="&#x2026;" k="49" />
<hkern u1="&#x37;" u2="&#x201e;" k="49" />
<hkern u1="&#x37;" u2="&#x201a;" k="49" />
<hkern u1="&#x37;" u2="&#x2014;" k="25" />
<hkern u1="&#x37;" u2="&#x2013;" k="25" />
<hkern u1="&#x37;" u2="&#xa2;" k="25" />
<hkern u1="&#x37;" u2="&#x3b;" k="12" />
<hkern u1="&#x37;" u2="&#x3a;" k="12" />
<hkern u1="&#x37;" u2="&#x39;" k="-6" />
<hkern u1="&#x37;" u2="&#x35;" k="-10" />
<hkern u1="&#x37;" u2="&#x34;" k="37" />
<hkern u1="&#x37;" u2="&#x33;" k="-12" />
<hkern u1="&#x37;" u2="&#x31;" k="-12" />
<hkern u1="&#x37;" u2="&#x30;" k="6" />
<hkern u1="&#x37;" u2="&#x2e;" k="49" />
<hkern u1="&#x37;" u2="&#x2d;" k="25" />
<hkern u1="&#x37;" u2="&#x2c;" k="49" />
<hkern u1="&#x38;" u2="&#x2026;" k="10" />
<hkern u1="&#x38;" u2="&#x201e;" k="10" />
<hkern u1="&#x38;" u2="&#x201a;" k="10" />
<hkern u1="&#x38;" u2="&#x37;" k="10" />
<hkern u1="&#x38;" u2="&#x2e;" k="10" />
<hkern u1="&#x38;" u2="&#x2c;" k="10" />
<hkern u1="&#x39;" u2="&#x2026;" k="18" />
<hkern u1="&#x39;" u2="&#x201e;" k="18" />
<hkern u1="&#x39;" u2="&#x201a;" k="18" />
<hkern u1="&#x39;" u2="&#x37;" k="29" />
<hkern u1="&#x39;" u2="&#x2e;" k="18" />
<hkern u1="&#x39;" u2="&#x2c;" k="18" />
<hkern u1="&#x3a;" u2="&#x178;" k="18" />
<hkern u1="&#x3a;" u2="&#x153;" k="6" />
<hkern u1="&#x3a;" u2="&#xe7;" k="6" />
<hkern u1="&#x3a;" u2="&#xe6;" k="6" />
<hkern u1="&#x3a;" u2="&#xdd;" k="18" />
<hkern u1="&#x3a;" u2="q" k="6" />
<hkern u1="&#x3a;" u2="o" k="6" />
<hkern u1="&#x3a;" u2="e" k="6" />
<hkern u1="&#x3a;" u2="d" k="6" />
<hkern u1="&#x3a;" u2="c" k="6" />
<hkern u1="&#x3a;" u2="a" k="6" />
<hkern u1="&#x3a;" u2="Y" k="18" />
<hkern u1="&#x3a;" u2="W" k="12" />
<hkern u1="&#x3a;" u2="V" k="12" />
<hkern u1="&#x3a;" u2="T" k="31" />
<hkern u1="&#x3a;" u2="&#x37;" k="12" />
<hkern u1="&#x3a;" u2="&#x31;" k="18" />
<hkern u1="&#x3b;" u2="&#x178;" k="18" />
<hkern u1="&#x3b;" u2="&#x153;" k="6" />
<hkern u1="&#x3b;" u2="&#xe7;" k="6" />
<hkern u1="&#x3b;" u2="&#xe6;" k="6" />
<hkern u1="&#x3b;" u2="&#xdd;" k="18" />
<hkern u1="&#x3b;" u2="q" k="6" />
<hkern u1="&#x3b;" u2="o" k="6" />
<hkern u1="&#x3b;" u2="e" k="6" />
<hkern u1="&#x3b;" u2="d" k="6" />
<hkern u1="&#x3b;" u2="c" k="6" />
<hkern u1="&#x3b;" u2="a" k="6" />
<hkern u1="&#x3b;" u2="Y" k="18" />
<hkern u1="&#x3b;" u2="W" k="12" />
<hkern u1="&#x3b;" u2="V" k="12" />
<hkern u1="&#x3b;" u2="T" k="31" />
<hkern u1="&#x3b;" u2="&#x37;" k="12" />
<hkern u1="&#x3b;" u2="&#x31;" k="18" />
<hkern u1="&#x3e;" u2="&#x37;" k="37" />
<hkern u1="&#x40;" u2="&#x178;" k="18" />
<hkern u1="&#x40;" u2="&#xdd;" k="18" />
<hkern u1="&#x40;" u2="&#xc6;" k="6" />
<hkern u1="&#x40;" u2="&#xc5;" k="6" />
<hkern u1="&#x40;" u2="&#xc4;" k="6" />
<hkern u1="&#x40;" u2="&#xc3;" k="6" />
<hkern u1="&#x40;" u2="&#xc2;" k="6" />
<hkern u1="&#x40;" u2="&#xc1;" k="6" />
<hkern u1="&#x40;" u2="&#xc0;" k="6" />
<hkern u1="&#x40;" u2="Y" k="18" />
<hkern u1="&#x40;" u2="W" k="12" />
<hkern u1="&#x40;" u2="T" k="18" />
<hkern u1="&#x40;" u2="A" k="6" />
<hkern u1="A" u2="&#x2122;" k="43" />
<hkern u1="A" u2="&#x2026;" k="-18" />
<hkern u1="A" u2="&#x201e;" k="-18" />
<hkern u1="A" u2="&#x201d;" k="18" />
<hkern u1="A" u2="&#x201c;" k="37" />
<hkern u1="A" u2="&#x201a;" k="-18" />
<hkern u1="A" u2="&#x2019;" k="18" />
<hkern u1="A" u2="&#x2018;" k="37" />
<hkern u1="A" u2="&#x2014;" k="-6" />
<hkern u1="A" u2="&#x2013;" k="-6" />
<hkern u1="A" u2="&#x178;" k="37" />
<hkern u1="A" u2="&#x153;" k="4" />
<hkern u1="A" u2="&#x152;" k="25" />
<hkern u1="A" u2="&#xe7;" k="4" />
<hkern u1="A" u2="&#xe6;" k="4" />
<hkern u1="A" u2="&#xdd;" k="37" />
<hkern u1="A" u2="&#xd8;" k="25" />
<hkern u1="A" u2="&#xd6;" k="25" />
<hkern u1="A" u2="&#xd5;" k="25" />
<hkern u1="A" u2="&#xd4;" k="25" />
<hkern u1="A" u2="&#xd3;" k="25" />
<hkern u1="A" u2="&#xd2;" k="25" />
<hkern u1="A" u2="&#xc6;" k="-6" />
<hkern u1="A" u2="&#xc5;" k="-6" />
<hkern u1="A" u2="&#xc4;" k="-6" />
<hkern u1="A" u2="&#xc3;" k="-6" />
<hkern u1="A" u2="&#xc2;" k="-6" />
<hkern u1="A" u2="&#xc1;" k="-6" />
<hkern u1="A" u2="&#xc0;" k="-6" />
<hkern u1="A" u2="&#xae;" k="6" />
<hkern u1="A" u2="&#xa9;" k="6" />
<hkern u1="A" u2="z" k="-12" />
<hkern u1="A" u2="y" k="6" />
<hkern u1="A" u2="x" k="-12" />
<hkern u1="A" u2="w" k="6" />
<hkern u1="A" u2="v" k="6" />
<hkern u1="A" u2="u" k="4" />
<hkern u1="A" u2="t" k="4" />
<hkern u1="A" u2="r" k="4" />
<hkern u1="A" u2="q" k="4" />
<hkern u1="A" u2="p" k="4" />
<hkern u1="A" u2="o" k="4" />
<hkern u1="A" u2="n" k="4" />
<hkern u1="A" u2="m" k="4" />
<hkern u1="A" u2="e" k="4" />
<hkern u1="A" u2="d" k="4" />
<hkern u1="A" u2="c" k="4" />
<hkern u1="A" u2="a" k="4" />
<hkern u1="A" u2="Y" k="37" />
<hkern u1="A" u2="X" k="-12" />
<hkern u1="A" u2="W" k="43" />
<hkern u1="A" u2="V" k="43" />
<hkern u1="A" u2="T" k="55" />
<hkern u1="A" u2="S" k="6" />
<hkern u1="A" u2="Q" k="25" />
<hkern u1="A" u2="O" k="25" />
<hkern u1="A" u2="J" k="6" />
<hkern u1="A" u2="G" k="25" />
<hkern u1="A" u2="C" k="25" />
<hkern u1="A" u2="A" k="-6" />
<hkern u1="A" u2="&#x40;" k="6" />
<hkern u1="A" u2="&#x2e;" k="-18" />
<hkern u1="A" u2="&#x2d;" k="-6" />
<hkern u1="A" u2="&#x2c;" k="-18" />
<hkern u1="A" u2="&#x2a;" k="37" />
<hkern u1="B" u2="&#x178;" k="12" />
<hkern u1="B" u2="&#xdd;" k="12" />
<hkern u1="B" u2="Y" k="12" />
<hkern u1="B" u2="W" k="12" />
<hkern u1="B" u2="V" k="4" />
<hkern u1="C" u2="&#x153;" k="-4" />
<hkern u1="C" u2="&#xe7;" k="-4" />
<hkern u1="C" u2="&#xe6;" k="-4" />
<hkern u1="C" u2="z" k="6" />
<hkern u1="C" u2="y" k="4" />
<hkern u1="C" u2="w" k="4" />
<hkern u1="C" u2="v" k="4" />
<hkern u1="C" u2="t" k="4" />
<hkern u1="C" u2="q" k="-4" />
<hkern u1="C" u2="o" k="-4" />
<hkern u1="C" u2="e" k="-4" />
<hkern u1="C" u2="d" k="-4" />
<hkern u1="C" u2="c" k="-4" />
<hkern u1="C" u2="a" k="-4" />
<hkern u1="C" u2="Z" k="6" />
<hkern u1="C" u2="X" k="-6" />
<hkern u1="C" u2="V" k="-12" />
<hkern u1="C" u2="T" k="12" />
<hkern u1="D" u2="&#x2026;" k="25" />
<hkern u1="D" u2="&#x201e;" k="25" />
<hkern u1="D" u2="&#x201c;" k="12" />
<hkern u1="D" u2="&#x201a;" k="25" />
<hkern u1="D" u2="&#x2018;" k="12" />
<hkern u1="D" u2="&#x178;" k="23" />
<hkern u1="D" u2="&#x153;" k="4" />
<hkern u1="D" u2="&#xe7;" k="4" />
<hkern u1="D" u2="&#xe6;" k="4" />
<hkern u1="D" u2="&#xdd;" k="23" />
<hkern u1="D" u2="z" k="6" />
<hkern u1="D" u2="y" k="-6" />
<hkern u1="D" u2="x" k="6" />
<hkern u1="D" u2="w" k="-4" />
<hkern u1="D" u2="v" k="-6" />
<hkern u1="D" u2="u" k="4" />
<hkern u1="D" u2="r" k="4" />
<hkern u1="D" u2="q" k="4" />
<hkern u1="D" u2="p" k="4" />
<hkern u1="D" u2="o" k="4" />
<hkern u1="D" u2="n" k="4" />
<hkern u1="D" u2="m" k="4" />
<hkern u1="D" u2="l" k="6" />
<hkern u1="D" u2="k" k="6" />
<hkern u1="D" u2="h" k="6" />
<hkern u1="D" u2="e" k="4" />
<hkern u1="D" u2="d" k="4" />
<hkern u1="D" u2="c" k="4" />
<hkern u1="D" u2="b" k="6" />
<hkern u1="D" u2="a" k="4" />
<hkern u1="D" u2="Z" k="29" />
<hkern u1="D" u2="Y" k="23" />
<hkern u1="D" u2="X" k="16" />
<hkern u1="D" u2="W" k="25" />
<hkern u1="D" u2="V" k="25" />
<hkern u1="D" u2="T" k="31" />
<hkern u1="D" u2="J" k="29" />
<hkern u1="D" u2="&#x3f;" k="12" />
<hkern u1="D" u2="&#x2f;" k="31" />
<hkern u1="D" u2="&#x2e;" k="25" />
<hkern u1="D" u2="&#x2c;" k="25" />
<hkern u1="E" u2="&#x153;" k="23" />
<hkern u1="E" u2="&#xe7;" k="23" />
<hkern u1="E" u2="&#xe6;" k="23" />
<hkern u1="E" u2="&#xae;" k="12" />
<hkern u1="E" u2="&#xa9;" k="12" />
<hkern u1="E" u2="y" k="12" />
<hkern u1="E" u2="v" k="12" />
<hkern u1="E" u2="u" k="12" />
<hkern u1="E" u2="t" k="12" />
<hkern u1="E" u2="s" k="12" />
<hkern u1="E" u2="r" k="12" />
<hkern u1="E" u2="q" k="23" />
<hkern u1="E" u2="p" k="12" />
<hkern u1="E" u2="o" k="23" />
<hkern u1="E" u2="n" k="12" />
<hkern u1="E" u2="m" k="12" />
<hkern u1="E" u2="g" k="4" />
<hkern u1="E" u2="f" k="6" />
<hkern u1="E" u2="e" k="23" />
<hkern u1="E" u2="d" k="23" />
<hkern u1="E" u2="c" k="23" />
<hkern u1="E" u2="a" k="23" />
<hkern u1="E" u2="T" k="-10" />
<hkern u1="E" u2="&#x40;" k="12" />
<hkern u1="F" u2="&#x2122;" k="-31" />
<hkern u1="F" u2="&#x203a;" k="18" />
<hkern u1="F" u2="&#x2039;" k="18" />
<hkern u1="F" u2="&#x2026;" k="55" />
<hkern u1="F" u2="&#x201e;" k="55" />
<hkern u1="F" u2="&#x201a;" k="55" />
<hkern u1="F" u2="&#x2014;" k="31" />
<hkern u1="F" u2="&#x2013;" k="31" />
<hkern u1="F" u2="&#x178;" k="-16" />
<hkern u1="F" u2="&#x153;" k="31" />
<hkern u1="F" u2="&#xe7;" k="31" />
<hkern u1="F" u2="&#xe6;" k="31" />
<hkern u1="F" u2="&#xdd;" k="-16" />
<hkern u1="F" u2="&#xc6;" k="25" />
<hkern u1="F" u2="&#xc5;" k="25" />
<hkern u1="F" u2="&#xc4;" k="25" />
<hkern u1="F" u2="&#xc3;" k="25" />
<hkern u1="F" u2="&#xc2;" k="25" />
<hkern u1="F" u2="&#xc1;" k="25" />
<hkern u1="F" u2="&#xc0;" k="25" />
<hkern u1="F" u2="&#xbb;" k="18" />
<hkern u1="F" u2="&#xae;" k="12" />
<hkern u1="F" u2="&#xab;" k="18" />
<hkern u1="F" u2="&#xa9;" k="12" />
<hkern u1="F" u2="z" k="25" />
<hkern u1="F" u2="y" k="-18" />
<hkern u1="F" u2="x" k="25" />
<hkern u1="F" u2="w" k="12" />
<hkern u1="F" u2="v" k="-18" />
<hkern u1="F" u2="u" k="25" />
<hkern u1="F" u2="t" k="12" />
<hkern u1="F" u2="s" k="25" />
<hkern u1="F" u2="r" k="25" />
<hkern u1="F" u2="q" k="31" />
<hkern u1="F" u2="p" k="25" />
<hkern u1="F" u2="o" k="31" />
<hkern u1="F" u2="n" k="25" />
<hkern u1="F" u2="m" k="25" />
<hkern u1="F" u2="l" k="6" />
<hkern u1="F" u2="k" k="6" />
<hkern u1="F" u2="j" k="6" />
<hkern u1="F" u2="i" k="6" />
<hkern u1="F" u2="h" k="6" />
<hkern u1="F" u2="g" k="18" />
<hkern u1="F" u2="f" k="12" />
<hkern u1="F" u2="e" k="31" />
<hkern u1="F" u2="d" k="31" />
<hkern u1="F" u2="c" k="31" />
<hkern u1="F" u2="b" k="6" />
<hkern u1="F" u2="a" k="31" />
<hkern u1="F" u2="Y" k="-16" />
<hkern u1="F" u2="X" k="-6" />
<hkern u1="F" u2="W" k="-4" />
<hkern u1="F" u2="V" k="-12" />
<hkern u1="F" u2="T" k="-18" />
<hkern u1="F" u2="J" k="55" />
<hkern u1="F" u2="A" k="25" />
<hkern u1="F" u2="&#x40;" k="12" />
<hkern u1="F" u2="&#x3b;" k="18" />
<hkern u1="F" u2="&#x3a;" k="18" />
<hkern u1="F" u2="&#x2f;" k="49" />
<hkern u1="F" u2="&#x2e;" k="55" />
<hkern u1="F" u2="&#x2d;" k="31" />
<hkern u1="F" u2="&#x2c;" k="55" />
<hkern u1="F" u2="&#x26;" k="25" />
<hkern u1="G" u2="&#x2122;" k="18" />
<hkern u1="G" u2="&#x2026;" k="-12" />
<hkern u1="G" u2="&#x201e;" k="-12" />
<hkern u1="G" u2="&#x201a;" k="-12" />
<hkern u1="G" u2="&#x178;" k="6" />
<hkern u1="G" u2="&#x153;" k="-10" />
<hkern u1="G" u2="&#x152;" k="-6" />
<hkern u1="G" u2="&#xe7;" k="-10" />
<hkern u1="G" u2="&#xe6;" k="-10" />
<hkern u1="G" u2="&#xde;" k="-10" />
<hkern u1="G" u2="&#xdd;" k="6" />
<hkern u1="G" u2="&#xd8;" k="-6" />
<hkern u1="G" u2="&#xd6;" k="-6" />
<hkern u1="G" u2="&#xd5;" k="-6" />
<hkern u1="G" u2="&#xd4;" k="-6" />
<hkern u1="G" u2="&#xd3;" k="-6" />
<hkern u1="G" u2="&#xd2;" k="-6" />
<hkern u1="G" u2="&#xd1;" k="-10" />
<hkern u1="G" u2="&#xd0;" k="-10" />
<hkern u1="G" u2="&#xcf;" k="-10" />
<hkern u1="G" u2="&#xce;" k="-10" />
<hkern u1="G" u2="&#xcd;" k="-10" />
<hkern u1="G" u2="&#xcc;" k="-10" />
<hkern u1="G" u2="&#xcb;" k="-10" />
<hkern u1="G" u2="&#xca;" k="-10" />
<hkern u1="G" u2="&#xc9;" k="-10" />
<hkern u1="G" u2="&#xc8;" k="-10" />
<hkern u1="G" u2="&#xc6;" k="-10" />
<hkern u1="G" u2="&#xc5;" k="-10" />
<hkern u1="G" u2="&#xc4;" k="-10" />
<hkern u1="G" u2="&#xc3;" k="-10" />
<hkern u1="G" u2="&#xc2;" k="-10" />
<hkern u1="G" u2="&#xc1;" k="-10" />
<hkern u1="G" u2="&#xc0;" k="-10" />
<hkern u1="G" u2="y" k="-6" />
<hkern u1="G" u2="v" k="-6" />
<hkern u1="G" u2="u" k="-10" />
<hkern u1="G" u2="t" k="6" />
<hkern u1="G" u2="r" k="-10" />
<hkern u1="G" u2="q" k="-10" />
<hkern u1="G" u2="p" k="-10" />
<hkern u1="G" u2="o" k="-10" />
<hkern u1="G" u2="n" k="-10" />
<hkern u1="G" u2="m" k="-10" />
<hkern u1="G" u2="e" k="-10" />
<hkern u1="G" u2="d" k="-10" />
<hkern u1="G" u2="c" k="-10" />
<hkern u1="G" u2="a" k="-10" />
<hkern u1="G" u2="Y" k="6" />
<hkern u1="G" u2="X" k="-6" />
<hkern u1="G" u2="W" k="4" />
<hkern u1="G" u2="V" k="6" />
<hkern u1="G" u2="T" k="16" />
<hkern u1="G" u2="R" k="-10" />
<hkern u1="G" u2="Q" k="-6" />
<hkern u1="G" u2="P" k="-10" />
<hkern u1="G" u2="O" k="-6" />
<hkern u1="G" u2="N" k="-10" />
<hkern u1="G" u2="M" k="-10" />
<hkern u1="G" u2="L" k="-10" />
<hkern u1="G" u2="K" k="-10" />
<hkern u1="G" u2="I" k="-10" />
<hkern u1="G" u2="H" k="-10" />
<hkern u1="G" u2="G" k="-6" />
<hkern u1="G" u2="F" k="-10" />
<hkern u1="G" u2="E" k="-10" />
<hkern u1="G" u2="D" k="-10" />
<hkern u1="G" u2="C" k="-6" />
<hkern u1="G" u2="B" k="-10" />
<hkern u1="G" u2="A" k="-10" />
<hkern u1="G" u2="&#x2e;" k="-12" />
<hkern u1="G" u2="&#x2c;" k="-12" />
<hkern u1="H" u2="y" k="-12" />
<hkern u1="H" u2="v" k="-12" />
<hkern u1="H" u2="&#x2f;" k="12" />
<hkern u1="I" u2="y" k="-12" />
<hkern u1="I" u2="v" k="-12" />
<hkern u1="I" u2="&#x2f;" k="12" />
<hkern u1="J" u2="&#x2026;" k="6" />
<hkern u1="J" u2="&#x201e;" k="6" />
<hkern u1="J" u2="&#x201a;" k="6" />
<hkern u1="J" u2="&#xc6;" k="-12" />
<hkern u1="J" u2="&#xc5;" k="-12" />
<hkern u1="J" u2="&#xc4;" k="-12" />
<hkern u1="J" u2="&#xc3;" k="-12" />
<hkern u1="J" u2="&#xc2;" k="-12" />
<hkern u1="J" u2="&#xc1;" k="-12" />
<hkern u1="J" u2="&#xc0;" k="-12" />
<hkern u1="J" u2="y" k="-12" />
<hkern u1="J" u2="v" k="-12" />
<hkern u1="J" u2="J" k="6" />
<hkern u1="J" u2="A" k="-12" />
<hkern u1="J" u2="&#x2e;" k="6" />
<hkern u1="J" u2="&#x2c;" k="6" />
<hkern u1="K" u2="&#x2014;" k="25" />
<hkern u1="K" u2="&#x2013;" k="25" />
<hkern u1="K" u2="&#x178;" k="-10" />
<hkern u1="K" u2="&#x153;" k="18" />
<hkern u1="K" u2="&#x152;" k="12" />
<hkern u1="K" u2="&#xf0;" k="12" />
<hkern u1="K" u2="&#xe7;" k="18" />
<hkern u1="K" u2="&#xe6;" k="18" />
<hkern u1="K" u2="&#xdd;" k="-10" />
<hkern u1="K" u2="&#xdc;" k="6" />
<hkern u1="K" u2="&#xdb;" k="6" />
<hkern u1="K" u2="&#xda;" k="6" />
<hkern u1="K" u2="&#xd9;" k="6" />
<hkern u1="K" u2="&#xd8;" k="12" />
<hkern u1="K" u2="&#xd6;" k="12" />
<hkern u1="K" u2="&#xd5;" k="12" />
<hkern u1="K" u2="&#xd4;" k="12" />
<hkern u1="K" u2="&#xd3;" k="12" />
<hkern u1="K" u2="&#xd2;" k="12" />
<hkern u1="K" u2="&#xae;" k="18" />
<hkern u1="K" u2="&#xa9;" k="18" />
<hkern u1="K" u2="y" k="12" />
<hkern u1="K" u2="v" k="12" />
<hkern u1="K" u2="t" k="6" />
<hkern u1="K" u2="q" k="18" />
<hkern u1="K" u2="o" k="18" />
<hkern u1="K" u2="g" k="6" />
<hkern u1="K" u2="f" k="4" />
<hkern u1="K" u2="e" k="18" />
<hkern u1="K" u2="d" k="18" />
<hkern u1="K" u2="c" k="18" />
<hkern u1="K" u2="a" k="18" />
<hkern u1="K" u2="Z" k="4" />
<hkern u1="K" u2="Y" k="-10" />
<hkern u1="K" u2="X" k="-6" />
<hkern u1="K" u2="W" k="6" />
<hkern u1="K" u2="V" k="-10" />
<hkern u1="K" u2="U" k="6" />
<hkern u1="K" u2="T" k="-4" />
<hkern u1="K" u2="S" k="4" />
<hkern u1="K" u2="Q" k="12" />
<hkern u1="K" u2="O" k="12" />
<hkern u1="K" u2="G" k="12" />
<hkern u1="K" u2="C" k="12" />
<hkern u1="K" u2="&#x40;" k="18" />
<hkern u1="K" u2="&#x2d;" k="25" />
<hkern u1="K" u2="&#x26;" k="12" />
<hkern u1="L" u2="&#x2122;" k="43" />
<hkern u1="L" u2="&#x201d;" k="37" />
<hkern u1="L" u2="&#x201c;" k="61" />
<hkern u1="L" u2="&#x2019;" k="37" />
<hkern u1="L" u2="&#x2018;" k="61" />
<hkern u1="L" u2="&#x178;" k="49" />
<hkern u1="L" u2="&#x153;" k="12" />
<hkern u1="L" u2="&#x152;" k="31" />
<hkern u1="L" u2="&#xe7;" k="12" />
<hkern u1="L" u2="&#xe6;" k="12" />
<hkern u1="L" u2="&#xdd;" k="49" />
<hkern u1="L" u2="&#xdc;" k="10" />
<hkern u1="L" u2="&#xdb;" k="10" />
<hkern u1="L" u2="&#xda;" k="10" />
<hkern u1="L" u2="&#xd9;" k="10" />
<hkern u1="L" u2="&#xd8;" k="31" />
<hkern u1="L" u2="&#xd6;" k="31" />
<hkern u1="L" u2="&#xd5;" k="31" />
<hkern u1="L" u2="&#xd4;" k="31" />
<hkern u1="L" u2="&#xd3;" k="31" />
<hkern u1="L" u2="&#xd2;" k="31" />
<hkern u1="L" u2="&#xc6;" k="-12" />
<hkern u1="L" u2="&#xc5;" k="-12" />
<hkern u1="L" u2="&#xc4;" k="-12" />
<hkern u1="L" u2="&#xc3;" k="-12" />
<hkern u1="L" u2="&#xc2;" k="-12" />
<hkern u1="L" u2="&#xc1;" k="-12" />
<hkern u1="L" u2="&#xc0;" k="-12" />
<hkern u1="L" u2="&#xae;" k="31" />
<hkern u1="L" u2="&#xa9;" k="31" />
<hkern u1="L" u2="y" k="25" />
<hkern u1="L" u2="w" k="6" />
<hkern u1="L" u2="v" k="25" />
<hkern u1="L" u2="q" k="12" />
<hkern u1="L" u2="o" k="12" />
<hkern u1="L" u2="e" k="12" />
<hkern u1="L" u2="d" k="12" />
<hkern u1="L" u2="c" k="12" />
<hkern u1="L" u2="a" k="12" />
<hkern u1="L" u2="\" k="31" />
<hkern u1="L" u2="Y" k="49" />
<hkern u1="L" u2="W" k="43" />
<hkern u1="L" u2="V" k="55" />
<hkern u1="L" u2="U" k="10" />
<hkern u1="L" u2="T" k="80" />
<hkern u1="L" u2="Q" k="31" />
<hkern u1="L" u2="O" k="31" />
<hkern u1="L" u2="G" k="31" />
<hkern u1="L" u2="C" k="31" />
<hkern u1="L" u2="A" k="-12" />
<hkern u1="L" u2="&#x40;" k="31" />
<hkern u1="M" u2="y" k="-12" />
<hkern u1="M" u2="v" k="-12" />
<hkern u1="M" u2="&#x2f;" k="12" />
<hkern u1="N" u2="y" k="-12" />
<hkern u1="N" u2="v" k="-12" />
<hkern u1="N" u2="&#x2f;" k="12" />
<hkern u1="O" u2="&#x2026;" k="25" />
<hkern u1="O" u2="&#x201e;" k="25" />
<hkern u1="O" u2="&#x201c;" k="12" />
<hkern u1="O" u2="&#x201a;" k="25" />
<hkern u1="O" u2="&#x2018;" k="12" />
<hkern u1="O" u2="&#x178;" k="23" />
<hkern u1="O" u2="&#x153;" k="4" />
<hkern u1="O" u2="&#xe7;" k="4" />
<hkern u1="O" u2="&#xe6;" k="4" />
<hkern u1="O" u2="&#xdd;" k="23" />
<hkern u1="O" u2="z" k="6" />
<hkern u1="O" u2="y" k="-6" />
<hkern u1="O" u2="x" k="6" />
<hkern u1="O" u2="w" k="-4" />
<hkern u1="O" u2="v" k="-6" />
<hkern u1="O" u2="u" k="4" />
<hkern u1="O" u2="r" k="4" />
<hkern u1="O" u2="q" k="4" />
<hkern u1="O" u2="p" k="4" />
<hkern u1="O" u2="o" k="4" />
<hkern u1="O" u2="n" k="4" />
<hkern u1="O" u2="m" k="4" />
<hkern u1="O" u2="l" k="6" />
<hkern u1="O" u2="k" k="6" />
<hkern u1="O" u2="h" k="6" />
<hkern u1="O" u2="e" k="4" />
<hkern u1="O" u2="d" k="4" />
<hkern u1="O" u2="c" k="4" />
<hkern u1="O" u2="b" k="6" />
<hkern u1="O" u2="a" k="4" />
<hkern u1="O" u2="Z" k="29" />
<hkern u1="O" u2="Y" k="23" />
<hkern u1="O" u2="X" k="16" />
<hkern u1="O" u2="W" k="25" />
<hkern u1="O" u2="V" k="25" />
<hkern u1="O" u2="T" k="31" />
<hkern u1="O" u2="J" k="29" />
<hkern u1="O" u2="&#x3f;" k="12" />
<hkern u1="O" u2="&#x2f;" k="31" />
<hkern u1="O" u2="&#x2e;" k="25" />
<hkern u1="O" u2="&#x2c;" k="25" />
<hkern u1="P" u2="&#x2026;" k="37" />
<hkern u1="P" u2="&#x201e;" k="37" />
<hkern u1="P" u2="&#x201a;" k="37" />
<hkern u1="P" u2="&#x178;" k="4" />
<hkern u1="P" u2="&#x153;" k="6" />
<hkern u1="P" u2="&#x152;" k="-6" />
<hkern u1="P" u2="&#xe7;" k="6" />
<hkern u1="P" u2="&#xe6;" k="6" />
<hkern u1="P" u2="&#xdd;" k="4" />
<hkern u1="P" u2="&#xd8;" k="-6" />
<hkern u1="P" u2="&#xd6;" k="-6" />
<hkern u1="P" u2="&#xd5;" k="-6" />
<hkern u1="P" u2="&#xd4;" k="-6" />
<hkern u1="P" u2="&#xd3;" k="-6" />
<hkern u1="P" u2="&#xd2;" k="-6" />
<hkern u1="P" u2="&#xc6;" k="23" />
<hkern u1="P" u2="&#xc5;" k="23" />
<hkern u1="P" u2="&#xc4;" k="23" />
<hkern u1="P" u2="&#xc3;" k="23" />
<hkern u1="P" u2="&#xc2;" k="23" />
<hkern u1="P" u2="&#xc1;" k="23" />
<hkern u1="P" u2="&#xc0;" k="23" />
<hkern u1="P" u2="y" k="-35" />
<hkern u1="P" u2="x" k="-4" />
<hkern u1="P" u2="w" k="-12" />
<hkern u1="P" u2="v" k="-35" />
<hkern u1="P" u2="t" k="-6" />
<hkern u1="P" u2="q" k="6" />
<hkern u1="P" u2="o" k="6" />
<hkern u1="P" u2="f" k="-6" />
<hkern u1="P" u2="e" k="6" />
<hkern u1="P" u2="d" k="6" />
<hkern u1="P" u2="c" k="6" />
<hkern u1="P" u2="a" k="6" />
<hkern u1="P" u2="Z" k="16" />
<hkern u1="P" u2="Y" k="4" />
<hkern u1="P" u2="X" k="-4" />
<hkern u1="P" u2="W" k="10" />
<hkern u1="P" u2="V" k="-4" />
<hkern u1="P" u2="Q" k="-6" />
<hkern u1="P" u2="O" k="-6" />
<hkern u1="P" u2="J" k="53" />
<hkern u1="P" u2="G" k="-6" />
<hkern u1="P" u2="C" k="-6" />
<hkern u1="P" u2="A" k="23" />
<hkern u1="P" u2="&#x2e;" k="37" />
<hkern u1="P" u2="&#x2c;" k="37" />
<hkern u1="P" u2="&#x26;" k="12" />
<hkern u1="Q" u2="&#x2026;" k="25" />
<hkern u1="Q" u2="&#x201e;" k="25" />
<hkern u1="Q" u2="&#x201c;" k="12" />
<hkern u1="Q" u2="&#x201a;" k="25" />
<hkern u1="Q" u2="&#x2018;" k="12" />
<hkern u1="Q" u2="&#x178;" k="23" />
<hkern u1="Q" u2="&#x153;" k="4" />
<hkern u1="Q" u2="&#xe7;" k="4" />
<hkern u1="Q" u2="&#xe6;" k="4" />
<hkern u1="Q" u2="&#xdd;" k="23" />
<hkern u1="Q" u2="z" k="6" />
<hkern u1="Q" u2="y" k="-6" />
<hkern u1="Q" u2="x" k="6" />
<hkern u1="Q" u2="w" k="-4" />
<hkern u1="Q" u2="v" k="-6" />
<hkern u1="Q" u2="u" k="4" />
<hkern u1="Q" u2="r" k="4" />
<hkern u1="Q" u2="q" k="4" />
<hkern u1="Q" u2="p" k="4" />
<hkern u1="Q" u2="o" k="4" />
<hkern u1="Q" u2="n" k="4" />
<hkern u1="Q" u2="m" k="4" />
<hkern u1="Q" u2="l" k="6" />
<hkern u1="Q" u2="k" k="6" />
<hkern u1="Q" u2="h" k="6" />
<hkern u1="Q" u2="e" k="4" />
<hkern u1="Q" u2="d" k="4" />
<hkern u1="Q" u2="c" k="4" />
<hkern u1="Q" u2="b" k="6" />
<hkern u1="Q" u2="a" k="4" />
<hkern u1="Q" u2="Z" k="29" />
<hkern u1="Q" u2="Y" k="23" />
<hkern u1="Q" u2="X" k="16" />
<hkern u1="Q" u2="W" k="25" />
<hkern u1="Q" u2="V" k="25" />
<hkern u1="Q" u2="T" k="31" />
<hkern u1="Q" u2="J" k="29" />
<hkern u1="Q" u2="&#x3f;" k="12" />
<hkern u1="Q" u2="&#x2f;" k="18" />
<hkern u1="Q" u2="&#x2e;" k="25" />
<hkern u1="Q" u2="&#x2c;" k="25" />
<hkern u1="R" u2="&#x178;" k="-10" />
<hkern u1="R" u2="&#xdd;" k="-10" />
<hkern u1="R" u2="&#xc6;" k="-12" />
<hkern u1="R" u2="&#xc5;" k="-12" />
<hkern u1="R" u2="&#xc4;" k="-12" />
<hkern u1="R" u2="&#xc3;" k="-12" />
<hkern u1="R" u2="&#xc2;" k="-12" />
<hkern u1="R" u2="&#xc1;" k="-12" />
<hkern u1="R" u2="&#xc0;" k="-12" />
<hkern u1="R" u2="y" k="-18" />
<hkern u1="R" u2="w" k="-10" />
<hkern u1="R" u2="v" k="-18" />
<hkern u1="R" u2="Y" k="-10" />
<hkern u1="R" u2="X" k="-6" />
<hkern u1="R" u2="V" k="-6" />
<hkern u1="R" u2="T" k="6" />
<hkern u1="R" u2="J" k="6" />
<hkern u1="R" u2="A" k="-12" />
<hkern u1="R" u2="&#x26;" k="-6" />
<hkern u1="S" u2="&#x178;" k="6" />
<hkern u1="S" u2="&#xdd;" k="6" />
<hkern u1="S" u2="&#xc6;" k="-25" />
<hkern u1="S" u2="&#xc5;" k="-25" />
<hkern u1="S" u2="&#xc4;" k="-25" />
<hkern u1="S" u2="&#xc3;" k="-25" />
<hkern u1="S" u2="&#xc2;" k="-25" />
<hkern u1="S" u2="&#xc1;" k="-25" />
<hkern u1="S" u2="&#xc0;" k="-25" />
<hkern u1="S" u2="y" k="-6" />
<hkern u1="S" u2="v" k="-6" />
<hkern u1="S" u2="Y" k="6" />
<hkern u1="S" u2="X" k="-4" />
<hkern u1="S" u2="W" k="12" />
<hkern u1="S" u2="V" k="6" />
<hkern u1="S" u2="A" k="-25" />
<hkern u1="T" u2="&#x2122;" k="-31" />
<hkern u1="T" u2="&#x203a;" k="25" />
<hkern u1="T" u2="&#x2039;" k="68" />
<hkern u1="T" u2="&#x2026;" k="37" />
<hkern u1="T" u2="&#x201e;" k="37" />
<hkern u1="T" u2="&#x201a;" k="37" />
<hkern u1="T" u2="&#x2014;" k="43" />
<hkern u1="T" u2="&#x2013;" k="43" />
<hkern u1="T" u2="&#x178;" k="-18" />
<hkern u1="T" u2="&#x153;" k="59" />
<hkern u1="T" u2="&#x152;" k="6" />
<hkern u1="T" u2="&#xe7;" k="59" />
<hkern u1="T" u2="&#xe6;" k="59" />
<hkern u1="T" u2="&#xdd;" k="-18" />
<hkern u1="T" u2="&#xd8;" k="6" />
<hkern u1="T" u2="&#xd6;" k="6" />
<hkern u1="T" u2="&#xd5;" k="6" />
<hkern u1="T" u2="&#xd4;" k="6" />
<hkern u1="T" u2="&#xd3;" k="6" />
<hkern u1="T" u2="&#xd2;" k="6" />
<hkern u1="T" u2="&#xc6;" k="31" />
<hkern u1="T" u2="&#xc5;" k="31" />
<hkern u1="T" u2="&#xc4;" k="31" />
<hkern u1="T" u2="&#xc3;" k="31" />
<hkern u1="T" u2="&#xc2;" k="31" />
<hkern u1="T" u2="&#xc1;" k="31" />
<hkern u1="T" u2="&#xc0;" k="31" />
<hkern u1="T" u2="&#xbf;" k="31" />
<hkern u1="T" u2="&#xbb;" k="25" />
<hkern u1="T" u2="&#xae;" k="18" />
<hkern u1="T" u2="&#xab;" k="68" />
<hkern u1="T" u2="&#xa9;" k="18" />
<hkern u1="T" u2="z" k="49" />
<hkern u1="T" u2="y" k="43" />
<hkern u1="T" u2="x" k="49" />
<hkern u1="T" u2="w" k="37" />
<hkern u1="T" u2="v" k="43" />
<hkern u1="T" u2="u" k="49" />
<hkern u1="T" u2="t" k="12" />
<hkern u1="T" u2="s" k="61" />
<hkern u1="T" u2="r" k="49" />
<hkern u1="T" u2="q" k="59" />
<hkern u1="T" u2="p" k="49" />
<hkern u1="T" u2="o" k="59" />
<hkern u1="T" u2="n" k="49" />
<hkern u1="T" u2="m" k="49" />
<hkern u1="T" u2="g" k="59" />
<hkern u1="T" u2="f" k="6" />
<hkern u1="T" u2="e" k="59" />
<hkern u1="T" u2="d" k="59" />
<hkern u1="T" u2="c" k="59" />
<hkern u1="T" u2="a" k="59" />
<hkern u1="T" u2="\" k="-6" />
<hkern u1="T" u2="Z" k="6" />
<hkern u1="T" u2="Y" k="-18" />
<hkern u1="T" u2="X" k="-6" />
<hkern u1="T" u2="W" k="-6" />
<hkern u1="T" u2="V" k="-18" />
<hkern u1="T" u2="T" k="-6" />
<hkern u1="T" u2="Q" k="6" />
<hkern u1="T" u2="O" k="6" />
<hkern u1="T" u2="J" k="53" />
<hkern u1="T" u2="G" k="6" />
<hkern u1="T" u2="C" k="6" />
<hkern u1="T" u2="A" k="31" />
<hkern u1="T" u2="&#x40;" k="18" />
<hkern u1="T" u2="&#x3b;" k="31" />
<hkern u1="T" u2="&#x3a;" k="31" />
<hkern u1="T" u2="&#x2e;" k="37" />
<hkern u1="T" u2="&#x2d;" k="43" />
<hkern u1="T" u2="&#x2c;" k="37" />
<hkern u1="T" u2="&#x26;" k="25" />
<hkern u1="U" u2="&#x2026;" k="6" />
<hkern u1="U" u2="&#x201e;" k="6" />
<hkern u1="U" u2="&#x201a;" k="6" />
<hkern u1="U" u2="&#xc6;" k="-12" />
<hkern u1="U" u2="&#xc5;" k="-12" />
<hkern u1="U" u2="&#xc4;" k="-12" />
<hkern u1="U" u2="&#xc3;" k="-12" />
<hkern u1="U" u2="&#xc2;" k="-12" />
<hkern u1="U" u2="&#xc1;" k="-12" />
<hkern u1="U" u2="&#xc0;" k="-12" />
<hkern u1="U" u2="y" k="-12" />
<hkern u1="U" u2="v" k="-12" />
<hkern u1="U" u2="J" k="6" />
<hkern u1="U" u2="A" k="-12" />
<hkern u1="U" u2="&#x2e;" k="6" />
<hkern u1="U" u2="&#x2c;" k="6" />
<hkern u1="V" u2="&#x2122;" k="-37" />
<hkern u1="V" u2="&#x203a;" k="12" />
<hkern u1="V" u2="&#x2039;" k="31" />
<hkern u1="V" u2="&#x2026;" k="37" />
<hkern u1="V" u2="&#x201e;" k="37" />
<hkern u1="V" u2="&#x201a;" k="37" />
<hkern u1="V" u2="&#x2014;" k="37" />
<hkern u1="V" u2="&#x2013;" k="37" />
<hkern u1="V" u2="&#x178;" k="-23" />
<hkern u1="V" u2="&#x153;" k="18" />
<hkern u1="V" u2="&#xe7;" k="18" />
<hkern u1="V" u2="&#xe6;" k="18" />
<hkern u1="V" u2="&#xdd;" k="-23" />
<hkern u1="V" u2="&#xc6;" k="18" />
<hkern u1="V" u2="&#xc5;" k="18" />
<hkern u1="V" u2="&#xc4;" k="18" />
<hkern u1="V" u2="&#xc3;" k="18" />
<hkern u1="V" u2="&#xc2;" k="18" />
<hkern u1="V" u2="&#xc1;" k="18" />
<hkern u1="V" u2="&#xc0;" k="18" />
<hkern u1="V" u2="&#xbb;" k="12" />
<hkern u1="V" u2="&#xab;" k="31" />
<hkern u1="V" u2="&#x7d;" k="-18" />
<hkern u1="V" u2="y" k="-12" />
<hkern u1="V" u2="x" k="16" />
<hkern u1="V" u2="w" k="-6" />
<hkern u1="V" u2="v" k="-12" />
<hkern u1="V" u2="u" k="12" />
<hkern u1="V" u2="t" k="6" />
<hkern u1="V" u2="s" k="25" />
<hkern u1="V" u2="r" k="12" />
<hkern u1="V" u2="q" k="18" />
<hkern u1="V" u2="p" k="12" />
<hkern u1="V" u2="o" k="18" />
<hkern u1="V" u2="n" k="12" />
<hkern u1="V" u2="m" k="12" />
<hkern u1="V" u2="l" k="6" />
<hkern u1="V" u2="k" k="6" />
<hkern u1="V" u2="h" k="6" />
<hkern u1="V" u2="g" k="23" />
<hkern u1="V" u2="f" k="6" />
<hkern u1="V" u2="e" k="18" />
<hkern u1="V" u2="d" k="18" />
<hkern u1="V" u2="c" k="18" />
<hkern u1="V" u2="b" k="6" />
<hkern u1="V" u2="a" k="18" />
<hkern u1="V" u2="]" k="-18" />
<hkern u1="V" u2="Z" k="-4" />
<hkern u1="V" u2="Y" k="-23" />
<hkern u1="V" u2="X" k="-12" />
<hkern u1="V" u2="W" k="4" />
<hkern u1="V" u2="V" k="-18" />
<hkern u1="V" u2="T" k="-18" />
<hkern u1="V" u2="S" k="-18" />
<hkern u1="V" u2="J" k="31" />
<hkern u1="V" u2="A" k="18" />
<hkern u1="V" u2="&#x3b;" k="12" />
<hkern u1="V" u2="&#x3a;" k="12" />
<hkern u1="V" u2="&#x2e;" k="37" />
<hkern u1="V" u2="&#x2d;" k="37" />
<hkern u1="V" u2="&#x2c;" k="37" />
<hkern u1="V" u2="&#x29;" k="-18" />
<hkern u1="V" u2="&#x26;" k="25" />
<hkern u1="W" u2="&#x2122;" k="-31" />
<hkern u1="W" u2="&#x203a;" k="12" />
<hkern u1="W" u2="&#x2039;" k="25" />
<hkern u1="W" u2="&#x2026;" k="12" />
<hkern u1="W" u2="&#x201e;" k="12" />
<hkern u1="W" u2="&#x201a;" k="12" />
<hkern u1="W" u2="&#x2014;" k="25" />
<hkern u1="W" u2="&#x2013;" k="25" />
<hkern u1="W" u2="&#x178;" k="-4" />
<hkern u1="W" u2="&#x153;" k="18" />
<hkern u1="W" u2="&#x152;" k="12" />
<hkern u1="W" u2="&#xe7;" k="18" />
<hkern u1="W" u2="&#xe6;" k="18" />
<hkern u1="W" u2="&#xdd;" k="-4" />
<hkern u1="W" u2="&#xd8;" k="12" />
<hkern u1="W" u2="&#xd6;" k="12" />
<hkern u1="W" u2="&#xd5;" k="12" />
<hkern u1="W" u2="&#xd4;" k="12" />
<hkern u1="W" u2="&#xd3;" k="12" />
<hkern u1="W" u2="&#xd2;" k="12" />
<hkern u1="W" u2="&#xc6;" k="18" />
<hkern u1="W" u2="&#xc5;" k="18" />
<hkern u1="W" u2="&#xc4;" k="18" />
<hkern u1="W" u2="&#xc3;" k="18" />
<hkern u1="W" u2="&#xc2;" k="18" />
<hkern u1="W" u2="&#xc1;" k="18" />
<hkern u1="W" u2="&#xc0;" k="18" />
<hkern u1="W" u2="&#xbb;" k="12" />
<hkern u1="W" u2="&#xae;" k="12" />
<hkern u1="W" u2="&#xab;" k="25" />
<hkern u1="W" u2="&#xa9;" k="12" />
<hkern u1="W" u2="&#x7d;" k="-6" />
<hkern u1="W" u2="z" k="18" />
<hkern u1="W" u2="y" k="-16" />
<hkern u1="W" u2="x" k="18" />
<hkern u1="W" u2="w" k="18" />
<hkern u1="W" u2="v" k="-16" />
<hkern u1="W" u2="u" k="16" />
<hkern u1="W" u2="t" k="18" />
<hkern u1="W" u2="s" k="31" />
<hkern u1="W" u2="r" k="16" />
<hkern u1="W" u2="q" k="18" />
<hkern u1="W" u2="p" k="16" />
<hkern u1="W" u2="o" k="18" />
<hkern u1="W" u2="n" k="16" />
<hkern u1="W" u2="m" k="16" />
<hkern u1="W" u2="l" k="6" />
<hkern u1="W" u2="k" k="6" />
<hkern u1="W" u2="j" k="12" />
<hkern u1="W" u2="i" k="12" />
<hkern u1="W" u2="h" k="6" />
<hkern u1="W" u2="g" k="31" />
<hkern u1="W" u2="f" k="10" />
<hkern u1="W" u2="e" k="18" />
<hkern u1="W" u2="d" k="18" />
<hkern u1="W" u2="c" k="18" />
<hkern u1="W" u2="b" k="6" />
<hkern u1="W" u2="a" k="18" />
<hkern u1="W" u2="]" k="-6" />
<hkern u1="W" u2="Z" k="-4" />
<hkern u1="W" u2="Y" k="-4" />
<hkern u1="W" u2="X" k="6" />
<hkern u1="W" u2="W" k="16" />
<hkern u1="W" u2="V" k="4" />
<hkern u1="W" u2="T" k="-6" />
<hkern u1="W" u2="S" k="-12" />
<hkern u1="W" u2="Q" k="12" />
<hkern u1="W" u2="O" k="12" />
<hkern u1="W" u2="J" k="25" />
<hkern u1="W" u2="G" k="12" />
<hkern u1="W" u2="C" k="12" />
<hkern u1="W" u2="A" k="18" />
<hkern u1="W" u2="&#x40;" k="12" />
<hkern u1="W" u2="&#x3b;" k="12" />
<hkern u1="W" u2="&#x3a;" k="12" />
<hkern u1="W" u2="&#x2e;" k="12" />
<hkern u1="W" u2="&#x2d;" k="25" />
<hkern u1="W" u2="&#x2c;" k="12" />
<hkern u1="W" u2="&#x29;" k="-6" />
<hkern u1="W" u2="&#x26;" k="18" />
<hkern u1="X" u2="&#x2122;" k="-31" />
<hkern u1="X" u2="&#x2014;" k="18" />
<hkern u1="X" u2="&#x2013;" k="18" />
<hkern u1="X" u2="&#x178;" k="-12" />
<hkern u1="X" u2="&#x153;" k="6" />
<hkern u1="X" u2="&#x152;" k="10" />
<hkern u1="X" u2="&#xe7;" k="6" />
<hkern u1="X" u2="&#xe6;" k="6" />
<hkern u1="X" u2="&#xdd;" k="-12" />
<hkern u1="X" u2="&#xd8;" k="10" />
<hkern u1="X" u2="&#xd6;" k="10" />
<hkern u1="X" u2="&#xd5;" k="10" />
<hkern u1="X" u2="&#xd4;" k="10" />
<hkern u1="X" u2="&#xd3;" k="10" />
<hkern u1="X" u2="&#xd2;" k="10" />
<hkern u1="X" u2="&#xc6;" k="-35" />
<hkern u1="X" u2="&#xc5;" k="-35" />
<hkern u1="X" u2="&#xc4;" k="-35" />
<hkern u1="X" u2="&#xc3;" k="-35" />
<hkern u1="X" u2="&#xc2;" k="-35" />
<hkern u1="X" u2="&#xc1;" k="-35" />
<hkern u1="X" u2="&#xc0;" k="-35" />
<hkern u1="X" u2="&#x7d;" k="-25" />
<hkern u1="X" u2="y" k="-6" />
<hkern u1="X" u2="w" k="6" />
<hkern u1="X" u2="v" k="-6" />
<hkern u1="X" u2="u" k="6" />
<hkern u1="X" u2="t" k="6" />
<hkern u1="X" u2="r" k="6" />
<hkern u1="X" u2="q" k="6" />
<hkern u1="X" u2="p" k="6" />
<hkern u1="X" u2="o" k="6" />
<hkern u1="X" u2="n" k="6" />
<hkern u1="X" u2="m" k="6" />
<hkern u1="X" u2="f" k="6" />
<hkern u1="X" u2="e" k="6" />
<hkern u1="X" u2="d" k="6" />
<hkern u1="X" u2="c" k="6" />
<hkern u1="X" u2="a" k="6" />
<hkern u1="X" u2="]" k="-25" />
<hkern u1="X" u2="Y" k="-12" />
<hkern u1="X" u2="X" k="-29" />
<hkern u1="X" u2="W" k="6" />
<hkern u1="X" u2="V" k="-12" />
<hkern u1="X" u2="T" k="-6" />
<hkern u1="X" u2="S" k="-4" />
<hkern u1="X" u2="Q" k="10" />
<hkern u1="X" u2="O" k="10" />
<hkern u1="X" u2="G" k="10" />
<hkern u1="X" u2="C" k="10" />
<hkern u1="X" u2="A" k="-35" />
<hkern u1="X" u2="&#x2d;" k="18" />
<hkern u1="X" u2="&#x29;" k="-25" />
<hkern u1="X" u2="&#x26;" k="12" />
<hkern u1="Y" u2="&#x2122;" k="-37" />
<hkern u1="Y" u2="&#x203a;" k="25" />
<hkern u1="Y" u2="&#x2039;" k="37" />
<hkern u1="Y" u2="&#x2026;" k="31" />
<hkern u1="Y" u2="&#x201e;" k="31" />
<hkern u1="Y" u2="&#x201a;" k="31" />
<hkern u1="Y" u2="&#x2014;" k="31" />
<hkern u1="Y" u2="&#x2013;" k="31" />
<hkern u1="Y" u2="&#x178;" k="-18" />
<hkern u1="Y" u2="&#x153;" k="35" />
<hkern u1="Y" u2="&#x152;" k="-4" />
<hkern u1="Y" u2="&#xe7;" k="35" />
<hkern u1="Y" u2="&#xe6;" k="35" />
<hkern u1="Y" u2="&#xdd;" k="-18" />
<hkern u1="Y" u2="&#xd8;" k="-4" />
<hkern u1="Y" u2="&#xd6;" k="-4" />
<hkern u1="Y" u2="&#xd5;" k="-4" />
<hkern u1="Y" u2="&#xd4;" k="-4" />
<hkern u1="Y" u2="&#xd3;" k="-4" />
<hkern u1="Y" u2="&#xd2;" k="-4" />
<hkern u1="Y" u2="&#xc6;" k="12" />
<hkern u1="Y" u2="&#xc5;" k="12" />
<hkern u1="Y" u2="&#xc4;" k="12" />
<hkern u1="Y" u2="&#xc3;" k="12" />
<hkern u1="Y" u2="&#xc2;" k="12" />
<hkern u1="Y" u2="&#xc1;" k="12" />
<hkern u1="Y" u2="&#xc0;" k="12" />
<hkern u1="Y" u2="&#xbb;" k="25" />
<hkern u1="Y" u2="&#xae;" k="18" />
<hkern u1="Y" u2="&#xab;" k="37" />
<hkern u1="Y" u2="&#xa9;" k="18" />
<hkern u1="Y" u2="&#x7d;" k="-31" />
<hkern u1="Y" u2="z" k="18" />
<hkern u1="Y" u2="y" k="12" />
<hkern u1="Y" u2="x" k="12" />
<hkern u1="Y" u2="w" k="6" />
<hkern u1="Y" u2="v" k="12" />
<hkern u1="Y" u2="u" k="25" />
<hkern u1="Y" u2="t" k="12" />
<hkern u1="Y" u2="s" k="31" />
<hkern u1="Y" u2="r" k="25" />
<hkern u1="Y" u2="q" k="35" />
<hkern u1="Y" u2="p" k="25" />
<hkern u1="Y" u2="o" k="35" />
<hkern u1="Y" u2="n" k="25" />
<hkern u1="Y" u2="m" k="25" />
<hkern u1="Y" u2="g" k="25" />
<hkern u1="Y" u2="f" k="6" />
<hkern u1="Y" u2="e" k="35" />
<hkern u1="Y" u2="d" k="35" />
<hkern u1="Y" u2="c" k="35" />
<hkern u1="Y" u2="a" k="35" />
<hkern u1="Y" u2="]" k="-31" />
<hkern u1="Y" u2="Y" k="-18" />
<hkern u1="Y" u2="X" k="-12" />
<hkern u1="Y" u2="W" k="-4" />
<hkern u1="Y" u2="V" k="-23" />
<hkern u1="Y" u2="T" k="-18" />
<hkern u1="Y" u2="S" k="-18" />
<hkern u1="Y" u2="Q" k="-4" />
<hkern u1="Y" u2="O" k="-4" />
<hkern u1="Y" u2="J" k="43" />
<hkern u1="Y" u2="G" k="-4" />
<hkern u1="Y" u2="C" k="-4" />
<hkern u1="Y" u2="A" k="12" />
<hkern u1="Y" u2="&#x40;" k="18" />
<hkern u1="Y" u2="&#x3b;" k="18" />
<hkern u1="Y" u2="&#x3a;" k="18" />
<hkern u1="Y" u2="&#x2e;" k="31" />
<hkern u1="Y" u2="&#x2d;" k="31" />
<hkern u1="Y" u2="&#x2c;" k="31" />
<hkern u1="Y" u2="&#x29;" k="-31" />
<hkern u1="Y" u2="&#x26;" k="18" />
<hkern u1="Z" u2="&#x2122;" k="-31" />
<hkern u1="Z" u2="&#x2014;" k="31" />
<hkern u1="Z" u2="&#x2013;" k="31" />
<hkern u1="Z" u2="&#x153;" k="25" />
<hkern u1="Z" u2="&#x152;" k="29" />
<hkern u1="Z" u2="&#xf0;" k="18" />
<hkern u1="Z" u2="&#xe7;" k="25" />
<hkern u1="Z" u2="&#xe6;" k="25" />
<hkern u1="Z" u2="&#xd8;" k="29" />
<hkern u1="Z" u2="&#xd6;" k="29" />
<hkern u1="Z" u2="&#xd5;" k="29" />
<hkern u1="Z" u2="&#xd4;" k="29" />
<hkern u1="Z" u2="&#xd3;" k="29" />
<hkern u1="Z" u2="&#xd2;" k="29" />
<hkern u1="Z" u2="&#xc6;" k="6" />
<hkern u1="Z" u2="&#xc5;" k="6" />
<hkern u1="Z" u2="&#xc4;" k="6" />
<hkern u1="Z" u2="&#xc3;" k="6" />
<hkern u1="Z" u2="&#xc2;" k="6" />
<hkern u1="Z" u2="&#xc1;" k="6" />
<hkern u1="Z" u2="&#xc0;" k="6" />
<hkern u1="Z" u2="y" k="18" />
<hkern u1="Z" u2="w" k="6" />
<hkern u1="Z" u2="v" k="18" />
<hkern u1="Z" u2="u" k="12" />
<hkern u1="Z" u2="r" k="12" />
<hkern u1="Z" u2="q" k="25" />
<hkern u1="Z" u2="p" k="12" />
<hkern u1="Z" u2="o" k="25" />
<hkern u1="Z" u2="n" k="12" />
<hkern u1="Z" u2="m" k="12" />
<hkern u1="Z" u2="l" k="6" />
<hkern u1="Z" u2="k" k="6" />
<hkern u1="Z" u2="j" k="10" />
<hkern u1="Z" u2="i" k="12" />
<hkern u1="Z" u2="h" k="6" />
<hkern u1="Z" u2="g" k="12" />
<hkern u1="Z" u2="f" k="12" />
<hkern u1="Z" u2="e" k="25" />
<hkern u1="Z" u2="d" k="25" />
<hkern u1="Z" u2="c" k="25" />
<hkern u1="Z" u2="b" k="6" />
<hkern u1="Z" u2="a" k="25" />
<hkern u1="Z" u2="V" k="-4" />
<hkern u1="Z" u2="T" k="6" />
<hkern u1="Z" u2="Q" k="29" />
<hkern u1="Z" u2="O" k="29" />
<hkern u1="Z" u2="J" k="6" />
<hkern u1="Z" u2="G" k="29" />
<hkern u1="Z" u2="C" k="29" />
<hkern u1="Z" u2="A" k="6" />
<hkern u1="Z" u2="&#x2d;" k="31" />
<hkern u1="[" u2="&#x178;" k="-31" />
<hkern u1="[" u2="&#xdd;" k="-31" />
<hkern u1="[" u2="j" k="-92" />
<hkern u1="[" u2="g" k="-12" />
<hkern u1="[" u2="Y" k="-31" />
<hkern u1="[" u2="X" k="-25" />
<hkern u1="[" u2="W" k="-6" />
<hkern u1="[" u2="V" k="-18" />
<hkern u1="\" u2="&#x178;" k="37" />
<hkern u1="\" u2="&#x153;" k="6" />
<hkern u1="\" u2="&#xe7;" k="6" />
<hkern u1="\" u2="&#xe6;" k="6" />
<hkern u1="\" u2="&#xdd;" k="37" />
<hkern u1="\" u2="q" k="6" />
<hkern u1="\" u2="o" k="6" />
<hkern u1="\" u2="j" k="-80" />
<hkern u1="\" u2="e" k="6" />
<hkern u1="\" u2="d" k="6" />
<hkern u1="\" u2="c" k="6" />
<hkern u1="\" u2="a" k="6" />
<hkern u1="\" u2="Y" k="37" />
<hkern u1="\" u2="W" k="31" />
<hkern u1="\" u2="V" k="43" />
<hkern u1="\" u2="T" k="55" />
<hkern u1="a" u2="&#x2122;" k="12" />
<hkern u1="a" u2="&#x201c;" k="18" />
<hkern u1="a" u2="&#x2018;" k="18" />
<hkern u1="a" u2="z" k="-4" />
<hkern u1="a" u2="y" k="4" />
<hkern u1="a" u2="v" k="4" />
<hkern u1="a" u2="t" k="6" />
<hkern u1="a" u2="\" k="6" />
<hkern u1="a" u2="&#x3f;" k="6" />
<hkern u1="b" u2="&#x2122;" k="18" />
<hkern u1="b" u2="&#x2026;" k="12" />
<hkern u1="b" u2="&#x201e;" k="12" />
<hkern u1="b" u2="&#x201c;" k="25" />
<hkern u1="b" u2="&#x201a;" k="12" />
<hkern u1="b" u2="&#x2018;" k="25" />
<hkern u1="b" u2="z" k="10" />
<hkern u1="b" u2="y" k="4" />
<hkern u1="b" u2="x" k="4" />
<hkern u1="b" u2="w" k="12" />
<hkern u1="b" u2="v" k="4" />
<hkern u1="b" u2="t" k="6" />
<hkern u1="b" u2="s" k="-4" />
<hkern u1="b" u2="\" k="6" />
<hkern u1="b" u2="&#x3f;" k="12" />
<hkern u1="b" u2="&#x3b;" k="6" />
<hkern u1="b" u2="&#x3a;" k="6" />
<hkern u1="b" u2="&#x2f;" k="6" />
<hkern u1="b" u2="&#x2e;" k="12" />
<hkern u1="b" u2="&#x2c;" k="12" />
<hkern u1="d" u2="y" k="-6" />
<hkern u1="d" u2="v" k="-6" />
<hkern u1="e" u2="y" k="4" />
<hkern u1="e" u2="x" k="10" />
<hkern u1="e" u2="v" k="4" />
<hkern u1="f" u2="&#x2122;" k="-55" />
<hkern u1="f" u2="&#x203a;" k="12" />
<hkern u1="f" u2="&#x2039;" k="31" />
<hkern u1="f" u2="&#x2026;" k="49" />
<hkern u1="f" u2="&#x201e;" k="49" />
<hkern u1="f" u2="&#x201d;" k="-31" />
<hkern u1="f" u2="&#x201c;" k="-18" />
<hkern u1="f" u2="&#x201a;" k="49" />
<hkern u1="f" u2="&#x2019;" k="-31" />
<hkern u1="f" u2="&#x2018;" k="-18" />
<hkern u1="f" u2="&#x2014;" k="18" />
<hkern u1="f" u2="&#x2013;" k="18" />
<hkern u1="f" u2="&#x153;" k="6" />
<hkern u1="f" u2="&#xf0;" k="12" />
<hkern u1="f" u2="&#xe7;" k="6" />
<hkern u1="f" u2="&#xe6;" k="6" />
<hkern u1="f" u2="&#xbb;" k="12" />
<hkern u1="f" u2="&#xae;" k="-18" />
<hkern u1="f" u2="&#xab;" k="31" />
<hkern u1="f" u2="&#xa9;" k="-18" />
<hkern u1="f" u2="&#x7d;" k="-43" />
<hkern u1="f" u2="y" k="-25" />
<hkern u1="f" u2="w" k="-6" />
<hkern u1="f" u2="v" k="-25" />
<hkern u1="f" u2="u" k="4" />
<hkern u1="f" u2="r" k="4" />
<hkern u1="f" u2="q" k="6" />
<hkern u1="f" u2="p" k="4" />
<hkern u1="f" u2="o" k="6" />
<hkern u1="f" u2="n" k="4" />
<hkern u1="f" u2="m" k="4" />
<hkern u1="f" u2="e" k="6" />
<hkern u1="f" u2="d" k="6" />
<hkern u1="f" u2="c" k="6" />
<hkern u1="f" u2="a" k="6" />
<hkern u1="f" u2="]" k="-43" />
<hkern u1="f" u2="\" k="-25" />
<hkern u1="f" u2="&#x40;" k="-18" />
<hkern u1="f" u2="&#x3f;" k="-25" />
<hkern u1="f" u2="&#x3b;" k="6" />
<hkern u1="f" u2="&#x3a;" k="6" />
<hkern u1="f" u2="&#x2f;" k="31" />
<hkern u1="f" u2="&#x2e;" k="49" />
<hkern u1="f" u2="&#x2d;" k="18" />
<hkern u1="f" u2="&#x2c;" k="49" />
<hkern u1="f" u2="&#x2a;" k="-12" />
<hkern u1="f" u2="&#x29;" k="-43" />
<hkern u1="f" u2="&#x26;" k="18" />
<hkern u1="g" u2="&#x201d;" k="-25" />
<hkern u1="g" u2="&#x201c;" k="-12" />
<hkern u1="g" u2="&#x2019;" k="-25" />
<hkern u1="g" u2="&#x2018;" k="-12" />
<hkern u1="g" u2="&#x7d;" k="-12" />
<hkern u1="g" u2="t" k="-6" />
<hkern u1="g" u2="j" k="-31" />
<hkern u1="g" u2="g" k="-6" />
<hkern u1="g" u2="]" k="-12" />
<hkern u1="g" u2="&#x3b;" k="-12" />
<hkern u1="g" u2="&#x2f;" k="-25" />
<hkern u1="g" u2="&#x29;" k="-12" />
<hkern u1="h" u2="&#x2122;" k="12" />
<hkern u1="h" u2="&#x201c;" k="18" />
<hkern u1="h" u2="&#x2018;" k="18" />
<hkern u1="h" u2="z" k="-4" />
<hkern u1="h" u2="y" k="4" />
<hkern u1="h" u2="v" k="4" />
<hkern u1="h" u2="t" k="6" />
<hkern u1="h" u2="\" k="6" />
<hkern u1="h" u2="&#x3f;" k="6" />
<hkern u1="i" u2="y" k="-4" />
<hkern u1="i" u2="w" k="-4" />
<hkern u1="i" u2="v" k="-4" />
<hkern u1="j" u2="y" k="-12" />
<hkern u1="j" u2="v" k="-12" />
<hkern u1="j" u2="j" k="-12" />
<hkern u1="k" u2="&#x153;" k="6" />
<hkern u1="k" u2="&#xe7;" k="6" />
<hkern u1="k" u2="&#xe6;" k="6" />
<hkern u1="k" u2="y" k="-16" />
<hkern u1="k" u2="x" k="-6" />
<hkern u1="k" u2="v" k="-16" />
<hkern u1="k" u2="q" k="6" />
<hkern u1="k" u2="o" k="6" />
<hkern u1="k" u2="g" k="12" />
<hkern u1="k" u2="e" k="6" />
<hkern u1="k" u2="d" k="6" />
<hkern u1="k" u2="c" k="6" />
<hkern u1="k" u2="a" k="6" />
<hkern u1="l" u2="y" k="-6" />
<hkern u1="l" u2="v" k="-6" />
<hkern u1="m" u2="&#x2122;" k="12" />
<hkern u1="m" u2="&#x201c;" k="18" />
<hkern u1="m" u2="&#x2018;" k="18" />
<hkern u1="m" u2="z" k="-4" />
<hkern u1="m" u2="y" k="4" />
<hkern u1="m" u2="v" k="4" />
<hkern u1="m" u2="t" k="6" />
<hkern u1="m" u2="\" k="6" />
<hkern u1="m" u2="&#x3f;" k="6" />
<hkern u1="n" u2="&#x2122;" k="12" />
<hkern u1="n" u2="&#x201c;" k="18" />
<hkern u1="n" u2="&#x2018;" k="18" />
<hkern u1="n" u2="z" k="-4" />
<hkern u1="n" u2="y" k="4" />
<hkern u1="n" u2="v" k="4" />
<hkern u1="n" u2="t" k="6" />
<hkern u1="n" u2="\" k="6" />
<hkern u1="n" u2="&#x3f;" k="6" />
<hkern u1="o" u2="&#x2122;" k="18" />
<hkern u1="o" u2="&#x2026;" k="12" />
<hkern u1="o" u2="&#x201e;" k="12" />
<hkern u1="o" u2="&#x201c;" k="25" />
<hkern u1="o" u2="&#x201a;" k="12" />
<hkern u1="o" u2="&#x2018;" k="25" />
<hkern u1="o" u2="z" k="10" />
<hkern u1="o" u2="y" k="4" />
<hkern u1="o" u2="x" k="4" />
<hkern u1="o" u2="w" k="12" />
<hkern u1="o" u2="v" k="4" />
<hkern u1="o" u2="t" k="6" />
<hkern u1="o" u2="s" k="-4" />
<hkern u1="o" u2="\" k="6" />
<hkern u1="o" u2="&#x3f;" k="12" />
<hkern u1="o" u2="&#x3b;" k="6" />
<hkern u1="o" u2="&#x3a;" k="6" />
<hkern u1="o" u2="&#x2f;" k="6" />
<hkern u1="o" u2="&#x2e;" k="12" />
<hkern u1="o" u2="&#x2c;" k="12" />
<hkern u1="p" u2="&#x2122;" k="18" />
<hkern u1="p" u2="&#x2026;" k="12" />
<hkern u1="p" u2="&#x201e;" k="12" />
<hkern u1="p" u2="&#x201c;" k="25" />
<hkern u1="p" u2="&#x201a;" k="12" />
<hkern u1="p" u2="&#x2018;" k="25" />
<hkern u1="p" u2="z" k="10" />
<hkern u1="p" u2="y" k="4" />
<hkern u1="p" u2="x" k="4" />
<hkern u1="p" u2="w" k="12" />
<hkern u1="p" u2="v" k="4" />
<hkern u1="p" u2="t" k="6" />
<hkern u1="p" u2="s" k="-4" />
<hkern u1="p" u2="\" k="6" />
<hkern u1="p" u2="&#x3f;" k="12" />
<hkern u1="p" u2="&#x3b;" k="6" />
<hkern u1="p" u2="&#x3a;" k="6" />
<hkern u1="p" u2="&#x2f;" k="6" />
<hkern u1="p" u2="&#x2e;" k="12" />
<hkern u1="p" u2="&#x2c;" k="12" />
<hkern u1="q" u2="j" k="-18" />
<hkern u1="r" u2="&#x2026;" k="31" />
<hkern u1="r" u2="&#x201e;" k="31" />
<hkern u1="r" u2="&#x201d;" k="-25" />
<hkern u1="r" u2="&#x201c;" k="-12" />
<hkern u1="r" u2="&#x201a;" k="31" />
<hkern u1="r" u2="&#x2019;" k="-25" />
<hkern u1="r" u2="&#x2018;" k="-12" />
<hkern u1="r" u2="&#x2014;" k="12" />
<hkern u1="r" u2="&#x2013;" k="12" />
<hkern u1="r" u2="z" k="6" />
<hkern u1="r" u2="y" k="-25" />
<hkern u1="r" u2="x" k="-6" />
<hkern u1="r" u2="w" k="-18" />
<hkern u1="r" u2="v" k="-25" />
<hkern u1="r" u2="t" k="-12" />
<hkern u1="r" u2="g" k="6" />
<hkern u1="r" u2="f" k="-10" />
<hkern u1="r" u2="&#x3f;" k="-12" />
<hkern u1="r" u2="&#x2f;" k="37" />
<hkern u1="r" u2="&#x2e;" k="31" />
<hkern u1="r" u2="&#x2d;" k="12" />
<hkern u1="r" u2="&#x2c;" k="31" />
<hkern u1="s" u2="y" k="-4" />
<hkern u1="s" u2="v" k="-4" />
<hkern u1="t" u2="&#x2026;" k="-6" />
<hkern u1="t" u2="&#x201e;" k="-6" />
<hkern u1="t" u2="&#x201c;" k="-6" />
<hkern u1="t" u2="&#x201a;" k="-6" />
<hkern u1="t" u2="&#x2018;" k="-6" />
<hkern u1="t" u2="&#x153;" k="4" />
<hkern u1="t" u2="&#xe7;" k="4" />
<hkern u1="t" u2="&#xe6;" k="4" />
<hkern u1="t" u2="z" k="-6" />
<hkern u1="t" u2="y" k="-16" />
<hkern u1="t" u2="x" k="-4" />
<hkern u1="t" u2="w" k="-6" />
<hkern u1="t" u2="v" k="-16" />
<hkern u1="t" u2="q" k="4" />
<hkern u1="t" u2="o" k="4" />
<hkern u1="t" u2="e" k="4" />
<hkern u1="t" u2="d" k="4" />
<hkern u1="t" u2="c" k="4" />
<hkern u1="t" u2="a" k="4" />
<hkern u1="t" u2="&#x2e;" k="-6" />
<hkern u1="t" u2="&#x2c;" k="-6" />
<hkern u1="u" u2="y" k="-6" />
<hkern u1="u" u2="v" k="-6" />
<hkern u1="v" u2="&#x2026;" k="31" />
<hkern u1="v" u2="&#x201e;" k="31" />
<hkern u1="v" u2="&#x201d;" k="-18" />
<hkern u1="v" u2="&#x201c;" k="-18" />
<hkern u1="v" u2="&#x201a;" k="31" />
<hkern u1="v" u2="&#x2019;" k="-18" />
<hkern u1="v" u2="&#x2018;" k="-18" />
<hkern u1="v" u2="&#x2014;" k="6" />
<hkern u1="v" u2="&#x2013;" k="6" />
<hkern u1="v" u2="&#x153;" k="-4" />
<hkern u1="v" u2="&#xe7;" k="-4" />
<hkern u1="v" u2="&#xe6;" k="-4" />
<hkern u1="v" u2="z" k="-6" />
<hkern u1="v" u2="y" k="-25" />
<hkern u1="v" u2="w" k="-12" />
<hkern u1="v" u2="v" k="-25" />
<hkern u1="v" u2="t" k="-6" />
<hkern u1="v" u2="s" k="-4" />
<hkern u1="v" u2="q" k="-4" />
<hkern u1="v" u2="o" k="-4" />
<hkern u1="v" u2="e" k="-4" />
<hkern u1="v" u2="d" k="-4" />
<hkern u1="v" u2="c" k="-4" />
<hkern u1="v" u2="a" k="-4" />
<hkern u1="v" u2="&#x2e;" k="31" />
<hkern u1="v" u2="&#x2d;" k="6" />
<hkern u1="v" u2="&#x2c;" k="31" />
<hkern u1="w" u2="&#x2026;" k="18" />
<hkern u1="w" u2="&#x201e;" k="18" />
<hkern u1="w" u2="&#x201d;" k="-12" />
<hkern u1="w" u2="&#x201c;" k="-12" />
<hkern u1="w" u2="&#x201a;" k="18" />
<hkern u1="w" u2="&#x2019;" k="-12" />
<hkern u1="w" u2="&#x2018;" k="-12" />
<hkern u1="w" u2="y" k="-12" />
<hkern u1="w" u2="v" k="-12" />
<hkern u1="w" u2="&#x2e;" k="18" />
<hkern u1="w" u2="&#x2c;" k="18" />
<hkern u1="x" u2="&#x2039;" k="31" />
<hkern u1="x" u2="&#x201d;" k="-12" />
<hkern u1="x" u2="&#x201c;" k="-6" />
<hkern u1="x" u2="&#x2019;" k="-12" />
<hkern u1="x" u2="&#x2018;" k="-6" />
<hkern u1="x" u2="&#x2014;" k="18" />
<hkern u1="x" u2="&#x2013;" k="18" />
<hkern u1="x" u2="&#x153;" k="10" />
<hkern u1="x" u2="&#xe7;" k="10" />
<hkern u1="x" u2="&#xe6;" k="10" />
<hkern u1="x" u2="&#xab;" k="31" />
<hkern u1="x" u2="z" k="-16" />
<hkern u1="x" u2="y" k="-18" />
<hkern u1="x" u2="x" k="-6" />
<hkern u1="x" u2="v" k="-18" />
<hkern u1="x" u2="q" k="10" />
<hkern u1="x" u2="o" k="10" />
<hkern u1="x" u2="e" k="10" />
<hkern u1="x" u2="d" k="10" />
<hkern u1="x" u2="c" k="10" />
<hkern u1="x" u2="a" k="10" />
<hkern u1="x" u2="&#x2d;" k="18" />
<hkern u1="y" u2="&#x2026;" k="31" />
<hkern u1="y" u2="&#x201e;" k="31" />
<hkern u1="y" u2="&#x201d;" k="-18" />
<hkern u1="y" u2="&#x201c;" k="-18" />
<hkern u1="y" u2="&#x201a;" k="31" />
<hkern u1="y" u2="&#x2019;" k="-18" />
<hkern u1="y" u2="&#x2018;" k="-18" />
<hkern u1="y" u2="&#x2014;" k="6" />
<hkern u1="y" u2="&#x2013;" k="6" />
<hkern u1="y" u2="&#x153;" k="-4" />
<hkern u1="y" u2="&#xe7;" k="-4" />
<hkern u1="y" u2="&#xe6;" k="-4" />
<hkern u1="y" u2="z" k="-6" />
<hkern u1="y" u2="y" k="-25" />
<hkern u1="y" u2="w" k="-12" />
<hkern u1="y" u2="v" k="-25" />
<hkern u1="y" u2="t" k="-6" />
<hkern u1="y" u2="s" k="-4" />
<hkern u1="y" u2="q" k="-4" />
<hkern u1="y" u2="o" k="-4" />
<hkern u1="y" u2="e" k="-4" />
<hkern u1="y" u2="d" k="-4" />
<hkern u1="y" u2="c" k="-4" />
<hkern u1="y" u2="a" k="-4" />
<hkern u1="y" u2="&#x2e;" k="31" />
<hkern u1="y" u2="&#x2d;" k="6" />
<hkern u1="y" u2="&#x2c;" k="31" />
<hkern u1="z" u2="&#x2039;" k="37" />
<hkern u1="z" u2="&#x2014;" k="18" />
<hkern u1="z" u2="&#x2013;" k="18" />
<hkern u1="z" u2="&#x153;" k="12" />
<hkern u1="z" u2="&#xe7;" k="12" />
<hkern u1="z" u2="&#xe6;" k="12" />
<hkern u1="z" u2="&#xab;" k="37" />
<hkern u1="z" u2="y" k="-18" />
<hkern u1="z" u2="v" k="-18" />
<hkern u1="z" u2="u" k="10" />
<hkern u1="z" u2="t" k="-4" />
<hkern u1="z" u2="r" k="10" />
<hkern u1="z" u2="q" k="12" />
<hkern u1="z" u2="p" k="10" />
<hkern u1="z" u2="o" k="12" />
<hkern u1="z" u2="n" k="10" />
<hkern u1="z" u2="m" k="10" />
<hkern u1="z" u2="g" k="6" />
<hkern u1="z" u2="e" k="12" />
<hkern u1="z" u2="d" k="12" />
<hkern u1="z" u2="c" k="12" />
<hkern u1="z" u2="a" k="12" />
<hkern u1="z" u2="&#x2d;" k="18" />
<hkern u1="&#x7b;" u2="&#x178;" k="-31" />
<hkern u1="&#x7b;" u2="&#xdd;" k="-31" />
<hkern u1="&#x7b;" u2="j" k="-92" />
<hkern u1="&#x7b;" u2="g" k="-12" />
<hkern u1="&#x7b;" u2="Y" k="-31" />
<hkern u1="&#x7b;" u2="X" k="-25" />
<hkern u1="&#x7b;" u2="W" k="-6" />
<hkern u1="&#x7b;" u2="V" k="-18" />
<hkern u1="&#xa3;" u2="&#x34;" k="12" />
<hkern u1="&#xa3;" u2="&#x31;" k="-6" />
<hkern u1="&#xa4;" u2="&#x34;" k="12" />
<hkern u1="&#xa9;" u2="&#x178;" k="18" />
<hkern u1="&#xa9;" u2="&#xdd;" k="18" />
<hkern u1="&#xa9;" u2="&#xc6;" k="6" />
<hkern u1="&#xa9;" u2="&#xc5;" k="6" />
<hkern u1="&#xa9;" u2="&#xc4;" k="6" />
<hkern u1="&#xa9;" u2="&#xc3;" k="6" />
<hkern u1="&#xa9;" u2="&#xc2;" k="6" />
<hkern u1="&#xa9;" u2="&#xc1;" k="6" />
<hkern u1="&#xa9;" u2="&#xc0;" k="6" />
<hkern u1="&#xa9;" u2="Y" k="18" />
<hkern u1="&#xa9;" u2="W" k="12" />
<hkern u1="&#xa9;" u2="T" k="18" />
<hkern u1="&#xa9;" u2="A" k="6" />
<hkern u1="&#xab;" u2="&#x178;" k="25" />
<hkern u1="&#xab;" u2="&#xdd;" k="25" />
<hkern u1="&#xab;" u2="Y" k="25" />
<hkern u1="&#xab;" u2="W" k="12" />
<hkern u1="&#xab;" u2="V" k="12" />
<hkern u1="&#xab;" u2="T" k="25" />
<hkern u1="&#xae;" u2="&#x178;" k="18" />
<hkern u1="&#xae;" u2="&#xdd;" k="18" />
<hkern u1="&#xae;" u2="&#xc6;" k="6" />
<hkern u1="&#xae;" u2="&#xc5;" k="6" />
<hkern u1="&#xae;" u2="&#xc4;" k="6" />
<hkern u1="&#xae;" u2="&#xc3;" k="6" />
<hkern u1="&#xae;" u2="&#xc2;" k="6" />
<hkern u1="&#xae;" u2="&#xc1;" k="6" />
<hkern u1="&#xae;" u2="&#xc0;" k="6" />
<hkern u1="&#xae;" u2="Y" k="18" />
<hkern u1="&#xae;" u2="W" k="12" />
<hkern u1="&#xae;" u2="T" k="18" />
<hkern u1="&#xae;" u2="A" k="6" />
<hkern u1="&#xb0;" u2="&#x34;" k="41" />
<hkern u1="&#xbb;" u2="&#x178;" k="31" />
<hkern u1="&#xbb;" u2="&#xdd;" k="31" />
<hkern u1="&#xbb;" u2="z" k="31" />
<hkern u1="&#xbb;" u2="x" k="31" />
<hkern u1="&#xbb;" u2="Y" k="31" />
<hkern u1="&#xbb;" u2="W" k="25" />
<hkern u1="&#xbb;" u2="V" k="31" />
<hkern u1="&#xbb;" u2="T" k="68" />
<hkern u1="&#xbf;" u2="&#x178;" k="25" />
<hkern u1="&#xbf;" u2="&#x152;" k="12" />
<hkern u1="&#xbf;" u2="&#xdd;" k="25" />
<hkern u1="&#xbf;" u2="&#xd8;" k="12" />
<hkern u1="&#xbf;" u2="&#xd6;" k="12" />
<hkern u1="&#xbf;" u2="&#xd5;" k="12" />
<hkern u1="&#xbf;" u2="&#xd4;" k="12" />
<hkern u1="&#xbf;" u2="&#xd3;" k="12" />
<hkern u1="&#xbf;" u2="&#xd2;" k="12" />
<hkern u1="&#xbf;" u2="y" k="18" />
<hkern u1="&#xbf;" u2="x" k="6" />
<hkern u1="&#xbf;" u2="w" k="12" />
<hkern u1="&#xbf;" u2="v" k="18" />
<hkern u1="&#xbf;" u2="Y" k="25" />
<hkern u1="&#xbf;" u2="W" k="18" />
<hkern u1="&#xbf;" u2="V" k="31" />
<hkern u1="&#xbf;" u2="T" k="37" />
<hkern u1="&#xbf;" u2="Q" k="12" />
<hkern u1="&#xbf;" u2="O" k="12" />
<hkern u1="&#xbf;" u2="G" k="12" />
<hkern u1="&#xbf;" u2="C" k="12" />
<hkern u1="&#xbf;" u2="&#x37;" k="18" />
<hkern u1="&#xbf;" u2="&#x33;" k="-6" />
<hkern u1="&#xbf;" u2="&#x31;" k="25" />
<hkern u1="&#xc6;" u2="&#x153;" k="23" />
<hkern u1="&#xc6;" u2="&#xe7;" k="23" />
<hkern u1="&#xc6;" u2="&#xe6;" k="23" />
<hkern u1="&#xc6;" u2="&#xae;" k="12" />
<hkern u1="&#xc6;" u2="&#xa9;" k="12" />
<hkern u1="&#xc6;" u2="y" k="12" />
<hkern u1="&#xc6;" u2="v" k="12" />
<hkern u1="&#xc6;" u2="u" k="12" />
<hkern u1="&#xc6;" u2="t" k="12" />
<hkern u1="&#xc6;" u2="s" k="12" />
<hkern u1="&#xc6;" u2="r" k="12" />
<hkern u1="&#xc6;" u2="q" k="23" />
<hkern u1="&#xc6;" u2="p" k="12" />
<hkern u1="&#xc6;" u2="o" k="23" />
<hkern u1="&#xc6;" u2="n" k="12" />
<hkern u1="&#xc6;" u2="m" k="12" />
<hkern u1="&#xc6;" u2="g" k="4" />
<hkern u1="&#xc6;" u2="f" k="6" />
<hkern u1="&#xc6;" u2="e" k="23" />
<hkern u1="&#xc6;" u2="d" k="23" />
<hkern u1="&#xc6;" u2="c" k="23" />
<hkern u1="&#xc6;" u2="a" k="23" />
<hkern u1="&#xc6;" u2="T" k="-10" />
<hkern u1="&#xc6;" u2="&#x40;" k="12" />
<hkern u1="&#xd0;" u2="&#x2026;" k="25" />
<hkern u1="&#xd0;" u2="&#x201e;" k="25" />
<hkern u1="&#xd0;" u2="&#x201c;" k="12" />
<hkern u1="&#xd0;" u2="&#x201a;" k="25" />
<hkern u1="&#xd0;" u2="&#x2018;" k="12" />
<hkern u1="&#xd0;" u2="&#x178;" k="23" />
<hkern u1="&#xd0;" u2="&#x153;" k="4" />
<hkern u1="&#xd0;" u2="&#xe7;" k="4" />
<hkern u1="&#xd0;" u2="&#xe6;" k="4" />
<hkern u1="&#xd0;" u2="&#xdd;" k="23" />
<hkern u1="&#xd0;" u2="z" k="6" />
<hkern u1="&#xd0;" u2="y" k="-6" />
<hkern u1="&#xd0;" u2="x" k="6" />
<hkern u1="&#xd0;" u2="w" k="-4" />
<hkern u1="&#xd0;" u2="v" k="-6" />
<hkern u1="&#xd0;" u2="u" k="4" />
<hkern u1="&#xd0;" u2="r" k="4" />
<hkern u1="&#xd0;" u2="q" k="4" />
<hkern u1="&#xd0;" u2="p" k="4" />
<hkern u1="&#xd0;" u2="o" k="4" />
<hkern u1="&#xd0;" u2="n" k="4" />
<hkern u1="&#xd0;" u2="m" k="4" />
<hkern u1="&#xd0;" u2="l" k="6" />
<hkern u1="&#xd0;" u2="k" k="6" />
<hkern u1="&#xd0;" u2="h" k="6" />
<hkern u1="&#xd0;" u2="e" k="4" />
<hkern u1="&#xd0;" u2="d" k="4" />
<hkern u1="&#xd0;" u2="c" k="4" />
<hkern u1="&#xd0;" u2="b" k="6" />
<hkern u1="&#xd0;" u2="a" k="4" />
<hkern u1="&#xd0;" u2="Z" k="29" />
<hkern u1="&#xd0;" u2="Y" k="23" />
<hkern u1="&#xd0;" u2="X" k="16" />
<hkern u1="&#xd0;" u2="W" k="25" />
<hkern u1="&#xd0;" u2="V" k="25" />
<hkern u1="&#xd0;" u2="T" k="31" />
<hkern u1="&#xd0;" u2="J" k="29" />
<hkern u1="&#xd0;" u2="&#x3f;" k="12" />
<hkern u1="&#xd0;" u2="&#x2f;" k="31" />
<hkern u1="&#xd0;" u2="&#x2e;" k="25" />
<hkern u1="&#xd0;" u2="&#x2c;" k="25" />
<hkern u1="&#xd8;" u2="&#x2026;" k="25" />
<hkern u1="&#xd8;" u2="&#x201e;" k="25" />
<hkern u1="&#xd8;" u2="&#x201c;" k="12" />
<hkern u1="&#xd8;" u2="&#x201a;" k="25" />
<hkern u1="&#xd8;" u2="&#x2018;" k="12" />
<hkern u1="&#xd8;" u2="&#x178;" k="23" />
<hkern u1="&#xd8;" u2="&#x153;" k="4" />
<hkern u1="&#xd8;" u2="&#xe7;" k="4" />
<hkern u1="&#xd8;" u2="&#xe6;" k="4" />
<hkern u1="&#xd8;" u2="&#xdd;" k="23" />
<hkern u1="&#xd8;" u2="z" k="6" />
<hkern u1="&#xd8;" u2="y" k="-6" />
<hkern u1="&#xd8;" u2="x" k="6" />
<hkern u1="&#xd8;" u2="w" k="-4" />
<hkern u1="&#xd8;" u2="v" k="-6" />
<hkern u1="&#xd8;" u2="u" k="4" />
<hkern u1="&#xd8;" u2="r" k="4" />
<hkern u1="&#xd8;" u2="q" k="4" />
<hkern u1="&#xd8;" u2="p" k="4" />
<hkern u1="&#xd8;" u2="o" k="4" />
<hkern u1="&#xd8;" u2="n" k="4" />
<hkern u1="&#xd8;" u2="m" k="4" />
<hkern u1="&#xd8;" u2="l" k="6" />
<hkern u1="&#xd8;" u2="k" k="6" />
<hkern u1="&#xd8;" u2="h" k="6" />
<hkern u1="&#xd8;" u2="e" k="4" />
<hkern u1="&#xd8;" u2="d" k="4" />
<hkern u1="&#xd8;" u2="c" k="4" />
<hkern u1="&#xd8;" u2="b" k="6" />
<hkern u1="&#xd8;" u2="a" k="4" />
<hkern u1="&#xd8;" u2="Z" k="29" />
<hkern u1="&#xd8;" u2="Y" k="23" />
<hkern u1="&#xd8;" u2="X" k="16" />
<hkern u1="&#xd8;" u2="W" k="25" />
<hkern u1="&#xd8;" u2="V" k="25" />
<hkern u1="&#xd8;" u2="T" k="31" />
<hkern u1="&#xd8;" u2="J" k="29" />
<hkern u1="&#xd8;" u2="&#x3f;" k="12" />
<hkern u1="&#xd8;" u2="&#x2f;" k="31" />
<hkern u1="&#xd8;" u2="&#x2e;" k="25" />
<hkern u1="&#xd8;" u2="&#x2c;" k="25" />
<hkern u1="&#xde;" u2="&#x2026;" k="25" />
<hkern u1="&#xde;" u2="&#x201e;" k="25" />
<hkern u1="&#xde;" u2="&#x201c;" k="12" />
<hkern u1="&#xde;" u2="&#x201a;" k="25" />
<hkern u1="&#xde;" u2="&#x2018;" k="12" />
<hkern u1="&#xde;" u2="&#x178;" k="23" />
<hkern u1="&#xde;" u2="&#x153;" k="4" />
<hkern u1="&#xde;" u2="&#xe7;" k="4" />
<hkern u1="&#xde;" u2="&#xe6;" k="4" />
<hkern u1="&#xde;" u2="&#xdd;" k="23" />
<hkern u1="&#xde;" u2="z" k="6" />
<hkern u1="&#xde;" u2="y" k="-6" />
<hkern u1="&#xde;" u2="x" k="6" />
<hkern u1="&#xde;" u2="w" k="-4" />
<hkern u1="&#xde;" u2="v" k="-6" />
<hkern u1="&#xde;" u2="u" k="4" />
<hkern u1="&#xde;" u2="r" k="4" />
<hkern u1="&#xde;" u2="q" k="4" />
<hkern u1="&#xde;" u2="p" k="4" />
<hkern u1="&#xde;" u2="o" k="4" />
<hkern u1="&#xde;" u2="n" k="4" />
<hkern u1="&#xde;" u2="m" k="4" />
<hkern u1="&#xde;" u2="l" k="6" />
<hkern u1="&#xde;" u2="k" k="6" />
<hkern u1="&#xde;" u2="h" k="6" />
<hkern u1="&#xde;" u2="e" k="4" />
<hkern u1="&#xde;" u2="d" k="4" />
<hkern u1="&#xde;" u2="c" k="4" />
<hkern u1="&#xde;" u2="b" k="6" />
<hkern u1="&#xde;" u2="a" k="4" />
<hkern u1="&#xde;" u2="Z" k="29" />
<hkern u1="&#xde;" u2="Y" k="23" />
<hkern u1="&#xde;" u2="X" k="16" />
<hkern u1="&#xde;" u2="W" k="25" />
<hkern u1="&#xde;" u2="V" k="25" />
<hkern u1="&#xde;" u2="T" k="31" />
<hkern u1="&#xde;" u2="J" k="29" />
<hkern u1="&#xde;" u2="&#x3f;" k="12" />
<hkern u1="&#xde;" u2="&#x2f;" k="31" />
<hkern u1="&#xde;" u2="&#x2e;" k="25" />
<hkern u1="&#xde;" u2="&#x2c;" k="25" />
<hkern u1="&#xdf;" u2="&#x2122;" k="18" />
<hkern u1="&#xdf;" u2="&#x2026;" k="12" />
<hkern u1="&#xdf;" u2="&#x201e;" k="12" />
<hkern u1="&#xdf;" u2="&#x201c;" k="25" />
<hkern u1="&#xdf;" u2="&#x201a;" k="12" />
<hkern u1="&#xdf;" u2="&#x2018;" k="25" />
<hkern u1="&#xdf;" u2="z" k="10" />
<hkern u1="&#xdf;" u2="y" k="4" />
<hkern u1="&#xdf;" u2="x" k="4" />
<hkern u1="&#xdf;" u2="w" k="12" />
<hkern u1="&#xdf;" u2="v" k="4" />
<hkern u1="&#xdf;" u2="t" k="6" />
<hkern u1="&#xdf;" u2="s" k="-4" />
<hkern u1="&#xdf;" u2="g" k="6" />
<hkern u1="&#xdf;" u2="\" k="6" />
<hkern u1="&#xdf;" u2="&#x3f;" k="12" />
<hkern u1="&#xdf;" u2="&#x3b;" k="6" />
<hkern u1="&#xdf;" u2="&#x3a;" k="6" />
<hkern u1="&#xdf;" u2="&#x2f;" k="6" />
<hkern u1="&#xdf;" u2="&#x2e;" k="12" />
<hkern u1="&#xdf;" u2="&#x2c;" k="12" />
<hkern u1="&#xe6;" u2="y" k="4" />
<hkern u1="&#xe6;" u2="x" k="10" />
<hkern u1="&#xe6;" u2="v" k="4" />
<hkern u1="&#xf8;" u2="&#x2122;" k="18" />
<hkern u1="&#xf8;" u2="&#x2026;" k="12" />
<hkern u1="&#xf8;" u2="&#x201e;" k="12" />
<hkern u1="&#xf8;" u2="&#x201c;" k="25" />
<hkern u1="&#xf8;" u2="&#x201a;" k="12" />
<hkern u1="&#xf8;" u2="&#x2018;" k="25" />
<hkern u1="&#xf8;" u2="z" k="10" />
<hkern u1="&#xf8;" u2="y" k="4" />
<hkern u1="&#xf8;" u2="x" k="4" />
<hkern u1="&#xf8;" u2="w" k="12" />
<hkern u1="&#xf8;" u2="v" k="4" />
<hkern u1="&#xf8;" u2="t" k="6" />
<hkern u1="&#xf8;" u2="s" k="-4" />
<hkern u1="&#xf8;" u2="\" k="6" />
<hkern u1="&#xf8;" u2="&#x3f;" k="12" />
<hkern u1="&#xf8;" u2="&#x3b;" k="6" />
<hkern u1="&#xf8;" u2="&#x3a;" k="6" />
<hkern u1="&#xf8;" u2="&#x2f;" k="6" />
<hkern u1="&#xf8;" u2="&#x2e;" k="12" />
<hkern u1="&#xf8;" u2="&#x2c;" k="12" />
<hkern u1="&#x152;" u2="&#x153;" k="23" />
<hkern u1="&#x152;" u2="&#xe7;" k="23" />
<hkern u1="&#x152;" u2="&#xe6;" k="23" />
<hkern u1="&#x152;" u2="&#xae;" k="12" />
<hkern u1="&#x152;" u2="&#xa9;" k="12" />
<hkern u1="&#x152;" u2="y" k="12" />
<hkern u1="&#x152;" u2="v" k="12" />
<hkern u1="&#x152;" u2="u" k="12" />
<hkern u1="&#x152;" u2="t" k="12" />
<hkern u1="&#x152;" u2="s" k="12" />
<hkern u1="&#x152;" u2="r" k="12" />
<hkern u1="&#x152;" u2="q" k="23" />
<hkern u1="&#x152;" u2="p" k="12" />
<hkern u1="&#x152;" u2="o" k="23" />
<hkern u1="&#x152;" u2="n" k="12" />
<hkern u1="&#x152;" u2="m" k="12" />
<hkern u1="&#x152;" u2="g" k="4" />
<hkern u1="&#x152;" u2="f" k="6" />
<hkern u1="&#x152;" u2="e" k="23" />
<hkern u1="&#x152;" u2="d" k="23" />
<hkern u1="&#x152;" u2="c" k="23" />
<hkern u1="&#x152;" u2="a" k="23" />
<hkern u1="&#x152;" u2="T" k="-10" />
<hkern u1="&#x152;" u2="&#x40;" k="12" />
<hkern u1="&#x153;" u2="y" k="4" />
<hkern u1="&#x153;" u2="x" k="10" />
<hkern u1="&#x153;" u2="v" k="4" />
<hkern u1="&#x178;" u2="&#x2122;" k="-37" />
<hkern u1="&#x178;" u2="&#x203a;" k="25" />
<hkern u1="&#x178;" u2="&#x2039;" k="37" />
<hkern u1="&#x178;" u2="&#x2026;" k="31" />
<hkern u1="&#x178;" u2="&#x201e;" k="31" />
<hkern u1="&#x178;" u2="&#x201a;" k="31" />
<hkern u1="&#x178;" u2="&#x2014;" k="31" />
<hkern u1="&#x178;" u2="&#x2013;" k="31" />
<hkern u1="&#x178;" u2="&#x178;" k="-18" />
<hkern u1="&#x178;" u2="&#x153;" k="35" />
<hkern u1="&#x178;" u2="&#x152;" k="-4" />
<hkern u1="&#x178;" u2="&#xe7;" k="35" />
<hkern u1="&#x178;" u2="&#xe6;" k="35" />
<hkern u1="&#x178;" u2="&#xdd;" k="-18" />
<hkern u1="&#x178;" u2="&#xd8;" k="-4" />
<hkern u1="&#x178;" u2="&#xd6;" k="-4" />
<hkern u1="&#x178;" u2="&#xd5;" k="-4" />
<hkern u1="&#x178;" u2="&#xd4;" k="-4" />
<hkern u1="&#x178;" u2="&#xd3;" k="-4" />
<hkern u1="&#x178;" u2="&#xd2;" k="-4" />
<hkern u1="&#x178;" u2="&#xc6;" k="12" />
<hkern u1="&#x178;" u2="&#xc5;" k="12" />
<hkern u1="&#x178;" u2="&#xc4;" k="12" />
<hkern u1="&#x178;" u2="&#xc3;" k="12" />
<hkern u1="&#x178;" u2="&#xc2;" k="12" />
<hkern u1="&#x178;" u2="&#xc1;" k="12" />
<hkern u1="&#x178;" u2="&#xc0;" k="12" />
<hkern u1="&#x178;" u2="&#xbb;" k="25" />
<hkern u1="&#x178;" u2="&#xae;" k="18" />
<hkern u1="&#x178;" u2="&#xab;" k="37" />
<hkern u1="&#x178;" u2="&#xa9;" k="18" />
<hkern u1="&#x178;" u2="&#x7d;" k="-31" />
<hkern u1="&#x178;" u2="z" k="18" />
<hkern u1="&#x178;" u2="y" k="12" />
<hkern u1="&#x178;" u2="x" k="12" />
<hkern u1="&#x178;" u2="w" k="6" />
<hkern u1="&#x178;" u2="v" k="12" />
<hkern u1="&#x178;" u2="u" k="25" />
<hkern u1="&#x178;" u2="t" k="12" />
<hkern u1="&#x178;" u2="s" k="31" />
<hkern u1="&#x178;" u2="r" k="25" />
<hkern u1="&#x178;" u2="q" k="35" />
<hkern u1="&#x178;" u2="p" k="25" />
<hkern u1="&#x178;" u2="o" k="35" />
<hkern u1="&#x178;" u2="n" k="25" />
<hkern u1="&#x178;" u2="m" k="25" />
<hkern u1="&#x178;" u2="g" k="25" />
<hkern u1="&#x178;" u2="f" k="6" />
<hkern u1="&#x178;" u2="e" k="35" />
<hkern u1="&#x178;" u2="d" k="35" />
<hkern u1="&#x178;" u2="c" k="35" />
<hkern u1="&#x178;" u2="a" k="35" />
<hkern u1="&#x178;" u2="]" k="-31" />
<hkern u1="&#x178;" u2="Y" k="-18" />
<hkern u1="&#x178;" u2="X" k="-12" />
<hkern u1="&#x178;" u2="W" k="-4" />
<hkern u1="&#x178;" u2="V" k="-23" />
<hkern u1="&#x178;" u2="T" k="-18" />
<hkern u1="&#x178;" u2="S" k="-18" />
<hkern u1="&#x178;" u2="Q" k="-4" />
<hkern u1="&#x178;" u2="O" k="-4" />
<hkern u1="&#x178;" u2="J" k="43" />
<hkern u1="&#x178;" u2="G" k="-4" />
<hkern u1="&#x178;" u2="C" k="-4" />
<hkern u1="&#x178;" u2="A" k="12" />
<hkern u1="&#x178;" u2="&#x40;" k="18" />
<hkern u1="&#x178;" u2="&#x3b;" k="18" />
<hkern u1="&#x178;" u2="&#x3a;" k="18" />
<hkern u1="&#x178;" u2="&#x2e;" k="31" />
<hkern u1="&#x178;" u2="&#x2d;" k="31" />
<hkern u1="&#x178;" u2="&#x2c;" k="31" />
<hkern u1="&#x178;" u2="&#x29;" k="-31" />
<hkern u1="&#x178;" u2="&#x26;" k="18" />
<hkern u1="&#x2013;" u2="&#x178;" k="31" />
<hkern u1="&#x2013;" u2="&#x153;" k="6" />
<hkern u1="&#x2013;" u2="&#xe7;" k="6" />
<hkern u1="&#x2013;" u2="&#xe6;" k="6" />
<hkern u1="&#x2013;" u2="&#xdd;" k="31" />
<hkern u1="&#x2013;" u2="&#xc6;" k="-6" />
<hkern u1="&#x2013;" u2="&#xc5;" k="-6" />
<hkern u1="&#x2013;" u2="&#xc4;" k="-6" />
<hkern u1="&#x2013;" u2="&#xc3;" k="-6" />
<hkern u1="&#x2013;" u2="&#xc2;" k="-6" />
<hkern u1="&#x2013;" u2="&#xc1;" k="-6" />
<hkern u1="&#x2013;" u2="&#xc0;" k="-6" />
<hkern u1="&#x2013;" u2="z" k="18" />
<hkern u1="&#x2013;" u2="y" k="6" />
<hkern u1="&#x2013;" u2="x" k="18" />
<hkern u1="&#x2013;" u2="v" k="6" />
<hkern u1="&#x2013;" u2="q" k="6" />
<hkern u1="&#x2013;" u2="o" k="6" />
<hkern u1="&#x2013;" u2="e" k="6" />
<hkern u1="&#x2013;" u2="d" k="6" />
<hkern u1="&#x2013;" u2="c" k="6" />
<hkern u1="&#x2013;" u2="a" k="6" />
<hkern u1="&#x2013;" u2="Z" k="6" />
<hkern u1="&#x2013;" u2="Y" k="31" />
<hkern u1="&#x2013;" u2="X" k="18" />
<hkern u1="&#x2013;" u2="W" k="25" />
<hkern u1="&#x2013;" u2="V" k="31" />
<hkern u1="&#x2013;" u2="T" k="43" />
<hkern u1="&#x2013;" u2="A" k="-6" />
<hkern u1="&#x2013;" u2="&#x37;" k="18" />
<hkern u1="&#x2013;" u2="&#x31;" k="12" />
<hkern u1="&#x2014;" u2="&#x178;" k="31" />
<hkern u1="&#x2014;" u2="&#x153;" k="6" />
<hkern u1="&#x2014;" u2="&#xe7;" k="6" />
<hkern u1="&#x2014;" u2="&#xe6;" k="6" />
<hkern u1="&#x2014;" u2="&#xdd;" k="31" />
<hkern u1="&#x2014;" u2="&#xc6;" k="-6" />
<hkern u1="&#x2014;" u2="&#xc5;" k="-6" />
<hkern u1="&#x2014;" u2="&#xc4;" k="-6" />
<hkern u1="&#x2014;" u2="&#xc3;" k="-6" />
<hkern u1="&#x2014;" u2="&#xc2;" k="-6" />
<hkern u1="&#x2014;" u2="&#xc1;" k="-6" />
<hkern u1="&#x2014;" u2="&#xc0;" k="-6" />
<hkern u1="&#x2014;" u2="z" k="18" />
<hkern u1="&#x2014;" u2="y" k="6" />
<hkern u1="&#x2014;" u2="x" k="18" />
<hkern u1="&#x2014;" u2="v" k="6" />
<hkern u1="&#x2014;" u2="q" k="6" />
<hkern u1="&#x2014;" u2="o" k="6" />
<hkern u1="&#x2014;" u2="e" k="6" />
<hkern u1="&#x2014;" u2="d" k="6" />
<hkern u1="&#x2014;" u2="c" k="6" />
<hkern u1="&#x2014;" u2="a" k="6" />
<hkern u1="&#x2014;" u2="Z" k="6" />
<hkern u1="&#x2014;" u2="Y" k="31" />
<hkern u1="&#x2014;" u2="X" k="18" />
<hkern u1="&#x2014;" u2="W" k="25" />
<hkern u1="&#x2014;" u2="V" k="31" />
<hkern u1="&#x2014;" u2="T" k="43" />
<hkern u1="&#x2014;" u2="A" k="-6" />
<hkern u1="&#x2014;" u2="&#x37;" k="18" />
<hkern u1="&#x2014;" u2="&#x31;" k="12" />
<hkern u1="&#x2018;" u2="&#x178;" k="-18" />
<hkern u1="&#x2018;" u2="&#x153;" k="18" />
<hkern u1="&#x2018;" u2="&#xe7;" k="18" />
<hkern u1="&#x2018;" u2="&#xe6;" k="18" />
<hkern u1="&#x2018;" u2="&#xdd;" k="-18" />
<hkern u1="&#x2018;" u2="&#xc6;" k="25" />
<hkern u1="&#x2018;" u2="&#xc5;" k="25" />
<hkern u1="&#x2018;" u2="&#xc4;" k="25" />
<hkern u1="&#x2018;" u2="&#xc3;" k="25" />
<hkern u1="&#x2018;" u2="&#xc2;" k="25" />
<hkern u1="&#x2018;" u2="&#xc1;" k="25" />
<hkern u1="&#x2018;" u2="&#xc0;" k="25" />
<hkern u1="&#x2018;" u2="u" k="6" />
<hkern u1="&#x2018;" u2="s" k="18" />
<hkern u1="&#x2018;" u2="r" k="6" />
<hkern u1="&#x2018;" u2="q" k="18" />
<hkern u1="&#x2018;" u2="p" k="6" />
<hkern u1="&#x2018;" u2="o" k="18" />
<hkern u1="&#x2018;" u2="n" k="6" />
<hkern u1="&#x2018;" u2="m" k="6" />
<hkern u1="&#x2018;" u2="g" k="25" />
<hkern u1="&#x2018;" u2="e" k="18" />
<hkern u1="&#x2018;" u2="d" k="18" />
<hkern u1="&#x2018;" u2="c" k="18" />
<hkern u1="&#x2018;" u2="a" k="18" />
<hkern u1="&#x2018;" u2="Y" k="-18" />
<hkern u1="&#x2018;" u2="X" k="-12" />
<hkern u1="&#x2018;" u2="W" k="-12" />
<hkern u1="&#x2018;" u2="V" k="-12" />
<hkern u1="&#x2018;" u2="T" k="-6" />
<hkern u1="&#x2018;" u2="J" k="68" />
<hkern u1="&#x2018;" u2="A" k="25" />
<hkern u1="&#x2019;" u2="&#x153;" k="31" />
<hkern u1="&#x2019;" u2="&#x152;" k="25" />
<hkern u1="&#x2019;" u2="&#xe7;" k="31" />
<hkern u1="&#x2019;" u2="&#xe6;" k="31" />
<hkern u1="&#x2019;" u2="&#xd8;" k="25" />
<hkern u1="&#x2019;" u2="&#xd6;" k="25" />
<hkern u1="&#x2019;" u2="&#xd5;" k="25" />
<hkern u1="&#x2019;" u2="&#xd4;" k="25" />
<hkern u1="&#x2019;" u2="&#xd3;" k="25" />
<hkern u1="&#x2019;" u2="&#xd2;" k="25" />
<hkern u1="&#x2019;" u2="q" k="31" />
<hkern u1="&#x2019;" u2="o" k="31" />
<hkern u1="&#x2019;" u2="e" k="31" />
<hkern u1="&#x2019;" u2="d" k="31" />
<hkern u1="&#x2019;" u2="c" k="31" />
<hkern u1="&#x2019;" u2="a" k="31" />
<hkern u1="&#x2019;" u2="S" k="6" />
<hkern u1="&#x2019;" u2="Q" k="25" />
<hkern u1="&#x2019;" u2="O" k="25" />
<hkern u1="&#x2019;" u2="J" k="74" />
<hkern u1="&#x2019;" u2="G" k="25" />
<hkern u1="&#x2019;" u2="C" k="25" />
<hkern u1="&#x201a;" u2="&#x178;" k="31" />
<hkern u1="&#x201a;" u2="&#x153;" k="12" />
<hkern u1="&#x201a;" u2="&#x152;" k="25" />
<hkern u1="&#x201a;" u2="&#xe7;" k="12" />
<hkern u1="&#x201a;" u2="&#xe6;" k="12" />
<hkern u1="&#x201a;" u2="&#xdd;" k="31" />
<hkern u1="&#x201a;" u2="&#xdc;" k="6" />
<hkern u1="&#x201a;" u2="&#xdb;" k="6" />
<hkern u1="&#x201a;" u2="&#xda;" k="6" />
<hkern u1="&#x201a;" u2="&#xd9;" k="6" />
<hkern u1="&#x201a;" u2="&#xd8;" k="25" />
<hkern u1="&#x201a;" u2="&#xd6;" k="25" />
<hkern u1="&#x201a;" u2="&#xd5;" k="25" />
<hkern u1="&#x201a;" u2="&#xd4;" k="25" />
<hkern u1="&#x201a;" u2="&#xd3;" k="25" />
<hkern u1="&#x201a;" u2="&#xd2;" k="25" />
<hkern u1="&#x201a;" u2="&#xc6;" k="-18" />
<hkern u1="&#x201a;" u2="&#xc5;" k="-18" />
<hkern u1="&#x201a;" u2="&#xc4;" k="-18" />
<hkern u1="&#x201a;" u2="&#xc3;" k="-18" />
<hkern u1="&#x201a;" u2="&#xc2;" k="-18" />
<hkern u1="&#x201a;" u2="&#xc1;" k="-18" />
<hkern u1="&#x201a;" u2="&#xc0;" k="-18" />
<hkern u1="&#x201a;" u2="y" k="18" />
<hkern u1="&#x201a;" u2="w" k="6" />
<hkern u1="&#x201a;" u2="v" k="18" />
<hkern u1="&#x201a;" u2="u" k="12" />
<hkern u1="&#x201a;" u2="t" k="25" />
<hkern u1="&#x201a;" u2="r" k="12" />
<hkern u1="&#x201a;" u2="q" k="12" />
<hkern u1="&#x201a;" u2="p" k="12" />
<hkern u1="&#x201a;" u2="o" k="12" />
<hkern u1="&#x201a;" u2="n" k="12" />
<hkern u1="&#x201a;" u2="m" k="12" />
<hkern u1="&#x201a;" u2="j" k="-18" />
<hkern u1="&#x201a;" u2="e" k="12" />
<hkern u1="&#x201a;" u2="d" k="12" />
<hkern u1="&#x201a;" u2="c" k="12" />
<hkern u1="&#x201a;" u2="a" k="12" />
<hkern u1="&#x201a;" u2="Y" k="31" />
<hkern u1="&#x201a;" u2="W" k="12" />
<hkern u1="&#x201a;" u2="V" k="37" />
<hkern u1="&#x201a;" u2="U" k="6" />
<hkern u1="&#x201a;" u2="T" k="37" />
<hkern u1="&#x201a;" u2="Q" k="25" />
<hkern u1="&#x201a;" u2="O" k="25" />
<hkern u1="&#x201a;" u2="G" k="25" />
<hkern u1="&#x201a;" u2="C" k="25" />
<hkern u1="&#x201a;" u2="A" k="-18" />
<hkern u1="&#x201a;" u2="&#x39;" k="6" />
<hkern u1="&#x201a;" u2="&#x38;" k="10" />
<hkern u1="&#x201a;" u2="&#x36;" k="18" />
<hkern u1="&#x201a;" u2="&#x34;" k="31" />
<hkern u1="&#x201a;" u2="&#x31;" k="37" />
<hkern u1="&#x201a;" u2="&#x30;" k="25" />
<hkern u1="&#x201c;" u2="&#x178;" k="-18" />
<hkern u1="&#x201c;" u2="&#x153;" k="18" />
<hkern u1="&#x201c;" u2="&#xe7;" k="18" />
<hkern u1="&#x201c;" u2="&#xe6;" k="18" />
<hkern u1="&#x201c;" u2="&#xdd;" k="-18" />
<hkern u1="&#x201c;" u2="&#xc6;" k="25" />
<hkern u1="&#x201c;" u2="&#xc5;" k="25" />
<hkern u1="&#x201c;" u2="&#xc4;" k="25" />
<hkern u1="&#x201c;" u2="&#xc3;" k="25" />
<hkern u1="&#x201c;" u2="&#xc2;" k="25" />
<hkern u1="&#x201c;" u2="&#xc1;" k="25" />
<hkern u1="&#x201c;" u2="&#xc0;" k="25" />
<hkern u1="&#x201c;" u2="u" k="6" />
<hkern u1="&#x201c;" u2="s" k="18" />
<hkern u1="&#x201c;" u2="r" k="6" />
<hkern u1="&#x201c;" u2="q" k="18" />
<hkern u1="&#x201c;" u2="p" k="6" />
<hkern u1="&#x201c;" u2="o" k="18" />
<hkern u1="&#x201c;" u2="n" k="6" />
<hkern u1="&#x201c;" u2="m" k="6" />
<hkern u1="&#x201c;" u2="g" k="25" />
<hkern u1="&#x201c;" u2="e" k="18" />
<hkern u1="&#x201c;" u2="d" k="18" />
<hkern u1="&#x201c;" u2="c" k="18" />
<hkern u1="&#x201c;" u2="a" k="18" />
<hkern u1="&#x201c;" u2="Y" k="-18" />
<hkern u1="&#x201c;" u2="X" k="-12" />
<hkern u1="&#x201c;" u2="W" k="-12" />
<hkern u1="&#x201c;" u2="V" k="-12" />
<hkern u1="&#x201c;" u2="T" k="-6" />
<hkern u1="&#x201c;" u2="J" k="68" />
<hkern u1="&#x201c;" u2="A" k="25" />
<hkern u1="&#x201d;" u2="&#x153;" k="31" />
<hkern u1="&#x201d;" u2="&#x152;" k="25" />
<hkern u1="&#x201d;" u2="&#xe7;" k="31" />
<hkern u1="&#x201d;" u2="&#xe6;" k="31" />
<hkern u1="&#x201d;" u2="&#xd8;" k="25" />
<hkern u1="&#x201d;" u2="&#xd6;" k="25" />
<hkern u1="&#x201d;" u2="&#xd5;" k="25" />
<hkern u1="&#x201d;" u2="&#xd4;" k="25" />
<hkern u1="&#x201d;" u2="&#xd3;" k="25" />
<hkern u1="&#x201d;" u2="&#xd2;" k="25" />
<hkern u1="&#x201d;" u2="q" k="31" />
<hkern u1="&#x201d;" u2="o" k="31" />
<hkern u1="&#x201d;" u2="e" k="31" />
<hkern u1="&#x201d;" u2="d" k="31" />
<hkern u1="&#x201d;" u2="c" k="31" />
<hkern u1="&#x201d;" u2="a" k="31" />
<hkern u1="&#x201d;" u2="S" k="6" />
<hkern u1="&#x201d;" u2="Q" k="25" />
<hkern u1="&#x201d;" u2="O" k="25" />
<hkern u1="&#x201d;" u2="J" k="74" />
<hkern u1="&#x201d;" u2="G" k="25" />
<hkern u1="&#x201d;" u2="C" k="25" />
<hkern u1="&#x201e;" u2="&#x178;" k="31" />
<hkern u1="&#x201e;" u2="&#x153;" k="12" />
<hkern u1="&#x201e;" u2="&#x152;" k="25" />
<hkern u1="&#x201e;" u2="&#xe7;" k="12" />
<hkern u1="&#x201e;" u2="&#xe6;" k="12" />
<hkern u1="&#x201e;" u2="&#xdd;" k="31" />
<hkern u1="&#x201e;" u2="&#xdc;" k="6" />
<hkern u1="&#x201e;" u2="&#xdb;" k="6" />
<hkern u1="&#x201e;" u2="&#xda;" k="6" />
<hkern u1="&#x201e;" u2="&#xd9;" k="6" />
<hkern u1="&#x201e;" u2="&#xd8;" k="25" />
<hkern u1="&#x201e;" u2="&#xd6;" k="25" />
<hkern u1="&#x201e;" u2="&#xd5;" k="25" />
<hkern u1="&#x201e;" u2="&#xd4;" k="25" />
<hkern u1="&#x201e;" u2="&#xd3;" k="25" />
<hkern u1="&#x201e;" u2="&#xd2;" k="25" />
<hkern u1="&#x201e;" u2="&#xc6;" k="-18" />
<hkern u1="&#x201e;" u2="&#xc5;" k="-18" />
<hkern u1="&#x201e;" u2="&#xc4;" k="-18" />
<hkern u1="&#x201e;" u2="&#xc3;" k="-18" />
<hkern u1="&#x201e;" u2="&#xc2;" k="-18" />
<hkern u1="&#x201e;" u2="&#xc1;" k="-18" />
<hkern u1="&#x201e;" u2="&#xc0;" k="-18" />
<hkern u1="&#x201e;" u2="y" k="18" />
<hkern u1="&#x201e;" u2="w" k="6" />
<hkern u1="&#x201e;" u2="v" k="18" />
<hkern u1="&#x201e;" u2="u" k="12" />
<hkern u1="&#x201e;" u2="t" k="25" />
<hkern u1="&#x201e;" u2="r" k="12" />
<hkern u1="&#x201e;" u2="q" k="12" />
<hkern u1="&#x201e;" u2="p" k="12" />
<hkern u1="&#x201e;" u2="o" k="12" />
<hkern u1="&#x201e;" u2="n" k="12" />
<hkern u1="&#x201e;" u2="m" k="12" />
<hkern u1="&#x201e;" u2="j" k="-18" />
<hkern u1="&#x201e;" u2="e" k="12" />
<hkern u1="&#x201e;" u2="d" k="12" />
<hkern u1="&#x201e;" u2="c" k="12" />
<hkern u1="&#x201e;" u2="a" k="12" />
<hkern u1="&#x201e;" u2="Y" k="31" />
<hkern u1="&#x201e;" u2="W" k="12" />
<hkern u1="&#x201e;" u2="V" k="37" />
<hkern u1="&#x201e;" u2="U" k="6" />
<hkern u1="&#x201e;" u2="T" k="37" />
<hkern u1="&#x201e;" u2="Q" k="25" />
<hkern u1="&#x201e;" u2="O" k="25" />
<hkern u1="&#x201e;" u2="G" k="25" />
<hkern u1="&#x201e;" u2="C" k="25" />
<hkern u1="&#x201e;" u2="A" k="-18" />
<hkern u1="&#x201e;" u2="&#x39;" k="6" />
<hkern u1="&#x201e;" u2="&#x38;" k="10" />
<hkern u1="&#x201e;" u2="&#x36;" k="18" />
<hkern u1="&#x201e;" u2="&#x34;" k="31" />
<hkern u1="&#x201e;" u2="&#x31;" k="37" />
<hkern u1="&#x201e;" u2="&#x30;" k="25" />
<hkern u1="&#x2026;" u2="&#x178;" k="31" />
<hkern u1="&#x2026;" u2="&#x153;" k="12" />
<hkern u1="&#x2026;" u2="&#x152;" k="25" />
<hkern u1="&#x2026;" u2="&#xe7;" k="12" />
<hkern u1="&#x2026;" u2="&#xe6;" k="12" />
<hkern u1="&#x2026;" u2="&#xdd;" k="31" />
<hkern u1="&#x2026;" u2="&#xdc;" k="6" />
<hkern u1="&#x2026;" u2="&#xdb;" k="6" />
<hkern u1="&#x2026;" u2="&#xda;" k="6" />
<hkern u1="&#x2026;" u2="&#xd9;" k="6" />
<hkern u1="&#x2026;" u2="&#xd8;" k="25" />
<hkern u1="&#x2026;" u2="&#xd6;" k="25" />
<hkern u1="&#x2026;" u2="&#xd5;" k="25" />
<hkern u1="&#x2026;" u2="&#xd4;" k="25" />
<hkern u1="&#x2026;" u2="&#xd3;" k="25" />
<hkern u1="&#x2026;" u2="&#xd2;" k="25" />
<hkern u1="&#x2026;" u2="&#xc6;" k="-18" />
<hkern u1="&#x2026;" u2="&#xc5;" k="-18" />
<hkern u1="&#x2026;" u2="&#xc4;" k="-18" />
<hkern u1="&#x2026;" u2="&#xc3;" k="-18" />
<hkern u1="&#x2026;" u2="&#xc2;" k="-18" />
<hkern u1="&#x2026;" u2="&#xc1;" k="-18" />
<hkern u1="&#x2026;" u2="&#xc0;" k="-18" />
<hkern u1="&#x2026;" u2="y" k="18" />
<hkern u1="&#x2026;" u2="w" k="6" />
<hkern u1="&#x2026;" u2="v" k="18" />
<hkern u1="&#x2026;" u2="u" k="12" />
<hkern u1="&#x2026;" u2="t" k="25" />
<hkern u1="&#x2026;" u2="r" k="12" />
<hkern u1="&#x2026;" u2="q" k="12" />
<hkern u1="&#x2026;" u2="p" k="12" />
<hkern u1="&#x2026;" u2="o" k="12" />
<hkern u1="&#x2026;" u2="n" k="12" />
<hkern u1="&#x2026;" u2="m" k="12" />
<hkern u1="&#x2026;" u2="e" k="12" />
<hkern u1="&#x2026;" u2="d" k="12" />
<hkern u1="&#x2026;" u2="c" k="12" />
<hkern u1="&#x2026;" u2="a" k="12" />
<hkern u1="&#x2026;" u2="Y" k="31" />
<hkern u1="&#x2026;" u2="W" k="12" />
<hkern u1="&#x2026;" u2="V" k="37" />
<hkern u1="&#x2026;" u2="U" k="6" />
<hkern u1="&#x2026;" u2="T" k="37" />
<hkern u1="&#x2026;" u2="Q" k="25" />
<hkern u1="&#x2026;" u2="O" k="25" />
<hkern u1="&#x2026;" u2="G" k="25" />
<hkern u1="&#x2026;" u2="C" k="25" />
<hkern u1="&#x2026;" u2="A" k="-18" />
<hkern u1="&#x2026;" u2="&#x39;" k="6" />
<hkern u1="&#x2026;" u2="&#x38;" k="10" />
<hkern u1="&#x2026;" u2="&#x36;" k="18" />
<hkern u1="&#x2026;" u2="&#x34;" k="31" />
<hkern u1="&#x2026;" u2="&#x31;" k="37" />
<hkern u1="&#x2026;" u2="&#x30;" k="25" />
<hkern u1="&#x2039;" u2="&#x178;" k="25" />
<hkern u1="&#x2039;" u2="&#xdd;" k="25" />
<hkern u1="&#x2039;" u2="Y" k="25" />
<hkern u1="&#x2039;" u2="W" k="12" />
<hkern u1="&#x2039;" u2="V" k="12" />
<hkern u1="&#x2039;" u2="T" k="25" />
<hkern u1="&#x203a;" u2="&#x178;" k="31" />
<hkern u1="&#x203a;" u2="&#xdd;" k="31" />
<hkern u1="&#x203a;" u2="z" k="31" />
<hkern u1="&#x203a;" u2="x" k="31" />
<hkern u1="&#x203a;" u2="Y" k="31" />
<hkern u1="&#x203a;" u2="W" k="25" />
<hkern u1="&#x203a;" u2="V" k="31" />
<hkern u1="&#x203a;" u2="T" k="68" />
</font>
</defs></svg> 