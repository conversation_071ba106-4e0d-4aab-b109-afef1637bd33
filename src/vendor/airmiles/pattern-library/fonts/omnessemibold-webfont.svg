<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="omnessemibold" horiz-adv-x="255" >
<font-face units-per-em="1000" ascent="700" descent="-300" />
<missing-glyph horiz-adv-x="170" />
<glyph unicode="&#xfb01;" horiz-adv-x="637" d="M510 569q-37 0 -58 19t-21 53q0 33 20.5 52t57.5 19q38 0 58.5 -18.5t20.5 -52.5t-21 -53t-57 -19zM105 63v315h-40q-59 0 -59 52v8q0 50 59 50h40v26q0 187 169 187q61 0 92.5 -22t31.5 -55q0 -15 -7 -30t-14.5 -22t-8.5 -5q-28 26 -67 26q-31 0 -45.5 -18.5 t-14.5 -60.5v-26h63q59 0 59 -52v-9q0 -49 -59 -49h-58v-315q0 -37 -16 -52t-47 -15h-15q-63 0 -63 67zM438 63v362q0 68 64 68h14q64 0 64 -68v-362q0 -67 -63 -67h-14q-65 0 -65 67z" />
<glyph unicode="&#xfb02;" horiz-adv-x="637" d="M105 63v315h-40q-59 0 -59 52v8q0 50 59 50h40v26q0 187 169 187q61 0 92.5 -22t31.5 -55q0 -15 -7 -30t-14.5 -22t-8.5 -5q-28 26 -67 26q-31 0 -45.5 -18.5t-14.5 -60.5v-26h63q59 0 59 -52v-9q0 -49 -59 -49h-58v-315q0 -37 -16 -52t-47 -15h-15q-63 0 -63 67zM438 63 v616q0 3 7 6.5t20 6.5t27 3q37 0 62.5 -25.5t25.5 -87.5v-519q0 -67 -63 -67h-14q-65 0 -65 67z" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="333" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="170" />
<glyph unicode=" "  horiz-adv-x="170" />
<glyph unicode="&#x09;" horiz-adv-x="170" />
<glyph unicode="&#xa0;" horiz-adv-x="170" />
<glyph unicode="!" horiz-adv-x="277" d="M130 201q-24 0 -35 45l-39 321q-9 41 10.5 65t58.5 24h25q40 0 60 -24t11 -65l-39 -321q-10 -45 -37 -45h-15zM62 59v14q0 66 67 66h17q68 0 68 -66v-14q0 -66 -68 -66h-17q-67 0 -67 66z" />
<glyph unicode="&#x22;" horiz-adv-x="430" d="M302 355q-15 0 -22.5 8.5t-11.5 27.5l-29 187q-12 76 71 76q84 0 72 -76l-30 -187q-6 -36 -33 -36h-17zM110 355q-27 0 -33 36l-30 187q-12 76 71 76q84 0 72 -76l-29 -187q-7 -36 -34 -36h-17z" />
<glyph unicode="#" horiz-adv-x="679" d="M96 55q0 16 7 37l17 56h-54q-58 0 -58 44v16q0 45 58 45h87l43 141h-54q-58 0 -58 45v16q0 44 58 44h87l47 153q2 6 16 6q21 0 41.5 -17t20.5 -48q0 -16 -7 -37l-18 -57h129l47 153q2 6 16 6q21 0 41.5 -17t20.5 -48q0 -16 -7 -37l-18 -57h55q58 0 58 -44v-15 q0 -46 -58 -46h-87l-44 -141h54q58 0 58 -44v-16q0 -45 -58 -45h-86l-47 -153q-2 -5 -16 -5q-21 0 -41.5 17t-20.5 48q0 16 7 37l17 56h-128l-47 -153q-2 -5 -16 -5q-21 0 -41.5 17t-20.5 48zM252 252h130l45 143h-131z" />
<glyph unicode="$" horiz-adv-x="561" d="M32 125q0 19 12.5 37t25 26.5t14.5 6.5q30 -36 82 -60t108 -24q53 0 82.5 20.5t29.5 54.5q0 20 -9.5 34t-32 24t-40.5 15.5t-53 13.5q-32 7 -55.5 14t-53.5 21.5t-49 33.5t-32.5 49t-13.5 68q0 72 47 121t129 64v37q0 68 57 68h6q30 0 43.5 -16t13.5 -52v-34 q80 -9 126 -41t46 -72q0 -20 -13 -39t-26 -28t-15 -6q-29 34 -75 55.5t-94 21.5q-50 0 -79 -20.5t-29 -54.5q0 -35 31 -51.5t101 -31.5q33 -7 58 -14.5t56 -22.5t51 -34t34 -49t14 -68q0 -76 -49.5 -125.5t-137.5 -63.5v-39q0 -68 -58 -68h-5q-31 0 -44 16t-13 52v37 q-88 9 -139 44.5t-51 79.5z" />
<glyph unicode="%" horiz-adv-x="738" d="M71 38q0 23 27 51l503 570q3 3 18.5 -1.5t30 -17.5t14.5 -29q0 -23 -26 -52l-504 -570q-3 -3 -18 2t-30 18t-15 29zM182 340q-73 0 -118.5 43t-45.5 111v8q0 68 46 111.5t119 43.5t118 -43t45 -111v-8q0 -68 -46 -111.5t-118 -43.5zM183 417q29 0 47.5 20t18.5 54v15 q0 34 -19 54t-48 20q-30 0 -48 -19.5t-18 -54.5v-15q0 -33 18.5 -53.5t48.5 -20.5zM392 145v8q0 68 46 111.5t119 43.5q72 0 117.5 -43t45.5 -111v-8q0 -68 -46 -111.5t-118 -43.5q-73 0 -118.5 43t-45.5 111zM490 142q0 -33 18.5 -53t48.5 -20q29 0 47.5 19.5t18.5 53.5v15 q0 34 -19 54.5t-48 20.5q-30 0 -48 -20t-18 -55v-15z" />
<glyph unicode="&#x26;" horiz-adv-x="687" d="M19 182q0 130 150 182q-58 58 -58 128q0 74 55.5 121t145.5 47q82 0 132.5 -43.5t50.5 -112.5q0 -110 -142 -156l123 -107q18 34 42 106q14 37 30.5 52t42.5 15q19 0 34.5 -9t22.5 -17.5t6 -11.5q-26 -113 -95 -209l131 -113q2 -2 -5 -16.5t-27 -29.5t-45 -15 q-47 0 -84 41l-46 44q-91 -91 -221 -91q-113 0 -178 51t-65 144zM285 399q97 30 97 94q0 31 -20 51t-51 20q-34 0 -53 -21t-19 -53q0 -47 46 -91zM146 196q0 -50 35 -79t91 -29q75 0 135 62l-174 155q-87 -35 -87 -109z" />
<glyph unicode="'" horiz-adv-x="238" d="M110 355q-27 0 -33 36l-30 187q-12 76 71 76q84 0 72 -76l-29 -187q-7 -36 -34 -36h-17z" />
<glyph unicode="(" horiz-adv-x="405" d="M38 229q0 175 92.5 300.5t201.5 125.5q67 0 67 -60q0 -16 -9 -30t-15 -16q-93 -23 -144.5 -110.5t-51.5 -209.5q0 -124 51.5 -211t144.5 -110q6 -2 15 -16t9 -30q0 -60 -67 -60q-110 0 -202 124t-92 303z" />
<glyph unicode=")" horiz-adv-x="405" d="M30 -92q93 23 144.5 110t51.5 211q0 122 -51.5 209.5t-144.5 110.5q-6 2 -15 16t-9 30q0 60 67 60q109 0 201.5 -125.5t92.5 -300.5q0 -179 -92 -303t-202 -124q-67 0 -67 60q0 16 9 30t15 16z" />
<glyph unicode="*" horiz-adv-x="411" d="M91 307l-3 2q-29 16 -9 54l44 78l-78 36q-20 10 -24.5 20.5t0.5 28.5l1 4q5 17 15 23t33 2l87 -19l10 85q4 23 12.5 30.5t26.5 7.5h4q17 0 26 -7.5t13 -30.5l8 -85l86 19q40 6 49 -25l1 -4q6 -17 1.5 -28t-25.5 -21l-79 -36l45 -78q18 -34 -10 -54l-3 -2q-27 -18 -53 8 l-63 67l-61 -67q-16 -15 -29 -16t-25 8z" />
<glyph unicode="+" horiz-adv-x="536" d="M86 206q-58 0 -58 50v7q0 49 58 49h126v129q0 58 51 58h11q50 0 50 -58v-129h126q58 0 58 -49v-7q0 -50 -58 -50h-126v-136q0 -58 -50 -58h-11q-51 0 -51 58v136h-126z" />
<glyph unicode="," horiz-adv-x="239" d="M11 -78l45 159q8 34 21.5 47.5t45.5 13.5h15q56 0 56 -61v-6q0 -38 -32 -75l-79 -110q-18 -21 -48.5 -9.5t-23.5 41.5z" />
<glyph unicode="-" horiz-adv-x="428" d="M94 180q-28 0 -43 15t-15 39v21q0 23 15 38.5t43 15.5h240q28 0 43 -15.5t15 -38.5v-21q0 -24 -15 -39t-43 -15h-240z" />
<glyph unicode="." horiz-adv-x="239" d="M43 59v14q0 66 66 66h18q68 0 68 -66v-14q0 -66 -68 -66h-18q-66 0 -66 66z" />
<glyph unicode="/" horiz-adv-x="359" d="M5 -111l241 763q0 5 30 5q72 0 72 -56q0 -13 -8 -42l-240 -763q-2 -5 -30 -5q-73 0 -73 56q0 15 8 42z" />
<glyph unicode="0" horiz-adv-x="643" d="M38 291v64q0 141 76 223t208 82t207.5 -81.5t75.5 -222.5v-64q0 -141 -76 -223t-208 -82t-207.5 81.5t-75.5 222.5zM187 283q0 -85 35 -130.5t100 -45.5t100 45t35 130v83q0 85 -35.5 131t-100.5 46t-99.5 -45t-34.5 -131v-83z" />
<glyph unicode="1" horiz-adv-x="391" d="M165 61v405q-25 -18 -57 -18q-39 0 -64.5 23t-25.5 60q0 22 9 39.5t18 24.5t12 6q28 -22 61 -22q32 0 56 20.5t29 54.5h43q65 0 65 -68v-525q0 -68 -65 -68h-18q-63 0 -63 68z" />
<glyph unicode="2" horiz-adv-x="554" d="M34 75v31q0 44 17 80.5t49 64.5t61.5 46t71.5 38q8 4 25.5 12t25.5 12t22.5 11t21.5 12t17.5 12t15.5 13t10.5 14t7.5 16.5t2 17.5q0 41 -28.5 64t-80.5 23q-58 0 -92.5 -32.5t-47.5 -81.5q-1 0 -10 -1.5t-23.5 2.5t-28.5 10t-24 21t-10 36q0 65 66 115t179 50 q116 0 179.5 -53.5t63.5 -147.5q0 -43 -14 -75.5t-43.5 -56t-56 -38t-70.5 -32.5q-41 -18 -63.5 -29t-48.5 -27.5t-40 -36t-19 -44.5h294q70 0 70 -55v-7q0 -59 -70 -59h-340q-51 0 -70 15.5t-19 59.5z" />
<glyph unicode="3" horiz-adv-x="564" d="M14 134q0 21 9 36t21.5 22.5t25.5 11t22 3t9 -2.5q16 -50 58 -76.5t103 -26.5q59 0 93 24.5t34 67.5q0 89 -115 89h-34q-69 0 -69 50v8q0 50 67 50h48q44 0 68 20t24 55q0 38 -31.5 60t-86.5 22q-108 0 -145 -93q0 -2 -9 -2t-21.5 3.5t-25 10.5t-21 22t-8.5 35 q0 54 65 95.5t167 41.5q123 0 189 -46t66 -131q0 -101 -113 -135q126 -41 126 -164q0 -93 -70.5 -145t-197.5 -52q-112 0 -180 45.5t-68 101.5z" />
<glyph unicode="4" horiz-adv-x="610" d="M107 158q-38 0 -60.5 17.5t-22.5 45.5v25q1 32 31 77l198 276q22 34 45 44.5t77 10.5q57 0 81.5 -21.5t24.5 -74.5v-288h50q34 0 48.5 -12.5t14.5 -40.5v-5q0 -29 -14.5 -41.5t-48.5 -12.5h-50v-97q0 -68 -62 -68h-13q-29 0 -44.5 18t-15.5 50v97h-239zM154 267h198v280z " />
<glyph unicode="5" horiz-adv-x="551" d="M21 113q0 20 14.5 38t29 26.5t16.5 5.5q65 -87 179 -87q62 0 98 28.5t36 76.5q0 50 -33.5 77t-95.5 27q-81 0 -134 -40q-87 3 -87 86v214q0 82 71 82h314q69 0 69 -58v-5q0 -57 -69 -57h-261v-143q59 22 118 22q109 0 174 -55t65 -150q0 -99 -70.5 -156.5t-192.5 -57.5 q-108 0 -174.5 39t-66.5 87z" />
<glyph unicode="6" horiz-adv-x="603" d="M38 291v58q0 145 78 228t213 83q102 0 161 -36.5t59 -88.5q0 -25 -18 -41.5t-35 -21.5t-19 -2q-47 73 -138 73q-156 0 -156 -190v-5q69 64 175 64q100 0 158.5 -51t58.5 -143v-5q0 -100 -71.5 -163t-184.5 -63q-132 0 -206.5 82.5t-74.5 221.5zM186 254 q4 -75 37.5 -115.5t92.5 -40.5q52 0 85 29t33 76v5q0 49 -30.5 75t-86.5 26q-71 0 -131 -55z" />
<glyph unicode="7" horiz-adv-x="544" d="M98 46l269 477h-285q-65 0 -65 58v9q0 57 65 57h347q57 0 76.5 -19.5t19.5 -54.5v-9q0 -9 -2.5 -19t-5 -17.5t-8.5 -20.5t-9 -18t-11 -19t-9 -16l-224 -409q-29 -52 -76 -52q-22 0 -43 11.5t-31.5 24t-7.5 17.5z" />
<glyph unicode="8" horiz-adv-x="600" d="M30 175q0 117 129 157q-110 38 -110 145q0 83 69.5 134t182.5 51q114 0 180.5 -49.5t66.5 -133.5q0 -106 -108 -146q129 -39 129 -158q0 -84 -73.5 -135.5t-196.5 -51.5q-126 0 -197.5 50t-71.5 137zM300 377q52 0 82.5 23.5t30.5 64.5q0 40 -30.5 65t-82.5 25 q-54 0 -83.5 -24.5t-29.5 -65.5q0 -39 31 -63.5t82 -24.5zM174 189q0 -43 34 -69t92 -26q60 0 93 25.5t33 69.5t-34 70t-92 26q-59 0 -92.5 -26t-33.5 -70z" />
<glyph unicode="9" horiz-adv-x="603" d="M51 100q0 25 12 43t24 24t15 4q61 -68 157 -68q83 0 122 45t39 141l-1 6q-69 -64 -174 -64q-100 0 -158.5 51t-58.5 143v4q0 100 71.5 165.5t184.5 65.5q130 0 205 -81t75 -223v-62q0 -150 -76 -229t-219 -79q-103 0 -160.5 33.5t-57.5 80.5zM286 334q72 0 130 56 q-2 75 -36 117t-93 42q-51 0 -84.5 -31.5t-33.5 -79.5v-3q0 -49 30.5 -75t86.5 -26z" />
<glyph unicode=":" d="M118 298q-67 0 -67 66v14q0 67 67 67h17q68 0 68 -67v-14q0 -66 -68 -66h-17zM51 59v14q0 66 67 66h17q68 0 68 -66v-14q0 -66 -68 -66h-17q-67 0 -67 66z" />
<glyph unicode=";" d="M118 298q-67 0 -67 66v14q0 67 67 67h17q68 0 68 -67v-14q0 -66 -68 -66h-17zM20 -78l44 159q9 34 22 47.5t46 13.5h14q57 0 57 -61v-6q0 -38 -32 -75l-80 -110q-18 -21 -48 -9.5t-23 41.5z" />
<glyph unicode="&#x3c;" horiz-adv-x="459" d="M80 189q-42 24 -42 70q0 48 42 70l304 165q3 1 11.5 -7.5t16.5 -24.5t8 -33q2 -47 -59 -77l-182 -92l182 -92q61 -30 59 -77q0 -17 -8 -33.5t-16 -25t-12 -6.5z" />
<glyph unicode="=" horiz-adv-x="536" d="M101 318q-29 0 -43.5 15.5t-14.5 39.5v12q0 24 14.5 39t43.5 15h335q28 0 43 -15.5t15 -38.5v-12q0 -24 -14.5 -39.5t-43.5 -15.5h-335zM101 76q-29 0 -43.5 15.5t-14.5 39.5v12q0 24 14.5 39t43.5 15h335q28 0 43 -15.5t15 -38.5v-12q0 -24 -14.5 -39.5t-43.5 -15.5 h-335z" />
<glyph unicode="&#x3e;" horiz-adv-x="458" d="M75 26q-4 -2 -12.5 6.5t-16.5 25t-8 33.5q0 48 60 77l181 92l-181 92q-61 30 -59 77q0 17 8 33t16 24.5t12 7.5l304 -165q41 -21 41 -70q0 -47 -41 -70z" />
<glyph unicode="?" horiz-adv-x="499" d="M168 244q1 46 19.5 79t44 49.5t50.5 29.5t42.5 29.5t17.5 39.5q0 34 -24.5 53.5t-68.5 19.5q-58 0 -90.5 -33.5t-39.5 -81.5q-1 -3 -24.5 -0.5t-47.5 20t-24 50.5q0 60 61 110.5t167 50.5q105 0 165.5 -49t60.5 -130q0 -46 -19 -78.5t-46.5 -51t-55 -35t-48.5 -39.5 t-23 -55q-7 -27 -57 -27q-28 0 -44.5 12.5t-15.5 36.5zM153 59v14q0 66 66 66h18q68 0 68 -66v-14q0 -66 -68 -66h-18q-66 0 -66 66z" />
<glyph unicode="@" horiz-adv-x="740" d="M39 323q0 148 94 242.5t242 94.5q153 0 241 -83t88 -214q0 -95 -42.5 -146t-121.5 -51q-85 0 -110 61q-28 -62 -97 -62q-60 0 -96.5 43t-36.5 108q0 68 37.5 110t95.5 42q57 0 91 -48q12 45 57 45q9 0 17.5 -2.5t13 -5t4.5 -4.5v-152q0 -37 12.5 -53.5t39.5 -16.5 q34 0 54 33.5t20 97.5q0 105 -69.5 172.5t-197.5 67.5q-121 0 -198 -78.5t-77 -200.5q0 -121 78 -199.5t201 -78.5q102 0 171 57q17 14 33 14q11 0 20.5 -9t13.5 -18.5t2 -11.5q-92 -90 -240 -90q-153 0 -246.5 93t-93.5 243zM294 316q0 -33 18 -53t47 -20q25 0 42.5 14.5 t19.5 38.5v56q-21 38 -61 38q-30 0 -48 -20t-18 -54z" />
<glyph unicode="A" horiz-adv-x="656" d="M10 38l221 571q7 22 28 33t68 11q48 0 68 -11t27 -33l226 -570q3 -6 -8 -17t-31.5 -20t-40.5 -9q-59 0 -85 70l-39 102h-237l-39 -104q-24 -68 -81 -68q-19 0 -38.5 8.5t-30.5 19t-8 17.5zM236 275h178l-89 235z" />
<glyph unicode="B" horiz-adv-x="637" d="M58 82v483q0 39 20.5 60.5t56.5 21.5h222q109 0 167 -42.5t58 -122.5q0 -106 -116 -138q70 -16 105 -58t35 -102q0 -94 -62.5 -139t-198.5 -45h-210q-36 0 -56.5 21.5t-20.5 60.5zM204 375h118q51 0 79.5 22t28.5 61q0 79 -108 79h-118v-162zM204 110h126q66 0 95.5 21 t29.5 67q0 83 -124 83h-127v-171z" />
<glyph unicode="C" horiz-adv-x="654" d="M37 323q0 148 93 242.5t241 94.5q116 0 182.5 -49t66.5 -97q0 -29 -21 -48t-41.5 -25t-22.5 -2q-24 41 -66.5 64.5t-91.5 23.5q-82 0 -135 -56.5t-53 -146.5t53.5 -147t135.5 -57q56 0 99 26t62 72q2 4 23.5 -2t43 -25t21.5 -48q0 -51 -69 -103.5t-189 -52.5 q-147 0 -239.5 93.5t-92.5 242.5z" />
<glyph unicode="D" horiz-adv-x="683" d="M58 81v484q0 38 21.5 60t56.5 22h164q163 0 255.5 -85.5t92.5 -235.5q0 -151 -95 -238.5t-258 -87.5h-155q-34 0 -58 23.5t-24 57.5zM204 126h90q97 0 152 52.5t55 146.5q0 93 -54.5 144.5t-150.5 51.5h-92v-395z" />
<glyph unicode="E" horiz-adv-x="600" d="M58 81v484q0 39 20.5 60.5t56.5 21.5h361q68 0 68 -62v-5q0 -62 -68 -62h-290v-128h212q68 0 68 -57v-4q0 -60 -68 -60h-212v-141h295q68 0 68 -58v-6q0 -64 -68 -64h-366q-36 0 -56.5 21t-20.5 60z" />
<glyph unicode="F" horiz-adv-x="556" d="M58 78v487q0 39 20.5 60.5t56.5 21.5h334q68 0 68 -59v-6q0 -61 -68 -61h-265v-161h191q68 0 68 -57v-3q0 -60 -68 -60h-187v-176q0 -68 -65 -68h-23q-62 0 -62 82z" />
<glyph unicode="G" horiz-adv-x="719" d="M37 323q0 148 95 242.5t247 94.5q113 0 181.5 -43.5t68.5 -97.5q0 -26 -18.5 -44t-36.5 -23.5t-21 -2.5q-27 40 -72 62.5t-95 22.5q-87 0 -143 -60t-56 -156q0 -97 52 -156.5t138 -59.5q68 0 113 33.5t52 89.5v17h-115q-37 0 -54 12t-17 41v6q0 30 17 41.5t54 11.5h183 q63 0 61 -70v-274q0 -2 -6.5 -5.5t-18.5 -6t-24 -2.5q-70 0 -77 83q-61 -90 -201 -90q-139 0 -223 93.5t-84 240.5z" />
<glyph unicode="H" horiz-adv-x="692" d="M58 63v521q0 67 62 67h22q64 0 64 -67v-197h280v197q0 67 62 67h21q65 0 65 -67v-521q0 -67 -65 -67h-21q-62 0 -62 67v199h-280v-199q0 -67 -64 -67h-22q-62 0 -62 67z" />
<glyph unicode="I" horiz-adv-x="280" d="M66 63v521q0 67 63 67h20q65 0 65 -67v-521q0 -67 -65 -67h-20q-63 0 -63 67z" />
<glyph unicode="J" horiz-adv-x="497" d="M19 102q0 25 13.5 44.5t27.5 27t15 4.5q43 -66 117 -66q54 0 77.5 28.5t23.5 97.5v346q0 67 62 67h21q64 0 64 -67v-348q0 -127 -57 -188t-173 -61q-87 0 -139 33t-52 82z" />
<glyph unicode="K" horiz-adv-x="614" d="M58 63v521q0 67 62 67h21q65 0 65 -67v-231l212 250q36 48 81 48q25 0 46 -13.5t29.5 -27t6.5 -18.5l-219 -262l227 -276q3 -5 -6.5 -18.5t-32 -26.5t-48.5 -13q-47 0 -81 46l-215 258v-237q0 -67 -65 -67h-21q-62 0 -62 67z" />
<glyph unicode="L" horiz-adv-x="539" d="M58 81v503q0 67 62 67h21q65 0 65 -67v-454h242q68 0 68 -62v-4q0 -64 -68 -64h-313q-36 0 -56.5 21t-20.5 60z" />
<glyph unicode="M" horiz-adv-x="759" d="M58 63v514q0 36 19 55t54 19h41q33 0 48.5 -11t28.5 -41l131 -307l132 307q14 30 29.5 41t45.5 11h39q75 0 75 -74v-514q0 -67 -58 -67h-23q-58 0 -58 67v385l-108 -262q-8 -18 -12 -24.5t-20 -14t-43 -7.5q-28 0 -43.5 7.5t-19 13.5t-11.5 25l-108 257v-380 q0 -34 -13.5 -50.5t-44.5 -16.5h-23q-58 0 -58 67z" />
<glyph unicode="N" horiz-adv-x="692" d="M58 63v515q0 35 17 54t47 19h18q27 0 41.5 -9t33.5 -33l280 -385v360q0 67 63 67h14q62 0 62 -67v-515q0 -35 -16.5 -54t-46.5 -19h-12q-28 0 -41 7.5t-30 28.5l-291 400v-369q0 -67 -64 -67h-14q-61 0 -61 67z" />
<glyph unicode="O" horiz-adv-x="737" d="M35 323q0 148 94 242.5t240 94.5q147 0 240 -94t93 -242t-93.5 -242.5t-240.5 -94.5t-240 94t-93 242zM182 324q0 -92 52 -149t135 -57t135 57t52 148q0 92 -52.5 149.5t-135.5 57.5t-134.5 -56.5t-51.5 -149.5z" />
<glyph unicode="P" horiz-adv-x="589" d="M58 63v502q0 39 20.5 60.5t56.5 21.5h169q129 0 197.5 -56t68.5 -163q0 -218 -266 -218h-98v-147q0 -67 -64 -67h-22q-62 0 -62 67zM204 321h98q60 0 92 27.5t32 78.5q0 105 -125 105h-97v-211z" />
<glyph unicode="Q" horiz-adv-x="737" d="M35 323q0 148 94.5 242.5t240.5 94.5q147 0 239.5 -93.5t92.5 -242.5q0 -114 -66 -202l78 -83q3 -3 -6.5 -15.5t-30.5 -24.5t-45 -12q-46 0 -90 48q-80 -48 -173 -48q-147 0 -240.5 94t-93.5 242zM182 323q0 -90 53 -148t135 -58q45 0 84 17l-63 66q-19 21 -22 46.5 t16 44.5l6 6q42 33 89 -17l58 -62q22 47 22 105q0 91 -52.5 149.5t-136.5 58.5q-85 0 -137 -58t-52 -150z" />
<glyph unicode="R" horiz-adv-x="617" d="M58 77v488q0 39 20.5 60.5t56.5 21.5h191q126 0 190.5 -51.5t64.5 -150.5q0 -78 -43.5 -128.5t-124.5 -63.5l168 -203q4 -4 -4.5 -17t-30 -25t-50.5 -12q-31 0 -52 13.5t-45 46.5l-138 174h-56v-167q0 -67 -64 -67h-21q-62 0 -62 81zM203 338h113q58 0 87.5 25.5 t29.5 71.5q0 97 -120 97h-110v-194z" />
<glyph unicode="S" horiz-adv-x="569" d="M26 117q0 19 13 38t26.5 28.5t16.5 7.5q31 -38 83.5 -63.5t109.5 -25.5t87.5 20.5t30.5 58.5q0 15 -7 27.5t-15.5 19.5t-28.5 14t-32 10t-40 9l-6 1.5t-5.5 1t-6.5 1.5q-45 10 -77.5 22t-65.5 34t-50 57t-17 82q0 91 68.5 145.5t184.5 54.5q109 0 170.5 -36t61.5 -82 q0 -21 -14.5 -40.5t-28.5 -29t-16 -7.5q-30 35 -76 57.5t-96 22.5q-53 0 -82.5 -20.5t-29.5 -56.5q0 -9 2 -16.5t6.5 -14t9 -11t14 -8.5t16 -7t19.5 -6t21 -5t24.5 -5.5t25.5 -5.5q101 -22 160 -67.5t59 -129.5q0 -97 -69 -151t-196 -54q-120 0 -185 40t-65 90z" />
<glyph unicode="T" horiz-adv-x="605" d="M230 63v455h-147q-68 0 -68 64v4q0 61 68 61h439q68 0 68 -61v-4q0 -64 -68 -64h-145v-455q0 -67 -65 -67h-21q-61 0 -61 67z" />
<glyph unicode="U" horiz-adv-x="663" d="M53 272v312q0 67 64 67h19q64 0 64 -67v-310q0 -157 132 -157q131 0 131 158v309q0 67 64 67h20q63 0 63 -67v-310q0 -140 -71 -212.5t-208 -72.5t-207.5 71.5t-70.5 211.5z" />
<glyph unicode="V" horiz-adv-x="645" d="M237 50l-226 552q-3 7 9.5 18.5t35.5 21t45 9.5q61 0 83 -68l142 -418l141 418q21 68 80 68q22 0 44 -9.5t33.5 -21t8.5 -18.5l-222 -552q-13 -30 -33 -43t-55 -13t-54 12.5t-32 43.5z" />
<glyph unicode="W" horiz-adv-x="951" d="M192 51l-152 553q-1 4 8 14.5t31.5 21t50.5 10.5q67 0 82 -81l82 -386l98 337q15 59 88 59q69 0 85 -59l97 -336l83 387q15 80 81 80q26 0 47 -10.5t30 -21.5t8 -15l-149 -553q-7 -31 -29 -44t-62 -13q-39 0 -60 13t-31 44l-102 350l-106 -350q-15 -57 -90 -57 q-40 0 -61 12.5t-29 44.5z" />
<glyph unicode="X" horiz-adv-x="636" d="M19 50l199 273l-195 270q-3 5 9 19t36 26.5t48 12.5q49 0 79 -52l124 -193l130 194q30 52 77 52q23 0 46 -12.5t34.5 -26t8.5 -18.5l-198 -272l198 -271q3 -5 -9 -18.5t-35.5 -26t-47.5 -12.5q-51 0 -80 51l-128 195l-130 -196q-31 -52 -78 -52q-23 0 -45.5 12.5t-34 26 t-8.5 18.5z" />
<glyph unicode="Y" horiz-adv-x="603" d="M228 63v197l-226 334q-3 5 5 18t32 26t57 13q42 0 70 -51l136 -223l138 222q30 52 71 52q30 0 53 -13t31.5 -26.5t5.5 -17.5l-225 -334v-197q0 -67 -65 -67h-20q-63 0 -63 67z" />
<glyph unicode="Z" horiz-adv-x="614" d="M40 62v10q0 29 10 47t38 52l296 352h-274q-65 0 -65 58v7q0 59 65 59h378q42 0 62.5 -17.5t20.5 -44.5v-9q0 -29 -10.5 -48.5t-36.5 -48.5l-301 -355h293q66 0 66 -58v-7q0 -59 -66 -59h-393q-41 0 -62 18t-21 44z" />
<glyph unicode="[" horiz-adv-x="373" d="M59 -117v689q0 39 20.5 60.5t56.5 21.5h152q36 0 52 -12.5t16 -41.5v-6q0 -30 -16 -43t-52 -13h-98v-622h100q36 0 52 -12.5t16 -40.5v-6q0 -29 -16 -42t-52 -13h-154q-36 0 -56.5 21t-20.5 60z" />
<glyph unicode="\" horiz-adv-x="359" d="M259 -204l-241 763q-8 29 -8 42q0 56 72 56q30 0 30 -5l241 -763q8 -27 8 -42q0 -56 -72 -56q-30 0 -30 5z" />
<glyph unicode="]" horiz-adv-x="373" d="M83 -84h100v622h-98q-36 0 -52 13t-16 43v6q0 29 16 41.5t52 12.5h152q36 0 56.5 -21.5t20.5 -60.5v-689q0 -39 -20.5 -60t-56.5 -21h-154q-36 0 -52 13t-16 42v6q0 28 16 40.5t52 12.5z" />
<glyph unicode="^" horiz-adv-x="500" d="M144 441q-20 0 -33 12.5t-13 31.5q0 41 57.5 92t94.5 60q36 -9 93.5 -60t57.5 -92q0 -19 -12.5 -31.5t-32.5 -12.5q-49 0 -107 101q-56 -101 -105 -101z" />
<glyph unicode="_" horiz-adv-x="496" d="M11 -51q0 23 15 37t43 14h358q28 0 43 -14t15 -37v-6q0 -51 -58 -51h-358q-28 0 -43 13.5t-15 37.5v6z" />
<glyph unicode="`" horiz-adv-x="500" d="M147 594q-49 30 -49 76q0 23 18.5 39t45.5 16q24 0 50 -17q30 -19 59.5 -53t44.5 -61t11 -34t-31.5 -7.5t-70 9.5t-78.5 32z" />
<glyph unicode="a" horiz-adv-x="546" d="M28 130q0 84 76.5 124t237.5 42h16q0 97 -100 97q-85 0 -121 -75q0 -3 -22.5 -1t-45.5 17t-23 44q0 50 58 85.5t163 35.5q230 0 230 -201v-289q0 -5 -16.5 -10.5t-36.5 -5.5q-71 0 -83 77q-56 -81 -169 -81q-76 0 -120 39t-44 102zM163 140q0 -27 20.5 -43t55.5 -16 q52 0 85.5 31t33.5 79v28l-26 -1q-85 -2 -127 -20.5t-42 -57.5z" />
<glyph unicode="b" horiz-adv-x="605" d="M53 63v616q0 3 7 6.5t20 6.5t26 3q88 0 88 -113v-165q53 82 158 82q100 0 163 -69.5t63 -185.5q0 -114 -63.5 -185.5t-164.5 -71.5q-54 0 -95 23.5t-61 61.5v-9q0 -67 -62 -67h-15q-64 0 -64 67zM194 211q1 -49 36 -80.5t86 -31.5q57 0 91.5 39.5t34.5 104.5 q0 64 -34.5 103.5t-90.5 39.5q-89 0 -123 -80v-95z" />
<glyph unicode="c" horiz-adv-x="514" d="M27 244q0 111 72.5 182.5t184.5 71.5q100 0 154 -43.5t54 -88.5q0 -26 -19 -42t-38 -20t-21 0q-33 81 -124 81q-58 0 -95.5 -40t-37.5 -101q0 -63 37.5 -103.5t95.5 -40.5q91 0 124 81q1 4 20.5 0.5t38.5 -20t19 -43.5q0 -43 -55 -87t-153 -44q-113 0 -185 72t-72 185z " />
<glyph unicode="d" horiz-adv-x="605" d="M27 243q0 116 63 186t165 70q51 0 92 -20.5t64 -53.5v254q0 3 7 6.5t20 6.5t26 3q88 0 88 -113v-519q0 -67 -62 -67h-15q-64 0 -64 67v16q-52 -92 -158 -92q-100 0 -163 71t-63 185zM163 243q0 -64 35 -104t90 -40q51 0 87 32.5t36 83.5v93q-34 78 -121 78 q-58 0 -92.5 -39t-34.5 -104z" />
<glyph unicode="e" horiz-adv-x="550" d="M27 240q0 112 72 185.5t183 73.5q108 0 175.5 -68t67.5 -176v-10q-1 -20 -8 -29.5t-26 -9.5h-337q7 -54 45.5 -85t96.5 -31q96 0 138 72q1 2 17.5 -2t33.5 -20t17 -41q0 -43 -57.5 -77.5t-153.5 -34.5q-120 0 -192 69t-72 184zM154 288h240q-2 51 -32.5 82t-81.5 31 q-49 0 -83.5 -31.5t-42.5 -81.5z" />
<glyph unicode="f" horiz-adv-x="381" d="M105 63v315h-40q-59 0 -59 52v8q0 50 59 50h40v26q0 187 169 187q61 0 92.5 -22t31.5 -55q0 -15 -7 -30t-14.5 -22t-8.5 -5q-28 26 -67 26q-31 0 -45.5 -18.5t-14.5 -60.5v-26h63q59 0 59 -52v-9q0 -49 -59 -49h-58v-315q0 -37 -16 -52t-47 -15h-15q-63 0 -63 67z" />
<glyph unicode="g" horiz-adv-x="512" d="M-5 -75q0 50 41.5 83t110.5 43q-17 15 -17 45q0 33 33 57q-63 18 -98.5 60.5t-35.5 100.5q0 79 63 131.5t160 52.5q55 0 105 -21l21 40q27 49 70 49q22 0 40 -14.5t24.5 -29t4.5 -16.5l-85 -81q44 -47 44 -109q0 -78 -61 -126.5t-162 -48.5h-7q-2 -4 -2 -10 q0 -8 4.5 -13.5t17.5 -10t24.5 -7.5t37 -9l42.5 -10q141 -39 141 -138q0 -71 -69.5 -112.5t-193.5 -41.5q-131 0 -192 33t-61 103zM252 224q41 0 67 25.5t26 66.5t-26 66t-68 25q-41 0 -66.5 -25t-25.5 -66t26 -66.5t67 -25.5zM129 -67q0 -55 121 -55q61 0 93.5 15.5 t32.5 44.5q0 8 -3.5 15t-11 12.5t-15 9t-21 7.5t-23 6t-27 6t-26.5 6q-54 -4 -87 -21.5t-33 -45.5z" />
<glyph unicode="h" horiz-adv-x="581" d="M53 63v616q0 3 7 6.5t20 6.5t26 3q88 0 88 -113v-172q54 89 158 89q91 0 136 -57.5t45 -148.5v-230q0 -67 -62 -67h-15q-64 0 -64 67v210q0 51 -24 79.5t-65 28.5q-50 0 -79.5 -30.5t-29.5 -83.5v-204q0 -67 -62 -67h-15q-64 0 -64 67z" />
<glyph unicode="i" d="M128 569q-37 0 -58 19t-21 53q0 33 21 52t58 19t58 -18.5t21 -52.5t-21 -53t-58 -19zM57 63v362q0 68 63 68h15q63 0 63 -68v-362q0 -67 -62 -67h-15q-64 0 -64 67z" />
<glyph unicode="j" d="M128 569q-37 0 -58 19t-21 53q0 33 21 52t58 19t58 -18.5t21 -52.5t-21 -53t-58 -19zM57 -26v451q0 68 63 68h15q63 0 63 -68v-458q0 -80 -41.5 -123.5t-119.5 -43.5q-55 0 -86 21t-31 58q0 15 7 30t14 22t8 6q23 -25 57 -25q51 0 51 62z" />
<glyph unicode="k" horiz-adv-x="514" d="M53 63v616q0 3 7 6.5t20 6.5t26 3q88 0 88 -113v-306l137 173q31 45 78 45q20 0 39.5 -10.5t28.5 -22t7 -15.5l-154 -191l174 -213q2 -4 -7.5 -15.5t-29.5 -22t-40 -10.5q-47 0 -80 45l-153 185v-161q0 -67 -62 -67h-15q-64 0 -64 67z" />
<glyph unicode="l" d="M57 63v616q0 3 7 6.5t20 6.5t26 3q88 0 88 -113v-519q0 -67 -62 -67h-15q-64 0 -64 67z" />
<glyph unicode="m" horiz-adv-x="887" d="M53 63v414q0 2 7 6t20 7t26 3q72 0 85 -78q57 84 151 84q119 0 156 -100q59 100 165 100q90 0 133 -52t43 -142v-242q0 -67 -62 -67h-15q-64 0 -64 67v219q0 99 -85 99q-30 0 -56 -16.5t-41 -43.5v-258q0 -67 -62 -67h-14q-65 0 -65 67v216q0 102 -84 102 q-30 0 -56 -16.5t-41 -43.5v-258q0 -67 -62 -67h-15q-64 0 -64 67z" />
<glyph unicode="n" horiz-adv-x="581" d="M53 63v414q0 2 7 6t20 7t26 3q72 0 85 -80q58 86 161 86q91 0 136 -56t45 -146v-234q0 -67 -62 -67h-15q-64 0 -64 67v213q0 49 -23.5 77t-66.5 28q-74 0 -108 -64v-254q0 -67 -62 -67h-15q-64 0 -64 67z" />
<glyph unicode="o" horiz-adv-x="576" d="M27 244q0 113 73 184t189 71q115 0 187.5 -71.5t72.5 -185.5t-72.5 -184.5t-189.5 -70.5q-116 0 -188 71t-72 186zM151 244q0 -64 39 -105t98 -41t97.5 41t38.5 103q0 64 -38.5 105.5t-97.5 41.5t-98 -41t-39 -104z" />
<glyph unicode="p" horiz-adv-x="605" d="M53 -127v604q0 2 7 6t20 7t26 3q75 0 86 -85q50 91 160 91q100 0 163 -69.5t63 -185.5q0 -114 -63.5 -185.5t-164.5 -71.5q-54 0 -95 25t-61 64v-203q0 -68 -62 -68h-15q-64 0 -64 68zM194 211q1 -49 36 -80.5t86 -31.5q57 0 91.5 39.5t34.5 104.5q0 64 -34.5 103.5 t-90.5 39.5q-89 0 -123 -80v-95z" />
<glyph unicode="q" horiz-adv-x="605" d="M27 243q0 116 63 186t165 70q53 0 95.5 -22.5t63.5 -57.5q15 74 85 74q13 0 26 -3t20 -7t7 -6v-604q0 -68 -64 -68h-15q-62 0 -62 68v206q-52 -92 -158 -92q-100 0 -163 71t-63 185zM163 243q0 -64 35 -104t90 -40q51 0 87 33.5t36 83.5v91q-35 79 -121 79 q-58 0 -92.5 -39t-34.5 -104z" />
<glyph unicode="r" horiz-adv-x="415" d="M53 63v414q0 2 7 6t20 7t26 3q75 0 86 -83q32 89 119 89q42 0 66.5 -22t24.5 -60q0 -25 -11 -43.5t-21.5 -25.5t-12.5 -5q-26 24 -64 24q-99 0 -99 -158v-146q0 -67 -62 -67h-15q-64 0 -64 67z" />
<glyph unicode="s" horiz-adv-x="477" d="M20 95q0 16 10 31t20.5 22t12.5 5q61 -65 161 -65q86 0 86 47q0 24 -21 34t-73 21q-7 1 -11 2q-25 6 -37 9t-36.5 11t-38.5 18t-29.5 25t-23 36.5t-7.5 48.5q0 71 55.5 114.5t148.5 43.5q95 0 146.5 -30t51.5 -74q0 -17 -11 -32t-22 -22.5t-13 -5.5q-56 65 -143 65 q-83 0 -83 -48q0 -22 19 -31t67 -20l6 -1.5t5.5 -1t6.5 -1.5q30 -7 46 -11t46 -16t46 -26t29 -39t13 -57q0 -74 -57.5 -116t-157.5 -42q-102 0 -157 30.5t-55 75.5z" />
<glyph unicode="t" horiz-adv-x="402" d="M97 154v224h-24q-59 0 -59 49v9q0 52 59 52h24v62q0 68 64 68h12q63 0 63 -68v-62h85q57 0 57 -50v-8q0 -52 -58 -52h-87v-208q0 -72 54 -72q37 0 63 33q1 2 9.5 -3.5t16.5 -19.5t8 -32q0 -38 -35.5 -63.5t-93.5 -25.5q-158 0 -158 167z" />
<glyph unicode="u" horiz-adv-x="581" d="M49 189v236q0 68 62 68h15q64 0 64 -68v-215q0 -49 24 -77t68 -28q71 0 105 64v256q0 68 63 68h14q64 0 64 -68v-413q0 -3 -7 -6.5t-20 -6.5t-26 -3q-71 0 -85 77q-58 -86 -160 -86q-91 0 -136 56t-45 146z" />
<glyph unicode="v" horiz-adv-x="532" d="M188 30l-177 422q-2 4 8 14t29.5 19t38.5 9q57 0 80 -72l99 -293l102 293q23 72 77 72q20 0 39 -9t29 -19t8 -14l-175 -421q-15 -37 -79 -37t-79 36z" />
<glyph unicode="w" horiz-adv-x="755" d="M142 35l-127 417q-1 5 12 15t34.5 18.5t39.5 8.5q56 0 68 -62l60 -291l78 253q13 46 76 46q62 0 74 -46l74 -248l60 286q11 62 65 62q19 0 40 -9t33 -19t10 -14l-124 -417q-11 -41 -75 -41q-38 0 -55 9.5t-27 38.5l-79 246l-80 -246q-9 -28 -26.5 -38t-54.5 -10 q-64 0 -76 41z" />
<glyph unicode="x" horiz-adv-x="519" d="M19 41l148 202l-147 204q-2 4 7.5 15t29.5 21.5t41 10.5q52 0 79 -51l83 -130l83 130q28 51 77 51q22 0 42.5 -10.5t30.5 -21.5t7 -15l-149 -204l147 -202q3 -4 -7 -15t-30.5 -21.5t-43.5 -10.5q-49 0 -76 51l-83 130l-82 -130q-28 -51 -78 -51q-21 0 -41.5 10.5 t-30.5 22t-7 14.5z" />
<glyph unicode="y" horiz-adv-x="531" d="M179 63l-168 389q-2 4 8.5 14t29.5 19t39 9q57 0 79 -71l106 -279l94 278q22 72 78 72q18 0 37.5 -8.5t30 -18t8.5 -13.5l-222 -577q-27 -69 -80 -69q-19 0 -38 8.5t-29 17.5t-8 13l81 184q-34 4 -46 32z" />
<glyph unicode="z" horiz-adv-x="494" d="M30 50v8q0 22 6 34t25 33l218 254h-189q-57 0 -57 51v7q0 51 57 51h314q27 0 40.5 -14t13.5 -35v-7q0 -21 -6.5 -33t-24.5 -32l-224 -258h207q57 0 57 -51v-6q0 -52 -57 -52h-327q-27 0 -40 14.5t-13 35.5z" />
<glyph unicode="{" horiz-adv-x="462" d="M95 125q-46 24 -60 43t-14 60t15 60.5t59 42.5l43 23q13 150 82.5 226t155.5 76q32 0 52 -17t20 -45q0 -18 -9 -31.5t-15 -14.5q-83 -11 -117.5 -71t-39.5 -183l-119 -66l118 -66q5 -123 40 -182.5t118 -71.5q2 0 7.5 -5.5t11 -17t5.5 -23.5q0 -28 -20.5 -44.5 t-51.5 -16.5q-86 0 -156 76t-83 226z" />
<glyph unicode="|" horiz-adv-x="271" d="M65 -130v718q0 68 63 68h15q63 0 63 -68v-718q0 -68 -62 -68h-15q-64 0 -64 68z" />
<glyph unicode="}" horiz-adv-x="462" d="M38 -92q83 12 117.5 71.5t39.5 182.5l118 66l-118 66q-5 123 -39.5 183t-118.5 71q-3 1 -8 6t-10 16.5t-5 23.5q0 28 20 45t51 17q86 0 156 -76t83 -226l42 -23q44 -23 59.5 -42.5t15.5 -60.5t-14.5 -60t-60.5 -43l-42 -22q-13 -150 -82.5 -226t-155.5 -76 q-32 0 -52 16.5t-20 44.5q0 12 5.5 23.5t11 17t7.5 5.5z" />
<glyph unicode="~" horiz-adv-x="499" d="M130 160q-21 0 -34 13.5t-13 34.5q0 44 26 73.5t63 29.5q66 0 140 -69q21 65 57 65q21 0 34 -13.5t13 -34.5q0 -44 -26 -73t-63 -29q-67 0 -140 68q-21 -65 -57 -65z" />
<glyph unicode="&#xa1;" horiz-adv-x="277" d="M130 508q-68 0 -68 67v14q0 66 68 66h18q66 0 66 -66v-14q0 -67 -66 -67h-18zM55 80l40 321q10 46 37 46h14q14 0 21.5 -10.5t13.5 -35.5l40 -321q9 -41 -10.5 -65t-58.5 -24h-25q-40 0 -60.5 24t-11.5 65z" />
<glyph unicode="&#xa2;" horiz-adv-x="529" d="M231 27q-96 18 -150.5 85t-54.5 170q0 99 55.5 167.5t148.5 87.5v58q0 68 57 68h5q30 0 43.5 -16t13.5 -52v-54q76 -9 117.5 -43t41.5 -75q0 -24 -18 -41t-36 -22t-21 -2q-22 39 -52.5 56.5t-80.5 17.5q-62 0 -100.5 -40.5t-38.5 -108.5q0 -69 40.5 -109.5t105.5 -40.5 q91 0 130 79q3 3 21 -1.5t36.5 -20.5t18.5 -40q0 -44 -43 -80t-119 -46v-57q0 -68 -58 -68h-4q-31 0 -44.5 16t-13.5 52v60z" />
<glyph unicode="&#xa3;" horiz-adv-x="599" d="M16 55v7q0 53 64 53h24l25 134h-41q-23 0 -36.5 12.5t-13.5 33.5v12q0 22 13.5 34.5t36.5 12.5h60l20 109q35 197 219 197q87 0 137.5 -39t50.5 -95q0 -33 -20.5 -50.5t-41 -19.5t-20.5 1q-18 89 -101 89q-44 0 -67 -23.5t-33 -76.5l-17 -92h132q23 0 36.5 -12.5 t13.5 -34.5v-12q0 -21 -13.5 -33.5t-36.5 -12.5h-151l-24 -134h272q34 0 49 -12.5t15 -40.5v-7q0 -29 -15 -42t-49 -13h-424q-33 0 -48.5 13t-15.5 42z" />
<glyph unicode="&#xa4;" horiz-adv-x="631" d="M73 207q-50 0 -50 39v10q0 39 50 39h35v58h-35q-50 0 -50 40v10q0 39 50 39h44q23 102 95 160t177 58q96 0 155 -39t59 -91q0 -23 -16.5 -41.5t-33 -25t-19.5 -3.5q-29 41 -61.5 60.5t-83.5 19.5q-98 0 -131 -98h159q50 0 50 -39v-10q0 -40 -50 -40h-171v-58h171 q50 0 50 -39v-10q0 -39 -50 -39h-159q16 -47 51 -73.5t84 -26.5q51 0 83.5 21t58.5 64q3 4 20 -3t34 -25.5t17 -41.5q0 -49 -57 -92t-157 -43q-109 0 -181.5 58t-94.5 162h-43z" />
<glyph unicode="&#xa5;" horiz-adv-x="614" d="M101 107q-26 0 -39.5 12t-13.5 30v11q0 42 53 42h141v53h-141q-53 0 -53 42v11q0 18 13.5 30t39.5 12h98l-157 241q-3 5 3.5 19t28 28t53.5 14q47 0 71 -51l109 -209l115 209q27 52 72 52q27 0 47 -14.5t27 -28.5t4 -19l-156 -241h96q27 0 40.5 -11.5t13.5 -30.5v-11 q0 -42 -54 -42h-139v-53h139q54 0 54 -42v-11q0 -19 -13.5 -30.5t-40.5 -11.5h-139v-46q0 -35 -13.5 -51.5t-45.5 -16.5h-15q-57 0 -57 68v46h-141z" />
<glyph unicode="&#xa7;" horiz-adv-x="569" d="M34 -58q0 17 11.5 33t23 23t13.5 5q29 -36 81 -58t108 -22q107 0 107 60q0 16 -7.5 27t-30 20t-37.5 13t-56 13q-37 9 -60.5 15t-57 20t-52.5 30.5t-33.5 43t-14.5 59.5q0 95 105 134q-80 39 -80 124q0 80 64.5 129t170.5 49q110 0 163 -36t53 -79q0 -18 -12 -35 t-24.5 -24t-14.5 -5q-66 76 -158 76q-46 0 -74 -16t-28 -45t33 -44.5t108 -33.5q36 -9 59 -15.5t53.5 -20.5t48.5 -29.5t31 -41t13 -57.5q0 -94 -103 -133q81 -40 81 -126q0 -80 -67 -128.5t-181 -48.5q-115 0 -175.5 36.5t-60.5 87.5zM152 245q0 -13 4.5 -24t17 -19 t22.5 -13.5t32.5 -12t34 -9t40.5 -8.5q13 -3 19 -4l11 -2q38 7 61 29t23 49q0 26 -24 44.5t-47.5 25t-78.5 18.5q-9 2 -13 3q-2 0 -5 0.5t-4 0.5q-42 -7 -67.5 -29.5t-25.5 -48.5z" />
<glyph unicode="&#xa8;" horiz-adv-x="500" d="M353 544q-35 0 -54.5 19t-19.5 53t19 53t52 19q34 0 54 -19t20 -53t-19.5 -53t-51.5 -19zM150 544q-34 0 -54 19t-20 53t19.5 53t51.5 19q35 0 54.5 -19t19.5 -53t-19 -53t-52 -19z" />
<glyph unicode="&#xa9;" horiz-adv-x="746" d="M40 323q0 146 94 241.5t240 95.5q147 0 240 -94t93 -241t-94 -242.5t-240 -95.5q-147 0 -240 94.5t-93 241.5zM104 324q0 -120 76 -199t194 -79q120 0 194.5 77.5t74.5 199.5q0 121 -76 199.5t-194 78.5q-120 0 -194.5 -77.5t-74.5 -199.5zM199 322q0 79 52 130.5 t131 51.5q77 0 116 -41q19 -19 18 -41q0 -15 -11.5 -27.5t-23 -17t-13.5 -2.5q-30 43 -83 43q-37 0 -63.5 -27t-26.5 -68q0 -42 26.5 -69t65.5 -27q55 0 84 46q1 3 13 -2t23.5 -17.5t11.5 -28.5q1 -22 -21 -44q-39 -41 -115 -41q-81 0 -132.5 51t-51.5 131z" />
<glyph unicode="&#xaa;" horiz-adv-x="444" d="M162 252q-63 0 -100.5 30.5t-37.5 80.5q0 43 21 69.5t79.5 43t160.5 19.5q-2 77 -79 77q-59 0 -93 -60q-3 0 -19.5 -0.5t-35.5 12.5t-19 36q0 41 45 70.5t130 29.5q186 0 186 -166v-226q0 -5 -13.5 -9t-30.5 -4q-55 0 -66 61q-19 -30 -53 -47t-75 -17zM201 329 q36 0 60.5 22t24.5 53v32q-86 -4 -118.5 -19t-32.5 -43q0 -21 18 -33t48 -12zM148 52v12q0 60 60 60h16q61 0 61 -60v-12q0 -60 -61 -60h-16q-60 0 -60 60z" />
<glyph unicode="&#xab;" horiz-adv-x="533" d="M253 245q0 10 31.5 53.5t79 85.5t80.5 42h2q24 0 37.5 -14t13.5 -36v-3q0 -45 -136 -128q136 -84 136 -127v-4q0 -22 -13.5 -36t-37.5 -14h-2q-33 0 -80.5 42t-79 85.5t-31.5 53.5zM209 82q-30 0 -73.5 38t-73 77t-29.5 48t29.5 48t73 77t73.5 38h2q22 0 34 -12.5 t12 -33.5v-2q0 -42 -121 -115q121 -75 121 -115v-2q0 -20 -12 -33t-34 -13h-2z" />
<glyph unicode="&#xad;" horiz-adv-x="428" d="M94 180q-28 0 -43 15t-15 39v21q0 23 15 38.5t43 15.5h240q28 0 43 -15.5t15 -38.5v-21q0 -24 -15 -39t-43 -15h-240z" />
<glyph unicode="&#xae;" horiz-adv-x="746" d="M40 323q0 146 94 241.5t240 95.5q147 0 240 -94t93 -241t-94 -242.5t-240 -95.5q-147 0 -240 94.5t-93 241.5zM104 324q0 -120 76 -199t194 -79q120 0 194.5 77.5t74.5 199.5q0 121 -76 199.5t-194 78.5q-120 0 -194.5 -77.5t-74.5 -199.5zM231 200v248q0 52 49 52h107 q146 0 146 -113q0 -83 -82 -103l80 -105q2 -3 -5 -11t-21 -15.5t-28 -7.5q-34 0 -55 35l-61 87h-36v-77q0 -43 -41 -43h-13q-40 0 -40 53zM323 336h59q57 0 57 45q0 46 -59 46h-57v-91z" />
<glyph unicode="&#xaf;" horiz-adv-x="500" d="M138 555q-62 0 -62 51v6q0 24 16.5 37t45.5 13h224q62 0 62 -50v-6q0 -51 -62 -51h-224z" />
<glyph unicode="&#xb0;" horiz-adv-x="388" d="M193 339q-71 0 -115.5 43t-44.5 113v8q0 70 45.5 113.5t117.5 43.5q70 0 115 -43t45 -113v-8q0 -70 -46 -113.5t-117 -43.5zM195 415q31 0 51 21t20 57v13q0 35 -20.5 57t-51.5 22q-32 0 -52 -21t-20 -58v-13q0 -35 21 -56.5t52 -21.5z" />
<glyph unicode="&#xb1;" horiz-adv-x="600" d="M294 216q-51 0 -51 58v102h-115q-58 0 -58 49v8q0 22 14.5 35.5t43.5 13.5h115v109q0 58 51 58h11q50 0 50 -58v-109h116q29 0 43.5 -13.5t14.5 -35.5v-8q0 -49 -58 -49h-116v-102q0 -58 -50 -58h-11zM60 115v8q0 50 58 50h363q58 0 58 -50v-8q0 -50 -58 -50h-363 q-58 0 -58 50z" />
<glyph unicode="&#xb2;" horiz-adv-x="336" d="M89 285q-36 0 -48.5 10.5t-12.5 39.5v20q0 26 7.5 45t25 33.5t33 23.5t44.5 22q5 2 14 6t14 6t12.5 5.5t11.5 6t9 5.5t7.5 6t5.5 6.5t4 7.5t1 8q0 19 -13.5 31t-37.5 12q-29 0 -47 -18.5t-23 -44.5q-3 1 -18 0t-32 11t-17 34q0 39 38.5 66.5t104.5 27.5t103 -31t37 -84 q0 -25 -7.5 -43.5t-24 -31.5t-30.5 -20t-39 -17q-9 -4 -26.5 -10.5t-26.5 -10.5t-21 -10.5t-18.5 -14t-9.5 -17.5q52 1 158 1q47 0 47 -38v-3q0 -40 -47 -40h-178z" />
<glyph unicode="&#xb3;" horiz-adv-x="336" d="M159 278q-65 0 -104.5 24.5t-39.5 58.5q0 23 15.5 35.5t29 12t16.5 -0.5q16 -55 80 -55q29 0 47 12t18 32q0 39 -53 39h-14q-46 0 -46 33v6q0 33 45 33h22q18 0 30 9t12 24q0 17 -17 28t-45 11q-58 0 -73 -52q-3 0 -15.5 -0.5t-27.5 12t-15 35.5q0 34 36.5 57t97.5 23 q148 0 148 -101q0 -56 -62 -74q69 -23 69 -91q0 -53 -39.5 -82t-114.5 -29z" />
<glyph unicode="&#xb4;" horiz-adv-x="500" d="M172 560q-4 7 11 34t44.5 61t59.5 53q26 17 50 17q27 0 45.5 -16t18.5 -39q0 -46 -49 -76q-36 -22 -78.5 -32t-70 -9.5t-31.5 7.5z" />
<glyph unicode="&#xb6;" horiz-adv-x="623" d="M473 36v575q0 43 38 43h14q40 0 40 -43v-575q0 -43 -40 -43h-14q-38 0 -38 43zM326 36v243h-69q-115 0 -175.5 46.5t-60.5 137.5q0 189 233 189h111q25 0 39 -14t14 -37v-565q0 -43 -39 -43h-13q-40 0 -40 43z" />
<glyph unicode="&#xb7;" horiz-adv-x="245" d="M113 172q-67 0 -67 66v14q0 67 67 67h17q68 0 68 -67v-14q0 -66 -68 -66h-17z" />
<glyph unicode="&#xb8;" horiz-adv-x="500" d="M190 -91q9 25 22.5 35t45.5 10h15q59 0 59 -42v-5q0 -22 -38 -54l-77 -76q-14 -8 -32 -8.5t-31 9t-8 23.5z" />
<glyph unicode="&#xb9;" horiz-adv-x="336" d="M82 285q-46 0 -46 40v1q0 43 46 43h53v165q-19 -14 -43 -14q-25 0 -42.5 15t-17.5 39q0 15 6.5 27t13.5 17t9 4q18 -13 37 -13t33.5 11.5t18.5 31.5h33q45 0 45 -48v-235h38q47 0 47 -43v-1q0 -40 -47 -40h-184z" />
<glyph unicode="&#xba;" horiz-adv-x="477" d="M238 249q-91 0 -149 58.5t-58 146.5q0 87 59.5 146.5t149.5 59.5t148.5 -58.5t58.5 -146.5q0 -87 -59.5 -146.5t-149.5 -59.5zM239 345q44 0 73.5 31.5t29.5 78.5t-29.5 79t-73.5 32t-73.5 -32t-29.5 -79q0 -46 29.5 -78t73.5 -32zM169 52v12q0 60 60 60h16q61 0 61 -60 v-12q0 -60 -61 -60h-16q-60 0 -60 60z" />
<glyph unicode="&#xbb;" horiz-adv-x="533" d="M88 64q-25 0 -38.5 14t-13.5 37v3q0 43 136 128q-136 82 -136 127v4q0 22 13.5 35.5t38.5 13.5h1q33 0 80.5 -42t79 -85t31.5 -54q0 -10 -31.5 -53.5t-79 -85.5t-80.5 -42h-1zM276 128v3q0 37 121 115q-121 73 -121 115v2q0 20 12 33t34 13h2q30 0 73.5 -38t73 -77.5 t29.5 -48.5t-29.5 -48t-73 -77t-73.5 -38h-2q-22 0 -34 13t-12 33z" />
<glyph unicode="&#xbc;" horiz-adv-x="792" d="M82 285q-46 0 -46 40v1q0 43 46 43h53v165q-19 -14 -43 -14q-25 0 -42.5 15t-17.5 39q0 15 6.5 27t13.5 17t9 4q18 -13 37 -13t33.5 11.5t18.5 31.5h33q45 0 45 -48v-235h38q47 0 47 -43v-1q0 -40 -47 -40h-184zM139 5q0 22 23 51q4 5 37.5 45t82.5 98.5t84 102.5 l265 334q3 1 13 0.5t20.5 -10.5t10.5 -26q0 -21 -23 -51q-117 -139 -204 -247l-264 -333q-4 -2 -14.5 -1t-20.5 11.5t-10 25.5zM463 124v15q1 15 18 49l93 144q12 22 28 28.5t55 6.5q38 0 54 -14.5t16 -51.5v-144h17q23 0 33 -8t10 -27v-3q0 -19 -10 -27.5t-33 -8.5h-17v-40 q0 -47 -43 -47h-5q-41 0 -41 47v40h-120q-25 0 -40 11.5t-15 29.5zM546 155h97l-1 148z" />
<glyph unicode="&#xbd;" horiz-adv-x="792" d="M82 285q-46 0 -46 40v1q0 43 46 43h53v165q-19 -14 -43 -14q-25 0 -42.5 15t-17.5 39q0 15 6.5 27t13.5 17t9 4q18 -13 37 -13t33.5 11.5t18.5 31.5h33q45 0 45 -48v-235h38q47 0 47 -43v-1q0 -40 -47 -40h-184zM114 5q0 22 23 51q4 5 37.5 45t82.5 98.5t84 102.5 l265 334q3 1 13 0.5t20.5 -10.5t10.5 -26q0 -21 -23 -51q-117 -139 -204 -247l-264 -333q-4 -2 -14.5 -1t-20.5 11.5t-10 25.5zM484 50v20q0 26 7.5 45t25 33.5t33 23.5t44.5 22q5 2 14 6t14 6t12.5 5.5t11.5 6t9 5.5t7.5 6t5.5 6.5t4 7.5t1 8q0 19 -13.5 31t-37.5 12 q-29 0 -47 -18.5t-23 -44.5q-3 1 -18 0t-32 11t-17 34q0 39 38.5 66.5t104.5 27.5t103 -31t37 -84q0 -25 -7.5 -43.5t-24 -31.5t-30.5 -20t-39 -17q-9 -4 -26.5 -10.5t-26.5 -10.5t-21 -10.5t-18.5 -14t-9.5 -17.5q52 1 158 1q47 0 47 -38v-3q0 -40 -47 -40h-178 q-36 0 -48.5 10.5t-12.5 39.5z" />
<glyph unicode="&#xbe;" horiz-adv-x="792" d="M159 278q-65 0 -104.5 24.5t-39.5 58.5q0 23 15.5 35.5t29 12t16.5 -0.5q16 -55 80 -55q29 0 47 12t18 32q0 39 -53 39h-14q-46 0 -46 33v6q0 33 45 33h22q18 0 30 9t12 24q0 17 -17 28t-45 11q-58 0 -73 -52q-3 0 -15.5 -0.5t-27.5 12t-15 35.5q0 34 36.5 57t97.5 23 q148 0 148 -101q0 -56 -62 -74q69 -23 69 -91q0 -53 -39.5 -82t-114.5 -29zM135 5q0 22 23 51q4 5 37.5 45t82.5 98.5t84 102.5l265 334q3 1 13 0.5t20.5 -10.5t10.5 -26q0 -21 -23 -51q-117 -139 -204 -247l-264 -333q-4 -2 -14.5 -1t-20.5 11.5t-10 25.5zM463 124v15 q1 15 18 49l93 144q12 22 28 28.5t55 6.5q38 0 54 -14.5t16 -51.5v-144h17q23 0 33 -8t10 -27v-3q0 -19 -10 -27.5t-33 -8.5h-17v-40q0 -47 -43 -47h-5q-41 0 -41 47v40h-120q-25 0 -40 11.5t-15 29.5zM546 155h97l-1 148z" />
<glyph unicode="&#xbf;" horiz-adv-x="499" d="M262 508q-68 0 -68 67v14q0 66 68 66h17q67 0 67 -66v-14q0 -67 -67 -67h-17zM21 167q0 38 13.5 68t34.5 46.5t45.5 33t45.5 29t36.5 33.5t17.5 48q6 27 57 27q60 0 60 -49q-1 -46 -19.5 -78.5t-44 -49t-50.5 -29.5t-42.5 -29.5t-17.5 -39.5q0 -34 24.5 -54t68.5 -20 q58 0 90.5 33.5t39.5 81.5q1 0 10 1.5t23.5 -2.5t28.5 -10t24 -21.5t10 -36.5q0 -61 -61 -111t-168 -50q-105 0 -165.5 48.5t-60.5 130.5z" />
<glyph unicode="&#xc0;" horiz-adv-x="656" d="M208 755q-49 30 -49 76q0 23 18.5 39t45.5 16q24 0 50 -17q30 -19 59.5 -53t44.5 -61t11 -34t-31.5 -7.5t-70 9.5t-78.5 32zM10 38l221 571q7 22 28 33t68 11q48 0 68 -11t27 -33l226 -570q3 -6 -8 -17t-31.5 -20t-40.5 -9q-59 0 -85 70l-39 102h-237l-39 -104 q-24 -68 -81 -68q-19 0 -38.5 8.5t-30.5 19t-8 17.5zM236 275h178l-89 235z" />
<glyph unicode="&#xc1;" horiz-adv-x="656" d="M260 721q-4 7 11 34t44.5 61t59.5 53q26 17 50 17q27 0 45.5 -16t18.5 -39q0 -46 -49 -76q-36 -22 -78.5 -32t-70 -9.5t-31.5 7.5zM10 38l221 571q7 22 28 33t68 11q48 0 68 -11t27 -33l226 -570q3 -6 -8 -17t-31.5 -20t-40.5 -9q-59 0 -85 70l-39 102h-237l-39 -104 q-24 -68 -81 -68q-19 0 -38.5 8.5t-30.5 19t-8 17.5zM236 275h178l-89 235z" />
<glyph unicode="&#xc2;" horiz-adv-x="656" d="M221 705q-20 0 -33 12.5t-13 31.5q0 41 57.5 92t94.5 60q36 -9 93.5 -60t57.5 -92q0 -19 -12.5 -31.5t-32.5 -12.5q-49 0 -107 101q-56 -101 -105 -101zM10 38l221 571q7 22 28 33t68 11q48 0 68 -11t27 -33l226 -570q3 -6 -8 -17t-31.5 -20t-40.5 -9q-59 0 -85 70 l-39 102h-237l-39 -104q-24 -68 -81 -68q-19 0 -38.5 8.5t-30.5 19t-8 17.5zM236 275h178l-89 235z" />
<glyph unicode="&#xc3;" horiz-adv-x="656" d="M211 708q-21 0 -34 13.5t-13 34.5q0 44 26 73.5t63 29.5q66 0 140 -69q21 65 57 65q21 0 34 -13.5t13 -34.5q0 -44 -26 -73t-63 -29q-67 0 -140 68q-21 -65 -57 -65zM10 38l221 571q7 22 28 33t68 11q48 0 68 -11t27 -33l226 -570q3 -6 -8 -17t-31.5 -20t-40.5 -9 q-59 0 -85 70l-39 102h-237l-39 -104q-24 -68 -81 -68q-19 0 -38.5 8.5t-30.5 19t-8 17.5zM236 275h178l-89 235z" />
<glyph unicode="&#xc4;" horiz-adv-x="656" d="M430 705q-35 0 -54.5 19t-19.5 53t19 53t52 19q34 0 54 -19t20 -53t-19.5 -53t-51.5 -19zM227 705q-34 0 -54 19t-20 53t19.5 53t51.5 19q35 0 54.5 -19t19.5 -53t-19 -53t-52 -19zM10 38l221 571q7 22 28 33t68 11q48 0 68 -11t27 -33l226 -570q3 -6 -8 -17t-31.5 -20 t-40.5 -9q-59 0 -85 70l-39 102h-237l-39 -104q-24 -68 -81 -68q-19 0 -38.5 8.5t-30.5 19t-8 17.5zM236 275h178l-89 235z" />
<glyph unicode="&#xc5;" horiz-adv-x="656" d="M10 38l221 571q3 11 19 17q-48 32 -48 96q0 53 36 88t90 35q55 0 90 -35t35 -88q0 -64 -51 -96q16 -8 20 -17l226 -570q3 -6 -8 -17t-31.5 -20t-40.5 -9q-59 0 -85 70l-39 102h-237l-39 -104q-24 -68 -81 -68q-19 0 -38.5 8.5t-30.5 19t-8 17.5zM327 665q23 0 39 16.5 t16 40.5q0 25 -16 42t-39 17t-38.5 -17t-15.5 -42q0 -24 15.5 -40.5t38.5 -16.5zM236 275h178l-89 236z" />
<glyph unicode="&#xc6;" horiz-adv-x="875" d="M10 38l216 558q16 51 95 51h450q68 0 68 -62v-5q0 -62 -68 -62h-318l49 -128h191q68 0 68 -57v-4q0 -60 -68 -60h-141l55 -141h169q68 0 68 -58v-6q0 -64 -68 -64h-192q-44 0 -66.5 14t-38.5 55l-36 96h-236l-39 -104q-25 -68 -81 -68q-20 0 -39.5 8.5t-30 19t-7.5 17.5z M236 275h177l-88 235z" />
<glyph unicode="&#xc7;" horiz-adv-x="654" d="M37 323q0 148 93 242.5t241 94.5q116 0 182.5 -49t66.5 -97q0 -29 -21 -48t-41.5 -25t-22.5 -2q-24 41 -66.5 64.5t-91.5 23.5q-82 0 -135 -56.5t-53 -146.5t53.5 -147t135.5 -57q56 0 99 26t62 72q2 4 23.5 -2t43 -25t21.5 -48q0 -51 -69 -103.5t-189 -52.5 q-147 0 -239.5 93.5t-92.5 242.5zM292 -91q9 25 22.5 35t45.5 10h15q59 0 59 -42v-5q0 -22 -38 -54l-77 -76q-14 -8 -32 -8.5t-31 9t-8 23.5z" />
<glyph unicode="&#xc8;" horiz-adv-x="600" d="M220 755q-49 30 -49 76q0 23 18.5 39t45.5 16q24 0 50 -17q30 -19 59.5 -53t44.5 -61t11 -34t-31.5 -7.5t-70 9.5t-78.5 32zM58 81v484q0 39 20.5 60.5t56.5 21.5h361q68 0 68 -62v-5q0 -62 -68 -62h-290v-128h212q68 0 68 -57v-4q0 -60 -68 -60h-212v-141h295 q68 0 68 -58v-6q0 -64 -68 -64h-366q-36 0 -56.5 21t-20.5 60z" />
<glyph unicode="&#xc9;" horiz-adv-x="600" d="M224 721q-4 7 11 34t44.5 61t59.5 53q26 17 50 17q27 0 45.5 -16t18.5 -39q0 -46 -49 -76q-36 -22 -78.5 -32t-70 -9.5t-31.5 7.5zM58 81v484q0 39 20.5 60.5t56.5 21.5h361q68 0 68 -62v-5q0 -62 -68 -62h-290v-128h212q68 0 68 -57v-4q0 -60 -68 -60h-212v-141h295 q68 0 68 -58v-6q0 -64 -68 -64h-366q-36 0 -56.5 21t-20.5 60z" />
<glyph unicode="&#xca;" horiz-adv-x="600" d="M208 705q-20 0 -33 12.5t-13 31.5q0 41 57.5 92t94.5 60q36 -9 93.5 -60t57.5 -92q0 -19 -12.5 -31.5t-32.5 -12.5q-49 0 -107 101q-56 -101 -105 -101zM58 81v484q0 39 20.5 60.5t56.5 21.5h361q68 0 68 -62v-5q0 -62 -68 -62h-290v-128h212q68 0 68 -57v-4 q0 -60 -68 -60h-212v-141h295q68 0 68 -58v-6q0 -64 -68 -64h-366q-36 0 -56.5 21t-20.5 60z" />
<glyph unicode="&#xcb;" horiz-adv-x="600" d="M417 705q-35 0 -54.5 19t-19.5 53t19 53t52 19q34 0 54 -19t20 -53t-19.5 -53t-51.5 -19zM214 705q-34 0 -54 19t-20 53t19.5 53t51.5 19q35 0 54.5 -19t19.5 -53t-19 -53t-52 -19zM58 81v484q0 39 20.5 60.5t56.5 21.5h361q68 0 68 -62v-5q0 -62 -68 -62h-290v-128h212 q68 0 68 -57v-4q0 -60 -68 -60h-212v-141h295q68 0 68 -58v-6q0 -64 -68 -64h-366q-36 0 -56.5 21t-20.5 60z" />
<glyph unicode="&#xcc;" horiz-adv-x="280" d="M214 721q-4 -7 -31.5 -7.5t-70 9.5t-78.5 32q-49 30 -49 76q0 23 18.5 39t45.5 16q24 0 50 -17q30 -19 59.5 -53t44.5 -61t11 -34zM66 63v521q0 67 63 67h20q65 0 65 -67v-521q0 -67 -65 -67h-20q-63 0 -63 67z" />
<glyph unicode="&#xcd;" horiz-adv-x="280" d="M63 721q-4 7 11 34t44.5 61t59.5 53q26 17 50 17q27 0 45.5 -16t18.5 -39q0 -46 -49 -76q-36 -22 -78.5 -32t-70 -9.5t-31.5 7.5zM66 63v521q0 67 63 67h20q65 0 65 -67v-521q0 -67 -65 -67h-20q-63 0 -63 67z" />
<glyph unicode="&#xce;" horiz-adv-x="280" d="M33 705q-20 0 -33 12.5t-13 31.5q0 41 57.5 92t94.5 60q36 -9 93.5 -60t57.5 -92q0 -19 -12.5 -31.5t-32.5 -12.5q-49 0 -107 101q-56 -101 -105 -101zM66 63v521q0 67 63 67h20q65 0 65 -67v-521q0 -67 -65 -67h-20q-63 0 -63 67z" />
<glyph unicode="&#xcf;" horiz-adv-x="280" d="M242 705q-35 0 -54.5 19t-19.5 53t19 53t52 19q34 0 54 -19t20 -53t-19.5 -53t-51.5 -19zM39 705q-34 0 -54 19t-20 53t19.5 53t51.5 19q35 0 54.5 -19t19.5 -53t-19 -53t-52 -19zM66 63v521q0 67 63 67h20q65 0 65 -67v-521q0 -67 -65 -67h-20q-63 0 -63 67z" />
<glyph unicode="&#xd0;" horiz-adv-x="726" d="M95 81v191h-36q-26 0 -38.5 13t-12.5 34v12q0 20 12.5 33t38.5 13h36v188q0 38 22 60t56 22h167q162 0 256 -86t94 -235q0 -150 -96 -238t-259 -88h-158q-34 0 -58 23.5t-24 57.5zM242 127h95q98 0 152.5 52t54.5 146q0 92 -54.5 143.5t-150.5 51.5h-97v-143h125 q50 0 50 -46v-12q0 -47 -50 -47h-125v-145z" />
<glyph unicode="&#xd1;" horiz-adv-x="692" d="M226 708q-21 0 -34 13.5t-13 34.5q0 44 26 73.5t63 29.5q66 0 140 -69q21 65 57 65q21 0 34 -13.5t13 -34.5q0 -44 -26 -73t-63 -29q-67 0 -140 68q-21 -65 -57 -65zM58 63v515q0 35 17 54t47 19h18q27 0 41.5 -9t33.5 -33l280 -385v360q0 67 63 67h14q62 0 62 -67v-515 q0 -35 -16.5 -54t-46.5 -19h-12q-28 0 -41 7.5t-30 28.5l-291 400v-369q0 -67 -64 -67h-14q-61 0 -61 67z" />
<glyph unicode="&#xd2;" horiz-adv-x="737" d="M255 755q-49 30 -49 76q0 23 18.5 39t45.5 16q24 0 50 -17q30 -19 59.5 -53t44.5 -61t11 -34t-31.5 -7.5t-70 9.5t-78.5 32zM35 323q0 148 94 242.5t240 94.5q147 0 240 -94t93 -242t-93.5 -242.5t-240.5 -94.5t-240 94t-93 242zM182 324q0 -92 52 -149t135 -57t135 57 t52 148q0 92 -52.5 149.5t-135.5 57.5t-134.5 -56.5t-51.5 -149.5z" />
<glyph unicode="&#xd3;" horiz-adv-x="737" d="M293 721q-4 7 11 34t44.5 61t59.5 53q26 17 50 17q27 0 45.5 -16t18.5 -39q0 -46 -49 -76q-36 -22 -78.5 -32t-70 -9.5t-31.5 7.5zM35 323q0 148 94 242.5t240 94.5q147 0 240 -94t93 -242t-93.5 -242.5t-240.5 -94.5t-240 94t-93 242zM182 324q0 -92 52 -149t135 -57 t135 57t52 148q0 92 -52.5 149.5t-135.5 57.5t-134.5 -56.5t-51.5 -149.5z" />
<glyph unicode="&#xd4;" horiz-adv-x="737" d="M263 705q-20 0 -33 12.5t-13 31.5q0 41 57.5 92t94.5 60q36 -9 93.5 -60t57.5 -92q0 -19 -12.5 -31.5t-32.5 -12.5q-49 0 -107 101q-56 -101 -105 -101zM35 323q0 148 94 242.5t240 94.5q147 0 240 -94t93 -242t-93.5 -242.5t-240.5 -94.5t-240 94t-93 242zM182 324 q0 -92 52 -149t135 -57t135 57t52 148q0 92 -52.5 149.5t-135.5 57.5t-134.5 -56.5t-51.5 -149.5z" />
<glyph unicode="&#xd5;" horiz-adv-x="737" d="M250 708q-21 0 -34 13.5t-13 34.5q0 44 26 73.5t63 29.5q66 0 140 -69q21 65 57 65q21 0 34 -13.5t13 -34.5q0 -44 -26 -73t-63 -29q-67 0 -140 68q-21 -65 -57 -65zM35 323q0 148 94 242.5t240 94.5q147 0 240 -94t93 -242t-93.5 -242.5t-240.5 -94.5t-240 94t-93 242z M182 324q0 -92 52 -149t135 -57t135 57t52 148q0 92 -52.5 149.5t-135.5 57.5t-134.5 -56.5t-51.5 -149.5z" />
<glyph unicode="&#xd6;" horiz-adv-x="737" d="M472 705q-35 0 -54.5 19t-19.5 53t19 53t52 19q34 0 54 -19t20 -53t-19.5 -53t-51.5 -19zM269 705q-34 0 -54 19t-20 53t19.5 53t51.5 19q35 0 54.5 -19t19.5 -53t-19 -53t-52 -19zM35 323q0 148 94 242.5t240 94.5q147 0 240 -94t93 -242t-93.5 -242.5t-240.5 -94.5 t-240 94t-93 242zM182 324q0 -92 52 -149t135 -57t135 57t52 148q0 92 -52.5 149.5t-135.5 57.5t-134.5 -56.5t-51.5 -149.5z" />
<glyph unicode="&#xd7;" horiz-adv-x="554" d="M86 69q-37 37 5 77l112 111l-112 111q-40 39 -6 77l9 8q35 38 77 -7l107 -113l107 113q43 46 78 7l8 -8q36 -38 -5 -77l-112 -111l112 -111q42 -40 5 -77l-9 -8q-35 -37 -77 6l-107 114l-107 -114q-41 -42 -77 -6z" />
<glyph unicode="&#xd8;" horiz-adv-x="737" d="M38 6q0 21 23 48l47 52q-72 89 -72 217q0 145 93.5 241t240.5 96q109 0 191 -55l72 80q3 3 17.5 -1.5t28 -18t13.5 -29.5q0 -22 -23 -48l-40 -45q74 -91 74 -219q0 -146 -93.5 -241.5t-240.5 -95.5q-112 0 -194 56l-78 -86q-3 -4 -17 1t-28 18.5t-14 29.5zM202 207 l270 300q-46 27 -103 27q-89 0 -142.5 -58.5t-53.5 -151.5q0 -67 29 -117zM264 141q47 -28 106 -28q88 0 142.5 59t54.5 151q0 67 -31 120z" />
<glyph unicode="&#xd9;" horiz-adv-x="663" d="M222 749q-49 30 -49 76q0 23 18.5 39t45.5 16q24 0 50 -17q30 -19 59.5 -53t44.5 -61t11 -34t-31.5 -7.5t-70 9.5t-78.5 32zM53 272v312q0 67 64 67h19q64 0 64 -67v-310q0 -157 132 -157q131 0 131 158v309q0 67 64 67h20q63 0 63 -67v-310q0 -140 -71 -212.5 t-208 -72.5t-207.5 71.5t-70.5 211.5z" />
<glyph unicode="&#xda;" horiz-adv-x="663" d="M263 715q-4 7 11 34t44.5 61t59.5 53q26 17 50 17q27 0 45.5 -16t18.5 -39q0 -46 -49 -76q-36 -22 -78.5 -32t-70 -9.5t-31.5 7.5zM53 272v312q0 67 64 67h19q64 0 64 -67v-310q0 -157 132 -157q131 0 131 158v309q0 67 64 67h20q63 0 63 -67v-310q0 -140 -71 -212.5 t-208 -72.5t-207.5 71.5t-70.5 211.5z" />
<glyph unicode="&#xdb;" horiz-adv-x="663" d="M224 705q-20 0 -33 12.5t-13 31.5q0 41 57.5 92t94.5 60q36 -9 93.5 -60t57.5 -92q0 -19 -12.5 -31.5t-32.5 -12.5q-49 0 -107 101q-56 -101 -105 -101zM53 272v312q0 67 64 67h19q64 0 64 -67v-310q0 -157 132 -157q131 0 131 158v309q0 67 64 67h20q63 0 63 -67v-310 q0 -140 -71 -212.5t-208 -72.5t-207.5 71.5t-70.5 211.5z" />
<glyph unicode="&#xdc;" horiz-adv-x="663" d="M433 705q-35 0 -54.5 19t-19.5 53t19 53t52 19q34 0 54 -19t20 -53t-19.5 -53t-51.5 -19zM230 705q-34 0 -54 19t-20 53t19.5 53t51.5 19q35 0 54.5 -19t19.5 -53t-19 -53t-52 -19zM53 272v312q0 67 64 67h19q64 0 64 -67v-310q0 -157 132 -157q131 0 131 158v309 q0 67 64 67h20q63 0 63 -67v-310q0 -140 -71 -212.5t-208 -72.5t-207.5 71.5t-70.5 211.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="603" d="M228 715q-4 7 11 34t44.5 61t59.5 53q26 17 50 17q27 0 45.5 -16t18.5 -39q0 -46 -49 -76q-36 -22 -78.5 -32t-70 -9.5t-31.5 7.5zM228 63v197l-226 334q-3 5 5 18t32 26t57 13q42 0 70 -51l136 -223l138 222q30 52 71 52q30 0 53 -13t31.5 -26.5t5.5 -17.5l-225 -334 v-197q0 -67 -65 -67h-20q-63 0 -63 67z" />
<glyph unicode="&#xde;" horiz-adv-x="597" d="M58 63v521q0 67 62 67h21q65 0 65 -67v-44h95q130 0 198.5 -55t68.5 -160q0 -213 -266 -213h-96v-49q0 -67 -65 -67h-21q-62 0 -62 67zM206 224h96q124 0 124 100q0 101 -123 101h-97v-201z" />
<glyph unicode="&#xdf;" horiz-adv-x="585" d="M53 61v394q0 126 61 185.5t186 59.5q106 0 166.5 -46t60.5 -123q0 -119 -123 -141q81 -26 122 -80t41 -127q0 -85 -54.5 -140.5t-137.5 -55.5q-70 0 -107.5 24t-37.5 62q0 15 8 30t16.5 22.5t10.5 6.5q32 -34 83 -34q35 0 59.5 24.5t24.5 59.5q0 41 -25.5 70t-87.5 46 q-32 10 -47 26t-15 48q0 15 6.5 30t12.5 22l6 8q110 0 110 81q0 37 -25.5 58.5t-68.5 21.5q-104 0 -104 -127v-405q0 -68 -63 -68h-15q-29 0 -46 18t-17 50z" />
<glyph unicode="&#xe0;" horiz-adv-x="546" d="M175 594q-49 30 -49 76q0 23 18.5 39t45.5 16q24 0 50 -17q30 -19 59.5 -53t44.5 -61t11 -34t-31.5 -7.5t-70 9.5t-78.5 32zM28 130q0 84 76.5 124t237.5 42h16q0 97 -100 97q-85 0 -121 -75q0 -3 -22.5 -1t-45.5 17t-23 44q0 50 58 85.5t163 35.5q230 0 230 -201v-289 q0 -5 -16.5 -10.5t-36.5 -5.5q-71 0 -83 77q-56 -81 -169 -81q-76 0 -120 39t-44 102zM163 140q0 -27 20.5 -43t55.5 -16q52 0 85.5 31t33.5 79v28l-26 -1q-85 -2 -127 -20.5t-42 -57.5z" />
<glyph unicode="&#xe1;" horiz-adv-x="546" d="M191 560q-4 7 11 34t44.5 61t59.5 53q26 17 50 17q27 0 45.5 -16t18.5 -39q0 -46 -49 -76q-36 -22 -78.5 -32t-70 -9.5t-31.5 7.5zM28 130q0 84 76.5 124t237.5 42h16q0 97 -100 97q-85 0 -121 -75q0 -3 -22.5 -1t-45.5 17t-23 44q0 50 58 85.5t163 35.5q230 0 230 -201 v-289q0 -5 -16.5 -10.5t-36.5 -5.5q-71 0 -83 77q-56 -81 -169 -81q-76 0 -120 39t-44 102zM163 140q0 -27 20.5 -43t55.5 -16q52 0 85.5 31t33.5 79v28l-26 -1q-85 -2 -127 -20.5t-42 -57.5z" />
<glyph unicode="&#xe2;" horiz-adv-x="546" d="M167 544q-20 0 -33 12.5t-13 31.5q0 41 57.5 92t94.5 60q36 -9 93.5 -60t57.5 -92q0 -19 -12.5 -31.5t-32.5 -12.5q-49 0 -107 101q-56 -101 -105 -101zM28 130q0 84 76.5 124t237.5 42h16q0 97 -100 97q-85 0 -121 -75q0 -3 -22.5 -1t-45.5 17t-23 44q0 50 58 85.5 t163 35.5q230 0 230 -201v-289q0 -5 -16.5 -10.5t-36.5 -5.5q-71 0 -83 77q-56 -81 -169 -81q-76 0 -120 39t-44 102zM163 140q0 -27 20.5 -43t55.5 -16q52 0 85.5 31t33.5 79v28l-26 -1q-85 -2 -127 -20.5t-42 -57.5z" />
<glyph unicode="&#xe3;" horiz-adv-x="546" d="M150 547q-21 0 -34 13.5t-13 34.5q0 44 26 73.5t63 29.5q66 0 140 -69q21 65 57 65q21 0 34 -13.5t13 -34.5q0 -44 -26 -73t-63 -29q-67 0 -140 68q-21 -65 -57 -65zM28 130q0 84 76.5 124t237.5 42h16q0 97 -100 97q-85 0 -121 -75q0 -3 -22.5 -1t-45.5 17t-23 44 q0 50 58 85.5t163 35.5q230 0 230 -201v-289q0 -5 -16.5 -10.5t-36.5 -5.5q-71 0 -83 77q-56 -81 -169 -81q-76 0 -120 39t-44 102zM163 140q0 -27 20.5 -43t55.5 -16q52 0 85.5 31t33.5 79v28l-26 -1q-85 -2 -127 -20.5t-42 -57.5z" />
<glyph unicode="&#xe4;" horiz-adv-x="546" d="M372 544q-35 0 -54.5 19t-19.5 53t19 53t52 19q34 0 54 -19t20 -53t-19.5 -53t-51.5 -19zM169 544q-34 0 -54 19t-20 53t19.5 53t51.5 19q35 0 54.5 -19t19.5 -53t-19 -53t-52 -19zM28 130q0 84 76.5 124t237.5 42h16q0 97 -100 97q-85 0 -121 -75q0 -3 -22.5 -1 t-45.5 17t-23 44q0 50 58 85.5t163 35.5q230 0 230 -201v-289q0 -5 -16.5 -10.5t-36.5 -5.5q-71 0 -83 77q-56 -81 -169 -81q-76 0 -120 39t-44 102zM163 140q0 -27 20.5 -43t55.5 -16q52 0 85.5 31t33.5 79v28l-26 -1q-85 -2 -127 -20.5t-42 -57.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="546" d="M268 544q-56 0 -90.5 34.5t-34.5 87.5q0 52 36 87t90 35q56 0 90.5 -34.5t34.5 -87.5t-35.5 -87.5t-90.5 -34.5zM268 608q24 0 39.5 16.5t15.5 41.5t-16 42t-39 17t-38.5 -17t-15.5 -42q0 -24 16 -41t38 -17zM28 130q0 84 76.5 124t237.5 42h16q0 97 -100 97 q-85 0 -121 -75q0 -3 -22.5 -1t-45.5 17t-23 44q0 50 58 85.5t163 35.5q230 0 230 -201v-289q0 -5 -16.5 -10.5t-36.5 -5.5q-71 0 -83 77q-56 -81 -169 -81q-76 0 -120 39t-44 102zM163 140q0 -27 20.5 -43t55.5 -16q52 0 85.5 31t33.5 79v28l-26 -1q-85 -2 -127 -20.5 t-42 -57.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="869" d="M28 130q0 84 76.5 124t237.5 42h16q0 97 -100 97q-85 0 -121 -75q0 -3 -22.5 -1t-45.5 17t-23 44q0 49 55.5 85t154.5 36q122 0 173 -71q65 71 171 71q108 0 176 -68t68 -176v-10q-2 -39 -34 -39h-337q7 -54 45.5 -85t96.5 -31q96 0 137 72q1 2 18 -2t34 -20t17 -41 q0 -43 -58 -77.5t-153 -34.5q-156 0 -215 110q-26 -49 -80 -78.5t-122 -29.5q-75 0 -120 39t-45 102zM472 288h241q-3 52 -33.5 82.5t-80.5 30.5q-49 0 -84 -31.5t-43 -81.5zM163 140q0 -27 20.5 -43t55.5 -16q52 0 85.5 31t33.5 79v28l-26 -1q-85 -2 -127 -20.5t-42 -57.5z " />
<glyph unicode="&#xe7;" horiz-adv-x="514" d="M27 244q0 111 72.5 182.5t184.5 71.5q100 0 154 -43.5t54 -88.5q0 -26 -19 -42t-38 -20t-21 0q-33 81 -124 81q-58 0 -95.5 -40t-37.5 -101q0 -63 37.5 -103.5t95.5 -40.5q91 0 124 81q1 4 20.5 0.5t38.5 -20t19 -43.5q0 -43 -55 -87t-153 -44q-113 0 -185 72t-72 185z M215 -91q9 25 22.5 35t45.5 10h15q59 0 59 -42v-5q0 -22 -38 -54l-77 -76q-14 -8 -32 -8.5t-31 9t-8 23.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="550" d="M170 594q-49 30 -49 76q0 23 18.5 39t45.5 16q24 0 50 -17q30 -19 59.5 -53t44.5 -61t11 -34t-31.5 -7.5t-70 9.5t-78.5 32zM27 240q0 112 72 185.5t183 73.5q108 0 175.5 -68t67.5 -176v-10q-1 -20 -8 -29.5t-26 -9.5h-337q7 -54 45.5 -85t96.5 -31q96 0 138 72 q1 2 17.5 -2t33.5 -20t17 -41q0 -43 -57.5 -77.5t-153.5 -34.5q-120 0 -192 69t-72 184zM154 288h240q-2 51 -32.5 82t-81.5 31q-49 0 -83.5 -31.5t-42.5 -81.5z" />
<glyph unicode="&#xe9;" horiz-adv-x="550" d="M198 560q-4 7 11 34t44.5 61t59.5 53q26 17 50 17q27 0 45.5 -16t18.5 -39q0 -46 -49 -76q-36 -22 -78.5 -32t-70 -9.5t-31.5 7.5zM27 240q0 112 72 185.5t183 73.5q108 0 175.5 -68t67.5 -176v-10q-1 -20 -8 -29.5t-26 -9.5h-337q7 -54 45.5 -85t96.5 -31q96 0 138 72 q1 2 17.5 -2t33.5 -20t17 -41q0 -43 -57.5 -77.5t-153.5 -34.5q-120 0 -192 69t-72 184zM154 288h240q-2 51 -32.5 82t-81.5 31q-49 0 -83.5 -31.5t-42.5 -81.5z" />
<glyph unicode="&#xea;" horiz-adv-x="550" d="M175 544q-20 0 -33 12.5t-13 31.5q0 41 57.5 92t94.5 60q36 -9 93.5 -60t57.5 -92q0 -19 -12.5 -31.5t-32.5 -12.5q-49 0 -107 101q-56 -101 -105 -101zM27 240q0 112 72 185.5t183 73.5q108 0 175.5 -68t67.5 -176v-10q-1 -20 -8 -29.5t-26 -9.5h-337q7 -54 45.5 -85 t96.5 -31q96 0 138 72q1 2 17.5 -2t33.5 -20t17 -41q0 -43 -57.5 -77.5t-153.5 -34.5q-120 0 -192 69t-72 184zM154 288h240q-2 51 -32.5 82t-81.5 31q-49 0 -83.5 -31.5t-42.5 -81.5z" />
<glyph unicode="&#xeb;" horiz-adv-x="550" d="M384 544q-35 0 -54.5 19t-19.5 53t19 53t52 19q34 0 54 -19t20 -53t-19.5 -53t-51.5 -19zM181 544q-34 0 -54 19t-20 53t19.5 53t51.5 19q35 0 54.5 -19t19.5 -53t-19 -53t-52 -19zM27 240q0 112 72 185.5t183 73.5q108 0 175.5 -68t67.5 -176v-10q-1 -20 -8 -29.5 t-26 -9.5h-337q7 -54 45.5 -85t96.5 -31q96 0 138 72q1 2 17.5 -2t33.5 -20t17 -41q0 -43 -57.5 -77.5t-153.5 -34.5q-120 0 -192 69t-72 184zM154 288h240q-2 51 -32.5 82t-81.5 31q-49 0 -83.5 -31.5t-42.5 -81.5z" />
<glyph unicode="&#xec;" d="M24 594q-49 30 -49 76q0 23 18.5 39t45.5 16q24 0 50 -17q30 -19 59.5 -53t44.5 -61t11 -34t-31.5 -7.5t-70 9.5t-78.5 32zM57 63v362q0 68 63 68h15q63 0 63 -68v-362q0 -67 -62 -67h-15q-64 0 -64 67z" />
<glyph unicode="&#xed;" d="M53 560q-4 7 11 34t44.5 61t59.5 53q26 17 50 17q27 0 45.5 -16t18.5 -39q0 -46 -49 -76q-36 -22 -78.5 -32t-70 -9.5t-31.5 7.5zM57 63v362q0 68 63 68h15q63 0 63 -68v-362q0 -67 -62 -67h-15q-64 0 -64 67z" />
<glyph unicode="&#xee;" d="M23 544q-20 0 -33 12.5t-13 31.5q0 41 57.5 92t94.5 60q36 -9 93.5 -60t57.5 -92q0 -19 -12.5 -31.5t-32.5 -12.5q-49 0 -107 101q-56 -101 -105 -101zM57 63v362q0 68 63 68h15q63 0 63 -68v-362q0 -67 -62 -67h-15q-64 0 -64 67z" />
<glyph unicode="&#xef;" d="M232 544q-35 0 -54.5 19t-19.5 53t19 53t52 19q34 0 54 -19t20 -53t-19.5 -53t-51.5 -19zM29 544q-34 0 -54 19t-20 53t19.5 53t51.5 19q35 0 54.5 -19t19.5 -53t-19 -53t-52 -19zM57 63v362q0 68 63 68h15q63 0 63 -68v-362q0 -67 -62 -67h-15q-64 0 -64 67z" />
<glyph unicode="&#xf0;" horiz-adv-x="599" d="M31 226q0 102 65 166t169 64q52 0 94 -22.5t65 -56.5q-24 107 -111 176l-99 -53q-50 -26 -68 17l-2 5q-17 41 28 65l41 21q-46 19 -114 29q-6 1 0 21t33 40t70 20q75 0 149 -40l102 53q40 20 68 -17l3 -4q12 -16 4.5 -34t-32.5 -31l-59 -31q130 -125 130 -320 q0 -142 -76 -224.5t-201 -82.5q-118 0 -188.5 63.5t-70.5 175.5zM167 228q0 -59 34.5 -94.5t90.5 -35.5q61 0 96 40.5t35 106.5v41q-16 29 -51 49.5t-74 20.5q-62 0 -96.5 -34.5t-34.5 -93.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="581" d="M172 547q-21 0 -34 13.5t-13 34.5q0 44 26 73.5t63 29.5q66 0 140 -69q21 65 57 65q21 0 34 -13.5t13 -34.5q0 -44 -26 -73t-63 -29q-67 0 -140 68q-21 -65 -57 -65zM53 63v414q0 2 7 6t20 7t26 3q72 0 85 -80q58 86 161 86q91 0 136 -56t45 -146v-234q0 -67 -62 -67h-15 q-64 0 -64 67v213q0 49 -23.5 77t-66.5 28q-74 0 -108 -64v-254q0 -67 -62 -67h-15q-64 0 -64 67z" />
<glyph unicode="&#xf2;" horiz-adv-x="576" d="M175 594q-49 30 -49 76q0 23 18.5 39t45.5 16q24 0 50 -17q30 -19 59.5 -53t44.5 -61t11 -34t-31.5 -7.5t-70 9.5t-78.5 32zM27 244q0 113 73 184t189 71q115 0 187.5 -71.5t72.5 -185.5t-72.5 -184.5t-189.5 -70.5q-116 0 -188 71t-72 186zM151 244q0 -64 39 -105 t98 -41t97.5 41t38.5 103q0 64 -38.5 105.5t-97.5 41.5t-98 -41t-39 -104z" />
<glyph unicode="&#xf3;" horiz-adv-x="576" d="M222 560q-4 7 11 34t44.5 61t59.5 53q26 17 50 17q27 0 45.5 -16t18.5 -39q0 -46 -49 -76q-36 -22 -78.5 -32t-70 -9.5t-31.5 7.5zM27 244q0 113 73 184t189 71q115 0 187.5 -71.5t72.5 -185.5t-72.5 -184.5t-189.5 -70.5q-116 0 -188 71t-72 186zM151 244q0 -64 39 -105 t98 -41t97.5 41t38.5 103q0 64 -38.5 105.5t-97.5 41.5t-98 -41t-39 -104z" />
<glyph unicode="&#xf4;" horiz-adv-x="576" d="M183 544q-20 0 -33 12.5t-13 31.5q0 41 57.5 92t94.5 60q36 -9 93.5 -60t57.5 -92q0 -19 -12.5 -31.5t-32.5 -12.5q-49 0 -107 101q-56 -101 -105 -101zM27 244q0 113 73 184t189 71q115 0 187.5 -71.5t72.5 -185.5t-72.5 -184.5t-189.5 -70.5q-116 0 -188 71t-72 186z M151 244q0 -64 39 -105t98 -41t97.5 41t38.5 103q0 64 -38.5 105.5t-97.5 41.5t-98 -41t-39 -104z" />
<glyph unicode="&#xf5;" horiz-adv-x="576" d="M170 547q-21 0 -34 13.5t-13 34.5q0 44 26 73.5t63 29.5q66 0 140 -69q21 65 57 65q21 0 34 -13.5t13 -34.5q0 -44 -26 -73t-63 -29q-67 0 -140 68q-21 -65 -57 -65zM27 244q0 113 73 184t189 71q115 0 187.5 -71.5t72.5 -185.5t-72.5 -184.5t-189.5 -70.5 q-116 0 -188 71t-72 186zM151 244q0 -64 39 -105t98 -41t97.5 41t38.5 103q0 64 -38.5 105.5t-97.5 41.5t-98 -41t-39 -104z" />
<glyph unicode="&#xf6;" horiz-adv-x="576" d="M392 544q-35 0 -54.5 19t-19.5 53t19 53t52 19q34 0 54 -19t20 -53t-19.5 -53t-51.5 -19zM189 544q-34 0 -54 19t-20 53t19.5 53t51.5 19q35 0 54.5 -19t19.5 -53t-19 -53t-52 -19zM27 244q0 113 73 184t189 71q115 0 187.5 -71.5t72.5 -185.5t-72.5 -184.5t-189.5 -70.5 q-116 0 -188 71t-72 186zM151 244q0 -64 39 -105t98 -41t97.5 41t38.5 103q0 64 -38.5 105.5t-97.5 41.5t-98 -41t-39 -104z" />
<glyph unicode="&#xf7;" horiz-adv-x="600" d="M291 378q-58 0 -58 58v12q0 59 58 59h16q59 0 59 -59v-12q0 -58 -59 -58h-16zM118 204q-58 0 -58 52v7q0 51 58 51h363q58 0 58 -51v-7q0 -52 -58 -52h-363zM233 71v12q0 59 58 59h16q59 0 59 -59v-12q0 -58 -59 -58h-16q-58 0 -58 58z" />
<glyph unicode="&#xf8;" horiz-adv-x="576" d="M19 -12q0 21 23 45l43 48q-53 67 -53 161q0 109 74 183t185 74q74 0 139 -39l76 85q3 3 17 -1.5t27.5 -16.5t13.5 -27q0 -20 -22 -46l-46 -51q53 -69 53 -159q0 -109 -74 -183t-186 -74q-77 0 -137 37l-75 -82q-3 -3 -17 1.5t-27.5 17t-13.5 27.5zM172 165l187 205 q-31 22 -69 22q-60 0 -99.5 -42.5t-39.5 -106.5q0 -42 21 -78zM222 117q30 -22 68 -22q60 0 99.5 42t39.5 106q0 44 -22 77l-46 -51q-46 -51 -93 -101z" />
<glyph unicode="&#xf9;" horiz-adv-x="581" d="M176 594q-49 30 -49 76q0 23 18.5 39t45.5 16q24 0 50 -17q30 -19 59.5 -53t44.5 -61t11 -34t-31.5 -7.5t-70 9.5t-78.5 32zM49 189v236q0 68 62 68h15q64 0 64 -68v-215q0 -49 24 -77t68 -28q71 0 105 64v256q0 68 63 68h14q64 0 64 -68v-413q0 -3 -7 -6.5t-20 -6.5 t-26 -3q-71 0 -85 77q-58 -86 -160 -86q-91 0 -136 56t-45 146z" />
<glyph unicode="&#xfa;" horiz-adv-x="581" d="M228 560q-4 7 11 34t44.5 61t59.5 53q26 17 50 17q27 0 45.5 -16t18.5 -39q0 -46 -49 -76q-36 -22 -78.5 -32t-70 -9.5t-31.5 7.5zM49 189v236q0 68 62 68h15q64 0 64 -68v-215q0 -49 24 -77t68 -28q71 0 105 64v256q0 68 63 68h14q64 0 64 -68v-413q0 -3 -7 -6.5 t-20 -6.5t-26 -3q-71 0 -85 77q-58 -86 -160 -86q-91 0 -136 56t-45 146z" />
<glyph unicode="&#xfb;" horiz-adv-x="581" d="M185 544q-20 0 -33 12.5t-13 31.5q0 41 57.5 92t94.5 60q36 -9 93.5 -60t57.5 -92q0 -19 -12.5 -31.5t-32.5 -12.5q-49 0 -107 101q-56 -101 -105 -101zM49 189v236q0 68 62 68h15q64 0 64 -68v-215q0 -49 24 -77t68 -28q71 0 105 64v256q0 68 63 68h14q64 0 64 -68v-413 q0 -3 -7 -6.5t-20 -6.5t-26 -3q-71 0 -85 77q-58 -86 -160 -86q-91 0 -136 56t-45 146z" />
<glyph unicode="&#xfc;" horiz-adv-x="581" d="M395 544q-35 0 -54.5 19t-19.5 53t19 53t52 19q34 0 54 -19t20 -53t-19.5 -53t-51.5 -19zM192 544q-34 0 -54 19t-20 53t19.5 53t51.5 19q35 0 54.5 -19t19.5 -53t-19 -53t-52 -19zM49 189v236q0 68 62 68h15q64 0 64 -68v-215q0 -49 24 -77t68 -28q71 0 105 64v256 q0 68 63 68h14q64 0 64 -68v-413q0 -3 -7 -6.5t-20 -6.5t-26 -3q-71 0 -85 77q-58 -86 -160 -86q-91 0 -136 56t-45 146z" />
<glyph unicode="&#xfd;" horiz-adv-x="531" d="M200 555q-4 7 11 34t44.5 61t59.5 53q26 17 50 17q27 0 45.5 -16t18.5 -39q0 -46 -49 -76q-36 -22 -78.5 -32t-70 -9.5t-31.5 7.5zM179 63l-168 389q-2 4 8.5 14t29.5 19t39 9q57 0 79 -71l106 -279l94 278q22 72 78 72q18 0 37.5 -8.5t30 -18t8.5 -13.5l-222 -577 q-27 -69 -80 -69q-19 0 -38 8.5t-29 17.5t-8 13l81 184q-34 4 -46 32z" />
<glyph unicode="&#xfe;" horiz-adv-x="611" d="M53 -127v806q0 3 7 6.5t20 6.5t26 3q88 0 88 -113v-170q59 87 158 87q100 0 163 -70t63 -186t-62.5 -186t-165.5 -70q-50 0 -91 21.5t-65 54.5v-190q0 -68 -62 -68h-15q-64 0 -64 68zM194 216q0 -51 35 -84t87 -33q57 0 91.5 39.5t34.5 104.5q0 64 -34.5 103.5 t-90.5 39.5q-89 0 -123 -80v-90z" />
<glyph unicode="&#xff;" horiz-adv-x="531" d="M372 544q-35 0 -54.5 19t-19.5 53t19 53t52 19q34 0 54 -19t20 -53t-19.5 -53t-51.5 -19zM169 544q-34 0 -54 19t-20 53t19.5 53t51.5 19q35 0 54.5 -19t19.5 -53t-19 -53t-52 -19zM179 63l-168 389q-2 4 8.5 14t29.5 19t39 9q57 0 79 -71l106 -279l94 278q22 72 78 72 q18 0 37.5 -8.5t30 -18t8.5 -13.5l-222 -577q-27 -69 -80 -69q-19 0 -38 8.5t-29 17.5t-8 13l81 184q-34 4 -46 32z" />
<glyph unicode="&#x152;" horiz-adv-x="1030" d="M35 323q0 148 94 242.5t240 94.5q55 0 101 -13h456q68 0 68 -62v-5q0 -62 -68 -62h-292q54 -47 61 -128h154q68 0 68 -57v-4q0 -60 -68 -60h-153q-4 -87 -63 -141h299q67 0 67 -58v-6q0 -64 -67 -64h-462q-46 -13 -102 -13q-147 0 -240 94t-93 242zM182 324 q0 -92 52 -149t135 -57t135 57t52 148q0 92 -52.5 149.5t-135.5 57.5t-134.5 -56.5t-51.5 -149.5z" />
<glyph unicode="&#x153;" horiz-adv-x="947" d="M27 244q0 113 73 184t189 71q126 0 195 -94q69 94 195 94q108 0 175.5 -68t67.5 -176v-10q-1 -20 -8 -29.5t-26 -9.5h-337q7 -54 45.5 -85t96.5 -31q96 0 138 72q1 2 17.5 -2t33.5 -20t17 -41q0 -43 -57.5 -77.5t-153.5 -34.5q-142 0 -208 91q-69 -91 -193 -91 q-116 0 -188 71t-72 186zM551 288h240q-2 51 -32.5 82t-81.5 31q-49 0 -83.5 -31.5t-42.5 -81.5zM151 244q0 -64 39 -105t98 -41t97.5 41t38.5 103q0 64 -38.5 105.5t-97.5 41.5t-98 -41t-39 -104z" />
<glyph unicode="&#x178;" horiz-adv-x="603" d="M404 705q-35 0 -54.5 19t-19.5 53t19 53t52 19q34 0 54 -19t20 -53t-19.5 -53t-51.5 -19zM201 705q-34 0 -54 19t-20 53t19.5 53t51.5 19q35 0 54.5 -19t19.5 -53t-19 -53t-52 -19zM228 63v197l-226 334q-3 5 5 18t32 26t57 13q42 0 70 -51l136 -223l138 222q30 52 71 52 q30 0 53 -13t31.5 -26.5t5.5 -17.5l-225 -334v-197q0 -67 -65 -67h-20q-63 0 -63 67z" />
<glyph unicode="&#x2c6;" horiz-adv-x="500" d="M144 544q-20 0 -33 12.5t-13 31.5q0 41 57.5 92t94.5 60q36 -9 93.5 -60t57.5 -92q0 -19 -12.5 -31.5t-32.5 -12.5q-49 0 -107 101q-56 -101 -105 -101z" />
<glyph unicode="&#x2dc;" horiz-adv-x="500" d="M130 547q-21 0 -34 13.5t-13 34.5q0 44 26 73.5t63 29.5q66 0 140 -69q21 65 57 65q21 0 34 -13.5t13 -34.5q0 -44 -26 -73t-63 -29q-67 0 -140 68q-21 -65 -57 -65z" />
<glyph unicode="&#x2000;" horiz-adv-x="450" />
<glyph unicode="&#x2001;" horiz-adv-x="901" />
<glyph unicode="&#x2002;" horiz-adv-x="450" />
<glyph unicode="&#x2003;" horiz-adv-x="901" />
<glyph unicode="&#x2004;" horiz-adv-x="300" />
<glyph unicode="&#x2005;" horiz-adv-x="225" />
<glyph unicode="&#x2006;" horiz-adv-x="150" />
<glyph unicode="&#x2007;" horiz-adv-x="150" />
<glyph unicode="&#x2008;" horiz-adv-x="112" />
<glyph unicode="&#x2009;" horiz-adv-x="180" />
<glyph unicode="&#x200a;" horiz-adv-x="50" />
<glyph unicode="&#x2010;" horiz-adv-x="428" d="M94 180q-28 0 -43 15t-15 39v21q0 23 15 38.5t43 15.5h240q28 0 43 -15.5t15 -38.5v-21q0 -24 -15 -39t-43 -15h-240z" />
<glyph unicode="&#x2011;" horiz-adv-x="428" d="M94 180q-28 0 -43 15t-15 39v21q0 23 15 38.5t43 15.5h240q28 0 43 -15.5t15 -38.5v-21q0 -24 -15 -39t-43 -15h-240z" />
<glyph unicode="&#x2012;" horiz-adv-x="428" d="M94 180q-28 0 -43 15t-15 39v21q0 23 15 38.5t43 15.5h240q28 0 43 -15.5t15 -38.5v-21q0 -24 -15 -39t-43 -15h-240z" />
<glyph unicode="&#x2013;" horiz-adv-x="556" d="M94 185q-29 0 -43.5 15t-14.5 39v12q0 23 14.5 38t43.5 15h363q29 0 43.5 -15t14.5 -38v-12q0 -24 -14.5 -39t-43.5 -15h-363z" />
<glyph unicode="&#x2014;" horiz-adv-x="720" d="M94 185q-29 0 -43.5 15t-14.5 39v12q0 23 14.5 38t43.5 15h532q29 0 43.5 -15t14.5 -38v-12q0 -24 -14.5 -39t-43.5 -15h-532z" />
<glyph unicode="&#x2018;" horiz-adv-x="234" d="M86 394q-57 0 -57 62v5q0 36 33 76l79 109q18 21 48.5 9.5t23.5 -41.5l-45 -158q-9 -34 -22 -48t-45 -14h-15z" />
<glyph unicode="&#x2019;" horiz-adv-x="234" d="M93 407q-18 -21 -48 -9t-23 41l45 159q8 34 21 47.5t46 13.5h14q57 0 57 -61v-6q0 -38 -32 -75z" />
<glyph unicode="&#x201a;" horiz-adv-x="234" d="M16 -77l45 159q9 34 22 47.5t46 13.5h14q57 0 57 -61v-6q0 -39 -33 -75l-79 -110q-18 -20 -48.5 -8.5t-23.5 40.5z" />
<glyph unicode="&#x201c;" horiz-adv-x="435" d="M287 394q-56 0 -56 62v5q0 38 32 76l79 109q19 21 49 9.5t23 -41.5l-45 -158q-8 -34 -21.5 -48t-45.5 -14h-15zM86 394q-57 0 -57 62v5q0 37 33 76l79 109q18 21 48.5 9.5t23.5 -41.5l-46 -158q-8 -35 -21 -48.5t-45 -13.5h-15z" />
<glyph unicode="&#x201d;" horiz-adv-x="435" d="M222 439l46 159q9 34 21.5 47.5t44.5 13.5h16q56 0 56 -61v-6q0 -37 -32 -75l-80 -110q-18 -21 -48.5 -9t-23.5 41zM93 407q-18 -20 -48.5 -8.5t-23.5 40.5l45 159q8 34 21.5 47.5t45.5 13.5h15q56 0 56 -61v-6q0 -40 -32 -75z" />
<glyph unicode="&#x201e;" horiz-adv-x="435" d="M217 -77l45 159q9 34 21.5 47.5t45.5 13.5h15q56 0 56 -61v-6q0 -37 -32 -75l-79 -110q-18 -21 -49 -9t-23 41zM16 -77l45 159q8 34 21 47.5t46 13.5h14q57 0 57 -61v-6q0 -38 -32 -75l-80 -110q-18 -21 -48 -9t-23 41z" />
<glyph unicode="&#x2022;" horiz-adv-x="347" d="M174 116q-55 0 -91.5 37t-36.5 92t37 93t91 38t91 -38t37 -93q0 -53 -37.5 -91t-90.5 -38z" />
<glyph unicode="&#x2026;" horiz-adv-x="716" d="M520 59v14q0 66 67 66h17q68 0 68 -66v-14q0 -66 -68 -66h-17q-67 0 -67 66zM282 59v14q0 66 66 66h18q68 0 68 -66v-14q0 -66 -68 -66h-18q-66 0 -66 66zM43 59v14q0 66 66 66h18q68 0 68 -66v-14q0 -66 -68 -66h-18q-66 0 -66 66z" />
<glyph unicode="&#x202f;" horiz-adv-x="180" />
<glyph unicode="&#x2039;" horiz-adv-x="318" d="M229 64q-33 0 -81.5 42t-81.5 85.5t-33 53.5t33 53.5t81.5 85.5t81.5 42h2q24 0 37.5 -14t13.5 -36v-3q0 -46 -135 -128q135 -85 135 -127v-4q0 -22 -13.5 -36t-37.5 -14h-2z" />
<glyph unicode="&#x203a;" horiz-adv-x="318" d="M88 64q-24 0 -37.5 14t-13.5 36v4q0 44 135 127q-135 81 -135 128v3q0 22 13.5 36t37.5 14h1q33 0 82 -42t81.5 -85.5t32.5 -53.5t-32.5 -53.5t-81.5 -85.5t-82 -42h-1z" />
<glyph unicode="&#x205f;" horiz-adv-x="225" />
<glyph unicode="&#x20ac;" horiz-adv-x="631" d="M73 207q-50 0 -50 39v10q0 39 50 39h35v58h-35q-50 0 -50 40v10q0 39 50 39h44q23 102 95 160t177 58q96 0 155 -39t59 -91q0 -23 -16.5 -41.5t-33 -25t-19.5 -3.5q-29 41 -61.5 60.5t-83.5 19.5q-98 0 -131 -98h159q50 0 50 -39v-10q0 -40 -50 -40h-171v-58h171 q50 0 50 -39v-10q0 -39 -50 -39h-159q16 -47 51 -73.5t84 -26.5q51 0 83.5 21t58.5 64q3 4 20 -3t34 -25.5t17 -41.5q0 -49 -57 -92t-157 -43q-109 0 -181.5 58t-94.5 162h-43z" />
<glyph unicode="&#x2122;" horiz-adv-x="691" d="M368 331q-33 0 -33 39v240q0 41 36 41h29q28 0 39 -24l56 -136l55 136q11 24 38 24h29q36 0 36 -41v-240q0 -39 -34 -39h-12q-34 0 -34 39v160l-42 -117q-2 -7 -4 -10t-10 -5.5t-23 -2.5q-6 0 -11 0.5t-8 0.5t-6 1.5l-4 2t-2.5 3t-2 3t-1.5 3.5t-1 4l-44 115v-158 q0 -39 -32 -39h-14zM148 331q-35 0 -35 39v203h-62q-38 0 -38 37v3q0 35 38 35h207q39 0 39 -35v-3q0 -37 -39 -37h-61v-203q0 -39 -37 -39h-12z" />
<glyph unicode="&#x25fc;" horiz-adv-x="495" d="M0 495h495v-495h-495v495z" />
<hkern u1="&#x21;" u2="&#x153;" k="5" />
<hkern u1="&#x21;" u2="&#xe7;" k="5" />
<hkern u1="&#x21;" u2="&#xe6;" k="5" />
<hkern u1="&#x21;" u2="q" k="5" />
<hkern u1="&#x21;" u2="o" k="5" />
<hkern u1="&#x21;" u2="e" k="5" />
<hkern u1="&#x21;" u2="d" k="5" />
<hkern u1="&#x21;" u2="c" k="5" />
<hkern u1="&#x21;" u2="a" k="5" />
<hkern u1="&#x26;" u2="&#x178;" k="35" />
<hkern u1="&#x26;" u2="&#xdd;" k="35" />
<hkern u1="&#x26;" u2="&#xc6;" k="-29" />
<hkern u1="&#x26;" u2="&#xc5;" k="-29" />
<hkern u1="&#x26;" u2="&#xc4;" k="-29" />
<hkern u1="&#x26;" u2="&#xc3;" k="-29" />
<hkern u1="&#x26;" u2="&#xc2;" k="-29" />
<hkern u1="&#x26;" u2="&#xc1;" k="-29" />
<hkern u1="&#x26;" u2="&#xc0;" k="-29" />
<hkern u1="&#x26;" u2="Y" k="35" />
<hkern u1="&#x26;" u2="X" k="-20" />
<hkern u1="&#x26;" u2="W" k="25" />
<hkern u1="&#x26;" u2="V" k="34" />
<hkern u1="&#x26;" u2="T" k="40" />
<hkern u1="&#x26;" u2="J" k="-20" />
<hkern u1="&#x26;" u2="A" k="-25" />
<hkern u1="&#x28;" u2="&#x178;" k="-50" />
<hkern u1="&#x28;" u2="&#xdd;" k="-50" />
<hkern u1="&#x28;" u2="j" k="-150" />
<hkern u1="&#x28;" u2="g" k="-20" />
<hkern u1="&#x28;" u2="Y" k="-50" />
<hkern u1="&#x28;" u2="X" k="-40" />
<hkern u1="&#x28;" u2="W" k="-10" />
<hkern u1="&#x28;" u2="V" k="-30" />
<hkern u1="&#x28;" u2="J" k="9" />
<hkern u1="&#x28;" u2="&#x37;" k="-9" />
<hkern u1="&#x2a;" u2="&#xc6;" k="69" />
<hkern u1="&#x2a;" u2="&#xc5;" k="69" />
<hkern u1="&#x2a;" u2="&#xc4;" k="69" />
<hkern u1="&#x2a;" u2="&#xc3;" k="69" />
<hkern u1="&#x2a;" u2="&#xc2;" k="69" />
<hkern u1="&#x2a;" u2="&#xc1;" k="69" />
<hkern u1="&#x2a;" u2="&#xc0;" k="69" />
<hkern u1="&#x2a;" u2="T" k="-7" />
<hkern u1="&#x2a;" u2="J" k="32" />
<hkern u1="&#x2a;" u2="A" k="69" />
<hkern u1="&#x2c;" u2="&#x178;" k="50" />
<hkern u1="&#x2c;" u2="&#x153;" k="20" />
<hkern u1="&#x2c;" u2="&#x152;" k="36" />
<hkern u1="&#x2c;" u2="&#xe7;" k="20" />
<hkern u1="&#x2c;" u2="&#xe6;" k="20" />
<hkern u1="&#x2c;" u2="&#xdd;" k="50" />
<hkern u1="&#x2c;" u2="&#xdc;" k="10" />
<hkern u1="&#x2c;" u2="&#xdb;" k="10" />
<hkern u1="&#x2c;" u2="&#xda;" k="10" />
<hkern u1="&#x2c;" u2="&#xd9;" k="10" />
<hkern u1="&#x2c;" u2="&#xd8;" k="36" />
<hkern u1="&#x2c;" u2="&#xd6;" k="36" />
<hkern u1="&#x2c;" u2="&#xd5;" k="36" />
<hkern u1="&#x2c;" u2="&#xd4;" k="36" />
<hkern u1="&#x2c;" u2="&#xd3;" k="36" />
<hkern u1="&#x2c;" u2="&#xd2;" k="36" />
<hkern u1="&#x2c;" u2="&#xc6;" k="-30" />
<hkern u1="&#x2c;" u2="&#xc5;" k="-30" />
<hkern u1="&#x2c;" u2="&#xc4;" k="-30" />
<hkern u1="&#x2c;" u2="&#xc3;" k="-30" />
<hkern u1="&#x2c;" u2="&#xc2;" k="-30" />
<hkern u1="&#x2c;" u2="&#xc1;" k="-30" />
<hkern u1="&#x2c;" u2="&#xc0;" k="-30" />
<hkern u1="&#x2c;" u2="y" k="30" />
<hkern u1="&#x2c;" u2="w" k="10" />
<hkern u1="&#x2c;" u2="v" k="30" />
<hkern u1="&#x2c;" u2="u" k="20" />
<hkern u1="&#x2c;" u2="t" k="40" />
<hkern u1="&#x2c;" u2="r" k="20" />
<hkern u1="&#x2c;" u2="q" k="20" />
<hkern u1="&#x2c;" u2="p" k="20" />
<hkern u1="&#x2c;" u2="o" k="20" />
<hkern u1="&#x2c;" u2="n" k="20" />
<hkern u1="&#x2c;" u2="m" k="20" />
<hkern u1="&#x2c;" u2="f" k="18" />
<hkern u1="&#x2c;" u2="e" k="20" />
<hkern u1="&#x2c;" u2="d" k="20" />
<hkern u1="&#x2c;" u2="c" k="20" />
<hkern u1="&#x2c;" u2="a" k="20" />
<hkern u1="&#x2c;" u2="Y" k="50" />
<hkern u1="&#x2c;" u2="W" k="25" />
<hkern u1="&#x2c;" u2="V" k="65" />
<hkern u1="&#x2c;" u2="U" k="10" />
<hkern u1="&#x2c;" u2="T" k="74" />
<hkern u1="&#x2c;" u2="Q" k="36" />
<hkern u1="&#x2c;" u2="O" k="36" />
<hkern u1="&#x2c;" u2="G" k="36" />
<hkern u1="&#x2c;" u2="C" k="36" />
<hkern u1="&#x2c;" u2="A" k="-30" />
<hkern u1="&#x2c;" u2="&#x39;" k="6" />
<hkern u1="&#x2c;" u2="&#x38;" k="15" />
<hkern u1="&#x2c;" u2="&#x37;" k="5" />
<hkern u1="&#x2c;" u2="&#x36;" k="26" />
<hkern u1="&#x2c;" u2="&#x35;" k="-9" />
<hkern u1="&#x2c;" u2="&#x34;" k="28" />
<hkern u1="&#x2c;" u2="&#x33;" k="-5" />
<hkern u1="&#x2c;" u2="&#x32;" k="-5" />
<hkern u1="&#x2c;" u2="&#x31;" k="76" />
<hkern u1="&#x2c;" u2="&#x30;" k="27" />
<hkern u1="&#x2d;" u2="&#x178;" k="50" />
<hkern u1="&#x2d;" u2="&#x153;" k="10" />
<hkern u1="&#x2d;" u2="&#xe7;" k="10" />
<hkern u1="&#x2d;" u2="&#xe6;" k="10" />
<hkern u1="&#x2d;" u2="&#xdd;" k="50" />
<hkern u1="&#x2d;" u2="&#xc6;" k="-10" />
<hkern u1="&#x2d;" u2="&#xc5;" k="-10" />
<hkern u1="&#x2d;" u2="&#xc4;" k="-10" />
<hkern u1="&#x2d;" u2="&#xc3;" k="-10" />
<hkern u1="&#x2d;" u2="&#xc2;" k="-10" />
<hkern u1="&#x2d;" u2="&#xc1;" k="-10" />
<hkern u1="&#x2d;" u2="&#xc0;" k="-10" />
<hkern u1="&#x2d;" u2="z" k="21" />
<hkern u1="&#x2d;" u2="y" k="10" />
<hkern u1="&#x2d;" u2="x" k="30" />
<hkern u1="&#x2d;" u2="v" k="10" />
<hkern u1="&#x2d;" u2="q" k="10" />
<hkern u1="&#x2d;" u2="o" k="10" />
<hkern u1="&#x2d;" u2="e" k="10" />
<hkern u1="&#x2d;" u2="d" k="10" />
<hkern u1="&#x2d;" u2="c" k="10" />
<hkern u1="&#x2d;" u2="a" k="10" />
<hkern u1="&#x2d;" u2="Z" k="10" />
<hkern u1="&#x2d;" u2="Y" k="50" />
<hkern u1="&#x2d;" u2="X" k="30" />
<hkern u1="&#x2d;" u2="W" k="40" />
<hkern u1="&#x2d;" u2="V" k="50" />
<hkern u1="&#x2d;" u2="T" k="61" />
<hkern u1="&#x2d;" u2="A" k="-10" />
<hkern u1="&#x2d;" u2="&#x37;" k="30" />
<hkern u1="&#x2d;" u2="&#x31;" k="20" />
<hkern u1="&#x2e;" u2="&#x178;" k="50" />
<hkern u1="&#x2e;" u2="&#x153;" k="20" />
<hkern u1="&#x2e;" u2="&#x152;" k="36" />
<hkern u1="&#x2e;" u2="&#xe7;" k="20" />
<hkern u1="&#x2e;" u2="&#xe6;" k="20" />
<hkern u1="&#x2e;" u2="&#xdd;" k="50" />
<hkern u1="&#x2e;" u2="&#xdc;" k="10" />
<hkern u1="&#x2e;" u2="&#xdb;" k="10" />
<hkern u1="&#x2e;" u2="&#xda;" k="10" />
<hkern u1="&#x2e;" u2="&#xd9;" k="10" />
<hkern u1="&#x2e;" u2="&#xd8;" k="36" />
<hkern u1="&#x2e;" u2="&#xd6;" k="36" />
<hkern u1="&#x2e;" u2="&#xd5;" k="36" />
<hkern u1="&#x2e;" u2="&#xd4;" k="36" />
<hkern u1="&#x2e;" u2="&#xd3;" k="36" />
<hkern u1="&#x2e;" u2="&#xd2;" k="36" />
<hkern u1="&#x2e;" u2="&#xc6;" k="-30" />
<hkern u1="&#x2e;" u2="&#xc5;" k="-30" />
<hkern u1="&#x2e;" u2="&#xc4;" k="-30" />
<hkern u1="&#x2e;" u2="&#xc3;" k="-30" />
<hkern u1="&#x2e;" u2="&#xc2;" k="-30" />
<hkern u1="&#x2e;" u2="&#xc1;" k="-30" />
<hkern u1="&#x2e;" u2="&#xc0;" k="-30" />
<hkern u1="&#x2e;" u2="y" k="30" />
<hkern u1="&#x2e;" u2="w" k="10" />
<hkern u1="&#x2e;" u2="v" k="30" />
<hkern u1="&#x2e;" u2="u" k="20" />
<hkern u1="&#x2e;" u2="t" k="40" />
<hkern u1="&#x2e;" u2="r" k="20" />
<hkern u1="&#x2e;" u2="q" k="20" />
<hkern u1="&#x2e;" u2="p" k="20" />
<hkern u1="&#x2e;" u2="o" k="20" />
<hkern u1="&#x2e;" u2="n" k="20" />
<hkern u1="&#x2e;" u2="m" k="20" />
<hkern u1="&#x2e;" u2="f" k="18" />
<hkern u1="&#x2e;" u2="e" k="20" />
<hkern u1="&#x2e;" u2="d" k="20" />
<hkern u1="&#x2e;" u2="c" k="20" />
<hkern u1="&#x2e;" u2="a" k="20" />
<hkern u1="&#x2e;" u2="Y" k="50" />
<hkern u1="&#x2e;" u2="W" k="25" />
<hkern u1="&#x2e;" u2="V" k="65" />
<hkern u1="&#x2e;" u2="U" k="10" />
<hkern u1="&#x2e;" u2="T" k="74" />
<hkern u1="&#x2e;" u2="Q" k="36" />
<hkern u1="&#x2e;" u2="O" k="36" />
<hkern u1="&#x2e;" u2="G" k="36" />
<hkern u1="&#x2e;" u2="C" k="36" />
<hkern u1="&#x2e;" u2="A" k="-30" />
<hkern u1="&#x2e;" u2="&#x39;" k="6" />
<hkern u1="&#x2e;" u2="&#x38;" k="15" />
<hkern u1="&#x2e;" u2="&#x37;" k="5" />
<hkern u1="&#x2e;" u2="&#x36;" k="26" />
<hkern u1="&#x2e;" u2="&#x35;" k="-9" />
<hkern u1="&#x2e;" u2="&#x34;" k="28" />
<hkern u1="&#x2e;" u2="&#x33;" k="-5" />
<hkern u1="&#x2e;" u2="&#x32;" k="-5" />
<hkern u1="&#x2e;" u2="&#x31;" k="76" />
<hkern u1="&#x2e;" u2="&#x30;" k="27" />
<hkern u1="&#x2f;" u2="&#x153;" k="10" />
<hkern u1="&#x2f;" u2="&#x152;" k="-6" />
<hkern u1="&#x2f;" u2="&#xe7;" k="10" />
<hkern u1="&#x2f;" u2="&#xe6;" k="10" />
<hkern u1="&#x2f;" u2="&#xde;" k="-5" />
<hkern u1="&#x2f;" u2="&#xd8;" k="-6" />
<hkern u1="&#x2f;" u2="&#xd6;" k="-6" />
<hkern u1="&#x2f;" u2="&#xd5;" k="-6" />
<hkern u1="&#x2f;" u2="&#xd4;" k="-6" />
<hkern u1="&#x2f;" u2="&#xd3;" k="-6" />
<hkern u1="&#x2f;" u2="&#xd2;" k="-6" />
<hkern u1="&#x2f;" u2="&#xd1;" k="-5" />
<hkern u1="&#x2f;" u2="&#xd0;" k="-5" />
<hkern u1="&#x2f;" u2="&#xcf;" k="-5" />
<hkern u1="&#x2f;" u2="&#xce;" k="-5" />
<hkern u1="&#x2f;" u2="&#xcd;" k="-5" />
<hkern u1="&#x2f;" u2="&#xcc;" k="-5" />
<hkern u1="&#x2f;" u2="&#xcb;" k="-5" />
<hkern u1="&#x2f;" u2="&#xca;" k="-5" />
<hkern u1="&#x2f;" u2="&#xc9;" k="-5" />
<hkern u1="&#x2f;" u2="&#xc8;" k="-5" />
<hkern u1="&#x2f;" u2="q" k="10" />
<hkern u1="&#x2f;" u2="o" k="10" />
<hkern u1="&#x2f;" u2="g" k="15" />
<hkern u1="&#x2f;" u2="e" k="10" />
<hkern u1="&#x2f;" u2="d" k="10" />
<hkern u1="&#x2f;" u2="c" k="10" />
<hkern u1="&#x2f;" u2="a" k="10" />
<hkern u1="&#x2f;" u2="R" k="-5" />
<hkern u1="&#x2f;" u2="Q" k="-6" />
<hkern u1="&#x2f;" u2="P" k="-5" />
<hkern u1="&#x2f;" u2="O" k="-6" />
<hkern u1="&#x2f;" u2="N" k="-5" />
<hkern u1="&#x2f;" u2="M" k="-5" />
<hkern u1="&#x2f;" u2="L" k="-5" />
<hkern u1="&#x2f;" u2="K" k="-5" />
<hkern u1="&#x2f;" u2="J" k="23" />
<hkern u1="&#x2f;" u2="I" k="-5" />
<hkern u1="&#x2f;" u2="H" k="-5" />
<hkern u1="&#x2f;" u2="G" k="-6" />
<hkern u1="&#x2f;" u2="F" k="-5" />
<hkern u1="&#x2f;" u2="E" k="-5" />
<hkern u1="&#x2f;" u2="D" k="-5" />
<hkern u1="&#x2f;" u2="C" k="-6" />
<hkern u1="&#x2f;" u2="B" k="-5" />
<hkern u1="&#x2f;" u2="&#x37;" k="-18" />
<hkern u1="&#x2f;" u2="&#x35;" k="-9" />
<hkern u1="&#x2f;" u2="&#x34;" k="11" />
<hkern u1="&#x2f;" u2="&#x33;" k="-9" />
<hkern u1="&#x2f;" u2="&#x32;" k="-7" />
<hkern u1="&#x2f;" u2="&#x31;" k="-20" />
<hkern u1="&#x30;" u2="&#x2026;" k="27" />
<hkern u1="&#x30;" u2="&#x201e;" k="27" />
<hkern u1="&#x30;" u2="&#x201a;" k="27" />
<hkern u1="&#x30;" u2="&#x37;" k="20" />
<hkern u1="&#x30;" u2="&#x2e;" k="27" />
<hkern u1="&#x30;" u2="&#x2c;" k="27" />
<hkern u1="&#x31;" u2="&#x2026;" k="-5" />
<hkern u1="&#x31;" u2="&#x201e;" k="-5" />
<hkern u1="&#x31;" u2="&#x201a;" k="-5" />
<hkern u1="&#x31;" u2="&#x37;" k="-5" />
<hkern u1="&#x31;" u2="&#x2e;" k="-5" />
<hkern u1="&#x31;" u2="&#x2c;" k="-5" />
<hkern u1="&#x32;" u2="&#x2014;" k="10" />
<hkern u1="&#x32;" u2="&#x2013;" k="10" />
<hkern u1="&#x32;" u2="&#x34;" k="12" />
<hkern u1="&#x32;" u2="&#x2d;" k="10" />
<hkern u1="&#x33;" u2="&#x2026;" k="11" />
<hkern u1="&#x33;" u2="&#x201e;" k="11" />
<hkern u1="&#x33;" u2="&#x201a;" k="11" />
<hkern u1="&#x33;" u2="&#x37;" k="9" />
<hkern u1="&#x33;" u2="&#x2f;" k="9" />
<hkern u1="&#x33;" u2="&#x2e;" k="11" />
<hkern u1="&#x33;" u2="&#x2c;" k="11" />
<hkern u1="&#x34;" u2="&#x2122;" k="46" />
<hkern u1="&#x34;" u2="&#x2026;" k="-9" />
<hkern u1="&#x34;" u2="&#x201e;" k="-9" />
<hkern u1="&#x34;" u2="&#x201d;" k="14" />
<hkern u1="&#x34;" u2="&#x201c;" k="18" />
<hkern u1="&#x34;" u2="&#x201a;" k="-9" />
<hkern u1="&#x34;" u2="&#x2019;" k="14" />
<hkern u1="&#x34;" u2="&#x2018;" k="18" />
<hkern u1="&#x34;" u2="&#xb0;" k="22" />
<hkern u1="&#x34;" u2="&#x37;" k="15" />
<hkern u1="&#x34;" u2="&#x31;" k="14" />
<hkern u1="&#x34;" u2="&#x2f;" k="5" />
<hkern u1="&#x34;" u2="&#x2e;" k="-9" />
<hkern u1="&#x34;" u2="&#x2c;" k="-9" />
<hkern u1="&#x35;" u2="&#x2026;" k="10" />
<hkern u1="&#x35;" u2="&#x201e;" k="10" />
<hkern u1="&#x35;" u2="&#x201a;" k="10" />
<hkern u1="&#x35;" u2="&#x37;" k="1" />
<hkern u1="&#x35;" u2="&#x2f;" k="5" />
<hkern u1="&#x35;" u2="&#x2e;" k="10" />
<hkern u1="&#x35;" u2="&#x2c;" k="10" />
<hkern u1="&#x36;" u2="&#x2026;" k="6" />
<hkern u1="&#x36;" u2="&#x201e;" k="6" />
<hkern u1="&#x36;" u2="&#x201a;" k="6" />
<hkern u1="&#x36;" u2="&#x37;" k="10" />
<hkern u1="&#x36;" u2="&#x2e;" k="6" />
<hkern u1="&#x36;" u2="&#x2c;" k="6" />
<hkern u1="&#x37;" u2="&#x2026;" k="80" />
<hkern u1="&#x37;" u2="&#x201e;" k="80" />
<hkern u1="&#x37;" u2="&#x201a;" k="80" />
<hkern u1="&#x37;" u2="&#x2014;" k="40" />
<hkern u1="&#x37;" u2="&#x2013;" k="40" />
<hkern u1="&#x37;" u2="&#xa2;" k="40" />
<hkern u1="&#x37;" u2="&#x7d;" k="-5" />
<hkern u1="&#x37;" u2="]" k="-5" />
<hkern u1="&#x37;" u2="&#x3f;" k="-5" />
<hkern u1="&#x37;" u2="&#x3b;" k="20" />
<hkern u1="&#x37;" u2="&#x3a;" k="20" />
<hkern u1="&#x37;" u2="&#x39;" k="-10" />
<hkern u1="&#x37;" u2="&#x38;" k="2" />
<hkern u1="&#x37;" u2="&#x36;" k="2" />
<hkern u1="&#x37;" u2="&#x35;" k="-15" />
<hkern u1="&#x37;" u2="&#x34;" k="38" />
<hkern u1="&#x37;" u2="&#x33;" k="-18" />
<hkern u1="&#x37;" u2="&#x31;" k="-20" />
<hkern u1="&#x37;" u2="&#x30;" k="8" />
<hkern u1="&#x37;" u2="&#x2f;" k="41" />
<hkern u1="&#x37;" u2="&#x2e;" k="80" />
<hkern u1="&#x37;" u2="&#x2d;" k="40" />
<hkern u1="&#x37;" u2="&#x2c;" k="80" />
<hkern u1="&#x37;" u2="&#x29;" k="-5" />
<hkern u1="&#x38;" u2="&#x2026;" k="15" />
<hkern u1="&#x38;" u2="&#x201e;" k="15" />
<hkern u1="&#x38;" u2="&#x201a;" k="15" />
<hkern u1="&#x38;" u2="&#x37;" k="15" />
<hkern u1="&#x38;" u2="&#x2e;" k="15" />
<hkern u1="&#x38;" u2="&#x2c;" k="15" />
<hkern u1="&#x39;" u2="&#x2026;" k="26" />
<hkern u1="&#x39;" u2="&#x201e;" k="26" />
<hkern u1="&#x39;" u2="&#x201a;" k="26" />
<hkern u1="&#x39;" u2="&#x37;" k="29" />
<hkern u1="&#x39;" u2="&#x2e;" k="26" />
<hkern u1="&#x39;" u2="&#x2c;" k="26" />
<hkern u1="&#x3a;" u2="&#x178;" k="30" />
<hkern u1="&#x3a;" u2="&#x153;" k="10" />
<hkern u1="&#x3a;" u2="&#xe7;" k="10" />
<hkern u1="&#x3a;" u2="&#xe6;" k="10" />
<hkern u1="&#x3a;" u2="&#xdd;" k="30" />
<hkern u1="&#x3a;" u2="q" k="10" />
<hkern u1="&#x3a;" u2="o" k="10" />
<hkern u1="&#x3a;" u2="e" k="10" />
<hkern u1="&#x3a;" u2="d" k="10" />
<hkern u1="&#x3a;" u2="c" k="10" />
<hkern u1="&#x3a;" u2="a" k="10" />
<hkern u1="&#x3a;" u2="Y" k="30" />
<hkern u1="&#x3a;" u2="W" k="20" />
<hkern u1="&#x3a;" u2="V" k="20" />
<hkern u1="&#x3a;" u2="T" k="50" />
<hkern u1="&#x3a;" u2="&#x37;" k="20" />
<hkern u1="&#x3a;" u2="&#x31;" k="30" />
<hkern u1="&#x3b;" u2="&#x178;" k="30" />
<hkern u1="&#x3b;" u2="&#x153;" k="10" />
<hkern u1="&#x3b;" u2="&#xe7;" k="10" />
<hkern u1="&#x3b;" u2="&#xe6;" k="10" />
<hkern u1="&#x3b;" u2="&#xdd;" k="30" />
<hkern u1="&#x3b;" u2="q" k="10" />
<hkern u1="&#x3b;" u2="o" k="10" />
<hkern u1="&#x3b;" u2="e" k="10" />
<hkern u1="&#x3b;" u2="d" k="10" />
<hkern u1="&#x3b;" u2="c" k="10" />
<hkern u1="&#x3b;" u2="a" k="10" />
<hkern u1="&#x3b;" u2="Y" k="30" />
<hkern u1="&#x3b;" u2="W" k="20" />
<hkern u1="&#x3b;" u2="V" k="20" />
<hkern u1="&#x3b;" u2="T" k="50" />
<hkern u1="&#x3b;" u2="&#x37;" k="20" />
<hkern u1="&#x3b;" u2="&#x31;" k="30" />
<hkern u1="&#x3e;" u2="&#x37;" k="33" />
<hkern u1="&#x40;" u2="&#x178;" k="30" />
<hkern u1="&#x40;" u2="&#xdd;" k="30" />
<hkern u1="&#x40;" u2="&#xc6;" k="10" />
<hkern u1="&#x40;" u2="&#xc5;" k="10" />
<hkern u1="&#x40;" u2="&#xc4;" k="10" />
<hkern u1="&#x40;" u2="&#xc3;" k="10" />
<hkern u1="&#x40;" u2="&#xc2;" k="10" />
<hkern u1="&#x40;" u2="&#xc1;" k="10" />
<hkern u1="&#x40;" u2="&#xc0;" k="10" />
<hkern u1="&#x40;" u2="g" k="-5" />
<hkern u1="&#x40;" u2="Y" k="30" />
<hkern u1="&#x40;" u2="X" k="14" />
<hkern u1="&#x40;" u2="W" k="20" />
<hkern u1="&#x40;" u2="V" k="5" />
<hkern u1="&#x40;" u2="T" k="30" />
<hkern u1="&#x40;" u2="A" k="10" />
<hkern u1="&#x40;" u2="&#x33;" k="5" />
<hkern u1="A" u2="&#x2122;" k="32" />
<hkern u1="A" u2="&#x2026;" k="-14" />
<hkern u1="A" u2="&#x201e;" k="-14" />
<hkern u1="A" u2="&#x201d;" k="14" />
<hkern u1="A" u2="&#x201c;" k="27" />
<hkern u1="A" u2="&#x201a;" k="-14" />
<hkern u1="A" u2="&#x2019;" k="14" />
<hkern u1="A" u2="&#x2018;" k="27" />
<hkern u1="A" u2="&#x2014;" k="-5" />
<hkern u1="A" u2="&#x2013;" k="-5" />
<hkern u1="A" u2="&#x178;" k="25" />
<hkern u1="A" u2="&#x153;" k="2" />
<hkern u1="A" u2="&#x152;" k="9" />
<hkern u1="A" u2="&#xe7;" k="2" />
<hkern u1="A" u2="&#xe6;" k="2" />
<hkern u1="A" u2="&#xdd;" k="25" />
<hkern u1="A" u2="&#xd8;" k="9" />
<hkern u1="A" u2="&#xd6;" k="9" />
<hkern u1="A" u2="&#xd5;" k="9" />
<hkern u1="A" u2="&#xd4;" k="9" />
<hkern u1="A" u2="&#xd3;" k="9" />
<hkern u1="A" u2="&#xd2;" k="9" />
<hkern u1="A" u2="&#xc6;" k="-5" />
<hkern u1="A" u2="&#xc5;" k="-5" />
<hkern u1="A" u2="&#xc4;" k="-5" />
<hkern u1="A" u2="&#xc3;" k="-5" />
<hkern u1="A" u2="&#xc2;" k="-5" />
<hkern u1="A" u2="&#xc1;" k="-5" />
<hkern u1="A" u2="&#xc0;" k="-5" />
<hkern u1="A" u2="&#xae;" k="5" />
<hkern u1="A" u2="&#xa9;" k="5" />
<hkern u1="A" u2="z" k="-5" />
<hkern u1="A" u2="y" k="32" />
<hkern u1="A" u2="x" k="-5" />
<hkern u1="A" u2="w" k="23" />
<hkern u1="A" u2="v" k="32" />
<hkern u1="A" u2="u" k="2" />
<hkern u1="A" u2="t" k="2" />
<hkern u1="A" u2="s" k="-7" />
<hkern u1="A" u2="r" k="2" />
<hkern u1="A" u2="q" k="2" />
<hkern u1="A" u2="p" k="2" />
<hkern u1="A" u2="o" k="2" />
<hkern u1="A" u2="n" k="2" />
<hkern u1="A" u2="m" k="2" />
<hkern u1="A" u2="g" k="7" />
<hkern u1="A" u2="f" k="18" />
<hkern u1="A" u2="e" k="2" />
<hkern u1="A" u2="d" k="2" />
<hkern u1="A" u2="c" k="2" />
<hkern u1="A" u2="a" k="-5" />
<hkern u1="A" u2="Y" k="25" />
<hkern u1="A" u2="X" k="-5" />
<hkern u1="A" u2="W" k="29" />
<hkern u1="A" u2="V" k="36" />
<hkern u1="A" u2="T" k="25" />
<hkern u1="A" u2="S" k="-2" />
<hkern u1="A" u2="Q" k="9" />
<hkern u1="A" u2="O" k="9" />
<hkern u1="A" u2="J" k="-5" />
<hkern u1="A" u2="G" k="9" />
<hkern u1="A" u2="C" k="9" />
<hkern u1="A" u2="A" k="-5" />
<hkern u1="A" u2="&#x40;" k="5" />
<hkern u1="A" u2="&#x3f;" k="14" />
<hkern u1="A" u2="&#x2e;" k="-14" />
<hkern u1="A" u2="&#x2d;" k="-5" />
<hkern u1="A" u2="&#x2c;" k="-14" />
<hkern u1="A" u2="&#x2a;" k="36" />
<hkern u1="B" u2="&#x153;" k="-5" />
<hkern u1="B" u2="&#xe7;" k="-5" />
<hkern u1="B" u2="&#xe6;" k="-5" />
<hkern u1="B" u2="y" k="7" />
<hkern u1="B" u2="w" k="5" />
<hkern u1="B" u2="v" k="7" />
<hkern u1="B" u2="q" k="-5" />
<hkern u1="B" u2="o" k="-5" />
<hkern u1="B" u2="e" k="-5" />
<hkern u1="B" u2="d" k="-5" />
<hkern u1="B" u2="c" k="-5" />
<hkern u1="B" u2="a" k="-5" />
<hkern u1="B" u2="W" k="20" />
<hkern u1="B" u2="V" k="5" />
<hkern u1="C" u2="&#x178;" k="-20" />
<hkern u1="C" u2="&#x153;" k="-3" />
<hkern u1="C" u2="&#x152;" k="5" />
<hkern u1="C" u2="&#xe7;" k="-3" />
<hkern u1="C" u2="&#xe6;" k="-3" />
<hkern u1="C" u2="&#xdd;" k="-20" />
<hkern u1="C" u2="&#xd8;" k="5" />
<hkern u1="C" u2="&#xd6;" k="5" />
<hkern u1="C" u2="&#xd5;" k="5" />
<hkern u1="C" u2="&#xd4;" k="5" />
<hkern u1="C" u2="&#xd3;" k="5" />
<hkern u1="C" u2="&#xd2;" k="5" />
<hkern u1="C" u2="z" k="10" />
<hkern u1="C" u2="y" k="5" />
<hkern u1="C" u2="w" k="5" />
<hkern u1="C" u2="v" k="5" />
<hkern u1="C" u2="t" k="5" />
<hkern u1="C" u2="q" k="-3" />
<hkern u1="C" u2="o" k="-3" />
<hkern u1="C" u2="e" k="-3" />
<hkern u1="C" u2="d" k="-3" />
<hkern u1="C" u2="c" k="-3" />
<hkern u1="C" u2="a" k="-3" />
<hkern u1="C" u2="Y" k="-20" />
<hkern u1="C" u2="X" k="-10" />
<hkern u1="C" u2="V" k="-20" />
<hkern u1="C" u2="Q" k="5" />
<hkern u1="C" u2="O" k="5" />
<hkern u1="C" u2="G" k="5" />
<hkern u1="C" u2="C" k="5" />
<hkern u1="D" u2="&#x2026;" k="36" />
<hkern u1="D" u2="&#x201e;" k="36" />
<hkern u1="D" u2="&#x201c;" k="20" />
<hkern u1="D" u2="&#x201a;" k="36" />
<hkern u1="D" u2="&#x2018;" k="20" />
<hkern u1="D" u2="&#x178;" k="21" />
<hkern u1="D" u2="&#x153;" k="3" />
<hkern u1="D" u2="&#xe7;" k="3" />
<hkern u1="D" u2="&#xe6;" k="3" />
<hkern u1="D" u2="&#xdd;" k="21" />
<hkern u1="D" u2="&#xc6;" k="20" />
<hkern u1="D" u2="&#xc5;" k="20" />
<hkern u1="D" u2="&#xc4;" k="20" />
<hkern u1="D" u2="&#xc3;" k="20" />
<hkern u1="D" u2="&#xc2;" k="20" />
<hkern u1="D" u2="&#xc1;" k="20" />
<hkern u1="D" u2="&#xc0;" k="20" />
<hkern u1="D" u2="z" k="10" />
<hkern u1="D" u2="x" k="10" />
<hkern u1="D" u2="u" k="3" />
<hkern u1="D" u2="r" k="3" />
<hkern u1="D" u2="q" k="3" />
<hkern u1="D" u2="p" k="3" />
<hkern u1="D" u2="o" k="3" />
<hkern u1="D" u2="n" k="3" />
<hkern u1="D" u2="m" k="3" />
<hkern u1="D" u2="l" k="6" />
<hkern u1="D" u2="k" k="6" />
<hkern u1="D" u2="h" k="6" />
<hkern u1="D" u2="e" k="3" />
<hkern u1="D" u2="d" k="3" />
<hkern u1="D" u2="c" k="3" />
<hkern u1="D" u2="b" k="6" />
<hkern u1="D" u2="a" k="3" />
<hkern u1="D" u2="Z" k="29" />
<hkern u1="D" u2="Y" k="21" />
<hkern u1="D" u2="X" k="27" />
<hkern u1="D" u2="W" k="36" />
<hkern u1="D" u2="V" k="20" />
<hkern u1="D" u2="T" k="23" />
<hkern u1="D" u2="J" k="32" />
<hkern u1="D" u2="A" k="20" />
<hkern u1="D" u2="&#x3f;" k="20" />
<hkern u1="D" u2="&#x2f;" k="50" />
<hkern u1="D" u2="&#x2e;" k="36" />
<hkern u1="D" u2="&#x2c;" k="36" />
<hkern u1="E" u2="&#x2039;" k="9" />
<hkern u1="E" u2="&#x153;" k="23" />
<hkern u1="E" u2="&#x152;" k="11" />
<hkern u1="E" u2="&#xfe;" k="2" />
<hkern u1="E" u2="&#xe7;" k="23" />
<hkern u1="E" u2="&#xe6;" k="23" />
<hkern u1="E" u2="&#xd8;" k="11" />
<hkern u1="E" u2="&#xd6;" k="11" />
<hkern u1="E" u2="&#xd5;" k="11" />
<hkern u1="E" u2="&#xd4;" k="11" />
<hkern u1="E" u2="&#xd3;" k="11" />
<hkern u1="E" u2="&#xd2;" k="11" />
<hkern u1="E" u2="&#xae;" k="20" />
<hkern u1="E" u2="&#xab;" k="9" />
<hkern u1="E" u2="&#xa9;" k="20" />
<hkern u1="E" u2="y" k="20" />
<hkern u1="E" u2="v" k="20" />
<hkern u1="E" u2="u" k="5" />
<hkern u1="E" u2="r" k="5" />
<hkern u1="E" u2="q" k="23" />
<hkern u1="E" u2="p" k="5" />
<hkern u1="E" u2="o" k="23" />
<hkern u1="E" u2="n" k="5" />
<hkern u1="E" u2="m" k="5" />
<hkern u1="E" u2="l" k="2" />
<hkern u1="E" u2="k" k="2" />
<hkern u1="E" u2="h" k="2" />
<hkern u1="E" u2="g" k="12" />
<hkern u1="E" u2="f" k="10" />
<hkern u1="E" u2="e" k="23" />
<hkern u1="E" u2="d" k="23" />
<hkern u1="E" u2="c" k="23" />
<hkern u1="E" u2="b" k="2" />
<hkern u1="E" u2="a" k="14" />
<hkern u1="E" u2="W" k="2" />
<hkern u1="E" u2="V" k="-5" />
<hkern u1="E" u2="T" k="-11" />
<hkern u1="E" u2="Q" k="11" />
<hkern u1="E" u2="O" k="11" />
<hkern u1="E" u2="J" k="-2" />
<hkern u1="E" u2="G" k="11" />
<hkern u1="E" u2="C" k="11" />
<hkern u1="E" u2="&#x40;" k="20" />
<hkern u1="F" u2="&#x2122;" k="-9" />
<hkern u1="F" u2="&#x2039;" k="16" />
<hkern u1="F" u2="&#x2026;" k="90" />
<hkern u1="F" u2="&#x201e;" k="90" />
<hkern u1="F" u2="&#x201a;" k="90" />
<hkern u1="F" u2="&#x2014;" k="41" />
<hkern u1="F" u2="&#x2013;" k="41" />
<hkern u1="F" u2="&#x178;" k="-25" />
<hkern u1="F" u2="&#x153;" k="50" />
<hkern u1="F" u2="&#x152;" k="9" />
<hkern u1="F" u2="&#xfe;" k="5" />
<hkern u1="F" u2="&#xe7;" k="50" />
<hkern u1="F" u2="&#xe6;" k="50" />
<hkern u1="F" u2="&#xdd;" k="-25" />
<hkern u1="F" u2="&#xd8;" k="9" />
<hkern u1="F" u2="&#xd6;" k="9" />
<hkern u1="F" u2="&#xd5;" k="9" />
<hkern u1="F" u2="&#xd4;" k="9" />
<hkern u1="F" u2="&#xd3;" k="9" />
<hkern u1="F" u2="&#xd2;" k="9" />
<hkern u1="F" u2="&#xc6;" k="50" />
<hkern u1="F" u2="&#xc5;" k="50" />
<hkern u1="F" u2="&#xc4;" k="50" />
<hkern u1="F" u2="&#xc3;" k="50" />
<hkern u1="F" u2="&#xc2;" k="50" />
<hkern u1="F" u2="&#xc1;" k="50" />
<hkern u1="F" u2="&#xc0;" k="50" />
<hkern u1="F" u2="&#xae;" k="20" />
<hkern u1="F" u2="&#xab;" k="16" />
<hkern u1="F" u2="&#xa9;" k="20" />
<hkern u1="F" u2="z" k="40" />
<hkern u1="F" u2="y" k="20" />
<hkern u1="F" u2="x" k="40" />
<hkern u1="F" u2="w" k="20" />
<hkern u1="F" u2="v" k="20" />
<hkern u1="F" u2="u" k="40" />
<hkern u1="F" u2="t" k="20" />
<hkern u1="F" u2="s" k="40" />
<hkern u1="F" u2="r" k="40" />
<hkern u1="F" u2="q" k="50" />
<hkern u1="F" u2="p" k="40" />
<hkern u1="F" u2="o" k="50" />
<hkern u1="F" u2="n" k="40" />
<hkern u1="F" u2="m" k="40" />
<hkern u1="F" u2="l" k="10" />
<hkern u1="F" u2="k" k="10" />
<hkern u1="F" u2="j" k="10" />
<hkern u1="F" u2="i" k="10" />
<hkern u1="F" u2="h" k="10" />
<hkern u1="F" u2="g" k="30" />
<hkern u1="F" u2="f" k="20" />
<hkern u1="F" u2="e" k="50" />
<hkern u1="F" u2="d" k="50" />
<hkern u1="F" u2="c" k="50" />
<hkern u1="F" u2="b" k="10" />
<hkern u1="F" u2="a" k="50" />
<hkern u1="F" u2="Y" k="-25" />
<hkern u1="F" u2="X" k="-10" />
<hkern u1="F" u2="W" k="-5" />
<hkern u1="F" u2="V" k="-20" />
<hkern u1="F" u2="T" k="-21" />
<hkern u1="F" u2="Q" k="9" />
<hkern u1="F" u2="O" k="9" />
<hkern u1="F" u2="J" k="90" />
<hkern u1="F" u2="G" k="9" />
<hkern u1="F" u2="C" k="9" />
<hkern u1="F" u2="A" k="50" />
<hkern u1="F" u2="&#x40;" k="20" />
<hkern u1="F" u2="&#x3b;" k="30" />
<hkern u1="F" u2="&#x3a;" k="30" />
<hkern u1="F" u2="&#x2f;" k="80" />
<hkern u1="F" u2="&#x2e;" k="90" />
<hkern u1="F" u2="&#x2d;" k="41" />
<hkern u1="F" u2="&#x2c;" k="90" />
<hkern u1="F" u2="&#x26;" k="36" />
<hkern u1="G" u2="&#x2122;" k="30" />
<hkern u1="G" u2="&#x2026;" k="-20" />
<hkern u1="G" u2="&#x201e;" k="-20" />
<hkern u1="G" u2="&#x201a;" k="-20" />
<hkern u1="G" u2="&#x178;" k="20" />
<hkern u1="G" u2="&#xdd;" k="20" />
<hkern u1="G" u2="y" k="15" />
<hkern u1="G" u2="w" k="7" />
<hkern u1="G" u2="v" k="15" />
<hkern u1="G" u2="t" k="10" />
<hkern u1="G" u2="Y" k="20" />
<hkern u1="G" u2="W" k="8" />
<hkern u1="G" u2="V" k="19" />
<hkern u1="G" u2="T" k="25" />
<hkern u1="G" u2="&#x2e;" k="-20" />
<hkern u1="G" u2="&#x2c;" k="-20" />
<hkern u1="H" u2="y" k="10" />
<hkern u1="H" u2="v" k="10" />
<hkern u1="H" u2="&#x2f;" k="20" />
<hkern u1="I" u2="y" k="10" />
<hkern u1="I" u2="v" k="10" />
<hkern u1="I" u2="&#x2f;" k="20" />
<hkern u1="J" u2="&#x2026;" k="10" />
<hkern u1="J" u2="&#x201e;" k="10" />
<hkern u1="J" u2="&#x201a;" k="10" />
<hkern u1="J" u2="&#xc6;" k="9" />
<hkern u1="J" u2="&#xc5;" k="9" />
<hkern u1="J" u2="&#xc4;" k="9" />
<hkern u1="J" u2="&#xc3;" k="9" />
<hkern u1="J" u2="&#xc2;" k="9" />
<hkern u1="J" u2="&#xc1;" k="9" />
<hkern u1="J" u2="&#xc0;" k="9" />
<hkern u1="J" u2="J" k="10" />
<hkern u1="J" u2="A" k="12" />
<hkern u1="J" u2="&#x2e;" k="10" />
<hkern u1="J" u2="&#x2c;" k="10" />
<hkern u1="K" u2="&#x2014;" k="36" />
<hkern u1="K" u2="&#x2013;" k="36" />
<hkern u1="K" u2="&#x178;" k="-8" />
<hkern u1="K" u2="&#x153;" k="20" />
<hkern u1="K" u2="&#x152;" k="17" />
<hkern u1="K" u2="&#xf0;" k="20" />
<hkern u1="K" u2="&#xe7;" k="20" />
<hkern u1="K" u2="&#xe6;" k="20" />
<hkern u1="K" u2="&#xdd;" k="-8" />
<hkern u1="K" u2="&#xdc;" k="10" />
<hkern u1="K" u2="&#xdb;" k="10" />
<hkern u1="K" u2="&#xda;" k="10" />
<hkern u1="K" u2="&#xd9;" k="10" />
<hkern u1="K" u2="&#xd8;" k="17" />
<hkern u1="K" u2="&#xd6;" k="17" />
<hkern u1="K" u2="&#xd5;" k="17" />
<hkern u1="K" u2="&#xd4;" k="17" />
<hkern u1="K" u2="&#xd3;" k="17" />
<hkern u1="K" u2="&#xd2;" k="17" />
<hkern u1="K" u2="&#xae;" k="30" />
<hkern u1="K" u2="&#xa9;" k="30" />
<hkern u1="K" u2="y" k="27" />
<hkern u1="K" u2="x" k="5" />
<hkern u1="K" u2="w" k="11" />
<hkern u1="K" u2="v" k="27" />
<hkern u1="K" u2="t" k="10" />
<hkern u1="K" u2="q" k="20" />
<hkern u1="K" u2="o" k="20" />
<hkern u1="K" u2="g" k="10" />
<hkern u1="K" u2="f" k="5" />
<hkern u1="K" u2="e" k="20" />
<hkern u1="K" u2="d" k="20" />
<hkern u1="K" u2="c" k="20" />
<hkern u1="K" u2="a" k="15" />
<hkern u1="K" u2="Z" k="5" />
<hkern u1="K" u2="Y" k="-8" />
<hkern u1="K" u2="X" k="-8" />
<hkern u1="K" u2="W" k="17" />
<hkern u1="K" u2="V" k="-4" />
<hkern u1="K" u2="U" k="10" />
<hkern u1="K" u2="T" k="-5" />
<hkern u1="K" u2="S" k="5" />
<hkern u1="K" u2="Q" k="17" />
<hkern u1="K" u2="O" k="17" />
<hkern u1="K" u2="G" k="17" />
<hkern u1="K" u2="C" k="17" />
<hkern u1="K" u2="&#x40;" k="30" />
<hkern u1="K" u2="&#x2d;" k="36" />
<hkern u1="K" u2="&#x26;" k="20" />
<hkern u1="L" u2="&#x2122;" k="70" />
<hkern u1="L" u2="&#x2039;" k="9" />
<hkern u1="L" u2="&#x201d;" k="65" />
<hkern u1="L" u2="&#x201c;" k="100" />
<hkern u1="L" u2="&#x2019;" k="65" />
<hkern u1="L" u2="&#x2018;" k="100" />
<hkern u1="L" u2="&#x178;" k="87" />
<hkern u1="L" u2="&#x153;" k="16" />
<hkern u1="L" u2="&#x152;" k="39" />
<hkern u1="L" u2="&#xe7;" k="16" />
<hkern u1="L" u2="&#xe6;" k="9" />
<hkern u1="L" u2="&#xdd;" k="87" />
<hkern u1="L" u2="&#xdc;" k="15" />
<hkern u1="L" u2="&#xdb;" k="15" />
<hkern u1="L" u2="&#xda;" k="15" />
<hkern u1="L" u2="&#xd9;" k="15" />
<hkern u1="L" u2="&#xd8;" k="39" />
<hkern u1="L" u2="&#xd6;" k="39" />
<hkern u1="L" u2="&#xd5;" k="39" />
<hkern u1="L" u2="&#xd4;" k="39" />
<hkern u1="L" u2="&#xd3;" k="39" />
<hkern u1="L" u2="&#xd2;" k="39" />
<hkern u1="L" u2="&#xc6;" k="-20" />
<hkern u1="L" u2="&#xc5;" k="-20" />
<hkern u1="L" u2="&#xc4;" k="-20" />
<hkern u1="L" u2="&#xc3;" k="-20" />
<hkern u1="L" u2="&#xc2;" k="-20" />
<hkern u1="L" u2="&#xc1;" k="-20" />
<hkern u1="L" u2="&#xc0;" k="-20" />
<hkern u1="L" u2="&#xae;" k="50" />
<hkern u1="L" u2="&#xab;" k="9" />
<hkern u1="L" u2="&#xa9;" k="50" />
<hkern u1="L" u2="y" k="49" />
<hkern u1="L" u2="w" k="19" />
<hkern u1="L" u2="v" k="49" />
<hkern u1="L" u2="q" k="16" />
<hkern u1="L" u2="o" k="16" />
<hkern u1="L" u2="e" k="16" />
<hkern u1="L" u2="d" k="16" />
<hkern u1="L" u2="c" k="16" />
<hkern u1="L" u2="a" k="9" />
<hkern u1="L" u2="\" k="50" />
<hkern u1="L" u2="Z" k="-2" />
<hkern u1="L" u2="Y" k="87" />
<hkern u1="L" u2="W" k="70" />
<hkern u1="L" u2="V" k="90" />
<hkern u1="L" u2="U" k="15" />
<hkern u1="L" u2="T" k="98" />
<hkern u1="L" u2="Q" k="39" />
<hkern u1="L" u2="O" k="39" />
<hkern u1="L" u2="J" k="-7" />
<hkern u1="L" u2="G" k="39" />
<hkern u1="L" u2="C" k="39" />
<hkern u1="L" u2="A" k="-20" />
<hkern u1="L" u2="&#x40;" k="50" />
<hkern u1="L" u2="&#x3f;" k="32" />
<hkern u1="L" u2="&#x2a;" k="27" />
<hkern u1="M" u2="y" k="10" />
<hkern u1="M" u2="v" k="10" />
<hkern u1="M" u2="&#x2f;" k="20" />
<hkern u1="N" u2="y" k="10" />
<hkern u1="N" u2="v" k="10" />
<hkern u1="N" u2="&#x2f;" k="20" />
<hkern u1="O" u2="&#x2026;" k="36" />
<hkern u1="O" u2="&#x201e;" k="36" />
<hkern u1="O" u2="&#x201c;" k="20" />
<hkern u1="O" u2="&#x201a;" k="36" />
<hkern u1="O" u2="&#x2018;" k="20" />
<hkern u1="O" u2="&#x178;" k="21" />
<hkern u1="O" u2="&#x153;" k="3" />
<hkern u1="O" u2="&#xe7;" k="3" />
<hkern u1="O" u2="&#xe6;" k="3" />
<hkern u1="O" u2="&#xdd;" k="21" />
<hkern u1="O" u2="&#xc6;" k="20" />
<hkern u1="O" u2="&#xc5;" k="20" />
<hkern u1="O" u2="&#xc4;" k="20" />
<hkern u1="O" u2="&#xc3;" k="20" />
<hkern u1="O" u2="&#xc2;" k="20" />
<hkern u1="O" u2="&#xc1;" k="20" />
<hkern u1="O" u2="&#xc0;" k="20" />
<hkern u1="O" u2="z" k="10" />
<hkern u1="O" u2="x" k="10" />
<hkern u1="O" u2="u" k="3" />
<hkern u1="O" u2="r" k="3" />
<hkern u1="O" u2="q" k="3" />
<hkern u1="O" u2="p" k="3" />
<hkern u1="O" u2="o" k="3" />
<hkern u1="O" u2="n" k="3" />
<hkern u1="O" u2="m" k="3" />
<hkern u1="O" u2="l" k="6" />
<hkern u1="O" u2="k" k="6" />
<hkern u1="O" u2="h" k="6" />
<hkern u1="O" u2="e" k="3" />
<hkern u1="O" u2="d" k="3" />
<hkern u1="O" u2="c" k="3" />
<hkern u1="O" u2="b" k="6" />
<hkern u1="O" u2="a" k="3" />
<hkern u1="O" u2="Z" k="29" />
<hkern u1="O" u2="Y" k="21" />
<hkern u1="O" u2="X" k="27" />
<hkern u1="O" u2="W" k="36" />
<hkern u1="O" u2="V" k="20" />
<hkern u1="O" u2="T" k="23" />
<hkern u1="O" u2="J" k="32" />
<hkern u1="O" u2="A" k="20" />
<hkern u1="O" u2="&#x3f;" k="20" />
<hkern u1="O" u2="&#x2f;" k="50" />
<hkern u1="O" u2="&#x2e;" k="36" />
<hkern u1="O" u2="&#x2c;" k="36" />
<hkern u1="P" u2="&#x2026;" k="60" />
<hkern u1="P" u2="&#x201e;" k="60" />
<hkern u1="P" u2="&#x201a;" k="60" />
<hkern u1="P" u2="&#x178;" k="-11" />
<hkern u1="P" u2="&#x153;" k="10" />
<hkern u1="P" u2="&#xe7;" k="10" />
<hkern u1="P" u2="&#xe6;" k="10" />
<hkern u1="P" u2="&#xdd;" k="-11" />
<hkern u1="P" u2="&#xc6;" k="37" />
<hkern u1="P" u2="&#xc5;" k="37" />
<hkern u1="P" u2="&#xc4;" k="37" />
<hkern u1="P" u2="&#xc3;" k="37" />
<hkern u1="P" u2="&#xc2;" k="37" />
<hkern u1="P" u2="&#xc1;" k="37" />
<hkern u1="P" u2="&#xc0;" k="37" />
<hkern u1="P" u2="y" k="-15" />
<hkern u1="P" u2="x" k="-5" />
<hkern u1="P" u2="w" k="-10" />
<hkern u1="P" u2="v" k="-15" />
<hkern u1="P" u2="t" k="-6" />
<hkern u1="P" u2="q" k="10" />
<hkern u1="P" u2="o" k="10" />
<hkern u1="P" u2="f" k="-6" />
<hkern u1="P" u2="e" k="10" />
<hkern u1="P" u2="d" k="10" />
<hkern u1="P" u2="c" k="10" />
<hkern u1="P" u2="a" k="10" />
<hkern u1="P" u2="Z" k="25" />
<hkern u1="P" u2="Y" k="-11" />
<hkern u1="P" u2="X" k="2" />
<hkern u1="P" u2="W" k="2" />
<hkern u1="P" u2="V" k="-12" />
<hkern u1="P" u2="T" k="-5" />
<hkern u1="P" u2="J" k="69" />
<hkern u1="P" u2="A" k="38" />
<hkern u1="P" u2="&#x2e;" k="60" />
<hkern u1="P" u2="&#x2c;" k="60" />
<hkern u1="P" u2="&#x2a;" k="-5" />
<hkern u1="P" u2="&#x26;" k="16" />
<hkern u1="Q" u2="&#x2026;" k="36" />
<hkern u1="Q" u2="&#x201e;" k="9" />
<hkern u1="Q" u2="&#x201c;" k="20" />
<hkern u1="Q" u2="&#x201a;" k="9" />
<hkern u1="Q" u2="&#x2018;" k="20" />
<hkern u1="Q" u2="&#x178;" k="21" />
<hkern u1="Q" u2="&#x153;" k="3" />
<hkern u1="Q" u2="&#xe7;" k="3" />
<hkern u1="Q" u2="&#xe6;" k="3" />
<hkern u1="Q" u2="&#xdd;" k="21" />
<hkern u1="Q" u2="&#xc6;" k="20" />
<hkern u1="Q" u2="&#xc5;" k="20" />
<hkern u1="Q" u2="&#xc4;" k="20" />
<hkern u1="Q" u2="&#xc3;" k="20" />
<hkern u1="Q" u2="&#xc2;" k="20" />
<hkern u1="Q" u2="&#xc1;" k="20" />
<hkern u1="Q" u2="&#xc0;" k="20" />
<hkern u1="Q" u2="z" k="10" />
<hkern u1="Q" u2="x" k="10" />
<hkern u1="Q" u2="u" k="3" />
<hkern u1="Q" u2="r" k="3" />
<hkern u1="Q" u2="q" k="3" />
<hkern u1="Q" u2="p" k="3" />
<hkern u1="Q" u2="o" k="3" />
<hkern u1="Q" u2="n" k="3" />
<hkern u1="Q" u2="m" k="3" />
<hkern u1="Q" u2="l" k="6" />
<hkern u1="Q" u2="k" k="6" />
<hkern u1="Q" u2="h" k="6" />
<hkern u1="Q" u2="e" k="3" />
<hkern u1="Q" u2="d" k="3" />
<hkern u1="Q" u2="c" k="3" />
<hkern u1="Q" u2="b" k="6" />
<hkern u1="Q" u2="a" k="3" />
<hkern u1="Q" u2="Z" k="29" />
<hkern u1="Q" u2="Y" k="21" />
<hkern u1="Q" u2="X" k="5" />
<hkern u1="Q" u2="W" k="36" />
<hkern u1="Q" u2="V" k="20" />
<hkern u1="Q" u2="T" k="23" />
<hkern u1="Q" u2="J" k="20" />
<hkern u1="Q" u2="A" k="20" />
<hkern u1="Q" u2="&#x3f;" k="20" />
<hkern u1="Q" u2="&#x2f;" k="30" />
<hkern u1="Q" u2="&#x2e;" k="-9" />
<hkern u1="Q" u2="&#x2c;" k="9" />
<hkern u1="R" u2="&#x178;" k="-11" />
<hkern u1="R" u2="&#x152;" k="2" />
<hkern u1="R" u2="&#xdd;" k="-11" />
<hkern u1="R" u2="&#xd8;" k="2" />
<hkern u1="R" u2="&#xd6;" k="2" />
<hkern u1="R" u2="&#xd5;" k="2" />
<hkern u1="R" u2="&#xd4;" k="2" />
<hkern u1="R" u2="&#xd3;" k="2" />
<hkern u1="R" u2="&#xd2;" k="2" />
<hkern u1="R" u2="Y" k="-11" />
<hkern u1="R" u2="X" k="-10" />
<hkern u1="R" u2="W" k="7" />
<hkern u1="R" u2="V" k="-9" />
<hkern u1="R" u2="T" k="6" />
<hkern u1="R" u2="Q" k="2" />
<hkern u1="R" u2="O" k="2" />
<hkern u1="R" u2="J" k="8" />
<hkern u1="R" u2="G" k="2" />
<hkern u1="R" u2="C" k="2" />
<hkern u1="R" u2="&#x26;" k="-10" />
<hkern u1="S" u2="&#x178;" k="-10" />
<hkern u1="S" u2="&#xdd;" k="-10" />
<hkern u1="S" u2="&#xc6;" k="-5" />
<hkern u1="S" u2="&#xc5;" k="-5" />
<hkern u1="S" u2="&#xc4;" k="-5" />
<hkern u1="S" u2="&#xc3;" k="-5" />
<hkern u1="S" u2="&#xc2;" k="-5" />
<hkern u1="S" u2="&#xc1;" k="-5" />
<hkern u1="S" u2="&#xc0;" k="-5" />
<hkern u1="S" u2="Y" k="-10" />
<hkern u1="S" u2="X" k="-5" />
<hkern u1="S" u2="W" k="5" />
<hkern u1="S" u2="V" k="-8" />
<hkern u1="S" u2="A" k="-5" />
<hkern u1="T" u2="&#x203a;" k="40" />
<hkern u1="T" u2="&#x2039;" k="110" />
<hkern u1="T" u2="&#x2026;" k="74" />
<hkern u1="T" u2="&#x201e;" k="74" />
<hkern u1="T" u2="&#x201a;" k="74" />
<hkern u1="T" u2="&#x2014;" k="61" />
<hkern u1="T" u2="&#x2013;" k="61" />
<hkern u1="T" u2="&#x178;" k="-25" />
<hkern u1="T" u2="&#x153;" k="84" />
<hkern u1="T" u2="&#x152;" k="23" />
<hkern u1="T" u2="&#xe7;" k="84" />
<hkern u1="T" u2="&#xe6;" k="73" />
<hkern u1="T" u2="&#xdd;" k="-25" />
<hkern u1="T" u2="&#xd8;" k="23" />
<hkern u1="T" u2="&#xd6;" k="23" />
<hkern u1="T" u2="&#xd5;" k="23" />
<hkern u1="T" u2="&#xd4;" k="23" />
<hkern u1="T" u2="&#xd3;" k="23" />
<hkern u1="T" u2="&#xd2;" k="23" />
<hkern u1="T" u2="&#xc6;" k="63" />
<hkern u1="T" u2="&#xc5;" k="63" />
<hkern u1="T" u2="&#xc4;" k="63" />
<hkern u1="T" u2="&#xc3;" k="63" />
<hkern u1="T" u2="&#xc2;" k="63" />
<hkern u1="T" u2="&#xc1;" k="63" />
<hkern u1="T" u2="&#xc0;" k="63" />
<hkern u1="T" u2="&#xbf;" k="50" />
<hkern u1="T" u2="&#xbb;" k="40" />
<hkern u1="T" u2="&#xae;" k="30" />
<hkern u1="T" u2="&#xab;" k="110" />
<hkern u1="T" u2="&#xa9;" k="30" />
<hkern u1="T" u2="z" k="53" />
<hkern u1="T" u2="y" k="43" />
<hkern u1="T" u2="x" k="53" />
<hkern u1="T" u2="w" k="38" />
<hkern u1="T" u2="v" k="43" />
<hkern u1="T" u2="u" k="53" />
<hkern u1="T" u2="t" k="20" />
<hkern u1="T" u2="s" k="78" />
<hkern u1="T" u2="r" k="53" />
<hkern u1="T" u2="q" k="84" />
<hkern u1="T" u2="p" k="53" />
<hkern u1="T" u2="o" k="84" />
<hkern u1="T" u2="n" k="53" />
<hkern u1="T" u2="m" k="53" />
<hkern u1="T" u2="g" k="95" />
<hkern u1="T" u2="f" k="10" />
<hkern u1="T" u2="e" k="84" />
<hkern u1="T" u2="d" k="84" />
<hkern u1="T" u2="c" k="84" />
<hkern u1="T" u2="a" k="73" />
<hkern u1="T" u2="\" k="-10" />
<hkern u1="T" u2="Z" k="8" />
<hkern u1="T" u2="Y" k="-25" />
<hkern u1="T" u2="X" k="-10" />
<hkern u1="T" u2="V" k="-20" />
<hkern u1="T" u2="T" k="-10" />
<hkern u1="T" u2="Q" k="23" />
<hkern u1="T" u2="O" k="23" />
<hkern u1="T" u2="J" k="92" />
<hkern u1="T" u2="G" k="23" />
<hkern u1="T" u2="C" k="23" />
<hkern u1="T" u2="A" k="61" />
<hkern u1="T" u2="&#x40;" k="30" />
<hkern u1="T" u2="&#x3b;" k="50" />
<hkern u1="T" u2="&#x3a;" k="50" />
<hkern u1="T" u2="&#x2f;" k="23" />
<hkern u1="T" u2="&#x2e;" k="74" />
<hkern u1="T" u2="&#x2d;" k="61" />
<hkern u1="T" u2="&#x2c;" k="74" />
<hkern u1="T" u2="&#x2a;" k="-7" />
<hkern u1="T" u2="&#x26;" k="40" />
<hkern u1="U" u2="&#x2026;" k="10" />
<hkern u1="U" u2="&#x201e;" k="10" />
<hkern u1="U" u2="&#x201a;" k="10" />
<hkern u1="U" u2="&#xc6;" k="9" />
<hkern u1="U" u2="&#xc5;" k="9" />
<hkern u1="U" u2="&#xc4;" k="9" />
<hkern u1="U" u2="&#xc3;" k="9" />
<hkern u1="U" u2="&#xc2;" k="9" />
<hkern u1="U" u2="&#xc1;" k="9" />
<hkern u1="U" u2="&#xc0;" k="9" />
<hkern u1="U" u2="J" k="10" />
<hkern u1="U" u2="A" k="12" />
<hkern u1="U" u2="&#x2e;" k="10" />
<hkern u1="U" u2="&#x2c;" k="10" />
<hkern u1="V" u2="&#x203a;" k="20" />
<hkern u1="V" u2="&#x2039;" k="50" />
<hkern u1="V" u2="&#x2026;" k="65" />
<hkern u1="V" u2="&#x201e;" k="65" />
<hkern u1="V" u2="&#x201a;" k="65" />
<hkern u1="V" u2="&#x2014;" k="60" />
<hkern u1="V" u2="&#x2013;" k="60" />
<hkern u1="V" u2="&#x178;" k="-26" />
<hkern u1="V" u2="&#x153;" k="49" />
<hkern u1="V" u2="&#x152;" k="20" />
<hkern u1="V" u2="&#xfe;" k="5" />
<hkern u1="V" u2="&#xe7;" k="49" />
<hkern u1="V" u2="&#xe6;" k="45" />
<hkern u1="V" u2="&#xdd;" k="-26" />
<hkern u1="V" u2="&#xd8;" k="20" />
<hkern u1="V" u2="&#xd6;" k="20" />
<hkern u1="V" u2="&#xd5;" k="20" />
<hkern u1="V" u2="&#xd4;" k="20" />
<hkern u1="V" u2="&#xd3;" k="20" />
<hkern u1="V" u2="&#xd2;" k="20" />
<hkern u1="V" u2="&#xc6;" k="64" />
<hkern u1="V" u2="&#xc5;" k="64" />
<hkern u1="V" u2="&#xc4;" k="64" />
<hkern u1="V" u2="&#xc3;" k="64" />
<hkern u1="V" u2="&#xc2;" k="64" />
<hkern u1="V" u2="&#xc1;" k="64" />
<hkern u1="V" u2="&#xc0;" k="64" />
<hkern u1="V" u2="&#xbb;" k="20" />
<hkern u1="V" u2="&#xae;" k="5" />
<hkern u1="V" u2="&#xab;" k="50" />
<hkern u1="V" u2="&#xa9;" k="5" />
<hkern u1="V" u2="&#x7d;" k="-30" />
<hkern u1="V" u2="y" k="10" />
<hkern u1="V" u2="x" k="25" />
<hkern u1="V" u2="w" k="10" />
<hkern u1="V" u2="v" k="10" />
<hkern u1="V" u2="u" k="30" />
<hkern u1="V" u2="t" k="10" />
<hkern u1="V" u2="s" k="40" />
<hkern u1="V" u2="r" k="30" />
<hkern u1="V" u2="q" k="49" />
<hkern u1="V" u2="p" k="30" />
<hkern u1="V" u2="o" k="49" />
<hkern u1="V" u2="n" k="30" />
<hkern u1="V" u2="m" k="30" />
<hkern u1="V" u2="l" k="10" />
<hkern u1="V" u2="k" k="10" />
<hkern u1="V" u2="h" k="10" />
<hkern u1="V" u2="g" k="35" />
<hkern u1="V" u2="f" k="10" />
<hkern u1="V" u2="e" k="49" />
<hkern u1="V" u2="d" k="49" />
<hkern u1="V" u2="c" k="49" />
<hkern u1="V" u2="b" k="10" />
<hkern u1="V" u2="a" k="45" />
<hkern u1="V" u2="]" k="-30" />
<hkern u1="V" u2="Y" k="-26" />
<hkern u1="V" u2="X" k="-11" />
<hkern u1="V" u2="W" k="7" />
<hkern u1="V" u2="V" k="-13" />
<hkern u1="V" u2="T" k="-20" />
<hkern u1="V" u2="S" k="-6" />
<hkern u1="V" u2="Q" k="20" />
<hkern u1="V" u2="O" k="20" />
<hkern u1="V" u2="J" k="68" />
<hkern u1="V" u2="G" k="20" />
<hkern u1="V" u2="C" k="20" />
<hkern u1="V" u2="A" k="67" />
<hkern u1="V" u2="&#x40;" k="5" />
<hkern u1="V" u2="&#x3b;" k="20" />
<hkern u1="V" u2="&#x3a;" k="20" />
<hkern u1="V" u2="&#x2e;" k="65" />
<hkern u1="V" u2="&#x2d;" k="60" />
<hkern u1="V" u2="&#x2c;" k="65" />
<hkern u1="V" u2="&#x29;" k="-30" />
<hkern u1="V" u2="&#x26;" k="36" />
<hkern u1="W" u2="&#x203a;" k="20" />
<hkern u1="W" u2="&#x2039;" k="40" />
<hkern u1="W" u2="&#x2026;" k="25" />
<hkern u1="W" u2="&#x201e;" k="25" />
<hkern u1="W" u2="&#x201a;" k="25" />
<hkern u1="W" u2="&#x2014;" k="40" />
<hkern u1="W" u2="&#x2013;" k="40" />
<hkern u1="W" u2="&#x153;" k="54" />
<hkern u1="W" u2="&#x152;" k="36" />
<hkern u1="W" u2="&#xfe;" k="5" />
<hkern u1="W" u2="&#xe7;" k="54" />
<hkern u1="W" u2="&#xe6;" k="50" />
<hkern u1="W" u2="&#xd8;" k="36" />
<hkern u1="W" u2="&#xd6;" k="36" />
<hkern u1="W" u2="&#xd5;" k="36" />
<hkern u1="W" u2="&#xd4;" k="36" />
<hkern u1="W" u2="&#xd3;" k="36" />
<hkern u1="W" u2="&#xd2;" k="36" />
<hkern u1="W" u2="&#xc6;" k="57" />
<hkern u1="W" u2="&#xc5;" k="57" />
<hkern u1="W" u2="&#xc4;" k="57" />
<hkern u1="W" u2="&#xc3;" k="57" />
<hkern u1="W" u2="&#xc2;" k="57" />
<hkern u1="W" u2="&#xc1;" k="57" />
<hkern u1="W" u2="&#xc0;" k="57" />
<hkern u1="W" u2="&#xbb;" k="20" />
<hkern u1="W" u2="&#xae;" k="20" />
<hkern u1="W" u2="&#xab;" k="40" />
<hkern u1="W" u2="&#xa9;" k="20" />
<hkern u1="W" u2="&#x7d;" k="-10" />
<hkern u1="W" u2="z" k="30" />
<hkern u1="W" u2="y" k="30" />
<hkern u1="W" u2="x" k="30" />
<hkern u1="W" u2="w" k="30" />
<hkern u1="W" u2="v" k="30" />
<hkern u1="W" u2="u" k="35" />
<hkern u1="W" u2="t" k="30" />
<hkern u1="W" u2="s" k="50" />
<hkern u1="W" u2="r" k="35" />
<hkern u1="W" u2="q" k="54" />
<hkern u1="W" u2="p" k="35" />
<hkern u1="W" u2="o" k="54" />
<hkern u1="W" u2="n" k="35" />
<hkern u1="W" u2="m" k="35" />
<hkern u1="W" u2="l" k="10" />
<hkern u1="W" u2="k" k="10" />
<hkern u1="W" u2="j" k="20" />
<hkern u1="W" u2="i" k="20" />
<hkern u1="W" u2="h" k="10" />
<hkern u1="W" u2="g" k="50" />
<hkern u1="W" u2="f" k="15" />
<hkern u1="W" u2="e" k="54" />
<hkern u1="W" u2="d" k="54" />
<hkern u1="W" u2="c" k="54" />
<hkern u1="W" u2="b" k="10" />
<hkern u1="W" u2="a" k="50" />
<hkern u1="W" u2="]" k="-10" />
<hkern u1="W" u2="X" k="15" />
<hkern u1="W" u2="W" k="14" />
<hkern u1="W" u2="V" k="7" />
<hkern u1="W" u2="S" k="7" />
<hkern u1="W" u2="Q" k="36" />
<hkern u1="W" u2="O" k="36" />
<hkern u1="W" u2="J" k="60" />
<hkern u1="W" u2="G" k="36" />
<hkern u1="W" u2="C" k="36" />
<hkern u1="W" u2="A" k="59" />
<hkern u1="W" u2="&#x40;" k="20" />
<hkern u1="W" u2="&#x3b;" k="20" />
<hkern u1="W" u2="&#x3a;" k="20" />
<hkern u1="W" u2="&#x2e;" k="25" />
<hkern u1="W" u2="&#x2d;" k="40" />
<hkern u1="W" u2="&#x2c;" k="25" />
<hkern u1="W" u2="&#x29;" k="-10" />
<hkern u1="W" u2="&#x26;" k="37" />
<hkern u1="X" u2="&#x2014;" k="30" />
<hkern u1="X" u2="&#x2013;" k="30" />
<hkern u1="X" u2="&#x178;" k="-16" />
<hkern u1="X" u2="&#x153;" k="10" />
<hkern u1="X" u2="&#x152;" k="27" />
<hkern u1="X" u2="&#xe7;" k="10" />
<hkern u1="X" u2="&#xe6;" k="10" />
<hkern u1="X" u2="&#xdd;" k="-16" />
<hkern u1="X" u2="&#xd8;" k="27" />
<hkern u1="X" u2="&#xd6;" k="27" />
<hkern u1="X" u2="&#xd5;" k="27" />
<hkern u1="X" u2="&#xd4;" k="27" />
<hkern u1="X" u2="&#xd3;" k="27" />
<hkern u1="X" u2="&#xd2;" k="27" />
<hkern u1="X" u2="&#xc6;" k="-10" />
<hkern u1="X" u2="&#xc5;" k="-10" />
<hkern u1="X" u2="&#xc4;" k="-10" />
<hkern u1="X" u2="&#xc3;" k="-10" />
<hkern u1="X" u2="&#xc2;" k="-10" />
<hkern u1="X" u2="&#xc1;" k="-10" />
<hkern u1="X" u2="&#xc0;" k="-10" />
<hkern u1="X" u2="&#xae;" k="14" />
<hkern u1="X" u2="&#xa9;" k="14" />
<hkern u1="X" u2="&#x7d;" k="-40" />
<hkern u1="X" u2="y" k="20" />
<hkern u1="X" u2="w" k="10" />
<hkern u1="X" u2="v" k="20" />
<hkern u1="X" u2="u" k="10" />
<hkern u1="X" u2="t" k="10" />
<hkern u1="X" u2="r" k="10" />
<hkern u1="X" u2="q" k="10" />
<hkern u1="X" u2="p" k="10" />
<hkern u1="X" u2="o" k="10" />
<hkern u1="X" u2="n" k="10" />
<hkern u1="X" u2="m" k="10" />
<hkern u1="X" u2="f" k="10" />
<hkern u1="X" u2="e" k="10" />
<hkern u1="X" u2="d" k="10" />
<hkern u1="X" u2="c" k="10" />
<hkern u1="X" u2="a" k="10" />
<hkern u1="X" u2="]" k="-40" />
<hkern u1="X" u2="Y" k="-16" />
<hkern u1="X" u2="X" k="-6" />
<hkern u1="X" u2="W" k="15" />
<hkern u1="X" u2="V" k="-11" />
<hkern u1="X" u2="T" k="-10" />
<hkern u1="X" u2="S" k="-7" />
<hkern u1="X" u2="Q" k="27" />
<hkern u1="X" u2="O" k="27" />
<hkern u1="X" u2="G" k="27" />
<hkern u1="X" u2="C" k="27" />
<hkern u1="X" u2="A" k="-10" />
<hkern u1="X" u2="&#x40;" k="14" />
<hkern u1="X" u2="&#x2d;" k="30" />
<hkern u1="X" u2="&#x29;" k="-40" />
<hkern u1="X" u2="&#x26;" k="11" />
<hkern u1="Y" u2="&#x203a;" k="40" />
<hkern u1="Y" u2="&#x2039;" k="60" />
<hkern u1="Y" u2="&#x2026;" k="50" />
<hkern u1="Y" u2="&#x201e;" k="50" />
<hkern u1="Y" u2="&#x201a;" k="50" />
<hkern u1="Y" u2="&#x2014;" k="50" />
<hkern u1="Y" u2="&#x2013;" k="50" />
<hkern u1="Y" u2="&#x153;" k="64" />
<hkern u1="Y" u2="&#x152;" k="21" />
<hkern u1="Y" u2="&#xe7;" k="64" />
<hkern u1="Y" u2="&#xe6;" k="60" />
<hkern u1="Y" u2="&#xd8;" k="21" />
<hkern u1="Y" u2="&#xd6;" k="21" />
<hkern u1="Y" u2="&#xd5;" k="21" />
<hkern u1="Y" u2="&#xd4;" k="21" />
<hkern u1="Y" u2="&#xd3;" k="21" />
<hkern u1="Y" u2="&#xd2;" k="21" />
<hkern u1="Y" u2="&#xc6;" k="52" />
<hkern u1="Y" u2="&#xc5;" k="52" />
<hkern u1="Y" u2="&#xc4;" k="52" />
<hkern u1="Y" u2="&#xc3;" k="52" />
<hkern u1="Y" u2="&#xc2;" k="52" />
<hkern u1="Y" u2="&#xc1;" k="52" />
<hkern u1="Y" u2="&#xc0;" k="52" />
<hkern u1="Y" u2="&#xbb;" k="40" />
<hkern u1="Y" u2="&#xae;" k="30" />
<hkern u1="Y" u2="&#xab;" k="60" />
<hkern u1="Y" u2="&#xa9;" k="30" />
<hkern u1="Y" u2="&#x7d;" k="-50" />
<hkern u1="Y" u2="z" k="30" />
<hkern u1="Y" u2="y" k="20" />
<hkern u1="Y" u2="x" k="20" />
<hkern u1="Y" u2="w" k="10" />
<hkern u1="Y" u2="v" k="20" />
<hkern u1="Y" u2="u" k="40" />
<hkern u1="Y" u2="t" k="20" />
<hkern u1="Y" u2="s" k="52" />
<hkern u1="Y" u2="r" k="40" />
<hkern u1="Y" u2="q" k="64" />
<hkern u1="Y" u2="p" k="40" />
<hkern u1="Y" u2="o" k="64" />
<hkern u1="Y" u2="n" k="40" />
<hkern u1="Y" u2="m" k="40" />
<hkern u1="Y" u2="g" k="40" />
<hkern u1="Y" u2="f" k="10" />
<hkern u1="Y" u2="e" k="64" />
<hkern u1="Y" u2="d" k="64" />
<hkern u1="Y" u2="c" k="64" />
<hkern u1="Y" u2="a" k="60" />
<hkern u1="Y" u2="]" k="-50" />
<hkern u1="Y" u2="X" k="-16" />
<hkern u1="Y" u2="V" k="-26" />
<hkern u1="Y" u2="T" k="-25" />
<hkern u1="Y" u2="S" k="-10" />
<hkern u1="Y" u2="Q" k="21" />
<hkern u1="Y" u2="O" k="21" />
<hkern u1="Y" u2="J" k="79" />
<hkern u1="Y" u2="G" k="21" />
<hkern u1="Y" u2="C" k="21" />
<hkern u1="Y" u2="A" k="53" />
<hkern u1="Y" u2="&#x40;" k="30" />
<hkern u1="Y" u2="&#x3b;" k="30" />
<hkern u1="Y" u2="&#x3a;" k="30" />
<hkern u1="Y" u2="&#x2f;" k="27" />
<hkern u1="Y" u2="&#x2e;" k="50" />
<hkern u1="Y" u2="&#x2d;" k="50" />
<hkern u1="Y" u2="&#x2c;" k="50" />
<hkern u1="Y" u2="&#x29;" k="-50" />
<hkern u1="Y" u2="&#x26;" k="30" />
<hkern u1="Z" u2="&#x2014;" k="32" />
<hkern u1="Z" u2="&#x2013;" k="32" />
<hkern u1="Z" u2="&#x153;" k="27" />
<hkern u1="Z" u2="&#x152;" k="27" />
<hkern u1="Z" u2="&#xfe;" k="5" />
<hkern u1="Z" u2="&#xf0;" k="30" />
<hkern u1="Z" u2="&#xe7;" k="27" />
<hkern u1="Z" u2="&#xe6;" k="17" />
<hkern u1="Z" u2="&#xd8;" k="27" />
<hkern u1="Z" u2="&#xd6;" k="27" />
<hkern u1="Z" u2="&#xd5;" k="27" />
<hkern u1="Z" u2="&#xd4;" k="27" />
<hkern u1="Z" u2="&#xd3;" k="27" />
<hkern u1="Z" u2="&#xd2;" k="27" />
<hkern u1="Z" u2="&#xc6;" k="10" />
<hkern u1="Z" u2="&#xc5;" k="10" />
<hkern u1="Z" u2="&#xc4;" k="10" />
<hkern u1="Z" u2="&#xc3;" k="10" />
<hkern u1="Z" u2="&#xc2;" k="10" />
<hkern u1="Z" u2="&#xc1;" k="10" />
<hkern u1="Z" u2="&#xc0;" k="10" />
<hkern u1="Z" u2="y" k="30" />
<hkern u1="Z" u2="w" k="10" />
<hkern u1="Z" u2="v" k="30" />
<hkern u1="Z" u2="u" k="20" />
<hkern u1="Z" u2="r" k="20" />
<hkern u1="Z" u2="q" k="27" />
<hkern u1="Z" u2="p" k="20" />
<hkern u1="Z" u2="o" k="27" />
<hkern u1="Z" u2="n" k="20" />
<hkern u1="Z" u2="m" k="20" />
<hkern u1="Z" u2="l" k="10" />
<hkern u1="Z" u2="k" k="10" />
<hkern u1="Z" u2="j" k="15" />
<hkern u1="Z" u2="i" k="20" />
<hkern u1="Z" u2="h" k="10" />
<hkern u1="Z" u2="g" k="20" />
<hkern u1="Z" u2="f" k="20" />
<hkern u1="Z" u2="e" k="27" />
<hkern u1="Z" u2="d" k="27" />
<hkern u1="Z" u2="c" k="27" />
<hkern u1="Z" u2="b" k="10" />
<hkern u1="Z" u2="a" k="17" />
<hkern u1="Z" u2="T" k="3" />
<hkern u1="Z" u2="Q" k="27" />
<hkern u1="Z" u2="O" k="27" />
<hkern u1="Z" u2="J" k="1" />
<hkern u1="Z" u2="G" k="27" />
<hkern u1="Z" u2="C" k="27" />
<hkern u1="Z" u2="A" k="10" />
<hkern u1="Z" u2="&#x2d;" k="32" />
<hkern u1="[" u2="&#x178;" k="-50" />
<hkern u1="[" u2="&#xdd;" k="-50" />
<hkern u1="[" u2="j" k="-150" />
<hkern u1="[" u2="g" k="-20" />
<hkern u1="[" u2="Y" k="-50" />
<hkern u1="[" u2="X" k="-40" />
<hkern u1="[" u2="W" k="-10" />
<hkern u1="[" u2="V" k="-30" />
<hkern u1="[" u2="J" k="9" />
<hkern u1="[" u2="&#x37;" k="-9" />
<hkern u1="\" u2="&#x178;" k="60" />
<hkern u1="\" u2="&#x153;" k="10" />
<hkern u1="\" u2="&#xe7;" k="10" />
<hkern u1="\" u2="&#xe6;" k="10" />
<hkern u1="\" u2="&#xdd;" k="60" />
<hkern u1="\" u2="q" k="10" />
<hkern u1="\" u2="o" k="10" />
<hkern u1="\" u2="j" k="-130" />
<hkern u1="\" u2="e" k="10" />
<hkern u1="\" u2="d" k="10" />
<hkern u1="\" u2="c" k="10" />
<hkern u1="\" u2="a" k="10" />
<hkern u1="\" u2="Y" k="60" />
<hkern u1="\" u2="W" k="50" />
<hkern u1="\" u2="V" k="70" />
<hkern u1="\" u2="T" k="90" />
<hkern u1="a" u2="&#x2122;" k="20" />
<hkern u1="a" u2="&#x201c;" k="30" />
<hkern u1="a" u2="&#x2018;" k="30" />
<hkern u1="a" u2="y" k="7" />
<hkern u1="a" u2="v" k="7" />
<hkern u1="a" u2="t" k="10" />
<hkern u1="a" u2="f" k="2" />
<hkern u1="a" u2="\" k="10" />
<hkern u1="a" u2="&#x3f;" k="15" />
<hkern u1="b" u2="&#x2122;" k="30" />
<hkern u1="b" u2="&#x2026;" k="20" />
<hkern u1="b" u2="&#x201e;" k="20" />
<hkern u1="b" u2="&#x201c;" k="31" />
<hkern u1="b" u2="&#x201a;" k="20" />
<hkern u1="b" u2="&#x2018;" k="31" />
<hkern u1="b" u2="z" k="17" />
<hkern u1="b" u2="y" k="17" />
<hkern u1="b" u2="x" k="20" />
<hkern u1="b" u2="w" k="7" />
<hkern u1="b" u2="v" k="17" />
<hkern u1="b" u2="t" k="7" />
<hkern u1="b" u2="f" k="5" />
<hkern u1="b" u2="\" k="10" />
<hkern u1="b" u2="&#x3f;" k="27" />
<hkern u1="b" u2="&#x3b;" k="10" />
<hkern u1="b" u2="&#x3a;" k="10" />
<hkern u1="b" u2="&#x2f;" k="10" />
<hkern u1="b" u2="&#x2e;" k="20" />
<hkern u1="b" u2="&#x2c;" k="20" />
<hkern u1="b" u2="&#x21;" k="9" />
<hkern u1="c" u2="&#x2039;" k="9" />
<hkern u1="c" u2="&#xab;" k="9" />
<hkern u1="e" u2="y" k="15" />
<hkern u1="e" u2="x" k="17" />
<hkern u1="e" u2="w" k="5" />
<hkern u1="e" u2="v" k="15" />
<hkern u1="e" u2="t" k="2" />
<hkern u1="e" u2="f" k="5" />
<hkern u1="f" u2="&#x2122;" k="-45" />
<hkern u1="f" u2="&#x203a;" k="7" />
<hkern u1="f" u2="&#x2039;" k="28" />
<hkern u1="f" u2="&#x2026;" k="67" />
<hkern u1="f" u2="&#x201e;" k="67" />
<hkern u1="f" u2="&#x201d;" k="-50" />
<hkern u1="f" u2="&#x201c;" k="-30" />
<hkern u1="f" u2="&#x201a;" k="67" />
<hkern u1="f" u2="&#x2019;" k="-50" />
<hkern u1="f" u2="&#x2018;" k="-30" />
<hkern u1="f" u2="&#x2014;" k="21" />
<hkern u1="f" u2="&#x2013;" k="21" />
<hkern u1="f" u2="&#x153;" k="18" />
<hkern u1="f" u2="&#xfe;" k="-7" />
<hkern u1="f" u2="&#xf0;" k="20" />
<hkern u1="f" u2="&#xe7;" k="18" />
<hkern u1="f" u2="&#xe6;" k="18" />
<hkern u1="f" u2="&#xbb;" k="7" />
<hkern u1="f" u2="&#xae;" k="-9" />
<hkern u1="f" u2="&#xab;" k="28" />
<hkern u1="f" u2="&#xa9;" k="-9" />
<hkern u1="f" u2="&#x7d;" k="-70" />
<hkern u1="f" u2="u" k="3" />
<hkern u1="f" u2="t" k="-5" />
<hkern u1="f" u2="r" k="3" />
<hkern u1="f" u2="q" k="18" />
<hkern u1="f" u2="p" k="3" />
<hkern u1="f" u2="o" k="18" />
<hkern u1="f" u2="n" k="3" />
<hkern u1="f" u2="m" k="3" />
<hkern u1="f" u2="l" k="-7" />
<hkern u1="f" u2="k" k="-7" />
<hkern u1="f" u2="h" k="-7" />
<hkern u1="f" u2="g" k="5" />
<hkern u1="f" u2="f" k="-5" />
<hkern u1="f" u2="e" k="18" />
<hkern u1="f" u2="d" k="18" />
<hkern u1="f" u2="c" k="18" />
<hkern u1="f" u2="b" k="-7" />
<hkern u1="f" u2="a" k="18" />
<hkern u1="f" u2="]" k="-70" />
<hkern u1="f" u2="\" k="-40" />
<hkern u1="f" u2="&#x40;" k="-9" />
<hkern u1="f" u2="&#x3f;" k="-40" />
<hkern u1="f" u2="&#x3b;" k="1" />
<hkern u1="f" u2="&#x3a;" k="1" />
<hkern u1="f" u2="&#x2f;" k="59" />
<hkern u1="f" u2="&#x2e;" k="67" />
<hkern u1="f" u2="&#x2d;" k="21" />
<hkern u1="f" u2="&#x2c;" k="67" />
<hkern u1="f" u2="&#x2a;" k="-25" />
<hkern u1="f" u2="&#x29;" k="-70" />
<hkern u1="f" u2="&#x26;" k="21" />
<hkern u1="f" u2="&#x21;" k="-18" />
<hkern u1="g" u2="&#x201d;" k="-40" />
<hkern u1="g" u2="&#x201c;" k="-20" />
<hkern u1="g" u2="&#x2019;" k="-40" />
<hkern u1="g" u2="&#x2018;" k="-20" />
<hkern u1="g" u2="&#xae;" k="-9" />
<hkern u1="g" u2="&#xa9;" k="-9" />
<hkern u1="g" u2="&#x7d;" k="-20" />
<hkern u1="g" u2="t" k="-10" />
<hkern u1="g" u2="j" k="-50" />
<hkern u1="g" u2="g" k="-10" />
<hkern u1="g" u2="]" k="-20" />
<hkern u1="g" u2="&#x40;" k="-9" />
<hkern u1="g" u2="&#x3f;" k="-14" />
<hkern u1="g" u2="&#x3b;" k="-20" />
<hkern u1="g" u2="&#x2f;" k="-45" />
<hkern u1="g" u2="&#x2c;" k="-26" />
<hkern u1="g" u2="&#x2a;" k="-18" />
<hkern u1="g" u2="&#x29;" k="-20" />
<hkern u1="h" u2="&#x2122;" k="20" />
<hkern u1="h" u2="&#x201c;" k="30" />
<hkern u1="h" u2="&#x2018;" k="30" />
<hkern u1="h" u2="y" k="7" />
<hkern u1="h" u2="v" k="7" />
<hkern u1="h" u2="t" k="10" />
<hkern u1="h" u2="f" k="2" />
<hkern u1="h" u2="\" k="10" />
<hkern u1="h" u2="&#x3f;" k="15" />
<hkern u1="k" u2="&#x2039;" k="18" />
<hkern u1="k" u2="&#x153;" k="12" />
<hkern u1="k" u2="&#xe7;" k="12" />
<hkern u1="k" u2="&#xe6;" k="12" />
<hkern u1="k" u2="&#xab;" k="18" />
<hkern u1="k" u2="y" k="2" />
<hkern u1="k" u2="v" k="2" />
<hkern u1="k" u2="q" k="12" />
<hkern u1="k" u2="o" k="12" />
<hkern u1="k" u2="g" k="20" />
<hkern u1="k" u2="e" k="12" />
<hkern u1="k" u2="d" k="12" />
<hkern u1="k" u2="c" k="12" />
<hkern u1="k" u2="a" k="12" />
<hkern u1="m" u2="&#x2122;" k="20" />
<hkern u1="m" u2="&#x201c;" k="30" />
<hkern u1="m" u2="&#x2018;" k="30" />
<hkern u1="m" u2="y" k="7" />
<hkern u1="m" u2="v" k="7" />
<hkern u1="m" u2="t" k="10" />
<hkern u1="m" u2="f" k="2" />
<hkern u1="m" u2="\" k="10" />
<hkern u1="m" u2="&#x3f;" k="15" />
<hkern u1="n" u2="&#x2122;" k="20" />
<hkern u1="n" u2="&#x201c;" k="30" />
<hkern u1="n" u2="&#x2018;" k="30" />
<hkern u1="n" u2="y" k="7" />
<hkern u1="n" u2="v" k="7" />
<hkern u1="n" u2="t" k="10" />
<hkern u1="n" u2="f" k="2" />
<hkern u1="n" u2="\" k="10" />
<hkern u1="n" u2="&#x3f;" k="15" />
<hkern u1="o" u2="&#x2122;" k="30" />
<hkern u1="o" u2="&#x2026;" k="20" />
<hkern u1="o" u2="&#x201e;" k="20" />
<hkern u1="o" u2="&#x201c;" k="31" />
<hkern u1="o" u2="&#x201a;" k="20" />
<hkern u1="o" u2="&#x2018;" k="31" />
<hkern u1="o" u2="z" k="17" />
<hkern u1="o" u2="y" k="17" />
<hkern u1="o" u2="x" k="20" />
<hkern u1="o" u2="w" k="7" />
<hkern u1="o" u2="v" k="17" />
<hkern u1="o" u2="t" k="7" />
<hkern u1="o" u2="f" k="5" />
<hkern u1="o" u2="\" k="10" />
<hkern u1="o" u2="&#x3f;" k="27" />
<hkern u1="o" u2="&#x3b;" k="10" />
<hkern u1="o" u2="&#x3a;" k="10" />
<hkern u1="o" u2="&#x2f;" k="10" />
<hkern u1="o" u2="&#x2e;" k="20" />
<hkern u1="o" u2="&#x2c;" k="20" />
<hkern u1="o" u2="&#x21;" k="9" />
<hkern u1="p" u2="&#x2122;" k="30" />
<hkern u1="p" u2="&#x2026;" k="20" />
<hkern u1="p" u2="&#x201e;" k="20" />
<hkern u1="p" u2="&#x201c;" k="31" />
<hkern u1="p" u2="&#x201a;" k="20" />
<hkern u1="p" u2="&#x2018;" k="31" />
<hkern u1="p" u2="z" k="17" />
<hkern u1="p" u2="y" k="17" />
<hkern u1="p" u2="x" k="20" />
<hkern u1="p" u2="w" k="7" />
<hkern u1="p" u2="v" k="17" />
<hkern u1="p" u2="t" k="7" />
<hkern u1="p" u2="f" k="5" />
<hkern u1="p" u2="\" k="10" />
<hkern u1="p" u2="&#x3f;" k="27" />
<hkern u1="p" u2="&#x3b;" k="10" />
<hkern u1="p" u2="&#x3a;" k="10" />
<hkern u1="p" u2="&#x2f;" k="10" />
<hkern u1="p" u2="&#x2e;" k="20" />
<hkern u1="p" u2="&#x2c;" k="20" />
<hkern u1="p" u2="&#x21;" k="9" />
<hkern u1="r" u2="&#x2026;" k="68" />
<hkern u1="r" u2="&#x201e;" k="68" />
<hkern u1="r" u2="&#x201d;" k="-40" />
<hkern u1="r" u2="&#x201c;" k="-20" />
<hkern u1="r" u2="&#x201a;" k="68" />
<hkern u1="r" u2="&#x2019;" k="-40" />
<hkern u1="r" u2="&#x2018;" k="-20" />
<hkern u1="r" u2="&#x2014;" k="16" />
<hkern u1="r" u2="&#x2013;" k="16" />
<hkern u1="r" u2="&#x153;" k="7" />
<hkern u1="r" u2="&#xe7;" k="7" />
<hkern u1="r" u2="&#xe6;" k="7" />
<hkern u1="r" u2="x" k="2" />
<hkern u1="r" u2="t" k="-11" />
<hkern u1="r" u2="q" k="7" />
<hkern u1="r" u2="o" k="7" />
<hkern u1="r" u2="g" k="10" />
<hkern u1="r" u2="f" k="-8" />
<hkern u1="r" u2="e" k="7" />
<hkern u1="r" u2="d" k="7" />
<hkern u1="r" u2="c" k="7" />
<hkern u1="r" u2="a" k="7" />
<hkern u1="r" u2="&#x3f;" k="-11" />
<hkern u1="r" u2="&#x2f;" k="60" />
<hkern u1="r" u2="&#x2e;" k="68" />
<hkern u1="r" u2="&#x2d;" k="16" />
<hkern u1="r" u2="&#x2c;" k="68" />
<hkern u1="r" u2="&#x2a;" k="-5" />
<hkern u1="r" u2="&#x26;" k="14" />
<hkern u1="s" u2="s" k="-2" />
<hkern u1="s" u2="g" k="5" />
<hkern u1="s" u2="&#x3f;" k="9" />
<hkern u1="t" u2="&#x2026;" k="-10" />
<hkern u1="t" u2="&#x201e;" k="-10" />
<hkern u1="t" u2="&#x201c;" k="-10" />
<hkern u1="t" u2="&#x201a;" k="-10" />
<hkern u1="t" u2="&#x2018;" k="-10" />
<hkern u1="t" u2="&#x153;" k="10" />
<hkern u1="t" u2="&#xe7;" k="10" />
<hkern u1="t" u2="&#xe6;" k="10" />
<hkern u1="t" u2="y" k="7" />
<hkern u1="t" u2="v" k="7" />
<hkern u1="t" u2="t" k="2" />
<hkern u1="t" u2="q" k="10" />
<hkern u1="t" u2="o" k="10" />
<hkern u1="t" u2="e" k="10" />
<hkern u1="t" u2="d" k="10" />
<hkern u1="t" u2="c" k="10" />
<hkern u1="t" u2="a" k="10" />
<hkern u1="t" u2="&#x2e;" k="-10" />
<hkern u1="t" u2="&#x2c;" k="-10" />
<hkern u1="u" u2="&#x3f;" k="5" />
<hkern u1="v" u2="&#x2039;" k="14" />
<hkern u1="v" u2="&#x2026;" k="50" />
<hkern u1="v" u2="&#x201e;" k="50" />
<hkern u1="v" u2="&#x201d;" k="-30" />
<hkern u1="v" u2="&#x201c;" k="-30" />
<hkern u1="v" u2="&#x201a;" k="50" />
<hkern u1="v" u2="&#x2019;" k="-30" />
<hkern u1="v" u2="&#x2018;" k="-30" />
<hkern u1="v" u2="&#x2014;" k="10" />
<hkern u1="v" u2="&#x2013;" k="10" />
<hkern u1="v" u2="&#x153;" k="17" />
<hkern u1="v" u2="&#xe7;" k="17" />
<hkern u1="v" u2="&#xe6;" k="17" />
<hkern u1="v" u2="&#xab;" k="14" />
<hkern u1="v" u2="y" k="7" />
<hkern u1="v" u2="x" k="9" />
<hkern u1="v" u2="w" k="7" />
<hkern u1="v" u2="v" k="7" />
<hkern u1="v" u2="q" k="17" />
<hkern u1="v" u2="o" k="17" />
<hkern u1="v" u2="e" k="17" />
<hkern u1="v" u2="d" k="17" />
<hkern u1="v" u2="c" k="17" />
<hkern u1="v" u2="a" k="17" />
<hkern u1="v" u2="&#x2e;" k="50" />
<hkern u1="v" u2="&#x2d;" k="10" />
<hkern u1="v" u2="&#x2c;" k="50" />
<hkern u1="w" u2="&#x2039;" k="9" />
<hkern u1="w" u2="&#x2026;" k="30" />
<hkern u1="w" u2="&#x201e;" k="30" />
<hkern u1="w" u2="&#x201d;" k="-20" />
<hkern u1="w" u2="&#x201c;" k="-20" />
<hkern u1="w" u2="&#x201a;" k="30" />
<hkern u1="w" u2="&#x2019;" k="-20" />
<hkern u1="w" u2="&#x2018;" k="-20" />
<hkern u1="w" u2="&#x153;" k="7" />
<hkern u1="w" u2="&#xe7;" k="7" />
<hkern u1="w" u2="&#xe6;" k="7" />
<hkern u1="w" u2="&#xab;" k="9" />
<hkern u1="w" u2="y" k="7" />
<hkern u1="w" u2="x" k="7" />
<hkern u1="w" u2="w" k="7" />
<hkern u1="w" u2="v" k="7" />
<hkern u1="w" u2="q" k="7" />
<hkern u1="w" u2="o" k="7" />
<hkern u1="w" u2="e" k="7" />
<hkern u1="w" u2="d" k="7" />
<hkern u1="w" u2="c" k="7" />
<hkern u1="w" u2="a" k="7" />
<hkern u1="w" u2="&#x2e;" k="30" />
<hkern u1="w" u2="&#x2c;" k="30" />
<hkern u1="x" u2="&#x2039;" k="50" />
<hkern u1="x" u2="&#x201d;" k="-20" />
<hkern u1="x" u2="&#x201c;" k="-10" />
<hkern u1="x" u2="&#x2019;" k="-20" />
<hkern u1="x" u2="&#x2018;" k="-10" />
<hkern u1="x" u2="&#x2014;" k="30" />
<hkern u1="x" u2="&#x2013;" k="30" />
<hkern u1="x" u2="&#x153;" k="20" />
<hkern u1="x" u2="&#xe7;" k="20" />
<hkern u1="x" u2="&#xe6;" k="20" />
<hkern u1="x" u2="&#xab;" k="50" />
<hkern u1="x" u2="y" k="9" />
<hkern u1="x" u2="w" k="7" />
<hkern u1="x" u2="v" k="9" />
<hkern u1="x" u2="q" k="20" />
<hkern u1="x" u2="o" k="20" />
<hkern u1="x" u2="e" k="20" />
<hkern u1="x" u2="d" k="20" />
<hkern u1="x" u2="c" k="20" />
<hkern u1="x" u2="a" k="20" />
<hkern u1="x" u2="&#x2d;" k="30" />
<hkern u1="y" u2="&#x2039;" k="14" />
<hkern u1="y" u2="&#x2026;" k="50" />
<hkern u1="y" u2="&#x201e;" k="50" />
<hkern u1="y" u2="&#x201d;" k="-30" />
<hkern u1="y" u2="&#x201c;" k="-30" />
<hkern u1="y" u2="&#x201a;" k="50" />
<hkern u1="y" u2="&#x2019;" k="-30" />
<hkern u1="y" u2="&#x2018;" k="-30" />
<hkern u1="y" u2="&#x2014;" k="10" />
<hkern u1="y" u2="&#x2013;" k="10" />
<hkern u1="y" u2="&#x153;" k="17" />
<hkern u1="y" u2="&#xe7;" k="17" />
<hkern u1="y" u2="&#xe6;" k="17" />
<hkern u1="y" u2="&#xab;" k="14" />
<hkern u1="y" u2="y" k="7" />
<hkern u1="y" u2="x" k="9" />
<hkern u1="y" u2="w" k="7" />
<hkern u1="y" u2="v" k="7" />
<hkern u1="y" u2="q" k="17" />
<hkern u1="y" u2="o" k="17" />
<hkern u1="y" u2="e" k="17" />
<hkern u1="y" u2="d" k="17" />
<hkern u1="y" u2="c" k="17" />
<hkern u1="y" u2="a" k="17" />
<hkern u1="y" u2="&#x2e;" k="50" />
<hkern u1="y" u2="&#x2d;" k="10" />
<hkern u1="y" u2="&#x2c;" k="50" />
<hkern u1="z" u2="&#x2039;" k="51" />
<hkern u1="z" u2="&#x2014;" k="21" />
<hkern u1="z" u2="&#x2013;" k="21" />
<hkern u1="z" u2="&#x153;" k="20" />
<hkern u1="z" u2="&#xe7;" k="20" />
<hkern u1="z" u2="&#xe6;" k="20" />
<hkern u1="z" u2="&#xab;" k="51" />
<hkern u1="z" u2="q" k="20" />
<hkern u1="z" u2="o" k="20" />
<hkern u1="z" u2="g" k="6" />
<hkern u1="z" u2="e" k="20" />
<hkern u1="z" u2="d" k="20" />
<hkern u1="z" u2="c" k="20" />
<hkern u1="z" u2="a" k="20" />
<hkern u1="z" u2="&#x2d;" k="21" />
<hkern u1="&#x7b;" u2="&#x178;" k="-50" />
<hkern u1="&#x7b;" u2="&#xdd;" k="-50" />
<hkern u1="&#x7b;" u2="j" k="-150" />
<hkern u1="&#x7b;" u2="g" k="-20" />
<hkern u1="&#x7b;" u2="Y" k="-50" />
<hkern u1="&#x7b;" u2="X" k="-40" />
<hkern u1="&#x7b;" u2="W" k="-10" />
<hkern u1="&#x7b;" u2="V" k="-30" />
<hkern u1="&#x7b;" u2="J" k="9" />
<hkern u1="&#x7b;" u2="&#x37;" k="-9" />
<hkern u1="&#xa1;" u2="&#x178;" k="9" />
<hkern u1="&#xa1;" u2="&#xdd;" k="9" />
<hkern u1="&#xa1;" u2="y" k="18" />
<hkern u1="&#xa1;" u2="w" k="11" />
<hkern u1="&#xa1;" u2="v" k="18" />
<hkern u1="&#xa1;" u2="f" k="9" />
<hkern u1="&#xa1;" u2="Y" k="9" />
<hkern u1="&#xa1;" u2="W" k="9" />
<hkern u1="&#xa1;" u2="V" k="9" />
<hkern u1="&#xa3;" u2="&#x34;" k="20" />
<hkern u1="&#xa3;" u2="&#x31;" k="-10" />
<hkern u1="&#xa4;" u2="&#x34;" k="20" />
<hkern u1="&#xa9;" u2="&#x178;" k="30" />
<hkern u1="&#xa9;" u2="&#xdd;" k="30" />
<hkern u1="&#xa9;" u2="&#xc6;" k="10" />
<hkern u1="&#xa9;" u2="&#xc5;" k="10" />
<hkern u1="&#xa9;" u2="&#xc4;" k="10" />
<hkern u1="&#xa9;" u2="&#xc3;" k="10" />
<hkern u1="&#xa9;" u2="&#xc2;" k="10" />
<hkern u1="&#xa9;" u2="&#xc1;" k="10" />
<hkern u1="&#xa9;" u2="&#xc0;" k="10" />
<hkern u1="&#xa9;" u2="g" k="-5" />
<hkern u1="&#xa9;" u2="Y" k="30" />
<hkern u1="&#xa9;" u2="X" k="14" />
<hkern u1="&#xa9;" u2="W" k="20" />
<hkern u1="&#xa9;" u2="V" k="5" />
<hkern u1="&#xa9;" u2="T" k="30" />
<hkern u1="&#xa9;" u2="A" k="10" />
<hkern u1="&#xa9;" u2="&#x33;" k="5" />
<hkern u1="&#xab;" u2="&#x178;" k="40" />
<hkern u1="&#xab;" u2="&#xdd;" k="40" />
<hkern u1="&#xab;" u2="f" k="-9" />
<hkern u1="&#xab;" u2="Y" k="40" />
<hkern u1="&#xab;" u2="W" k="20" />
<hkern u1="&#xab;" u2="V" k="20" />
<hkern u1="&#xab;" u2="T" k="40" />
<hkern u1="&#xae;" u2="&#x178;" k="30" />
<hkern u1="&#xae;" u2="&#xdd;" k="30" />
<hkern u1="&#xae;" u2="&#xc6;" k="10" />
<hkern u1="&#xae;" u2="&#xc5;" k="10" />
<hkern u1="&#xae;" u2="&#xc4;" k="10" />
<hkern u1="&#xae;" u2="&#xc3;" k="10" />
<hkern u1="&#xae;" u2="&#xc2;" k="10" />
<hkern u1="&#xae;" u2="&#xc1;" k="10" />
<hkern u1="&#xae;" u2="&#xc0;" k="10" />
<hkern u1="&#xae;" u2="g" k="-5" />
<hkern u1="&#xae;" u2="Y" k="30" />
<hkern u1="&#xae;" u2="X" k="14" />
<hkern u1="&#xae;" u2="W" k="20" />
<hkern u1="&#xae;" u2="V" k="5" />
<hkern u1="&#xae;" u2="T" k="30" />
<hkern u1="&#xae;" u2="A" k="10" />
<hkern u1="&#xae;" u2="&#x33;" k="5" />
<hkern u1="&#xb0;" u2="&#x34;" k="36" />
<hkern u1="&#xbb;" u2="&#x178;" k="50" />
<hkern u1="&#xbb;" u2="&#xdd;" k="50" />
<hkern u1="&#xbb;" u2="z" k="41" />
<hkern u1="&#xbb;" u2="y" k="14" />
<hkern u1="&#xbb;" u2="x" k="50" />
<hkern u1="&#xbb;" u2="w" k="9" />
<hkern u1="&#xbb;" u2="v" k="14" />
<hkern u1="&#xbb;" u2="f" k="5" />
<hkern u1="&#xbb;" u2="Y" k="50" />
<hkern u1="&#xbb;" u2="W" k="40" />
<hkern u1="&#xbb;" u2="V" k="50" />
<hkern u1="&#xbb;" u2="T" k="110" />
<hkern u1="&#xbf;" u2="&#x178;" k="49" />
<hkern u1="&#xbf;" u2="&#x153;" k="-5" />
<hkern u1="&#xbf;" u2="&#x152;" k="20" />
<hkern u1="&#xbf;" u2="&#xe7;" k="-5" />
<hkern u1="&#xbf;" u2="&#xe6;" k="-5" />
<hkern u1="&#xbf;" u2="&#xdd;" k="49" />
<hkern u1="&#xbf;" u2="&#xd8;" k="20" />
<hkern u1="&#xbf;" u2="&#xd6;" k="20" />
<hkern u1="&#xbf;" u2="&#xd5;" k="20" />
<hkern u1="&#xbf;" u2="&#xd4;" k="20" />
<hkern u1="&#xbf;" u2="&#xd3;" k="20" />
<hkern u1="&#xbf;" u2="&#xd2;" k="20" />
<hkern u1="&#xbf;" u2="y" k="39" />
<hkern u1="&#xbf;" u2="x" k="10" />
<hkern u1="&#xbf;" u2="w" k="29" />
<hkern u1="&#xbf;" u2="v" k="39" />
<hkern u1="&#xbf;" u2="t" k="18" />
<hkern u1="&#xbf;" u2="s" k="-5" />
<hkern u1="&#xbf;" u2="q" k="-5" />
<hkern u1="&#xbf;" u2="o" k="-5" />
<hkern u1="&#xbf;" u2="e" k="-5" />
<hkern u1="&#xbf;" u2="d" k="-5" />
<hkern u1="&#xbf;" u2="c" k="-5" />
<hkern u1="&#xbf;" u2="a" k="-5" />
<hkern u1="&#xbf;" u2="Y" k="49" />
<hkern u1="&#xbf;" u2="W" k="39" />
<hkern u1="&#xbf;" u2="V" k="59" />
<hkern u1="&#xbf;" u2="T" k="65" />
<hkern u1="&#xbf;" u2="Q" k="20" />
<hkern u1="&#xbf;" u2="O" k="20" />
<hkern u1="&#xbf;" u2="G" k="20" />
<hkern u1="&#xbf;" u2="C" k="20" />
<hkern u1="&#xbf;" u2="&#x37;" k="35" />
<hkern u1="&#xbf;" u2="&#x34;" k="-5" />
<hkern u1="&#xbf;" u2="&#x33;" k="-6" />
<hkern u1="&#xbf;" u2="&#x32;" k="-9" />
<hkern u1="&#xbf;" u2="&#x31;" k="49" />
<hkern u1="&#xc0;" u2="&#x2122;" k="70" />
<hkern u1="&#xc0;" u2="&#x2026;" k="-30" />
<hkern u1="&#xc0;" u2="&#x201e;" k="-30" />
<hkern u1="&#xc0;" u2="&#x201d;" k="30" />
<hkern u1="&#xc0;" u2="&#x201c;" k="60" />
<hkern u1="&#xc0;" u2="&#x201a;" k="-30" />
<hkern u1="&#xc0;" u2="&#x2019;" k="30" />
<hkern u1="&#xc0;" u2="&#x2018;" k="60" />
<hkern u1="&#xc0;" u2="&#x2014;" k="-10" />
<hkern u1="&#xc0;" u2="&#x2013;" k="-10" />
<hkern u1="&#xc0;" u2="&#x178;" k="52" />
<hkern u1="&#xc0;" u2="&#x153;" k="5" />
<hkern u1="&#xc0;" u2="&#x152;" k="20" />
<hkern u1="&#xc0;" u2="&#xe7;" k="5" />
<hkern u1="&#xc0;" u2="&#xe6;" k="5" />
<hkern u1="&#xc0;" u2="&#xdd;" k="52" />
<hkern u1="&#xc0;" u2="&#xd8;" k="20" />
<hkern u1="&#xc0;" u2="&#xd6;" k="20" />
<hkern u1="&#xc0;" u2="&#xd5;" k="20" />
<hkern u1="&#xc0;" u2="&#xd4;" k="20" />
<hkern u1="&#xc0;" u2="&#xd3;" k="20" />
<hkern u1="&#xc0;" u2="&#xd2;" k="20" />
<hkern u1="&#xc0;" u2="&#xc6;" k="-10" />
<hkern u1="&#xc0;" u2="&#xc5;" k="-10" />
<hkern u1="&#xc0;" u2="&#xc4;" k="-10" />
<hkern u1="&#xc0;" u2="&#xc3;" k="-10" />
<hkern u1="&#xc0;" u2="&#xc2;" k="-10" />
<hkern u1="&#xc0;" u2="&#xc1;" k="-10" />
<hkern u1="&#xc0;" u2="&#xc0;" k="-10" />
<hkern u1="&#xc0;" u2="&#xae;" k="10" />
<hkern u1="&#xc0;" u2="&#xa9;" k="10" />
<hkern u1="&#xc0;" u2="z" k="-10" />
<hkern u1="&#xc0;" u2="y" k="48" />
<hkern u1="&#xc0;" u2="x" k="-16" />
<hkern u1="&#xc0;" u2="w" k="28" />
<hkern u1="&#xc0;" u2="v" k="48" />
<hkern u1="&#xc0;" u2="u" k="5" />
<hkern u1="&#xc0;" u2="t" k="5" />
<hkern u1="&#xc0;" u2="s" k="-7" />
<hkern u1="&#xc0;" u2="r" k="5" />
<hkern u1="&#xc0;" u2="q" k="5" />
<hkern u1="&#xc0;" u2="p" k="5" />
<hkern u1="&#xc0;" u2="o" k="5" />
<hkern u1="&#xc0;" u2="n" k="5" />
<hkern u1="&#xc0;" u2="m" k="5" />
<hkern u1="&#xc0;" u2="g" k="7" />
<hkern u1="&#xc0;" u2="f" k="18" />
<hkern u1="&#xc0;" u2="e" k="5" />
<hkern u1="&#xc0;" u2="d" k="5" />
<hkern u1="&#xc0;" u2="c" k="5" />
<hkern u1="&#xc0;" u2="a" k="5" />
<hkern u1="&#xc0;" u2="Y" k="52" />
<hkern u1="&#xc0;" u2="X" k="-10" />
<hkern u1="&#xc0;" u2="W" k="57" />
<hkern u1="&#xc0;" u2="V" k="64" />
<hkern u1="&#xc0;" u2="T" k="63" />
<hkern u1="&#xc0;" u2="S" k="-5" />
<hkern u1="&#xc0;" u2="Q" k="20" />
<hkern u1="&#xc0;" u2="O" k="20" />
<hkern u1="&#xc0;" u2="J" k="-10" />
<hkern u1="&#xc0;" u2="G" k="20" />
<hkern u1="&#xc0;" u2="C" k="20" />
<hkern u1="&#xc0;" u2="A" k="-10" />
<hkern u1="&#xc0;" u2="&#x40;" k="10" />
<hkern u1="&#xc0;" u2="&#x3f;" k="14" />
<hkern u1="&#xc0;" u2="&#x2e;" k="-30" />
<hkern u1="&#xc0;" u2="&#x2d;" k="-10" />
<hkern u1="&#xc0;" u2="&#x2c;" k="-30" />
<hkern u1="&#xc0;" u2="&#x2a;" k="69" />
<hkern u1="&#xc1;" u2="&#x2122;" k="70" />
<hkern u1="&#xc1;" u2="&#x2026;" k="-30" />
<hkern u1="&#xc1;" u2="&#x201e;" k="-30" />
<hkern u1="&#xc1;" u2="&#x201d;" k="30" />
<hkern u1="&#xc1;" u2="&#x201c;" k="60" />
<hkern u1="&#xc1;" u2="&#x201a;" k="-30" />
<hkern u1="&#xc1;" u2="&#x2019;" k="30" />
<hkern u1="&#xc1;" u2="&#x2018;" k="60" />
<hkern u1="&#xc1;" u2="&#x2014;" k="-10" />
<hkern u1="&#xc1;" u2="&#x2013;" k="-10" />
<hkern u1="&#xc1;" u2="&#x178;" k="52" />
<hkern u1="&#xc1;" u2="&#x153;" k="5" />
<hkern u1="&#xc1;" u2="&#x152;" k="20" />
<hkern u1="&#xc1;" u2="&#xe7;" k="5" />
<hkern u1="&#xc1;" u2="&#xe6;" k="5" />
<hkern u1="&#xc1;" u2="&#xdd;" k="52" />
<hkern u1="&#xc1;" u2="&#xd8;" k="20" />
<hkern u1="&#xc1;" u2="&#xd6;" k="20" />
<hkern u1="&#xc1;" u2="&#xd5;" k="20" />
<hkern u1="&#xc1;" u2="&#xd4;" k="20" />
<hkern u1="&#xc1;" u2="&#xd3;" k="20" />
<hkern u1="&#xc1;" u2="&#xd2;" k="20" />
<hkern u1="&#xc1;" u2="&#xc6;" k="-10" />
<hkern u1="&#xc1;" u2="&#xc5;" k="-10" />
<hkern u1="&#xc1;" u2="&#xc4;" k="-10" />
<hkern u1="&#xc1;" u2="&#xc3;" k="-10" />
<hkern u1="&#xc1;" u2="&#xc2;" k="-10" />
<hkern u1="&#xc1;" u2="&#xc1;" k="-10" />
<hkern u1="&#xc1;" u2="&#xc0;" k="-10" />
<hkern u1="&#xc1;" u2="&#xae;" k="10" />
<hkern u1="&#xc1;" u2="&#xa9;" k="10" />
<hkern u1="&#xc1;" u2="z" k="-10" />
<hkern u1="&#xc1;" u2="y" k="48" />
<hkern u1="&#xc1;" u2="x" k="-16" />
<hkern u1="&#xc1;" u2="w" k="28" />
<hkern u1="&#xc1;" u2="v" k="48" />
<hkern u1="&#xc1;" u2="u" k="5" />
<hkern u1="&#xc1;" u2="t" k="5" />
<hkern u1="&#xc1;" u2="s" k="-7" />
<hkern u1="&#xc1;" u2="r" k="5" />
<hkern u1="&#xc1;" u2="q" k="5" />
<hkern u1="&#xc1;" u2="p" k="5" />
<hkern u1="&#xc1;" u2="o" k="5" />
<hkern u1="&#xc1;" u2="n" k="5" />
<hkern u1="&#xc1;" u2="m" k="5" />
<hkern u1="&#xc1;" u2="g" k="7" />
<hkern u1="&#xc1;" u2="f" k="18" />
<hkern u1="&#xc1;" u2="e" k="5" />
<hkern u1="&#xc1;" u2="d" k="5" />
<hkern u1="&#xc1;" u2="c" k="5" />
<hkern u1="&#xc1;" u2="a" k="5" />
<hkern u1="&#xc1;" u2="Y" k="52" />
<hkern u1="&#xc1;" u2="X" k="-10" />
<hkern u1="&#xc1;" u2="W" k="57" />
<hkern u1="&#xc1;" u2="V" k="64" />
<hkern u1="&#xc1;" u2="T" k="63" />
<hkern u1="&#xc1;" u2="S" k="-5" />
<hkern u1="&#xc1;" u2="Q" k="20" />
<hkern u1="&#xc1;" u2="O" k="20" />
<hkern u1="&#xc1;" u2="J" k="-10" />
<hkern u1="&#xc1;" u2="G" k="20" />
<hkern u1="&#xc1;" u2="C" k="20" />
<hkern u1="&#xc1;" u2="A" k="-10" />
<hkern u1="&#xc1;" u2="&#x40;" k="10" />
<hkern u1="&#xc1;" u2="&#x3f;" k="14" />
<hkern u1="&#xc1;" u2="&#x2e;" k="-30" />
<hkern u1="&#xc1;" u2="&#x2d;" k="-10" />
<hkern u1="&#xc1;" u2="&#x2c;" k="-30" />
<hkern u1="&#xc1;" u2="&#x2a;" k="69" />
<hkern u1="&#xc2;" u2="&#x2122;" k="70" />
<hkern u1="&#xc2;" u2="&#x2026;" k="-30" />
<hkern u1="&#xc2;" u2="&#x201e;" k="-30" />
<hkern u1="&#xc2;" u2="&#x201d;" k="30" />
<hkern u1="&#xc2;" u2="&#x201c;" k="60" />
<hkern u1="&#xc2;" u2="&#x201a;" k="-30" />
<hkern u1="&#xc2;" u2="&#x2019;" k="30" />
<hkern u1="&#xc2;" u2="&#x2018;" k="60" />
<hkern u1="&#xc2;" u2="&#x2014;" k="-10" />
<hkern u1="&#xc2;" u2="&#x2013;" k="-10" />
<hkern u1="&#xc2;" u2="&#x178;" k="52" />
<hkern u1="&#xc2;" u2="&#x153;" k="5" />
<hkern u1="&#xc2;" u2="&#x152;" k="20" />
<hkern u1="&#xc2;" u2="&#xe7;" k="5" />
<hkern u1="&#xc2;" u2="&#xe6;" k="5" />
<hkern u1="&#xc2;" u2="&#xdd;" k="52" />
<hkern u1="&#xc2;" u2="&#xd8;" k="20" />
<hkern u1="&#xc2;" u2="&#xd6;" k="20" />
<hkern u1="&#xc2;" u2="&#xd5;" k="20" />
<hkern u1="&#xc2;" u2="&#xd4;" k="20" />
<hkern u1="&#xc2;" u2="&#xd3;" k="20" />
<hkern u1="&#xc2;" u2="&#xd2;" k="20" />
<hkern u1="&#xc2;" u2="&#xc6;" k="-10" />
<hkern u1="&#xc2;" u2="&#xc5;" k="-10" />
<hkern u1="&#xc2;" u2="&#xc4;" k="-10" />
<hkern u1="&#xc2;" u2="&#xc3;" k="-10" />
<hkern u1="&#xc2;" u2="&#xc2;" k="-10" />
<hkern u1="&#xc2;" u2="&#xc1;" k="-10" />
<hkern u1="&#xc2;" u2="&#xc0;" k="-10" />
<hkern u1="&#xc2;" u2="&#xae;" k="10" />
<hkern u1="&#xc2;" u2="&#xa9;" k="10" />
<hkern u1="&#xc2;" u2="z" k="-10" />
<hkern u1="&#xc2;" u2="y" k="48" />
<hkern u1="&#xc2;" u2="x" k="-16" />
<hkern u1="&#xc2;" u2="w" k="28" />
<hkern u1="&#xc2;" u2="v" k="48" />
<hkern u1="&#xc2;" u2="u" k="5" />
<hkern u1="&#xc2;" u2="t" k="5" />
<hkern u1="&#xc2;" u2="s" k="-7" />
<hkern u1="&#xc2;" u2="r" k="5" />
<hkern u1="&#xc2;" u2="q" k="5" />
<hkern u1="&#xc2;" u2="p" k="5" />
<hkern u1="&#xc2;" u2="o" k="5" />
<hkern u1="&#xc2;" u2="n" k="5" />
<hkern u1="&#xc2;" u2="m" k="5" />
<hkern u1="&#xc2;" u2="g" k="7" />
<hkern u1="&#xc2;" u2="f" k="18" />
<hkern u1="&#xc2;" u2="e" k="5" />
<hkern u1="&#xc2;" u2="d" k="5" />
<hkern u1="&#xc2;" u2="c" k="5" />
<hkern u1="&#xc2;" u2="a" k="5" />
<hkern u1="&#xc2;" u2="Y" k="52" />
<hkern u1="&#xc2;" u2="X" k="-10" />
<hkern u1="&#xc2;" u2="W" k="57" />
<hkern u1="&#xc2;" u2="V" k="64" />
<hkern u1="&#xc2;" u2="T" k="63" />
<hkern u1="&#xc2;" u2="S" k="-5" />
<hkern u1="&#xc2;" u2="Q" k="20" />
<hkern u1="&#xc2;" u2="O" k="20" />
<hkern u1="&#xc2;" u2="J" k="-10" />
<hkern u1="&#xc2;" u2="G" k="20" />
<hkern u1="&#xc2;" u2="C" k="20" />
<hkern u1="&#xc2;" u2="A" k="-10" />
<hkern u1="&#xc2;" u2="&#x40;" k="10" />
<hkern u1="&#xc2;" u2="&#x3f;" k="14" />
<hkern u1="&#xc2;" u2="&#x2e;" k="-30" />
<hkern u1="&#xc2;" u2="&#x2d;" k="-10" />
<hkern u1="&#xc2;" u2="&#x2c;" k="-30" />
<hkern u1="&#xc2;" u2="&#x2a;" k="69" />
<hkern u1="&#xc3;" u2="&#x2122;" k="70" />
<hkern u1="&#xc3;" u2="&#x2026;" k="-30" />
<hkern u1="&#xc3;" u2="&#x201e;" k="-30" />
<hkern u1="&#xc3;" u2="&#x201d;" k="30" />
<hkern u1="&#xc3;" u2="&#x201c;" k="60" />
<hkern u1="&#xc3;" u2="&#x201a;" k="-30" />
<hkern u1="&#xc3;" u2="&#x2019;" k="30" />
<hkern u1="&#xc3;" u2="&#x2018;" k="60" />
<hkern u1="&#xc3;" u2="&#x2014;" k="-10" />
<hkern u1="&#xc3;" u2="&#x2013;" k="-10" />
<hkern u1="&#xc3;" u2="&#x178;" k="52" />
<hkern u1="&#xc3;" u2="&#x153;" k="5" />
<hkern u1="&#xc3;" u2="&#x152;" k="20" />
<hkern u1="&#xc3;" u2="&#xe7;" k="5" />
<hkern u1="&#xc3;" u2="&#xe6;" k="5" />
<hkern u1="&#xc3;" u2="&#xdd;" k="52" />
<hkern u1="&#xc3;" u2="&#xd8;" k="20" />
<hkern u1="&#xc3;" u2="&#xd6;" k="20" />
<hkern u1="&#xc3;" u2="&#xd5;" k="20" />
<hkern u1="&#xc3;" u2="&#xd4;" k="20" />
<hkern u1="&#xc3;" u2="&#xd3;" k="20" />
<hkern u1="&#xc3;" u2="&#xd2;" k="20" />
<hkern u1="&#xc3;" u2="&#xc6;" k="-10" />
<hkern u1="&#xc3;" u2="&#xc5;" k="-10" />
<hkern u1="&#xc3;" u2="&#xc4;" k="-10" />
<hkern u1="&#xc3;" u2="&#xc3;" k="-10" />
<hkern u1="&#xc3;" u2="&#xc2;" k="-10" />
<hkern u1="&#xc3;" u2="&#xc1;" k="-10" />
<hkern u1="&#xc3;" u2="&#xc0;" k="-10" />
<hkern u1="&#xc3;" u2="&#xae;" k="10" />
<hkern u1="&#xc3;" u2="&#xa9;" k="10" />
<hkern u1="&#xc3;" u2="z" k="-10" />
<hkern u1="&#xc3;" u2="y" k="48" />
<hkern u1="&#xc3;" u2="x" k="-16" />
<hkern u1="&#xc3;" u2="w" k="28" />
<hkern u1="&#xc3;" u2="v" k="48" />
<hkern u1="&#xc3;" u2="u" k="5" />
<hkern u1="&#xc3;" u2="t" k="5" />
<hkern u1="&#xc3;" u2="s" k="-7" />
<hkern u1="&#xc3;" u2="r" k="5" />
<hkern u1="&#xc3;" u2="q" k="5" />
<hkern u1="&#xc3;" u2="p" k="5" />
<hkern u1="&#xc3;" u2="o" k="5" />
<hkern u1="&#xc3;" u2="n" k="5" />
<hkern u1="&#xc3;" u2="m" k="5" />
<hkern u1="&#xc3;" u2="g" k="7" />
<hkern u1="&#xc3;" u2="f" k="18" />
<hkern u1="&#xc3;" u2="e" k="5" />
<hkern u1="&#xc3;" u2="d" k="5" />
<hkern u1="&#xc3;" u2="c" k="5" />
<hkern u1="&#xc3;" u2="a" k="5" />
<hkern u1="&#xc3;" u2="Y" k="52" />
<hkern u1="&#xc3;" u2="X" k="-10" />
<hkern u1="&#xc3;" u2="W" k="57" />
<hkern u1="&#xc3;" u2="V" k="64" />
<hkern u1="&#xc3;" u2="T" k="63" />
<hkern u1="&#xc3;" u2="S" k="-5" />
<hkern u1="&#xc3;" u2="Q" k="20" />
<hkern u1="&#xc3;" u2="O" k="20" />
<hkern u1="&#xc3;" u2="J" k="-10" />
<hkern u1="&#xc3;" u2="G" k="20" />
<hkern u1="&#xc3;" u2="C" k="20" />
<hkern u1="&#xc3;" u2="A" k="-10" />
<hkern u1="&#xc3;" u2="&#x40;" k="10" />
<hkern u1="&#xc3;" u2="&#x3f;" k="14" />
<hkern u1="&#xc3;" u2="&#x2e;" k="-30" />
<hkern u1="&#xc3;" u2="&#x2d;" k="-10" />
<hkern u1="&#xc3;" u2="&#x2c;" k="-30" />
<hkern u1="&#xc3;" u2="&#x2a;" k="69" />
<hkern u1="&#xc4;" u2="&#x2122;" k="70" />
<hkern u1="&#xc4;" u2="&#x2026;" k="-30" />
<hkern u1="&#xc4;" u2="&#x201e;" k="-30" />
<hkern u1="&#xc4;" u2="&#x201d;" k="30" />
<hkern u1="&#xc4;" u2="&#x201c;" k="60" />
<hkern u1="&#xc4;" u2="&#x201a;" k="-30" />
<hkern u1="&#xc4;" u2="&#x2019;" k="30" />
<hkern u1="&#xc4;" u2="&#x2018;" k="60" />
<hkern u1="&#xc4;" u2="&#x2014;" k="-10" />
<hkern u1="&#xc4;" u2="&#x2013;" k="-10" />
<hkern u1="&#xc4;" u2="&#x178;" k="52" />
<hkern u1="&#xc4;" u2="&#x153;" k="5" />
<hkern u1="&#xc4;" u2="&#x152;" k="20" />
<hkern u1="&#xc4;" u2="&#xe7;" k="5" />
<hkern u1="&#xc4;" u2="&#xe6;" k="5" />
<hkern u1="&#xc4;" u2="&#xdd;" k="52" />
<hkern u1="&#xc4;" u2="&#xd8;" k="20" />
<hkern u1="&#xc4;" u2="&#xd6;" k="20" />
<hkern u1="&#xc4;" u2="&#xd5;" k="20" />
<hkern u1="&#xc4;" u2="&#xd4;" k="20" />
<hkern u1="&#xc4;" u2="&#xd3;" k="20" />
<hkern u1="&#xc4;" u2="&#xd2;" k="20" />
<hkern u1="&#xc4;" u2="&#xc6;" k="-10" />
<hkern u1="&#xc4;" u2="&#xc5;" k="-10" />
<hkern u1="&#xc4;" u2="&#xc4;" k="-10" />
<hkern u1="&#xc4;" u2="&#xc3;" k="-10" />
<hkern u1="&#xc4;" u2="&#xc2;" k="-10" />
<hkern u1="&#xc4;" u2="&#xc1;" k="-10" />
<hkern u1="&#xc4;" u2="&#xc0;" k="-10" />
<hkern u1="&#xc4;" u2="&#xae;" k="10" />
<hkern u1="&#xc4;" u2="&#xa9;" k="10" />
<hkern u1="&#xc4;" u2="z" k="-10" />
<hkern u1="&#xc4;" u2="y" k="48" />
<hkern u1="&#xc4;" u2="x" k="-16" />
<hkern u1="&#xc4;" u2="w" k="28" />
<hkern u1="&#xc4;" u2="v" k="48" />
<hkern u1="&#xc4;" u2="u" k="5" />
<hkern u1="&#xc4;" u2="t" k="5" />
<hkern u1="&#xc4;" u2="s" k="-7" />
<hkern u1="&#xc4;" u2="r" k="5" />
<hkern u1="&#xc4;" u2="q" k="5" />
<hkern u1="&#xc4;" u2="p" k="5" />
<hkern u1="&#xc4;" u2="o" k="5" />
<hkern u1="&#xc4;" u2="n" k="5" />
<hkern u1="&#xc4;" u2="m" k="5" />
<hkern u1="&#xc4;" u2="g" k="7" />
<hkern u1="&#xc4;" u2="f" k="18" />
<hkern u1="&#xc4;" u2="e" k="5" />
<hkern u1="&#xc4;" u2="d" k="5" />
<hkern u1="&#xc4;" u2="c" k="5" />
<hkern u1="&#xc4;" u2="a" k="5" />
<hkern u1="&#xc4;" u2="Y" k="52" />
<hkern u1="&#xc4;" u2="X" k="-10" />
<hkern u1="&#xc4;" u2="W" k="57" />
<hkern u1="&#xc4;" u2="V" k="64" />
<hkern u1="&#xc4;" u2="T" k="63" />
<hkern u1="&#xc4;" u2="S" k="-5" />
<hkern u1="&#xc4;" u2="Q" k="20" />
<hkern u1="&#xc4;" u2="O" k="20" />
<hkern u1="&#xc4;" u2="J" k="-10" />
<hkern u1="&#xc4;" u2="G" k="20" />
<hkern u1="&#xc4;" u2="C" k="20" />
<hkern u1="&#xc4;" u2="A" k="-10" />
<hkern u1="&#xc4;" u2="&#x40;" k="10" />
<hkern u1="&#xc4;" u2="&#x3f;" k="14" />
<hkern u1="&#xc4;" u2="&#x2e;" k="-30" />
<hkern u1="&#xc4;" u2="&#x2d;" k="-10" />
<hkern u1="&#xc4;" u2="&#x2c;" k="-30" />
<hkern u1="&#xc4;" u2="&#x2a;" k="69" />
<hkern u1="&#xc5;" u2="&#x2122;" k="70" />
<hkern u1="&#xc5;" u2="&#x2026;" k="-30" />
<hkern u1="&#xc5;" u2="&#x201e;" k="-30" />
<hkern u1="&#xc5;" u2="&#x201d;" k="30" />
<hkern u1="&#xc5;" u2="&#x201c;" k="60" />
<hkern u1="&#xc5;" u2="&#x201a;" k="-30" />
<hkern u1="&#xc5;" u2="&#x2019;" k="30" />
<hkern u1="&#xc5;" u2="&#x2018;" k="60" />
<hkern u1="&#xc5;" u2="&#x2014;" k="-10" />
<hkern u1="&#xc5;" u2="&#x2013;" k="-10" />
<hkern u1="&#xc5;" u2="&#x178;" k="52" />
<hkern u1="&#xc5;" u2="&#x153;" k="5" />
<hkern u1="&#xc5;" u2="&#x152;" k="20" />
<hkern u1="&#xc5;" u2="&#xe7;" k="5" />
<hkern u1="&#xc5;" u2="&#xe6;" k="5" />
<hkern u1="&#xc5;" u2="&#xdd;" k="52" />
<hkern u1="&#xc5;" u2="&#xd8;" k="20" />
<hkern u1="&#xc5;" u2="&#xd6;" k="20" />
<hkern u1="&#xc5;" u2="&#xd5;" k="20" />
<hkern u1="&#xc5;" u2="&#xd4;" k="20" />
<hkern u1="&#xc5;" u2="&#xd3;" k="20" />
<hkern u1="&#xc5;" u2="&#xd2;" k="20" />
<hkern u1="&#xc5;" u2="&#xc6;" k="-10" />
<hkern u1="&#xc5;" u2="&#xc5;" k="-10" />
<hkern u1="&#xc5;" u2="&#xc4;" k="-10" />
<hkern u1="&#xc5;" u2="&#xc3;" k="-10" />
<hkern u1="&#xc5;" u2="&#xc2;" k="-10" />
<hkern u1="&#xc5;" u2="&#xc1;" k="-10" />
<hkern u1="&#xc5;" u2="&#xc0;" k="-10" />
<hkern u1="&#xc5;" u2="&#xae;" k="10" />
<hkern u1="&#xc5;" u2="&#xa9;" k="10" />
<hkern u1="&#xc5;" u2="z" k="-10" />
<hkern u1="&#xc5;" u2="y" k="48" />
<hkern u1="&#xc5;" u2="x" k="-16" />
<hkern u1="&#xc5;" u2="w" k="28" />
<hkern u1="&#xc5;" u2="v" k="48" />
<hkern u1="&#xc5;" u2="u" k="5" />
<hkern u1="&#xc5;" u2="t" k="5" />
<hkern u1="&#xc5;" u2="s" k="-7" />
<hkern u1="&#xc5;" u2="r" k="5" />
<hkern u1="&#xc5;" u2="q" k="5" />
<hkern u1="&#xc5;" u2="p" k="5" />
<hkern u1="&#xc5;" u2="o" k="5" />
<hkern u1="&#xc5;" u2="n" k="5" />
<hkern u1="&#xc5;" u2="m" k="5" />
<hkern u1="&#xc5;" u2="g" k="7" />
<hkern u1="&#xc5;" u2="f" k="18" />
<hkern u1="&#xc5;" u2="e" k="5" />
<hkern u1="&#xc5;" u2="d" k="5" />
<hkern u1="&#xc5;" u2="c" k="5" />
<hkern u1="&#xc5;" u2="a" k="5" />
<hkern u1="&#xc5;" u2="Y" k="52" />
<hkern u1="&#xc5;" u2="X" k="-10" />
<hkern u1="&#xc5;" u2="W" k="57" />
<hkern u1="&#xc5;" u2="V" k="64" />
<hkern u1="&#xc5;" u2="T" k="63" />
<hkern u1="&#xc5;" u2="S" k="-5" />
<hkern u1="&#xc5;" u2="Q" k="20" />
<hkern u1="&#xc5;" u2="O" k="20" />
<hkern u1="&#xc5;" u2="J" k="-10" />
<hkern u1="&#xc5;" u2="G" k="20" />
<hkern u1="&#xc5;" u2="C" k="20" />
<hkern u1="&#xc5;" u2="A" k="-10" />
<hkern u1="&#xc5;" u2="&#x40;" k="10" />
<hkern u1="&#xc5;" u2="&#x3f;" k="14" />
<hkern u1="&#xc5;" u2="&#x2e;" k="-30" />
<hkern u1="&#xc5;" u2="&#x2d;" k="-10" />
<hkern u1="&#xc5;" u2="&#x2c;" k="-30" />
<hkern u1="&#xc5;" u2="&#x2a;" k="69" />
<hkern u1="&#xc6;" u2="&#x2039;" k="9" />
<hkern u1="&#xc6;" u2="&#x153;" k="23" />
<hkern u1="&#xc6;" u2="&#x152;" k="11" />
<hkern u1="&#xc6;" u2="&#xfe;" k="2" />
<hkern u1="&#xc6;" u2="&#xe7;" k="23" />
<hkern u1="&#xc6;" u2="&#xe6;" k="23" />
<hkern u1="&#xc6;" u2="&#xd8;" k="11" />
<hkern u1="&#xc6;" u2="&#xd6;" k="11" />
<hkern u1="&#xc6;" u2="&#xd5;" k="11" />
<hkern u1="&#xc6;" u2="&#xd4;" k="11" />
<hkern u1="&#xc6;" u2="&#xd3;" k="11" />
<hkern u1="&#xc6;" u2="&#xd2;" k="11" />
<hkern u1="&#xc6;" u2="&#xae;" k="20" />
<hkern u1="&#xc6;" u2="&#xab;" k="9" />
<hkern u1="&#xc6;" u2="&#xa9;" k="20" />
<hkern u1="&#xc6;" u2="y" k="20" />
<hkern u1="&#xc6;" u2="v" k="20" />
<hkern u1="&#xc6;" u2="u" k="5" />
<hkern u1="&#xc6;" u2="r" k="5" />
<hkern u1="&#xc6;" u2="q" k="23" />
<hkern u1="&#xc6;" u2="p" k="5" />
<hkern u1="&#xc6;" u2="o" k="23" />
<hkern u1="&#xc6;" u2="n" k="5" />
<hkern u1="&#xc6;" u2="m" k="5" />
<hkern u1="&#xc6;" u2="l" k="2" />
<hkern u1="&#xc6;" u2="k" k="2" />
<hkern u1="&#xc6;" u2="h" k="2" />
<hkern u1="&#xc6;" u2="g" k="12" />
<hkern u1="&#xc6;" u2="f" k="10" />
<hkern u1="&#xc6;" u2="e" k="23" />
<hkern u1="&#xc6;" u2="d" k="23" />
<hkern u1="&#xc6;" u2="c" k="23" />
<hkern u1="&#xc6;" u2="b" k="2" />
<hkern u1="&#xc6;" u2="a" k="23" />
<hkern u1="&#xc6;" u2="W" k="2" />
<hkern u1="&#xc6;" u2="V" k="-5" />
<hkern u1="&#xc6;" u2="T" k="-11" />
<hkern u1="&#xc6;" u2="Q" k="11" />
<hkern u1="&#xc6;" u2="O" k="11" />
<hkern u1="&#xc6;" u2="J" k="-2" />
<hkern u1="&#xc6;" u2="G" k="11" />
<hkern u1="&#xc6;" u2="C" k="11" />
<hkern u1="&#xc6;" u2="&#x40;" k="20" />
<hkern u1="&#xc8;" u2="&#x2039;" k="9" />
<hkern u1="&#xc8;" u2="&#x153;" k="23" />
<hkern u1="&#xc8;" u2="&#x152;" k="11" />
<hkern u1="&#xc8;" u2="&#xfe;" k="2" />
<hkern u1="&#xc8;" u2="&#xe7;" k="23" />
<hkern u1="&#xc8;" u2="&#xe6;" k="23" />
<hkern u1="&#xc8;" u2="&#xd8;" k="11" />
<hkern u1="&#xc8;" u2="&#xd6;" k="11" />
<hkern u1="&#xc8;" u2="&#xd5;" k="11" />
<hkern u1="&#xc8;" u2="&#xd4;" k="11" />
<hkern u1="&#xc8;" u2="&#xd3;" k="11" />
<hkern u1="&#xc8;" u2="&#xd2;" k="11" />
<hkern u1="&#xc8;" u2="&#xae;" k="20" />
<hkern u1="&#xc8;" u2="&#xab;" k="9" />
<hkern u1="&#xc8;" u2="&#xa9;" k="20" />
<hkern u1="&#xc8;" u2="y" k="20" />
<hkern u1="&#xc8;" u2="v" k="20" />
<hkern u1="&#xc8;" u2="u" k="5" />
<hkern u1="&#xc8;" u2="r" k="5" />
<hkern u1="&#xc8;" u2="q" k="23" />
<hkern u1="&#xc8;" u2="p" k="5" />
<hkern u1="&#xc8;" u2="o" k="23" />
<hkern u1="&#xc8;" u2="n" k="5" />
<hkern u1="&#xc8;" u2="m" k="5" />
<hkern u1="&#xc8;" u2="l" k="2" />
<hkern u1="&#xc8;" u2="k" k="2" />
<hkern u1="&#xc8;" u2="h" k="2" />
<hkern u1="&#xc8;" u2="g" k="12" />
<hkern u1="&#xc8;" u2="f" k="10" />
<hkern u1="&#xc8;" u2="e" k="23" />
<hkern u1="&#xc8;" u2="d" k="23" />
<hkern u1="&#xc8;" u2="c" k="23" />
<hkern u1="&#xc8;" u2="b" k="2" />
<hkern u1="&#xc8;" u2="a" k="23" />
<hkern u1="&#xc8;" u2="W" k="2" />
<hkern u1="&#xc8;" u2="V" k="-5" />
<hkern u1="&#xc8;" u2="T" k="-11" />
<hkern u1="&#xc8;" u2="Q" k="11" />
<hkern u1="&#xc8;" u2="O" k="11" />
<hkern u1="&#xc8;" u2="J" k="-2" />
<hkern u1="&#xc8;" u2="G" k="11" />
<hkern u1="&#xc8;" u2="C" k="11" />
<hkern u1="&#xc8;" u2="&#x40;" k="20" />
<hkern u1="&#xc9;" u2="&#x2039;" k="9" />
<hkern u1="&#xc9;" u2="&#x153;" k="23" />
<hkern u1="&#xc9;" u2="&#x152;" k="11" />
<hkern u1="&#xc9;" u2="&#xfe;" k="2" />
<hkern u1="&#xc9;" u2="&#xe7;" k="23" />
<hkern u1="&#xc9;" u2="&#xe6;" k="23" />
<hkern u1="&#xc9;" u2="&#xd8;" k="11" />
<hkern u1="&#xc9;" u2="&#xd6;" k="11" />
<hkern u1="&#xc9;" u2="&#xd5;" k="11" />
<hkern u1="&#xc9;" u2="&#xd4;" k="11" />
<hkern u1="&#xc9;" u2="&#xd3;" k="11" />
<hkern u1="&#xc9;" u2="&#xd2;" k="11" />
<hkern u1="&#xc9;" u2="&#xae;" k="20" />
<hkern u1="&#xc9;" u2="&#xab;" k="9" />
<hkern u1="&#xc9;" u2="&#xa9;" k="20" />
<hkern u1="&#xc9;" u2="y" k="20" />
<hkern u1="&#xc9;" u2="v" k="20" />
<hkern u1="&#xc9;" u2="u" k="5" />
<hkern u1="&#xc9;" u2="r" k="5" />
<hkern u1="&#xc9;" u2="q" k="23" />
<hkern u1="&#xc9;" u2="p" k="5" />
<hkern u1="&#xc9;" u2="o" k="23" />
<hkern u1="&#xc9;" u2="n" k="5" />
<hkern u1="&#xc9;" u2="m" k="5" />
<hkern u1="&#xc9;" u2="l" k="2" />
<hkern u1="&#xc9;" u2="k" k="2" />
<hkern u1="&#xc9;" u2="h" k="2" />
<hkern u1="&#xc9;" u2="g" k="12" />
<hkern u1="&#xc9;" u2="f" k="10" />
<hkern u1="&#xc9;" u2="e" k="23" />
<hkern u1="&#xc9;" u2="d" k="23" />
<hkern u1="&#xc9;" u2="c" k="23" />
<hkern u1="&#xc9;" u2="b" k="2" />
<hkern u1="&#xc9;" u2="a" k="23" />
<hkern u1="&#xc9;" u2="W" k="2" />
<hkern u1="&#xc9;" u2="V" k="-5" />
<hkern u1="&#xc9;" u2="T" k="-11" />
<hkern u1="&#xc9;" u2="Q" k="11" />
<hkern u1="&#xc9;" u2="O" k="11" />
<hkern u1="&#xc9;" u2="J" k="-2" />
<hkern u1="&#xc9;" u2="G" k="11" />
<hkern u1="&#xc9;" u2="C" k="11" />
<hkern u1="&#xc9;" u2="&#x40;" k="20" />
<hkern u1="&#xca;" u2="&#x2039;" k="9" />
<hkern u1="&#xca;" u2="&#x153;" k="23" />
<hkern u1="&#xca;" u2="&#x152;" k="11" />
<hkern u1="&#xca;" u2="&#xfe;" k="2" />
<hkern u1="&#xca;" u2="&#xe7;" k="23" />
<hkern u1="&#xca;" u2="&#xe6;" k="23" />
<hkern u1="&#xca;" u2="&#xd8;" k="11" />
<hkern u1="&#xca;" u2="&#xd6;" k="11" />
<hkern u1="&#xca;" u2="&#xd5;" k="11" />
<hkern u1="&#xca;" u2="&#xd4;" k="11" />
<hkern u1="&#xca;" u2="&#xd3;" k="11" />
<hkern u1="&#xca;" u2="&#xd2;" k="11" />
<hkern u1="&#xca;" u2="&#xae;" k="20" />
<hkern u1="&#xca;" u2="&#xab;" k="9" />
<hkern u1="&#xca;" u2="&#xa9;" k="20" />
<hkern u1="&#xca;" u2="y" k="20" />
<hkern u1="&#xca;" u2="v" k="20" />
<hkern u1="&#xca;" u2="u" k="5" />
<hkern u1="&#xca;" u2="r" k="5" />
<hkern u1="&#xca;" u2="q" k="23" />
<hkern u1="&#xca;" u2="p" k="5" />
<hkern u1="&#xca;" u2="o" k="23" />
<hkern u1="&#xca;" u2="n" k="5" />
<hkern u1="&#xca;" u2="m" k="5" />
<hkern u1="&#xca;" u2="l" k="2" />
<hkern u1="&#xca;" u2="k" k="2" />
<hkern u1="&#xca;" u2="h" k="2" />
<hkern u1="&#xca;" u2="g" k="12" />
<hkern u1="&#xca;" u2="f" k="10" />
<hkern u1="&#xca;" u2="e" k="23" />
<hkern u1="&#xca;" u2="d" k="23" />
<hkern u1="&#xca;" u2="c" k="23" />
<hkern u1="&#xca;" u2="b" k="2" />
<hkern u1="&#xca;" u2="a" k="23" />
<hkern u1="&#xca;" u2="W" k="2" />
<hkern u1="&#xca;" u2="V" k="-5" />
<hkern u1="&#xca;" u2="T" k="-11" />
<hkern u1="&#xca;" u2="Q" k="11" />
<hkern u1="&#xca;" u2="O" k="11" />
<hkern u1="&#xca;" u2="J" k="-2" />
<hkern u1="&#xca;" u2="G" k="11" />
<hkern u1="&#xca;" u2="C" k="11" />
<hkern u1="&#xca;" u2="&#x40;" k="20" />
<hkern u1="&#xcb;" u2="&#x2039;" k="9" />
<hkern u1="&#xcb;" u2="&#x153;" k="23" />
<hkern u1="&#xcb;" u2="&#x152;" k="11" />
<hkern u1="&#xcb;" u2="&#xfe;" k="2" />
<hkern u1="&#xcb;" u2="&#xe7;" k="23" />
<hkern u1="&#xcb;" u2="&#xe6;" k="23" />
<hkern u1="&#xcb;" u2="&#xd8;" k="11" />
<hkern u1="&#xcb;" u2="&#xd6;" k="11" />
<hkern u1="&#xcb;" u2="&#xd5;" k="11" />
<hkern u1="&#xcb;" u2="&#xd4;" k="11" />
<hkern u1="&#xcb;" u2="&#xd3;" k="11" />
<hkern u1="&#xcb;" u2="&#xd2;" k="11" />
<hkern u1="&#xcb;" u2="&#xae;" k="20" />
<hkern u1="&#xcb;" u2="&#xab;" k="9" />
<hkern u1="&#xcb;" u2="&#xa9;" k="20" />
<hkern u1="&#xcb;" u2="y" k="20" />
<hkern u1="&#xcb;" u2="v" k="20" />
<hkern u1="&#xcb;" u2="u" k="5" />
<hkern u1="&#xcb;" u2="r" k="5" />
<hkern u1="&#xcb;" u2="q" k="23" />
<hkern u1="&#xcb;" u2="p" k="5" />
<hkern u1="&#xcb;" u2="o" k="23" />
<hkern u1="&#xcb;" u2="n" k="5" />
<hkern u1="&#xcb;" u2="m" k="5" />
<hkern u1="&#xcb;" u2="l" k="2" />
<hkern u1="&#xcb;" u2="k" k="2" />
<hkern u1="&#xcb;" u2="h" k="2" />
<hkern u1="&#xcb;" u2="g" k="12" />
<hkern u1="&#xcb;" u2="f" k="10" />
<hkern u1="&#xcb;" u2="e" k="23" />
<hkern u1="&#xcb;" u2="d" k="23" />
<hkern u1="&#xcb;" u2="c" k="23" />
<hkern u1="&#xcb;" u2="b" k="2" />
<hkern u1="&#xcb;" u2="a" k="23" />
<hkern u1="&#xcb;" u2="W" k="2" />
<hkern u1="&#xcb;" u2="V" k="-5" />
<hkern u1="&#xcb;" u2="T" k="-11" />
<hkern u1="&#xcb;" u2="Q" k="11" />
<hkern u1="&#xcb;" u2="O" k="11" />
<hkern u1="&#xcb;" u2="J" k="-2" />
<hkern u1="&#xcb;" u2="G" k="11" />
<hkern u1="&#xcb;" u2="C" k="11" />
<hkern u1="&#xcb;" u2="&#x40;" k="20" />
<hkern u1="&#xcc;" u2="y" k="10" />
<hkern u1="&#xcc;" u2="v" k="10" />
<hkern u1="&#xcc;" u2="&#x2f;" k="20" />
<hkern u1="&#xcd;" u2="y" k="10" />
<hkern u1="&#xcd;" u2="v" k="10" />
<hkern u1="&#xcd;" u2="&#x2f;" k="20" />
<hkern u1="&#xce;" u2="y" k="10" />
<hkern u1="&#xce;" u2="v" k="10" />
<hkern u1="&#xce;" u2="&#x2f;" k="20" />
<hkern u1="&#xcf;" u2="y" k="10" />
<hkern u1="&#xcf;" u2="v" k="10" />
<hkern u1="&#xcf;" u2="&#x2f;" k="20" />
<hkern u1="&#xd0;" u2="&#x2026;" k="36" />
<hkern u1="&#xd0;" u2="&#x201e;" k="36" />
<hkern u1="&#xd0;" u2="&#x201c;" k="20" />
<hkern u1="&#xd0;" u2="&#x201a;" k="36" />
<hkern u1="&#xd0;" u2="&#x2018;" k="20" />
<hkern u1="&#xd0;" u2="&#x178;" k="21" />
<hkern u1="&#xd0;" u2="&#x153;" k="3" />
<hkern u1="&#xd0;" u2="&#xe7;" k="3" />
<hkern u1="&#xd0;" u2="&#xe6;" k="3" />
<hkern u1="&#xd0;" u2="&#xdd;" k="21" />
<hkern u1="&#xd0;" u2="&#xc6;" k="20" />
<hkern u1="&#xd0;" u2="&#xc5;" k="20" />
<hkern u1="&#xd0;" u2="&#xc4;" k="20" />
<hkern u1="&#xd0;" u2="&#xc3;" k="20" />
<hkern u1="&#xd0;" u2="&#xc2;" k="20" />
<hkern u1="&#xd0;" u2="&#xc1;" k="20" />
<hkern u1="&#xd0;" u2="&#xc0;" k="20" />
<hkern u1="&#xd0;" u2="z" k="10" />
<hkern u1="&#xd0;" u2="x" k="10" />
<hkern u1="&#xd0;" u2="u" k="3" />
<hkern u1="&#xd0;" u2="r" k="3" />
<hkern u1="&#xd0;" u2="q" k="3" />
<hkern u1="&#xd0;" u2="p" k="3" />
<hkern u1="&#xd0;" u2="o" k="3" />
<hkern u1="&#xd0;" u2="n" k="3" />
<hkern u1="&#xd0;" u2="m" k="3" />
<hkern u1="&#xd0;" u2="l" k="6" />
<hkern u1="&#xd0;" u2="k" k="6" />
<hkern u1="&#xd0;" u2="h" k="6" />
<hkern u1="&#xd0;" u2="e" k="3" />
<hkern u1="&#xd0;" u2="d" k="3" />
<hkern u1="&#xd0;" u2="c" k="3" />
<hkern u1="&#xd0;" u2="b" k="6" />
<hkern u1="&#xd0;" u2="a" k="3" />
<hkern u1="&#xd0;" u2="Z" k="29" />
<hkern u1="&#xd0;" u2="Y" k="21" />
<hkern u1="&#xd0;" u2="X" k="27" />
<hkern u1="&#xd0;" u2="W" k="36" />
<hkern u1="&#xd0;" u2="V" k="20" />
<hkern u1="&#xd0;" u2="T" k="23" />
<hkern u1="&#xd0;" u2="J" k="32" />
<hkern u1="&#xd0;" u2="A" k="20" />
<hkern u1="&#xd0;" u2="&#x3f;" k="20" />
<hkern u1="&#xd0;" u2="&#x2f;" k="50" />
<hkern u1="&#xd0;" u2="&#x2e;" k="36" />
<hkern u1="&#xd0;" u2="&#x2c;" k="36" />
<hkern u1="&#xd1;" u2="y" k="10" />
<hkern u1="&#xd1;" u2="v" k="10" />
<hkern u1="&#xd1;" u2="&#x2f;" k="20" />
<hkern u1="&#xd2;" u2="&#x2026;" k="36" />
<hkern u1="&#xd2;" u2="&#x201e;" k="36" />
<hkern u1="&#xd2;" u2="&#x201c;" k="20" />
<hkern u1="&#xd2;" u2="&#x201a;" k="36" />
<hkern u1="&#xd2;" u2="&#x2018;" k="20" />
<hkern u1="&#xd2;" u2="&#x178;" k="21" />
<hkern u1="&#xd2;" u2="&#x153;" k="3" />
<hkern u1="&#xd2;" u2="&#xe7;" k="3" />
<hkern u1="&#xd2;" u2="&#xe6;" k="3" />
<hkern u1="&#xd2;" u2="&#xdd;" k="21" />
<hkern u1="&#xd2;" u2="&#xc6;" k="20" />
<hkern u1="&#xd2;" u2="&#xc5;" k="20" />
<hkern u1="&#xd2;" u2="&#xc4;" k="20" />
<hkern u1="&#xd2;" u2="&#xc3;" k="20" />
<hkern u1="&#xd2;" u2="&#xc2;" k="20" />
<hkern u1="&#xd2;" u2="&#xc1;" k="20" />
<hkern u1="&#xd2;" u2="&#xc0;" k="20" />
<hkern u1="&#xd2;" u2="z" k="10" />
<hkern u1="&#xd2;" u2="x" k="10" />
<hkern u1="&#xd2;" u2="u" k="3" />
<hkern u1="&#xd2;" u2="r" k="3" />
<hkern u1="&#xd2;" u2="q" k="3" />
<hkern u1="&#xd2;" u2="p" k="3" />
<hkern u1="&#xd2;" u2="o" k="3" />
<hkern u1="&#xd2;" u2="n" k="3" />
<hkern u1="&#xd2;" u2="m" k="3" />
<hkern u1="&#xd2;" u2="l" k="6" />
<hkern u1="&#xd2;" u2="k" k="6" />
<hkern u1="&#xd2;" u2="h" k="6" />
<hkern u1="&#xd2;" u2="e" k="3" />
<hkern u1="&#xd2;" u2="d" k="3" />
<hkern u1="&#xd2;" u2="c" k="3" />
<hkern u1="&#xd2;" u2="b" k="6" />
<hkern u1="&#xd2;" u2="a" k="3" />
<hkern u1="&#xd2;" u2="Z" k="29" />
<hkern u1="&#xd2;" u2="Y" k="21" />
<hkern u1="&#xd2;" u2="X" k="27" />
<hkern u1="&#xd2;" u2="W" k="36" />
<hkern u1="&#xd2;" u2="V" k="20" />
<hkern u1="&#xd2;" u2="T" k="23" />
<hkern u1="&#xd2;" u2="J" k="32" />
<hkern u1="&#xd2;" u2="A" k="20" />
<hkern u1="&#xd2;" u2="&#x3f;" k="20" />
<hkern u1="&#xd2;" u2="&#x2f;" k="50" />
<hkern u1="&#xd2;" u2="&#x2e;" k="36" />
<hkern u1="&#xd2;" u2="&#x2c;" k="36" />
<hkern u1="&#xd3;" u2="&#x2026;" k="36" />
<hkern u1="&#xd3;" u2="&#x201e;" k="36" />
<hkern u1="&#xd3;" u2="&#x201c;" k="20" />
<hkern u1="&#xd3;" u2="&#x201a;" k="36" />
<hkern u1="&#xd3;" u2="&#x2018;" k="20" />
<hkern u1="&#xd3;" u2="&#x178;" k="21" />
<hkern u1="&#xd3;" u2="&#x153;" k="3" />
<hkern u1="&#xd3;" u2="&#xe7;" k="3" />
<hkern u1="&#xd3;" u2="&#xe6;" k="3" />
<hkern u1="&#xd3;" u2="&#xdd;" k="21" />
<hkern u1="&#xd3;" u2="&#xc6;" k="20" />
<hkern u1="&#xd3;" u2="&#xc5;" k="20" />
<hkern u1="&#xd3;" u2="&#xc4;" k="20" />
<hkern u1="&#xd3;" u2="&#xc3;" k="20" />
<hkern u1="&#xd3;" u2="&#xc2;" k="20" />
<hkern u1="&#xd3;" u2="&#xc1;" k="20" />
<hkern u1="&#xd3;" u2="&#xc0;" k="20" />
<hkern u1="&#xd3;" u2="z" k="10" />
<hkern u1="&#xd3;" u2="x" k="10" />
<hkern u1="&#xd3;" u2="u" k="3" />
<hkern u1="&#xd3;" u2="r" k="3" />
<hkern u1="&#xd3;" u2="q" k="3" />
<hkern u1="&#xd3;" u2="p" k="3" />
<hkern u1="&#xd3;" u2="o" k="3" />
<hkern u1="&#xd3;" u2="n" k="3" />
<hkern u1="&#xd3;" u2="m" k="3" />
<hkern u1="&#xd3;" u2="l" k="6" />
<hkern u1="&#xd3;" u2="k" k="6" />
<hkern u1="&#xd3;" u2="h" k="6" />
<hkern u1="&#xd3;" u2="e" k="3" />
<hkern u1="&#xd3;" u2="d" k="3" />
<hkern u1="&#xd3;" u2="c" k="3" />
<hkern u1="&#xd3;" u2="b" k="6" />
<hkern u1="&#xd3;" u2="a" k="3" />
<hkern u1="&#xd3;" u2="Z" k="29" />
<hkern u1="&#xd3;" u2="Y" k="21" />
<hkern u1="&#xd3;" u2="X" k="27" />
<hkern u1="&#xd3;" u2="W" k="36" />
<hkern u1="&#xd3;" u2="V" k="20" />
<hkern u1="&#xd3;" u2="T" k="23" />
<hkern u1="&#xd3;" u2="J" k="32" />
<hkern u1="&#xd3;" u2="A" k="20" />
<hkern u1="&#xd3;" u2="&#x3f;" k="20" />
<hkern u1="&#xd3;" u2="&#x2f;" k="50" />
<hkern u1="&#xd3;" u2="&#x2e;" k="36" />
<hkern u1="&#xd3;" u2="&#x2c;" k="36" />
<hkern u1="&#xd4;" u2="&#x2026;" k="36" />
<hkern u1="&#xd4;" u2="&#x201e;" k="36" />
<hkern u1="&#xd4;" u2="&#x201c;" k="20" />
<hkern u1="&#xd4;" u2="&#x201a;" k="36" />
<hkern u1="&#xd4;" u2="&#x2018;" k="20" />
<hkern u1="&#xd4;" u2="&#x178;" k="21" />
<hkern u1="&#xd4;" u2="&#x153;" k="3" />
<hkern u1="&#xd4;" u2="&#xe7;" k="3" />
<hkern u1="&#xd4;" u2="&#xe6;" k="3" />
<hkern u1="&#xd4;" u2="&#xdd;" k="21" />
<hkern u1="&#xd4;" u2="&#xc6;" k="20" />
<hkern u1="&#xd4;" u2="&#xc5;" k="20" />
<hkern u1="&#xd4;" u2="&#xc4;" k="20" />
<hkern u1="&#xd4;" u2="&#xc3;" k="20" />
<hkern u1="&#xd4;" u2="&#xc2;" k="20" />
<hkern u1="&#xd4;" u2="&#xc1;" k="20" />
<hkern u1="&#xd4;" u2="&#xc0;" k="20" />
<hkern u1="&#xd4;" u2="z" k="10" />
<hkern u1="&#xd4;" u2="x" k="10" />
<hkern u1="&#xd4;" u2="u" k="3" />
<hkern u1="&#xd4;" u2="r" k="3" />
<hkern u1="&#xd4;" u2="q" k="3" />
<hkern u1="&#xd4;" u2="p" k="3" />
<hkern u1="&#xd4;" u2="o" k="3" />
<hkern u1="&#xd4;" u2="n" k="3" />
<hkern u1="&#xd4;" u2="m" k="3" />
<hkern u1="&#xd4;" u2="l" k="6" />
<hkern u1="&#xd4;" u2="k" k="6" />
<hkern u1="&#xd4;" u2="h" k="6" />
<hkern u1="&#xd4;" u2="e" k="3" />
<hkern u1="&#xd4;" u2="d" k="3" />
<hkern u1="&#xd4;" u2="c" k="3" />
<hkern u1="&#xd4;" u2="b" k="6" />
<hkern u1="&#xd4;" u2="a" k="3" />
<hkern u1="&#xd4;" u2="Z" k="29" />
<hkern u1="&#xd4;" u2="Y" k="21" />
<hkern u1="&#xd4;" u2="X" k="27" />
<hkern u1="&#xd4;" u2="W" k="36" />
<hkern u1="&#xd4;" u2="V" k="20" />
<hkern u1="&#xd4;" u2="T" k="23" />
<hkern u1="&#xd4;" u2="J" k="32" />
<hkern u1="&#xd4;" u2="A" k="20" />
<hkern u1="&#xd4;" u2="&#x3f;" k="20" />
<hkern u1="&#xd4;" u2="&#x2f;" k="50" />
<hkern u1="&#xd4;" u2="&#x2e;" k="36" />
<hkern u1="&#xd4;" u2="&#x2c;" k="36" />
<hkern u1="&#xd5;" u2="&#x2026;" k="36" />
<hkern u1="&#xd5;" u2="&#x201e;" k="36" />
<hkern u1="&#xd5;" u2="&#x201c;" k="20" />
<hkern u1="&#xd5;" u2="&#x201a;" k="36" />
<hkern u1="&#xd5;" u2="&#x2018;" k="20" />
<hkern u1="&#xd5;" u2="&#x178;" k="21" />
<hkern u1="&#xd5;" u2="&#x153;" k="3" />
<hkern u1="&#xd5;" u2="&#xe7;" k="3" />
<hkern u1="&#xd5;" u2="&#xe6;" k="3" />
<hkern u1="&#xd5;" u2="&#xdd;" k="21" />
<hkern u1="&#xd5;" u2="&#xc6;" k="20" />
<hkern u1="&#xd5;" u2="&#xc5;" k="20" />
<hkern u1="&#xd5;" u2="&#xc4;" k="20" />
<hkern u1="&#xd5;" u2="&#xc3;" k="20" />
<hkern u1="&#xd5;" u2="&#xc2;" k="20" />
<hkern u1="&#xd5;" u2="&#xc1;" k="20" />
<hkern u1="&#xd5;" u2="&#xc0;" k="20" />
<hkern u1="&#xd5;" u2="z" k="10" />
<hkern u1="&#xd5;" u2="x" k="10" />
<hkern u1="&#xd5;" u2="u" k="3" />
<hkern u1="&#xd5;" u2="r" k="3" />
<hkern u1="&#xd5;" u2="q" k="3" />
<hkern u1="&#xd5;" u2="p" k="3" />
<hkern u1="&#xd5;" u2="o" k="3" />
<hkern u1="&#xd5;" u2="n" k="3" />
<hkern u1="&#xd5;" u2="m" k="3" />
<hkern u1="&#xd5;" u2="l" k="6" />
<hkern u1="&#xd5;" u2="k" k="6" />
<hkern u1="&#xd5;" u2="h" k="6" />
<hkern u1="&#xd5;" u2="e" k="3" />
<hkern u1="&#xd5;" u2="d" k="3" />
<hkern u1="&#xd5;" u2="c" k="3" />
<hkern u1="&#xd5;" u2="b" k="6" />
<hkern u1="&#xd5;" u2="a" k="3" />
<hkern u1="&#xd5;" u2="Z" k="29" />
<hkern u1="&#xd5;" u2="Y" k="21" />
<hkern u1="&#xd5;" u2="X" k="27" />
<hkern u1="&#xd5;" u2="W" k="36" />
<hkern u1="&#xd5;" u2="V" k="20" />
<hkern u1="&#xd5;" u2="T" k="23" />
<hkern u1="&#xd5;" u2="J" k="32" />
<hkern u1="&#xd5;" u2="A" k="20" />
<hkern u1="&#xd5;" u2="&#x3f;" k="20" />
<hkern u1="&#xd5;" u2="&#x2f;" k="50" />
<hkern u1="&#xd5;" u2="&#x2e;" k="36" />
<hkern u1="&#xd5;" u2="&#x2c;" k="36" />
<hkern u1="&#xd6;" u2="&#x2026;" k="36" />
<hkern u1="&#xd6;" u2="&#x201e;" k="36" />
<hkern u1="&#xd6;" u2="&#x201c;" k="20" />
<hkern u1="&#xd6;" u2="&#x201a;" k="36" />
<hkern u1="&#xd6;" u2="&#x2018;" k="20" />
<hkern u1="&#xd6;" u2="&#x178;" k="21" />
<hkern u1="&#xd6;" u2="&#x153;" k="3" />
<hkern u1="&#xd6;" u2="&#xe7;" k="3" />
<hkern u1="&#xd6;" u2="&#xe6;" k="3" />
<hkern u1="&#xd6;" u2="&#xdd;" k="21" />
<hkern u1="&#xd6;" u2="&#xc6;" k="20" />
<hkern u1="&#xd6;" u2="&#xc5;" k="20" />
<hkern u1="&#xd6;" u2="&#xc4;" k="20" />
<hkern u1="&#xd6;" u2="&#xc3;" k="20" />
<hkern u1="&#xd6;" u2="&#xc2;" k="20" />
<hkern u1="&#xd6;" u2="&#xc1;" k="20" />
<hkern u1="&#xd6;" u2="&#xc0;" k="20" />
<hkern u1="&#xd6;" u2="z" k="10" />
<hkern u1="&#xd6;" u2="x" k="10" />
<hkern u1="&#xd6;" u2="u" k="3" />
<hkern u1="&#xd6;" u2="r" k="3" />
<hkern u1="&#xd6;" u2="q" k="3" />
<hkern u1="&#xd6;" u2="p" k="3" />
<hkern u1="&#xd6;" u2="o" k="3" />
<hkern u1="&#xd6;" u2="n" k="3" />
<hkern u1="&#xd6;" u2="m" k="3" />
<hkern u1="&#xd6;" u2="l" k="6" />
<hkern u1="&#xd6;" u2="k" k="6" />
<hkern u1="&#xd6;" u2="h" k="6" />
<hkern u1="&#xd6;" u2="e" k="3" />
<hkern u1="&#xd6;" u2="d" k="3" />
<hkern u1="&#xd6;" u2="c" k="3" />
<hkern u1="&#xd6;" u2="b" k="6" />
<hkern u1="&#xd6;" u2="a" k="3" />
<hkern u1="&#xd6;" u2="Z" k="29" />
<hkern u1="&#xd6;" u2="Y" k="21" />
<hkern u1="&#xd6;" u2="X" k="27" />
<hkern u1="&#xd6;" u2="W" k="36" />
<hkern u1="&#xd6;" u2="V" k="20" />
<hkern u1="&#xd6;" u2="T" k="23" />
<hkern u1="&#xd6;" u2="J" k="32" />
<hkern u1="&#xd6;" u2="A" k="20" />
<hkern u1="&#xd6;" u2="&#x3f;" k="20" />
<hkern u1="&#xd6;" u2="&#x2f;" k="50" />
<hkern u1="&#xd6;" u2="&#x2e;" k="36" />
<hkern u1="&#xd6;" u2="&#x2c;" k="36" />
<hkern u1="&#xd8;" u2="&#x2026;" k="36" />
<hkern u1="&#xd8;" u2="&#x201e;" k="36" />
<hkern u1="&#xd8;" u2="&#x201c;" k="20" />
<hkern u1="&#xd8;" u2="&#x201a;" k="36" />
<hkern u1="&#xd8;" u2="&#x2018;" k="20" />
<hkern u1="&#xd8;" u2="&#x178;" k="21" />
<hkern u1="&#xd8;" u2="&#x153;" k="3" />
<hkern u1="&#xd8;" u2="&#xe7;" k="3" />
<hkern u1="&#xd8;" u2="&#xe6;" k="3" />
<hkern u1="&#xd8;" u2="&#xdd;" k="21" />
<hkern u1="&#xd8;" u2="&#xc6;" k="20" />
<hkern u1="&#xd8;" u2="&#xc5;" k="20" />
<hkern u1="&#xd8;" u2="&#xc4;" k="20" />
<hkern u1="&#xd8;" u2="&#xc3;" k="20" />
<hkern u1="&#xd8;" u2="&#xc2;" k="20" />
<hkern u1="&#xd8;" u2="&#xc1;" k="20" />
<hkern u1="&#xd8;" u2="&#xc0;" k="20" />
<hkern u1="&#xd8;" u2="z" k="10" />
<hkern u1="&#xd8;" u2="x" k="10" />
<hkern u1="&#xd8;" u2="u" k="3" />
<hkern u1="&#xd8;" u2="r" k="3" />
<hkern u1="&#xd8;" u2="q" k="3" />
<hkern u1="&#xd8;" u2="p" k="3" />
<hkern u1="&#xd8;" u2="o" k="3" />
<hkern u1="&#xd8;" u2="n" k="3" />
<hkern u1="&#xd8;" u2="m" k="3" />
<hkern u1="&#xd8;" u2="l" k="6" />
<hkern u1="&#xd8;" u2="k" k="6" />
<hkern u1="&#xd8;" u2="h" k="6" />
<hkern u1="&#xd8;" u2="e" k="3" />
<hkern u1="&#xd8;" u2="d" k="3" />
<hkern u1="&#xd8;" u2="c" k="3" />
<hkern u1="&#xd8;" u2="b" k="6" />
<hkern u1="&#xd8;" u2="a" k="3" />
<hkern u1="&#xd8;" u2="Z" k="29" />
<hkern u1="&#xd8;" u2="Y" k="21" />
<hkern u1="&#xd8;" u2="X" k="27" />
<hkern u1="&#xd8;" u2="W" k="36" />
<hkern u1="&#xd8;" u2="V" k="20" />
<hkern u1="&#xd8;" u2="T" k="23" />
<hkern u1="&#xd8;" u2="J" k="32" />
<hkern u1="&#xd8;" u2="A" k="20" />
<hkern u1="&#xd8;" u2="&#x3f;" k="20" />
<hkern u1="&#xd8;" u2="&#x2f;" k="50" />
<hkern u1="&#xd8;" u2="&#x2e;" k="36" />
<hkern u1="&#xd8;" u2="&#x2c;" k="36" />
<hkern u1="&#xd9;" u2="&#x2026;" k="10" />
<hkern u1="&#xd9;" u2="&#x201e;" k="10" />
<hkern u1="&#xd9;" u2="&#x201a;" k="10" />
<hkern u1="&#xd9;" u2="&#xc6;" k="9" />
<hkern u1="&#xd9;" u2="&#xc5;" k="9" />
<hkern u1="&#xd9;" u2="&#xc4;" k="9" />
<hkern u1="&#xd9;" u2="&#xc3;" k="9" />
<hkern u1="&#xd9;" u2="&#xc2;" k="9" />
<hkern u1="&#xd9;" u2="&#xc1;" k="9" />
<hkern u1="&#xd9;" u2="&#xc0;" k="9" />
<hkern u1="&#xd9;" u2="J" k="10" />
<hkern u1="&#xd9;" u2="A" k="9" />
<hkern u1="&#xd9;" u2="&#x2e;" k="10" />
<hkern u1="&#xd9;" u2="&#x2c;" k="10" />
<hkern u1="&#xda;" u2="&#x2026;" k="10" />
<hkern u1="&#xda;" u2="&#x201e;" k="10" />
<hkern u1="&#xda;" u2="&#x201a;" k="10" />
<hkern u1="&#xda;" u2="&#xc6;" k="9" />
<hkern u1="&#xda;" u2="&#xc5;" k="9" />
<hkern u1="&#xda;" u2="&#xc4;" k="9" />
<hkern u1="&#xda;" u2="&#xc3;" k="9" />
<hkern u1="&#xda;" u2="&#xc2;" k="9" />
<hkern u1="&#xda;" u2="&#xc1;" k="9" />
<hkern u1="&#xda;" u2="&#xc0;" k="9" />
<hkern u1="&#xda;" u2="J" k="10" />
<hkern u1="&#xda;" u2="A" k="9" />
<hkern u1="&#xda;" u2="&#x2e;" k="10" />
<hkern u1="&#xda;" u2="&#x2c;" k="10" />
<hkern u1="&#xdb;" u2="&#x2026;" k="10" />
<hkern u1="&#xdb;" u2="&#x201e;" k="10" />
<hkern u1="&#xdb;" u2="&#x201a;" k="10" />
<hkern u1="&#xdb;" u2="&#xc6;" k="9" />
<hkern u1="&#xdb;" u2="&#xc5;" k="9" />
<hkern u1="&#xdb;" u2="&#xc4;" k="9" />
<hkern u1="&#xdb;" u2="&#xc3;" k="9" />
<hkern u1="&#xdb;" u2="&#xc2;" k="9" />
<hkern u1="&#xdb;" u2="&#xc1;" k="9" />
<hkern u1="&#xdb;" u2="&#xc0;" k="9" />
<hkern u1="&#xdb;" u2="J" k="10" />
<hkern u1="&#xdb;" u2="A" k="9" />
<hkern u1="&#xdb;" u2="&#x2e;" k="10" />
<hkern u1="&#xdb;" u2="&#x2c;" k="10" />
<hkern u1="&#xdc;" u2="&#x2026;" k="10" />
<hkern u1="&#xdc;" u2="&#x201e;" k="10" />
<hkern u1="&#xdc;" u2="&#x201a;" k="10" />
<hkern u1="&#xdc;" u2="&#xc6;" k="9" />
<hkern u1="&#xdc;" u2="&#xc5;" k="9" />
<hkern u1="&#xdc;" u2="&#xc4;" k="9" />
<hkern u1="&#xdc;" u2="&#xc3;" k="9" />
<hkern u1="&#xdc;" u2="&#xc2;" k="9" />
<hkern u1="&#xdc;" u2="&#xc1;" k="9" />
<hkern u1="&#xdc;" u2="&#xc0;" k="9" />
<hkern u1="&#xdc;" u2="J" k="10" />
<hkern u1="&#xdc;" u2="A" k="9" />
<hkern u1="&#xdc;" u2="&#x2e;" k="10" />
<hkern u1="&#xdc;" u2="&#x2c;" k="10" />
<hkern u1="&#xdd;" u2="&#x203a;" k="40" />
<hkern u1="&#xdd;" u2="&#x2039;" k="60" />
<hkern u1="&#xdd;" u2="&#x2026;" k="50" />
<hkern u1="&#xdd;" u2="&#x201e;" k="50" />
<hkern u1="&#xdd;" u2="&#x201a;" k="50" />
<hkern u1="&#xdd;" u2="&#x2014;" k="50" />
<hkern u1="&#xdd;" u2="&#x2013;" k="50" />
<hkern u1="&#xdd;" u2="&#x153;" k="64" />
<hkern u1="&#xdd;" u2="&#x152;" k="21" />
<hkern u1="&#xdd;" u2="&#xe7;" k="64" />
<hkern u1="&#xdd;" u2="&#xe6;" k="64" />
<hkern u1="&#xdd;" u2="&#xd8;" k="21" />
<hkern u1="&#xdd;" u2="&#xd6;" k="21" />
<hkern u1="&#xdd;" u2="&#xd5;" k="21" />
<hkern u1="&#xdd;" u2="&#xd4;" k="21" />
<hkern u1="&#xdd;" u2="&#xd3;" k="21" />
<hkern u1="&#xdd;" u2="&#xd2;" k="21" />
<hkern u1="&#xdd;" u2="&#xc6;" k="52" />
<hkern u1="&#xdd;" u2="&#xc5;" k="52" />
<hkern u1="&#xdd;" u2="&#xc4;" k="52" />
<hkern u1="&#xdd;" u2="&#xc3;" k="52" />
<hkern u1="&#xdd;" u2="&#xc2;" k="52" />
<hkern u1="&#xdd;" u2="&#xc1;" k="52" />
<hkern u1="&#xdd;" u2="&#xc0;" k="52" />
<hkern u1="&#xdd;" u2="&#xbb;" k="40" />
<hkern u1="&#xdd;" u2="&#xae;" k="30" />
<hkern u1="&#xdd;" u2="&#xab;" k="60" />
<hkern u1="&#xdd;" u2="&#xa9;" k="30" />
<hkern u1="&#xdd;" u2="&#x7d;" k="-50" />
<hkern u1="&#xdd;" u2="z" k="30" />
<hkern u1="&#xdd;" u2="y" k="20" />
<hkern u1="&#xdd;" u2="x" k="20" />
<hkern u1="&#xdd;" u2="w" k="10" />
<hkern u1="&#xdd;" u2="v" k="20" />
<hkern u1="&#xdd;" u2="u" k="40" />
<hkern u1="&#xdd;" u2="t" k="20" />
<hkern u1="&#xdd;" u2="s" k="52" />
<hkern u1="&#xdd;" u2="r" k="40" />
<hkern u1="&#xdd;" u2="q" k="64" />
<hkern u1="&#xdd;" u2="p" k="40" />
<hkern u1="&#xdd;" u2="o" k="64" />
<hkern u1="&#xdd;" u2="n" k="40" />
<hkern u1="&#xdd;" u2="m" k="40" />
<hkern u1="&#xdd;" u2="g" k="40" />
<hkern u1="&#xdd;" u2="f" k="10" />
<hkern u1="&#xdd;" u2="e" k="64" />
<hkern u1="&#xdd;" u2="d" k="64" />
<hkern u1="&#xdd;" u2="c" k="64" />
<hkern u1="&#xdd;" u2="a" k="64" />
<hkern u1="&#xdd;" u2="]" k="-50" />
<hkern u1="&#xdd;" u2="X" k="-16" />
<hkern u1="&#xdd;" u2="V" k="-26" />
<hkern u1="&#xdd;" u2="T" k="-25" />
<hkern u1="&#xdd;" u2="S" k="-10" />
<hkern u1="&#xdd;" u2="Q" k="21" />
<hkern u1="&#xdd;" u2="O" k="21" />
<hkern u1="&#xdd;" u2="J" k="79" />
<hkern u1="&#xdd;" u2="G" k="21" />
<hkern u1="&#xdd;" u2="C" k="21" />
<hkern u1="&#xdd;" u2="A" k="52" />
<hkern u1="&#xdd;" u2="&#x40;" k="30" />
<hkern u1="&#xdd;" u2="&#x3b;" k="30" />
<hkern u1="&#xdd;" u2="&#x3a;" k="30" />
<hkern u1="&#xdd;" u2="&#x2f;" k="27" />
<hkern u1="&#xdd;" u2="&#x2e;" k="50" />
<hkern u1="&#xdd;" u2="&#x2d;" k="50" />
<hkern u1="&#xdd;" u2="&#x2c;" k="50" />
<hkern u1="&#xdd;" u2="&#x29;" k="-50" />
<hkern u1="&#xdd;" u2="&#x26;" k="30" />
<hkern u1="&#xde;" u2="&#x2026;" k="36" />
<hkern u1="&#xde;" u2="&#x201e;" k="36" />
<hkern u1="&#xde;" u2="&#x201c;" k="20" />
<hkern u1="&#xde;" u2="&#x201a;" k="36" />
<hkern u1="&#xde;" u2="&#x2018;" k="20" />
<hkern u1="&#xde;" u2="&#x178;" k="21" />
<hkern u1="&#xde;" u2="&#x153;" k="3" />
<hkern u1="&#xde;" u2="&#xe7;" k="3" />
<hkern u1="&#xde;" u2="&#xe6;" k="3" />
<hkern u1="&#xde;" u2="&#xdd;" k="21" />
<hkern u1="&#xde;" u2="&#xc6;" k="20" />
<hkern u1="&#xde;" u2="&#xc5;" k="20" />
<hkern u1="&#xde;" u2="&#xc4;" k="20" />
<hkern u1="&#xde;" u2="&#xc3;" k="20" />
<hkern u1="&#xde;" u2="&#xc2;" k="20" />
<hkern u1="&#xde;" u2="&#xc1;" k="20" />
<hkern u1="&#xde;" u2="&#xc0;" k="20" />
<hkern u1="&#xde;" u2="z" k="10" />
<hkern u1="&#xde;" u2="x" k="10" />
<hkern u1="&#xde;" u2="u" k="3" />
<hkern u1="&#xde;" u2="r" k="3" />
<hkern u1="&#xde;" u2="q" k="3" />
<hkern u1="&#xde;" u2="p" k="3" />
<hkern u1="&#xde;" u2="o" k="3" />
<hkern u1="&#xde;" u2="n" k="3" />
<hkern u1="&#xde;" u2="m" k="3" />
<hkern u1="&#xde;" u2="l" k="6" />
<hkern u1="&#xde;" u2="k" k="6" />
<hkern u1="&#xde;" u2="h" k="6" />
<hkern u1="&#xde;" u2="e" k="3" />
<hkern u1="&#xde;" u2="d" k="3" />
<hkern u1="&#xde;" u2="c" k="3" />
<hkern u1="&#xde;" u2="b" k="6" />
<hkern u1="&#xde;" u2="a" k="3" />
<hkern u1="&#xde;" u2="Z" k="29" />
<hkern u1="&#xde;" u2="Y" k="21" />
<hkern u1="&#xde;" u2="X" k="27" />
<hkern u1="&#xde;" u2="W" k="36" />
<hkern u1="&#xde;" u2="V" k="20" />
<hkern u1="&#xde;" u2="T" k="23" />
<hkern u1="&#xde;" u2="J" k="32" />
<hkern u1="&#xde;" u2="A" k="20" />
<hkern u1="&#xde;" u2="&#x3f;" k="20" />
<hkern u1="&#xde;" u2="&#x2f;" k="50" />
<hkern u1="&#xde;" u2="&#x2e;" k="36" />
<hkern u1="&#xde;" u2="&#x2c;" k="36" />
<hkern u1="&#xdf;" u2="&#x2122;" k="30" />
<hkern u1="&#xdf;" u2="&#x2026;" k="20" />
<hkern u1="&#xdf;" u2="&#x201e;" k="20" />
<hkern u1="&#xdf;" u2="&#x201c;" k="31" />
<hkern u1="&#xdf;" u2="&#x201a;" k="20" />
<hkern u1="&#xdf;" u2="&#x2018;" k="31" />
<hkern u1="&#xdf;" u2="z" k="17" />
<hkern u1="&#xdf;" u2="y" k="17" />
<hkern u1="&#xdf;" u2="x" k="20" />
<hkern u1="&#xdf;" u2="w" k="7" />
<hkern u1="&#xdf;" u2="v" k="17" />
<hkern u1="&#xdf;" u2="t" k="10" />
<hkern u1="&#xdf;" u2="g" k="10" />
<hkern u1="&#xdf;" u2="f" k="5" />
<hkern u1="&#xdf;" u2="\" k="10" />
<hkern u1="&#xdf;" u2="&#x3f;" k="27" />
<hkern u1="&#xdf;" u2="&#x3b;" k="10" />
<hkern u1="&#xdf;" u2="&#x3a;" k="10" />
<hkern u1="&#xdf;" u2="&#x2f;" k="10" />
<hkern u1="&#xdf;" u2="&#x2e;" k="20" />
<hkern u1="&#xdf;" u2="&#x2c;" k="20" />
<hkern u1="&#xdf;" u2="&#x21;" k="9" />
<hkern u1="&#xe0;" u2="&#x2122;" k="20" />
<hkern u1="&#xe0;" u2="&#x201c;" k="30" />
<hkern u1="&#xe0;" u2="&#x2018;" k="30" />
<hkern u1="&#xe0;" u2="y" k="7" />
<hkern u1="&#xe0;" u2="v" k="7" />
<hkern u1="&#xe0;" u2="t" k="10" />
<hkern u1="&#xe0;" u2="f" k="2" />
<hkern u1="&#xe0;" u2="\" k="10" />
<hkern u1="&#xe0;" u2="&#x3f;" k="15" />
<hkern u1="&#xe1;" u2="&#x2122;" k="20" />
<hkern u1="&#xe1;" u2="&#x201c;" k="30" />
<hkern u1="&#xe1;" u2="&#x2018;" k="30" />
<hkern u1="&#xe1;" u2="y" k="7" />
<hkern u1="&#xe1;" u2="v" k="7" />
<hkern u1="&#xe1;" u2="t" k="10" />
<hkern u1="&#xe1;" u2="f" k="2" />
<hkern u1="&#xe1;" u2="\" k="10" />
<hkern u1="&#xe1;" u2="&#x3f;" k="15" />
<hkern u1="&#xe2;" u2="&#x2122;" k="20" />
<hkern u1="&#xe2;" u2="&#x201c;" k="30" />
<hkern u1="&#xe2;" u2="&#x2018;" k="30" />
<hkern u1="&#xe2;" u2="y" k="7" />
<hkern u1="&#xe2;" u2="v" k="7" />
<hkern u1="&#xe2;" u2="t" k="10" />
<hkern u1="&#xe2;" u2="f" k="2" />
<hkern u1="&#xe2;" u2="\" k="10" />
<hkern u1="&#xe2;" u2="&#x3f;" k="15" />
<hkern u1="&#xe4;" u2="&#x2122;" k="20" />
<hkern u1="&#xe4;" u2="&#x201c;" k="30" />
<hkern u1="&#xe4;" u2="&#x2018;" k="30" />
<hkern u1="&#xe4;" u2="y" k="7" />
<hkern u1="&#xe4;" u2="v" k="7" />
<hkern u1="&#xe4;" u2="t" k="10" />
<hkern u1="&#xe4;" u2="f" k="2" />
<hkern u1="&#xe4;" u2="\" k="10" />
<hkern u1="&#xe4;" u2="&#x3f;" k="15" />
<hkern u1="&#xe6;" u2="y" k="15" />
<hkern u1="&#xe6;" u2="x" k="17" />
<hkern u1="&#xe6;" u2="w" k="5" />
<hkern u1="&#xe6;" u2="v" k="15" />
<hkern u1="&#xe6;" u2="t" k="2" />
<hkern u1="&#xe6;" u2="f" k="5" />
<hkern u1="&#xe7;" u2="&#x2039;" k="9" />
<hkern u1="&#xe7;" u2="&#xab;" k="9" />
<hkern u1="&#xe8;" u2="y" k="15" />
<hkern u1="&#xe8;" u2="x" k="17" />
<hkern u1="&#xe8;" u2="w" k="5" />
<hkern u1="&#xe8;" u2="v" k="15" />
<hkern u1="&#xe8;" u2="t" k="2" />
<hkern u1="&#xe8;" u2="f" k="5" />
<hkern u1="&#xe9;" u2="y" k="15" />
<hkern u1="&#xe9;" u2="x" k="17" />
<hkern u1="&#xe9;" u2="w" k="5" />
<hkern u1="&#xe9;" u2="v" k="15" />
<hkern u1="&#xe9;" u2="t" k="2" />
<hkern u1="&#xe9;" u2="f" k="5" />
<hkern u1="&#xea;" u2="y" k="15" />
<hkern u1="&#xea;" u2="x" k="17" />
<hkern u1="&#xea;" u2="w" k="5" />
<hkern u1="&#xea;" u2="v" k="15" />
<hkern u1="&#xea;" u2="t" k="2" />
<hkern u1="&#xea;" u2="f" k="5" />
<hkern u1="&#xeb;" u2="y" k="15" />
<hkern u1="&#xeb;" u2="x" k="17" />
<hkern u1="&#xeb;" u2="w" k="5" />
<hkern u1="&#xeb;" u2="v" k="15" />
<hkern u1="&#xeb;" u2="t" k="2" />
<hkern u1="&#xeb;" u2="f" k="5" />
<hkern u1="&#xf1;" u2="&#x2122;" k="20" />
<hkern u1="&#xf1;" u2="&#x201c;" k="30" />
<hkern u1="&#xf1;" u2="&#x2018;" k="30" />
<hkern u1="&#xf1;" u2="y" k="7" />
<hkern u1="&#xf1;" u2="v" k="7" />
<hkern u1="&#xf1;" u2="t" k="10" />
<hkern u1="&#xf1;" u2="f" k="2" />
<hkern u1="&#xf1;" u2="\" k="10" />
<hkern u1="&#xf1;" u2="&#x3f;" k="15" />
<hkern u1="&#xf3;" u2="&#x2122;" k="30" />
<hkern u1="&#xf3;" u2="&#x2026;" k="20" />
<hkern u1="&#xf3;" u2="&#x201e;" k="20" />
<hkern u1="&#xf3;" u2="&#x201c;" k="31" />
<hkern u1="&#xf3;" u2="&#x201a;" k="20" />
<hkern u1="&#xf3;" u2="&#x2018;" k="31" />
<hkern u1="&#xf3;" u2="z" k="17" />
<hkern u1="&#xf3;" u2="y" k="17" />
<hkern u1="&#xf3;" u2="x" k="20" />
<hkern u1="&#xf3;" u2="w" k="7" />
<hkern u1="&#xf3;" u2="v" k="17" />
<hkern u1="&#xf3;" u2="t" k="7" />
<hkern u1="&#xf3;" u2="f" k="5" />
<hkern u1="&#xf3;" u2="\" k="10" />
<hkern u1="&#xf3;" u2="&#x3f;" k="27" />
<hkern u1="&#xf3;" u2="&#x3b;" k="10" />
<hkern u1="&#xf3;" u2="&#x3a;" k="10" />
<hkern u1="&#xf3;" u2="&#x2f;" k="10" />
<hkern u1="&#xf3;" u2="&#x2e;" k="20" />
<hkern u1="&#xf3;" u2="&#x2c;" k="20" />
<hkern u1="&#xf3;" u2="&#x21;" k="9" />
<hkern u1="&#xf4;" u2="&#x2122;" k="30" />
<hkern u1="&#xf4;" u2="&#x2026;" k="20" />
<hkern u1="&#xf4;" u2="&#x201e;" k="20" />
<hkern u1="&#xf4;" u2="&#x201c;" k="31" />
<hkern u1="&#xf4;" u2="&#x201a;" k="20" />
<hkern u1="&#xf4;" u2="&#x2018;" k="31" />
<hkern u1="&#xf4;" u2="z" k="17" />
<hkern u1="&#xf4;" u2="y" k="17" />
<hkern u1="&#xf4;" u2="x" k="20" />
<hkern u1="&#xf4;" u2="w" k="7" />
<hkern u1="&#xf4;" u2="v" k="17" />
<hkern u1="&#xf4;" u2="t" k="7" />
<hkern u1="&#xf4;" u2="f" k="5" />
<hkern u1="&#xf4;" u2="\" k="10" />
<hkern u1="&#xf4;" u2="&#x3f;" k="27" />
<hkern u1="&#xf4;" u2="&#x3b;" k="10" />
<hkern u1="&#xf4;" u2="&#x3a;" k="10" />
<hkern u1="&#xf4;" u2="&#x2f;" k="10" />
<hkern u1="&#xf4;" u2="&#x2e;" k="20" />
<hkern u1="&#xf4;" u2="&#x2c;" k="20" />
<hkern u1="&#xf4;" u2="&#x21;" k="9" />
<hkern u1="&#xf5;" u2="&#x2122;" k="30" />
<hkern u1="&#xf5;" u2="&#x2026;" k="20" />
<hkern u1="&#xf5;" u2="&#x201e;" k="20" />
<hkern u1="&#xf5;" u2="&#x201c;" k="31" />
<hkern u1="&#xf5;" u2="&#x201a;" k="20" />
<hkern u1="&#xf5;" u2="&#x2018;" k="31" />
<hkern u1="&#xf5;" u2="z" k="17" />
<hkern u1="&#xf5;" u2="y" k="17" />
<hkern u1="&#xf5;" u2="x" k="20" />
<hkern u1="&#xf5;" u2="w" k="7" />
<hkern u1="&#xf5;" u2="v" k="17" />
<hkern u1="&#xf5;" u2="t" k="7" />
<hkern u1="&#xf5;" u2="f" k="5" />
<hkern u1="&#xf5;" u2="\" k="10" />
<hkern u1="&#xf5;" u2="&#x3f;" k="27" />
<hkern u1="&#xf5;" u2="&#x3b;" k="10" />
<hkern u1="&#xf5;" u2="&#x3a;" k="10" />
<hkern u1="&#xf5;" u2="&#x2f;" k="10" />
<hkern u1="&#xf5;" u2="&#x2e;" k="20" />
<hkern u1="&#xf5;" u2="&#x2c;" k="20" />
<hkern u1="&#xf5;" u2="&#x21;" k="9" />
<hkern u1="&#xf6;" u2="&#x2122;" k="30" />
<hkern u1="&#xf6;" u2="&#x2026;" k="20" />
<hkern u1="&#xf6;" u2="&#x201e;" k="20" />
<hkern u1="&#xf6;" u2="&#x201c;" k="31" />
<hkern u1="&#xf6;" u2="&#x201a;" k="20" />
<hkern u1="&#xf6;" u2="&#x2018;" k="31" />
<hkern u1="&#xf6;" u2="z" k="17" />
<hkern u1="&#xf6;" u2="y" k="17" />
<hkern u1="&#xf6;" u2="x" k="20" />
<hkern u1="&#xf6;" u2="w" k="7" />
<hkern u1="&#xf6;" u2="v" k="17" />
<hkern u1="&#xf6;" u2="t" k="7" />
<hkern u1="&#xf6;" u2="f" k="5" />
<hkern u1="&#xf6;" u2="\" k="10" />
<hkern u1="&#xf6;" u2="&#x3f;" k="27" />
<hkern u1="&#xf6;" u2="&#x3b;" k="10" />
<hkern u1="&#xf6;" u2="&#x3a;" k="10" />
<hkern u1="&#xf6;" u2="&#x2f;" k="10" />
<hkern u1="&#xf6;" u2="&#x2e;" k="20" />
<hkern u1="&#xf6;" u2="&#x2c;" k="20" />
<hkern u1="&#xf6;" u2="&#x21;" k="9" />
<hkern u1="&#xf8;" u2="&#x2122;" k="30" />
<hkern u1="&#xf8;" u2="&#x2026;" k="20" />
<hkern u1="&#xf8;" u2="&#x201e;" k="20" />
<hkern u1="&#xf8;" u2="&#x201c;" k="31" />
<hkern u1="&#xf8;" u2="&#x201a;" k="20" />
<hkern u1="&#xf8;" u2="&#x2018;" k="31" />
<hkern u1="&#xf8;" u2="z" k="17" />
<hkern u1="&#xf8;" u2="y" k="17" />
<hkern u1="&#xf8;" u2="x" k="20" />
<hkern u1="&#xf8;" u2="w" k="7" />
<hkern u1="&#xf8;" u2="v" k="17" />
<hkern u1="&#xf8;" u2="t" k="7" />
<hkern u1="&#xf8;" u2="f" k="5" />
<hkern u1="&#xf8;" u2="\" k="10" />
<hkern u1="&#xf8;" u2="&#x3f;" k="27" />
<hkern u1="&#xf8;" u2="&#x3b;" k="10" />
<hkern u1="&#xf8;" u2="&#x3a;" k="10" />
<hkern u1="&#xf8;" u2="&#x2f;" k="10" />
<hkern u1="&#xf8;" u2="&#x2e;" k="20" />
<hkern u1="&#xf8;" u2="&#x2c;" k="20" />
<hkern u1="&#xf8;" u2="&#x21;" k="9" />
<hkern u1="&#x152;" u2="&#x2039;" k="9" />
<hkern u1="&#x152;" u2="&#x153;" k="23" />
<hkern u1="&#x152;" u2="&#x152;" k="11" />
<hkern u1="&#x152;" u2="&#xfe;" k="2" />
<hkern u1="&#x152;" u2="&#xe7;" k="23" />
<hkern u1="&#x152;" u2="&#xe6;" k="23" />
<hkern u1="&#x152;" u2="&#xd8;" k="11" />
<hkern u1="&#x152;" u2="&#xd6;" k="11" />
<hkern u1="&#x152;" u2="&#xd5;" k="11" />
<hkern u1="&#x152;" u2="&#xd4;" k="11" />
<hkern u1="&#x152;" u2="&#xd3;" k="11" />
<hkern u1="&#x152;" u2="&#xd2;" k="11" />
<hkern u1="&#x152;" u2="&#xae;" k="20" />
<hkern u1="&#x152;" u2="&#xab;" k="9" />
<hkern u1="&#x152;" u2="&#xa9;" k="20" />
<hkern u1="&#x152;" u2="y" k="20" />
<hkern u1="&#x152;" u2="v" k="20" />
<hkern u1="&#x152;" u2="u" k="5" />
<hkern u1="&#x152;" u2="r" k="5" />
<hkern u1="&#x152;" u2="q" k="23" />
<hkern u1="&#x152;" u2="p" k="5" />
<hkern u1="&#x152;" u2="o" k="23" />
<hkern u1="&#x152;" u2="n" k="5" />
<hkern u1="&#x152;" u2="m" k="5" />
<hkern u1="&#x152;" u2="l" k="2" />
<hkern u1="&#x152;" u2="k" k="2" />
<hkern u1="&#x152;" u2="h" k="2" />
<hkern u1="&#x152;" u2="g" k="12" />
<hkern u1="&#x152;" u2="f" k="10" />
<hkern u1="&#x152;" u2="e" k="23" />
<hkern u1="&#x152;" u2="d" k="23" />
<hkern u1="&#x152;" u2="c" k="23" />
<hkern u1="&#x152;" u2="b" k="2" />
<hkern u1="&#x152;" u2="a" k="23" />
<hkern u1="&#x152;" u2="W" k="2" />
<hkern u1="&#x152;" u2="V" k="-5" />
<hkern u1="&#x152;" u2="T" k="-11" />
<hkern u1="&#x152;" u2="Q" k="11" />
<hkern u1="&#x152;" u2="O" k="11" />
<hkern u1="&#x152;" u2="J" k="-2" />
<hkern u1="&#x152;" u2="G" k="11" />
<hkern u1="&#x152;" u2="C" k="11" />
<hkern u1="&#x152;" u2="&#x40;" k="20" />
<hkern u1="&#x153;" u2="y" k="15" />
<hkern u1="&#x153;" u2="x" k="17" />
<hkern u1="&#x153;" u2="w" k="5" />
<hkern u1="&#x153;" u2="v" k="15" />
<hkern u1="&#x153;" u2="t" k="2" />
<hkern u1="&#x153;" u2="f" k="5" />
<hkern u1="&#x178;" u2="&#x203a;" k="40" />
<hkern u1="&#x178;" u2="&#x2039;" k="60" />
<hkern u1="&#x178;" u2="&#x2026;" k="50" />
<hkern u1="&#x178;" u2="&#x201e;" k="50" />
<hkern u1="&#x178;" u2="&#x201a;" k="50" />
<hkern u1="&#x178;" u2="&#x2014;" k="50" />
<hkern u1="&#x178;" u2="&#x2013;" k="50" />
<hkern u1="&#x178;" u2="&#x153;" k="64" />
<hkern u1="&#x178;" u2="&#x152;" k="21" />
<hkern u1="&#x178;" u2="&#xe7;" k="64" />
<hkern u1="&#x178;" u2="&#xe6;" k="64" />
<hkern u1="&#x178;" u2="&#xd8;" k="21" />
<hkern u1="&#x178;" u2="&#xd6;" k="21" />
<hkern u1="&#x178;" u2="&#xd5;" k="21" />
<hkern u1="&#x178;" u2="&#xd4;" k="21" />
<hkern u1="&#x178;" u2="&#xd3;" k="21" />
<hkern u1="&#x178;" u2="&#xd2;" k="21" />
<hkern u1="&#x178;" u2="&#xc6;" k="52" />
<hkern u1="&#x178;" u2="&#xc5;" k="52" />
<hkern u1="&#x178;" u2="&#xc4;" k="52" />
<hkern u1="&#x178;" u2="&#xc3;" k="52" />
<hkern u1="&#x178;" u2="&#xc2;" k="52" />
<hkern u1="&#x178;" u2="&#xc1;" k="52" />
<hkern u1="&#x178;" u2="&#xc0;" k="52" />
<hkern u1="&#x178;" u2="&#xbb;" k="40" />
<hkern u1="&#x178;" u2="&#xae;" k="30" />
<hkern u1="&#x178;" u2="&#xab;" k="60" />
<hkern u1="&#x178;" u2="&#xa9;" k="30" />
<hkern u1="&#x178;" u2="&#x7d;" k="-50" />
<hkern u1="&#x178;" u2="z" k="30" />
<hkern u1="&#x178;" u2="y" k="20" />
<hkern u1="&#x178;" u2="x" k="20" />
<hkern u1="&#x178;" u2="w" k="10" />
<hkern u1="&#x178;" u2="v" k="20" />
<hkern u1="&#x178;" u2="u" k="40" />
<hkern u1="&#x178;" u2="t" k="20" />
<hkern u1="&#x178;" u2="s" k="52" />
<hkern u1="&#x178;" u2="r" k="40" />
<hkern u1="&#x178;" u2="q" k="64" />
<hkern u1="&#x178;" u2="p" k="40" />
<hkern u1="&#x178;" u2="o" k="64" />
<hkern u1="&#x178;" u2="n" k="40" />
<hkern u1="&#x178;" u2="m" k="40" />
<hkern u1="&#x178;" u2="g" k="40" />
<hkern u1="&#x178;" u2="f" k="10" />
<hkern u1="&#x178;" u2="e" k="64" />
<hkern u1="&#x178;" u2="d" k="64" />
<hkern u1="&#x178;" u2="c" k="64" />
<hkern u1="&#x178;" u2="a" k="64" />
<hkern u1="&#x178;" u2="]" k="-50" />
<hkern u1="&#x178;" u2="X" k="-16" />
<hkern u1="&#x178;" u2="V" k="-26" />
<hkern u1="&#x178;" u2="T" k="-25" />
<hkern u1="&#x178;" u2="S" k="-10" />
<hkern u1="&#x178;" u2="Q" k="21" />
<hkern u1="&#x178;" u2="O" k="21" />
<hkern u1="&#x178;" u2="J" k="79" />
<hkern u1="&#x178;" u2="G" k="21" />
<hkern u1="&#x178;" u2="C" k="21" />
<hkern u1="&#x178;" u2="A" k="52" />
<hkern u1="&#x178;" u2="&#x40;" k="30" />
<hkern u1="&#x178;" u2="&#x3b;" k="30" />
<hkern u1="&#x178;" u2="&#x3a;" k="30" />
<hkern u1="&#x178;" u2="&#x2f;" k="27" />
<hkern u1="&#x178;" u2="&#x2e;" k="50" />
<hkern u1="&#x178;" u2="&#x2d;" k="50" />
<hkern u1="&#x178;" u2="&#x2c;" k="50" />
<hkern u1="&#x178;" u2="&#x29;" k="-50" />
<hkern u1="&#x178;" u2="&#x26;" k="30" />
<hkern u1="&#x2013;" u2="&#x178;" k="50" />
<hkern u1="&#x2013;" u2="&#x153;" k="10" />
<hkern u1="&#x2013;" u2="&#xe7;" k="10" />
<hkern u1="&#x2013;" u2="&#xe6;" k="10" />
<hkern u1="&#x2013;" u2="&#xdd;" k="50" />
<hkern u1="&#x2013;" u2="&#xc6;" k="-10" />
<hkern u1="&#x2013;" u2="&#xc5;" k="-10" />
<hkern u1="&#x2013;" u2="&#xc4;" k="-10" />
<hkern u1="&#x2013;" u2="&#xc3;" k="-10" />
<hkern u1="&#x2013;" u2="&#xc2;" k="-10" />
<hkern u1="&#x2013;" u2="&#xc1;" k="-10" />
<hkern u1="&#x2013;" u2="&#xc0;" k="-10" />
<hkern u1="&#x2013;" u2="z" k="21" />
<hkern u1="&#x2013;" u2="y" k="10" />
<hkern u1="&#x2013;" u2="x" k="30" />
<hkern u1="&#x2013;" u2="v" k="10" />
<hkern u1="&#x2013;" u2="q" k="10" />
<hkern u1="&#x2013;" u2="o" k="10" />
<hkern u1="&#x2013;" u2="e" k="10" />
<hkern u1="&#x2013;" u2="d" k="10" />
<hkern u1="&#x2013;" u2="c" k="10" />
<hkern u1="&#x2013;" u2="a" k="10" />
<hkern u1="&#x2013;" u2="Z" k="10" />
<hkern u1="&#x2013;" u2="Y" k="50" />
<hkern u1="&#x2013;" u2="X" k="30" />
<hkern u1="&#x2013;" u2="W" k="40" />
<hkern u1="&#x2013;" u2="V" k="50" />
<hkern u1="&#x2013;" u2="T" k="61" />
<hkern u1="&#x2013;" u2="A" k="-10" />
<hkern u1="&#x2013;" u2="&#x37;" k="30" />
<hkern u1="&#x2013;" u2="&#x31;" k="20" />
<hkern u1="&#x2014;" u2="&#x178;" k="50" />
<hkern u1="&#x2014;" u2="&#x153;" k="10" />
<hkern u1="&#x2014;" u2="&#xe7;" k="10" />
<hkern u1="&#x2014;" u2="&#xe6;" k="10" />
<hkern u1="&#x2014;" u2="&#xdd;" k="50" />
<hkern u1="&#x2014;" u2="&#xc6;" k="-10" />
<hkern u1="&#x2014;" u2="&#xc5;" k="-10" />
<hkern u1="&#x2014;" u2="&#xc4;" k="-10" />
<hkern u1="&#x2014;" u2="&#xc3;" k="-10" />
<hkern u1="&#x2014;" u2="&#xc2;" k="-10" />
<hkern u1="&#x2014;" u2="&#xc1;" k="-10" />
<hkern u1="&#x2014;" u2="&#xc0;" k="-10" />
<hkern u1="&#x2014;" u2="z" k="21" />
<hkern u1="&#x2014;" u2="y" k="10" />
<hkern u1="&#x2014;" u2="x" k="30" />
<hkern u1="&#x2014;" u2="v" k="10" />
<hkern u1="&#x2014;" u2="q" k="10" />
<hkern u1="&#x2014;" u2="o" k="10" />
<hkern u1="&#x2014;" u2="e" k="10" />
<hkern u1="&#x2014;" u2="d" k="10" />
<hkern u1="&#x2014;" u2="c" k="10" />
<hkern u1="&#x2014;" u2="a" k="10" />
<hkern u1="&#x2014;" u2="Z" k="10" />
<hkern u1="&#x2014;" u2="Y" k="50" />
<hkern u1="&#x2014;" u2="X" k="30" />
<hkern u1="&#x2014;" u2="W" k="40" />
<hkern u1="&#x2014;" u2="V" k="50" />
<hkern u1="&#x2014;" u2="T" k="61" />
<hkern u1="&#x2014;" u2="A" k="-10" />
<hkern u1="&#x2014;" u2="&#x37;" k="30" />
<hkern u1="&#x2014;" u2="&#x31;" k="20" />
<hkern u1="&#x2018;" u2="&#x178;" k="-30" />
<hkern u1="&#x2018;" u2="&#x153;" k="30" />
<hkern u1="&#x2018;" u2="&#xe7;" k="30" />
<hkern u1="&#x2018;" u2="&#xe6;" k="30" />
<hkern u1="&#x2018;" u2="&#xdd;" k="-30" />
<hkern u1="&#x2018;" u2="&#xc6;" k="40" />
<hkern u1="&#x2018;" u2="&#xc5;" k="40" />
<hkern u1="&#x2018;" u2="&#xc4;" k="40" />
<hkern u1="&#x2018;" u2="&#xc3;" k="40" />
<hkern u1="&#x2018;" u2="&#xc2;" k="40" />
<hkern u1="&#x2018;" u2="&#xc1;" k="40" />
<hkern u1="&#x2018;" u2="&#xc0;" k="40" />
<hkern u1="&#x2018;" u2="u" k="10" />
<hkern u1="&#x2018;" u2="s" k="30" />
<hkern u1="&#x2018;" u2="r" k="10" />
<hkern u1="&#x2018;" u2="q" k="30" />
<hkern u1="&#x2018;" u2="p" k="10" />
<hkern u1="&#x2018;" u2="o" k="30" />
<hkern u1="&#x2018;" u2="n" k="10" />
<hkern u1="&#x2018;" u2="m" k="10" />
<hkern u1="&#x2018;" u2="g" k="40" />
<hkern u1="&#x2018;" u2="e" k="30" />
<hkern u1="&#x2018;" u2="d" k="30" />
<hkern u1="&#x2018;" u2="c" k="30" />
<hkern u1="&#x2018;" u2="a" k="30" />
<hkern u1="&#x2018;" u2="Y" k="-30" />
<hkern u1="&#x2018;" u2="X" k="-20" />
<hkern u1="&#x2018;" u2="W" k="-20" />
<hkern u1="&#x2018;" u2="V" k="-20" />
<hkern u1="&#x2018;" u2="T" k="-10" />
<hkern u1="&#x2018;" u2="J" k="115" />
<hkern u1="&#x2018;" u2="A" k="40" />
<hkern u1="&#x2019;" u2="&#x153;" k="50" />
<hkern u1="&#x2019;" u2="&#x152;" k="40" />
<hkern u1="&#x2019;" u2="&#xe7;" k="50" />
<hkern u1="&#x2019;" u2="&#xe6;" k="50" />
<hkern u1="&#x2019;" u2="&#xd8;" k="40" />
<hkern u1="&#x2019;" u2="&#xd6;" k="40" />
<hkern u1="&#x2019;" u2="&#xd5;" k="40" />
<hkern u1="&#x2019;" u2="&#xd4;" k="40" />
<hkern u1="&#x2019;" u2="&#xd3;" k="40" />
<hkern u1="&#x2019;" u2="&#xd2;" k="40" />
<hkern u1="&#x2019;" u2="&#xc6;" k="23" />
<hkern u1="&#x2019;" u2="&#xc5;" k="23" />
<hkern u1="&#x2019;" u2="&#xc4;" k="23" />
<hkern u1="&#x2019;" u2="&#xc3;" k="23" />
<hkern u1="&#x2019;" u2="&#xc2;" k="23" />
<hkern u1="&#x2019;" u2="&#xc1;" k="23" />
<hkern u1="&#x2019;" u2="&#xc0;" k="23" />
<hkern u1="&#x2019;" u2="q" k="50" />
<hkern u1="&#x2019;" u2="o" k="50" />
<hkern u1="&#x2019;" u2="e" k="50" />
<hkern u1="&#x2019;" u2="d" k="50" />
<hkern u1="&#x2019;" u2="c" k="50" />
<hkern u1="&#x2019;" u2="a" k="50" />
<hkern u1="&#x2019;" u2="S" k="10" />
<hkern u1="&#x2019;" u2="Q" k="40" />
<hkern u1="&#x2019;" u2="O" k="40" />
<hkern u1="&#x2019;" u2="J" k="120" />
<hkern u1="&#x2019;" u2="G" k="40" />
<hkern u1="&#x2019;" u2="C" k="40" />
<hkern u1="&#x2019;" u2="A" k="23" />
<hkern u1="&#x201a;" u2="&#x178;" k="50" />
<hkern u1="&#x201a;" u2="&#x153;" k="20" />
<hkern u1="&#x201a;" u2="&#x152;" k="36" />
<hkern u1="&#x201a;" u2="&#xe7;" k="20" />
<hkern u1="&#x201a;" u2="&#xe6;" k="20" />
<hkern u1="&#x201a;" u2="&#xdd;" k="50" />
<hkern u1="&#x201a;" u2="&#xdc;" k="10" />
<hkern u1="&#x201a;" u2="&#xdb;" k="10" />
<hkern u1="&#x201a;" u2="&#xda;" k="10" />
<hkern u1="&#x201a;" u2="&#xd9;" k="10" />
<hkern u1="&#x201a;" u2="&#xd8;" k="36" />
<hkern u1="&#x201a;" u2="&#xd6;" k="36" />
<hkern u1="&#x201a;" u2="&#xd5;" k="36" />
<hkern u1="&#x201a;" u2="&#xd4;" k="36" />
<hkern u1="&#x201a;" u2="&#xd3;" k="36" />
<hkern u1="&#x201a;" u2="&#xd2;" k="36" />
<hkern u1="&#x201a;" u2="&#xc6;" k="-30" />
<hkern u1="&#x201a;" u2="&#xc5;" k="-30" />
<hkern u1="&#x201a;" u2="&#xc4;" k="-30" />
<hkern u1="&#x201a;" u2="&#xc3;" k="-30" />
<hkern u1="&#x201a;" u2="&#xc2;" k="-30" />
<hkern u1="&#x201a;" u2="&#xc1;" k="-30" />
<hkern u1="&#x201a;" u2="&#xc0;" k="-30" />
<hkern u1="&#x201a;" u2="y" k="30" />
<hkern u1="&#x201a;" u2="w" k="10" />
<hkern u1="&#x201a;" u2="v" k="30" />
<hkern u1="&#x201a;" u2="u" k="20" />
<hkern u1="&#x201a;" u2="t" k="40" />
<hkern u1="&#x201a;" u2="r" k="20" />
<hkern u1="&#x201a;" u2="q" k="20" />
<hkern u1="&#x201a;" u2="p" k="20" />
<hkern u1="&#x201a;" u2="o" k="20" />
<hkern u1="&#x201a;" u2="n" k="20" />
<hkern u1="&#x201a;" u2="m" k="20" />
<hkern u1="&#x201a;" u2="j" k="-30" />
<hkern u1="&#x201a;" u2="f" k="18" />
<hkern u1="&#x201a;" u2="e" k="20" />
<hkern u1="&#x201a;" u2="d" k="20" />
<hkern u1="&#x201a;" u2="c" k="20" />
<hkern u1="&#x201a;" u2="a" k="20" />
<hkern u1="&#x201a;" u2="Y" k="50" />
<hkern u1="&#x201a;" u2="W" k="25" />
<hkern u1="&#x201a;" u2="V" k="65" />
<hkern u1="&#x201a;" u2="U" k="10" />
<hkern u1="&#x201a;" u2="T" k="74" />
<hkern u1="&#x201a;" u2="Q" k="36" />
<hkern u1="&#x201a;" u2="O" k="36" />
<hkern u1="&#x201a;" u2="G" k="36" />
<hkern u1="&#x201a;" u2="C" k="36" />
<hkern u1="&#x201a;" u2="A" k="-30" />
<hkern u1="&#x201a;" u2="&#x39;" k="6" />
<hkern u1="&#x201a;" u2="&#x38;" k="15" />
<hkern u1="&#x201a;" u2="&#x37;" k="5" />
<hkern u1="&#x201a;" u2="&#x36;" k="26" />
<hkern u1="&#x201a;" u2="&#x35;" k="-9" />
<hkern u1="&#x201a;" u2="&#x34;" k="28" />
<hkern u1="&#x201a;" u2="&#x33;" k="-5" />
<hkern u1="&#x201a;" u2="&#x32;" k="-5" />
<hkern u1="&#x201a;" u2="&#x31;" k="76" />
<hkern u1="&#x201a;" u2="&#x30;" k="27" />
<hkern u1="&#x201c;" u2="&#x178;" k="-30" />
<hkern u1="&#x201c;" u2="&#x153;" k="30" />
<hkern u1="&#x201c;" u2="&#xe7;" k="30" />
<hkern u1="&#x201c;" u2="&#xe6;" k="30" />
<hkern u1="&#x201c;" u2="&#xdd;" k="-30" />
<hkern u1="&#x201c;" u2="&#xc6;" k="40" />
<hkern u1="&#x201c;" u2="&#xc5;" k="40" />
<hkern u1="&#x201c;" u2="&#xc4;" k="40" />
<hkern u1="&#x201c;" u2="&#xc3;" k="40" />
<hkern u1="&#x201c;" u2="&#xc2;" k="40" />
<hkern u1="&#x201c;" u2="&#xc1;" k="40" />
<hkern u1="&#x201c;" u2="&#xc0;" k="40" />
<hkern u1="&#x201c;" u2="u" k="10" />
<hkern u1="&#x201c;" u2="s" k="30" />
<hkern u1="&#x201c;" u2="r" k="10" />
<hkern u1="&#x201c;" u2="q" k="30" />
<hkern u1="&#x201c;" u2="p" k="10" />
<hkern u1="&#x201c;" u2="o" k="30" />
<hkern u1="&#x201c;" u2="n" k="10" />
<hkern u1="&#x201c;" u2="m" k="10" />
<hkern u1="&#x201c;" u2="g" k="40" />
<hkern u1="&#x201c;" u2="e" k="30" />
<hkern u1="&#x201c;" u2="d" k="30" />
<hkern u1="&#x201c;" u2="c" k="30" />
<hkern u1="&#x201c;" u2="a" k="30" />
<hkern u1="&#x201c;" u2="Y" k="-30" />
<hkern u1="&#x201c;" u2="X" k="-20" />
<hkern u1="&#x201c;" u2="W" k="-20" />
<hkern u1="&#x201c;" u2="V" k="-20" />
<hkern u1="&#x201c;" u2="T" k="-10" />
<hkern u1="&#x201c;" u2="J" k="115" />
<hkern u1="&#x201c;" u2="A" k="40" />
<hkern u1="&#x201d;" u2="&#x153;" k="50" />
<hkern u1="&#x201d;" u2="&#x152;" k="40" />
<hkern u1="&#x201d;" u2="&#xe7;" k="50" />
<hkern u1="&#x201d;" u2="&#xe6;" k="50" />
<hkern u1="&#x201d;" u2="&#xd8;" k="40" />
<hkern u1="&#x201d;" u2="&#xd6;" k="40" />
<hkern u1="&#x201d;" u2="&#xd5;" k="40" />
<hkern u1="&#x201d;" u2="&#xd4;" k="40" />
<hkern u1="&#x201d;" u2="&#xd3;" k="40" />
<hkern u1="&#x201d;" u2="&#xd2;" k="40" />
<hkern u1="&#x201d;" u2="&#xc6;" k="23" />
<hkern u1="&#x201d;" u2="&#xc5;" k="23" />
<hkern u1="&#x201d;" u2="&#xc4;" k="23" />
<hkern u1="&#x201d;" u2="&#xc3;" k="23" />
<hkern u1="&#x201d;" u2="&#xc2;" k="23" />
<hkern u1="&#x201d;" u2="&#xc1;" k="23" />
<hkern u1="&#x201d;" u2="&#xc0;" k="23" />
<hkern u1="&#x201d;" u2="q" k="50" />
<hkern u1="&#x201d;" u2="o" k="50" />
<hkern u1="&#x201d;" u2="e" k="50" />
<hkern u1="&#x201d;" u2="d" k="50" />
<hkern u1="&#x201d;" u2="c" k="50" />
<hkern u1="&#x201d;" u2="a" k="50" />
<hkern u1="&#x201d;" u2="S" k="10" />
<hkern u1="&#x201d;" u2="Q" k="40" />
<hkern u1="&#x201d;" u2="O" k="40" />
<hkern u1="&#x201d;" u2="J" k="120" />
<hkern u1="&#x201d;" u2="G" k="40" />
<hkern u1="&#x201d;" u2="C" k="40" />
<hkern u1="&#x201d;" u2="A" k="23" />
<hkern u1="&#x201e;" u2="&#x178;" k="50" />
<hkern u1="&#x201e;" u2="&#x153;" k="20" />
<hkern u1="&#x201e;" u2="&#x152;" k="36" />
<hkern u1="&#x201e;" u2="&#xe7;" k="20" />
<hkern u1="&#x201e;" u2="&#xe6;" k="20" />
<hkern u1="&#x201e;" u2="&#xdd;" k="50" />
<hkern u1="&#x201e;" u2="&#xdc;" k="10" />
<hkern u1="&#x201e;" u2="&#xdb;" k="10" />
<hkern u1="&#x201e;" u2="&#xda;" k="10" />
<hkern u1="&#x201e;" u2="&#xd9;" k="10" />
<hkern u1="&#x201e;" u2="&#xd8;" k="36" />
<hkern u1="&#x201e;" u2="&#xd6;" k="36" />
<hkern u1="&#x201e;" u2="&#xd5;" k="36" />
<hkern u1="&#x201e;" u2="&#xd4;" k="36" />
<hkern u1="&#x201e;" u2="&#xd3;" k="36" />
<hkern u1="&#x201e;" u2="&#xd2;" k="36" />
<hkern u1="&#x201e;" u2="&#xc6;" k="-30" />
<hkern u1="&#x201e;" u2="&#xc5;" k="-30" />
<hkern u1="&#x201e;" u2="&#xc4;" k="-30" />
<hkern u1="&#x201e;" u2="&#xc3;" k="-30" />
<hkern u1="&#x201e;" u2="&#xc2;" k="-30" />
<hkern u1="&#x201e;" u2="&#xc1;" k="-30" />
<hkern u1="&#x201e;" u2="&#xc0;" k="-30" />
<hkern u1="&#x201e;" u2="y" k="30" />
<hkern u1="&#x201e;" u2="w" k="10" />
<hkern u1="&#x201e;" u2="v" k="30" />
<hkern u1="&#x201e;" u2="u" k="20" />
<hkern u1="&#x201e;" u2="t" k="40" />
<hkern u1="&#x201e;" u2="r" k="20" />
<hkern u1="&#x201e;" u2="q" k="20" />
<hkern u1="&#x201e;" u2="p" k="20" />
<hkern u1="&#x201e;" u2="o" k="20" />
<hkern u1="&#x201e;" u2="n" k="20" />
<hkern u1="&#x201e;" u2="m" k="20" />
<hkern u1="&#x201e;" u2="j" k="-30" />
<hkern u1="&#x201e;" u2="f" k="18" />
<hkern u1="&#x201e;" u2="e" k="20" />
<hkern u1="&#x201e;" u2="d" k="20" />
<hkern u1="&#x201e;" u2="c" k="20" />
<hkern u1="&#x201e;" u2="a" k="20" />
<hkern u1="&#x201e;" u2="Y" k="50" />
<hkern u1="&#x201e;" u2="W" k="25" />
<hkern u1="&#x201e;" u2="V" k="65" />
<hkern u1="&#x201e;" u2="U" k="10" />
<hkern u1="&#x201e;" u2="T" k="74" />
<hkern u1="&#x201e;" u2="Q" k="36" />
<hkern u1="&#x201e;" u2="O" k="36" />
<hkern u1="&#x201e;" u2="G" k="36" />
<hkern u1="&#x201e;" u2="C" k="36" />
<hkern u1="&#x201e;" u2="A" k="-30" />
<hkern u1="&#x201e;" u2="&#x39;" k="6" />
<hkern u1="&#x201e;" u2="&#x38;" k="15" />
<hkern u1="&#x201e;" u2="&#x37;" k="5" />
<hkern u1="&#x201e;" u2="&#x36;" k="26" />
<hkern u1="&#x201e;" u2="&#x35;" k="-9" />
<hkern u1="&#x201e;" u2="&#x34;" k="28" />
<hkern u1="&#x201e;" u2="&#x33;" k="-5" />
<hkern u1="&#x201e;" u2="&#x32;" k="-5" />
<hkern u1="&#x201e;" u2="&#x31;" k="76" />
<hkern u1="&#x201e;" u2="&#x30;" k="27" />
<hkern u1="&#x2026;" u2="&#x178;" k="50" />
<hkern u1="&#x2026;" u2="&#x153;" k="20" />
<hkern u1="&#x2026;" u2="&#x152;" k="36" />
<hkern u1="&#x2026;" u2="&#xe7;" k="20" />
<hkern u1="&#x2026;" u2="&#xe6;" k="20" />
<hkern u1="&#x2026;" u2="&#xdd;" k="50" />
<hkern u1="&#x2026;" u2="&#xdc;" k="10" />
<hkern u1="&#x2026;" u2="&#xdb;" k="10" />
<hkern u1="&#x2026;" u2="&#xda;" k="10" />
<hkern u1="&#x2026;" u2="&#xd9;" k="10" />
<hkern u1="&#x2026;" u2="&#xd8;" k="36" />
<hkern u1="&#x2026;" u2="&#xd6;" k="36" />
<hkern u1="&#x2026;" u2="&#xd5;" k="36" />
<hkern u1="&#x2026;" u2="&#xd4;" k="36" />
<hkern u1="&#x2026;" u2="&#xd3;" k="36" />
<hkern u1="&#x2026;" u2="&#xd2;" k="36" />
<hkern u1="&#x2026;" u2="&#xc6;" k="-30" />
<hkern u1="&#x2026;" u2="&#xc5;" k="-30" />
<hkern u1="&#x2026;" u2="&#xc4;" k="-30" />
<hkern u1="&#x2026;" u2="&#xc3;" k="-30" />
<hkern u1="&#x2026;" u2="&#xc2;" k="-30" />
<hkern u1="&#x2026;" u2="&#xc1;" k="-30" />
<hkern u1="&#x2026;" u2="&#xc0;" k="-30" />
<hkern u1="&#x2026;" u2="y" k="30" />
<hkern u1="&#x2026;" u2="w" k="10" />
<hkern u1="&#x2026;" u2="v" k="30" />
<hkern u1="&#x2026;" u2="u" k="20" />
<hkern u1="&#x2026;" u2="t" k="40" />
<hkern u1="&#x2026;" u2="r" k="20" />
<hkern u1="&#x2026;" u2="q" k="20" />
<hkern u1="&#x2026;" u2="p" k="20" />
<hkern u1="&#x2026;" u2="o" k="20" />
<hkern u1="&#x2026;" u2="n" k="20" />
<hkern u1="&#x2026;" u2="m" k="20" />
<hkern u1="&#x2026;" u2="f" k="18" />
<hkern u1="&#x2026;" u2="e" k="20" />
<hkern u1="&#x2026;" u2="d" k="20" />
<hkern u1="&#x2026;" u2="c" k="20" />
<hkern u1="&#x2026;" u2="a" k="20" />
<hkern u1="&#x2026;" u2="Y" k="50" />
<hkern u1="&#x2026;" u2="W" k="25" />
<hkern u1="&#x2026;" u2="V" k="65" />
<hkern u1="&#x2026;" u2="U" k="10" />
<hkern u1="&#x2026;" u2="T" k="74" />
<hkern u1="&#x2026;" u2="Q" k="36" />
<hkern u1="&#x2026;" u2="O" k="36" />
<hkern u1="&#x2026;" u2="G" k="36" />
<hkern u1="&#x2026;" u2="C" k="36" />
<hkern u1="&#x2026;" u2="A" k="-30" />
<hkern u1="&#x2026;" u2="&#x39;" k="6" />
<hkern u1="&#x2026;" u2="&#x38;" k="15" />
<hkern u1="&#x2026;" u2="&#x37;" k="5" />
<hkern u1="&#x2026;" u2="&#x36;" k="26" />
<hkern u1="&#x2026;" u2="&#x35;" k="-9" />
<hkern u1="&#x2026;" u2="&#x34;" k="28" />
<hkern u1="&#x2026;" u2="&#x33;" k="-5" />
<hkern u1="&#x2026;" u2="&#x32;" k="-5" />
<hkern u1="&#x2026;" u2="&#x31;" k="76" />
<hkern u1="&#x2026;" u2="&#x30;" k="27" />
<hkern u1="&#x2039;" u2="&#x178;" k="40" />
<hkern u1="&#x2039;" u2="&#xdd;" k="40" />
<hkern u1="&#x2039;" u2="f" k="-9" />
<hkern u1="&#x2039;" u2="Y" k="40" />
<hkern u1="&#x2039;" u2="W" k="20" />
<hkern u1="&#x2039;" u2="V" k="20" />
<hkern u1="&#x2039;" u2="T" k="40" />
<hkern u1="&#x203a;" u2="&#x178;" k="50" />
<hkern u1="&#x203a;" u2="&#xdd;" k="50" />
<hkern u1="&#x203a;" u2="z" k="41" />
<hkern u1="&#x203a;" u2="y" k="14" />
<hkern u1="&#x203a;" u2="x" k="50" />
<hkern u1="&#x203a;" u2="w" k="9" />
<hkern u1="&#x203a;" u2="v" k="14" />
<hkern u1="&#x203a;" u2="f" k="5" />
<hkern u1="&#x203a;" u2="Y" k="50" />
<hkern u1="&#x203a;" u2="W" k="40" />
<hkern u1="&#x203a;" u2="V" k="50" />
<hkern u1="&#x203a;" u2="T" k="110" />
</font>
</defs></svg> 