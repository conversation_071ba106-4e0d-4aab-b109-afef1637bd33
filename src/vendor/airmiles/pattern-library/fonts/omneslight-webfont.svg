<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="omneslight" horiz-adv-x="1505" >
<font-face units-per-em="2048" ascent="1434" descent="-614" />
<missing-glyph horiz-adv-x="360" />
<glyph unicode="&#xfb01;" horiz-adv-x="1038" d="M20 920v10q0 20 10.5 29.5t37.5 9.5h161v104q0 165 70 250.5t201 85.5q109 0 162 -53q22 -22 22 -51q0 -15 -6.5 -27t-13.5 -17t-9 -4q-24 28 -67 47t-88 19q-89 0 -132.5 -62t-43.5 -194v-98h215q49 0 49 -39v-10q0 -39 -49 -39h-213v-840q0 -47 -43 -47h-11 q-43 0 -43 47v840h-161q-27 0 -37.5 9.5t-10.5 29.5zM770 1298q0 70 72 70q65 0 65 -70q0 -67 -69 -67q-68 0 -68 67zM788 41v887q0 47 43 47h13q43 0 43 -47v-887q0 -47 -43 -47h-13q-43 0 -43 47z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1038" d="M20 920v10q0 20 10.5 29.5t37.5 9.5h161v104q0 165 70 250.5t201 85.5q109 0 162 -53q22 -22 22 -51q0 -15 -6.5 -27t-13.5 -17t-9 -4q-24 28 -67 47t-88 19q-89 0 -132.5 -62t-43.5 -194v-98h215q49 0 49 -39v-10q0 -39 -49 -39h-213v-840q0 -47 -43 -47h-11 q-43 0 -43 47v840h-161q-27 0 -37.5 9.5t-10.5 29.5zM788 41v1337q0 3 11 7t24 4q64 0 64 -97v-1251q0 -47 -43 -47h-10q-46 0 -46 47z" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="360" />
<glyph unicode=" "  horiz-adv-x="360" />
<glyph unicode="&#x09;" horiz-adv-x="360" />
<glyph unicode="&#xa0;" horiz-adv-x="360" />
<glyph unicode="!" horiz-adv-x="475" d="M160 61v15q0 71 69 71h17q69 0 69 -71v-15q0 -71 -69 -71h-17q-69 0 -69 71zM176 1272q-3 59 43 59h37q46 0 43 -59l-31 -877q-1 -16 -2.5 -23.5t-6.5 -12.5t-15 -5h-13q-22 0 -22 41z" />
<glyph unicode="&#x22;" horiz-adv-x="679" d="M139 1249q-4 45 11 63.5t51 18.5q35 0 50 -18t11 -64l-29 -368q-2 -22 -6.5 -31.5t-17.5 -9.5h-16q-13 0 -18.5 9.5t-8.5 31.5zM416 1249q-4 46 11 64t50 18q36 0 51 -18.5t11 -63.5l-29 -368q-3 -41 -25 -41h-16q-22 0 -25 41z" />
<glyph unicode="#" horiz-adv-x="1316" d="M31 416v8q0 41 47 41h244l122 395h-215q-47 0 -47 39v8q0 41 47 41h242l115 379q4 4 20 4q32 0 44.5 -27.5t-5.5 -95.5l-80 -260h361l114 379q4 4 21 4q32 0 44.5 -27.5t-5.5 -95.5l-80 -260h219q47 0 47 -39v-8q0 -41 -47 -41h-246l-121 -395h215q48 0 48 -39v-8 q0 -41 -48 -41h-241l-115 -379q-4 -4 -20 -4q-32 0 -44.5 28t5.5 95l80 260h-361l-115 -379q-4 -4 -20 -4q-32 0 -44.5 28t5.5 95l80 260h-219q-47 0 -47 39zM416 463h364l121 399h-364z" />
<glyph unicode="$" horiz-adv-x="1095" d="M84 246q0 19 14 35t28.5 22t18.5 4q38 -89 130 -154.5t233 -76.5v557q-48 12 -85 23t-79.5 28t-73.5 35.5t-61.5 45.5t-49.5 58.5t-30.5 74t-11.5 91.5q0 143 110.5 241.5t278.5 110.5v134q0 27 10 37t31 10h10q20 0 29.5 -10.5t9.5 -36.5v-132q108 -7 192.5 -42.5 t133.5 -86.5q57 -54 57 -104q0 -19 -14.5 -35t-29 -22.5t-17.5 -3.5q-36 79 -120.5 135.5t-205.5 66.5v-534q72 -17 124 -33.5t111.5 -47t96.5 -67.5t61.5 -95t24.5 -130q0 -150 -117 -251.5t-299 -110.5v-138q0 -47 -41 -47h-10q-21 0 -31 10t-10 37v140q-231 17 -355 141 q-63 63 -63 121zM219 989q0 -57 20.5 -99t63.5 -70.5t90 -46.5t119 -36v512q-128 -8 -210.5 -80t-82.5 -180zM588 76q142 4 231.5 77.5t89.5 186.5q0 51 -17.5 91.5t-45 67t-72 49t-86 36t-100.5 28.5v-536z" />
<glyph unicode="%" horiz-adv-x="1449" d="M84 1030v14q0 124 85.5 211.5t209.5 87.5q125 0 209 -85.5t84 -208.5v-17q0 -122 -85.5 -210.5t-207.5 -88.5q-125 0 -210 86.5t-85 210.5zM162 31q0 21 9 37.5t36 46.5l1026 1231q4 2 15.5 -1t22.5 -14t11 -28q0 -31 -47 -84l-1024 -1231q-4 -2 -15.5 1.5t-22.5 14.5 t-11 27zM174 1028q0 -91 58 -153t147 -62t147 62t58 153v21q0 93 -59 155t-148 62q-87 0 -145 -62t-58 -155v-21zM778 287v14q0 124 85.5 211.5t209.5 87.5t208.5 -86.5t84.5 -208.5v-16q0 -123 -86 -211t-209 -88q-124 0 -208.5 86.5t-84.5 210.5zM868 285q0 -91 58 -153 t147 -62q88 0 145.5 61.5t57.5 153.5v20q0 93 -58 155t-147 62t-146 -62t-57 -155v-20z" />
<glyph unicode="&#x26;" horiz-adv-x="1312" d="M53 362q0 152 98 250.5t265 145.5q-127 141 -127 280q0 130 89.5 219t231.5 89q133 0 220.5 -84.5t87.5 -206.5q0 -126 -88 -205t-252 -123l391 -367q97 162 137 355q16 78 68 78q12 0 23 -6t16.5 -12t5.5 -9q-56 -273 -183 -465l289 -264q4 -4 0 -14t-18.5 -19.5 t-36.5 -9.5q-41 0 -76 33l-213 196q-199 -243 -479 -243q-202 0 -325.5 103t-123.5 279zM160 367q0 -137 95 -218t253 -81q244 0 410 217l-445 415q-70 -21 -123 -48t-98 -66.5t-68.5 -94.5t-23.5 -124zM387 1040q0 -131 129 -254q153 38 230 99t77 168q0 87 -59.5 148 t-155.5 61q-102 0 -161.5 -63.5t-59.5 -158.5z" />
<glyph unicode="'" horiz-adv-x="401" d="M139 1249q-4 45 11 63.5t51 18.5q35 0 50 -18t11 -64l-29 -368q-2 -22 -6.5 -31.5t-17.5 -9.5h-16q-13 0 -18.5 9.5t-8.5 31.5z" />
<glyph unicode="(" horiz-adv-x="718" d="M117 467q0 220 73.5 412t188.5 307q73 73 146 109t128 36q45 0 45 -35q0 -11 -5 -24.5t-9 -14.5q-138 -29 -244.5 -142.5t-163.5 -282t-57 -363.5q0 -201 57 -371t163.5 -282.5t244.5 -141.5q4 -1 9 -14.5t5 -24.5q0 -19 -11 -26.5t-34 -7.5q-55 0 -129 35t-145 106 q-120 120 -191 308t-71 417z" />
<glyph unicode=")" horiz-adv-x="718" d="M20 -365q0 10 6 23.5t9 15.5q138 29 244.5 141.5t163.5 282.5t57 371q0 195 -57 363.5t-163.5 282t-244.5 142.5q-3 2 -9 15.5t-6 23.5q0 35 46 35q56 0 129 -36t145 -109q117 -114 189.5 -305.5t72.5 -413.5q0 -229 -71 -417t-191 -308q-71 -71 -144 -106t-130 -35 q-46 0 -46 34z" />
<glyph unicode="*" horiz-adv-x="772" d="M74 1106q0 51 49 39l225 -66l6 232q0 23 8 31t27 8q18 0 26 -7.5t9 -31.5l6 -232l223 66q52 13 52 -39q0 -17 -29 -29l-217 -80l131 -190q20 -33 4 -45q-37 -33 -62 4l-143 182l-143 -182q-29 -37 -62 -4q-15 12 2 45l133 190l-219 80q-26 10 -26 29z" />
<glyph unicode="+" horiz-adv-x="1064" d="M88 541v10q0 39 49 39h350v352q0 47 41 47h9q43 0 43 -47v-352h350q47 0 47 -39v-10q0 -39 -47 -39h-350v-377q0 -26 -10.5 -36.5t-32.5 -10.5h-9q-41 0 -41 47v377h-350q-49 0 -49 39z" />
<glyph unicode="," horiz-adv-x="391" d="M33 -242l121 346q12 32 22.5 44t36.5 12h8q20 0 32.5 -14.5t12.5 -39.5v-6q0 -34 -22 -73l-160 -291q-7 -14 -33 -6.5t-18 28.5z" />
<glyph unicode="-" horiz-adv-x="718" d="M86 502v10q0 41 47 41h453q47 0 47 -41v-10q0 -39 -47 -39h-453q-47 0 -47 39z" />
<glyph unicode="." horiz-adv-x="391" d="M117 61v15q0 34 18.5 52.5t52.5 18.5h15q71 0 71 -71v-15q0 -71 -71 -71h-15q-34 0 -52.5 18.5t-18.5 52.5z" />
<glyph unicode="/" horiz-adv-x="649" d="M25.5 -340q0.5 24 11.5 59l491 1608q0 4 19 4t31.5 -4.5t22 -15.5t9 -35t-11.5 -59l-492 -1608q0 -4 -18 -4q-19 0 -31.5 4.5t-22 15.5t-9 35z" />
<glyph unicode="0" horiz-adv-x="1280" d="M121 596v131q0 284 140 451.5t378 167.5t379 -167t141 -450v-129q0 -284 -141.5 -452t-380.5 -168q-238 0 -377 166t-139 450zM223 602q0 -247 112 -390.5t304 -143.5q195 0 306.5 142t111.5 390v123q0 247 -113 392t-305 145q-193 0 -304.5 -143.5t-111.5 -389.5v-125z " />
<glyph unicode="1" horiz-adv-x="688" d="M43 1165q0 16 7.5 30t15.5 20.5t10 5.5q39 -52 117 -52q69 0 117.5 47t51.5 115h44q47 0 47 -49v-1241q0 -47 -45 -47h-15q-43 0 -43 47v1114q-55 -88 -166 -88q-75 0 -116 41q-25 25 -25 57z" />
<glyph unicode="2" horiz-adv-x="1052" d="M88 66v47q0 70 15 130.5t46.5 109t66 86.5t87.5 74t96.5 60.5t106.5 55.5q161 81 214 118q125 86 140 197q2 17 2 35q0 123 -86 198.5t-241 75.5q-157 0 -249 -80.5t-126 -207.5q-3 -2 -19 -2.5t-33.5 14.5t-17.5 41q0 34 25.5 89.5t72.5 102.5q138 138 353 138 q198 0 310 -102t112 -269q0 -47 -11 -88.5t-28 -74t-48.5 -64t-59.5 -54.5t-74.5 -50t-81.5 -44.5t-91 -44.5q-60 -30 -99.5 -51.5t-88.5 -53t-79.5 -62.5t-59 -70.5t-41.5 -87.5t-13 -103v-35h754q49 0 49 -43v-8q0 -43 -49 -43h-784q-70 0 -70 66z" />
<glyph unicode="3" horiz-adv-x="1069" d="M35 264q0 24 14 39.5t28 19.5t17 1q36 -113 145 -183.5t261 -70.5q170 0 270 79t100 213q0 140 -97 216.5t-275 76.5h-138q-51 0 -51 41v6q0 41 47 41h156q150 0 233.5 68.5t83.5 187.5q0 118 -88 189.5t-241 71.5q-126 0 -225 -61.5t-140 -154.5q-2 -4 -16.5 2 t-28.5 21.5t-14 34.5q0 42 59 104q62 61 157 100.5t208 39.5q203 0 318.5 -93t115.5 -254q0 -113 -66 -188.5t-182 -103.5q135 -34 212 -124t77 -221q0 -175 -128 -278.5t-349 -103.5q-125 0 -224 39t-157 98q-40 40 -61 80t-21 67z" />
<glyph unicode="4" horiz-adv-x="1161" d="M76 449v30q0 20 9.5 37.5t33.5 48.5l571 709q29 37 45.5 49t48.5 12q80 0 80 -67v-781h201q47 0 47 -43v-8q0 -41 -47 -41h-201v-354q0 -47 -43 -47h-14q-18 0 -30.5 14t-12.5 33v354h-614q-34 0 -54 15.5t-20 38.5zM168 487h600v750z" />
<glyph unicode="5" horiz-adv-x="1073" d="M70 233q0 19 14.5 34t29.5 20.5t17 1.5q47 -98 150.5 -157.5t242.5 -59.5q171 0 273 89t102 242q0 144 -97 231t-257 87q-209 0 -350 -103q-68 9 -68 74v572q0 26 17.5 43.5t43.5 17.5h697q47 0 47 -43v-8q0 -41 -47 -41h-660v-498q145 80 320 80q205 0 330.5 -111.5 t125.5 -295.5q0 -199 -129.5 -313.5t-351.5 -114.5q-122 0 -223 39t-164 102t-63 112z" />
<glyph unicode="6" horiz-adv-x="1175" d="M121 578v149q0 291 138 455t376 164q114 0 200 -32.5t138 -84.5q27 -27 41 -56t14 -49q0 -17 -13 -31.5t-26.5 -21t-15.5 -4.5q-47 86 -132 139.5t-206 53.5q-194 0 -304 -138t-110 -389v-121q65 88 174 144.5t244 56.5q201 0 323.5 -108t122.5 -285v-8 q0 -188 -129.5 -310t-328.5 -122q-237 0 -371.5 159.5t-134.5 438.5zM225 508q17 -211 121.5 -325.5t274.5 -114.5q161 0 262.5 93t101.5 242v9q0 148 -94 231.5t-262 83.5q-130 0 -241.5 -62.5t-162.5 -156.5z" />
<glyph unicode="7" horiz-adv-x="1030" d="M55 1270v8q0 47 47 47h824q59 0 59 -45v-10q0 -18 -29 -74l-600 -1163q-18 -39 -55 -39q-16 0 -30 9t-20.5 19t-4.5 13l622 1190h-766q-47 0 -47 45z" />
<glyph unicode="8" horiz-adv-x="1159" d="M88 350q0 120 80 212t227 126q-130 31 -197 111.5t-67 191.5q0 157 123.5 256t325.5 99q207 0 326.5 -96.5t119.5 -256.5q0 -110 -66.5 -191t-195.5 -114q148 -31 226.5 -122.5t78.5 -215.5q0 -163 -135 -266.5t-354 -103.5q-226 0 -359 101.5t-133 268.5zM190 354 q0 -128 104.5 -207t287.5 -79q186 0 285.5 76t99.5 210q0 130 -103.5 209.5t-283.5 79.5q-183 0 -286.5 -79t-103.5 -210zM233 989q0 -117 93.5 -187.5t255.5 -70.5t252 68.5t90 189.5t-91.5 196t-252.5 75q-166 0 -256.5 -73t-90.5 -198z" />
<glyph unicode="9" horiz-adv-x="1175" d="M90 887v12q0 189 130.5 318t328.5 129q232 0 369 -156.5t137 -441.5v-150q0 -303 -134 -460.5t-384 -157.5q-115 0 -203.5 32t-140.5 84q-60 63 -60 113q0 17 13 31.5t26.5 21t17.5 4.5q42 -89 131 -143.5t216 -54.5q205 0 311 130.5t106 393.5v100q-61 -85 -171 -142.5 t-246 -57.5q-202 0 -324.5 108t-122.5 287zM190 895q0 -149 94 -233t261 -84q132 0 243 63t164 158q-13 220 -119 340.5t-280 120.5q-158 0 -260.5 -101t-102.5 -252v-12z" />
<glyph unicode=":" horiz-adv-x="440" d="M141 61v15q0 34 19 52.5t53 18.5h14q72 0 72 -71v-15q0 -71 -72 -71h-14q-34 0 -53 18.5t-19 52.5zM141 784v17q0 33 19.5 52t52.5 19h14q35 0 53.5 -19t18.5 -52v-17q0 -69 -72 -69h-14q-34 0 -53 18t-19 51z" />
<glyph unicode=";" horiz-adv-x="440" d="M57 -242l121 346q12 32 23 44t37 12h8q20 0 32.5 -14.5t12.5 -39.5v-6q0 -33 -23 -73l-159 -291q-7 -14 -33.5 -6.5t-18.5 28.5zM141 784v17q0 33 19.5 52t52.5 19h14q35 0 53.5 -19t18.5 -52v-17q0 -69 -72 -69h-14q-34 0 -53 18t-19 51z" />
<glyph unicode="&#x3c;" horiz-adv-x="944" d="M96 545q0 30 7 42.5t24 22.5l670 371q3 1 10.5 -4.5t15 -17t8.5 -23.5q3 -39 -61 -76l-577 -315l577 -314q64 -37 61 -75q-1 -21 -13.5 -35t-20.5 -12l-670 370q-17 10 -24 23t-7 43z" />
<glyph unicode="=" horiz-adv-x="1064" d="M111 303v10q0 41 47 41h749q27 0 37 -10t10 -31v-10q0 -21 -10 -31t-37 -10h-749q-47 0 -47 41zM111 756v10q0 41 47 41h749q27 0 37 -10t10 -31v-10q0 -41 -47 -41h-749q-47 0 -47 41z" />
<glyph unicode="&#x3e;" horiz-adv-x="942" d="M111 156q-3 38 61 75l578 314l-578 315q-68 38 -61 76q1 12 8 23.5t14.5 17t9.5 4.5l670 -371q19 -10 26 -22.5t7 -42.5q0 -31 -7 -43.5t-26 -22.5l-670 -370q-3 -1 -10 4t-14 17t-8 26z" />
<glyph unicode="?" horiz-adv-x="937" d="M74 1028q0 35 24.5 92.5t71.5 106.5q122 119 319 119q175 0 281.5 -91.5t106.5 -234.5q0 -46 -12 -86.5t-29 -68.5t-47.5 -57t-53.5 -46.5t-61 -42.5q-45 -31 -66 -46t-56 -48.5t-51.5 -63.5t-30 -77t-13.5 -103q0 -29 -43 -29t-43 35q2 67 17 123.5t34.5 93t54 72 t59.5 54.5t66 46q42 28 66 46.5t52 48t41 64t13 77.5q0 111 -77.5 177t-209.5 66q-80 0 -143 -24.5t-102.5 -66.5t-62.5 -91t-32 -106q-3 -4 -20.5 -0.5t-35 19.5t-17.5 42zM346 53v31q0 32 13 45.5t47 13.5h28q34 0 48 -13.5t14 -45.5v-31q0 -32 -14 -45.5t-48 -13.5h-28 q-32 0 -46 13.5t-14 45.5z" />
<glyph unicode="@" d="M96 662q0 197 84.5 353t236.5 243.5t345 87.5q300 0 478.5 -167.5t178.5 -449.5q0 -184 -70.5 -285.5t-203.5 -101.5q-81 0 -136 43t-67 115q-30 -73 -88.5 -114.5t-140.5 -41.5q-118 0 -196.5 86t-78.5 219q0 134 78.5 219.5t200.5 85.5q71 0 127.5 -33.5t87.5 -86.5v43 q0 35 17.5 53t43.5 18q9 0 17.5 -2t13 -5t4.5 -5v-328q0 -190 135 -190q91 0 134.5 85t43.5 226q0 248 -157.5 396.5t-421.5 148.5q-260 0 -425 -172t-165 -440q0 -266 170.5 -437.5t439.5 -171.5q100 0 194.5 28t158.5 71q27 20 53 20q18 0 32.5 -22t8.5 -29 q-74 -59 -194 -100t-253 -41q-309 0 -497.5 188t-188.5 494zM539 649q0 -95 55.5 -157t140.5 -62q83 0 140 55.5t57 137.5v106q-26 65 -76 103t-119 38q-88 0 -143 -61.5t-55 -159.5z" />
<glyph unicode="A" horiz-adv-x="1222" d="M31 25l504 1271q16 39 75 39q62 0 78 -39l504 -1271q2 -11 -22 -23q-15 -8 -33 -8q-10 0 -21 3q-31 7 -45 46l-166 416h-592l-166 -416q-15 -41 -45 -47q-10 -2 -19 -2q-18 0 -33 9q-22 13 -19 22zM346 549h526l-262 670z" />
<glyph unicode="B" horiz-adv-x="1218" d="M162 61v1203q0 26 16.5 43.5t42.5 17.5h420q211 0 320.5 -87t109.5 -251q0 -114 -64.5 -189.5t-189.5 -107.5q157 -27 236.5 -112t79.5 -216q0 -184 -119 -273t-359 -89h-434q-26 0 -42.5 17.5t-16.5 43.5zM264 92h389q194 0 283.5 64.5t89.5 210.5q0 266 -369 266h-393 v-541zM264 723h383q149 0 233.5 70t84.5 188q0 252 -324 252h-377v-510z" />
<glyph unicode="C" horiz-adv-x="1345" d="M100 659q0 148 48 275.5t133.5 218t208 142t266.5 51.5q143 0 250 -44.5t174 -111.5q32 -32 51.5 -67.5t19.5 -57.5q0 -23 -14.5 -38.5t-29 -20.5t-17.5 -2q-54 113 -171 183t-265 70q-243 0 -396 -167t-153 -428q0 -259 154 -426.5t399 -167.5q173 0 289.5 82t156.5 202 q3 4 19 -0.5t32.5 -20t16.5 -38.5q0 -26 -24 -71.5t-66 -86.5q-65 -66 -176 -110.5t-252 -44.5q-194 0 -343 86.5t-230 241t-81 351.5z" />
<glyph unicode="D" horiz-adv-x="1355" d="M162 61v1203q0 26 16.5 43.5t42.5 17.5h314q342 0 532 -174.5t190 -484.5q0 -311 -194 -488.5t-539 -177.5h-303q-26 0 -42.5 17.5t-16.5 43.5zM264 90h266q295 0 458 152t163 422q0 267 -163 419t-453 152h-271v-1145z" />
<glyph unicode="E" horiz-adv-x="1155" d="M162 61v1203q0 26 16.5 43.5t42.5 17.5h795q49 0 49 -41v-10q0 -41 -49 -41h-752v-508h582q49 0 49 -41v-6q0 -43 -49 -43h-582v-541h770q49 0 49 -41v-10q0 -43 -49 -43h-813q-26 0 -42.5 17.5t-16.5 43.5z" />
<glyph unicode="F" horiz-adv-x="1073" d="M162 41v1223q0 26 16.5 43.5t42.5 17.5h756q49 0 49 -41v-10q0 -41 -49 -41h-713v-551h531q47 0 47 -43v-6q0 -41 -47 -41h-531v-551q0 -47 -45 -47h-14q-43 0 -43 47z" />
<glyph unicode="G" horiz-adv-x="1421" d="M100 659q0 148 48 275.5t132.5 218t206.5 142t265 51.5q139 0 245.5 -43.5t171.5 -108.5q34 -34 53 -68t19 -53q0 -26 -14.5 -41t-29 -18t-17.5 0q-53 106 -168.5 174.5t-263.5 68.5q-240 0 -391.5 -167t-151.5 -428q0 -171 62.5 -304.5t184 -211.5t283.5 -78 q194 0 326 113.5t147 287.5v90h-409q-58 0 -58 41v8q0 43 58 43h424q33 0 54 -22t21 -56v-569q0 -1 -4 -3.5t-12 -4.5t-16 -2q-29 0 -43.5 21t-14.5 79v164q-54 -129 -183 -203.5t-302 -74.5q-188 0 -330.5 88t-217.5 242t-75 349z" />
<glyph unicode="H" horiz-adv-x="1376" d="M162 41v1243q0 47 43 47h14q45 0 45 -47v-573h848v573q0 47 43 47h14q45 0 45 -47v-1243q0 -47 -45 -47h-14q-43 0 -43 47v575h-848v-575q0 -47 -45 -47h-14q-43 0 -43 47z" />
<glyph unicode="I" horiz-adv-x="464" d="M182 41v1243q0 47 43 47h15q45 0 45 -47v-1243q0 -47 -45 -47h-15q-43 0 -43 47z" />
<glyph unicode="J" horiz-adv-x="892" d="M51 180q0 19 14.5 35t29 23.5t16.5 3.5q32 -78 103 -126t165 -48q143 0 207.5 82.5t64.5 265.5v868q0 47 45 47h15q43 0 43 -47v-868q0 -222 -91.5 -329t-283.5 -107q-74 0 -139.5 22.5t-104.5 55t-61.5 65.5t-22.5 57z" />
<glyph unicode="K" horiz-adv-x="1089" d="M162 41v1243q0 47 43 47h14q45 0 45 -47v-594l629 610q19 18 38 26q14 6 25 5q4 -1 8 -1q14 -2 23.5 -10t14 -19.5t2.5 -22.5l-627 -598l645 -633v-6q0 -19 -14 -33q-17 -16 -45 -16h-1q-28 0 -53 26l-645 648v-625q0 -47 -45 -47h-14q-43 0 -43 47z" />
<glyph unicode="L" horiz-adv-x="1024" d="M162 61v1223q0 47 43 47h14q45 0 45 -47v-1190h649q48 0 48 -43v-8q0 -43 -48 -43h-692q-26 0 -42.5 17.5t-16.5 43.5z" />
<glyph unicode="M" d="M162 41v1225q0 29 19.5 47t51.5 18h25q37 0 53 -15.5t37 -60.5l406 -802l407 802q19 44 35.5 60t52.5 16h21q34 0 53.5 -18t19.5 -47v-1225q0 -47 -43 -47h-12q-43 0 -43 47v1173l-405 -792q-26 -64 -88 -64q-36 0 -55.5 16t-32.5 48l-406 792v-1173q0 -24 -9 -35.5 t-32 -11.5h-16q-39 0 -39 47z" />
<glyph unicode="N" horiz-adv-x="1378" d="M162 41v1221q0 31 14 50t41 19h27q26 0 42.5 -12.5t37.5 -42.5l798 -1118v1126q0 25 9 36t32 11h15q41 0 41 -47v-1223q0 -30 -15 -48.5t-39 -18.5h-8q-28 0 -41 9t-29 34l-829 1161v-1157q0 -47 -43 -47h-14q-39 0 -39 47z" />
<glyph unicode="O" d="M100 659q0 147 48 274.5t133.5 218t208 142.5t266.5 52q192 0 340 -87.5t228.5 -242.5t80.5 -352t-81.5 -352.5t-230.5 -243.5t-341 -88q-290 0 -471 190t-181 489zM205 662q0 -169 69 -304.5t195 -212.5t285 -77q242 0 394 167.5t152 426.5q0 257 -153 426t-395 169 q-160 0 -285.5 -77t-193.5 -212t-68 -306z" />
<glyph unicode="P" horiz-adv-x="1118" d="M162 41v1223q0 26 16.5 43.5t42.5 17.5h340q234 0 361 -104t127 -293q0 -195 -127 -299.5t-371 -104.5h-287v-483q0 -21 -12 -34t-33 -13h-14q-43 0 -43 47zM264 614h295q189 0 286 80t97 230q0 151 -96 230t-283 79h-299v-619z" />
<glyph unicode="Q" d="M100 659q0 147 48 274.5t133.5 218t208 142.5t266.5 52q192 0 340 -87.5t228.5 -242.5t80.5 -352q0 -140 -44.5 -262t-127.5 -214l158 -159q3 -3 -3.5 -14t-22 -22t-34.5 -11q-25 0 -53 28l-113 115q-175 -145 -413 -145q-290 0 -471 190t-181 489zM205 662 q0 -169 69 -304.5t195 -212.5t285 -77q206 0 346 118l-299 303q-18 18 -18 38.5t14 31.5q16 18 34.5 17t34.5 -19l299 -305q135 163 135 410q0 257 -153 426t-395 169q-160 0 -285.5 -77t-193.5 -212t-68 -306z" />
<glyph unicode="R" horiz-adv-x="1146" d="M162 41v1223q0 26 16.5 43.5t42.5 17.5h359q233 0 355 -96.5t122 -280.5q0 -182 -119 -281.5t-334 -99.5h-45l492 -524q3 -4 -3.5 -16t-22.5 -22.5t-36 -10.5q-31 0 -63 37l-498 536h-166v-526q0 -47 -45 -47h-12q-43 0 -43 47zM262 655h328q179 0 269.5 76.5t90.5 212.5 q0 142 -92 215.5t-274 73.5h-322v-578z" />
<glyph unicode="S" horiz-adv-x="1095" d="M82 244q0 20 14.5 36.5t29.5 22.5t17 2q45 -100 149.5 -168.5t264.5 -68.5q163 0 256.5 73t93.5 199q0 46 -13 83.5t-42 65.5t-59 48t-80 37.5t-88 28t-99 24.5q-52 13 -87.5 22.5t-82.5 26.5t-78 35t-64 45.5t-52.5 59.5t-32 76t-12.5 97q0 156 123 256.5t309 100.5 q125 0 219.5 -36t151.5 -91q57 -57 57 -107q0 -20 -14.5 -36.5t-29 -23t-18.5 -3.5q-40 90 -134.5 149t-231.5 59q-146 0 -239 -73t-93 -193q0 -30 6 -56t15 -46t27.5 -38t34 -31t44 -26t49 -21.5t57.5 -19t61 -16.5t67 -16q61 -14 106.5 -27.5t99 -35t90.5 -49t69.5 -64.5 t48.5 -88t16 -113q0 -164 -123 -264t-326 -100q-139 0 -244 40t-168 103q-65 62 -65 121z" />
<glyph unicode="T" horiz-adv-x="1165" d="M47 1274v8q0 43 47 43h977q49 0 49 -43v-8q0 -41 -49 -41h-436v-1192q0 -47 -45 -47h-15q-43 0 -43 47v1192h-438q-47 0 -47 41z" />
<glyph unicode="U" horiz-adv-x="1292" d="M141 516v768q0 47 43 47h15q45 0 45 -47v-760q0 -452 403 -452q204 0 304 112.5t100 339.5v760q0 25 10 36t33 11h18q21 0 30 -11t9 -36v-764q0 -264 -129.5 -402t-378.5 -138q-247 0 -374.5 136t-127.5 400z" />
<glyph unicode="V" horiz-adv-x="1214" d="M33 1300q-2 10 21 23q15 8 33 8q10 0 21 -2q31 -7 46 -47l454 -1173l457 1173q15 40 45 47q10 2 20 2q18 0 33 -8q20 -11 20 -20q0 -2 -1 -3l-506 -1269q-8 -22 -22.5 -31.5t-45.5 -9.5q-32 0 -46.5 9.5t-22.5 31.5z" />
<glyph unicode="W" horiz-adv-x="1806" d="M104 1309q-1 9 24 19q11 4 23 4q15 0 33 -7q30 -13 41 -61l277 -1172l321 969q10 30 28 44.5t56 14.5q37 0 53.5 -13.5t26.5 -45.5l320 -967l282 1170q11 48 39 61q16 7 30 7q12 0 22 -4q24 -10 22 -19l-316 -1276q-6 -25 -23 -34t-58 -9q-37 0 -52.5 10t-23.5 35 l-322 983l-334 -983q-6 -25 -20.5 -35t-50.5 -10q-41 0 -58.5 9t-21.5 34z" />
<glyph unicode="X" horiz-adv-x="1163" d="M47 43l475 631l-456 610q-3 4 2 15.5t19 21.5t32 10q38 0 57 -31l410 -555l418 558q23 32 51 32q16 0 29.5 -10t19 -21.5t2.5 -15.5l-465 -616l471 -625q1 -2 1 -4q0 -4 -3 -12q-5 -12 -19 -22.5t-32 -10.5q-36 0 -58 31l-421 575l-430 -577q-24 -33 -52 -33 q-24 0 -40 20q-12 15 -13 23q0 4 2 6z" />
<glyph unicode="Y" horiz-adv-x="1101" d="M18 1288q-3 14 16 30q16 13 41 13h7q29 -2 49 -35l420 -637l420 635q14 22 31 31q13 7 24 7q4 0 7 -1q14 -3 26 -10.5t18.5 -17t5.5 -15.5l-481 -721v-526q0 -47 -45 -47h-14q-43 0 -43 47v526z" />
<glyph unicode="Z" horiz-adv-x="1198" d="M96 57v15q0 24 11.5 46t48.5 68l813 1047h-817q-48 0 -48 41v8q0 43 48 43h847q82 0 82 -55v-15q0 -35 -59 -110l-817 -1051h856q49 0 49 -43v-8q0 -43 -49 -43h-883q-42 0 -62 16t-20 41z" />
<glyph unicode="[" horiz-adv-x="659" d="M172 -334v1598q0 27 17 44t44 17h324q26 0 36.5 -9.5t10.5 -29.5v-10q0 -21 -10 -30t-37 -9h-291v-1542h291q27 0 37 -10t10 -31v-10q0 -20 -10.5 -29.5t-36.5 -9.5h-324q-27 0 -44 17t-17 44z" />
<glyph unicode="\" horiz-adv-x="649" d="M38.5 1276q-0.5 24 8.5 35t21.5 15.5t31.5 4.5t19 -4l491 -1608q11 -35 11.5 -59t-8.5 -35t-21.5 -15.5t-30.5 -4.5q-16 0 -20 4l-492 1608q-10 35 -10.5 59z" />
<glyph unicode="]" horiz-adv-x="659" d="M55 -346q0 41 47 41h293v1542h-293q-26 0 -36.5 9t-10.5 30v10q0 39 47 39h324q27 0 44 -17t17 -44v-1598q0 -27 -17 -44t-44 -17h-324q-47 0 -47 39v10z" />
<glyph unicode="^" horiz-adv-x="1024" d="M266 956q0 23 23 56t82 102q35 39 69 71.5t52 47t20 14.5q3 0 20.5 -14t50.5 -46t66 -69q58 -68 82.5 -103.5t24.5 -58.5q0 -15 -8.5 -24.5t-22.5 -9.5q-20 0 -47.5 26.5t-85.5 102.5l-82 108l-82 -112q-52 -69 -80.5 -97t-48.5 -28q-14 0 -23.5 9.5t-9.5 24.5z" />
<glyph unicode="_" horiz-adv-x="925" d="M27 -43q0 43 47 43h778q47 0 47 -43v-8q0 -43 -47 -43h-778q-47 0 -47 43v8z" />
<glyph unicode="`" horiz-adv-x="1024" d="M285 1354q0 18 12.5 29.5t30.5 11.5q34 0 92 -45q65 -49 123 -103.5t85.5 -88t24.5 -38.5q-5 -6 -44.5 7t-109.5 48.5t-137 84.5q-45 33 -61 53t-16 41z" />
<glyph unicode="a" horiz-adv-x="1026" d="M76 246q0 163 166.5 237.5t533.5 79.5h21v49q0 136 -78.5 211.5t-218.5 75.5q-120 0 -204 -50.5t-126 -133.5q-5 0 -15.5 3t-25 17t-14.5 37q0 16 15 43.5t40 50.5q123 123 336 123q181 0 284 -100t103 -275v-610q0 -1 -5 -3.5t-13.5 -4.5t-16.5 -2q-61 0 -61 98v101 q-48 -96 -151 -154.5t-234 -58.5q-155 0 -245.5 72t-90.5 194zM176 246q0 -86 65.5 -135.5t178.5 -49.5q158 0 267.5 95t109.5 233v96h-27q-318 -6 -456 -61t-138 -178z" />
<glyph unicode="b" horiz-adv-x="1159" d="M141 41v1337q0 3 10 7t23 4q66 0 66 -97v-522q130 219 383 219q196 0 328 -140.5t132 -361.5q0 -224 -131.5 -365.5t-335.5 -141.5q-139 0 -238.5 63t-139.5 154v-156q0 -47 -41 -47h-11q-45 0 -45 47zM238 416q0 -146 107.5 -248t262.5 -102q169 0 273 116.5t104 304.5 q0 180 -106.5 298t-266.5 118q-135 0 -231 -70.5t-143 -193.5v-223z" />
<glyph unicode="c" horiz-adv-x="1048" d="M76 487q0 217 139 359.5t354 142.5q211 0 336 -125q35 -36 54.5 -74t19.5 -63q0 -20 -15.5 -33t-30.5 -16t-18 0q-38 99 -122 162t-224 63q-172 0 -283.5 -119t-111.5 -297q0 -181 110.5 -300t284.5 -119q140 0 224 62t122 161q2 4 17.5 0.5t31 -16.5t15.5 -33 q0 -24 -19.5 -63t-56.5 -77q-122 -122 -334 -122q-216 0 -354.5 144t-138.5 363z" />
<glyph unicode="d" horiz-adv-x="1159" d="M76 485q0 221 132.5 362.5t334.5 141.5q134 0 233.5 -60t143.5 -149v598q0 3 9.5 7t22.5 4q66 0 66 -97v-1251q0 -47 -45 -47h-10q-43 0 -43 47v156q-57 -102 -153 -159.5t-230 -57.5q-199 0 -330 140.5t-131 364.5zM174 487q0 -184 105 -302.5t268 -118.5 q155 0 264 101.5t109 246.5v227q-46 123 -140 192.5t-231 69.5q-164 0 -269.5 -116.5t-105.5 -299.5z" />
<glyph unicode="e" horiz-adv-x="1075" d="M76 481q0 143 62 259t173 182.5t248 66.5q200 0 322.5 -130.5t122.5 -342.5v-10q0 -21 -9.5 -28t-33.5 -7h-789q3 -180 113.5 -291.5t285.5 -111.5q248 0 347 194q4 3 17.5 -1.5t26.5 -19.5t13 -36q0 -17 -16 -46t-46 -59q-50 -50 -139 -85t-203 -35q-221 0 -358 140.5 t-137 360.5zM176 555h725q-6 156 -99.5 252t-242.5 96t-254.5 -97t-128.5 -251z" />
<glyph unicode="f" horiz-adv-x="636" d="M20 920v10q0 20 10.5 29.5t37.5 9.5h161v104q0 165 70 250.5t201 85.5q109 0 162 -53q22 -22 22 -51q0 -15 -6.5 -27t-13.5 -17t-9 -4q-24 28 -67 47t-88 19q-89 0 -132.5 -62t-43.5 -194v-98h215q49 0 49 -39v-10q0 -39 -49 -39h-213v-840q0 -47 -43 -47h-11 q-43 0 -43 47v840h-161q-27 0 -37.5 9.5t-10.5 29.5z" />
<glyph unicode="g" horiz-adv-x="925" d="M0 -168q0 108 94.5 183.5t237.5 101.5q-74 34 -74 102q0 60 55 98q-108 36 -174.5 122.5t-66.5 195.5q0 149 110 251.5t275 102.5q139 0 241 -76l90 123q15 24 33 35q16 10 30 10q2 0 4 -1q16 -1 29 -7.5t21 -17t8 -19.5l-159 -174q88 -95 88 -219q0 -147 -111.5 -247.5 t-277.5 -100.5q-20 0 -66 4q-35 -21 -35 -63q0 -16 5.5 -28t20 -22t28 -17t42.5 -15t49 -13t62 -14.5t68 -15.5q309 -72 309 -258q0 -135 -128 -209t-357 -74q-451 0 -451 262zM96 -168q0 -88 92 -135t265 -47q182 0 283.5 51t101.5 147q0 37 -19.5 66.5t-55 50.5t-75 35.5 t-90.5 26.5q-22 6 -68.5 16t-70.5 16q-97 -7 -178.5 -35.5t-133 -79t-51.5 -112.5zM172 639q0 -117 79.5 -191.5t203.5 -74.5q125 0 204.5 74.5t79.5 191.5q0 118 -78.5 193t-205.5 75q-125 0 -204 -75t-79 -193z" />
<glyph unicode="h" horiz-adv-x="1107" d="M141 41v1337q0 3 10 7t23 4q66 0 66 -97v-514q44 96 135 153.5t209 57.5q191 0 291 -116t100 -316v-516q0 -47 -43 -47h-12q-43 0 -43 47v510q0 163 -78 254.5t-221 91.5q-147 0 -242.5 -94t-95.5 -246v-516q0 -47 -43 -47h-13q-43 0 -43 47z" />
<glyph unicode="i" horiz-adv-x="401" d="M133 1298q0 70 70 70q67 0 67 -70q0 -67 -71 -67q-66 0 -66 67zM152 41v887q0 47 43 47h12q43 0 43 -47v-887q0 -47 -43 -47h-12q-43 0 -43 47z" />
<glyph unicode="j" horiz-adv-x="401" d="M-170 -301q0 14 7 28t14 21t8 6q57 -57 141 -57q78 0 115 49.5t37 151.5v1030q0 47 43 47h12q43 0 43 -47v-1037q0 -136 -64 -208t-186 -72q-103 0 -150 47q-20 20 -20 41zM133 1298q0 70 70 70q67 0 67 -70q0 -67 -71 -67q-66 0 -66 67z" />
<glyph unicode="k" horiz-adv-x="878" d="M141 41v1337q0 3 10 7t23 4q66 0 66 -97v-770l442 414q23 23 44 32.5t34 6t23 -11.5t13.5 -18t2.5 -17l-451 -412l508 -471q1 -7 -3 -17.5t-13.5 -19.5t-22.5 -13q-4 -1 -9 -1q-10 0 -24 6q-20 9 -45 33l-499 467v-459q0 -47 -43 -47h-13q-43 0 -43 47z" />
<glyph unicode="l" horiz-adv-x="401" d="M152 41v1337q0 3 9.5 7t22.5 4q66 0 66 -97v-1251q0 -47 -43 -47h-12q-43 0 -43 47z" />
<glyph unicode="m" horiz-adv-x="1753" d="M141 41v924q0 1 4.5 3.5t12.5 4.5t16 2q64 0 64 -98v-84q48 90 134 143t189 53q127 0 213.5 -60.5t120.5 -166.5q45 104 137.5 165.5t214.5 61.5q180 0 276.5 -108.5t96.5 -309.5v-530q0 -47 -43 -47h-12q-43 0 -43 47v524q0 160 -72.5 246t-208.5 86q-101 0 -187.5 -60 t-123.5 -165v-631q0 -47 -43 -47h-13q-43 0 -43 47v547q0 147 -71.5 228t-208.5 81q-99 0 -184.5 -60t-126.5 -163v-633q0 -47 -43 -47h-13q-43 0 -43 47z" />
<glyph unicode="n" horiz-adv-x="1107" d="M141 41v924q0 1 4.5 3.5t12.5 4.5t16 2q64 0 64 -98v-89q52 92 141.5 146.5t206.5 54.5q189 0 289 -116t100 -316v-516q0 -47 -43 -47h-12q-43 0 -43 47v510q0 162 -77 254t-218 92q-119 0 -208 -60.5t-134 -164.5v-631q0 -47 -43 -47h-13q-43 0 -43 47z" />
<glyph unicode="o" horiz-adv-x="1142" d="M76 485q0 218 139 361t356 143q215 0 354.5 -144.5t139.5 -363.5q0 -216 -139.5 -358.5t-354.5 -142.5q-217 0 -356 143.5t-139 361.5zM174 485q0 -179 113 -299t284 -120q170 0 283 118.5t113 296.5q0 180 -112.5 301t-283.5 121t-284 -119.5t-113 -298.5z" />
<glyph unicode="p" horiz-adv-x="1159" d="M141 -348v1313q0 1 4.5 3.5t12.5 4.5t16 2q64 0 64 -98v-107q130 219 385 219q196 0 328 -140.5t132 -361.5q0 -224 -131.5 -365.5t-335.5 -141.5q-138 0 -237 63t-139 154v-545q0 -47 -43 -47h-11q-45 0 -45 47zM238 416q0 -146 107.5 -248t262.5 -102q169 0 273 116.5 t104 304.5q0 180 -106.5 298t-266.5 118q-135 0 -231 -70.5t-143 -193.5v-223z" />
<glyph unicode="q" horiz-adv-x="1159" d="M76 485q0 221 132.5 362.5t334.5 141.5q135 0 234.5 -60.5t142.5 -150.5v99q0 98 63 98q8 0 16.5 -2t13.5 -4.5t5 -3.5v-1313q0 -47 -45 -47h-10q-43 0 -43 47v543q-57 -100 -153.5 -157.5t-229.5 -57.5q-199 0 -330 140.5t-131 364.5zM174 487q0 -184 105 -302.5 t268 -118.5q155 0 264 102.5t109 247.5v221q-46 124 -140 195t-231 71q-164 0 -269.5 -116.5t-105.5 -299.5z" />
<glyph unicode="r" horiz-adv-x="671" d="M141 41v924q0 1 4.5 3.5t12.5 4.5t16 2q64 0 64 -98v-121q59 233 268 233q58 0 96.5 -23t38.5 -59q0 -17 -6.5 -29.5t-13 -17.5t-9.5 -4q-42 39 -116 39q-127 0 -191.5 -115.5t-64.5 -318.5v-420q0 -47 -43 -47h-11q-45 0 -45 47z" />
<glyph unicode="s" horiz-adv-x="909" d="M63 172q0 18 13 31.5t25.5 18.5t15.5 3q48 -74 134 -119t208 -45q129 0 203.5 52.5t74.5 136.5q0 32 -8.5 57t-31 44t-42 31.5t-62.5 25t-70.5 19t-86.5 19.5q-39 8 -61 13t-59 15t-59 20t-51.5 24.5t-47 32.5t-34.5 41t-24.5 52.5t-7.5 64.5q0 125 100.5 202.5 t262.5 77.5q207 0 313 -106q37 -37 37 -72q0 -18 -12.5 -31.5t-25.5 -18.5t-15 -1q-38 65 -117.5 106t-179.5 41q-124 0 -196.5 -51.5t-72.5 -132.5q0 -30 8.5 -54t29 -42t41 -30.5t59.5 -25t69 -20t84 -19.5q37 -8 56.5 -12t55 -13.5t55.5 -17t49.5 -20t46.5 -25.5 t37.5 -32t31 -40.5t18 -49t7.5 -58.5q0 -127 -103 -205.5t-276 -78.5q-119 0 -207 31.5t-137 80.5q-48 48 -48 80z" />
<glyph unicode="t" horiz-adv-x="679" d="M35 920v10q0 20 10.5 29.5t36.5 9.5h119v188q0 49 43 49h12q41 0 41 -49v-188h281q49 0 49 -39v-10q0 -39 -49 -39h-281v-590q0 -120 38.5 -174t123.5 -54q100 0 157 84q2 2 10 -2t15.5 -16.5t7.5 -30.5q0 -31 -33 -61q-54 -57 -161 -57q-129 0 -191.5 73.5t-62.5 227.5 v600h-119q-26 0 -36.5 9.5t-10.5 29.5z" />
<glyph unicode="u" horiz-adv-x="1107" d="M133 412v516q0 47 43 47h12q43 0 43 -47v-510q0 -162 77 -254t218 -92q119 0 208 61t134 166v629q0 47 43 47h13q43 0 43 -47v-924q0 -1 -5 -3.5t-13.5 -4.5t-16.5 -2q-62 0 -62 98v88q-52 -91 -142 -145.5t-208 -54.5q-188 0 -287.5 116t-99.5 316z" />
<glyph unicode="v" horiz-adv-x="944" d="M31 946q0 12 21 24q10 6 23 6t29 -6q30 -12 48 -61l321 -821l324 821q18 49 47 61q14 6 26.5 6t22.5 -6q20 -12 20 -24l-370 -915q-8 -22 -22.5 -31.5t-49.5 -9.5q-36 0 -48 8t-20 31z" />
<glyph unicode="w" horiz-adv-x="1359" d="M59 948q0 4 9 12q10 8 24 12q12 4 24 3h6q16 -1 31 -19t23 -53l199 -813l233 686q6 24 22.5 33.5t53.5 9.5q36 0 52 -9.5t24 -33.5l231 -686l197 813q8 34 23 52t30.5 19.5t29.5 -3.5t23 -12.5t9 -10.5l-240 -917q-5 -22 -20 -31.5t-54 -9.5q-34 0 -46 9t-21 32l-238 692 l-244 -692q-7 -23 -19.5 -32t-47.5 -9q-39 0 -53 9.5t-19 31.5z" />
<glyph unicode="x" horiz-adv-x="931" d="M49 37l357 448l-351 447q-2 4 2 14t16.5 19.5t30.5 9.5q24 0 39 -11t35 -38l291 -379l287 377q38 51 73 51q18 0 31 -10q14 -11 18 -21q3 -7 4 -10q0 -1 -1 -2l-355 -447l351 -448q2 -4 -2 -14t-17 -19.5t-31 -9.5q-24 0 -38.5 11t-34.5 38l-293 383l-287 -381 q-39 -51 -74 -51q-18 0 -31 10t-17 20q-3 7 -4 11q0 1 1 2z" />
<glyph unicode="y" horiz-adv-x="937" d="M29 946q0 14 21 26q10 5 22 5q13 0 29 -7q30 -12 49 -59l339 -800l302 794q18 51 48 65q16 7 30 7q11 0 21 -5q21 -12 21 -24l-499 -1278q-22 -65 -70 -65q-12 0 -25 5.5t-20 11.5t-6 9l172 422q-27 0 -40 8t-22 31z" />
<glyph unicode="z" horiz-adv-x="942" d="M76 43v14q0 12 7.5 24.5t31.5 43.5l612 760h-598q-27 0 -38 9t-11 28v10q0 19 11 28t38 9h678q49 0 49 -43v-8q0 -19 -39 -66l-618 -768h622q51 0 51 -37v-10q0 -37 -51 -37h-696q-49 0 -49 43z" />
<glyph unicode="{" horiz-adv-x="806" d="M53 467q0 39 13 54.5t55 39.5l153 90q7 182 62 323.5t137 225.5q66 66 134 100.5t118 34.5q39 0 39 -43q0 -9 -5 -23.5t-9 -15.5q-193 -54 -281 -213t-96 -440l-234 -133l234 -133q8 -283 96 -442.5t281 -213.5q4 -1 9 -14t5 -24q0 -41 -39 -41q-50 0 -118 34.5 t-134 100.5q-82 82 -137 223.5t-62 325.5l-153 90q-42 24 -55 39.5t-13 54.5z" />
<glyph unicode="|" horiz-adv-x="471" d="M186 -348v1632q0 47 43 47h13q43 0 43 -47v-1632q0 -47 -43 -47h-13q-43 0 -43 47z" />
<glyph unicode="}" horiz-adv-x="806" d="M41 -360q0 11 5 24t9 14q193 54 281 213.5t96 442.5l234 133l-234 133q-8 281 -96 440t-281 213q-4 1 -9 15.5t-5 23.5q0 43 39 43q49 0 118 -35t134 -100q82 -84 136.5 -225.5t61.5 -323.5l154 -90q42 -24 55 -39.5t13 -54.5t-13.5 -55.5t-54.5 -38.5l-154 -90 q-7 -184 -61.5 -325.5t-136.5 -223.5q-65 -65 -134 -100t-118 -35q-39 0 -39 41z" />
<glyph unicode="~" horiz-adv-x="1021" d="M233 367q6 95 44.5 148.5t107.5 53.5q47 0 83.5 -22t74.5 -62q32 -34 53 -47.5t43 -13.5q28 0 44.5 21.5t25.5 64.5q6 25 15.5 36t27.5 11q20 0 34 -14q-5 -94 -43 -147.5t-108 -53.5q-46 0 -82 22t-76 62q-33 33 -52.5 46t-41.5 13q-29 0 -44.5 -21t-25.5 -65 q-6 -24 -15.5 -34.5t-27.5 -10.5q-17 0 -37 13z" />
<glyph unicode="&#xa1;" horiz-adv-x="475" d="M160 1249v15q0 71 71 71h15q69 0 69 -71v-15q0 -71 -69 -71h-15q-71 0 -71 71zM176 53l31 877q2 23 6.5 32t17.5 9h13q13 0 17.5 -9t6.5 -32l31 -877q5 -59 -43 -59h-37q-46 0 -43 59z" />
<glyph unicode="&#xa2;" horiz-adv-x="1085" d="M92 575q0 208 126 349.5t321 160.5v213q0 48 41 48h8q21 0 31 -10.5t10 -37.5v-211q199 -11 309 -126q29 -29 46 -60.5t17 -48.5q0 -19 -12.5 -35t-26 -21.5t-16.5 -2.5q-92 193 -321 206v-845q124 4 208 64t123 157q4 4 18 0t28 -18.5t14 -34.5q0 -23 -20 -59.5 t-56 -72.5q-51 -51 -131 -85.5t-180 -38.5v-220q0 -27 -10 -37t-31 -10h-8q-41 0 -41 47v222q-203 19 -325 154.5t-122 352.5zM193 575q0 -176 96.5 -287.5t253.5 -129.5v841q-157 -14 -253.5 -127.5t-96.5 -296.5z" />
<glyph unicode="&#xa3;" horiz-adv-x="1146" d="M47 43v8q0 43 47 43h127l84 455h-184q-47 0 -47 41v10q0 41 47 41h201l57 307q36 200 129 301t256 101q155 0 244 -93q35 -37 54 -83t19 -87q0 -26 -17 -41.5t-33 -15.5h-21q-14 108 -78 170t-168 62q-127 0 -191 -75.5t-96 -244.5l-55 -301h375q47 0 47 -41v-10 q0 -41 -47 -41h-391l-84 -455h690q47 0 47 -45v-6q0 -43 -47 -43h-918q-47 0 -47 43z" />
<glyph unicode="&#xa4;" horiz-adv-x="1280" d="M74 502v8q0 21 11 30t38 9h149q-2 23 -2 69v93q0 48 2 75h-149q-27 0 -38 9t-11 30v9q0 21 11 30.5t38 9.5h162q37 218 169.5 345t329.5 127q112 0 197 -35t137 -90q70 -73 70 -136q0 -20 -14.5 -34.5t-29 -19t-18.5 -1.5q-97 230 -348 230q-152 0 -256.5 -103.5 t-138.5 -282.5h473q26 0 36.5 -9.5t10.5 -30.5v-9q0 -21 -10.5 -30t-36.5 -9h-485q-2 -25 -2 -75v-97q0 -42 2 -65h485q26 0 36.5 -9t10.5 -30v-8q0 -21 -10.5 -30t-36.5 -9h-473q34 -183 140.5 -289t264.5 -106q259 0 349 241q3 4 17.5 -0.5t29 -19.5t14.5 -37 q0 -70 -76 -143q-57 -58 -141 -93.5t-188 -35.5q-208 0 -341.5 127.5t-168.5 355.5h-160q-27 0 -38 9t-11 30z" />
<glyph unicode="&#xa5;" horiz-adv-x="1132" d="M70 1288q-5 8 16.5 25.5t53 17t50.5 -34.5l375 -628l381 626q21 37 51 37.5t49.5 -17t14.5 -26.5l-418 -659h324q26 0 36.5 -9t10.5 -30v-8q0 -21 -10.5 -30t-36.5 -9h-355v-203h355q47 0 47 -39v-10q0 -39 -47 -39h-355v-211q0 -47 -43 -47h-10q-41 0 -41 47v211h-354 q-47 0 -47 39v10q0 39 47 39h354v203h-354q-26 0 -36.5 9t-10.5 30v8q0 21 10.5 30t36.5 9h323z" />
<glyph unicode="&#xa7;" horiz-adv-x="1026" d="M68 -84q0 19 13.5 35t27 22.5t14.5 3.5q100 -208 379 -208q148 0 234.5 56.5t86.5 151.5q0 45 -18.5 79.5t-38.5 55t-76.5 41t-86.5 28.5t-114 27l-12 3q-45 10 -74.5 17.5t-71 20t-68.5 24.5t-59.5 29.5t-53 38.5t-39 47t-27 59t-8.5 71q0 95 65.5 161.5t180.5 94.5 q-100 36 -154.5 96.5t-54.5 163.5q0 136 116.5 224t288.5 88q223 0 342 -119q53 -56 53 -101q0 -19 -12.5 -34t-25.5 -21t-17 -4q-105 188 -340 188q-131 0 -219 -57.5t-88 -150.5q0 -43 17 -76.5t43.5 -56.5t73 -42.5t92 -32.5t114.5 -28q45 -10 73.5 -17.5t70.5 -20 t69 -24.5t60 -29.5t53.5 -38t39 -46.5t27 -58.5t8.5 -70.5q0 -95 -63.5 -162.5t-173.5 -97.5q98 -33 152.5 -93t54.5 -169q0 -137 -115.5 -221.5t-302.5 -84.5q-131 0 -225.5 35t-147.5 88q-63 63 -63 115zM170 526q0 -43 19.5 -76.5t47.5 -55.5t79.5 -41.5t93 -30 t110.5 -25.5l80 -18q108 10 183 74t75 143q0 36 -11.5 64.5t-38 51t-53 38t-73.5 30t-80.5 23t-93.5 22.5l-78 16q-112 -12 -186 -75t-74 -140z" />
<glyph unicode="&#xa8;" horiz-adv-x="1024" d="M272 1194q0 36 21 57t55 21q37 0 57.5 -20.5t20.5 -57.5q0 -36 -21 -57t-55 -21q-36 0 -57 21t-21 57zM598 1194q0 37 20.5 57.5t55.5 20.5q36 0 57 -21t21 -57t-21 -57t-55 -21q-37 0 -57.5 20.5t-20.5 57.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1533" d="M100 659q0 145 51 272.5t139 218.5t212.5 143.5t265.5 52.5q145 0 269.5 -50.5t212 -140t137 -216t49.5 -273.5q0 -145 -51 -272.5t-139.5 -218t-213 -143t-266.5 -52.5q-193 0 -345 86.5t-236.5 241.5t-84.5 351zM182 662q0 -264 166 -436.5t422 -172.5 q257 0 420.5 171.5t163.5 439.5q0 130 -44.5 243.5t-122 194t-186.5 126.5t-235 46q-258 0 -421 -171t-163 -441zM428 659q0 155 101.5 260t252.5 105q74 0 134 -24t94 -60q43 -43 43 -82q0 -15 -12.5 -27.5t-25 -17t-14.5 -2.5q-26 58 -83 93.5t-138 35.5 q-109 0 -183.5 -80.5t-74.5 -197.5q0 -122 74 -201.5t192 -79.5q87 0 142 39.5t80 99.5q2 3 15 -1t25.5 -16.5t12.5 -29.5q0 -40 -53 -96q-34 -34 -93 -57t-133 -23q-158 0 -257 101.5t-99 260.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="851" d="M72 748q0 82 47 134t174 85.5t348 42.5v20q0 110 -59 172t-174 62q-171 0 -252 -144q-2 -2 -14 1.5t-25 16t-13 31.5q0 31 48 82q99 95 262 95q156 0 237.5 -85t81.5 -231v-473q0 -3 -10 -6.5t-21 -3.5q-28 0 -43.5 18t-15.5 68v63q-37 -71 -115 -115t-172 -44 q-131 0 -207.5 56t-76.5 155zM166 752q0 -66 54.5 -103t148.5 -37q113 0 193.5 67t80.5 159v104q-144 -6 -239.5 -21t-146.5 -40.5t-71 -55.5t-20 -73zM344 55v13q0 65 66 65h12q65 0 65 -65v-13q0 -65 -65 -65h-12q-66 0 -66 65z" />
<glyph unicode="&#xab;" horiz-adv-x="999" d="M90 496q0 5 19.5 28.5t61 64t91.5 83.5q78 67 121 96t68 29q15 0 23.5 -10t8.5 -25q0 -33 -155 -154q-32 -25 -152 -114q84 -59 160 -119q81 -64 114 -97.5t33 -50.5q0 -15 -8.5 -24.5t-23.5 -9.5q-25 0 -67 28t-122 96q-51 43 -92 84t-60.5 65t-19.5 30zM471 496 q0 5 20.5 29.5t64 68t95.5 88.5q85 74 132 106.5t75 32.5q17 0 28 -11t11 -26q0 -23 -41 -62.5t-137 -111.5q-16 -13 -156 -116q109 -77 160 -119q98 -76 136 -112t38 -58q0 -14 -11 -24.5t-28 -10.5q-28 0 -73.5 31t-131.5 104q-53 46 -96.5 89.5t-64.5 69.5t-21 32z" />
<glyph unicode="&#xad;" horiz-adv-x="718" d="M86 502v10q0 41 47 41h453q47 0 47 -41v-10q0 -39 -47 -39h-453q-47 0 -47 39z" />
<glyph unicode="&#xae;" horiz-adv-x="1533" d="M100 659q0 145 51 272.5t139 218.5t212.5 143.5t265.5 52.5q145 0 269.5 -50.5t212 -140t137 -216t49.5 -273.5q0 -145 -51 -272.5t-139.5 -218t-213 -143t-266.5 -52.5q-193 0 -345 86.5t-236.5 241.5t-84.5 351zM182 662q0 -264 166 -436.5t422 -172.5 q257 0 420.5 171.5t163.5 439.5q0 130 -44.5 243.5t-122 194t-186.5 126.5t-235 46q-258 0 -421 -171t-163 -441zM526 360v607q0 26 14 40.5t38 14.5h206q130 0 198.5 -54t68.5 -159q0 -96 -62.5 -151t-183.5 -56l239 -250q2 -4 -3 -13.5t-18 -17.5t-28 -8q-37 0 -63 33 l-225 250h-89v-240q0 -43 -38 -43h-15q-39 0 -39 47zM616 672h170q85 0 130 36t45 101q0 135 -179 135h-166v-272z" />
<glyph unicode="&#xaf;" horiz-adv-x="1024" d="M244 1204v8q0 41 59 41h418q59 0 59 -41v-8q0 -43 -59 -43h-418q-59 0 -59 43z" />
<glyph unicode="&#xb0;" horiz-adv-x="782" d="M104 1044v17q0 123 83.5 206t207.5 83q123 0 203 -82t80 -205v-16q0 -123 -84 -206t-207 -83t-203 81t-80 205zM188 1047q0 -88 58.5 -148.5t144.5 -60.5t144.5 59t58.5 147v17q0 88 -59.5 148.5t-145.5 60.5t-143.5 -59.5t-57.5 -147.5v-16z" />
<glyph unicode="&#xb1;" horiz-adv-x="1228" d="M193 303v10q0 21 10 31t37 10h749q47 0 47 -41v-10q0 -41 -47 -41h-749q-27 0 -37 10t-10 31zM223 872v11q0 21 10.5 30t36.5 9h297v299q0 49 43 49h8q41 0 41 -49v-299h297q27 0 37.5 -9t10.5 -30v-11q0 -20 -10.5 -29t-37.5 -9h-297v-316q0 -47 -41 -47h-8 q-22 0 -32.5 10.5t-10.5 36.5v316h-297q-47 0 -47 38z" />
<glyph unicode="&#xb2;" horiz-adv-x="688" d="M86 1151q0 25 18.5 59t51.5 62q77 67 196 67q118 0 184 -58.5t66 -156.5q0 -31 -7 -57.5t-23.5 -48.5t-32.5 -38t-45 -33.5t-50 -28t-57 -27.5q-43 -20 -67 -31.5t-57 -33t-50 -41.5t-30 -50t-13 -65v-4h399q45 0 45 -37v-6q0 -39 -45 -39h-419q-33 0 -47.5 12.5 t-14.5 40.5v33q0 56 17.5 100t53.5 76.5t70.5 54t89.5 47.5q7 3 20 10q46 23 67.5 35t50 33t40 44t11.5 52q0 60 -43 97.5t-121 37.5q-80 0 -126.5 -42.5t-63.5 -108.5q-3 0 -18.5 -1.5t-32.5 11t-17 35.5z" />
<glyph unicode="&#xb3;" horiz-adv-x="688" d="M55 733q0 22 14 34.5t28 15t16 -0.5q19 -61 74.5 -98t136.5 -37q86 0 140 41t54 107t-50.5 103.5t-137.5 37.5h-66q-45 0 -45 35v6q0 33 45 33h76q71 0 114.5 34t43.5 89q0 58 -48 93.5t-126 35.5q-70 0 -120.5 -34t-70.5 -83q-2 -2 -15 1t-26.5 15.5t-13.5 32.5 q0 22 19.5 48t52.5 46q76 51 176 51q123 0 190.5 -52.5t67.5 -145.5q0 -124 -131 -162q72 -21 111.5 -69t39.5 -119q0 -102 -74 -161t-204 -59q-102 0 -174 45q-45 25 -71 58t-26 59z" />
<glyph unicode="&#xb4;" horiz-adv-x="1024" d="M371 1120q-3 4 24.5 38t85.5 88.5t123 103.5q60 45 92 45q19 0 32 -11.5t13 -29.5q0 -20 -16.5 -40t-60.5 -54q-67 -48 -137.5 -84t-110.5 -49t-45 -7z" />
<glyph unicode="&#xb6;" horiz-adv-x="1196" d="M72 969q0 174 116.5 266t343.5 92h232q27 0 44 -17t17 -44v-1225q0 -47 -43 -47h-12q-45 0 -45 47v582h-182q-231 0 -351 87t-120 259zM932 41v1243q0 47 45 47h12q45 0 45 -47v-1243q0 -47 -45 -47h-12q-45 0 -45 47z" />
<glyph unicode="&#xb7;" horiz-adv-x="421" d="M133 500v16q0 34 18 53t52 19h14q35 0 53.5 -19t18.5 -53v-16q0 -33 -18.5 -51.5t-53.5 -18.5h-14q-33 0 -51.5 18.5t-18.5 51.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="1024" d="M346 -412l127 260q9 21 14 30t17 16.5t31 7.5h8q21 0 36 -12.5t15 -32.5v-7q0 -4 -1.5 -8.5t-3.5 -8.5t-6.5 -10.5t-7 -10.5t-9.5 -13t-11 -14l-158 -221q-10 -12 -35.5 -4.5t-15.5 28.5z" />
<glyph unicode="&#xb9;" horiz-adv-x="688" d="M102 1217q0 14 7 26t14.5 17t9.5 4q24 -35 82 -35q46 0 81 29t38 73h35q45 0 45 -47v-618h149q45 0 45 -41v-4q0 -37 -45 -37h-411q-46 0 -46 37v4q0 41 46 41h170v526q-39 -51 -113 -51q-54 0 -82 28q-25 18 -25 48z" />
<glyph unicode="&#xba;" horiz-adv-x="950" d="M82 938q0 171 114 289.5t281 118.5q168 0 279.5 -116.5t111.5 -287.5t-113 -289t-280 -118q-168 0 -280.5 116t-112.5 287zM174 940q0 -136 86.5 -230t214.5 -94q131 0 217 93t86 231q0 137 -86.5 230.5t-214.5 93.5q-131 0 -217 -93t-86 -231zM403 55v13q0 31 16.5 48 t47.5 17h14q66 0 66 -65v-13q0 -65 -66 -65h-14q-31 0 -47.5 17t-16.5 48z" />
<glyph unicode="&#xbb;" horiz-adv-x="999" d="M104 207q0 41 177 174l155 117q-125 89 -160 116q-95 73 -133.5 111.5t-38.5 60.5q0 14 10 24.5t27 10.5q28 0 73.5 -31t131.5 -104q53 -47 96.5 -90t64.5 -68.5t21 -31.5t-20.5 -31t-63.5 -68t-96 -90q-83 -72 -131 -104.5t-76 -32.5q-17 0 -27 10.5t-10 26.5zM516 227 q0 18 35.5 53t120.5 103q57 46 151 115q-87 62 -159 118q-83 67 -115.5 99t-32.5 49q0 15 9.5 24t23.5 9q25 0 66.5 -28t121.5 -95q51 -44 92.5 -85t61.5 -64.5t20 -28.5q0 -6 -20 -30t-61.5 -64.5t-92.5 -84.5q-79 -66 -121 -94t-67 -28q-14 0 -23.5 9.5t-9.5 22.5z" />
<glyph unicode="&#xbc;" horiz-adv-x="1622" d="M102 1217q0 14 7 26t14.5 17t9.5 4q24 -35 82 -35q46 0 81 29t38 73h35q45 0 45 -47v-618h149q45 0 45 -41v-4q0 -37 -45 -37h-411q-46 0 -46 37v4q0 41 46 41h170v526q-39 -51 -113 -51q-54 0 -82 28q-25 18 -25 48zM283 -23q0 22 6.5 36.5t29.5 45.5q12 16 89 109 t189.5 230t180.5 220l539 676q45 0 45 -32q0 -35 -39 -82q-11 -15 -88.5 -109.5t-188.5 -230.5t-180 -222q-70 -87 -207 -258.5t-233.5 -293t-97.5 -121.5q-8 -5 -26.5 3t-18.5 29zM977 252v20q2 23 29 60l288 375q18 26 34 34.5t52 8.5q38 0 54 -15t16 -49v-399h96 q43 0 43 -37v-4q0 -20 -10 -28.5t-33 -8.5h-96v-168q0 -45 -41 -45h-6q-39 0 -39 45v168h-326q-27 0 -44 12t-17 31zM1063 285h303l-2 389z" />
<glyph unicode="&#xbd;" horiz-adv-x="1622" d="M102 1217q0 14 7 26t14.5 17t9.5 4q24 -35 82 -35q46 0 81 29t38 73h35q45 0 45 -47v-618h149q45 0 45 -41v-4q0 -37 -45 -37h-411q-46 0 -46 37v4q0 41 46 41h170v526q-39 -51 -113 -51q-54 0 -82 28q-25 18 -25 48zM242 -23q0 21 6.5 36t30.5 46q13 16 88.5 108 t190 232t179.5 219l539 676q45 0 45 -32q0 -35 -39 -82q-11 -15 -88.5 -109.5t-188.5 -230.5t-180 -222q-70 -87 -207 -258.5t-233.5 -293t-97.5 -121.5q-8 -5 -26.5 3t-18.5 29zM1020 567q0 25 18.5 59t51.5 62q78 68 196 68t184 -58.5t66 -156.5q0 -46 -15.5 -82t-50 -64 t-64.5 -45t-85 -43q-43 -20 -67 -31.5t-57 -33t-50 -41.5t-30 -50t-13 -65v-4h399q45 0 45 -37v-6q0 -39 -45 -39h-420q-33 0 -47 12.5t-14 40.5v33q0 56 17.5 100t53.5 77t70.5 54.5t89.5 47.5q7 3 20 10q46 23 67.5 35t50 33t40 44t11.5 52q0 60 -43 97.5t-121 37.5 q-80 0 -127 -43t-64 -109q-3 0 -18 -1.5t-32 11t-17 35.5z" />
<glyph unicode="&#xbe;" horiz-adv-x="1622" d="M55 733q0 22 14 34.5t28 15t16 -0.5q19 -61 74.5 -98t136.5 -37q86 0 140 41t54 107t-50.5 103.5t-137.5 37.5h-66q-45 0 -45 35v6q0 33 45 33h76q71 0 114.5 34t43.5 89q0 58 -48 93.5t-126 35.5q-70 0 -120.5 -34t-70.5 -83q-2 -2 -15 1t-26.5 15.5t-13.5 32.5 q0 22 19.5 48t52.5 46q76 51 176 51q123 0 190.5 -52.5t67.5 -145.5q0 -124 -131 -162q72 -21 111.5 -69t39.5 -119q0 -102 -74 -161t-204 -59q-102 0 -174 45q-45 25 -71 58t-26 59zM283 -23q0 22 6.5 36.5t29.5 45.5q12 16 89 109t189.5 230t180.5 220l539 676 q45 0 45 -32q0 -35 -39 -82q-11 -15 -88.5 -109.5t-188.5 -230.5t-180 -222q-70 -87 -207 -258.5t-233.5 -293t-97.5 -121.5q-8 -5 -26.5 3t-18.5 29zM977 252v20q2 23 29 60l288 375q18 26 34 34.5t52 8.5q38 0 54 -15t16 -49v-399h96q43 0 43 -37v-4q0 -20 -10 -28.5 t-33 -8.5h-96v-168q0 -45 -41 -45h-6q-39 0 -39 45v168h-326q-27 0 -44 12t-17 31zM1063 285h303l-2 389z" />
<glyph unicode="&#xbf;" horiz-adv-x="937" d="M61 303q0 58 16 104.5t50 84t61 59.5t76 55q45 31 66 46t56 48.5t51.5 63.5t30 77t13.5 103q0 29 43 29t43 -35q-2 -67 -17.5 -123t-35 -92.5t-54 -72.5t-59 -54.5t-65.5 -46.5q-42 -28 -66 -46.5t-52 -48t-41 -65t-13 -78.5q0 -107 76.5 -174t214.5 -67q79 0 141 24.5 t101 66.5t62 91t32 106q3 4 20.5 0.5t35 -19t17.5 -40.5q0 -36 -24.5 -94t-71.5 -105q-120 -120 -317 -120q-179 0 -284.5 93t-105.5 230zM442 1241v31q0 32 14 45.5t48 13.5h28q32 0 46 -13.5t14 -45.5v-31q0 -33 -13 -46t-47 -13h-28q-34 0 -48 13.5t-14 45.5z" />
<glyph unicode="&#xc0;" horiz-adv-x="1222" d="M31 25l504 1271q16 39 75 39q62 0 78 -39l504 -1271q2 -10 -21.5 -22.5t-54.5 -5.5t-45 46l-166 416h-592l-166 -416q-16 -40 -45.5 -46.5t-51.5 6.5t-19 22zM324 1704q0 18 12.5 29.5t30.5 11.5q34 0 92 -45q65 -49 123 -103.5t85.5 -88t24.5 -38.5q-5 -6 -44.5 7 t-109.5 48.5t-137 84.5q-45 33 -61 53t-16 41zM346 549h526l-262 670z" />
<glyph unicode="&#xc1;" horiz-adv-x="1222" d="M31 25l504 1271q16 39 75 39q62 0 78 -39l504 -1271q2 -10 -21.5 -22.5t-54.5 -5.5t-45 46l-166 416h-592l-166 -416q-16 -40 -45.5 -46.5t-51.5 6.5t-19 22zM346 549h526l-262 670zM508 1477q-3 4 25 37.5t85.5 88t122.5 103.5q60 45 93 45q19 0 32 -11.5t13 -29.5 q0 -20 -16.5 -40t-61.5 -54q-67 -48 -137.5 -84t-110.5 -48.5t-45 -6.5z" />
<glyph unicode="&#xc2;" horiz-adv-x="1222" d="M31 25l504 1271q16 39 75 39q62 0 78 -39l504 -1271q2 -10 -21.5 -22.5t-54.5 -5.5t-45 46l-166 416h-592l-166 -416q-16 -40 -45.5 -46.5t-51.5 6.5t-19 22zM346 549h526l-262 670zM365 1493q0 22 24.5 57.5t79.5 98.5q35 40 69 72.5t51.5 46.5t20.5 14t21 -14t51 -46 t66 -69q56 -64 81 -100.5t25 -59.5q0 -35 -31 -35q-20 0 -47 26t-86 101l-82 109l-82 -111q-51 -69 -80 -97t-49 -28q-14 0 -23 9t-9 26z" />
<glyph unicode="&#xc3;" horiz-adv-x="1222" d="M31 25l504 1271q16 39 75 39q62 0 78 -39l504 -1271q2 -10 -21.5 -22.5t-54.5 -5.5t-45 46l-166 416h-592l-166 -416q-16 -40 -45.5 -46.5t-51.5 6.5t-19 22zM340 1493q6 94 45 148.5t107 54.5q48 0 84 -22.5t73 -63.5q33 -33 53.5 -46.5t42.5 -13.5q28 0 44.5 21.5 t25.5 64.5q6 25 15.5 36t27.5 11t35 -14q-5 -94 -43.5 -147.5t-108.5 -53.5q-46 0 -82 22t-75 62q-33 33 -53 46.5t-42 13.5q-29 0 -44 -21t-25 -65q-6 -25 -15.5 -36t-27.5 -11q-16 0 -37 14zM346 549h526l-262 670z" />
<glyph unicode="&#xc4;" horiz-adv-x="1222" d="M31 25l504 1271q16 39 75 39q62 0 78 -39l504 -1271q2 -10 -21.5 -22.5t-54.5 -5.5t-45 46l-166 416h-592l-166 -416q-16 -40 -45.5 -46.5t-51.5 6.5t-19 22zM346 549h526l-262 670zM371 1544q0 36 20.5 57t54.5 21q37 0 57.5 -20.5t20.5 -57.5q0 -36 -20.5 -57 t-54.5 -21q-36 0 -57 21t-21 57zM696 1544q0 37 20.5 57.5t55.5 20.5q36 0 57 -21t21 -57t-21 -57t-55 -21q-37 0 -57.5 20.5t-20.5 57.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1222" d="M31 25l495 1253q-62 22 -98.5 76t-36.5 125q0 96 62.5 159.5t158.5 63.5t157.5 -63.5t61.5 -159.5q0 -72 -37 -125.5t-100 -75.5l498 -1253q2 -10 -21.5 -22.5t-54.5 -5.5t-45 46l-166 416h-592l-166 -416q-16 -40 -45.5 -46.5t-51.5 6.5t-19 22zM346 549h526l-262 674z M465 1479q0 -65 41.5 -109.5t103.5 -44.5q63 0 104.5 44t41.5 110t-41.5 110.5t-104.5 44.5t-104 -44.5t-41 -110.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1728" d="M31 25l495 1253q21 47 68 47h987q47 0 47 -41v-10q0 -41 -47 -41h-872l200 -506h527q49 0 49 -43v-8q0 -43 -49 -43h-488l213 -539h447q47 0 47 -41v-10q0 -43 -47 -43h-461q-37 0 -55 13t-31 44l-154 402h-596l-164 -416q-16 -40 -45.5 -46.5t-51.5 6.5t-19 22zM346 549 h526l-262 670z" />
<glyph unicode="&#xc7;" horiz-adv-x="1345" d="M100 659q0 148 48 275.5t133.5 218t208 142t266.5 51.5q143 0 250 -44.5t174 -111.5q32 -32 51.5 -67.5t19.5 -57.5q0 -23 -14.5 -38.5t-29 -20.5t-17.5 -2q-54 113 -171 183t-265 70q-243 0 -396 -167t-153 -428q0 -259 154 -426.5t399 -167.5q173 0 289.5 82t156.5 202 q3 4 19 -0.5t32.5 -20t16.5 -38.5q0 -26 -24 -71.5t-66 -86.5q-65 -66 -176 -110.5t-252 -44.5q-194 0 -343 86.5t-230 241t-81 351.5zM549 -412l127 260q7 16 10.5 23t10.5 16t16.5 12t23.5 3h8q21 0 36.5 -12.5t15.5 -32.5v-7q0 -4 -1.5 -8.5t-3.5 -8.5t-6.5 -10.5 t-7 -10.5t-9.5 -13t-11 -14l-158 -221q-10 -12 -35.5 -4.5t-15.5 28.5z" />
<glyph unicode="&#xc8;" horiz-adv-x="1155" d="M162 61v1203q0 26 16.5 43.5t42.5 17.5h795q49 0 49 -41v-10q0 -41 -49 -41h-752v-508h582q49 0 49 -41v-6q0 -43 -49 -43h-582v-541h770q49 0 49 -41v-10q0 -43 -49 -43h-813q-26 0 -42.5 17.5t-16.5 43.5zM383 1710q0 18 12.5 29.5t30.5 11.5q34 0 92 -45 q66 -49 124 -103.5t85.5 -88t24.5 -37.5q-5 -6 -44.5 6.5t-109.5 48.5t-137 84q-45 33 -61.5 53t-16.5 41z" />
<glyph unicode="&#xc9;" horiz-adv-x="1155" d="M162 61v1203q0 26 16.5 43.5t42.5 17.5h795q49 0 49 -41v-10q0 -41 -49 -41h-752v-508h582q49 0 49 -41v-6q0 -43 -49 -43h-582v-541h770q49 0 49 -41v-10q0 -43 -49 -43h-813q-26 0 -42.5 17.5t-16.5 43.5zM473 1477q-3 4 25 37.5t86 88t123 103.5q60 45 92 45 q19 0 32 -11.5t13 -29.5q0 -20 -16.5 -40t-61.5 -54q-67 -48 -137.5 -84t-110.5 -48.5t-45 -6.5z" />
<glyph unicode="&#xca;" horiz-adv-x="1155" d="M162 61v1203q0 26 16.5 43.5t42.5 17.5h795q49 0 49 -41v-10q0 -41 -49 -41h-752v-508h582q49 0 49 -41v-6q0 -43 -49 -43h-582v-541h770q49 0 49 -41v-10q0 -43 -49 -43h-813q-26 0 -42.5 17.5t-16.5 43.5zM373 1507q0 22 24.5 57.5t79.5 98.5q35 40 69 72.5t51.5 46.5 t20.5 14t21 -14t51 -46t66 -69q56 -64 81 -100.5t25 -59.5q0 -34 -31 -34q-20 0 -47.5 25.5t-85.5 100.5l-82 109l-81 -111q-51 -69 -80 -96.5t-49 -27.5q-14 0 -23.5 9t-9.5 25z" />
<glyph unicode="&#xcb;" horiz-adv-x="1155" d="M162 61v1203q0 26 16.5 43.5t42.5 17.5h795q49 0 49 -41v-10q0 -41 -49 -41h-752v-508h582q49 0 49 -41v-6q0 -43 -49 -43h-582v-541h770q49 0 49 -41v-10q0 -43 -49 -43h-813q-26 0 -42.5 17.5t-16.5 43.5zM375 1550q0 36 21 57t55 21q37 0 57 -20.5t20 -57.5 q0 -36 -20.5 -56.5t-54.5 -20.5q-36 0 -57 20.5t-21 56.5zM700 1550q0 37 20.5 57.5t55.5 20.5q36 0 57 -21t21 -57t-21 -56.5t-55 -20.5q-37 0 -57.5 20t-20.5 57z" />
<glyph unicode="&#xcc;" horiz-adv-x="464" d="M-39 1704q0 18 12.5 29.5t30.5 11.5q34 0 92 -45q65 -49 123.5 -103.5t86 -88.5t24.5 -38q-5 -6 -44.5 7t-109.5 48.5t-137 84.5q-45 33 -61.5 53t-16.5 41zM182 41v1243q0 47 43 47h15q45 0 45 -47v-1243q0 -47 -45 -47h-15q-43 0 -43 47z" />
<glyph unicode="&#xcd;" horiz-adv-x="464" d="M137 1470q-3 4 25 38t86 88.5t123 103.5q60 45 92 45q19 0 32 -11.5t13 -29.5q0 -20 -16.5 -40t-61.5 -54q-67 -48 -137.5 -84t-110.5 -49t-45 -7zM182 41v1243q0 47 43 47h15q45 0 45 -47v-1243q0 -47 -45 -47h-15q-43 0 -43 47z" />
<glyph unicode="&#xce;" horiz-adv-x="464" d="M-12 1501q0 22 24.5 57.5t79.5 98.5q35 40 69 72.5t51.5 46.5t20.5 14t21 -14t51 -46t66 -69q56 -64 81 -100.5t25 -59.5q0 -35 -31 -35q-20 0 -47 26t-86 101l-82 109l-81 -111q-51 -69 -80.5 -97t-49.5 -28q-14 0 -23 9t-9 26zM182 41v1243q0 47 43 47h15q45 0 45 -47 v-1243q0 -47 -45 -47h-15q-43 0 -43 47z" />
<glyph unicode="&#xcf;" horiz-adv-x="464" d="M-6 1550q0 36 21 57t55 21q37 0 57 -20.5t20 -57.5q0 -36 -20.5 -56.5t-54.5 -20.5q-36 0 -57 20.5t-21 56.5zM182 41v1243q0 47 43 47h15q45 0 45 -47v-1243q0 -47 -45 -47h-15q-43 0 -43 47zM319 1550q0 37 20.5 57.5t55.5 20.5q36 0 57 -21t21 -57t-21 -56.5 t-55 -20.5q-37 0 -57.5 20t-20.5 57z" />
<glyph unicode="&#xd0;" horiz-adv-x="1456" d="M45 657v11q0 41 47 41h156v555q0 26 17.5 43.5t43.5 17.5h320q340 0 534.5 -176t194.5 -483q0 -309 -198.5 -487.5t-541.5 -178.5h-309q-26 0 -43.5 17.5t-17.5 43.5v557h-156q-26 0 -36.5 9.5t-10.5 29.5zM350 94h281q297 0 459.5 150t162.5 420q0 266 -162.5 417.5 t-453.5 151.5h-287v-524h465q47 0 47 -41v-11q0 -39 -47 -39h-465v-524z" />
<glyph unicode="&#xd1;" horiz-adv-x="1378" d="M162 41v1221q0 31 14 50t41 19h27q26 0 42.5 -12.5t37.5 -42.5l798 -1118v1126q0 25 9 36t32 11h15q41 0 41 -47v-1223q0 -30 -15 -48.5t-39 -18.5h-8q-28 0 -41 9t-29 34l-829 1161v-1157q0 -47 -43 -47h-14q-39 0 -39 47zM420 1485q6 94 44.5 148.5t106.5 54.5 q80 0 158 -86q33 -33 53.5 -46.5t42.5 -13.5q28 0 44.5 21.5t25.5 64.5q6 25 15.5 36t27.5 11t35 -14q-5 -94 -43.5 -147.5t-108.5 -53.5q-46 0 -82 22t-75 62q-33 33 -53 46.5t-42 13.5q-29 0 -44 -21t-25 -65q-6 -25 -15.5 -36.5t-27.5 -11.5q-15 0 -37 15z" />
<glyph unicode="&#xd2;" d="M100 659q0 147 48 274.5t133.5 218t208 142.5t266.5 52q192 0 340 -87.5t228.5 -242.5t80.5 -352t-81.5 -352.5t-230.5 -243.5t-341 -88q-290 0 -471 190t-181 489zM205 662q0 -169 69 -304.5t195 -212.5t285 -77q242 0 394 167.5t152 426.5q0 257 -153 426t-395 169 q-160 0 -285.5 -77t-193.5 -212t-68 -306zM469 1710q0 18 12.5 29.5t30.5 11.5q34 0 92 -45q66 -49 124 -103.5t85.5 -88t24.5 -37.5q-5 -6 -44.5 6.5t-109.5 48.5t-137 84q-45 33 -61.5 53t-16.5 41z" />
<glyph unicode="&#xd3;" d="M100 659q0 147 48 274.5t133.5 218t208 142.5t266.5 52q192 0 340 -87.5t228.5 -242.5t80.5 -352t-81.5 -352.5t-230.5 -243.5t-341 -88q-290 0 -471 190t-181 489zM205 662q0 -169 69 -304.5t195 -212.5t285 -77q242 0 394 167.5t152 426.5q0 257 -153 426t-395 169 q-160 0 -285.5 -77t-193.5 -212t-68 -306zM655 1477q-3 4 25 37.5t86 88t123 103.5q60 45 92 45q19 0 32 -11.5t13 -29.5q0 -20 -16.5 -40t-61.5 -54q-67 -48 -137.5 -84t-110.5 -48.5t-45 -6.5z" />
<glyph unicode="&#xd4;" d="M100 659q0 147 48 274.5t133.5 218t208 142.5t266.5 52q192 0 340 -87.5t228.5 -242.5t80.5 -352t-81.5 -352.5t-230.5 -243.5t-341 -88q-290 0 -471 190t-181 489zM205 662q0 -169 69 -304.5t195 -212.5t285 -77q242 0 394 167.5t152 426.5q0 257 -153 426t-395 169 q-160 0 -285.5 -77t-193.5 -212t-68 -306zM502 1507q0 22 24.5 57.5t79.5 98.5q35 40 69.5 72.5t52 46.5t20.5 14t20.5 -14t50.5 -46t66 -69q56 -64 81 -100.5t25 -59.5q0 -34 -30 -34q-21 0 -48.5 26t-85.5 100l-82 109l-81 -111q-51 -69 -80 -96.5t-49 -27.5 q-14 0 -23.5 9t-9.5 25z" />
<glyph unicode="&#xd5;" d="M100 659q0 147 48 274.5t133.5 218t208 142.5t266.5 52q192 0 340 -87.5t228.5 -242.5t80.5 -352t-81.5 -352.5t-230.5 -243.5t-341 -88q-290 0 -471 190t-181 489zM205 662q0 -169 69 -304.5t195 -212.5t285 -77q242 0 394 167.5t152 426.5q0 257 -153 426t-395 169 q-160 0 -285.5 -77t-193.5 -212t-68 -306zM469 1495q6 94 45 148.5t107 54.5q48 0 84 -22.5t73 -63.5q33 -33 53.5 -46.5t42.5 -13.5q28 0 44.5 21.5t25.5 64.5q6 25 15.5 36.5t27.5 11.5q16 0 35 -15q-5 -94 -43.5 -147.5t-108.5 -53.5q-46 0 -82 22t-75 62 q-33 33 -53 46.5t-42 13.5q-29 0 -44 -21t-25 -65q-6 -25 -15.5 -36t-27.5 -11q-16 0 -37 14z" />
<glyph unicode="&#xd6;" d="M100 659q0 147 48 274.5t133.5 218t208 142.5t266.5 52q192 0 340 -87.5t228.5 -242.5t80.5 -352t-81.5 -352.5t-230.5 -243.5t-341 -88q-290 0 -471 190t-181 489zM205 662q0 -169 69 -304.5t195 -212.5t285 -77q242 0 394 167.5t152 426.5q0 257 -153 426t-395 169 q-160 0 -285.5 -77t-193.5 -212t-68 -306zM508 1550q0 36 21 57t55 21q37 0 57.5 -20.5t20.5 -57.5q0 -36 -21 -56.5t-55 -20.5q-36 0 -57 20.5t-21 56.5zM834 1550q0 37 20 57.5t55 20.5q36 0 57 -21t21 -57t-21 -56.5t-55 -20.5q-37 0 -57 20t-20 57z" />
<glyph unicode="&#xd7;" horiz-adv-x="1064" d="M195 193q-30 30 2 65l278 285l-278 282q-33 33 -2 64l6 6q29 29 65 -6l271 -285l270 285q35 35 65 6l7 -6q29 -29 -5 -64l-276 -282l276 -285q34 -36 5 -65l-7 -7q-29 -29 -65 7l-270 286l-271 -286q-36 -36 -65 -7z" />
<glyph unicode="&#xd8;" d="M94 -27q0 34 39 76l127 146q-160 190 -160 464q0 192 80.5 348t230 247.5t339.5 91.5q238 0 407 -140l162 187q3 3 15.5 -2.5t24 -20t11.5 -31.5q0 -33 -39 -75l-106 -123q83 -91 127.5 -213.5t44.5 -263.5q0 -191 -80.5 -346.5t-230.5 -246.5t-341 -91q-245 0 -417 145 q0 1 -46 -50.5t-92 -102.5l-45 -52q-3 -3 -15.5 2.5t-24 19.5t-11.5 31zM203 662q0 -236 129 -396l758 869q-140 118 -342 118q-122 0 -225 -45.5t-172.5 -124.5t-108.5 -187.5t-39 -233.5zM393 201q145 -129 357 -129q162 0 287 78t191 211.5t66 300.5q0 248 -141 407 q-758 -868 -760 -868z" />
<glyph unicode="&#xd9;" horiz-adv-x="1292" d="M141 516v768q0 47 43 47h15q45 0 45 -47v-760q0 -452 403 -452q204 0 304 112.5t100 339.5v760q0 25 10 36t33 11h18q21 0 30 -11t9 -36v-764q0 -264 -129.5 -402t-378.5 -138q-247 0 -374.5 136t-127.5 400zM385 1688q0 18 12.5 29.5t30.5 11.5q33 0 92 -46 q66 -49 124 -103.5t85.5 -88t24.5 -37.5q-5 -6 -44.5 6.5t-109.5 48.5t-137 84q-45 33 -61.5 53.5t-16.5 41.5z" />
<glyph unicode="&#xda;" horiz-adv-x="1292" d="M141 516v768q0 47 43 47h15q45 0 45 -47v-760q0 -452 403 -452q204 0 304 112.5t100 339.5v760q0 25 10 36t33 11h18q21 0 30 -11t9 -36v-764q0 -264 -129.5 -402t-378.5 -138q-247 0 -374.5 136t-127.5 400zM541 1454q-3 4 25 37.5t85.5 88t122.5 103.5q62 46 92 46 q19 0 32 -11.5t13 -29.5q0 -20 -16.5 -41t-60.5 -54q-67 -48 -137.5 -84t-110.5 -48.5t-45 -6.5z" />
<glyph unicode="&#xdb;" horiz-adv-x="1292" d="M141 516v768q0 47 43 47h15q45 0 45 -47v-760q0 -452 403 -452q204 0 304 112.5t100 339.5v760q0 25 10 36t33 11h18q21 0 30 -11t9 -36v-764q0 -264 -129.5 -402t-378.5 -138q-247 0 -374.5 136t-127.5 400zM395 1499q0 22 24.5 57t80.5 99q35 40 69 72.5t51.5 46.5 t20.5 14t20.5 -14t50.5 -46t66 -69q57 -65 82 -101t25 -59q0 -35 -31 -35q-20 0 -47 26t-86 101l-82 109l-82 -111q-51 -69 -80 -97t-49 -28q-14 0 -23.5 9t-9.5 26z" />
<glyph unicode="&#xdc;" horiz-adv-x="1292" d="M141 516v768q0 47 43 47h15q45 0 45 -47v-760q0 -452 403 -452q204 0 304 112.5t100 339.5v760q0 25 10 36t33 11h18q21 0 30 -11t9 -36v-764q0 -264 -129.5 -402t-378.5 -138q-247 0 -374.5 136t-127.5 400zM401 1544q0 36 21 57t55 21q37 0 57.5 -20.5t20.5 -57.5 q0 -36 -21 -57t-55 -21q-36 0 -57 21t-21 57zM727 1544q0 37 20.5 57.5t55.5 20.5q36 0 57 -21t21 -57t-21 -57t-55 -21q-37 0 -57.5 20.5t-20.5 57.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1101" d="M18 1288q-3 14 15.5 29.5t48 13.5t49.5 -35l420 -637l420 635q14 22 31 31t31 6t26 -10.5t18.5 -17t5.5 -15.5l-481 -721v-526q0 -47 -45 -47h-14q-43 0 -43 47v526zM434 1450q-3 4 25 37.5t86 88t123 103.5q60 45 92 45q19 0 32 -11.5t13 -29.5q0 -20 -16.5 -40 t-61.5 -54q-67 -48 -137.5 -84t-110.5 -48.5t-45 -6.5z" />
<glyph unicode="&#xde;" horiz-adv-x="1130" d="M162 41v1243q0 47 43 47h14q45 0 45 -47v-217h299q233 0 359.5 -104t126.5 -291q0 -192 -126 -296t-370 -104h-289v-231q0 -47 -45 -47h-14q-43 0 -43 47zM264 362h297q188 0 284.5 79t96.5 227q0 149 -96 228t-283 79h-299v-613z" />
<glyph unicode="&#xdf;" horiz-adv-x="1052" d="M141 41v905q0 224 105.5 340.5t292.5 116.5q164 0 265 -91.5t101 -232.5q0 -139 -85 -219t-259 -92q224 -52 322 -159t98 -267q0 -156 -103 -259t-255 -103q-167 0 -244 77q-39 39 -39 70q0 17 9.5 31.5t19.5 21t12 4.5q81 -118 244 -118q110 0 184 78.5t74 197.5 q0 127 -81.5 214.5t-269.5 131.5q-48 12 -67 31t-19 51q0 20 9 37.5t18 23.5q169 3 252.5 61.5t83.5 180.5q0 108 -73.5 175t-198.5 67q-143 0 -220 -91.5t-77 -277.5v-905q0 -47 -43 -47h-11q-45 0 -45 47z" />
<glyph unicode="&#xe0;" horiz-adv-x="1026" d="M76 246q0 163 166.5 237.5t533.5 79.5h21v49q0 136 -78.5 211.5t-218.5 75.5q-120 0 -204 -50.5t-126 -133.5q-5 0 -15.5 3t-25 17t-14.5 37q0 16 15 43.5t40 50.5q123 123 336 123q181 0 284 -100t103 -275v-610q0 -1 -5 -3.5t-13.5 -4.5t-16.5 -2q-61 0 -61 98v101 q-48 -96 -151 -154.5t-234 -58.5q-155 0 -245.5 72t-90.5 194zM176 246q0 -86 65.5 -135.5t178.5 -49.5q158 0 267.5 95t109.5 233v96h-27q-318 -6 -456 -61t-138 -178zM279 1354q0 18 12.5 29.5t30.5 11.5q34 0 92 -45q65 -49 123 -103.5t85.5 -88t24.5 -38.5 q-5 -6 -44.5 7t-109.5 48.5t-137 84.5q-45 33 -61 53t-16 41z" />
<glyph unicode="&#xe1;" horiz-adv-x="1026" d="M76 246q0 163 166.5 237.5t533.5 79.5h21v49q0 136 -78.5 211.5t-218.5 75.5q-120 0 -204 -50.5t-126 -133.5q-5 0 -15.5 3t-25 17t-14.5 37q0 16 15 43.5t40 50.5q123 123 336 123q181 0 284 -100t103 -275v-610q0 -1 -5 -3.5t-13.5 -4.5t-16.5 -2q-61 0 -61 98v101 q-48 -96 -151 -154.5t-234 -58.5q-155 0 -245.5 72t-90.5 194zM176 246q0 -86 65.5 -135.5t178.5 -49.5q158 0 267.5 95t109.5 233v96h-27q-318 -6 -456 -61t-138 -178zM371 1120q-3 4 24.5 38t85.5 88.5t123 103.5q60 45 92 45q19 0 32 -11.5t13 -29.5q0 -20 -16.5 -40 t-60.5 -54q-67 -48 -137.5 -84t-110.5 -49t-45 -7z" />
<glyph unicode="&#xe2;" horiz-adv-x="1026" d="M76 246q0 163 166.5 237.5t533.5 79.5h21v49q0 136 -78.5 211.5t-218.5 75.5q-120 0 -204 -50.5t-126 -133.5q-5 0 -15.5 3t-25 17t-14.5 37q0 16 15 43.5t40 50.5q123 123 336 123q181 0 284 -100t103 -275v-610q0 -1 -5 -3.5t-13.5 -4.5t-16.5 -2q-61 0 -61 98v101 q-48 -96 -151 -154.5t-234 -58.5q-155 0 -245.5 72t-90.5 194zM176 246q0 -86 65.5 -135.5t178.5 -49.5q158 0 267.5 95t109.5 233v96h-27q-318 -6 -456 -61t-138 -178zM262 1151q0 22 24.5 57t80.5 99q35 40 69 72.5t51.5 46.5t20.5 14t20.5 -14t50.5 -46t66 -69 q57 -65 82 -101t25 -59q0 -35 -31 -35q-20 0 -47 26t-86 101l-82 109l-82 -111q-51 -69 -80 -97t-49 -28q-14 0 -23.5 9t-9.5 26z" />
<glyph unicode="&#xe3;" horiz-adv-x="1026" d="M76 246q0 163 166.5 237.5t533.5 79.5h21v49q0 136 -78.5 211.5t-218.5 75.5q-120 0 -204 -50.5t-126 -133.5q-5 0 -15.5 3t-25 17t-14.5 37q0 16 15 43.5t40 50.5q123 123 336 123q181 0 284 -100t103 -275v-610q0 -1 -5 -3.5t-13.5 -4.5t-16.5 -2q-61 0 -61 98v101 q-48 -96 -151 -154.5t-234 -58.5q-155 0 -245.5 72t-90.5 194zM176 246q0 -86 65.5 -135.5t178.5 -49.5q158 0 267.5 95t109.5 233v96h-27q-318 -6 -456 -61t-138 -178zM229 1139q6 94 44.5 148t107.5 54q80 0 158 -86q33 -33 53.5 -46t42.5 -13q28 0 44.5 21.5t25.5 64.5 q6 25 15.5 36t27.5 11q17 0 34 -14q-5 -94 -43 -147.5t-108 -53.5q-78 0 -158 84q-33 33 -52.5 46t-41.5 13q-29 0 -44.5 -21t-25.5 -65q-6 -25 -15.5 -36t-27.5 -11q-14 0 -37 15z" />
<glyph unicode="&#xe4;" horiz-adv-x="1026" d="M76 246q0 163 166.5 237.5t533.5 79.5h21v49q0 136 -78.5 211.5t-218.5 75.5q-120 0 -204 -50.5t-126 -133.5q-5 0 -15.5 3t-25 17t-14.5 37q0 16 15 43.5t40 50.5q123 123 336 123q181 0 284 -100t103 -275v-610q0 -1 -5 -3.5t-13.5 -4.5t-16.5 -2q-61 0 -61 98v101 q-48 -96 -151 -154.5t-234 -58.5q-155 0 -245.5 72t-90.5 194zM176 246q0 -86 65.5 -135.5t178.5 -49.5q158 0 267.5 95t109.5 233v96h-27q-318 -6 -456 -61t-138 -178zM270 1194q0 36 21 57t55 21q37 0 57.5 -20.5t20.5 -57.5q0 -36 -21 -57t-55 -21q-36 0 -57 21t-21 57z M596 1194q0 37 20.5 57.5t55.5 20.5q36 0 57 -21t21 -57t-21 -57t-55 -21q-37 0 -57.5 20.5t-20.5 57.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="1026" d="M76 246q0 163 166.5 237.5t533.5 79.5h21v49q0 136 -78.5 211.5t-218.5 75.5q-120 0 -204 -50.5t-126 -133.5q-5 0 -15.5 3t-25 17t-14.5 37q0 16 15 43.5t40 50.5q123 123 336 123q181 0 284 -100t103 -275v-610q0 -1 -5 -3.5t-13.5 -4.5t-16.5 -2q-61 0 -61 98v101 q-48 -96 -151 -154.5t-234 -58.5q-155 0 -245.5 72t-90.5 194zM176 246q0 -86 65.5 -135.5t178.5 -49.5q158 0 267.5 95t109.5 233v96h-27q-318 -6 -456 -61t-138 -178zM291 1335q0 96 63 160t158 64q96 0 157.5 -64t61.5 -160q0 -94 -63 -157.5t-158 -63.5 q-96 0 -157.5 62.5t-61.5 158.5zM365 1335q0 -65 41 -109t104 -44t104 43.5t41 109.5t-41 111t-104 45t-104 -45t-41 -111z" />
<glyph unicode="&#xe6;" horiz-adv-x="1775" d="M76 246q0 163 166.5 238t533.5 81h21v43q0 139 -78.5 215t-218.5 76q-120 0 -204 -50.5t-126 -133.5q-5 0 -15.5 3t-25 17t-14.5 37q0 16 15 43.5t40 50.5q53 53 138 88t198 35q144 0 235 -68.5t119 -195.5q52 123 158 193.5t244 70.5q197 0 319.5 -131.5t122.5 -343.5 v-8q0 -22 -9.5 -29.5t-33.5 -7.5h-780q3 -178 113 -289.5t282 -111.5q243 0 342 194q3 3 16.5 -1.5t27 -19.5t13.5 -36q0 -17 -15.5 -46t-45.5 -59q-50 -50 -138.5 -85t-199.5 -35q-160 0 -274 77t-162 211q-40 -130 -157 -209t-271 -79q-155 0 -245.5 72t-90.5 194z M176 246q0 -86 65.5 -135.5t178.5 -49.5q160 0 268.5 92.5t108.5 235.5v98h-25q-319 -6 -457.5 -61.5t-138.5 -179.5zM885 553h717q-8 157 -101 253.5t-239 96.5q-147 0 -251 -97.5t-126 -252.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="1048" d="M76 487q0 217 139 359.5t354 142.5q211 0 336 -125q35 -36 54.5 -74t19.5 -63q0 -20 -15.5 -33t-30.5 -16t-18 0q-38 99 -122 162t-224 63q-172 0 -283.5 -119t-111.5 -297q0 -181 110.5 -300t284.5 -119q140 0 224 62t122 161q2 4 17.5 0.5t31 -16.5t15.5 -33 q0 -24 -19.5 -63t-56.5 -77q-122 -122 -334 -122q-216 0 -354.5 144t-138.5 363zM389 -412l127 260q9 21 14 30t17 16.5t31 7.5h8q21 0 36 -12.5t15 -32.5v-7q0 -4 -1.5 -8.5t-3.5 -8.5t-6.5 -10.5t-7 -10.5t-9.5 -13t-11 -14l-158 -221q-10 -12 -35.5 -4.5t-15.5 28.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="1075" d="M76 481q0 143 62 259t173 182.5t248 66.5q200 0 322.5 -130.5t122.5 -342.5v-10q0 -21 -9.5 -28t-33.5 -7h-789q3 -180 113.5 -291.5t285.5 -111.5q248 0 347 194q4 3 17.5 -1.5t26.5 -19.5t13 -36q0 -17 -16 -46t-46 -59q-50 -50 -139 -85t-203 -35q-221 0 -358 140.5 t-137 360.5zM176 555h725q-6 156 -99.5 252t-242.5 96t-254.5 -97t-128.5 -251zM289 1354q0 18 12.5 29.5t30.5 11.5q34 0 92 -45q65 -49 123 -103.5t85.5 -88t24.5 -38.5q-5 -6 -44.5 7t-109 48.5t-136.5 84.5q-45 33 -61.5 53t-16.5 41z" />
<glyph unicode="&#xe9;" horiz-adv-x="1075" d="M76 481q0 143 62 259t173 182.5t248 66.5q200 0 322.5 -130.5t122.5 -342.5v-10q0 -21 -9.5 -28t-33.5 -7h-789q3 -180 113.5 -291.5t285.5 -111.5q248 0 347 194q4 3 17.5 -1.5t26.5 -19.5t13 -36q0 -17 -16 -46t-46 -59q-50 -50 -139 -85t-203 -35q-221 0 -358 140.5 t-137 360.5zM176 555h725q-6 156 -99.5 252t-242.5 96t-254.5 -97t-128.5 -251zM434 1120q-3 4 25 38t86 88.5t123 103.5q60 45 92 45q19 0 32 -11.5t13 -29.5q0 -20 -16.5 -40t-61.5 -54q-67 -48 -137.5 -84t-110.5 -49t-45 -7z" />
<glyph unicode="&#xea;" horiz-adv-x="1075" d="M76 481q0 143 62 259t173 182.5t248 66.5q200 0 322.5 -130.5t122.5 -342.5v-10q0 -21 -9.5 -28t-33.5 -7h-789q3 -180 113.5 -291.5t285.5 -111.5q248 0 347 194q4 3 17.5 -1.5t26.5 -19.5t13 -36q0 -17 -16 -46t-46 -59q-50 -50 -139 -85t-203 -35q-221 0 -358 140.5 t-137 360.5zM176 555h725q-6 156 -99.5 252t-242.5 96t-254.5 -97t-128.5 -251zM307 1151q0 22 24.5 57t80.5 99q35 40 69 72.5t51.5 46.5t20.5 14t20.5 -14t50.5 -46t66 -69q57 -65 82 -101t25 -59q0 -35 -31 -35q-20 0 -47 26t-86 101l-82 109l-82 -111q-51 -69 -80 -97 t-49 -28q-14 0 -23.5 9t-9.5 26z" />
<glyph unicode="&#xeb;" horiz-adv-x="1075" d="M76 481q0 143 62 259t173 182.5t248 66.5q200 0 322.5 -130.5t122.5 -342.5v-10q0 -21 -9.5 -28t-33.5 -7h-789q3 -180 113.5 -291.5t285.5 -111.5q248 0 347 194q4 3 17.5 -1.5t26.5 -19.5t13 -36q0 -17 -16 -46t-46 -59q-50 -50 -139 -85t-203 -35q-221 0 -358 140.5 t-137 360.5zM176 555h725q-6 156 -99.5 252t-242.5 96t-254.5 -97t-128.5 -251zM313 1194q0 36 21 57t55 21q37 0 57.5 -20.5t20.5 -57.5q0 -36 -21 -57t-55 -21q-36 0 -57 21t-21 57zM639 1194q0 37 20.5 57.5t55.5 20.5q36 0 57 -21t21 -57t-21 -57t-55 -21 q-37 0 -57.5 20.5t-20.5 57.5z" />
<glyph unicode="&#xec;" horiz-adv-x="401" d="M-61 1354q0 18 12.5 29.5t30.5 11.5q34 0 92 -45q65 -49 123 -103.5t85.5 -88t24.5 -38.5q-5 -6 -44.5 7t-109.5 48.5t-137 84.5q-45 33 -61 53t-16 41zM152 41v887q0 47 43 47h12q43 0 43 -47v-887q0 -47 -43 -47h-12q-43 0 -43 47z" />
<glyph unicode="&#xed;" horiz-adv-x="401" d="M109 1120q-3 4 24.5 38t85.5 88.5t123 103.5q60 45 92 45q19 0 32 -11.5t13 -29.5q0 -20 -16.5 -40t-61.5 -54q-67 -48 -137 -84t-110 -49t-45 -7zM152 41v887q0 47 43 47h12q43 0 43 -47v-887q0 -47 -43 -47h-12q-43 0 -43 47z" />
<glyph unicode="&#xee;" horiz-adv-x="401" d="M-39 1145q0 22 24 56t81 99q35 40 69 73t51.5 47t20.5 14t20.5 -14t50.5 -46t66 -69q57 -65 82 -101t25 -59q0 -35 -31 -35q-20 0 -47 26t-86 101l-82 109l-82 -111q-51 -69 -80 -97t-49 -28q-14 0 -23.5 9t-9.5 26zM152 41v887q0 47 43 47h12q43 0 43 -47v-887 q0 -47 -43 -47h-12q-43 0 -43 47z" />
<glyph unicode="&#xef;" horiz-adv-x="401" d="M-33 1194q0 36 21 57t55 21q37 0 57.5 -20.5t20.5 -57.5q0 -36 -21 -57t-55 -21q-36 0 -57 21t-21 57zM152 41v887q0 47 43 47h12q43 0 43 -47v-887q0 -47 -43 -47h-12q-43 0 -43 47zM293 1194q0 37 20.5 57.5t55.5 20.5t56 -21t21 -57t-20.5 -57t-54.5 -21 q-37 0 -57.5 20.5t-20.5 57.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1132" d="M86 430q0 202 130 322t343 120q124 0 229 -59.5t154 -142.5q-15 160 -87.5 286.5t-190.5 212.5l-269 -155q-41 -22 -57 10l-4 8q-23 33 20 58l230 127q-177 100 -387 106q-2 3 -3.5 21t19.5 39.5t57 21.5q81 0 194.5 -33.5t219.5 -103.5l234 135q43 24 61 -10l4 -4 q19 -36 -22 -60l-207 -115q130 -107 209 -272t79 -385q0 -272 -131.5 -424.5t-357.5 -152.5q-212 0 -339.5 120.5t-127.5 329.5zM186 432q0 -172 99.5 -269t267.5 -97q193 0 290 134t99 363q-38 87 -144.5 155t-232.5 68q-181 0 -280 -92t-99 -262z" />
<glyph unicode="&#xf1;" horiz-adv-x="1107" d="M141 41v924q0 1 4.5 3.5t12.5 4.5t16 2q64 0 64 -98v-89q52 92 141.5 146.5t206.5 54.5q189 0 289 -116t100 -316v-516q0 -47 -43 -47h-12q-43 0 -43 47v510q0 162 -77 254t-218 92q-119 0 -208 -60.5t-134 -164.5v-631q0 -47 -43 -47h-13q-43 0 -43 47zM268 1139 q6 94 44.5 148t107.5 54q80 0 158 -86q33 -33 53.5 -46t42.5 -13q28 0 43.5 21t25.5 65q6 25 15.5 36t27.5 11t35 -14q-5 -94 -43 -147.5t-108 -53.5q-78 0 -158 84q-33 33 -52.5 46t-41.5 13q-29 0 -44.5 -21t-25.5 -65q-6 -25 -15.5 -36t-27.5 -11q-15 0 -37 15z" />
<glyph unicode="&#xf2;" horiz-adv-x="1142" d="M76 485q0 218 139 361t356 143q215 0 354.5 -144.5t139.5 -363.5q0 -216 -139.5 -358.5t-354.5 -142.5q-217 0 -356 143.5t-139 361.5zM174 485q0 -179 113 -299t284 -120q170 0 283 118.5t113 296.5q0 180 -112.5 301t-283.5 121t-284 -119.5t-113 -298.5zM297 1354 q0 18 12.5 29.5t30.5 11.5q34 0 92 -45q65 -49 123.5 -103.5t86 -88.5t24.5 -38q-5 -6 -44.5 7t-109.5 48.5t-137 84.5q-45 33 -61.5 53t-16.5 41z" />
<glyph unicode="&#xf3;" horiz-adv-x="1142" d="M76 485q0 218 139 361t356 143q215 0 354.5 -144.5t139.5 -363.5q0 -216 -139.5 -358.5t-354.5 -142.5q-217 0 -356 143.5t-139 361.5zM174 485q0 -179 113 -299t284 -120q170 0 283 118.5t113 296.5q0 180 -112.5 301t-283.5 121t-284 -119.5t-113 -298.5zM475 1120 q-3 4 25 38t86 88.5t123 103.5q60 45 92 45q19 0 32 -11.5t13 -29.5q0 -20 -16.5 -40t-61.5 -54q-67 -48 -137.5 -84t-110.5 -49t-45 -7z" />
<glyph unicode="&#xf4;" horiz-adv-x="1142" d="M76 485q0 218 139 361t356 143q215 0 354.5 -144.5t139.5 -363.5q0 -216 -139.5 -358.5t-354.5 -142.5q-217 0 -356 143.5t-139 361.5zM174 485q0 -179 113 -299t284 -120q170 0 283 118.5t113 296.5q0 180 -112.5 301t-283.5 121t-284 -119.5t-113 -298.5zM326 1151 q0 22 24.5 57.5t79.5 98.5q35 40 69 72.5t51.5 46.5t20.5 14t21 -14t51 -46t66 -69q56 -64 81 -100.5t25 -59.5q0 -35 -31 -35q-20 0 -47 26t-86 101l-82 109l-82 -111q-51 -69 -80 -97t-49 -28q-14 0 -23 9t-9 26z" />
<glyph unicode="&#xf5;" horiz-adv-x="1142" d="M76 485q0 218 139 361t356 143q215 0 354.5 -144.5t139.5 -363.5q0 -216 -139.5 -358.5t-354.5 -142.5q-217 0 -356 143.5t-139 361.5zM174 485q0 -179 113 -299t284 -120q170 0 283 118.5t113 296.5q0 180 -112.5 301t-283.5 121t-284 -119.5t-113 -298.5zM295 1139 q6 94 44.5 148t106.5 54q80 0 158 -86q33 -33 53.5 -46t42.5 -13q28 0 44.5 21.5t25.5 64.5q6 25 15.5 36t27.5 11q17 0 35 -14q-5 -94 -43.5 -147.5t-108.5 -53.5q-46 0 -82 22t-75 62q-33 33 -53 46t-42 13q-29 0 -44 -21t-25 -65q-6 -25 -15.5 -36t-27.5 -11 q-14 0 -37 15z" />
<glyph unicode="&#xf6;" horiz-adv-x="1142" d="M76 485q0 218 139 361t356 143q215 0 354.5 -144.5t139.5 -363.5q0 -216 -139.5 -358.5t-354.5 -142.5q-217 0 -356 143.5t-139 361.5zM174 485q0 -179 113 -299t284 -120q170 0 283 118.5t113 296.5q0 180 -112.5 301t-283.5 121t-284 -119.5t-113 -298.5zM332 1194 q0 36 21 57t55 21q37 0 57 -20.5t20 -57.5q0 -36 -20.5 -57t-54.5 -21q-36 0 -57 21t-21 57zM657 1194q0 37 20.5 57.5t55.5 20.5q36 0 57 -21t21 -57t-21 -57t-55 -21q-37 0 -57.5 20.5t-20.5 57.5z" />
<glyph unicode="&#xf7;" horiz-adv-x="1228" d="M184 541v10q0 41 47 41h764q27 0 37 -10t10 -31v-10q0 -41 -47 -41h-764q-47 0 -47 41zM535 238v14q0 34 18.5 53t52.5 19h15q71 0 71 -72v-14q0 -72 -71 -72h-15q-34 0 -52.5 19t-18.5 53zM535 840v14q0 34 18.5 53t52.5 19h15q71 0 71 -72v-14q0 -72 -71 -72h-15 q-34 0 -52.5 19t-18.5 53z" />
<glyph unicode="&#xf8;" horiz-adv-x="1142" d="M84 -20q0 29 35 67l90 98q-123 142 -123 338q0 139 65 255t178 183.5t249 67.5q177 0 305 -106l133 149q3 2 14 -2.5t21 -16.5t10 -26q0 -29 -35 -69l-84 -93q123 -142 123 -340q0 -139 -64.5 -255t-177 -183t-248.5 -67q-179 0 -307 106l-137 -152q-3 -3 -14.5 2 t-22 17.5t-10.5 26.5zM186 485q0 -156 90 -270l543 596q-108 90 -244 90q-169 0 -279 -120t-110 -296zM330 158q111 -90 248 -90q168 0 278.5 119.5t110.5 295.5q0 154 -95 273z" />
<glyph unicode="&#xf9;" horiz-adv-x="1107" d="M133 412v516q0 47 43 47h12q43 0 43 -47v-510q0 -162 77 -254t218 -92q119 0 208 61t134 166v629q0 47 43 47h13q43 0 43 -47v-924q0 -1 -5 -3.5t-13.5 -4.5t-16.5 -2q-62 0 -62 98v88q-52 -91 -142 -145.5t-208 -54.5q-188 0 -287.5 116t-99.5 316zM287 1348 q0 18 12.5 29.5t30.5 11.5q33 0 92 -46q65 -49 123 -103.5t85.5 -88t24.5 -37.5q-5 -6 -44.5 6.5t-109 48.5t-136.5 84q-45 33 -61.5 53.5t-16.5 41.5z" />
<glyph unicode="&#xfa;" horiz-adv-x="1107" d="M133 412v516q0 47 43 47h12q43 0 43 -47v-510q0 -162 77 -254t218 -92q119 0 208 61t134 166v629q0 47 43 47h13q43 0 43 -47v-924q0 -1 -5 -3.5t-13.5 -4.5t-16.5 -2q-62 0 -62 98v88q-52 -91 -142 -145.5t-208 -54.5q-188 0 -287.5 116t-99.5 316zM467 1114 q-3 4 25 37.5t85.5 88t122.5 103.5q62 46 93 46q19 0 32 -11.5t13 -29.5q0 -20 -17 -40.5t-61 -54.5q-67 -48 -137.5 -84t-110.5 -48.5t-45 -6.5z" />
<glyph unicode="&#xfb;" horiz-adv-x="1107" d="M133 412v516q0 47 43 47h12q43 0 43 -47v-510q0 -162 77 -254t218 -92q119 0 208 61t134 166v629q0 47 43 47h13q43 0 43 -47v-924q0 -1 -5 -3.5t-13.5 -4.5t-16.5 -2q-62 0 -62 98v88q-52 -91 -142 -145.5t-208 -54.5q-188 0 -287.5 116t-99.5 316zM311 1145q0 22 24 56 t81 99q35 40 69 73t51.5 47t20.5 14t20.5 -14t50.5 -46t66 -69q57 -65 82 -101t25 -59q0 -35 -31 -35q-20 0 -47 26t-86 101l-82 109l-82 -111q-51 -69 -80 -97t-49 -28q-14 0 -23.5 9t-9.5 26z" />
<glyph unicode="&#xfc;" horiz-adv-x="1107" d="M133 412v516q0 47 43 47h12q43 0 43 -47v-510q0 -162 77 -254t218 -92q119 0 208 61t134 166v629q0 47 43 47h13q43 0 43 -47v-924q0 -1 -5 -3.5t-13.5 -4.5t-16.5 -2q-62 0 -62 98v88q-52 -91 -142 -145.5t-208 -54.5q-188 0 -287.5 116t-99.5 316zM317 1188q0 36 21 57 t55 21q37 0 57.5 -20.5t20.5 -57.5q0 -36 -21 -57t-55 -21q-36 0 -57 21t-21 57zM643 1188q0 37 20.5 57.5t55.5 20.5q36 0 57 -21t21 -57t-21 -57t-55 -21q-37 0 -57.5 20.5t-20.5 57.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="937" d="M29 946q0 14 21 25.5t51 -1t49 -59.5l339 -800l302 794q18 51 48 64.5t51 2t21 -23.5l-499 -1278q-22 -65 -70 -65q-12 0 -25 5.5t-20 11.5t-6 9l172 422q-27 0 -40 8t-22 31zM377 1094q-3 4 25 37.5t85.5 88t122.5 103.5q60 45 92 45q19 0 32.5 -11.5t13.5 -29.5 q0 -20 -16.5 -40t-61.5 -54q-67 -48 -137.5 -84t-110.5 -48.5t-45 -6.5z" />
<glyph unicode="&#xfe;" horiz-adv-x="1173" d="M141 -348v1726q0 3 10 7t23 4q66 0 66 -97v-522q127 219 383 219q196 0 328 -140.5t132 -361.5q0 -224 -131.5 -365.5t-335.5 -141.5q-138 0 -237 63t-139 154v-545q0 -47 -43 -47h-11q-45 0 -45 47zM238 416q0 -146 107.5 -248t262.5 -102q169 0 273 116.5t104 304.5 q0 180 -106.5 298t-266.5 118q-135 0 -231 -70.5t-143 -193.5v-223z" />
<glyph unicode="&#xff;" horiz-adv-x="937" d="M29 946q0 14 21 25.5t51 -1t49 -59.5l339 -800l302 794q18 51 48 64.5t51 2t21 -23.5l-499 -1278q-22 -65 -70 -65q-12 0 -25 5.5t-20 11.5t-6 9l172 422q-27 0 -40 8t-22 31zM233 1194q0 36 21 57t55 21q37 0 57.5 -20.5t20.5 -57.5q0 -36 -21 -57t-55 -21q-36 0 -57 21 t-21 57zM559 1194q0 37 20.5 57.5t55.5 20.5q36 0 57 -21t21 -57t-21 -57t-55 -21q-37 0 -57.5 20.5t-20.5 57.5z" />
<glyph unicode="&#x152;" horiz-adv-x="2082" d="M100 659q0 147 48.5 274t133.5 217t206 141.5t262 51.5q86 0 163 -18h1033q47 0 47 -41v-10q0 -41 -47 -41h-824q118 -83 189.5 -214t83.5 -294h401q47 0 47 -41v-6q0 -43 -47 -43h-399q-6 -175 -78 -315t-197 -226h842q47 0 47 -41v-10q0 -43 -47 -43h-1055 q-77 -18 -164 -18q-286 0 -465.5 189t-179.5 488zM205 662q0 -169 69 -304t194 -211.5t282 -76.5q238 0 390 167t152 425q0 256 -153 424.5t-391 168.5q-157 0 -281.5 -76t-193 -211.5t-68.5 -305.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1947" d="M76 485q0 217 139 360.5t352 143.5q153 0 270 -83t169 -222q48 139 163 222t267 83q195 0 317.5 -131.5t122.5 -341.5v-10q0 -21 -9.5 -28t-33.5 -7h-780q3 -180 112.5 -291.5t282.5 -111.5q243 0 342 194q4 3 17.5 -1.5t26.5 -19.5t13 -36q0 -17 -15.5 -46t-45.5 -59 q-50 -50 -138.5 -85t-199.5 -35q-161 0 -279 80t-168 219q-50 -136 -165.5 -217.5t-268.5 -81.5q-214 0 -352.5 143.5t-138.5 361.5zM174 485q0 -179 111.5 -299t281.5 -120q167 0 278 118.5t111 298.5q0 178 -111.5 299t-277.5 121q-169 0 -281 -119.5t-112 -298.5z M1057 555h717q-6 156 -99 252t-239 96q-147 0 -251.5 -97t-127.5 -251z" />
<glyph unicode="&#x178;" horiz-adv-x="1101" d="M18 1288q-3 14 15.5 29.5t48 13.5t49.5 -35l420 -637l420 635q14 22 31 31t31 6t26 -10.5t18.5 -17t5.5 -15.5l-481 -721v-526q0 -47 -45 -47h-14q-43 0 -43 47v526zM309 1550q0 36 21 57t55 21q37 0 57.5 -20.5t20.5 -57.5q0 -36 -21 -56.5t-55 -20.5q-36 0 -57 20.5 t-21 56.5zM635 1550q0 37 20.5 57.5t55.5 20.5t56 -21t21 -57t-20.5 -56.5t-54.5 -20.5q-37 0 -57.5 20t-20.5 57z" />
<glyph unicode="&#x2c6;" horiz-adv-x="1024" d="M266 1151q0 22 24.5 57t80.5 99q35 40 69 72.5t51.5 46.5t20.5 14t20.5 -14t50.5 -46t66 -69q57 -65 82 -101t25 -59q0 -35 -31 -35q-20 0 -47 26t-86 101l-82 109l-82 -111q-51 -69 -80 -97t-49 -28q-14 0 -23.5 9t-9.5 26z" />
<glyph unicode="&#x2dc;" horiz-adv-x="1024" d="M233 1139q6 94 44.5 148t107.5 54q80 0 158 -86q33 -33 53.5 -46t42.5 -13q28 0 44.5 21.5t25.5 64.5q6 25 15.5 36t27.5 11q16 0 34 -14q-5 -94 -43 -147.5t-108 -53.5q-78 0 -158 84q-33 33 -52.5 46t-41.5 13q-29 0 -44.5 -21t-25.5 -65q-6 -25 -15.5 -36t-27.5 -11 q-14 0 -37 15z" />
<glyph unicode="&#x2000;" horiz-adv-x="898" />
<glyph unicode="&#x2001;" horiz-adv-x="1796" />
<glyph unicode="&#x2002;" horiz-adv-x="898" />
<glyph unicode="&#x2003;" horiz-adv-x="1796" />
<glyph unicode="&#x2004;" horiz-adv-x="598" />
<glyph unicode="&#x2005;" horiz-adv-x="449" />
<glyph unicode="&#x2006;" horiz-adv-x="299" />
<glyph unicode="&#x2007;" horiz-adv-x="299" />
<glyph unicode="&#x2008;" horiz-adv-x="224" />
<glyph unicode="&#x2009;" horiz-adv-x="359" />
<glyph unicode="&#x200a;" horiz-adv-x="99" />
<glyph unicode="&#x2010;" horiz-adv-x="718" d="M86 502v10q0 41 47 41h453q47 0 47 -41v-10q0 -39 -47 -39h-453q-47 0 -47 39z" />
<glyph unicode="&#x2011;" horiz-adv-x="718" d="M86 502v10q0 41 47 41h453q47 0 47 -41v-10q0 -39 -47 -39h-453q-47 0 -47 39z" />
<glyph unicode="&#x2012;" horiz-adv-x="718" d="M86 502v10q0 41 47 41h453q47 0 47 -41v-10q0 -39 -47 -39h-453q-47 0 -47 39z" />
<glyph unicode="&#x2013;" horiz-adv-x="1026" d="M86 504v8q0 41 47 41h760q27 0 37 -10t10 -31v-8q0 -21 -10 -30t-37 -9h-760q-26 0 -36.5 9t-10.5 30z" />
<glyph unicode="&#x2014;" horiz-adv-x="1361" d="M86 504v8q0 41 47 41h1094q27 0 38 -10t11 -31v-8q0 -21 -11 -30t-38 -9h-1094q-26 0 -36.5 9t-10.5 30z" />
<glyph unicode="&#x2018;" horiz-adv-x="393" d="M90 952v6q0 30 25 74l157 305q7 14 33.5 7.5t18.5 -29.5l-119 -361q-12 -30 -24 -42.5t-38 -12.5h-6q-21 0 -34 14.5t-13 38.5z" />
<glyph unicode="&#x2019;" horiz-adv-x="393" d="M70 932l120 358q12 33 22.5 44.5t37.5 11.5h8q19 0 32 -15t13 -39v-6q0 -37 -24 -74l-158 -305q-8 -14 -33.5 -6t-17.5 31z" />
<glyph unicode="&#x201a;" horiz-adv-x="380" d="M35 -252l121 358q12 32 22.5 44t36.5 12h8q20 0 32.5 -14t12.5 -39v-5q0 -36 -24 -75l-158 -303q-7 -15 -33 -8t-18 30z" />
<glyph unicode="&#x201c;" horiz-adv-x="675" d="M90 952v6q0 30 25 74l157 305q7 14 33.5 7.5t18.5 -29.5l-119 -361q-12 -30 -24 -42.5t-36 -12.5h-8q-20 0 -33.5 14.5t-13.5 38.5zM373 952v6q0 34 26 74l156 305q8 14 33.5 7.5t17.5 -29.5l-119 -361q-9 -22 -14 -31t-16 -16.5t-29 -7.5h-8q-20 0 -33.5 14.5 t-13.5 38.5z" />
<glyph unicode="&#x201d;" horiz-adv-x="675" d="M68 932l120 358q12 32 23 44t37 12h8q19 0 32 -15t13 -39v-6q0 -36 -25 -74l-157 -305q-6 -7 -19.5 -8t-25.5 8.5t-6 24.5zM352 932l121 358q12 32 22.5 44t36.5 12h9q19 0 32 -15t13 -39v-6q0 -34 -23 -74l-160 -305q-6 -7 -19 -8t-25 8.5t-7 24.5z" />
<glyph unicode="&#x201e;" horiz-adv-x="665" d="M35 -252l119 358q12 32 22.5 44t36.5 12h8q20 0 32.5 -14t12.5 -39v-5q0 -34 -22 -75l-160 -303q-4 -9 -18.5 -10t-25.5 8t-5 24zM319 -252l121 358q12 33 22.5 44.5t37.5 11.5h8q20 0 32.5 -14t12.5 -39v-5q0 -35 -25 -75l-157 -303q-8 -15 -34 -8t-18 30z" />
<glyph unicode="&#x2022;" horiz-adv-x="614" d="M129 508q0 75 51.5 127.5t126.5 52.5t126.5 -52.5t51.5 -127.5t-51.5 -127.5t-126.5 -52.5t-126.5 52.5t-51.5 127.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="1175" d="M117 61v15q0 34 18.5 52.5t52.5 18.5h15q71 0 71 -71v-15q0 -71 -71 -71h-15q-34 0 -52.5 18.5t-18.5 52.5zM510 61v15q0 34 18 52.5t52 18.5h14q72 0 72 -71v-15q0 -71 -72 -71h-14q-34 0 -52 18.5t-18 52.5zM901 61v15q0 34 18 52.5t52 18.5h16q34 0 53 -18.5t19 -52.5 v-15q0 -34 -19 -52.5t-53 -18.5h-16q-34 0 -52 18.5t-18 52.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="359" />
<glyph unicode="&#x2039;" horiz-adv-x="618" d="M90 496q0 5 20.5 29.5t64 68t95.5 88.5q83 73 131 106t76 33q17 0 28 -11t11 -26q0 -23 -41 -62.5t-137 -111.5l-156 -116q109 -77 160 -119q98 -76 136 -112t38 -58q0 -14 -11 -24.5t-28 -10.5q-28 0 -73.5 31t-131.5 104q-53 46 -96.5 89.5t-64.5 69.5t-21 32z" />
<glyph unicode="&#x203a;" horiz-adv-x="618" d="M104 207q0 41 177 174l155 117q-125 89 -160 116q-95 73 -133.5 111.5t-38.5 60.5q0 14 10 24.5t27 10.5q28 0 73.5 -31t131.5 -104q53 -47 96.5 -90t64.5 -68.5t21 -31.5t-20.5 -31t-63.5 -68t-96 -90q-83 -72 -131 -104.5t-76 -32.5q-17 0 -27 10.5t-10 26.5z" />
<glyph unicode="&#x205f;" horiz-adv-x="449" />
<glyph unicode="&#x20ac;" horiz-adv-x="1280" d="M74 502v8q0 21 11 30t38 9h149q-2 23 -2 69v93q0 48 2 75h-149q-27 0 -38 9t-11 30v9q0 21 11 30.5t38 9.5h162q37 218 169.5 345t329.5 127q112 0 197 -35t137 -90q70 -73 70 -136q0 -20 -14.5 -34.5t-29 -19t-18.5 -1.5q-97 230 -348 230q-152 0 -256.5 -103.5 t-138.5 -282.5h473q26 0 36.5 -9.5t10.5 -30.5v-9q0 -21 -10.5 -30t-36.5 -9h-485q-2 -25 -2 -75v-97q0 -42 2 -65h485q26 0 36.5 -9t10.5 -30v-8q0 -21 -10.5 -30t-36.5 -9h-473q34 -183 140.5 -289t264.5 -106q259 0 349 241q3 4 17.5 -0.5t29 -19.5t14.5 -37 q0 -70 -76 -143q-57 -58 -141 -93.5t-188 -35.5q-208 0 -341.5 127.5t-168.5 355.5h-160q-27 0 -38 9t-11 30z" />
<glyph unicode="&#x2122;" horiz-adv-x="1337" d="M35 1286v6q0 35 37 35h450q37 0 37 -35v-6q0 -33 -37 -33h-184v-514q0 -39 -37 -39h-10q-35 0 -35 39v514h-184q-37 0 -37 33zM664 739v547q0 45 41 45h36q19 0 29 -8t18 -27l164 -344l162 344q9 20 18.5 27.5t28.5 7.5h35q41 0 41 -45v-547q0 -39 -35 -39h-10 q-33 0 -33 39v484l-164 -353q-5 -12 -13 -17t-32 -5q-17 0 -25.5 3t-11 6.5t-6.5 12.5l-166 351v-482q0 -39 -30 -39h-15q-32 0 -32 39z" />
<glyph unicode="&#x25fc;" horiz-adv-x="972" d="M0 0v973h973v-973h-973z" />
<hkern u1="&#x26;" u2="&#x178;" k="61" />
<hkern u1="&#x26;" u2="&#xdd;" k="61" />
<hkern u1="&#x26;" u2="&#xc6;" k="-82" />
<hkern u1="&#x26;" u2="&#xc5;" k="-82" />
<hkern u1="&#x26;" u2="&#xc4;" k="-82" />
<hkern u1="&#x26;" u2="&#xc3;" k="-82" />
<hkern u1="&#x26;" u2="&#xc2;" k="-82" />
<hkern u1="&#x26;" u2="&#xc1;" k="-82" />
<hkern u1="&#x26;" u2="&#xc0;" k="-82" />
<hkern u1="&#x26;" u2="Y" k="61" />
<hkern u1="&#x26;" u2="X" k="-41" />
<hkern u1="&#x26;" u2="W" k="41" />
<hkern u1="&#x26;" u2="V" k="41" />
<hkern u1="&#x26;" u2="T" k="82" />
<hkern u1="&#x26;" u2="J" k="-41" />
<hkern u1="&#x26;" u2="A" k="-82" />
<hkern u1="&#x28;" u2="&#x178;" k="-102" />
<hkern u1="&#x28;" u2="&#xdd;" k="-102" />
<hkern u1="&#x28;" u2="j" k="-307" />
<hkern u1="&#x28;" u2="g" k="-41" />
<hkern u1="&#x28;" u2="Y" k="-102" />
<hkern u1="&#x28;" u2="X" k="-82" />
<hkern u1="&#x28;" u2="W" k="-20" />
<hkern u1="&#x28;" u2="V" k="-61" />
<hkern u1="&#x2a;" u2="&#xc6;" k="123" />
<hkern u1="&#x2a;" u2="&#xc5;" k="123" />
<hkern u1="&#x2a;" u2="&#xc4;" k="123" />
<hkern u1="&#x2a;" u2="&#xc3;" k="123" />
<hkern u1="&#x2a;" u2="&#xc2;" k="123" />
<hkern u1="&#x2a;" u2="&#xc1;" k="123" />
<hkern u1="&#x2a;" u2="&#xc0;" k="123" />
<hkern u1="&#x2a;" u2="A" k="123" />
<hkern u1="&#x2c;" u2="&#x178;" k="102" />
<hkern u1="&#x2c;" u2="&#x153;" k="41" />
<hkern u1="&#x2c;" u2="&#x152;" k="82" />
<hkern u1="&#x2c;" u2="&#xe7;" k="41" />
<hkern u1="&#x2c;" u2="&#xe6;" k="41" />
<hkern u1="&#x2c;" u2="&#xdd;" k="102" />
<hkern u1="&#x2c;" u2="&#xdc;" k="20" />
<hkern u1="&#x2c;" u2="&#xdb;" k="20" />
<hkern u1="&#x2c;" u2="&#xda;" k="20" />
<hkern u1="&#x2c;" u2="&#xd9;" k="20" />
<hkern u1="&#x2c;" u2="&#xd8;" k="82" />
<hkern u1="&#x2c;" u2="&#xd6;" k="82" />
<hkern u1="&#x2c;" u2="&#xd5;" k="82" />
<hkern u1="&#x2c;" u2="&#xd4;" k="82" />
<hkern u1="&#x2c;" u2="&#xd3;" k="82" />
<hkern u1="&#x2c;" u2="&#xd2;" k="82" />
<hkern u1="&#x2c;" u2="&#xc6;" k="-61" />
<hkern u1="&#x2c;" u2="&#xc5;" k="-61" />
<hkern u1="&#x2c;" u2="&#xc4;" k="-61" />
<hkern u1="&#x2c;" u2="&#xc3;" k="-61" />
<hkern u1="&#x2c;" u2="&#xc2;" k="-61" />
<hkern u1="&#x2c;" u2="&#xc1;" k="-61" />
<hkern u1="&#x2c;" u2="&#xc0;" k="-61" />
<hkern u1="&#x2c;" u2="y" k="61" />
<hkern u1="&#x2c;" u2="w" k="20" />
<hkern u1="&#x2c;" u2="v" k="61" />
<hkern u1="&#x2c;" u2="u" k="41" />
<hkern u1="&#x2c;" u2="t" k="82" />
<hkern u1="&#x2c;" u2="r" k="41" />
<hkern u1="&#x2c;" u2="q" k="41" />
<hkern u1="&#x2c;" u2="p" k="41" />
<hkern u1="&#x2c;" u2="o" k="41" />
<hkern u1="&#x2c;" u2="n" k="41" />
<hkern u1="&#x2c;" u2="m" k="41" />
<hkern u1="&#x2c;" u2="e" k="41" />
<hkern u1="&#x2c;" u2="d" k="41" />
<hkern u1="&#x2c;" u2="c" k="41" />
<hkern u1="&#x2c;" u2="a" k="41" />
<hkern u1="&#x2c;" u2="Y" k="102" />
<hkern u1="&#x2c;" u2="W" k="41" />
<hkern u1="&#x2c;" u2="V" k="123" />
<hkern u1="&#x2c;" u2="U" k="20" />
<hkern u1="&#x2c;" u2="T" k="123" />
<hkern u1="&#x2c;" u2="Q" k="82" />
<hkern u1="&#x2c;" u2="O" k="82" />
<hkern u1="&#x2c;" u2="G" k="82" />
<hkern u1="&#x2c;" u2="C" k="82" />
<hkern u1="&#x2c;" u2="A" k="-61" />
<hkern u1="&#x2c;" u2="&#x39;" k="20" />
<hkern u1="&#x2c;" u2="&#x38;" k="31" />
<hkern u1="&#x2c;" u2="&#x36;" k="61" />
<hkern u1="&#x2c;" u2="&#x34;" k="102" />
<hkern u1="&#x2c;" u2="&#x31;" k="123" />
<hkern u1="&#x2c;" u2="&#x30;" k="82" />
<hkern u1="&#x2d;" u2="&#x178;" k="102" />
<hkern u1="&#x2d;" u2="&#x153;" k="20" />
<hkern u1="&#x2d;" u2="&#xe7;" k="20" />
<hkern u1="&#x2d;" u2="&#xe6;" k="20" />
<hkern u1="&#x2d;" u2="&#xdd;" k="102" />
<hkern u1="&#x2d;" u2="&#xc6;" k="-20" />
<hkern u1="&#x2d;" u2="&#xc5;" k="-20" />
<hkern u1="&#x2d;" u2="&#xc4;" k="-20" />
<hkern u1="&#x2d;" u2="&#xc3;" k="-20" />
<hkern u1="&#x2d;" u2="&#xc2;" k="-20" />
<hkern u1="&#x2d;" u2="&#xc1;" k="-20" />
<hkern u1="&#x2d;" u2="&#xc0;" k="-20" />
<hkern u1="&#x2d;" u2="z" k="61" />
<hkern u1="&#x2d;" u2="y" k="20" />
<hkern u1="&#x2d;" u2="x" k="61" />
<hkern u1="&#x2d;" u2="v" k="20" />
<hkern u1="&#x2d;" u2="q" k="20" />
<hkern u1="&#x2d;" u2="o" k="20" />
<hkern u1="&#x2d;" u2="e" k="20" />
<hkern u1="&#x2d;" u2="d" k="20" />
<hkern u1="&#x2d;" u2="c" k="20" />
<hkern u1="&#x2d;" u2="a" k="20" />
<hkern u1="&#x2d;" u2="Z" k="20" />
<hkern u1="&#x2d;" u2="Y" k="102" />
<hkern u1="&#x2d;" u2="X" k="61" />
<hkern u1="&#x2d;" u2="W" k="82" />
<hkern u1="&#x2d;" u2="V" k="102" />
<hkern u1="&#x2d;" u2="T" k="143" />
<hkern u1="&#x2d;" u2="A" k="-20" />
<hkern u1="&#x2d;" u2="&#x37;" k="61" />
<hkern u1="&#x2d;" u2="&#x31;" k="41" />
<hkern u1="&#x2e;" u2="&#x178;" k="102" />
<hkern u1="&#x2e;" u2="&#x153;" k="41" />
<hkern u1="&#x2e;" u2="&#x152;" k="82" />
<hkern u1="&#x2e;" u2="&#xe7;" k="41" />
<hkern u1="&#x2e;" u2="&#xe6;" k="41" />
<hkern u1="&#x2e;" u2="&#xdd;" k="102" />
<hkern u1="&#x2e;" u2="&#xdc;" k="20" />
<hkern u1="&#x2e;" u2="&#xdb;" k="20" />
<hkern u1="&#x2e;" u2="&#xda;" k="20" />
<hkern u1="&#x2e;" u2="&#xd9;" k="20" />
<hkern u1="&#x2e;" u2="&#xd8;" k="82" />
<hkern u1="&#x2e;" u2="&#xd6;" k="82" />
<hkern u1="&#x2e;" u2="&#xd5;" k="82" />
<hkern u1="&#x2e;" u2="&#xd4;" k="82" />
<hkern u1="&#x2e;" u2="&#xd3;" k="82" />
<hkern u1="&#x2e;" u2="&#xd2;" k="82" />
<hkern u1="&#x2e;" u2="&#xc6;" k="-61" />
<hkern u1="&#x2e;" u2="&#xc5;" k="-61" />
<hkern u1="&#x2e;" u2="&#xc4;" k="-61" />
<hkern u1="&#x2e;" u2="&#xc3;" k="-61" />
<hkern u1="&#x2e;" u2="&#xc2;" k="-61" />
<hkern u1="&#x2e;" u2="&#xc1;" k="-61" />
<hkern u1="&#x2e;" u2="&#xc0;" k="-61" />
<hkern u1="&#x2e;" u2="y" k="61" />
<hkern u1="&#x2e;" u2="w" k="20" />
<hkern u1="&#x2e;" u2="v" k="61" />
<hkern u1="&#x2e;" u2="u" k="41" />
<hkern u1="&#x2e;" u2="t" k="82" />
<hkern u1="&#x2e;" u2="r" k="41" />
<hkern u1="&#x2e;" u2="q" k="41" />
<hkern u1="&#x2e;" u2="p" k="41" />
<hkern u1="&#x2e;" u2="o" k="41" />
<hkern u1="&#x2e;" u2="n" k="41" />
<hkern u1="&#x2e;" u2="m" k="41" />
<hkern u1="&#x2e;" u2="e" k="41" />
<hkern u1="&#x2e;" u2="d" k="41" />
<hkern u1="&#x2e;" u2="c" k="41" />
<hkern u1="&#x2e;" u2="a" k="41" />
<hkern u1="&#x2e;" u2="Y" k="102" />
<hkern u1="&#x2e;" u2="W" k="41" />
<hkern u1="&#x2e;" u2="V" k="123" />
<hkern u1="&#x2e;" u2="U" k="20" />
<hkern u1="&#x2e;" u2="T" k="123" />
<hkern u1="&#x2e;" u2="Q" k="82" />
<hkern u1="&#x2e;" u2="O" k="82" />
<hkern u1="&#x2e;" u2="G" k="82" />
<hkern u1="&#x2e;" u2="C" k="82" />
<hkern u1="&#x2e;" u2="A" k="-61" />
<hkern u1="&#x2e;" u2="&#x39;" k="20" />
<hkern u1="&#x2e;" u2="&#x38;" k="31" />
<hkern u1="&#x2e;" u2="&#x36;" k="61" />
<hkern u1="&#x2e;" u2="&#x34;" k="102" />
<hkern u1="&#x2e;" u2="&#x31;" k="123" />
<hkern u1="&#x2e;" u2="&#x30;" k="82" />
<hkern u1="&#x2f;" u2="&#x153;" k="20" />
<hkern u1="&#x2f;" u2="&#x152;" k="-20" />
<hkern u1="&#x2f;" u2="&#xe7;" k="20" />
<hkern u1="&#x2f;" u2="&#xe6;" k="20" />
<hkern u1="&#x2f;" u2="&#xd8;" k="-20" />
<hkern u1="&#x2f;" u2="&#xd6;" k="-20" />
<hkern u1="&#x2f;" u2="&#xd5;" k="-20" />
<hkern u1="&#x2f;" u2="&#xd4;" k="-20" />
<hkern u1="&#x2f;" u2="&#xd3;" k="-20" />
<hkern u1="&#x2f;" u2="&#xd2;" k="-20" />
<hkern u1="&#x2f;" u2="q" k="20" />
<hkern u1="&#x2f;" u2="o" k="20" />
<hkern u1="&#x2f;" u2="g" k="20" />
<hkern u1="&#x2f;" u2="e" k="20" />
<hkern u1="&#x2f;" u2="d" k="20" />
<hkern u1="&#x2f;" u2="c" k="20" />
<hkern u1="&#x2f;" u2="a" k="20" />
<hkern u1="&#x2f;" u2="Q" k="-20" />
<hkern u1="&#x2f;" u2="O" k="-20" />
<hkern u1="&#x2f;" u2="G" k="-20" />
<hkern u1="&#x2f;" u2="C" k="-20" />
<hkern u1="&#x2f;" u2="&#x34;" k="41" />
<hkern u1="&#x2f;" u2="&#x31;" k="-41" />
<hkern u1="&#x30;" u2="&#x2026;" k="82" />
<hkern u1="&#x30;" u2="&#x201e;" k="82" />
<hkern u1="&#x30;" u2="&#x201a;" k="82" />
<hkern u1="&#x30;" u2="&#x37;" k="41" />
<hkern u1="&#x30;" u2="&#x2e;" k="82" />
<hkern u1="&#x30;" u2="&#x2c;" k="82" />
<hkern u1="&#x32;" u2="&#x2014;" k="20" />
<hkern u1="&#x32;" u2="&#x2013;" k="20" />
<hkern u1="&#x32;" u2="&#x34;" k="61" />
<hkern u1="&#x32;" u2="&#x2d;" k="20" />
<hkern u1="&#x33;" u2="&#x2026;" k="41" />
<hkern u1="&#x33;" u2="&#x201e;" k="41" />
<hkern u1="&#x33;" u2="&#x201a;" k="41" />
<hkern u1="&#x33;" u2="&#x37;" k="41" />
<hkern u1="&#x33;" u2="&#x2e;" k="41" />
<hkern u1="&#x33;" u2="&#x2c;" k="41" />
<hkern u1="&#x34;" u2="&#x2122;" k="102" />
<hkern u1="&#x34;" u2="&#xb0;" k="82" />
<hkern u1="&#x34;" u2="&#x37;" k="20" />
<hkern u1="&#x35;" u2="&#x2026;" k="20" />
<hkern u1="&#x35;" u2="&#x201e;" k="20" />
<hkern u1="&#x35;" u2="&#x201a;" k="20" />
<hkern u1="&#x35;" u2="&#x37;" k="20" />
<hkern u1="&#x35;" u2="&#x2e;" k="20" />
<hkern u1="&#x35;" u2="&#x2c;" k="20" />
<hkern u1="&#x36;" u2="&#x2026;" k="20" />
<hkern u1="&#x36;" u2="&#x201e;" k="20" />
<hkern u1="&#x36;" u2="&#x201a;" k="20" />
<hkern u1="&#x36;" u2="&#x37;" k="20" />
<hkern u1="&#x36;" u2="&#x2e;" k="20" />
<hkern u1="&#x36;" u2="&#x2c;" k="20" />
<hkern u1="&#x37;" u2="&#x2026;" k="164" />
<hkern u1="&#x37;" u2="&#x201e;" k="164" />
<hkern u1="&#x37;" u2="&#x201a;" k="164" />
<hkern u1="&#x37;" u2="&#x2014;" k="82" />
<hkern u1="&#x37;" u2="&#x2013;" k="82" />
<hkern u1="&#x37;" u2="&#xa2;" k="82" />
<hkern u1="&#x37;" u2="&#x3b;" k="41" />
<hkern u1="&#x37;" u2="&#x3a;" k="41" />
<hkern u1="&#x37;" u2="&#x39;" k="-20" />
<hkern u1="&#x37;" u2="&#x35;" k="-31" />
<hkern u1="&#x37;" u2="&#x34;" k="123" />
<hkern u1="&#x37;" u2="&#x33;" k="-41" />
<hkern u1="&#x37;" u2="&#x31;" k="-41" />
<hkern u1="&#x37;" u2="&#x30;" k="20" />
<hkern u1="&#x37;" u2="&#x2e;" k="164" />
<hkern u1="&#x37;" u2="&#x2d;" k="82" />
<hkern u1="&#x37;" u2="&#x2c;" k="164" />
<hkern u1="&#x38;" u2="&#x2026;" k="31" />
<hkern u1="&#x38;" u2="&#x201e;" k="31" />
<hkern u1="&#x38;" u2="&#x201a;" k="31" />
<hkern u1="&#x38;" u2="&#x37;" k="31" />
<hkern u1="&#x38;" u2="&#x2e;" k="31" />
<hkern u1="&#x38;" u2="&#x2c;" k="31" />
<hkern u1="&#x39;" u2="&#x2026;" k="61" />
<hkern u1="&#x39;" u2="&#x201e;" k="61" />
<hkern u1="&#x39;" u2="&#x201a;" k="61" />
<hkern u1="&#x39;" u2="&#x37;" k="92" />
<hkern u1="&#x39;" u2="&#x2e;" k="61" />
<hkern u1="&#x39;" u2="&#x2c;" k="61" />
<hkern u1="&#x3a;" u2="&#x178;" k="61" />
<hkern u1="&#x3a;" u2="&#x153;" k="20" />
<hkern u1="&#x3a;" u2="&#xe7;" k="20" />
<hkern u1="&#x3a;" u2="&#xe6;" k="20" />
<hkern u1="&#x3a;" u2="&#xdd;" k="61" />
<hkern u1="&#x3a;" u2="q" k="20" />
<hkern u1="&#x3a;" u2="o" k="20" />
<hkern u1="&#x3a;" u2="e" k="20" />
<hkern u1="&#x3a;" u2="d" k="20" />
<hkern u1="&#x3a;" u2="c" k="20" />
<hkern u1="&#x3a;" u2="a" k="20" />
<hkern u1="&#x3a;" u2="Y" k="61" />
<hkern u1="&#x3a;" u2="W" k="41" />
<hkern u1="&#x3a;" u2="V" k="41" />
<hkern u1="&#x3a;" u2="T" k="102" />
<hkern u1="&#x3a;" u2="&#x37;" k="41" />
<hkern u1="&#x3a;" u2="&#x31;" k="61" />
<hkern u1="&#x3b;" u2="&#x178;" k="61" />
<hkern u1="&#x3b;" u2="&#x153;" k="20" />
<hkern u1="&#x3b;" u2="&#xe7;" k="20" />
<hkern u1="&#x3b;" u2="&#xe6;" k="20" />
<hkern u1="&#x3b;" u2="&#xdd;" k="61" />
<hkern u1="&#x3b;" u2="q" k="20" />
<hkern u1="&#x3b;" u2="o" k="20" />
<hkern u1="&#x3b;" u2="e" k="20" />
<hkern u1="&#x3b;" u2="d" k="20" />
<hkern u1="&#x3b;" u2="c" k="20" />
<hkern u1="&#x3b;" u2="a" k="20" />
<hkern u1="&#x3b;" u2="Y" k="61" />
<hkern u1="&#x3b;" u2="W" k="41" />
<hkern u1="&#x3b;" u2="V" k="41" />
<hkern u1="&#x3b;" u2="T" k="102" />
<hkern u1="&#x3b;" u2="&#x37;" k="41" />
<hkern u1="&#x3b;" u2="&#x31;" k="61" />
<hkern u1="&#x3e;" u2="&#x37;" k="123" />
<hkern u1="&#x40;" u2="&#x178;" k="61" />
<hkern u1="&#x40;" u2="&#xdd;" k="61" />
<hkern u1="&#x40;" u2="&#xc6;" k="20" />
<hkern u1="&#x40;" u2="&#xc5;" k="20" />
<hkern u1="&#x40;" u2="&#xc4;" k="20" />
<hkern u1="&#x40;" u2="&#xc3;" k="20" />
<hkern u1="&#x40;" u2="&#xc2;" k="20" />
<hkern u1="&#x40;" u2="&#xc1;" k="20" />
<hkern u1="&#x40;" u2="&#xc0;" k="20" />
<hkern u1="&#x40;" u2="Y" k="61" />
<hkern u1="&#x40;" u2="W" k="41" />
<hkern u1="&#x40;" u2="T" k="61" />
<hkern u1="&#x40;" u2="A" k="20" />
<hkern u1="A" u2="&#x2122;" k="143" />
<hkern u1="A" u2="&#x2026;" k="-61" />
<hkern u1="A" u2="&#x201e;" k="-61" />
<hkern u1="A" u2="&#x201d;" k="61" />
<hkern u1="A" u2="&#x201c;" k="123" />
<hkern u1="A" u2="&#x201a;" k="-61" />
<hkern u1="A" u2="&#x2019;" k="61" />
<hkern u1="A" u2="&#x2018;" k="123" />
<hkern u1="A" u2="&#x2014;" k="-20" />
<hkern u1="A" u2="&#x2013;" k="-20" />
<hkern u1="A" u2="&#x178;" k="78" />
<hkern u1="A" u2="&#x153;" k="8" />
<hkern u1="A" u2="&#x152;" k="35" />
<hkern u1="A" u2="&#xe7;" k="8" />
<hkern u1="A" u2="&#xe6;" k="8" />
<hkern u1="A" u2="&#xdd;" k="78" />
<hkern u1="A" u2="&#xd8;" k="35" />
<hkern u1="A" u2="&#xd6;" k="35" />
<hkern u1="A" u2="&#xd5;" k="35" />
<hkern u1="A" u2="&#xd4;" k="35" />
<hkern u1="A" u2="&#xd3;" k="35" />
<hkern u1="A" u2="&#xd2;" k="35" />
<hkern u1="A" u2="&#xc6;" k="-27" />
<hkern u1="A" u2="&#xc5;" k="-27" />
<hkern u1="A" u2="&#xc4;" k="-27" />
<hkern u1="A" u2="&#xc3;" k="-27" />
<hkern u1="A" u2="&#xc2;" k="-27" />
<hkern u1="A" u2="&#xc1;" k="-27" />
<hkern u1="A" u2="&#xc0;" k="-27" />
<hkern u1="A" u2="&#xae;" k="20" />
<hkern u1="A" u2="&#xa9;" k="20" />
<hkern u1="A" u2="z" k="-20" />
<hkern u1="A" u2="y" k="61" />
<hkern u1="A" u2="x" k="-41" />
<hkern u1="A" u2="w" k="20" />
<hkern u1="A" u2="v" k="61" />
<hkern u1="A" u2="u" k="10" />
<hkern u1="A" u2="t" k="10" />
<hkern u1="A" u2="r" k="10" />
<hkern u1="A" u2="q" k="8" />
<hkern u1="A" u2="p" k="10" />
<hkern u1="A" u2="o" k="8" />
<hkern u1="A" u2="n" k="10" />
<hkern u1="A" u2="m" k="10" />
<hkern u1="A" u2="e" k="8" />
<hkern u1="A" u2="d" k="8" />
<hkern u1="A" u2="c" k="8" />
<hkern u1="A" u2="a" k="-10" />
<hkern u1="A" u2="Z" k="-6" />
<hkern u1="A" u2="Y" k="78" />
<hkern u1="A" u2="X" k="-27" />
<hkern u1="A" u2="W" k="90" />
<hkern u1="A" u2="V" k="90" />
<hkern u1="A" u2="T" k="131" />
<hkern u1="A" u2="S" k="-10" />
<hkern u1="A" u2="Q" k="35" />
<hkern u1="A" u2="O" k="35" />
<hkern u1="A" u2="J" k="-20" />
<hkern u1="A" u2="G" k="35" />
<hkern u1="A" u2="C" k="35" />
<hkern u1="A" u2="A" k="-27" />
<hkern u1="A" u2="&#x40;" k="20" />
<hkern u1="A" u2="&#x2e;" k="-61" />
<hkern u1="A" u2="&#x2d;" k="-20" />
<hkern u1="A" u2="&#x2c;" k="-61" />
<hkern u1="A" u2="&#x2a;" k="123" />
<hkern u1="B" u2="W" k="41" />
<hkern u1="B" u2="V" k="10" />
<hkern u1="C" u2="&#x178;" k="-41" />
<hkern u1="C" u2="&#x153;" k="-10" />
<hkern u1="C" u2="&#xe7;" k="-10" />
<hkern u1="C" u2="&#xe6;" k="-10" />
<hkern u1="C" u2="&#xdd;" k="-41" />
<hkern u1="C" u2="z" k="20" />
<hkern u1="C" u2="y" k="10" />
<hkern u1="C" u2="w" k="10" />
<hkern u1="C" u2="v" k="10" />
<hkern u1="C" u2="t" k="10" />
<hkern u1="C" u2="q" k="-10" />
<hkern u1="C" u2="o" k="-10" />
<hkern u1="C" u2="e" k="-10" />
<hkern u1="C" u2="d" k="-10" />
<hkern u1="C" u2="c" k="-10" />
<hkern u1="C" u2="a" k="-10" />
<hkern u1="C" u2="Y" k="-41" />
<hkern u1="C" u2="X" k="-20" />
<hkern u1="C" u2="V" k="-41" />
<hkern u1="D" u2="&#x2026;" k="82" />
<hkern u1="D" u2="&#x201e;" k="82" />
<hkern u1="D" u2="&#x201c;" k="41" />
<hkern u1="D" u2="&#x201a;" k="82" />
<hkern u1="D" u2="&#x2018;" k="41" />
<hkern u1="D" u2="&#x178;" k="51" />
<hkern u1="D" u2="&#x153;" k="10" />
<hkern u1="D" u2="&#xe7;" k="10" />
<hkern u1="D" u2="&#xe6;" k="10" />
<hkern u1="D" u2="&#xdd;" k="51" />
<hkern u1="D" u2="&#xc6;" k="35" />
<hkern u1="D" u2="&#xc5;" k="35" />
<hkern u1="D" u2="&#xc4;" k="35" />
<hkern u1="D" u2="&#xc3;" k="35" />
<hkern u1="D" u2="&#xc2;" k="35" />
<hkern u1="D" u2="&#xc1;" k="35" />
<hkern u1="D" u2="&#xc0;" k="35" />
<hkern u1="D" u2="z" k="20" />
<hkern u1="D" u2="y" k="-10" />
<hkern u1="D" u2="x" k="18" />
<hkern u1="D" u2="v" k="-10" />
<hkern u1="D" u2="u" k="10" />
<hkern u1="D" u2="r" k="10" />
<hkern u1="D" u2="q" k="10" />
<hkern u1="D" u2="p" k="10" />
<hkern u1="D" u2="o" k="10" />
<hkern u1="D" u2="n" k="10" />
<hkern u1="D" u2="m" k="10" />
<hkern u1="D" u2="l" k="20" />
<hkern u1="D" u2="k" k="20" />
<hkern u1="D" u2="h" k="20" />
<hkern u1="D" u2="e" k="10" />
<hkern u1="D" u2="d" k="10" />
<hkern u1="D" u2="c" k="10" />
<hkern u1="D" u2="b" k="20" />
<hkern u1="D" u2="a" k="10" />
<hkern u1="D" u2="Z" k="92" />
<hkern u1="D" u2="Y" k="51" />
<hkern u1="D" u2="X" k="51" />
<hkern u1="D" u2="W" k="68" />
<hkern u1="D" u2="V" k="39" />
<hkern u1="D" u2="T" k="66" />
<hkern u1="D" u2="J" k="92" />
<hkern u1="D" u2="A" k="35" />
<hkern u1="D" u2="&#x3f;" k="41" />
<hkern u1="D" u2="&#x2f;" k="102" />
<hkern u1="D" u2="&#x2e;" k="82" />
<hkern u1="D" u2="&#x2c;" k="82" />
<hkern u1="E" u2="&#x153;" k="51" />
<hkern u1="E" u2="&#x152;" k="12" />
<hkern u1="E" u2="&#xe7;" k="51" />
<hkern u1="E" u2="&#xe6;" k="51" />
<hkern u1="E" u2="&#xd8;" k="12" />
<hkern u1="E" u2="&#xd6;" k="12" />
<hkern u1="E" u2="&#xd5;" k="12" />
<hkern u1="E" u2="&#xd4;" k="12" />
<hkern u1="E" u2="&#xd3;" k="12" />
<hkern u1="E" u2="&#xd2;" k="12" />
<hkern u1="E" u2="&#xae;" k="41" />
<hkern u1="E" u2="&#xa9;" k="41" />
<hkern u1="E" u2="y" k="41" />
<hkern u1="E" u2="v" k="41" />
<hkern u1="E" u2="q" k="51" />
<hkern u1="E" u2="o" k="51" />
<hkern u1="E" u2="g" k="10" />
<hkern u1="E" u2="f" k="20" />
<hkern u1="E" u2="e" k="51" />
<hkern u1="E" u2="d" k="51" />
<hkern u1="E" u2="c" k="51" />
<hkern u1="E" u2="a" k="51" />
<hkern u1="E" u2="T" k="-31" />
<hkern u1="E" u2="Q" k="12" />
<hkern u1="E" u2="O" k="12" />
<hkern u1="E" u2="G" k="12" />
<hkern u1="E" u2="C" k="12" />
<hkern u1="E" u2="&#x40;" k="41" />
<hkern u1="F" u2="&#x2026;" k="184" />
<hkern u1="F" u2="&#x201e;" k="184" />
<hkern u1="F" u2="&#x201a;" k="184" />
<hkern u1="F" u2="&#x2014;" k="102" />
<hkern u1="F" u2="&#x2013;" k="102" />
<hkern u1="F" u2="&#x178;" k="-51" />
<hkern u1="F" u2="&#x153;" k="109" />
<hkern u1="F" u2="&#xe7;" k="109" />
<hkern u1="F" u2="&#xe6;" k="109" />
<hkern u1="F" u2="&#xdd;" k="-51" />
<hkern u1="F" u2="&#xc6;" k="102" />
<hkern u1="F" u2="&#xc5;" k="102" />
<hkern u1="F" u2="&#xc4;" k="102" />
<hkern u1="F" u2="&#xc3;" k="102" />
<hkern u1="F" u2="&#xc2;" k="102" />
<hkern u1="F" u2="&#xc1;" k="102" />
<hkern u1="F" u2="&#xc0;" k="102" />
<hkern u1="F" u2="&#xae;" k="41" />
<hkern u1="F" u2="&#xa9;" k="41" />
<hkern u1="F" u2="z" k="82" />
<hkern u1="F" u2="y" k="41" />
<hkern u1="F" u2="x" k="82" />
<hkern u1="F" u2="w" k="41" />
<hkern u1="F" u2="v" k="41" />
<hkern u1="F" u2="u" k="82" />
<hkern u1="F" u2="t" k="41" />
<hkern u1="F" u2="s" k="82" />
<hkern u1="F" u2="r" k="82" />
<hkern u1="F" u2="q" k="109" />
<hkern u1="F" u2="p" k="82" />
<hkern u1="F" u2="o" k="109" />
<hkern u1="F" u2="n" k="82" />
<hkern u1="F" u2="m" k="82" />
<hkern u1="F" u2="l" k="20" />
<hkern u1="F" u2="k" k="20" />
<hkern u1="F" u2="j" k="20" />
<hkern u1="F" u2="i" k="20" />
<hkern u1="F" u2="h" k="20" />
<hkern u1="F" u2="g" k="61" />
<hkern u1="F" u2="f" k="41" />
<hkern u1="F" u2="e" k="109" />
<hkern u1="F" u2="d" k="109" />
<hkern u1="F" u2="c" k="109" />
<hkern u1="F" u2="b" k="20" />
<hkern u1="F" u2="a" k="109" />
<hkern u1="F" u2="Y" k="-51" />
<hkern u1="F" u2="X" k="-20" />
<hkern u1="F" u2="W" k="-10" />
<hkern u1="F" u2="V" k="-41" />
<hkern u1="F" u2="T" k="-61" />
<hkern u1="F" u2="J" k="184" />
<hkern u1="F" u2="A" k="102" />
<hkern u1="F" u2="&#x40;" k="41" />
<hkern u1="F" u2="&#x3b;" k="61" />
<hkern u1="F" u2="&#x3a;" k="61" />
<hkern u1="F" u2="&#x2f;" k="164" />
<hkern u1="F" u2="&#x2e;" k="184" />
<hkern u1="F" u2="&#x2d;" k="102" />
<hkern u1="F" u2="&#x2c;" k="184" />
<hkern u1="F" u2="&#x26;" k="82" />
<hkern u1="G" u2="&#x2122;" k="61" />
<hkern u1="G" u2="&#x2026;" k="-41" />
<hkern u1="G" u2="&#x201e;" k="-41" />
<hkern u1="G" u2="&#x201a;" k="-41" />
<hkern u1="G" u2="&#x178;" k="25" />
<hkern u1="G" u2="&#xdd;" k="25" />
<hkern u1="G" u2="y" k="20" />
<hkern u1="G" u2="v" k="20" />
<hkern u1="G" u2="t" k="20" />
<hkern u1="G" u2="Z" k="-4" />
<hkern u1="G" u2="Y" k="25" />
<hkern u1="G" u2="W" k="25" />
<hkern u1="G" u2="V" k="14" />
<hkern u1="G" u2="T" k="51" />
<hkern u1="G" u2="&#x2e;" k="-41" />
<hkern u1="G" u2="&#x2c;" k="-41" />
<hkern u1="H" u2="y" k="20" />
<hkern u1="H" u2="v" k="20" />
<hkern u1="H" u2="&#x2f;" k="41" />
<hkern u1="I" u2="y" k="20" />
<hkern u1="I" u2="v" k="20" />
<hkern u1="I" u2="&#x2f;" k="41" />
<hkern u1="J" u2="&#x2026;" k="20" />
<hkern u1="J" u2="&#x201e;" k="20" />
<hkern u1="J" u2="&#x201a;" k="20" />
<hkern u1="J" u2="J" k="20" />
<hkern u1="J" u2="&#x2e;" k="20" />
<hkern u1="J" u2="&#x2c;" k="20" />
<hkern u1="K" u2="&#x2014;" k="82" />
<hkern u1="K" u2="&#x2013;" k="82" />
<hkern u1="K" u2="&#x178;" k="-29" />
<hkern u1="K" u2="&#x153;" k="47" />
<hkern u1="K" u2="&#x152;" k="61" />
<hkern u1="K" u2="&#xf0;" k="41" />
<hkern u1="K" u2="&#xe7;" k="47" />
<hkern u1="K" u2="&#xe6;" k="47" />
<hkern u1="K" u2="&#xdd;" k="-29" />
<hkern u1="K" u2="&#xdc;" k="20" />
<hkern u1="K" u2="&#xdb;" k="20" />
<hkern u1="K" u2="&#xda;" k="20" />
<hkern u1="K" u2="&#xd9;" k="20" />
<hkern u1="K" u2="&#xd8;" k="61" />
<hkern u1="K" u2="&#xd6;" k="61" />
<hkern u1="K" u2="&#xd5;" k="61" />
<hkern u1="K" u2="&#xd4;" k="61" />
<hkern u1="K" u2="&#xd3;" k="61" />
<hkern u1="K" u2="&#xd2;" k="61" />
<hkern u1="K" u2="&#xae;" k="61" />
<hkern u1="K" u2="&#xa9;" k="61" />
<hkern u1="K" u2="y" k="41" />
<hkern u1="K" u2="v" k="41" />
<hkern u1="K" u2="u" k="12" />
<hkern u1="K" u2="t" k="20" />
<hkern u1="K" u2="q" k="47" />
<hkern u1="K" u2="o" k="47" />
<hkern u1="K" u2="g" k="20" />
<hkern u1="K" u2="f" k="10" />
<hkern u1="K" u2="e" k="47" />
<hkern u1="K" u2="d" k="47" />
<hkern u1="K" u2="c" k="47" />
<hkern u1="K" u2="a" k="41" />
<hkern u1="K" u2="Z" k="10" />
<hkern u1="K" u2="Y" k="-29" />
<hkern u1="K" u2="X" k="-14" />
<hkern u1="K" u2="W" k="25" />
<hkern u1="K" u2="V" k="-25" />
<hkern u1="K" u2="U" k="20" />
<hkern u1="K" u2="T" k="-10" />
<hkern u1="K" u2="S" k="10" />
<hkern u1="K" u2="Q" k="61" />
<hkern u1="K" u2="O" k="61" />
<hkern u1="K" u2="G" k="61" />
<hkern u1="K" u2="C" k="61" />
<hkern u1="K" u2="&#x40;" k="61" />
<hkern u1="K" u2="&#x2d;" k="82" />
<hkern u1="K" u2="&#x26;" k="41" />
<hkern u1="L" u2="&#x2122;" k="143" />
<hkern u1="L" u2="&#x201d;" k="123" />
<hkern u1="L" u2="&#x201c;" k="205" />
<hkern u1="L" u2="&#x2019;" k="123" />
<hkern u1="L" u2="&#x2018;" k="205" />
<hkern u1="L" u2="&#x178;" k="164" />
<hkern u1="L" u2="&#x153;" k="41" />
<hkern u1="L" u2="&#x152;" k="102" />
<hkern u1="L" u2="&#xe7;" k="41" />
<hkern u1="L" u2="&#xe6;" k="41" />
<hkern u1="L" u2="&#xdd;" k="164" />
<hkern u1="L" u2="&#xdc;" k="31" />
<hkern u1="L" u2="&#xdb;" k="31" />
<hkern u1="L" u2="&#xda;" k="31" />
<hkern u1="L" u2="&#xd9;" k="31" />
<hkern u1="L" u2="&#xd8;" k="102" />
<hkern u1="L" u2="&#xd6;" k="102" />
<hkern u1="L" u2="&#xd5;" k="102" />
<hkern u1="L" u2="&#xd4;" k="102" />
<hkern u1="L" u2="&#xd3;" k="102" />
<hkern u1="L" u2="&#xd2;" k="102" />
<hkern u1="L" u2="&#xc6;" k="-41" />
<hkern u1="L" u2="&#xc5;" k="-41" />
<hkern u1="L" u2="&#xc4;" k="-41" />
<hkern u1="L" u2="&#xc3;" k="-41" />
<hkern u1="L" u2="&#xc2;" k="-41" />
<hkern u1="L" u2="&#xc1;" k="-41" />
<hkern u1="L" u2="&#xc0;" k="-41" />
<hkern u1="L" u2="&#xae;" k="102" />
<hkern u1="L" u2="&#xa9;" k="102" />
<hkern u1="L" u2="y" k="82" />
<hkern u1="L" u2="w" k="20" />
<hkern u1="L" u2="v" k="82" />
<hkern u1="L" u2="u" k="12" />
<hkern u1="L" u2="q" k="41" />
<hkern u1="L" u2="o" k="41" />
<hkern u1="L" u2="e" k="41" />
<hkern u1="L" u2="d" k="41" />
<hkern u1="L" u2="c" k="41" />
<hkern u1="L" u2="a" k="41" />
<hkern u1="L" u2="\" k="102" />
<hkern u1="L" u2="Y" k="164" />
<hkern u1="L" u2="W" k="143" />
<hkern u1="L" u2="V" k="184" />
<hkern u1="L" u2="U" k="31" />
<hkern u1="L" u2="T" k="205" />
<hkern u1="L" u2="Q" k="102" />
<hkern u1="L" u2="O" k="102" />
<hkern u1="L" u2="G" k="102" />
<hkern u1="L" u2="C" k="102" />
<hkern u1="L" u2="A" k="-41" />
<hkern u1="L" u2="&#x40;" k="102" />
<hkern u1="M" u2="y" k="20" />
<hkern u1="M" u2="v" k="20" />
<hkern u1="M" u2="&#x2f;" k="41" />
<hkern u1="N" u2="y" k="20" />
<hkern u1="N" u2="v" k="20" />
<hkern u1="N" u2="&#x2f;" k="41" />
<hkern u1="O" u2="&#x2026;" k="82" />
<hkern u1="O" u2="&#x201e;" k="82" />
<hkern u1="O" u2="&#x201c;" k="41" />
<hkern u1="O" u2="&#x201a;" k="82" />
<hkern u1="O" u2="&#x2018;" k="41" />
<hkern u1="O" u2="&#x178;" k="51" />
<hkern u1="O" u2="&#x153;" k="10" />
<hkern u1="O" u2="&#xe7;" k="10" />
<hkern u1="O" u2="&#xe6;" k="10" />
<hkern u1="O" u2="&#xdd;" k="51" />
<hkern u1="O" u2="&#xc6;" k="35" />
<hkern u1="O" u2="&#xc5;" k="35" />
<hkern u1="O" u2="&#xc4;" k="35" />
<hkern u1="O" u2="&#xc3;" k="35" />
<hkern u1="O" u2="&#xc2;" k="35" />
<hkern u1="O" u2="&#xc1;" k="35" />
<hkern u1="O" u2="&#xc0;" k="35" />
<hkern u1="O" u2="z" k="20" />
<hkern u1="O" u2="y" k="-10" />
<hkern u1="O" u2="x" k="18" />
<hkern u1="O" u2="v" k="-10" />
<hkern u1="O" u2="u" k="10" />
<hkern u1="O" u2="r" k="10" />
<hkern u1="O" u2="q" k="10" />
<hkern u1="O" u2="p" k="10" />
<hkern u1="O" u2="o" k="10" />
<hkern u1="O" u2="n" k="10" />
<hkern u1="O" u2="m" k="10" />
<hkern u1="O" u2="l" k="20" />
<hkern u1="O" u2="k" k="20" />
<hkern u1="O" u2="h" k="20" />
<hkern u1="O" u2="e" k="10" />
<hkern u1="O" u2="d" k="10" />
<hkern u1="O" u2="c" k="10" />
<hkern u1="O" u2="b" k="20" />
<hkern u1="O" u2="a" k="10" />
<hkern u1="O" u2="Z" k="92" />
<hkern u1="O" u2="Y" k="51" />
<hkern u1="O" u2="X" k="51" />
<hkern u1="O" u2="W" k="68" />
<hkern u1="O" u2="V" k="39" />
<hkern u1="O" u2="T" k="66" />
<hkern u1="O" u2="J" k="92" />
<hkern u1="O" u2="A" k="35" />
<hkern u1="O" u2="&#x3f;" k="41" />
<hkern u1="O" u2="&#x2f;" k="102" />
<hkern u1="O" u2="&#x2e;" k="82" />
<hkern u1="O" u2="&#x2c;" k="82" />
<hkern u1="P" u2="&#x2026;" k="123" />
<hkern u1="P" u2="&#x201e;" k="123" />
<hkern u1="P" u2="&#x201a;" k="123" />
<hkern u1="P" u2="&#x178;" k="-31" />
<hkern u1="P" u2="&#x153;" k="20" />
<hkern u1="P" u2="&#xe7;" k="20" />
<hkern u1="P" u2="&#xe6;" k="20" />
<hkern u1="P" u2="&#xdd;" k="-31" />
<hkern u1="P" u2="&#xc6;" k="72" />
<hkern u1="P" u2="&#xc5;" k="72" />
<hkern u1="P" u2="&#xc4;" k="72" />
<hkern u1="P" u2="&#xc3;" k="72" />
<hkern u1="P" u2="&#xc2;" k="72" />
<hkern u1="P" u2="&#xc1;" k="72" />
<hkern u1="P" u2="&#xc0;" k="72" />
<hkern u1="P" u2="y" k="-37" />
<hkern u1="P" u2="x" k="-16" />
<hkern u1="P" u2="w" k="-18" />
<hkern u1="P" u2="v" k="-37" />
<hkern u1="P" u2="t" k="-20" />
<hkern u1="P" u2="q" k="20" />
<hkern u1="P" u2="o" k="20" />
<hkern u1="P" u2="f" k="-20" />
<hkern u1="P" u2="e" k="20" />
<hkern u1="P" u2="d" k="20" />
<hkern u1="P" u2="c" k="20" />
<hkern u1="P" u2="a" k="20" />
<hkern u1="P" u2="Z" k="51" />
<hkern u1="P" u2="Y" k="-31" />
<hkern u1="P" u2="X" k="-10" />
<hkern u1="P" u2="W" k="-10" />
<hkern u1="P" u2="V" k="-51" />
<hkern u1="P" u2="J" k="174" />
<hkern u1="P" u2="A" k="72" />
<hkern u1="P" u2="&#x2e;" k="123" />
<hkern u1="P" u2="&#x2c;" k="123" />
<hkern u1="P" u2="&#x26;" k="41" />
<hkern u1="Q" u2="&#x2026;" k="82" />
<hkern u1="Q" u2="&#x201e;" k="82" />
<hkern u1="Q" u2="&#x201c;" k="41" />
<hkern u1="Q" u2="&#x201a;" k="82" />
<hkern u1="Q" u2="&#x2018;" k="41" />
<hkern u1="Q" u2="&#x178;" k="51" />
<hkern u1="Q" u2="&#x153;" k="10" />
<hkern u1="Q" u2="&#xe7;" k="10" />
<hkern u1="Q" u2="&#xe6;" k="10" />
<hkern u1="Q" u2="&#xdd;" k="51" />
<hkern u1="Q" u2="&#xc6;" k="35" />
<hkern u1="Q" u2="&#xc5;" k="35" />
<hkern u1="Q" u2="&#xc4;" k="35" />
<hkern u1="Q" u2="&#xc3;" k="35" />
<hkern u1="Q" u2="&#xc2;" k="35" />
<hkern u1="Q" u2="&#xc1;" k="35" />
<hkern u1="Q" u2="&#xc0;" k="35" />
<hkern u1="Q" u2="z" k="20" />
<hkern u1="Q" u2="y" k="-10" />
<hkern u1="Q" u2="x" k="18" />
<hkern u1="Q" u2="v" k="-10" />
<hkern u1="Q" u2="u" k="10" />
<hkern u1="Q" u2="r" k="10" />
<hkern u1="Q" u2="q" k="10" />
<hkern u1="Q" u2="p" k="10" />
<hkern u1="Q" u2="o" k="10" />
<hkern u1="Q" u2="n" k="10" />
<hkern u1="Q" u2="m" k="10" />
<hkern u1="Q" u2="l" k="20" />
<hkern u1="Q" u2="k" k="20" />
<hkern u1="Q" u2="h" k="20" />
<hkern u1="Q" u2="e" k="10" />
<hkern u1="Q" u2="d" k="10" />
<hkern u1="Q" u2="c" k="10" />
<hkern u1="Q" u2="b" k="20" />
<hkern u1="Q" u2="a" k="10" />
<hkern u1="Q" u2="Z" k="92" />
<hkern u1="Q" u2="Y" k="51" />
<hkern u1="Q" u2="X" k="51" />
<hkern u1="Q" u2="W" k="68" />
<hkern u1="Q" u2="V" k="39" />
<hkern u1="Q" u2="T" k="66" />
<hkern u1="Q" u2="J" k="92" />
<hkern u1="Q" u2="A" k="35" />
<hkern u1="Q" u2="&#x3f;" k="41" />
<hkern u1="Q" u2="&#x2f;" k="61" />
<hkern u1="Q" u2="&#x2e;" k="25" />
<hkern u1="Q" u2="&#x2c;" k="82" />
<hkern u1="R" u2="&#x178;" k="-31" />
<hkern u1="R" u2="&#xdd;" k="-31" />
<hkern u1="R" u2="Y" k="-31" />
<hkern u1="R" u2="X" k="-20" />
<hkern u1="R" u2="V" k="-41" />
<hkern u1="R" u2="T" k="20" />
<hkern u1="R" u2="J" k="20" />
<hkern u1="R" u2="&#x26;" k="-20" />
<hkern u1="S" u2="&#x178;" k="-20" />
<hkern u1="S" u2="&#xdd;" k="-20" />
<hkern u1="S" u2="&#xc6;" k="-10" />
<hkern u1="S" u2="&#xc5;" k="-10" />
<hkern u1="S" u2="&#xc4;" k="-10" />
<hkern u1="S" u2="&#xc3;" k="-10" />
<hkern u1="S" u2="&#xc2;" k="-10" />
<hkern u1="S" u2="&#xc1;" k="-10" />
<hkern u1="S" u2="&#xc0;" k="-10" />
<hkern u1="S" u2="y" k="6" />
<hkern u1="S" u2="v" k="6" />
<hkern u1="S" u2="Y" k="-20" />
<hkern u1="S" u2="X" k="-10" />
<hkern u1="S" u2="V" k="-20" />
<hkern u1="S" u2="A" k="-10" />
<hkern u1="T" u2="&#x203a;" k="82" />
<hkern u1="T" u2="&#x2039;" k="225" />
<hkern u1="T" u2="&#x2026;" k="123" />
<hkern u1="T" u2="&#x201e;" k="123" />
<hkern u1="T" u2="&#x201a;" k="123" />
<hkern u1="T" u2="&#x2014;" k="143" />
<hkern u1="T" u2="&#x2013;" k="143" />
<hkern u1="T" u2="&#x178;" k="-41" />
<hkern u1="T" u2="&#x153;" k="195" />
<hkern u1="T" u2="&#x152;" k="66" />
<hkern u1="T" u2="&#xe7;" k="195" />
<hkern u1="T" u2="&#xe6;" k="195" />
<hkern u1="T" u2="&#xdd;" k="-41" />
<hkern u1="T" u2="&#xd8;" k="66" />
<hkern u1="T" u2="&#xd6;" k="66" />
<hkern u1="T" u2="&#xd5;" k="66" />
<hkern u1="T" u2="&#xd4;" k="66" />
<hkern u1="T" u2="&#xd3;" k="66" />
<hkern u1="T" u2="&#xd2;" k="66" />
<hkern u1="T" u2="&#xc6;" k="131" />
<hkern u1="T" u2="&#xc5;" k="131" />
<hkern u1="T" u2="&#xc4;" k="131" />
<hkern u1="T" u2="&#xc3;" k="131" />
<hkern u1="T" u2="&#xc2;" k="131" />
<hkern u1="T" u2="&#xc1;" k="131" />
<hkern u1="T" u2="&#xc0;" k="131" />
<hkern u1="T" u2="&#xbf;" k="102" />
<hkern u1="T" u2="&#xbb;" k="82" />
<hkern u1="T" u2="&#xae;" k="61" />
<hkern u1="T" u2="&#xab;" k="225" />
<hkern u1="T" u2="&#xa9;" k="61" />
<hkern u1="T" u2="z" k="164" />
<hkern u1="T" u2="y" k="150" />
<hkern u1="T" u2="x" k="164" />
<hkern u1="T" u2="w" k="129" />
<hkern u1="T" u2="v" k="150" />
<hkern u1="T" u2="u" k="164" />
<hkern u1="T" u2="t" k="41" />
<hkern u1="T" u2="s" k="205" />
<hkern u1="T" u2="r" k="164" />
<hkern u1="T" u2="q" k="195" />
<hkern u1="T" u2="p" k="164" />
<hkern u1="T" u2="o" k="195" />
<hkern u1="T" u2="n" k="164" />
<hkern u1="T" u2="m" k="164" />
<hkern u1="T" u2="g" k="195" />
<hkern u1="T" u2="f" k="20" />
<hkern u1="T" u2="e" k="195" />
<hkern u1="T" u2="d" k="195" />
<hkern u1="T" u2="c" k="195" />
<hkern u1="T" u2="a" k="195" />
<hkern u1="T" u2="\" k="-20" />
<hkern u1="T" u2="Z" k="20" />
<hkern u1="T" u2="Y" k="-41" />
<hkern u1="T" u2="X" k="-20" />
<hkern u1="T" u2="V" k="-41" />
<hkern u1="T" u2="T" k="-20" />
<hkern u1="T" u2="Q" k="66" />
<hkern u1="T" u2="O" k="66" />
<hkern u1="T" u2="J" k="180" />
<hkern u1="T" u2="G" k="66" />
<hkern u1="T" u2="C" k="66" />
<hkern u1="T" u2="A" k="131" />
<hkern u1="T" u2="&#x40;" k="61" />
<hkern u1="T" u2="&#x3b;" k="102" />
<hkern u1="T" u2="&#x3a;" k="102" />
<hkern u1="T" u2="&#x2e;" k="123" />
<hkern u1="T" u2="&#x2d;" k="143" />
<hkern u1="T" u2="&#x2c;" k="123" />
<hkern u1="T" u2="&#x26;" k="82" />
<hkern u1="U" u2="&#x2026;" k="20" />
<hkern u1="U" u2="&#x201e;" k="20" />
<hkern u1="U" u2="&#x201a;" k="20" />
<hkern u1="U" u2="J" k="20" />
<hkern u1="U" u2="&#x2e;" k="20" />
<hkern u1="U" u2="&#x2c;" k="20" />
<hkern u1="V" u2="&#x203a;" k="41" />
<hkern u1="V" u2="&#x2039;" k="102" />
<hkern u1="V" u2="&#x2026;" k="123" />
<hkern u1="V" u2="&#x201e;" k="123" />
<hkern u1="V" u2="&#x201a;" k="123" />
<hkern u1="V" u2="&#x2014;" k="123" />
<hkern u1="V" u2="&#x2013;" k="123" />
<hkern u1="V" u2="&#x178;" k="-61" />
<hkern u1="V" u2="&#x153;" k="82" />
<hkern u1="V" u2="&#x152;" k="39" />
<hkern u1="V" u2="&#xe7;" k="82" />
<hkern u1="V" u2="&#xe6;" k="82" />
<hkern u1="V" u2="&#xdd;" k="-61" />
<hkern u1="V" u2="&#xd8;" k="39" />
<hkern u1="V" u2="&#xd6;" k="39" />
<hkern u1="V" u2="&#xd5;" k="39" />
<hkern u1="V" u2="&#xd4;" k="39" />
<hkern u1="V" u2="&#xd3;" k="39" />
<hkern u1="V" u2="&#xd2;" k="39" />
<hkern u1="V" u2="&#xc6;" k="90" />
<hkern u1="V" u2="&#xc5;" k="90" />
<hkern u1="V" u2="&#xc4;" k="90" />
<hkern u1="V" u2="&#xc3;" k="90" />
<hkern u1="V" u2="&#xc2;" k="90" />
<hkern u1="V" u2="&#xc1;" k="90" />
<hkern u1="V" u2="&#xc0;" k="90" />
<hkern u1="V" u2="&#xbb;" k="41" />
<hkern u1="V" u2="&#xab;" k="102" />
<hkern u1="V" u2="&#x7d;" k="-61" />
<hkern u1="V" u2="y" k="20" />
<hkern u1="V" u2="x" k="51" />
<hkern u1="V" u2="w" k="20" />
<hkern u1="V" u2="v" k="20" />
<hkern u1="V" u2="u" k="61" />
<hkern u1="V" u2="t" k="20" />
<hkern u1="V" u2="s" k="82" />
<hkern u1="V" u2="r" k="61" />
<hkern u1="V" u2="q" k="82" />
<hkern u1="V" u2="p" k="61" />
<hkern u1="V" u2="o" k="82" />
<hkern u1="V" u2="n" k="61" />
<hkern u1="V" u2="m" k="61" />
<hkern u1="V" u2="l" k="20" />
<hkern u1="V" u2="k" k="20" />
<hkern u1="V" u2="h" k="20" />
<hkern u1="V" u2="g" k="72" />
<hkern u1="V" u2="f" k="20" />
<hkern u1="V" u2="e" k="82" />
<hkern u1="V" u2="d" k="82" />
<hkern u1="V" u2="c" k="82" />
<hkern u1="V" u2="b" k="20" />
<hkern u1="V" u2="a" k="82" />
<hkern u1="V" u2="]" k="-61" />
<hkern u1="V" u2="Y" k="-61" />
<hkern u1="V" u2="X" k="-41" />
<hkern u1="V" u2="W" k="10" />
<hkern u1="V" u2="V" k="-41" />
<hkern u1="V" u2="T" k="-41" />
<hkern u1="V" u2="S" k="-20" />
<hkern u1="V" u2="Q" k="39" />
<hkern u1="V" u2="O" k="39" />
<hkern u1="V" u2="J" k="115" />
<hkern u1="V" u2="G" k="39" />
<hkern u1="V" u2="C" k="39" />
<hkern u1="V" u2="A" k="90" />
<hkern u1="V" u2="&#x3b;" k="41" />
<hkern u1="V" u2="&#x3a;" k="41" />
<hkern u1="V" u2="&#x2e;" k="123" />
<hkern u1="V" u2="&#x2d;" k="123" />
<hkern u1="V" u2="&#x2c;" k="123" />
<hkern u1="V" u2="&#x29;" k="-61" />
<hkern u1="V" u2="&#x26;" k="82" />
<hkern u1="W" u2="&#x203a;" k="41" />
<hkern u1="W" u2="&#x2039;" k="82" />
<hkern u1="W" u2="&#x2026;" k="41" />
<hkern u1="W" u2="&#x201e;" k="41" />
<hkern u1="W" u2="&#x201a;" k="41" />
<hkern u1="W" u2="&#x2014;" k="82" />
<hkern u1="W" u2="&#x2013;" k="82" />
<hkern u1="W" u2="&#x153;" k="92" />
<hkern u1="W" u2="&#x152;" k="68" />
<hkern u1="W" u2="&#xe7;" k="92" />
<hkern u1="W" u2="&#xe6;" k="92" />
<hkern u1="W" u2="&#xd8;" k="68" />
<hkern u1="W" u2="&#xd6;" k="68" />
<hkern u1="W" u2="&#xd5;" k="68" />
<hkern u1="W" u2="&#xd4;" k="68" />
<hkern u1="W" u2="&#xd3;" k="68" />
<hkern u1="W" u2="&#xd2;" k="68" />
<hkern u1="W" u2="&#xc6;" k="90" />
<hkern u1="W" u2="&#xc5;" k="90" />
<hkern u1="W" u2="&#xc4;" k="90" />
<hkern u1="W" u2="&#xc3;" k="90" />
<hkern u1="W" u2="&#xc2;" k="90" />
<hkern u1="W" u2="&#xc1;" k="90" />
<hkern u1="W" u2="&#xc0;" k="90" />
<hkern u1="W" u2="&#xbb;" k="41" />
<hkern u1="W" u2="&#xae;" k="41" />
<hkern u1="W" u2="&#xab;" k="82" />
<hkern u1="W" u2="&#xa9;" k="41" />
<hkern u1="W" u2="&#x7d;" k="-20" />
<hkern u1="W" u2="z" k="61" />
<hkern u1="W" u2="y" k="61" />
<hkern u1="W" u2="x" k="61" />
<hkern u1="W" u2="w" k="61" />
<hkern u1="W" u2="v" k="61" />
<hkern u1="W" u2="u" k="72" />
<hkern u1="W" u2="t" k="61" />
<hkern u1="W" u2="s" k="102" />
<hkern u1="W" u2="r" k="72" />
<hkern u1="W" u2="q" k="92" />
<hkern u1="W" u2="p" k="72" />
<hkern u1="W" u2="o" k="92" />
<hkern u1="W" u2="n" k="72" />
<hkern u1="W" u2="m" k="72" />
<hkern u1="W" u2="l" k="20" />
<hkern u1="W" u2="k" k="20" />
<hkern u1="W" u2="j" k="41" />
<hkern u1="W" u2="i" k="41" />
<hkern u1="W" u2="h" k="20" />
<hkern u1="W" u2="g" k="102" />
<hkern u1="W" u2="f" k="31" />
<hkern u1="W" u2="e" k="92" />
<hkern u1="W" u2="d" k="92" />
<hkern u1="W" u2="c" k="92" />
<hkern u1="W" u2="b" k="20" />
<hkern u1="W" u2="a" k="92" />
<hkern u1="W" u2="]" k="-20" />
<hkern u1="W" u2="X" k="20" />
<hkern u1="W" u2="V" k="10" />
<hkern u1="W" u2="Q" k="68" />
<hkern u1="W" u2="O" k="68" />
<hkern u1="W" u2="J" k="94" />
<hkern u1="W" u2="G" k="68" />
<hkern u1="W" u2="C" k="68" />
<hkern u1="W" u2="A" k="90" />
<hkern u1="W" u2="&#x40;" k="41" />
<hkern u1="W" u2="&#x3b;" k="41" />
<hkern u1="W" u2="&#x3a;" k="41" />
<hkern u1="W" u2="&#x2e;" k="41" />
<hkern u1="W" u2="&#x2d;" k="82" />
<hkern u1="W" u2="&#x2c;" k="41" />
<hkern u1="W" u2="&#x29;" k="-20" />
<hkern u1="W" u2="&#x26;" k="61" />
<hkern u1="X" u2="&#x2014;" k="61" />
<hkern u1="X" u2="&#x2013;" k="61" />
<hkern u1="X" u2="&#x178;" k="-41" />
<hkern u1="X" u2="&#x153;" k="20" />
<hkern u1="X" u2="&#x152;" k="51" />
<hkern u1="X" u2="&#xe7;" k="20" />
<hkern u1="X" u2="&#xe6;" k="20" />
<hkern u1="X" u2="&#xdd;" k="-41" />
<hkern u1="X" u2="&#xd8;" k="51" />
<hkern u1="X" u2="&#xd6;" k="51" />
<hkern u1="X" u2="&#xd5;" k="51" />
<hkern u1="X" u2="&#xd4;" k="51" />
<hkern u1="X" u2="&#xd3;" k="51" />
<hkern u1="X" u2="&#xd2;" k="51" />
<hkern u1="X" u2="&#xc6;" k="-27" />
<hkern u1="X" u2="&#xc5;" k="-27" />
<hkern u1="X" u2="&#xc4;" k="-27" />
<hkern u1="X" u2="&#xc3;" k="-27" />
<hkern u1="X" u2="&#xc2;" k="-27" />
<hkern u1="X" u2="&#xc1;" k="-27" />
<hkern u1="X" u2="&#xc0;" k="-27" />
<hkern u1="X" u2="&#x7d;" k="-82" />
<hkern u1="X" u2="y" k="41" />
<hkern u1="X" u2="w" k="20" />
<hkern u1="X" u2="v" k="41" />
<hkern u1="X" u2="u" k="20" />
<hkern u1="X" u2="t" k="20" />
<hkern u1="X" u2="r" k="20" />
<hkern u1="X" u2="q" k="20" />
<hkern u1="X" u2="p" k="20" />
<hkern u1="X" u2="o" k="20" />
<hkern u1="X" u2="n" k="20" />
<hkern u1="X" u2="m" k="20" />
<hkern u1="X" u2="f" k="20" />
<hkern u1="X" u2="e" k="20" />
<hkern u1="X" u2="d" k="20" />
<hkern u1="X" u2="c" k="20" />
<hkern u1="X" u2="a" k="20" />
<hkern u1="X" u2="]" k="-82" />
<hkern u1="X" u2="Y" k="-41" />
<hkern u1="X" u2="X" k="-20" />
<hkern u1="X" u2="W" k="20" />
<hkern u1="X" u2="V" k="-41" />
<hkern u1="X" u2="T" k="-20" />
<hkern u1="X" u2="S" k="-10" />
<hkern u1="X" u2="Q" k="51" />
<hkern u1="X" u2="O" k="51" />
<hkern u1="X" u2="G" k="51" />
<hkern u1="X" u2="C" k="51" />
<hkern u1="X" u2="A" k="-27" />
<hkern u1="X" u2="&#x2d;" k="61" />
<hkern u1="X" u2="&#x29;" k="-82" />
<hkern u1="X" u2="&#x26;" k="41" />
<hkern u1="Y" u2="&#x203a;" k="82" />
<hkern u1="Y" u2="&#x2039;" k="123" />
<hkern u1="Y" u2="&#x2026;" k="102" />
<hkern u1="Y" u2="&#x201e;" k="102" />
<hkern u1="Y" u2="&#x201a;" k="102" />
<hkern u1="Y" u2="&#x2014;" k="102" />
<hkern u1="Y" u2="&#x2013;" k="102" />
<hkern u1="Y" u2="&#x153;" k="113" />
<hkern u1="Y" u2="&#x152;" k="51" />
<hkern u1="Y" u2="&#xe7;" k="113" />
<hkern u1="Y" u2="&#xe6;" k="113" />
<hkern u1="Y" u2="&#xd8;" k="51" />
<hkern u1="Y" u2="&#xd6;" k="51" />
<hkern u1="Y" u2="&#xd5;" k="51" />
<hkern u1="Y" u2="&#xd4;" k="51" />
<hkern u1="Y" u2="&#xd3;" k="51" />
<hkern u1="Y" u2="&#xd2;" k="51" />
<hkern u1="Y" u2="&#xc6;" k="78" />
<hkern u1="Y" u2="&#xc5;" k="78" />
<hkern u1="Y" u2="&#xc4;" k="78" />
<hkern u1="Y" u2="&#xc3;" k="78" />
<hkern u1="Y" u2="&#xc2;" k="78" />
<hkern u1="Y" u2="&#xc1;" k="78" />
<hkern u1="Y" u2="&#xc0;" k="78" />
<hkern u1="Y" u2="&#xbb;" k="82" />
<hkern u1="Y" u2="&#xae;" k="61" />
<hkern u1="Y" u2="&#xab;" k="123" />
<hkern u1="Y" u2="&#xa9;" k="61" />
<hkern u1="Y" u2="&#x7d;" k="-102" />
<hkern u1="Y" u2="z" k="61" />
<hkern u1="Y" u2="y" k="41" />
<hkern u1="Y" u2="x" k="41" />
<hkern u1="Y" u2="w" k="20" />
<hkern u1="Y" u2="v" k="41" />
<hkern u1="Y" u2="u" k="82" />
<hkern u1="Y" u2="t" k="41" />
<hkern u1="Y" u2="s" k="102" />
<hkern u1="Y" u2="r" k="82" />
<hkern u1="Y" u2="q" k="113" />
<hkern u1="Y" u2="p" k="82" />
<hkern u1="Y" u2="o" k="113" />
<hkern u1="Y" u2="n" k="82" />
<hkern u1="Y" u2="m" k="82" />
<hkern u1="Y" u2="g" k="82" />
<hkern u1="Y" u2="f" k="20" />
<hkern u1="Y" u2="e" k="113" />
<hkern u1="Y" u2="d" k="113" />
<hkern u1="Y" u2="c" k="113" />
<hkern u1="Y" u2="a" k="113" />
<hkern u1="Y" u2="]" k="-102" />
<hkern u1="Y" u2="X" k="-41" />
<hkern u1="Y" u2="V" k="-61" />
<hkern u1="Y" u2="T" k="-41" />
<hkern u1="Y" u2="S" k="-20" />
<hkern u1="Y" u2="Q" k="51" />
<hkern u1="Y" u2="O" k="51" />
<hkern u1="Y" u2="J" k="150" />
<hkern u1="Y" u2="G" k="51" />
<hkern u1="Y" u2="C" k="51" />
<hkern u1="Y" u2="A" k="78" />
<hkern u1="Y" u2="&#x40;" k="61" />
<hkern u1="Y" u2="&#x3b;" k="61" />
<hkern u1="Y" u2="&#x3a;" k="61" />
<hkern u1="Y" u2="&#x2e;" k="102" />
<hkern u1="Y" u2="&#x2d;" k="102" />
<hkern u1="Y" u2="&#x2c;" k="102" />
<hkern u1="Y" u2="&#x29;" k="-102" />
<hkern u1="Y" u2="&#x26;" k="61" />
<hkern u1="Z" u2="&#x2014;" k="102" />
<hkern u1="Z" u2="&#x2013;" k="102" />
<hkern u1="Z" u2="&#x153;" k="82" />
<hkern u1="Z" u2="&#x152;" k="92" />
<hkern u1="Z" u2="&#xf0;" k="61" />
<hkern u1="Z" u2="&#xe7;" k="82" />
<hkern u1="Z" u2="&#xe6;" k="68" />
<hkern u1="Z" u2="&#xd8;" k="92" />
<hkern u1="Z" u2="&#xd6;" k="92" />
<hkern u1="Z" u2="&#xd5;" k="92" />
<hkern u1="Z" u2="&#xd4;" k="92" />
<hkern u1="Z" u2="&#xd3;" k="92" />
<hkern u1="Z" u2="&#xd2;" k="92" />
<hkern u1="Z" u2="&#xc6;" k="14" />
<hkern u1="Z" u2="&#xc5;" k="14" />
<hkern u1="Z" u2="&#xc4;" k="14" />
<hkern u1="Z" u2="&#xc3;" k="14" />
<hkern u1="Z" u2="&#xc2;" k="14" />
<hkern u1="Z" u2="&#xc1;" k="14" />
<hkern u1="Z" u2="&#xc0;" k="14" />
<hkern u1="Z" u2="y" k="61" />
<hkern u1="Z" u2="w" k="20" />
<hkern u1="Z" u2="v" k="61" />
<hkern u1="Z" u2="u" k="41" />
<hkern u1="Z" u2="r" k="41" />
<hkern u1="Z" u2="q" k="82" />
<hkern u1="Z" u2="p" k="41" />
<hkern u1="Z" u2="o" k="82" />
<hkern u1="Z" u2="n" k="41" />
<hkern u1="Z" u2="m" k="41" />
<hkern u1="Z" u2="l" k="20" />
<hkern u1="Z" u2="k" k="20" />
<hkern u1="Z" u2="j" k="31" />
<hkern u1="Z" u2="i" k="41" />
<hkern u1="Z" u2="h" k="20" />
<hkern u1="Z" u2="g" k="41" />
<hkern u1="Z" u2="f" k="41" />
<hkern u1="Z" u2="e" k="82" />
<hkern u1="Z" u2="d" k="82" />
<hkern u1="Z" u2="c" k="82" />
<hkern u1="Z" u2="b" k="20" />
<hkern u1="Z" u2="a" k="68" />
<hkern u1="Z" u2="T" k="20" />
<hkern u1="Z" u2="Q" k="92" />
<hkern u1="Z" u2="O" k="92" />
<hkern u1="Z" u2="J" k="20" />
<hkern u1="Z" u2="G" k="92" />
<hkern u1="Z" u2="C" k="92" />
<hkern u1="Z" u2="A" k="14" />
<hkern u1="Z" u2="&#x2d;" k="102" />
<hkern u1="[" u2="&#x178;" k="-102" />
<hkern u1="[" u2="&#xdd;" k="-102" />
<hkern u1="[" u2="j" k="-307" />
<hkern u1="[" u2="g" k="-41" />
<hkern u1="[" u2="Y" k="-102" />
<hkern u1="[" u2="X" k="-82" />
<hkern u1="[" u2="W" k="-20" />
<hkern u1="[" u2="V" k="-61" />
<hkern u1="\" u2="&#x178;" k="123" />
<hkern u1="\" u2="&#x153;" k="20" />
<hkern u1="\" u2="&#xe7;" k="20" />
<hkern u1="\" u2="&#xe6;" k="20" />
<hkern u1="\" u2="&#xdd;" k="123" />
<hkern u1="\" u2="q" k="20" />
<hkern u1="\" u2="o" k="20" />
<hkern u1="\" u2="j" k="-266" />
<hkern u1="\" u2="e" k="20" />
<hkern u1="\" u2="d" k="20" />
<hkern u1="\" u2="c" k="20" />
<hkern u1="\" u2="a" k="20" />
<hkern u1="\" u2="Y" k="123" />
<hkern u1="\" u2="W" k="102" />
<hkern u1="\" u2="V" k="143" />
<hkern u1="\" u2="T" k="184" />
<hkern u1="a" u2="&#x2122;" k="41" />
<hkern u1="a" u2="&#x201c;" k="61" />
<hkern u1="a" u2="&#x2018;" k="61" />
<hkern u1="a" u2="y" k="10" />
<hkern u1="a" u2="v" k="10" />
<hkern u1="a" u2="t" k="20" />
<hkern u1="a" u2="\" k="20" />
<hkern u1="a" u2="&#x3f;" k="20" />
<hkern u1="b" u2="&#x2122;" k="61" />
<hkern u1="b" u2="&#x2026;" k="41" />
<hkern u1="b" u2="&#x201e;" k="41" />
<hkern u1="b" u2="&#x201c;" k="82" />
<hkern u1="b" u2="&#x201a;" k="41" />
<hkern u1="b" u2="&#x2018;" k="82" />
<hkern u1="b" u2="z" k="31" />
<hkern u1="b" u2="y" k="20" />
<hkern u1="b" u2="x" k="31" />
<hkern u1="b" u2="w" k="6" />
<hkern u1="b" u2="v" k="20" />
<hkern u1="b" u2="s" k="4" />
<hkern u1="b" u2="g" k="-4" />
<hkern u1="b" u2="f" k="4" />
<hkern u1="b" u2="\" k="20" />
<hkern u1="b" u2="&#x3f;" k="41" />
<hkern u1="b" u2="&#x3b;" k="20" />
<hkern u1="b" u2="&#x3a;" k="20" />
<hkern u1="b" u2="&#x2f;" k="20" />
<hkern u1="b" u2="&#x2e;" k="41" />
<hkern u1="b" u2="&#x2c;" k="41" />
<hkern u1="e" u2="y" k="20" />
<hkern u1="e" u2="x" k="31" />
<hkern u1="e" u2="v" k="20" />
<hkern u1="f" u2="&#x2122;" k="-94" />
<hkern u1="f" u2="&#x203a;" k="41" />
<hkern u1="f" u2="&#x2039;" k="102" />
<hkern u1="f" u2="&#x2026;" k="164" />
<hkern u1="f" u2="&#x201e;" k="164" />
<hkern u1="f" u2="&#x201d;" k="-102" />
<hkern u1="f" u2="&#x201c;" k="-61" />
<hkern u1="f" u2="&#x201a;" k="164" />
<hkern u1="f" u2="&#x2019;" k="-102" />
<hkern u1="f" u2="&#x2018;" k="-61" />
<hkern u1="f" u2="&#x2014;" k="61" />
<hkern u1="f" u2="&#x2013;" k="61" />
<hkern u1="f" u2="&#x153;" k="41" />
<hkern u1="f" u2="&#xf0;" k="41" />
<hkern u1="f" u2="&#xe7;" k="41" />
<hkern u1="f" u2="&#xe6;" k="41" />
<hkern u1="f" u2="&#xbb;" k="41" />
<hkern u1="f" u2="&#xab;" k="102" />
<hkern u1="f" u2="&#x7d;" k="-143" />
<hkern u1="f" u2="y" k="-6" />
<hkern u1="f" u2="v" k="-6" />
<hkern u1="f" u2="u" k="10" />
<hkern u1="f" u2="r" k="10" />
<hkern u1="f" u2="q" k="41" />
<hkern u1="f" u2="p" k="10" />
<hkern u1="f" u2="o" k="41" />
<hkern u1="f" u2="n" k="10" />
<hkern u1="f" u2="m" k="10" />
<hkern u1="f" u2="g" k="10" />
<hkern u1="f" u2="e" k="41" />
<hkern u1="f" u2="d" k="41" />
<hkern u1="f" u2="c" k="41" />
<hkern u1="f" u2="a" k="41" />
<hkern u1="f" u2="]" k="-143" />
<hkern u1="f" u2="\" k="-82" />
<hkern u1="f" u2="&#x3f;" k="-82" />
<hkern u1="f" u2="&#x3b;" k="20" />
<hkern u1="f" u2="&#x3a;" k="20" />
<hkern u1="f" u2="&#x2f;" k="102" />
<hkern u1="f" u2="&#x2e;" k="164" />
<hkern u1="f" u2="&#x2d;" k="61" />
<hkern u1="f" u2="&#x2c;" k="164" />
<hkern u1="f" u2="&#x2a;" k="-41" />
<hkern u1="f" u2="&#x29;" k="-143" />
<hkern u1="f" u2="&#x26;" k="61" />
<hkern u1="g" u2="&#x201d;" k="-82" />
<hkern u1="g" u2="&#x201c;" k="-41" />
<hkern u1="g" u2="&#x2019;" k="-82" />
<hkern u1="g" u2="&#x2018;" k="-41" />
<hkern u1="g" u2="&#x153;" k="-10" />
<hkern u1="g" u2="&#xe7;" k="-10" />
<hkern u1="g" u2="&#xe6;" k="-10" />
<hkern u1="g" u2="&#x7d;" k="-41" />
<hkern u1="g" u2="z" k="-16" />
<hkern u1="g" u2="y" k="-6" />
<hkern u1="g" u2="x" k="-18" />
<hkern u1="g" u2="w" k="-4" />
<hkern u1="g" u2="v" k="-6" />
<hkern u1="g" u2="u" k="-6" />
<hkern u1="g" u2="t" k="-20" />
<hkern u1="g" u2="r" k="-6" />
<hkern u1="g" u2="q" k="-10" />
<hkern u1="g" u2="p" k="-6" />
<hkern u1="g" u2="o" k="-10" />
<hkern u1="g" u2="n" k="-6" />
<hkern u1="g" u2="m" k="-6" />
<hkern u1="g" u2="j" k="-115" />
<hkern u1="g" u2="g" k="-43" />
<hkern u1="g" u2="f" k="-6" />
<hkern u1="g" u2="e" k="-10" />
<hkern u1="g" u2="d" k="-10" />
<hkern u1="g" u2="c" k="-10" />
<hkern u1="g" u2="a" k="-10" />
<hkern u1="g" u2="]" k="-41" />
<hkern u1="g" u2="&#x3b;" k="-41" />
<hkern u1="g" u2="&#x2f;" k="-82" />
<hkern u1="g" u2="&#x2c;" k="-43" />
<hkern u1="g" u2="&#x29;" k="-41" />
<hkern u1="h" u2="&#x2122;" k="41" />
<hkern u1="h" u2="&#x201c;" k="61" />
<hkern u1="h" u2="&#x2018;" k="61" />
<hkern u1="h" u2="y" k="10" />
<hkern u1="h" u2="v" k="10" />
<hkern u1="h" u2="t" k="20" />
<hkern u1="h" u2="\" k="20" />
<hkern u1="h" u2="&#x3f;" k="20" />
<hkern u1="k" u2="&#x153;" k="25" />
<hkern u1="k" u2="&#xe7;" k="25" />
<hkern u1="k" u2="&#xe6;" k="25" />
<hkern u1="k" u2="y" k="-10" />
<hkern u1="k" u2="w" k="-4" />
<hkern u1="k" u2="v" k="-10" />
<hkern u1="k" u2="q" k="25" />
<hkern u1="k" u2="o" k="25" />
<hkern u1="k" u2="g" k="29" />
<hkern u1="k" u2="e" k="25" />
<hkern u1="k" u2="d" k="25" />
<hkern u1="k" u2="c" k="25" />
<hkern u1="k" u2="a" k="25" />
<hkern u1="m" u2="&#x2122;" k="41" />
<hkern u1="m" u2="&#x201c;" k="61" />
<hkern u1="m" u2="&#x2018;" k="61" />
<hkern u1="m" u2="y" k="10" />
<hkern u1="m" u2="v" k="10" />
<hkern u1="m" u2="t" k="20" />
<hkern u1="m" u2="\" k="20" />
<hkern u1="m" u2="&#x3f;" k="20" />
<hkern u1="n" u2="&#x2122;" k="41" />
<hkern u1="n" u2="&#x201c;" k="61" />
<hkern u1="n" u2="&#x2018;" k="61" />
<hkern u1="n" u2="y" k="10" />
<hkern u1="n" u2="v" k="10" />
<hkern u1="n" u2="t" k="20" />
<hkern u1="n" u2="\" k="20" />
<hkern u1="n" u2="&#x3f;" k="20" />
<hkern u1="o" u2="&#x2122;" k="61" />
<hkern u1="o" u2="&#x2026;" k="41" />
<hkern u1="o" u2="&#x201e;" k="41" />
<hkern u1="o" u2="&#x201c;" k="82" />
<hkern u1="o" u2="&#x201a;" k="41" />
<hkern u1="o" u2="&#x2018;" k="82" />
<hkern u1="o" u2="z" k="31" />
<hkern u1="o" u2="y" k="20" />
<hkern u1="o" u2="x" k="31" />
<hkern u1="o" u2="w" k="6" />
<hkern u1="o" u2="v" k="20" />
<hkern u1="o" u2="s" k="4" />
<hkern u1="o" u2="g" k="-4" />
<hkern u1="o" u2="f" k="4" />
<hkern u1="o" u2="\" k="20" />
<hkern u1="o" u2="&#x3f;" k="41" />
<hkern u1="o" u2="&#x3b;" k="20" />
<hkern u1="o" u2="&#x3a;" k="20" />
<hkern u1="o" u2="&#x2f;" k="20" />
<hkern u1="o" u2="&#x2e;" k="41" />
<hkern u1="o" u2="&#x2c;" k="41" />
<hkern u1="p" u2="&#x2122;" k="61" />
<hkern u1="p" u2="&#x2026;" k="41" />
<hkern u1="p" u2="&#x201e;" k="41" />
<hkern u1="p" u2="&#x201c;" k="82" />
<hkern u1="p" u2="&#x201a;" k="41" />
<hkern u1="p" u2="&#x2018;" k="82" />
<hkern u1="p" u2="z" k="31" />
<hkern u1="p" u2="y" k="20" />
<hkern u1="p" u2="x" k="31" />
<hkern u1="p" u2="w" k="6" />
<hkern u1="p" u2="v" k="20" />
<hkern u1="p" u2="s" k="4" />
<hkern u1="p" u2="g" k="-4" />
<hkern u1="p" u2="f" k="4" />
<hkern u1="p" u2="\" k="20" />
<hkern u1="p" u2="&#x3f;" k="41" />
<hkern u1="p" u2="&#x3b;" k="20" />
<hkern u1="p" u2="&#x3a;" k="20" />
<hkern u1="p" u2="&#x2f;" k="20" />
<hkern u1="p" u2="&#x2e;" k="41" />
<hkern u1="p" u2="&#x2c;" k="41" />
<hkern u1="r" u2="&#x2026;" k="102" />
<hkern u1="r" u2="&#x201e;" k="102" />
<hkern u1="r" u2="&#x201d;" k="-82" />
<hkern u1="r" u2="&#x201c;" k="-41" />
<hkern u1="r" u2="&#x201a;" k="102" />
<hkern u1="r" u2="&#x2019;" k="-82" />
<hkern u1="r" u2="&#x2018;" k="-41" />
<hkern u1="r" u2="&#x2014;" k="41" />
<hkern u1="r" u2="&#x2013;" k="41" />
<hkern u1="r" u2="&#x153;" k="16" />
<hkern u1="r" u2="&#xe7;" k="16" />
<hkern u1="r" u2="&#xe6;" k="16" />
<hkern u1="r" u2="t" k="-41" />
<hkern u1="r" u2="q" k="16" />
<hkern u1="r" u2="o" k="16" />
<hkern u1="r" u2="g" k="20" />
<hkern u1="r" u2="f" k="-31" />
<hkern u1="r" u2="e" k="16" />
<hkern u1="r" u2="d" k="16" />
<hkern u1="r" u2="c" k="16" />
<hkern u1="r" u2="a" k="16" />
<hkern u1="r" u2="&#x3f;" k="-41" />
<hkern u1="r" u2="&#x2f;" k="123" />
<hkern u1="r" u2="&#x2e;" k="102" />
<hkern u1="r" u2="&#x2d;" k="41" />
<hkern u1="r" u2="&#x2c;" k="102" />
<hkern u1="s" u2="&#x153;" k="-4" />
<hkern u1="s" u2="&#xe7;" k="-4" />
<hkern u1="s" u2="&#xe6;" k="-4" />
<hkern u1="s" u2="q" k="-4" />
<hkern u1="s" u2="o" k="-4" />
<hkern u1="s" u2="e" k="-4" />
<hkern u1="s" u2="d" k="-4" />
<hkern u1="s" u2="c" k="-4" />
<hkern u1="s" u2="a" k="-4" />
<hkern u1="t" u2="&#x2026;" k="-20" />
<hkern u1="t" u2="&#x201e;" k="-20" />
<hkern u1="t" u2="&#x201c;" k="-20" />
<hkern u1="t" u2="&#x201a;" k="-20" />
<hkern u1="t" u2="&#x2018;" k="-20" />
<hkern u1="t" u2="&#x153;" k="16" />
<hkern u1="t" u2="&#xe7;" k="16" />
<hkern u1="t" u2="&#xe6;" k="16" />
<hkern u1="t" u2="z" k="-4" />
<hkern u1="t" u2="q" k="16" />
<hkern u1="t" u2="o" k="16" />
<hkern u1="t" u2="e" k="16" />
<hkern u1="t" u2="d" k="16" />
<hkern u1="t" u2="c" k="16" />
<hkern u1="t" u2="a" k="16" />
<hkern u1="t" u2="&#x2e;" k="-20" />
<hkern u1="t" u2="&#x2c;" k="-20" />
<hkern u1="v" u2="&#x2026;" k="102" />
<hkern u1="v" u2="&#x201e;" k="102" />
<hkern u1="v" u2="&#x201d;" k="-61" />
<hkern u1="v" u2="&#x201c;" k="-61" />
<hkern u1="v" u2="&#x201a;" k="102" />
<hkern u1="v" u2="&#x2019;" k="-61" />
<hkern u1="v" u2="&#x2018;" k="-61" />
<hkern u1="v" u2="&#x2014;" k="20" />
<hkern u1="v" u2="&#x2013;" k="20" />
<hkern u1="v" u2="&#x153;" k="20" />
<hkern u1="v" u2="&#xe7;" k="20" />
<hkern u1="v" u2="&#xe6;" k="20" />
<hkern u1="v" u2="t" k="-6" />
<hkern u1="v" u2="q" k="20" />
<hkern u1="v" u2="o" k="20" />
<hkern u1="v" u2="g" k="-4" />
<hkern u1="v" u2="f" k="-6" />
<hkern u1="v" u2="e" k="20" />
<hkern u1="v" u2="d" k="20" />
<hkern u1="v" u2="c" k="20" />
<hkern u1="v" u2="a" k="20" />
<hkern u1="v" u2="&#x2e;" k="102" />
<hkern u1="v" u2="&#x2d;" k="20" />
<hkern u1="v" u2="&#x2c;" k="102" />
<hkern u1="w" u2="&#x2026;" k="61" />
<hkern u1="w" u2="&#x201e;" k="61" />
<hkern u1="w" u2="&#x201d;" k="-41" />
<hkern u1="w" u2="&#x201c;" k="-41" />
<hkern u1="w" u2="&#x201a;" k="61" />
<hkern u1="w" u2="&#x2019;" k="-41" />
<hkern u1="w" u2="&#x2018;" k="-41" />
<hkern u1="w" u2="&#x153;" k="6" />
<hkern u1="w" u2="&#xe7;" k="6" />
<hkern u1="w" u2="&#xe6;" k="6" />
<hkern u1="w" u2="t" k="-4" />
<hkern u1="w" u2="q" k="6" />
<hkern u1="w" u2="o" k="6" />
<hkern u1="w" u2="e" k="6" />
<hkern u1="w" u2="d" k="6" />
<hkern u1="w" u2="c" k="6" />
<hkern u1="w" u2="a" k="6" />
<hkern u1="w" u2="&#x2e;" k="61" />
<hkern u1="w" u2="&#x2c;" k="61" />
<hkern u1="x" u2="&#x2039;" k="102" />
<hkern u1="x" u2="&#x201d;" k="-41" />
<hkern u1="x" u2="&#x201c;" k="-20" />
<hkern u1="x" u2="&#x2019;" k="-41" />
<hkern u1="x" u2="&#x2018;" k="-20" />
<hkern u1="x" u2="&#x2014;" k="61" />
<hkern u1="x" u2="&#x2013;" k="61" />
<hkern u1="x" u2="&#x153;" k="31" />
<hkern u1="x" u2="&#xe7;" k="31" />
<hkern u1="x" u2="&#xe6;" k="31" />
<hkern u1="x" u2="&#xab;" k="102" />
<hkern u1="x" u2="q" k="31" />
<hkern u1="x" u2="o" k="31" />
<hkern u1="x" u2="g" k="-6" />
<hkern u1="x" u2="e" k="31" />
<hkern u1="x" u2="d" k="31" />
<hkern u1="x" u2="c" k="31" />
<hkern u1="x" u2="a" k="31" />
<hkern u1="x" u2="&#x2d;" k="61" />
<hkern u1="y" u2="&#x2026;" k="102" />
<hkern u1="y" u2="&#x201e;" k="102" />
<hkern u1="y" u2="&#x201d;" k="-61" />
<hkern u1="y" u2="&#x201c;" k="-61" />
<hkern u1="y" u2="&#x201a;" k="102" />
<hkern u1="y" u2="&#x2019;" k="-61" />
<hkern u1="y" u2="&#x2018;" k="-61" />
<hkern u1="y" u2="&#x2014;" k="20" />
<hkern u1="y" u2="&#x2013;" k="20" />
<hkern u1="y" u2="&#x153;" k="20" />
<hkern u1="y" u2="&#xe7;" k="20" />
<hkern u1="y" u2="&#xe6;" k="20" />
<hkern u1="y" u2="t" k="-6" />
<hkern u1="y" u2="q" k="20" />
<hkern u1="y" u2="o" k="20" />
<hkern u1="y" u2="g" k="-4" />
<hkern u1="y" u2="f" k="-6" />
<hkern u1="y" u2="e" k="20" />
<hkern u1="y" u2="d" k="20" />
<hkern u1="y" u2="c" k="20" />
<hkern u1="y" u2="a" k="20" />
<hkern u1="y" u2="&#x2e;" k="102" />
<hkern u1="y" u2="&#x2d;" k="20" />
<hkern u1="y" u2="&#x2c;" k="102" />
<hkern u1="z" u2="&#x2039;" k="123" />
<hkern u1="z" u2="&#x2014;" k="61" />
<hkern u1="z" u2="&#x2013;" k="61" />
<hkern u1="z" u2="&#x153;" k="41" />
<hkern u1="z" u2="&#xe7;" k="41" />
<hkern u1="z" u2="&#xe6;" k="41" />
<hkern u1="z" u2="&#xab;" k="123" />
<hkern u1="z" u2="t" k="-4" />
<hkern u1="z" u2="q" k="41" />
<hkern u1="z" u2="o" k="41" />
<hkern u1="z" u2="g" k="18" />
<hkern u1="z" u2="e" k="41" />
<hkern u1="z" u2="d" k="41" />
<hkern u1="z" u2="c" k="41" />
<hkern u1="z" u2="a" k="41" />
<hkern u1="z" u2="&#x2d;" k="61" />
<hkern u1="&#x7b;" u2="&#x178;" k="-102" />
<hkern u1="&#x7b;" u2="&#xdd;" k="-102" />
<hkern u1="&#x7b;" u2="j" k="-307" />
<hkern u1="&#x7b;" u2="g" k="-41" />
<hkern u1="&#x7b;" u2="Y" k="-102" />
<hkern u1="&#x7b;" u2="X" k="-82" />
<hkern u1="&#x7b;" u2="W" k="-20" />
<hkern u1="&#x7b;" u2="V" k="-61" />
<hkern u1="&#xa3;" u2="&#x34;" k="41" />
<hkern u1="&#xa3;" u2="&#x31;" k="-20" />
<hkern u1="&#xa4;" u2="&#x34;" k="41" />
<hkern u1="&#xa9;" u2="&#x178;" k="61" />
<hkern u1="&#xa9;" u2="&#xdd;" k="61" />
<hkern u1="&#xa9;" u2="&#xc6;" k="20" />
<hkern u1="&#xa9;" u2="&#xc5;" k="20" />
<hkern u1="&#xa9;" u2="&#xc4;" k="20" />
<hkern u1="&#xa9;" u2="&#xc3;" k="20" />
<hkern u1="&#xa9;" u2="&#xc2;" k="20" />
<hkern u1="&#xa9;" u2="&#xc1;" k="20" />
<hkern u1="&#xa9;" u2="&#xc0;" k="20" />
<hkern u1="&#xa9;" u2="Y" k="61" />
<hkern u1="&#xa9;" u2="W" k="41" />
<hkern u1="&#xa9;" u2="T" k="61" />
<hkern u1="&#xa9;" u2="A" k="20" />
<hkern u1="&#xab;" u2="&#x178;" k="82" />
<hkern u1="&#xab;" u2="&#xdd;" k="82" />
<hkern u1="&#xab;" u2="Y" k="82" />
<hkern u1="&#xab;" u2="W" k="41" />
<hkern u1="&#xab;" u2="V" k="41" />
<hkern u1="&#xab;" u2="T" k="82" />
<hkern u1="&#xae;" u2="&#x178;" k="61" />
<hkern u1="&#xae;" u2="&#xdd;" k="61" />
<hkern u1="&#xae;" u2="&#xc6;" k="20" />
<hkern u1="&#xae;" u2="&#xc5;" k="20" />
<hkern u1="&#xae;" u2="&#xc4;" k="20" />
<hkern u1="&#xae;" u2="&#xc3;" k="20" />
<hkern u1="&#xae;" u2="&#xc2;" k="20" />
<hkern u1="&#xae;" u2="&#xc1;" k="20" />
<hkern u1="&#xae;" u2="&#xc0;" k="20" />
<hkern u1="&#xae;" u2="Y" k="61" />
<hkern u1="&#xae;" u2="W" k="41" />
<hkern u1="&#xae;" u2="T" k="61" />
<hkern u1="&#xae;" u2="A" k="20" />
<hkern u1="&#xb0;" u2="&#x34;" k="133" />
<hkern u1="&#xbb;" u2="&#x178;" k="102" />
<hkern u1="&#xbb;" u2="&#xdd;" k="102" />
<hkern u1="&#xbb;" u2="z" k="102" />
<hkern u1="&#xbb;" u2="x" k="102" />
<hkern u1="&#xbb;" u2="Y" k="102" />
<hkern u1="&#xbb;" u2="W" k="82" />
<hkern u1="&#xbb;" u2="V" k="102" />
<hkern u1="&#xbb;" u2="T" k="225" />
<hkern u1="&#xbf;" u2="&#x178;" k="82" />
<hkern u1="&#xbf;" u2="&#x152;" k="41" />
<hkern u1="&#xbf;" u2="&#xdd;" k="82" />
<hkern u1="&#xbf;" u2="&#xd8;" k="41" />
<hkern u1="&#xbf;" u2="&#xd6;" k="41" />
<hkern u1="&#xbf;" u2="&#xd5;" k="41" />
<hkern u1="&#xbf;" u2="&#xd4;" k="41" />
<hkern u1="&#xbf;" u2="&#xd3;" k="41" />
<hkern u1="&#xbf;" u2="&#xd2;" k="41" />
<hkern u1="&#xbf;" u2="y" k="61" />
<hkern u1="&#xbf;" u2="x" k="20" />
<hkern u1="&#xbf;" u2="w" k="41" />
<hkern u1="&#xbf;" u2="v" k="61" />
<hkern u1="&#xbf;" u2="Y" k="82" />
<hkern u1="&#xbf;" u2="W" k="61" />
<hkern u1="&#xbf;" u2="V" k="102" />
<hkern u1="&#xbf;" u2="T" k="123" />
<hkern u1="&#xbf;" u2="Q" k="41" />
<hkern u1="&#xbf;" u2="O" k="41" />
<hkern u1="&#xbf;" u2="G" k="41" />
<hkern u1="&#xbf;" u2="C" k="41" />
<hkern u1="&#xbf;" u2="&#x37;" k="61" />
<hkern u1="&#xbf;" u2="&#x33;" k="-20" />
<hkern u1="&#xbf;" u2="&#x31;" k="82" />
<hkern u1="&#xc0;" u2="&#x2122;" k="100" />
<hkern u1="&#xc0;" u2="&#x2026;" k="-43" />
<hkern u1="&#xc0;" u2="&#x201e;" k="-43" />
<hkern u1="&#xc0;" u2="&#x201d;" k="43" />
<hkern u1="&#xc0;" u2="&#x201c;" k="86" />
<hkern u1="&#xc0;" u2="&#x201a;" k="-43" />
<hkern u1="&#xc0;" u2="&#x2019;" k="43" />
<hkern u1="&#xc0;" u2="&#x2018;" k="86" />
<hkern u1="&#xc0;" u2="&#x2014;" k="-14" />
<hkern u1="&#xc0;" u2="&#x2013;" k="-14" />
<hkern u1="&#xc0;" u2="&#x178;" k="72" />
<hkern u1="&#xc0;" u2="&#x153;" k="8" />
<hkern u1="&#xc0;" u2="&#x152;" k="29" />
<hkern u1="&#xc0;" u2="&#xe7;" k="8" />
<hkern u1="&#xc0;" u2="&#xe6;" k="8" />
<hkern u1="&#xc0;" u2="&#xdd;" k="72" />
<hkern u1="&#xc0;" u2="&#xd8;" k="29" />
<hkern u1="&#xc0;" u2="&#xd6;" k="29" />
<hkern u1="&#xc0;" u2="&#xd5;" k="29" />
<hkern u1="&#xc0;" u2="&#xd4;" k="29" />
<hkern u1="&#xc0;" u2="&#xd3;" k="29" />
<hkern u1="&#xc0;" u2="&#xd2;" k="29" />
<hkern u1="&#xc0;" u2="&#xc6;" k="-14" />
<hkern u1="&#xc0;" u2="&#xc5;" k="-14" />
<hkern u1="&#xc0;" u2="&#xc4;" k="-14" />
<hkern u1="&#xc0;" u2="&#xc3;" k="-14" />
<hkern u1="&#xc0;" u2="&#xc2;" k="-14" />
<hkern u1="&#xc0;" u2="&#xc1;" k="-14" />
<hkern u1="&#xc0;" u2="&#xc0;" k="-14" />
<hkern u1="&#xc0;" u2="&#xae;" k="14" />
<hkern u1="&#xc0;" u2="&#xa9;" k="14" />
<hkern u1="&#xc0;" u2="z" k="-14" />
<hkern u1="&#xc0;" u2="y" k="43" />
<hkern u1="&#xc0;" u2="x" k="-29" />
<hkern u1="&#xc0;" u2="w" k="14" />
<hkern u1="&#xc0;" u2="v" k="43" />
<hkern u1="&#xc0;" u2="u" k="8" />
<hkern u1="&#xc0;" u2="t" k="8" />
<hkern u1="&#xc0;" u2="r" k="8" />
<hkern u1="&#xc0;" u2="q" k="8" />
<hkern u1="&#xc0;" u2="p" k="8" />
<hkern u1="&#xc0;" u2="o" k="8" />
<hkern u1="&#xc0;" u2="n" k="8" />
<hkern u1="&#xc0;" u2="m" k="8" />
<hkern u1="&#xc0;" u2="e" k="8" />
<hkern u1="&#xc0;" u2="d" k="8" />
<hkern u1="&#xc0;" u2="c" k="8" />
<hkern u1="&#xc0;" u2="a" k="8" />
<hkern u1="&#xc0;" u2="Y" k="72" />
<hkern u1="&#xc0;" u2="X" k="-14" />
<hkern u1="&#xc0;" u2="W" k="72" />
<hkern u1="&#xc0;" u2="V" k="72" />
<hkern u1="&#xc0;" u2="T" k="100" />
<hkern u1="&#xc0;" u2="S" k="-8" />
<hkern u1="&#xc0;" u2="Q" k="29" />
<hkern u1="&#xc0;" u2="O" k="29" />
<hkern u1="&#xc0;" u2="J" k="-14" />
<hkern u1="&#xc0;" u2="G" k="29" />
<hkern u1="&#xc0;" u2="C" k="29" />
<hkern u1="&#xc0;" u2="A" k="-14" />
<hkern u1="&#xc0;" u2="&#x40;" k="14" />
<hkern u1="&#xc0;" u2="&#x2e;" k="-43" />
<hkern u1="&#xc0;" u2="&#x2d;" k="-14" />
<hkern u1="&#xc0;" u2="&#x2c;" k="-43" />
<hkern u1="&#xc0;" u2="&#x2a;" k="86" />
<hkern u1="&#xc1;" u2="&#x2122;" k="100" />
<hkern u1="&#xc1;" u2="&#x2026;" k="-43" />
<hkern u1="&#xc1;" u2="&#x201e;" k="-43" />
<hkern u1="&#xc1;" u2="&#x201d;" k="43" />
<hkern u1="&#xc1;" u2="&#x201c;" k="86" />
<hkern u1="&#xc1;" u2="&#x201a;" k="-43" />
<hkern u1="&#xc1;" u2="&#x2019;" k="43" />
<hkern u1="&#xc1;" u2="&#x2018;" k="86" />
<hkern u1="&#xc1;" u2="&#x2014;" k="-14" />
<hkern u1="&#xc1;" u2="&#x2013;" k="-14" />
<hkern u1="&#xc1;" u2="&#x178;" k="72" />
<hkern u1="&#xc1;" u2="&#x153;" k="8" />
<hkern u1="&#xc1;" u2="&#x152;" k="29" />
<hkern u1="&#xc1;" u2="&#xe7;" k="8" />
<hkern u1="&#xc1;" u2="&#xe6;" k="8" />
<hkern u1="&#xc1;" u2="&#xdd;" k="72" />
<hkern u1="&#xc1;" u2="&#xd8;" k="29" />
<hkern u1="&#xc1;" u2="&#xd6;" k="29" />
<hkern u1="&#xc1;" u2="&#xd5;" k="29" />
<hkern u1="&#xc1;" u2="&#xd4;" k="29" />
<hkern u1="&#xc1;" u2="&#xd3;" k="29" />
<hkern u1="&#xc1;" u2="&#xd2;" k="29" />
<hkern u1="&#xc1;" u2="&#xc6;" k="-14" />
<hkern u1="&#xc1;" u2="&#xc5;" k="-14" />
<hkern u1="&#xc1;" u2="&#xc4;" k="-14" />
<hkern u1="&#xc1;" u2="&#xc3;" k="-14" />
<hkern u1="&#xc1;" u2="&#xc2;" k="-14" />
<hkern u1="&#xc1;" u2="&#xc1;" k="-14" />
<hkern u1="&#xc1;" u2="&#xc0;" k="-14" />
<hkern u1="&#xc1;" u2="&#xae;" k="14" />
<hkern u1="&#xc1;" u2="&#xa9;" k="14" />
<hkern u1="&#xc1;" u2="z" k="-14" />
<hkern u1="&#xc1;" u2="y" k="43" />
<hkern u1="&#xc1;" u2="x" k="-29" />
<hkern u1="&#xc1;" u2="w" k="14" />
<hkern u1="&#xc1;" u2="v" k="43" />
<hkern u1="&#xc1;" u2="u" k="8" />
<hkern u1="&#xc1;" u2="t" k="8" />
<hkern u1="&#xc1;" u2="r" k="8" />
<hkern u1="&#xc1;" u2="q" k="8" />
<hkern u1="&#xc1;" u2="p" k="8" />
<hkern u1="&#xc1;" u2="o" k="8" />
<hkern u1="&#xc1;" u2="n" k="8" />
<hkern u1="&#xc1;" u2="m" k="8" />
<hkern u1="&#xc1;" u2="e" k="8" />
<hkern u1="&#xc1;" u2="d" k="8" />
<hkern u1="&#xc1;" u2="c" k="8" />
<hkern u1="&#xc1;" u2="a" k="8" />
<hkern u1="&#xc1;" u2="Y" k="72" />
<hkern u1="&#xc1;" u2="X" k="-14" />
<hkern u1="&#xc1;" u2="W" k="72" />
<hkern u1="&#xc1;" u2="V" k="72" />
<hkern u1="&#xc1;" u2="T" k="100" />
<hkern u1="&#xc1;" u2="S" k="-8" />
<hkern u1="&#xc1;" u2="Q" k="29" />
<hkern u1="&#xc1;" u2="O" k="29" />
<hkern u1="&#xc1;" u2="J" k="-14" />
<hkern u1="&#xc1;" u2="G" k="29" />
<hkern u1="&#xc1;" u2="C" k="29" />
<hkern u1="&#xc1;" u2="A" k="-14" />
<hkern u1="&#xc1;" u2="&#x40;" k="14" />
<hkern u1="&#xc1;" u2="&#x2e;" k="-43" />
<hkern u1="&#xc1;" u2="&#x2d;" k="-14" />
<hkern u1="&#xc1;" u2="&#x2c;" k="-43" />
<hkern u1="&#xc1;" u2="&#x2a;" k="86" />
<hkern u1="&#xc2;" u2="&#x2122;" k="100" />
<hkern u1="&#xc2;" u2="&#x2026;" k="-43" />
<hkern u1="&#xc2;" u2="&#x201e;" k="-43" />
<hkern u1="&#xc2;" u2="&#x201d;" k="43" />
<hkern u1="&#xc2;" u2="&#x201c;" k="86" />
<hkern u1="&#xc2;" u2="&#x201a;" k="-43" />
<hkern u1="&#xc2;" u2="&#x2019;" k="43" />
<hkern u1="&#xc2;" u2="&#x2018;" k="86" />
<hkern u1="&#xc2;" u2="&#x2014;" k="-14" />
<hkern u1="&#xc2;" u2="&#x2013;" k="-14" />
<hkern u1="&#xc2;" u2="&#x178;" k="72" />
<hkern u1="&#xc2;" u2="&#x153;" k="8" />
<hkern u1="&#xc2;" u2="&#x152;" k="29" />
<hkern u1="&#xc2;" u2="&#xe7;" k="8" />
<hkern u1="&#xc2;" u2="&#xe6;" k="8" />
<hkern u1="&#xc2;" u2="&#xdd;" k="72" />
<hkern u1="&#xc2;" u2="&#xd8;" k="29" />
<hkern u1="&#xc2;" u2="&#xd6;" k="29" />
<hkern u1="&#xc2;" u2="&#xd5;" k="29" />
<hkern u1="&#xc2;" u2="&#xd4;" k="29" />
<hkern u1="&#xc2;" u2="&#xd3;" k="29" />
<hkern u1="&#xc2;" u2="&#xd2;" k="29" />
<hkern u1="&#xc2;" u2="&#xc6;" k="-14" />
<hkern u1="&#xc2;" u2="&#xc5;" k="-14" />
<hkern u1="&#xc2;" u2="&#xc4;" k="-14" />
<hkern u1="&#xc2;" u2="&#xc3;" k="-14" />
<hkern u1="&#xc2;" u2="&#xc2;" k="-14" />
<hkern u1="&#xc2;" u2="&#xc1;" k="-14" />
<hkern u1="&#xc2;" u2="&#xc0;" k="-14" />
<hkern u1="&#xc2;" u2="&#xae;" k="14" />
<hkern u1="&#xc2;" u2="&#xa9;" k="14" />
<hkern u1="&#xc2;" u2="z" k="-14" />
<hkern u1="&#xc2;" u2="y" k="43" />
<hkern u1="&#xc2;" u2="x" k="-29" />
<hkern u1="&#xc2;" u2="w" k="14" />
<hkern u1="&#xc2;" u2="v" k="43" />
<hkern u1="&#xc2;" u2="u" k="8" />
<hkern u1="&#xc2;" u2="t" k="8" />
<hkern u1="&#xc2;" u2="r" k="8" />
<hkern u1="&#xc2;" u2="q" k="8" />
<hkern u1="&#xc2;" u2="p" k="8" />
<hkern u1="&#xc2;" u2="o" k="8" />
<hkern u1="&#xc2;" u2="n" k="8" />
<hkern u1="&#xc2;" u2="m" k="8" />
<hkern u1="&#xc2;" u2="e" k="8" />
<hkern u1="&#xc2;" u2="d" k="8" />
<hkern u1="&#xc2;" u2="c" k="8" />
<hkern u1="&#xc2;" u2="a" k="8" />
<hkern u1="&#xc2;" u2="Y" k="72" />
<hkern u1="&#xc2;" u2="X" k="-14" />
<hkern u1="&#xc2;" u2="W" k="72" />
<hkern u1="&#xc2;" u2="V" k="72" />
<hkern u1="&#xc2;" u2="T" k="100" />
<hkern u1="&#xc2;" u2="S" k="-8" />
<hkern u1="&#xc2;" u2="Q" k="29" />
<hkern u1="&#xc2;" u2="O" k="29" />
<hkern u1="&#xc2;" u2="J" k="-14" />
<hkern u1="&#xc2;" u2="G" k="29" />
<hkern u1="&#xc2;" u2="C" k="29" />
<hkern u1="&#xc2;" u2="A" k="-14" />
<hkern u1="&#xc2;" u2="&#x40;" k="14" />
<hkern u1="&#xc2;" u2="&#x2e;" k="-43" />
<hkern u1="&#xc2;" u2="&#x2d;" k="-14" />
<hkern u1="&#xc2;" u2="&#x2c;" k="-43" />
<hkern u1="&#xc2;" u2="&#x2a;" k="86" />
<hkern u1="&#xc3;" u2="&#x2122;" k="100" />
<hkern u1="&#xc3;" u2="&#x2026;" k="-43" />
<hkern u1="&#xc3;" u2="&#x201e;" k="-43" />
<hkern u1="&#xc3;" u2="&#x201d;" k="43" />
<hkern u1="&#xc3;" u2="&#x201c;" k="86" />
<hkern u1="&#xc3;" u2="&#x201a;" k="-43" />
<hkern u1="&#xc3;" u2="&#x2019;" k="43" />
<hkern u1="&#xc3;" u2="&#x2018;" k="86" />
<hkern u1="&#xc3;" u2="&#x2014;" k="-14" />
<hkern u1="&#xc3;" u2="&#x2013;" k="-14" />
<hkern u1="&#xc3;" u2="&#x178;" k="72" />
<hkern u1="&#xc3;" u2="&#x153;" k="8" />
<hkern u1="&#xc3;" u2="&#x152;" k="29" />
<hkern u1="&#xc3;" u2="&#xe7;" k="8" />
<hkern u1="&#xc3;" u2="&#xe6;" k="8" />
<hkern u1="&#xc3;" u2="&#xdd;" k="72" />
<hkern u1="&#xc3;" u2="&#xd8;" k="29" />
<hkern u1="&#xc3;" u2="&#xd6;" k="29" />
<hkern u1="&#xc3;" u2="&#xd5;" k="29" />
<hkern u1="&#xc3;" u2="&#xd4;" k="29" />
<hkern u1="&#xc3;" u2="&#xd3;" k="29" />
<hkern u1="&#xc3;" u2="&#xd2;" k="29" />
<hkern u1="&#xc3;" u2="&#xc6;" k="-14" />
<hkern u1="&#xc3;" u2="&#xc5;" k="-14" />
<hkern u1="&#xc3;" u2="&#xc4;" k="-14" />
<hkern u1="&#xc3;" u2="&#xc3;" k="-14" />
<hkern u1="&#xc3;" u2="&#xc2;" k="-14" />
<hkern u1="&#xc3;" u2="&#xc1;" k="-14" />
<hkern u1="&#xc3;" u2="&#xc0;" k="-14" />
<hkern u1="&#xc3;" u2="&#xae;" k="14" />
<hkern u1="&#xc3;" u2="&#xa9;" k="14" />
<hkern u1="&#xc3;" u2="z" k="-14" />
<hkern u1="&#xc3;" u2="y" k="43" />
<hkern u1="&#xc3;" u2="x" k="-29" />
<hkern u1="&#xc3;" u2="w" k="14" />
<hkern u1="&#xc3;" u2="v" k="43" />
<hkern u1="&#xc3;" u2="u" k="8" />
<hkern u1="&#xc3;" u2="t" k="8" />
<hkern u1="&#xc3;" u2="r" k="8" />
<hkern u1="&#xc3;" u2="q" k="8" />
<hkern u1="&#xc3;" u2="p" k="8" />
<hkern u1="&#xc3;" u2="o" k="8" />
<hkern u1="&#xc3;" u2="n" k="8" />
<hkern u1="&#xc3;" u2="m" k="8" />
<hkern u1="&#xc3;" u2="e" k="8" />
<hkern u1="&#xc3;" u2="d" k="8" />
<hkern u1="&#xc3;" u2="c" k="8" />
<hkern u1="&#xc3;" u2="a" k="8" />
<hkern u1="&#xc3;" u2="Y" k="72" />
<hkern u1="&#xc3;" u2="X" k="-14" />
<hkern u1="&#xc3;" u2="W" k="72" />
<hkern u1="&#xc3;" u2="V" k="72" />
<hkern u1="&#xc3;" u2="T" k="100" />
<hkern u1="&#xc3;" u2="S" k="-8" />
<hkern u1="&#xc3;" u2="Q" k="29" />
<hkern u1="&#xc3;" u2="O" k="29" />
<hkern u1="&#xc3;" u2="J" k="-14" />
<hkern u1="&#xc3;" u2="G" k="29" />
<hkern u1="&#xc3;" u2="C" k="29" />
<hkern u1="&#xc3;" u2="A" k="-14" />
<hkern u1="&#xc3;" u2="&#x40;" k="14" />
<hkern u1="&#xc3;" u2="&#x2e;" k="-43" />
<hkern u1="&#xc3;" u2="&#x2d;" k="-14" />
<hkern u1="&#xc3;" u2="&#x2c;" k="-43" />
<hkern u1="&#xc3;" u2="&#x2a;" k="86" />
<hkern u1="&#xc4;" u2="&#x2122;" k="100" />
<hkern u1="&#xc4;" u2="&#x2026;" k="-43" />
<hkern u1="&#xc4;" u2="&#x201e;" k="-43" />
<hkern u1="&#xc4;" u2="&#x201d;" k="43" />
<hkern u1="&#xc4;" u2="&#x201c;" k="86" />
<hkern u1="&#xc4;" u2="&#x201a;" k="-43" />
<hkern u1="&#xc4;" u2="&#x2019;" k="43" />
<hkern u1="&#xc4;" u2="&#x2018;" k="86" />
<hkern u1="&#xc4;" u2="&#x2014;" k="-14" />
<hkern u1="&#xc4;" u2="&#x2013;" k="-14" />
<hkern u1="&#xc4;" u2="&#x178;" k="72" />
<hkern u1="&#xc4;" u2="&#x153;" k="8" />
<hkern u1="&#xc4;" u2="&#x152;" k="29" />
<hkern u1="&#xc4;" u2="&#xe7;" k="8" />
<hkern u1="&#xc4;" u2="&#xe6;" k="8" />
<hkern u1="&#xc4;" u2="&#xdd;" k="72" />
<hkern u1="&#xc4;" u2="&#xd8;" k="29" />
<hkern u1="&#xc4;" u2="&#xd6;" k="29" />
<hkern u1="&#xc4;" u2="&#xd5;" k="29" />
<hkern u1="&#xc4;" u2="&#xd4;" k="29" />
<hkern u1="&#xc4;" u2="&#xd3;" k="29" />
<hkern u1="&#xc4;" u2="&#xd2;" k="29" />
<hkern u1="&#xc4;" u2="&#xc6;" k="-14" />
<hkern u1="&#xc4;" u2="&#xc5;" k="-14" />
<hkern u1="&#xc4;" u2="&#xc4;" k="-14" />
<hkern u1="&#xc4;" u2="&#xc3;" k="-14" />
<hkern u1="&#xc4;" u2="&#xc2;" k="-14" />
<hkern u1="&#xc4;" u2="&#xc1;" k="-14" />
<hkern u1="&#xc4;" u2="&#xc0;" k="-14" />
<hkern u1="&#xc4;" u2="&#xae;" k="14" />
<hkern u1="&#xc4;" u2="&#xa9;" k="14" />
<hkern u1="&#xc4;" u2="z" k="-14" />
<hkern u1="&#xc4;" u2="y" k="43" />
<hkern u1="&#xc4;" u2="x" k="-29" />
<hkern u1="&#xc4;" u2="w" k="14" />
<hkern u1="&#xc4;" u2="v" k="43" />
<hkern u1="&#xc4;" u2="u" k="8" />
<hkern u1="&#xc4;" u2="t" k="8" />
<hkern u1="&#xc4;" u2="r" k="8" />
<hkern u1="&#xc4;" u2="q" k="8" />
<hkern u1="&#xc4;" u2="p" k="8" />
<hkern u1="&#xc4;" u2="o" k="8" />
<hkern u1="&#xc4;" u2="n" k="8" />
<hkern u1="&#xc4;" u2="m" k="8" />
<hkern u1="&#xc4;" u2="e" k="8" />
<hkern u1="&#xc4;" u2="d" k="8" />
<hkern u1="&#xc4;" u2="c" k="8" />
<hkern u1="&#xc4;" u2="a" k="8" />
<hkern u1="&#xc4;" u2="Y" k="72" />
<hkern u1="&#xc4;" u2="X" k="-14" />
<hkern u1="&#xc4;" u2="W" k="72" />
<hkern u1="&#xc4;" u2="V" k="72" />
<hkern u1="&#xc4;" u2="T" k="100" />
<hkern u1="&#xc4;" u2="S" k="-8" />
<hkern u1="&#xc4;" u2="Q" k="29" />
<hkern u1="&#xc4;" u2="O" k="29" />
<hkern u1="&#xc4;" u2="J" k="-14" />
<hkern u1="&#xc4;" u2="G" k="29" />
<hkern u1="&#xc4;" u2="C" k="29" />
<hkern u1="&#xc4;" u2="A" k="-14" />
<hkern u1="&#xc4;" u2="&#x40;" k="14" />
<hkern u1="&#xc4;" u2="&#x2e;" k="-43" />
<hkern u1="&#xc4;" u2="&#x2d;" k="-14" />
<hkern u1="&#xc4;" u2="&#x2c;" k="-43" />
<hkern u1="&#xc4;" u2="&#x2a;" k="86" />
<hkern u1="&#xc5;" u2="&#x2122;" k="100" />
<hkern u1="&#xc5;" u2="&#x2026;" k="-43" />
<hkern u1="&#xc5;" u2="&#x201e;" k="-43" />
<hkern u1="&#xc5;" u2="&#x201d;" k="43" />
<hkern u1="&#xc5;" u2="&#x201c;" k="86" />
<hkern u1="&#xc5;" u2="&#x201a;" k="-43" />
<hkern u1="&#xc5;" u2="&#x2019;" k="43" />
<hkern u1="&#xc5;" u2="&#x2018;" k="86" />
<hkern u1="&#xc5;" u2="&#x2014;" k="-14" />
<hkern u1="&#xc5;" u2="&#x2013;" k="-14" />
<hkern u1="&#xc5;" u2="&#x178;" k="72" />
<hkern u1="&#xc5;" u2="&#x153;" k="8" />
<hkern u1="&#xc5;" u2="&#x152;" k="29" />
<hkern u1="&#xc5;" u2="&#xe7;" k="8" />
<hkern u1="&#xc5;" u2="&#xe6;" k="8" />
<hkern u1="&#xc5;" u2="&#xdd;" k="72" />
<hkern u1="&#xc5;" u2="&#xd8;" k="29" />
<hkern u1="&#xc5;" u2="&#xd6;" k="29" />
<hkern u1="&#xc5;" u2="&#xd5;" k="29" />
<hkern u1="&#xc5;" u2="&#xd4;" k="29" />
<hkern u1="&#xc5;" u2="&#xd3;" k="29" />
<hkern u1="&#xc5;" u2="&#xd2;" k="29" />
<hkern u1="&#xc5;" u2="&#xc6;" k="-14" />
<hkern u1="&#xc5;" u2="&#xc5;" k="-14" />
<hkern u1="&#xc5;" u2="&#xc4;" k="-14" />
<hkern u1="&#xc5;" u2="&#xc3;" k="-14" />
<hkern u1="&#xc5;" u2="&#xc2;" k="-14" />
<hkern u1="&#xc5;" u2="&#xc1;" k="-14" />
<hkern u1="&#xc5;" u2="&#xc0;" k="-14" />
<hkern u1="&#xc5;" u2="&#xae;" k="14" />
<hkern u1="&#xc5;" u2="&#xa9;" k="14" />
<hkern u1="&#xc5;" u2="z" k="-14" />
<hkern u1="&#xc5;" u2="y" k="43" />
<hkern u1="&#xc5;" u2="x" k="-29" />
<hkern u1="&#xc5;" u2="w" k="14" />
<hkern u1="&#xc5;" u2="v" k="43" />
<hkern u1="&#xc5;" u2="u" k="8" />
<hkern u1="&#xc5;" u2="t" k="8" />
<hkern u1="&#xc5;" u2="r" k="8" />
<hkern u1="&#xc5;" u2="q" k="8" />
<hkern u1="&#xc5;" u2="p" k="8" />
<hkern u1="&#xc5;" u2="o" k="8" />
<hkern u1="&#xc5;" u2="n" k="8" />
<hkern u1="&#xc5;" u2="m" k="8" />
<hkern u1="&#xc5;" u2="e" k="8" />
<hkern u1="&#xc5;" u2="d" k="8" />
<hkern u1="&#xc5;" u2="c" k="8" />
<hkern u1="&#xc5;" u2="a" k="8" />
<hkern u1="&#xc5;" u2="Y" k="72" />
<hkern u1="&#xc5;" u2="X" k="-14" />
<hkern u1="&#xc5;" u2="W" k="72" />
<hkern u1="&#xc5;" u2="V" k="72" />
<hkern u1="&#xc5;" u2="T" k="100" />
<hkern u1="&#xc5;" u2="S" k="-8" />
<hkern u1="&#xc5;" u2="Q" k="29" />
<hkern u1="&#xc5;" u2="O" k="29" />
<hkern u1="&#xc5;" u2="J" k="-14" />
<hkern u1="&#xc5;" u2="G" k="29" />
<hkern u1="&#xc5;" u2="C" k="29" />
<hkern u1="&#xc5;" u2="A" k="-14" />
<hkern u1="&#xc5;" u2="&#x40;" k="14" />
<hkern u1="&#xc5;" u2="&#x2e;" k="-43" />
<hkern u1="&#xc5;" u2="&#x2d;" k="-14" />
<hkern u1="&#xc5;" u2="&#x2c;" k="-43" />
<hkern u1="&#xc5;" u2="&#x2a;" k="86" />
<hkern u1="&#xc6;" u2="&#x153;" k="51" />
<hkern u1="&#xc6;" u2="&#x152;" k="12" />
<hkern u1="&#xc6;" u2="&#xe7;" k="51" />
<hkern u1="&#xc6;" u2="&#xe6;" k="51" />
<hkern u1="&#xc6;" u2="&#xd8;" k="12" />
<hkern u1="&#xc6;" u2="&#xd6;" k="12" />
<hkern u1="&#xc6;" u2="&#xd5;" k="12" />
<hkern u1="&#xc6;" u2="&#xd4;" k="12" />
<hkern u1="&#xc6;" u2="&#xd3;" k="12" />
<hkern u1="&#xc6;" u2="&#xd2;" k="12" />
<hkern u1="&#xc6;" u2="&#xae;" k="41" />
<hkern u1="&#xc6;" u2="&#xa9;" k="41" />
<hkern u1="&#xc6;" u2="y" k="41" />
<hkern u1="&#xc6;" u2="v" k="41" />
<hkern u1="&#xc6;" u2="q" k="51" />
<hkern u1="&#xc6;" u2="o" k="51" />
<hkern u1="&#xc6;" u2="g" k="10" />
<hkern u1="&#xc6;" u2="f" k="20" />
<hkern u1="&#xc6;" u2="e" k="51" />
<hkern u1="&#xc6;" u2="d" k="51" />
<hkern u1="&#xc6;" u2="c" k="51" />
<hkern u1="&#xc6;" u2="a" k="51" />
<hkern u1="&#xc6;" u2="T" k="-31" />
<hkern u1="&#xc6;" u2="Q" k="12" />
<hkern u1="&#xc6;" u2="O" k="12" />
<hkern u1="&#xc6;" u2="G" k="12" />
<hkern u1="&#xc6;" u2="C" k="12" />
<hkern u1="&#xc6;" u2="&#x40;" k="41" />
<hkern u1="&#xc8;" u2="&#x153;" k="37" />
<hkern u1="&#xc8;" u2="&#xe7;" k="37" />
<hkern u1="&#xc8;" u2="&#xe6;" k="37" />
<hkern u1="&#xc8;" u2="&#xae;" k="29" />
<hkern u1="&#xc8;" u2="&#xa9;" k="29" />
<hkern u1="&#xc8;" u2="y" k="29" />
<hkern u1="&#xc8;" u2="v" k="29" />
<hkern u1="&#xc8;" u2="q" k="37" />
<hkern u1="&#xc8;" u2="o" k="37" />
<hkern u1="&#xc8;" u2="g" k="8" />
<hkern u1="&#xc8;" u2="f" k="14" />
<hkern u1="&#xc8;" u2="e" k="37" />
<hkern u1="&#xc8;" u2="d" k="37" />
<hkern u1="&#xc8;" u2="c" k="37" />
<hkern u1="&#xc8;" u2="a" k="37" />
<hkern u1="&#xc8;" u2="T" k="-23" />
<hkern u1="&#xc8;" u2="&#x40;" k="29" />
<hkern u1="&#xc9;" u2="&#x153;" k="37" />
<hkern u1="&#xc9;" u2="&#xe7;" k="37" />
<hkern u1="&#xc9;" u2="&#xe6;" k="37" />
<hkern u1="&#xc9;" u2="&#xae;" k="29" />
<hkern u1="&#xc9;" u2="&#xa9;" k="29" />
<hkern u1="&#xc9;" u2="y" k="29" />
<hkern u1="&#xc9;" u2="v" k="29" />
<hkern u1="&#xc9;" u2="q" k="37" />
<hkern u1="&#xc9;" u2="o" k="37" />
<hkern u1="&#xc9;" u2="g" k="8" />
<hkern u1="&#xc9;" u2="f" k="14" />
<hkern u1="&#xc9;" u2="e" k="37" />
<hkern u1="&#xc9;" u2="d" k="37" />
<hkern u1="&#xc9;" u2="c" k="37" />
<hkern u1="&#xc9;" u2="a" k="37" />
<hkern u1="&#xc9;" u2="T" k="-23" />
<hkern u1="&#xc9;" u2="&#x40;" k="29" />
<hkern u1="&#xca;" u2="&#x153;" k="37" />
<hkern u1="&#xca;" u2="&#xe7;" k="37" />
<hkern u1="&#xca;" u2="&#xe6;" k="37" />
<hkern u1="&#xca;" u2="&#xae;" k="29" />
<hkern u1="&#xca;" u2="&#xa9;" k="29" />
<hkern u1="&#xca;" u2="y" k="29" />
<hkern u1="&#xca;" u2="v" k="29" />
<hkern u1="&#xca;" u2="q" k="37" />
<hkern u1="&#xca;" u2="o" k="37" />
<hkern u1="&#xca;" u2="g" k="8" />
<hkern u1="&#xca;" u2="f" k="14" />
<hkern u1="&#xca;" u2="e" k="37" />
<hkern u1="&#xca;" u2="d" k="37" />
<hkern u1="&#xca;" u2="c" k="37" />
<hkern u1="&#xca;" u2="a" k="37" />
<hkern u1="&#xca;" u2="T" k="-23" />
<hkern u1="&#xca;" u2="&#x40;" k="29" />
<hkern u1="&#xcb;" u2="&#x153;" k="37" />
<hkern u1="&#xcb;" u2="&#xe7;" k="37" />
<hkern u1="&#xcb;" u2="&#xe6;" k="37" />
<hkern u1="&#xcb;" u2="&#xae;" k="29" />
<hkern u1="&#xcb;" u2="&#xa9;" k="29" />
<hkern u1="&#xcb;" u2="y" k="29" />
<hkern u1="&#xcb;" u2="v" k="29" />
<hkern u1="&#xcb;" u2="q" k="37" />
<hkern u1="&#xcb;" u2="o" k="37" />
<hkern u1="&#xcb;" u2="g" k="8" />
<hkern u1="&#xcb;" u2="f" k="14" />
<hkern u1="&#xcb;" u2="e" k="37" />
<hkern u1="&#xcb;" u2="d" k="37" />
<hkern u1="&#xcb;" u2="c" k="37" />
<hkern u1="&#xcb;" u2="a" k="37" />
<hkern u1="&#xcb;" u2="T" k="-23" />
<hkern u1="&#xcb;" u2="&#x40;" k="29" />
<hkern u1="&#xcc;" u2="y" k="14" />
<hkern u1="&#xcc;" u2="v" k="14" />
<hkern u1="&#xcc;" u2="&#x2f;" k="29" />
<hkern u1="&#xcd;" u2="y" k="14" />
<hkern u1="&#xcd;" u2="v" k="14" />
<hkern u1="&#xcd;" u2="&#x2f;" k="29" />
<hkern u1="&#xce;" u2="y" k="14" />
<hkern u1="&#xce;" u2="v" k="14" />
<hkern u1="&#xce;" u2="&#x2f;" k="29" />
<hkern u1="&#xcf;" u2="y" k="14" />
<hkern u1="&#xcf;" u2="v" k="14" />
<hkern u1="&#xcf;" u2="&#x2f;" k="29" />
<hkern u1="&#xd0;" u2="&#x2026;" k="82" />
<hkern u1="&#xd0;" u2="&#x201e;" k="82" />
<hkern u1="&#xd0;" u2="&#x201c;" k="41" />
<hkern u1="&#xd0;" u2="&#x201a;" k="82" />
<hkern u1="&#xd0;" u2="&#x2018;" k="41" />
<hkern u1="&#xd0;" u2="&#x178;" k="51" />
<hkern u1="&#xd0;" u2="&#x153;" k="10" />
<hkern u1="&#xd0;" u2="&#xe7;" k="10" />
<hkern u1="&#xd0;" u2="&#xe6;" k="10" />
<hkern u1="&#xd0;" u2="&#xdd;" k="51" />
<hkern u1="&#xd0;" u2="&#xc6;" k="35" />
<hkern u1="&#xd0;" u2="&#xc5;" k="35" />
<hkern u1="&#xd0;" u2="&#xc4;" k="35" />
<hkern u1="&#xd0;" u2="&#xc3;" k="35" />
<hkern u1="&#xd0;" u2="&#xc2;" k="35" />
<hkern u1="&#xd0;" u2="&#xc1;" k="35" />
<hkern u1="&#xd0;" u2="&#xc0;" k="35" />
<hkern u1="&#xd0;" u2="z" k="20" />
<hkern u1="&#xd0;" u2="y" k="-10" />
<hkern u1="&#xd0;" u2="x" k="18" />
<hkern u1="&#xd0;" u2="v" k="-10" />
<hkern u1="&#xd0;" u2="u" k="10" />
<hkern u1="&#xd0;" u2="r" k="10" />
<hkern u1="&#xd0;" u2="q" k="10" />
<hkern u1="&#xd0;" u2="p" k="10" />
<hkern u1="&#xd0;" u2="o" k="10" />
<hkern u1="&#xd0;" u2="n" k="10" />
<hkern u1="&#xd0;" u2="m" k="10" />
<hkern u1="&#xd0;" u2="l" k="20" />
<hkern u1="&#xd0;" u2="k" k="20" />
<hkern u1="&#xd0;" u2="h" k="20" />
<hkern u1="&#xd0;" u2="e" k="10" />
<hkern u1="&#xd0;" u2="d" k="10" />
<hkern u1="&#xd0;" u2="c" k="10" />
<hkern u1="&#xd0;" u2="b" k="20" />
<hkern u1="&#xd0;" u2="a" k="10" />
<hkern u1="&#xd0;" u2="Z" k="92" />
<hkern u1="&#xd0;" u2="Y" k="51" />
<hkern u1="&#xd0;" u2="X" k="51" />
<hkern u1="&#xd0;" u2="W" k="68" />
<hkern u1="&#xd0;" u2="V" k="39" />
<hkern u1="&#xd0;" u2="T" k="66" />
<hkern u1="&#xd0;" u2="J" k="92" />
<hkern u1="&#xd0;" u2="A" k="35" />
<hkern u1="&#xd0;" u2="&#x3f;" k="41" />
<hkern u1="&#xd0;" u2="&#x2f;" k="102" />
<hkern u1="&#xd0;" u2="&#x2e;" k="82" />
<hkern u1="&#xd0;" u2="&#x2c;" k="82" />
<hkern u1="&#xd1;" u2="y" k="14" />
<hkern u1="&#xd1;" u2="v" k="14" />
<hkern u1="&#xd1;" u2="&#x2f;" k="29" />
<hkern u1="&#xd2;" u2="&#x2026;" k="57" />
<hkern u1="&#xd2;" u2="&#x201e;" k="57" />
<hkern u1="&#xd2;" u2="&#x201c;" k="29" />
<hkern u1="&#xd2;" u2="&#x201a;" k="57" />
<hkern u1="&#xd2;" u2="&#x2018;" k="29" />
<hkern u1="&#xd2;" u2="&#x178;" k="37" />
<hkern u1="&#xd2;" u2="&#x153;" k="8" />
<hkern u1="&#xd2;" u2="&#xe7;" k="8" />
<hkern u1="&#xd2;" u2="&#xe6;" k="8" />
<hkern u1="&#xd2;" u2="&#xdd;" k="37" />
<hkern u1="&#xd2;" u2="&#xc6;" k="29" />
<hkern u1="&#xd2;" u2="&#xc5;" k="29" />
<hkern u1="&#xd2;" u2="&#xc4;" k="29" />
<hkern u1="&#xd2;" u2="&#xc3;" k="29" />
<hkern u1="&#xd2;" u2="&#xc2;" k="29" />
<hkern u1="&#xd2;" u2="&#xc1;" k="29" />
<hkern u1="&#xd2;" u2="&#xc0;" k="29" />
<hkern u1="&#xd2;" u2="z" k="14" />
<hkern u1="&#xd2;" u2="x" k="14" />
<hkern u1="&#xd2;" u2="u" k="8" />
<hkern u1="&#xd2;" u2="r" k="8" />
<hkern u1="&#xd2;" u2="q" k="8" />
<hkern u1="&#xd2;" u2="p" k="8" />
<hkern u1="&#xd2;" u2="o" k="8" />
<hkern u1="&#xd2;" u2="n" k="8" />
<hkern u1="&#xd2;" u2="m" k="8" />
<hkern u1="&#xd2;" u2="l" k="14" />
<hkern u1="&#xd2;" u2="k" k="14" />
<hkern u1="&#xd2;" u2="h" k="14" />
<hkern u1="&#xd2;" u2="e" k="8" />
<hkern u1="&#xd2;" u2="d" k="8" />
<hkern u1="&#xd2;" u2="c" k="8" />
<hkern u1="&#xd2;" u2="b" k="14" />
<hkern u1="&#xd2;" u2="a" k="8" />
<hkern u1="&#xd2;" u2="Z" k="63" />
<hkern u1="&#xd2;" u2="Y" k="37" />
<hkern u1="&#xd2;" u2="X" k="37" />
<hkern u1="&#xd2;" u2="W" k="57" />
<hkern u1="&#xd2;" u2="V" k="29" />
<hkern u1="&#xd2;" u2="T" k="43" />
<hkern u1="&#xd2;" u2="J" k="63" />
<hkern u1="&#xd2;" u2="A" k="29" />
<hkern u1="&#xd2;" u2="&#x3f;" k="29" />
<hkern u1="&#xd2;" u2="&#x2f;" k="72" />
<hkern u1="&#xd2;" u2="&#x2e;" k="57" />
<hkern u1="&#xd2;" u2="&#x2c;" k="57" />
<hkern u1="&#xd3;" u2="&#x2026;" k="57" />
<hkern u1="&#xd3;" u2="&#x201e;" k="57" />
<hkern u1="&#xd3;" u2="&#x201c;" k="29" />
<hkern u1="&#xd3;" u2="&#x201a;" k="57" />
<hkern u1="&#xd3;" u2="&#x2018;" k="29" />
<hkern u1="&#xd3;" u2="&#x178;" k="37" />
<hkern u1="&#xd3;" u2="&#x153;" k="8" />
<hkern u1="&#xd3;" u2="&#xe7;" k="8" />
<hkern u1="&#xd3;" u2="&#xe6;" k="8" />
<hkern u1="&#xd3;" u2="&#xdd;" k="37" />
<hkern u1="&#xd3;" u2="&#xc6;" k="29" />
<hkern u1="&#xd3;" u2="&#xc5;" k="29" />
<hkern u1="&#xd3;" u2="&#xc4;" k="29" />
<hkern u1="&#xd3;" u2="&#xc3;" k="29" />
<hkern u1="&#xd3;" u2="&#xc2;" k="29" />
<hkern u1="&#xd3;" u2="&#xc1;" k="29" />
<hkern u1="&#xd3;" u2="&#xc0;" k="29" />
<hkern u1="&#xd3;" u2="z" k="14" />
<hkern u1="&#xd3;" u2="x" k="14" />
<hkern u1="&#xd3;" u2="u" k="8" />
<hkern u1="&#xd3;" u2="r" k="8" />
<hkern u1="&#xd3;" u2="q" k="8" />
<hkern u1="&#xd3;" u2="p" k="8" />
<hkern u1="&#xd3;" u2="o" k="8" />
<hkern u1="&#xd3;" u2="n" k="8" />
<hkern u1="&#xd3;" u2="m" k="8" />
<hkern u1="&#xd3;" u2="l" k="14" />
<hkern u1="&#xd3;" u2="k" k="14" />
<hkern u1="&#xd3;" u2="h" k="14" />
<hkern u1="&#xd3;" u2="e" k="8" />
<hkern u1="&#xd3;" u2="d" k="8" />
<hkern u1="&#xd3;" u2="c" k="8" />
<hkern u1="&#xd3;" u2="b" k="14" />
<hkern u1="&#xd3;" u2="a" k="8" />
<hkern u1="&#xd3;" u2="Z" k="63" />
<hkern u1="&#xd3;" u2="Y" k="37" />
<hkern u1="&#xd3;" u2="X" k="37" />
<hkern u1="&#xd3;" u2="W" k="57" />
<hkern u1="&#xd3;" u2="V" k="29" />
<hkern u1="&#xd3;" u2="T" k="43" />
<hkern u1="&#xd3;" u2="J" k="63" />
<hkern u1="&#xd3;" u2="A" k="29" />
<hkern u1="&#xd3;" u2="&#x3f;" k="29" />
<hkern u1="&#xd3;" u2="&#x2f;" k="72" />
<hkern u1="&#xd3;" u2="&#x2e;" k="57" />
<hkern u1="&#xd3;" u2="&#x2c;" k="57" />
<hkern u1="&#xd4;" u2="&#x2026;" k="57" />
<hkern u1="&#xd4;" u2="&#x201e;" k="57" />
<hkern u1="&#xd4;" u2="&#x201c;" k="29" />
<hkern u1="&#xd4;" u2="&#x201a;" k="57" />
<hkern u1="&#xd4;" u2="&#x2018;" k="29" />
<hkern u1="&#xd4;" u2="&#x178;" k="37" />
<hkern u1="&#xd4;" u2="&#x153;" k="8" />
<hkern u1="&#xd4;" u2="&#xe7;" k="8" />
<hkern u1="&#xd4;" u2="&#xe6;" k="8" />
<hkern u1="&#xd4;" u2="&#xdd;" k="37" />
<hkern u1="&#xd4;" u2="&#xc6;" k="29" />
<hkern u1="&#xd4;" u2="&#xc5;" k="29" />
<hkern u1="&#xd4;" u2="&#xc4;" k="29" />
<hkern u1="&#xd4;" u2="&#xc3;" k="29" />
<hkern u1="&#xd4;" u2="&#xc2;" k="29" />
<hkern u1="&#xd4;" u2="&#xc1;" k="29" />
<hkern u1="&#xd4;" u2="&#xc0;" k="29" />
<hkern u1="&#xd4;" u2="z" k="14" />
<hkern u1="&#xd4;" u2="x" k="14" />
<hkern u1="&#xd4;" u2="u" k="8" />
<hkern u1="&#xd4;" u2="r" k="8" />
<hkern u1="&#xd4;" u2="q" k="8" />
<hkern u1="&#xd4;" u2="p" k="8" />
<hkern u1="&#xd4;" u2="o" k="8" />
<hkern u1="&#xd4;" u2="n" k="8" />
<hkern u1="&#xd4;" u2="m" k="8" />
<hkern u1="&#xd4;" u2="l" k="14" />
<hkern u1="&#xd4;" u2="k" k="14" />
<hkern u1="&#xd4;" u2="h" k="14" />
<hkern u1="&#xd4;" u2="e" k="8" />
<hkern u1="&#xd4;" u2="d" k="8" />
<hkern u1="&#xd4;" u2="c" k="8" />
<hkern u1="&#xd4;" u2="b" k="14" />
<hkern u1="&#xd4;" u2="a" k="8" />
<hkern u1="&#xd4;" u2="Z" k="63" />
<hkern u1="&#xd4;" u2="Y" k="37" />
<hkern u1="&#xd4;" u2="X" k="37" />
<hkern u1="&#xd4;" u2="W" k="57" />
<hkern u1="&#xd4;" u2="V" k="29" />
<hkern u1="&#xd4;" u2="T" k="43" />
<hkern u1="&#xd4;" u2="J" k="63" />
<hkern u1="&#xd4;" u2="A" k="29" />
<hkern u1="&#xd4;" u2="&#x3f;" k="29" />
<hkern u1="&#xd4;" u2="&#x2f;" k="72" />
<hkern u1="&#xd4;" u2="&#x2e;" k="57" />
<hkern u1="&#xd4;" u2="&#x2c;" k="57" />
<hkern u1="&#xd5;" u2="&#x2026;" k="57" />
<hkern u1="&#xd5;" u2="&#x201e;" k="57" />
<hkern u1="&#xd5;" u2="&#x201c;" k="29" />
<hkern u1="&#xd5;" u2="&#x201a;" k="57" />
<hkern u1="&#xd5;" u2="&#x2018;" k="29" />
<hkern u1="&#xd5;" u2="&#x178;" k="37" />
<hkern u1="&#xd5;" u2="&#x153;" k="8" />
<hkern u1="&#xd5;" u2="&#xe7;" k="8" />
<hkern u1="&#xd5;" u2="&#xe6;" k="8" />
<hkern u1="&#xd5;" u2="&#xdd;" k="37" />
<hkern u1="&#xd5;" u2="&#xc6;" k="29" />
<hkern u1="&#xd5;" u2="&#xc5;" k="29" />
<hkern u1="&#xd5;" u2="&#xc4;" k="29" />
<hkern u1="&#xd5;" u2="&#xc3;" k="29" />
<hkern u1="&#xd5;" u2="&#xc2;" k="29" />
<hkern u1="&#xd5;" u2="&#xc1;" k="29" />
<hkern u1="&#xd5;" u2="&#xc0;" k="29" />
<hkern u1="&#xd5;" u2="z" k="14" />
<hkern u1="&#xd5;" u2="x" k="14" />
<hkern u1="&#xd5;" u2="u" k="8" />
<hkern u1="&#xd5;" u2="r" k="8" />
<hkern u1="&#xd5;" u2="q" k="8" />
<hkern u1="&#xd5;" u2="p" k="8" />
<hkern u1="&#xd5;" u2="o" k="8" />
<hkern u1="&#xd5;" u2="n" k="8" />
<hkern u1="&#xd5;" u2="m" k="8" />
<hkern u1="&#xd5;" u2="l" k="14" />
<hkern u1="&#xd5;" u2="k" k="14" />
<hkern u1="&#xd5;" u2="h" k="14" />
<hkern u1="&#xd5;" u2="e" k="8" />
<hkern u1="&#xd5;" u2="d" k="8" />
<hkern u1="&#xd5;" u2="c" k="8" />
<hkern u1="&#xd5;" u2="b" k="14" />
<hkern u1="&#xd5;" u2="a" k="8" />
<hkern u1="&#xd5;" u2="Z" k="63" />
<hkern u1="&#xd5;" u2="Y" k="37" />
<hkern u1="&#xd5;" u2="X" k="37" />
<hkern u1="&#xd5;" u2="W" k="57" />
<hkern u1="&#xd5;" u2="V" k="29" />
<hkern u1="&#xd5;" u2="T" k="43" />
<hkern u1="&#xd5;" u2="J" k="63" />
<hkern u1="&#xd5;" u2="A" k="29" />
<hkern u1="&#xd5;" u2="&#x3f;" k="29" />
<hkern u1="&#xd5;" u2="&#x2f;" k="72" />
<hkern u1="&#xd5;" u2="&#x2e;" k="57" />
<hkern u1="&#xd5;" u2="&#x2c;" k="57" />
<hkern u1="&#xd6;" u2="&#x2026;" k="57" />
<hkern u1="&#xd6;" u2="&#x201e;" k="57" />
<hkern u1="&#xd6;" u2="&#x201c;" k="29" />
<hkern u1="&#xd6;" u2="&#x201a;" k="57" />
<hkern u1="&#xd6;" u2="&#x2018;" k="29" />
<hkern u1="&#xd6;" u2="&#x178;" k="37" />
<hkern u1="&#xd6;" u2="&#x153;" k="8" />
<hkern u1="&#xd6;" u2="&#xe7;" k="8" />
<hkern u1="&#xd6;" u2="&#xe6;" k="8" />
<hkern u1="&#xd6;" u2="&#xdd;" k="37" />
<hkern u1="&#xd6;" u2="&#xc6;" k="29" />
<hkern u1="&#xd6;" u2="&#xc5;" k="29" />
<hkern u1="&#xd6;" u2="&#xc4;" k="29" />
<hkern u1="&#xd6;" u2="&#xc3;" k="29" />
<hkern u1="&#xd6;" u2="&#xc2;" k="29" />
<hkern u1="&#xd6;" u2="&#xc1;" k="29" />
<hkern u1="&#xd6;" u2="&#xc0;" k="29" />
<hkern u1="&#xd6;" u2="z" k="14" />
<hkern u1="&#xd6;" u2="x" k="14" />
<hkern u1="&#xd6;" u2="u" k="8" />
<hkern u1="&#xd6;" u2="r" k="8" />
<hkern u1="&#xd6;" u2="q" k="8" />
<hkern u1="&#xd6;" u2="p" k="8" />
<hkern u1="&#xd6;" u2="o" k="8" />
<hkern u1="&#xd6;" u2="n" k="8" />
<hkern u1="&#xd6;" u2="m" k="8" />
<hkern u1="&#xd6;" u2="l" k="14" />
<hkern u1="&#xd6;" u2="k" k="14" />
<hkern u1="&#xd6;" u2="h" k="14" />
<hkern u1="&#xd6;" u2="e" k="8" />
<hkern u1="&#xd6;" u2="d" k="8" />
<hkern u1="&#xd6;" u2="c" k="8" />
<hkern u1="&#xd6;" u2="b" k="14" />
<hkern u1="&#xd6;" u2="a" k="8" />
<hkern u1="&#xd6;" u2="Z" k="63" />
<hkern u1="&#xd6;" u2="Y" k="37" />
<hkern u1="&#xd6;" u2="X" k="37" />
<hkern u1="&#xd6;" u2="W" k="57" />
<hkern u1="&#xd6;" u2="V" k="29" />
<hkern u1="&#xd6;" u2="T" k="43" />
<hkern u1="&#xd6;" u2="J" k="63" />
<hkern u1="&#xd6;" u2="A" k="29" />
<hkern u1="&#xd6;" u2="&#x3f;" k="29" />
<hkern u1="&#xd6;" u2="&#x2f;" k="72" />
<hkern u1="&#xd6;" u2="&#x2e;" k="57" />
<hkern u1="&#xd6;" u2="&#x2c;" k="57" />
<hkern u1="&#xd8;" u2="&#x2026;" k="82" />
<hkern u1="&#xd8;" u2="&#x201e;" k="82" />
<hkern u1="&#xd8;" u2="&#x201c;" k="41" />
<hkern u1="&#xd8;" u2="&#x201a;" k="82" />
<hkern u1="&#xd8;" u2="&#x2018;" k="41" />
<hkern u1="&#xd8;" u2="&#x178;" k="51" />
<hkern u1="&#xd8;" u2="&#x153;" k="10" />
<hkern u1="&#xd8;" u2="&#xe7;" k="10" />
<hkern u1="&#xd8;" u2="&#xe6;" k="10" />
<hkern u1="&#xd8;" u2="&#xdd;" k="51" />
<hkern u1="&#xd8;" u2="&#xc6;" k="35" />
<hkern u1="&#xd8;" u2="&#xc5;" k="35" />
<hkern u1="&#xd8;" u2="&#xc4;" k="35" />
<hkern u1="&#xd8;" u2="&#xc3;" k="35" />
<hkern u1="&#xd8;" u2="&#xc2;" k="35" />
<hkern u1="&#xd8;" u2="&#xc1;" k="35" />
<hkern u1="&#xd8;" u2="&#xc0;" k="35" />
<hkern u1="&#xd8;" u2="z" k="20" />
<hkern u1="&#xd8;" u2="y" k="-10" />
<hkern u1="&#xd8;" u2="x" k="18" />
<hkern u1="&#xd8;" u2="v" k="-10" />
<hkern u1="&#xd8;" u2="u" k="10" />
<hkern u1="&#xd8;" u2="r" k="10" />
<hkern u1="&#xd8;" u2="q" k="10" />
<hkern u1="&#xd8;" u2="p" k="10" />
<hkern u1="&#xd8;" u2="o" k="10" />
<hkern u1="&#xd8;" u2="n" k="10" />
<hkern u1="&#xd8;" u2="m" k="10" />
<hkern u1="&#xd8;" u2="l" k="20" />
<hkern u1="&#xd8;" u2="k" k="20" />
<hkern u1="&#xd8;" u2="h" k="20" />
<hkern u1="&#xd8;" u2="e" k="10" />
<hkern u1="&#xd8;" u2="d" k="10" />
<hkern u1="&#xd8;" u2="c" k="10" />
<hkern u1="&#xd8;" u2="b" k="20" />
<hkern u1="&#xd8;" u2="a" k="10" />
<hkern u1="&#xd8;" u2="Z" k="92" />
<hkern u1="&#xd8;" u2="Y" k="51" />
<hkern u1="&#xd8;" u2="X" k="51" />
<hkern u1="&#xd8;" u2="W" k="68" />
<hkern u1="&#xd8;" u2="V" k="39" />
<hkern u1="&#xd8;" u2="T" k="66" />
<hkern u1="&#xd8;" u2="J" k="92" />
<hkern u1="&#xd8;" u2="A" k="35" />
<hkern u1="&#xd8;" u2="&#x3f;" k="41" />
<hkern u1="&#xd8;" u2="&#x2f;" k="102" />
<hkern u1="&#xd8;" u2="&#x2e;" k="82" />
<hkern u1="&#xd8;" u2="&#x2c;" k="82" />
<hkern u1="&#xd9;" u2="&#x2026;" k="14" />
<hkern u1="&#xd9;" u2="&#x201e;" k="14" />
<hkern u1="&#xd9;" u2="&#x201a;" k="14" />
<hkern u1="&#xd9;" u2="J" k="14" />
<hkern u1="&#xd9;" u2="&#x2e;" k="14" />
<hkern u1="&#xd9;" u2="&#x2c;" k="14" />
<hkern u1="&#xda;" u2="&#x2026;" k="14" />
<hkern u1="&#xda;" u2="&#x201e;" k="14" />
<hkern u1="&#xda;" u2="&#x201a;" k="14" />
<hkern u1="&#xda;" u2="J" k="14" />
<hkern u1="&#xda;" u2="&#x2e;" k="14" />
<hkern u1="&#xda;" u2="&#x2c;" k="14" />
<hkern u1="&#xdb;" u2="&#x2026;" k="14" />
<hkern u1="&#xdb;" u2="&#x201e;" k="14" />
<hkern u1="&#xdb;" u2="&#x201a;" k="14" />
<hkern u1="&#xdb;" u2="J" k="14" />
<hkern u1="&#xdb;" u2="&#x2e;" k="14" />
<hkern u1="&#xdb;" u2="&#x2c;" k="14" />
<hkern u1="&#xdc;" u2="&#x2026;" k="14" />
<hkern u1="&#xdc;" u2="&#x201e;" k="14" />
<hkern u1="&#xdc;" u2="&#x201a;" k="14" />
<hkern u1="&#xdc;" u2="J" k="14" />
<hkern u1="&#xdc;" u2="&#x2e;" k="14" />
<hkern u1="&#xdc;" u2="&#x2c;" k="14" />
<hkern u1="&#xdd;" u2="&#x203a;" k="82" />
<hkern u1="&#xdd;" u2="&#x2039;" k="123" />
<hkern u1="&#xdd;" u2="&#x2026;" k="102" />
<hkern u1="&#xdd;" u2="&#x201e;" k="102" />
<hkern u1="&#xdd;" u2="&#x201a;" k="102" />
<hkern u1="&#xdd;" u2="&#x2014;" k="102" />
<hkern u1="&#xdd;" u2="&#x2013;" k="102" />
<hkern u1="&#xdd;" u2="&#x153;" k="113" />
<hkern u1="&#xdd;" u2="&#x152;" k="51" />
<hkern u1="&#xdd;" u2="&#xe7;" k="113" />
<hkern u1="&#xdd;" u2="&#xe6;" k="113" />
<hkern u1="&#xdd;" u2="&#xd8;" k="51" />
<hkern u1="&#xdd;" u2="&#xd6;" k="51" />
<hkern u1="&#xdd;" u2="&#xd5;" k="51" />
<hkern u1="&#xdd;" u2="&#xd4;" k="51" />
<hkern u1="&#xdd;" u2="&#xd3;" k="51" />
<hkern u1="&#xdd;" u2="&#xd2;" k="51" />
<hkern u1="&#xdd;" u2="&#xc6;" k="78" />
<hkern u1="&#xdd;" u2="&#xc5;" k="78" />
<hkern u1="&#xdd;" u2="&#xc4;" k="78" />
<hkern u1="&#xdd;" u2="&#xc3;" k="78" />
<hkern u1="&#xdd;" u2="&#xc2;" k="78" />
<hkern u1="&#xdd;" u2="&#xc1;" k="78" />
<hkern u1="&#xdd;" u2="&#xc0;" k="78" />
<hkern u1="&#xdd;" u2="&#xbb;" k="82" />
<hkern u1="&#xdd;" u2="&#xae;" k="61" />
<hkern u1="&#xdd;" u2="&#xab;" k="123" />
<hkern u1="&#xdd;" u2="&#xa9;" k="61" />
<hkern u1="&#xdd;" u2="&#x7d;" k="-102" />
<hkern u1="&#xdd;" u2="z" k="61" />
<hkern u1="&#xdd;" u2="y" k="41" />
<hkern u1="&#xdd;" u2="x" k="41" />
<hkern u1="&#xdd;" u2="w" k="20" />
<hkern u1="&#xdd;" u2="v" k="41" />
<hkern u1="&#xdd;" u2="u" k="82" />
<hkern u1="&#xdd;" u2="t" k="41" />
<hkern u1="&#xdd;" u2="s" k="102" />
<hkern u1="&#xdd;" u2="r" k="82" />
<hkern u1="&#xdd;" u2="q" k="113" />
<hkern u1="&#xdd;" u2="p" k="82" />
<hkern u1="&#xdd;" u2="o" k="113" />
<hkern u1="&#xdd;" u2="n" k="82" />
<hkern u1="&#xdd;" u2="m" k="82" />
<hkern u1="&#xdd;" u2="g" k="82" />
<hkern u1="&#xdd;" u2="f" k="20" />
<hkern u1="&#xdd;" u2="e" k="113" />
<hkern u1="&#xdd;" u2="d" k="113" />
<hkern u1="&#xdd;" u2="c" k="113" />
<hkern u1="&#xdd;" u2="a" k="113" />
<hkern u1="&#xdd;" u2="]" k="-102" />
<hkern u1="&#xdd;" u2="X" k="-41" />
<hkern u1="&#xdd;" u2="V" k="-61" />
<hkern u1="&#xdd;" u2="T" k="-41" />
<hkern u1="&#xdd;" u2="S" k="-20" />
<hkern u1="&#xdd;" u2="Q" k="51" />
<hkern u1="&#xdd;" u2="O" k="51" />
<hkern u1="&#xdd;" u2="J" k="150" />
<hkern u1="&#xdd;" u2="G" k="51" />
<hkern u1="&#xdd;" u2="C" k="51" />
<hkern u1="&#xdd;" u2="A" k="78" />
<hkern u1="&#xdd;" u2="&#x40;" k="61" />
<hkern u1="&#xdd;" u2="&#x3b;" k="61" />
<hkern u1="&#xdd;" u2="&#x3a;" k="61" />
<hkern u1="&#xdd;" u2="&#x2e;" k="102" />
<hkern u1="&#xdd;" u2="&#x2d;" k="102" />
<hkern u1="&#xdd;" u2="&#x2c;" k="102" />
<hkern u1="&#xdd;" u2="&#x29;" k="-102" />
<hkern u1="&#xdd;" u2="&#x26;" k="61" />
<hkern u1="&#xde;" u2="&#x2026;" k="82" />
<hkern u1="&#xde;" u2="&#x201e;" k="82" />
<hkern u1="&#xde;" u2="&#x201c;" k="41" />
<hkern u1="&#xde;" u2="&#x201a;" k="82" />
<hkern u1="&#xde;" u2="&#x2018;" k="41" />
<hkern u1="&#xde;" u2="&#x178;" k="51" />
<hkern u1="&#xde;" u2="&#x153;" k="10" />
<hkern u1="&#xde;" u2="&#xe7;" k="10" />
<hkern u1="&#xde;" u2="&#xe6;" k="10" />
<hkern u1="&#xde;" u2="&#xdd;" k="51" />
<hkern u1="&#xde;" u2="&#xc6;" k="35" />
<hkern u1="&#xde;" u2="&#xc5;" k="35" />
<hkern u1="&#xde;" u2="&#xc4;" k="35" />
<hkern u1="&#xde;" u2="&#xc3;" k="35" />
<hkern u1="&#xde;" u2="&#xc2;" k="35" />
<hkern u1="&#xde;" u2="&#xc1;" k="35" />
<hkern u1="&#xde;" u2="&#xc0;" k="35" />
<hkern u1="&#xde;" u2="z" k="20" />
<hkern u1="&#xde;" u2="y" k="-10" />
<hkern u1="&#xde;" u2="x" k="18" />
<hkern u1="&#xde;" u2="v" k="-10" />
<hkern u1="&#xde;" u2="u" k="10" />
<hkern u1="&#xde;" u2="r" k="10" />
<hkern u1="&#xde;" u2="q" k="10" />
<hkern u1="&#xde;" u2="p" k="10" />
<hkern u1="&#xde;" u2="o" k="10" />
<hkern u1="&#xde;" u2="n" k="10" />
<hkern u1="&#xde;" u2="m" k="10" />
<hkern u1="&#xde;" u2="l" k="20" />
<hkern u1="&#xde;" u2="k" k="20" />
<hkern u1="&#xde;" u2="h" k="20" />
<hkern u1="&#xde;" u2="e" k="10" />
<hkern u1="&#xde;" u2="d" k="10" />
<hkern u1="&#xde;" u2="c" k="10" />
<hkern u1="&#xde;" u2="b" k="20" />
<hkern u1="&#xde;" u2="a" k="10" />
<hkern u1="&#xde;" u2="Z" k="92" />
<hkern u1="&#xde;" u2="Y" k="51" />
<hkern u1="&#xde;" u2="X" k="51" />
<hkern u1="&#xde;" u2="W" k="68" />
<hkern u1="&#xde;" u2="V" k="39" />
<hkern u1="&#xde;" u2="T" k="66" />
<hkern u1="&#xde;" u2="J" k="92" />
<hkern u1="&#xde;" u2="A" k="35" />
<hkern u1="&#xde;" u2="&#x3f;" k="41" />
<hkern u1="&#xde;" u2="&#x2f;" k="102" />
<hkern u1="&#xde;" u2="&#x2e;" k="82" />
<hkern u1="&#xde;" u2="&#x2c;" k="82" />
<hkern u1="&#xdf;" u2="&#x2122;" k="61" />
<hkern u1="&#xdf;" u2="&#x2026;" k="41" />
<hkern u1="&#xdf;" u2="&#x201e;" k="41" />
<hkern u1="&#xdf;" u2="&#x201c;" k="82" />
<hkern u1="&#xdf;" u2="&#x201a;" k="41" />
<hkern u1="&#xdf;" u2="&#x2018;" k="82" />
<hkern u1="&#xdf;" u2="z" k="31" />
<hkern u1="&#xdf;" u2="y" k="20" />
<hkern u1="&#xdf;" u2="x" k="31" />
<hkern u1="&#xdf;" u2="w" k="6" />
<hkern u1="&#xdf;" u2="v" k="20" />
<hkern u1="&#xdf;" u2="t" k="20" />
<hkern u1="&#xdf;" u2="s" k="4" />
<hkern u1="&#xdf;" u2="g" k="20" />
<hkern u1="&#xdf;" u2="f" k="4" />
<hkern u1="&#xdf;" u2="\" k="20" />
<hkern u1="&#xdf;" u2="&#x3f;" k="41" />
<hkern u1="&#xdf;" u2="&#x3b;" k="20" />
<hkern u1="&#xdf;" u2="&#x3a;" k="20" />
<hkern u1="&#xdf;" u2="&#x2f;" k="20" />
<hkern u1="&#xdf;" u2="&#x2e;" k="41" />
<hkern u1="&#xdf;" u2="&#x2c;" k="41" />
<hkern u1="&#xe0;" u2="&#x2122;" k="29" />
<hkern u1="&#xe0;" u2="&#x201c;" k="43" />
<hkern u1="&#xe0;" u2="&#x2018;" k="43" />
<hkern u1="&#xe0;" u2="y" k="8" />
<hkern u1="&#xe0;" u2="v" k="8" />
<hkern u1="&#xe0;" u2="t" k="14" />
<hkern u1="&#xe0;" u2="\" k="14" />
<hkern u1="&#xe0;" u2="&#x3f;" k="14" />
<hkern u1="&#xe1;" u2="&#x2122;" k="29" />
<hkern u1="&#xe1;" u2="&#x201c;" k="43" />
<hkern u1="&#xe1;" u2="&#x2018;" k="43" />
<hkern u1="&#xe1;" u2="y" k="8" />
<hkern u1="&#xe1;" u2="v" k="8" />
<hkern u1="&#xe1;" u2="t" k="14" />
<hkern u1="&#xe1;" u2="\" k="14" />
<hkern u1="&#xe1;" u2="&#x3f;" k="14" />
<hkern u1="&#xe2;" u2="&#x2122;" k="29" />
<hkern u1="&#xe2;" u2="&#x201c;" k="43" />
<hkern u1="&#xe2;" u2="&#x2018;" k="43" />
<hkern u1="&#xe2;" u2="y" k="8" />
<hkern u1="&#xe2;" u2="v" k="8" />
<hkern u1="&#xe2;" u2="t" k="14" />
<hkern u1="&#xe2;" u2="\" k="14" />
<hkern u1="&#xe2;" u2="&#x3f;" k="14" />
<hkern u1="&#xe4;" u2="&#x2122;" k="29" />
<hkern u1="&#xe4;" u2="&#x201c;" k="43" />
<hkern u1="&#xe4;" u2="&#x2018;" k="43" />
<hkern u1="&#xe4;" u2="y" k="8" />
<hkern u1="&#xe4;" u2="v" k="8" />
<hkern u1="&#xe4;" u2="t" k="14" />
<hkern u1="&#xe4;" u2="\" k="14" />
<hkern u1="&#xe4;" u2="&#x3f;" k="14" />
<hkern u1="&#xe6;" u2="y" k="20" />
<hkern u1="&#xe6;" u2="x" k="31" />
<hkern u1="&#xe6;" u2="v" k="20" />
<hkern u1="&#xe8;" u2="y" k="14" />
<hkern u1="&#xe8;" u2="x" k="23" />
<hkern u1="&#xe8;" u2="v" k="14" />
<hkern u1="&#xe9;" u2="y" k="14" />
<hkern u1="&#xe9;" u2="x" k="23" />
<hkern u1="&#xe9;" u2="v" k="14" />
<hkern u1="&#xea;" u2="y" k="14" />
<hkern u1="&#xea;" u2="x" k="23" />
<hkern u1="&#xea;" u2="v" k="14" />
<hkern u1="&#xeb;" u2="y" k="14" />
<hkern u1="&#xeb;" u2="x" k="23" />
<hkern u1="&#xeb;" u2="v" k="14" />
<hkern u1="&#xf1;" u2="&#x2122;" k="29" />
<hkern u1="&#xf1;" u2="&#x201c;" k="43" />
<hkern u1="&#xf1;" u2="&#x2018;" k="43" />
<hkern u1="&#xf1;" u2="y" k="8" />
<hkern u1="&#xf1;" u2="v" k="8" />
<hkern u1="&#xf1;" u2="t" k="14" />
<hkern u1="&#xf1;" u2="\" k="14" />
<hkern u1="&#xf1;" u2="&#x3f;" k="14" />
<hkern u1="&#xf3;" u2="&#x2122;" k="43" />
<hkern u1="&#xf3;" u2="&#x2026;" k="29" />
<hkern u1="&#xf3;" u2="&#x201e;" k="29" />
<hkern u1="&#xf3;" u2="&#x201c;" k="57" />
<hkern u1="&#xf3;" u2="&#x201a;" k="29" />
<hkern u1="&#xf3;" u2="&#x2018;" k="57" />
<hkern u1="&#xf3;" u2="z" k="23" />
<hkern u1="&#xf3;" u2="y" k="14" />
<hkern u1="&#xf3;" u2="x" k="23" />
<hkern u1="&#xf3;" u2="v" k="14" />
<hkern u1="&#xf3;" u2="\" k="14" />
<hkern u1="&#xf3;" u2="&#x3f;" k="29" />
<hkern u1="&#xf3;" u2="&#x3b;" k="14" />
<hkern u1="&#xf3;" u2="&#x3a;" k="14" />
<hkern u1="&#xf3;" u2="&#x2f;" k="14" />
<hkern u1="&#xf3;" u2="&#x2e;" k="29" />
<hkern u1="&#xf3;" u2="&#x2c;" k="29" />
<hkern u1="&#xf4;" u2="&#x2122;" k="43" />
<hkern u1="&#xf4;" u2="&#x2026;" k="29" />
<hkern u1="&#xf4;" u2="&#x201e;" k="29" />
<hkern u1="&#xf4;" u2="&#x201c;" k="57" />
<hkern u1="&#xf4;" u2="&#x201a;" k="29" />
<hkern u1="&#xf4;" u2="&#x2018;" k="57" />
<hkern u1="&#xf4;" u2="z" k="23" />
<hkern u1="&#xf4;" u2="y" k="14" />
<hkern u1="&#xf4;" u2="x" k="23" />
<hkern u1="&#xf4;" u2="v" k="14" />
<hkern u1="&#xf4;" u2="\" k="14" />
<hkern u1="&#xf4;" u2="&#x3f;" k="29" />
<hkern u1="&#xf4;" u2="&#x3b;" k="14" />
<hkern u1="&#xf4;" u2="&#x3a;" k="14" />
<hkern u1="&#xf4;" u2="&#x2f;" k="14" />
<hkern u1="&#xf4;" u2="&#x2e;" k="29" />
<hkern u1="&#xf4;" u2="&#x2c;" k="29" />
<hkern u1="&#xf5;" u2="&#x2122;" k="43" />
<hkern u1="&#xf5;" u2="&#x2026;" k="29" />
<hkern u1="&#xf5;" u2="&#x201e;" k="29" />
<hkern u1="&#xf5;" u2="&#x201c;" k="57" />
<hkern u1="&#xf5;" u2="&#x201a;" k="29" />
<hkern u1="&#xf5;" u2="&#x2018;" k="57" />
<hkern u1="&#xf5;" u2="z" k="23" />
<hkern u1="&#xf5;" u2="y" k="14" />
<hkern u1="&#xf5;" u2="x" k="23" />
<hkern u1="&#xf5;" u2="v" k="14" />
<hkern u1="&#xf5;" u2="\" k="14" />
<hkern u1="&#xf5;" u2="&#x3f;" k="29" />
<hkern u1="&#xf5;" u2="&#x3b;" k="14" />
<hkern u1="&#xf5;" u2="&#x3a;" k="14" />
<hkern u1="&#xf5;" u2="&#x2f;" k="14" />
<hkern u1="&#xf5;" u2="&#x2e;" k="29" />
<hkern u1="&#xf5;" u2="&#x2c;" k="29" />
<hkern u1="&#xf6;" u2="&#x2122;" k="43" />
<hkern u1="&#xf6;" u2="&#x2026;" k="29" />
<hkern u1="&#xf6;" u2="&#x201e;" k="29" />
<hkern u1="&#xf6;" u2="&#x201c;" k="57" />
<hkern u1="&#xf6;" u2="&#x201a;" k="29" />
<hkern u1="&#xf6;" u2="&#x2018;" k="57" />
<hkern u1="&#xf6;" u2="z" k="23" />
<hkern u1="&#xf6;" u2="y" k="14" />
<hkern u1="&#xf6;" u2="x" k="23" />
<hkern u1="&#xf6;" u2="v" k="14" />
<hkern u1="&#xf6;" u2="\" k="14" />
<hkern u1="&#xf6;" u2="&#x3f;" k="29" />
<hkern u1="&#xf6;" u2="&#x3b;" k="14" />
<hkern u1="&#xf6;" u2="&#x3a;" k="14" />
<hkern u1="&#xf6;" u2="&#x2f;" k="14" />
<hkern u1="&#xf6;" u2="&#x2e;" k="29" />
<hkern u1="&#xf6;" u2="&#x2c;" k="29" />
<hkern u1="&#xf8;" u2="&#x2122;" k="61" />
<hkern u1="&#xf8;" u2="&#x2026;" k="41" />
<hkern u1="&#xf8;" u2="&#x201e;" k="41" />
<hkern u1="&#xf8;" u2="&#x201c;" k="82" />
<hkern u1="&#xf8;" u2="&#x201a;" k="41" />
<hkern u1="&#xf8;" u2="&#x2018;" k="82" />
<hkern u1="&#xf8;" u2="z" k="31" />
<hkern u1="&#xf8;" u2="y" k="20" />
<hkern u1="&#xf8;" u2="x" k="31" />
<hkern u1="&#xf8;" u2="w" k="6" />
<hkern u1="&#xf8;" u2="v" k="20" />
<hkern u1="&#xf8;" u2="s" k="4" />
<hkern u1="&#xf8;" u2="g" k="-4" />
<hkern u1="&#xf8;" u2="f" k="4" />
<hkern u1="&#xf8;" u2="\" k="20" />
<hkern u1="&#xf8;" u2="&#x3f;" k="41" />
<hkern u1="&#xf8;" u2="&#x3b;" k="20" />
<hkern u1="&#xf8;" u2="&#x3a;" k="20" />
<hkern u1="&#xf8;" u2="&#x2f;" k="20" />
<hkern u1="&#xf8;" u2="&#x2e;" k="41" />
<hkern u1="&#xf8;" u2="&#x2c;" k="41" />
<hkern u1="&#x152;" u2="&#x153;" k="51" />
<hkern u1="&#x152;" u2="&#x152;" k="12" />
<hkern u1="&#x152;" u2="&#xe7;" k="51" />
<hkern u1="&#x152;" u2="&#xe6;" k="51" />
<hkern u1="&#x152;" u2="&#xd8;" k="12" />
<hkern u1="&#x152;" u2="&#xd6;" k="12" />
<hkern u1="&#x152;" u2="&#xd5;" k="12" />
<hkern u1="&#x152;" u2="&#xd4;" k="12" />
<hkern u1="&#x152;" u2="&#xd3;" k="12" />
<hkern u1="&#x152;" u2="&#xd2;" k="12" />
<hkern u1="&#x152;" u2="&#xae;" k="41" />
<hkern u1="&#x152;" u2="&#xa9;" k="41" />
<hkern u1="&#x152;" u2="y" k="41" />
<hkern u1="&#x152;" u2="v" k="41" />
<hkern u1="&#x152;" u2="q" k="51" />
<hkern u1="&#x152;" u2="o" k="51" />
<hkern u1="&#x152;" u2="g" k="10" />
<hkern u1="&#x152;" u2="f" k="20" />
<hkern u1="&#x152;" u2="e" k="51" />
<hkern u1="&#x152;" u2="d" k="51" />
<hkern u1="&#x152;" u2="c" k="51" />
<hkern u1="&#x152;" u2="a" k="51" />
<hkern u1="&#x152;" u2="T" k="-31" />
<hkern u1="&#x152;" u2="Q" k="12" />
<hkern u1="&#x152;" u2="O" k="12" />
<hkern u1="&#x152;" u2="G" k="12" />
<hkern u1="&#x152;" u2="C" k="12" />
<hkern u1="&#x152;" u2="&#x40;" k="41" />
<hkern u1="&#x153;" u2="y" k="20" />
<hkern u1="&#x153;" u2="x" k="31" />
<hkern u1="&#x153;" u2="v" k="20" />
<hkern u1="&#x178;" u2="&#x203a;" k="82" />
<hkern u1="&#x178;" u2="&#x2039;" k="123" />
<hkern u1="&#x178;" u2="&#x2026;" k="102" />
<hkern u1="&#x178;" u2="&#x201e;" k="102" />
<hkern u1="&#x178;" u2="&#x201a;" k="102" />
<hkern u1="&#x178;" u2="&#x2014;" k="102" />
<hkern u1="&#x178;" u2="&#x2013;" k="102" />
<hkern u1="&#x178;" u2="&#x153;" k="113" />
<hkern u1="&#x178;" u2="&#x152;" k="51" />
<hkern u1="&#x178;" u2="&#xe7;" k="113" />
<hkern u1="&#x178;" u2="&#xe6;" k="113" />
<hkern u1="&#x178;" u2="&#xd8;" k="51" />
<hkern u1="&#x178;" u2="&#xd6;" k="51" />
<hkern u1="&#x178;" u2="&#xd5;" k="51" />
<hkern u1="&#x178;" u2="&#xd4;" k="51" />
<hkern u1="&#x178;" u2="&#xd3;" k="51" />
<hkern u1="&#x178;" u2="&#xd2;" k="51" />
<hkern u1="&#x178;" u2="&#xc6;" k="78" />
<hkern u1="&#x178;" u2="&#xc5;" k="78" />
<hkern u1="&#x178;" u2="&#xc4;" k="78" />
<hkern u1="&#x178;" u2="&#xc3;" k="78" />
<hkern u1="&#x178;" u2="&#xc2;" k="78" />
<hkern u1="&#x178;" u2="&#xc1;" k="78" />
<hkern u1="&#x178;" u2="&#xc0;" k="78" />
<hkern u1="&#x178;" u2="&#xbb;" k="82" />
<hkern u1="&#x178;" u2="&#xae;" k="61" />
<hkern u1="&#x178;" u2="&#xab;" k="123" />
<hkern u1="&#x178;" u2="&#xa9;" k="61" />
<hkern u1="&#x178;" u2="&#x7d;" k="-102" />
<hkern u1="&#x178;" u2="z" k="61" />
<hkern u1="&#x178;" u2="y" k="41" />
<hkern u1="&#x178;" u2="x" k="41" />
<hkern u1="&#x178;" u2="w" k="20" />
<hkern u1="&#x178;" u2="v" k="41" />
<hkern u1="&#x178;" u2="u" k="82" />
<hkern u1="&#x178;" u2="t" k="41" />
<hkern u1="&#x178;" u2="s" k="102" />
<hkern u1="&#x178;" u2="r" k="82" />
<hkern u1="&#x178;" u2="q" k="113" />
<hkern u1="&#x178;" u2="p" k="82" />
<hkern u1="&#x178;" u2="o" k="113" />
<hkern u1="&#x178;" u2="n" k="82" />
<hkern u1="&#x178;" u2="m" k="82" />
<hkern u1="&#x178;" u2="g" k="82" />
<hkern u1="&#x178;" u2="f" k="20" />
<hkern u1="&#x178;" u2="e" k="113" />
<hkern u1="&#x178;" u2="d" k="113" />
<hkern u1="&#x178;" u2="c" k="113" />
<hkern u1="&#x178;" u2="a" k="113" />
<hkern u1="&#x178;" u2="]" k="-102" />
<hkern u1="&#x178;" u2="X" k="-41" />
<hkern u1="&#x178;" u2="V" k="-61" />
<hkern u1="&#x178;" u2="T" k="-41" />
<hkern u1="&#x178;" u2="S" k="-20" />
<hkern u1="&#x178;" u2="Q" k="51" />
<hkern u1="&#x178;" u2="O" k="51" />
<hkern u1="&#x178;" u2="J" k="150" />
<hkern u1="&#x178;" u2="G" k="51" />
<hkern u1="&#x178;" u2="C" k="51" />
<hkern u1="&#x178;" u2="A" k="78" />
<hkern u1="&#x178;" u2="&#x40;" k="61" />
<hkern u1="&#x178;" u2="&#x3b;" k="61" />
<hkern u1="&#x178;" u2="&#x3a;" k="61" />
<hkern u1="&#x178;" u2="&#x2e;" k="102" />
<hkern u1="&#x178;" u2="&#x2d;" k="102" />
<hkern u1="&#x178;" u2="&#x2c;" k="102" />
<hkern u1="&#x178;" u2="&#x29;" k="-102" />
<hkern u1="&#x178;" u2="&#x26;" k="61" />
<hkern u1="&#x2013;" u2="&#x178;" k="102" />
<hkern u1="&#x2013;" u2="&#x153;" k="20" />
<hkern u1="&#x2013;" u2="&#xe7;" k="20" />
<hkern u1="&#x2013;" u2="&#xe6;" k="20" />
<hkern u1="&#x2013;" u2="&#xdd;" k="102" />
<hkern u1="&#x2013;" u2="&#xc6;" k="-20" />
<hkern u1="&#x2013;" u2="&#xc5;" k="-20" />
<hkern u1="&#x2013;" u2="&#xc4;" k="-20" />
<hkern u1="&#x2013;" u2="&#xc3;" k="-20" />
<hkern u1="&#x2013;" u2="&#xc2;" k="-20" />
<hkern u1="&#x2013;" u2="&#xc1;" k="-20" />
<hkern u1="&#x2013;" u2="&#xc0;" k="-20" />
<hkern u1="&#x2013;" u2="z" k="61" />
<hkern u1="&#x2013;" u2="y" k="20" />
<hkern u1="&#x2013;" u2="x" k="61" />
<hkern u1="&#x2013;" u2="v" k="20" />
<hkern u1="&#x2013;" u2="q" k="20" />
<hkern u1="&#x2013;" u2="o" k="20" />
<hkern u1="&#x2013;" u2="e" k="20" />
<hkern u1="&#x2013;" u2="d" k="20" />
<hkern u1="&#x2013;" u2="c" k="20" />
<hkern u1="&#x2013;" u2="a" k="20" />
<hkern u1="&#x2013;" u2="Z" k="20" />
<hkern u1="&#x2013;" u2="Y" k="102" />
<hkern u1="&#x2013;" u2="X" k="61" />
<hkern u1="&#x2013;" u2="W" k="82" />
<hkern u1="&#x2013;" u2="V" k="102" />
<hkern u1="&#x2013;" u2="T" k="143" />
<hkern u1="&#x2013;" u2="A" k="-20" />
<hkern u1="&#x2013;" u2="&#x37;" k="61" />
<hkern u1="&#x2013;" u2="&#x31;" k="41" />
<hkern u1="&#x2014;" u2="&#x178;" k="102" />
<hkern u1="&#x2014;" u2="&#x153;" k="20" />
<hkern u1="&#x2014;" u2="&#xe7;" k="20" />
<hkern u1="&#x2014;" u2="&#xe6;" k="20" />
<hkern u1="&#x2014;" u2="&#xdd;" k="102" />
<hkern u1="&#x2014;" u2="&#xc6;" k="-20" />
<hkern u1="&#x2014;" u2="&#xc5;" k="-20" />
<hkern u1="&#x2014;" u2="&#xc4;" k="-20" />
<hkern u1="&#x2014;" u2="&#xc3;" k="-20" />
<hkern u1="&#x2014;" u2="&#xc2;" k="-20" />
<hkern u1="&#x2014;" u2="&#xc1;" k="-20" />
<hkern u1="&#x2014;" u2="&#xc0;" k="-20" />
<hkern u1="&#x2014;" u2="z" k="61" />
<hkern u1="&#x2014;" u2="y" k="20" />
<hkern u1="&#x2014;" u2="x" k="61" />
<hkern u1="&#x2014;" u2="v" k="20" />
<hkern u1="&#x2014;" u2="q" k="20" />
<hkern u1="&#x2014;" u2="o" k="20" />
<hkern u1="&#x2014;" u2="e" k="20" />
<hkern u1="&#x2014;" u2="d" k="20" />
<hkern u1="&#x2014;" u2="c" k="20" />
<hkern u1="&#x2014;" u2="a" k="20" />
<hkern u1="&#x2014;" u2="Z" k="20" />
<hkern u1="&#x2014;" u2="Y" k="102" />
<hkern u1="&#x2014;" u2="X" k="61" />
<hkern u1="&#x2014;" u2="W" k="82" />
<hkern u1="&#x2014;" u2="V" k="102" />
<hkern u1="&#x2014;" u2="T" k="143" />
<hkern u1="&#x2014;" u2="A" k="-20" />
<hkern u1="&#x2014;" u2="&#x37;" k="61" />
<hkern u1="&#x2014;" u2="&#x31;" k="41" />
<hkern u1="&#x2018;" u2="&#x178;" k="-61" />
<hkern u1="&#x2018;" u2="&#x153;" k="61" />
<hkern u1="&#x2018;" u2="&#xe7;" k="61" />
<hkern u1="&#x2018;" u2="&#xe6;" k="61" />
<hkern u1="&#x2018;" u2="&#xdd;" k="-61" />
<hkern u1="&#x2018;" u2="&#xc6;" k="82" />
<hkern u1="&#x2018;" u2="&#xc5;" k="82" />
<hkern u1="&#x2018;" u2="&#xc4;" k="82" />
<hkern u1="&#x2018;" u2="&#xc3;" k="82" />
<hkern u1="&#x2018;" u2="&#xc2;" k="82" />
<hkern u1="&#x2018;" u2="&#xc1;" k="82" />
<hkern u1="&#x2018;" u2="&#xc0;" k="82" />
<hkern u1="&#x2018;" u2="u" k="20" />
<hkern u1="&#x2018;" u2="s" k="61" />
<hkern u1="&#x2018;" u2="r" k="20" />
<hkern u1="&#x2018;" u2="q" k="61" />
<hkern u1="&#x2018;" u2="p" k="20" />
<hkern u1="&#x2018;" u2="o" k="61" />
<hkern u1="&#x2018;" u2="n" k="20" />
<hkern u1="&#x2018;" u2="m" k="20" />
<hkern u1="&#x2018;" u2="g" k="82" />
<hkern u1="&#x2018;" u2="e" k="61" />
<hkern u1="&#x2018;" u2="d" k="61" />
<hkern u1="&#x2018;" u2="c" k="61" />
<hkern u1="&#x2018;" u2="a" k="61" />
<hkern u1="&#x2018;" u2="Y" k="-61" />
<hkern u1="&#x2018;" u2="X" k="-41" />
<hkern u1="&#x2018;" u2="W" k="-41" />
<hkern u1="&#x2018;" u2="V" k="-41" />
<hkern u1="&#x2018;" u2="T" k="-20" />
<hkern u1="&#x2018;" u2="J" k="225" />
<hkern u1="&#x2018;" u2="A" k="82" />
<hkern u1="&#x2019;" u2="&#x153;" k="102" />
<hkern u1="&#x2019;" u2="&#x152;" k="82" />
<hkern u1="&#x2019;" u2="&#xe7;" k="102" />
<hkern u1="&#x2019;" u2="&#xe6;" k="102" />
<hkern u1="&#x2019;" u2="&#xd8;" k="82" />
<hkern u1="&#x2019;" u2="&#xd6;" k="82" />
<hkern u1="&#x2019;" u2="&#xd5;" k="82" />
<hkern u1="&#x2019;" u2="&#xd4;" k="82" />
<hkern u1="&#x2019;" u2="&#xd3;" k="82" />
<hkern u1="&#x2019;" u2="&#xd2;" k="82" />
<hkern u1="&#x2019;" u2="q" k="102" />
<hkern u1="&#x2019;" u2="o" k="102" />
<hkern u1="&#x2019;" u2="e" k="102" />
<hkern u1="&#x2019;" u2="d" k="102" />
<hkern u1="&#x2019;" u2="c" k="102" />
<hkern u1="&#x2019;" u2="a" k="102" />
<hkern u1="&#x2019;" u2="S" k="20" />
<hkern u1="&#x2019;" u2="Q" k="82" />
<hkern u1="&#x2019;" u2="O" k="82" />
<hkern u1="&#x2019;" u2="J" k="246" />
<hkern u1="&#x2019;" u2="G" k="82" />
<hkern u1="&#x2019;" u2="C" k="82" />
<hkern u1="&#x201a;" u2="&#x178;" k="102" />
<hkern u1="&#x201a;" u2="&#x153;" k="41" />
<hkern u1="&#x201a;" u2="&#x152;" k="82" />
<hkern u1="&#x201a;" u2="&#xe7;" k="41" />
<hkern u1="&#x201a;" u2="&#xe6;" k="41" />
<hkern u1="&#x201a;" u2="&#xdd;" k="102" />
<hkern u1="&#x201a;" u2="&#xdc;" k="20" />
<hkern u1="&#x201a;" u2="&#xdb;" k="20" />
<hkern u1="&#x201a;" u2="&#xda;" k="20" />
<hkern u1="&#x201a;" u2="&#xd9;" k="20" />
<hkern u1="&#x201a;" u2="&#xd8;" k="82" />
<hkern u1="&#x201a;" u2="&#xd6;" k="82" />
<hkern u1="&#x201a;" u2="&#xd5;" k="82" />
<hkern u1="&#x201a;" u2="&#xd4;" k="82" />
<hkern u1="&#x201a;" u2="&#xd3;" k="82" />
<hkern u1="&#x201a;" u2="&#xd2;" k="82" />
<hkern u1="&#x201a;" u2="&#xc6;" k="-61" />
<hkern u1="&#x201a;" u2="&#xc5;" k="-61" />
<hkern u1="&#x201a;" u2="&#xc4;" k="-61" />
<hkern u1="&#x201a;" u2="&#xc3;" k="-61" />
<hkern u1="&#x201a;" u2="&#xc2;" k="-61" />
<hkern u1="&#x201a;" u2="&#xc1;" k="-61" />
<hkern u1="&#x201a;" u2="&#xc0;" k="-61" />
<hkern u1="&#x201a;" u2="y" k="61" />
<hkern u1="&#x201a;" u2="w" k="20" />
<hkern u1="&#x201a;" u2="v" k="61" />
<hkern u1="&#x201a;" u2="u" k="41" />
<hkern u1="&#x201a;" u2="t" k="82" />
<hkern u1="&#x201a;" u2="r" k="41" />
<hkern u1="&#x201a;" u2="q" k="41" />
<hkern u1="&#x201a;" u2="p" k="41" />
<hkern u1="&#x201a;" u2="o" k="41" />
<hkern u1="&#x201a;" u2="n" k="41" />
<hkern u1="&#x201a;" u2="m" k="41" />
<hkern u1="&#x201a;" u2="j" k="-61" />
<hkern u1="&#x201a;" u2="e" k="41" />
<hkern u1="&#x201a;" u2="d" k="41" />
<hkern u1="&#x201a;" u2="c" k="41" />
<hkern u1="&#x201a;" u2="a" k="41" />
<hkern u1="&#x201a;" u2="Y" k="102" />
<hkern u1="&#x201a;" u2="W" k="41" />
<hkern u1="&#x201a;" u2="V" k="123" />
<hkern u1="&#x201a;" u2="U" k="20" />
<hkern u1="&#x201a;" u2="T" k="123" />
<hkern u1="&#x201a;" u2="Q" k="82" />
<hkern u1="&#x201a;" u2="O" k="82" />
<hkern u1="&#x201a;" u2="G" k="82" />
<hkern u1="&#x201a;" u2="C" k="82" />
<hkern u1="&#x201a;" u2="A" k="-61" />
<hkern u1="&#x201a;" u2="&#x39;" k="20" />
<hkern u1="&#x201a;" u2="&#x38;" k="31" />
<hkern u1="&#x201a;" u2="&#x36;" k="61" />
<hkern u1="&#x201a;" u2="&#x34;" k="102" />
<hkern u1="&#x201a;" u2="&#x31;" k="123" />
<hkern u1="&#x201a;" u2="&#x30;" k="82" />
<hkern u1="&#x201c;" u2="&#x178;" k="-61" />
<hkern u1="&#x201c;" u2="&#x153;" k="61" />
<hkern u1="&#x201c;" u2="&#xe7;" k="61" />
<hkern u1="&#x201c;" u2="&#xe6;" k="61" />
<hkern u1="&#x201c;" u2="&#xdd;" k="-61" />
<hkern u1="&#x201c;" u2="&#xc6;" k="82" />
<hkern u1="&#x201c;" u2="&#xc5;" k="82" />
<hkern u1="&#x201c;" u2="&#xc4;" k="82" />
<hkern u1="&#x201c;" u2="&#xc3;" k="82" />
<hkern u1="&#x201c;" u2="&#xc2;" k="82" />
<hkern u1="&#x201c;" u2="&#xc1;" k="82" />
<hkern u1="&#x201c;" u2="&#xc0;" k="82" />
<hkern u1="&#x201c;" u2="u" k="20" />
<hkern u1="&#x201c;" u2="s" k="61" />
<hkern u1="&#x201c;" u2="r" k="20" />
<hkern u1="&#x201c;" u2="q" k="61" />
<hkern u1="&#x201c;" u2="p" k="20" />
<hkern u1="&#x201c;" u2="o" k="61" />
<hkern u1="&#x201c;" u2="n" k="20" />
<hkern u1="&#x201c;" u2="m" k="20" />
<hkern u1="&#x201c;" u2="g" k="82" />
<hkern u1="&#x201c;" u2="e" k="61" />
<hkern u1="&#x201c;" u2="d" k="61" />
<hkern u1="&#x201c;" u2="c" k="61" />
<hkern u1="&#x201c;" u2="a" k="61" />
<hkern u1="&#x201c;" u2="Y" k="-61" />
<hkern u1="&#x201c;" u2="X" k="-41" />
<hkern u1="&#x201c;" u2="W" k="-41" />
<hkern u1="&#x201c;" u2="V" k="-41" />
<hkern u1="&#x201c;" u2="T" k="-20" />
<hkern u1="&#x201c;" u2="J" k="225" />
<hkern u1="&#x201c;" u2="A" k="82" />
<hkern u1="&#x201d;" u2="&#x153;" k="102" />
<hkern u1="&#x201d;" u2="&#x152;" k="82" />
<hkern u1="&#x201d;" u2="&#xe7;" k="102" />
<hkern u1="&#x201d;" u2="&#xe6;" k="102" />
<hkern u1="&#x201d;" u2="&#xd8;" k="82" />
<hkern u1="&#x201d;" u2="&#xd6;" k="82" />
<hkern u1="&#x201d;" u2="&#xd5;" k="82" />
<hkern u1="&#x201d;" u2="&#xd4;" k="82" />
<hkern u1="&#x201d;" u2="&#xd3;" k="82" />
<hkern u1="&#x201d;" u2="&#xd2;" k="82" />
<hkern u1="&#x201d;" u2="q" k="102" />
<hkern u1="&#x201d;" u2="o" k="102" />
<hkern u1="&#x201d;" u2="e" k="102" />
<hkern u1="&#x201d;" u2="d" k="102" />
<hkern u1="&#x201d;" u2="c" k="102" />
<hkern u1="&#x201d;" u2="a" k="102" />
<hkern u1="&#x201d;" u2="S" k="20" />
<hkern u1="&#x201d;" u2="Q" k="82" />
<hkern u1="&#x201d;" u2="O" k="82" />
<hkern u1="&#x201d;" u2="J" k="246" />
<hkern u1="&#x201d;" u2="G" k="82" />
<hkern u1="&#x201d;" u2="C" k="82" />
<hkern u1="&#x201e;" u2="&#x178;" k="102" />
<hkern u1="&#x201e;" u2="&#x153;" k="41" />
<hkern u1="&#x201e;" u2="&#x152;" k="82" />
<hkern u1="&#x201e;" u2="&#xe7;" k="41" />
<hkern u1="&#x201e;" u2="&#xe6;" k="41" />
<hkern u1="&#x201e;" u2="&#xdd;" k="102" />
<hkern u1="&#x201e;" u2="&#xdc;" k="20" />
<hkern u1="&#x201e;" u2="&#xdb;" k="20" />
<hkern u1="&#x201e;" u2="&#xda;" k="20" />
<hkern u1="&#x201e;" u2="&#xd9;" k="20" />
<hkern u1="&#x201e;" u2="&#xd8;" k="82" />
<hkern u1="&#x201e;" u2="&#xd6;" k="82" />
<hkern u1="&#x201e;" u2="&#xd5;" k="82" />
<hkern u1="&#x201e;" u2="&#xd4;" k="82" />
<hkern u1="&#x201e;" u2="&#xd3;" k="82" />
<hkern u1="&#x201e;" u2="&#xd2;" k="82" />
<hkern u1="&#x201e;" u2="&#xc6;" k="-61" />
<hkern u1="&#x201e;" u2="&#xc5;" k="-61" />
<hkern u1="&#x201e;" u2="&#xc4;" k="-61" />
<hkern u1="&#x201e;" u2="&#xc3;" k="-61" />
<hkern u1="&#x201e;" u2="&#xc2;" k="-61" />
<hkern u1="&#x201e;" u2="&#xc1;" k="-61" />
<hkern u1="&#x201e;" u2="&#xc0;" k="-61" />
<hkern u1="&#x201e;" u2="y" k="61" />
<hkern u1="&#x201e;" u2="w" k="20" />
<hkern u1="&#x201e;" u2="v" k="61" />
<hkern u1="&#x201e;" u2="u" k="41" />
<hkern u1="&#x201e;" u2="t" k="82" />
<hkern u1="&#x201e;" u2="r" k="41" />
<hkern u1="&#x201e;" u2="q" k="41" />
<hkern u1="&#x201e;" u2="p" k="41" />
<hkern u1="&#x201e;" u2="o" k="41" />
<hkern u1="&#x201e;" u2="n" k="41" />
<hkern u1="&#x201e;" u2="m" k="41" />
<hkern u1="&#x201e;" u2="j" k="-61" />
<hkern u1="&#x201e;" u2="e" k="41" />
<hkern u1="&#x201e;" u2="d" k="41" />
<hkern u1="&#x201e;" u2="c" k="41" />
<hkern u1="&#x201e;" u2="a" k="41" />
<hkern u1="&#x201e;" u2="Y" k="102" />
<hkern u1="&#x201e;" u2="W" k="41" />
<hkern u1="&#x201e;" u2="V" k="123" />
<hkern u1="&#x201e;" u2="U" k="20" />
<hkern u1="&#x201e;" u2="T" k="123" />
<hkern u1="&#x201e;" u2="Q" k="82" />
<hkern u1="&#x201e;" u2="O" k="82" />
<hkern u1="&#x201e;" u2="G" k="82" />
<hkern u1="&#x201e;" u2="C" k="82" />
<hkern u1="&#x201e;" u2="A" k="-61" />
<hkern u1="&#x201e;" u2="&#x39;" k="20" />
<hkern u1="&#x201e;" u2="&#x38;" k="31" />
<hkern u1="&#x201e;" u2="&#x36;" k="61" />
<hkern u1="&#x201e;" u2="&#x34;" k="102" />
<hkern u1="&#x201e;" u2="&#x31;" k="123" />
<hkern u1="&#x201e;" u2="&#x30;" k="82" />
<hkern u1="&#x2026;" u2="&#x178;" k="102" />
<hkern u1="&#x2026;" u2="&#x153;" k="41" />
<hkern u1="&#x2026;" u2="&#x152;" k="82" />
<hkern u1="&#x2026;" u2="&#xe7;" k="41" />
<hkern u1="&#x2026;" u2="&#xe6;" k="41" />
<hkern u1="&#x2026;" u2="&#xdd;" k="102" />
<hkern u1="&#x2026;" u2="&#xdc;" k="20" />
<hkern u1="&#x2026;" u2="&#xdb;" k="20" />
<hkern u1="&#x2026;" u2="&#xda;" k="20" />
<hkern u1="&#x2026;" u2="&#xd9;" k="20" />
<hkern u1="&#x2026;" u2="&#xd8;" k="82" />
<hkern u1="&#x2026;" u2="&#xd6;" k="82" />
<hkern u1="&#x2026;" u2="&#xd5;" k="82" />
<hkern u1="&#x2026;" u2="&#xd4;" k="82" />
<hkern u1="&#x2026;" u2="&#xd3;" k="82" />
<hkern u1="&#x2026;" u2="&#xd2;" k="82" />
<hkern u1="&#x2026;" u2="&#xc6;" k="-61" />
<hkern u1="&#x2026;" u2="&#xc5;" k="-61" />
<hkern u1="&#x2026;" u2="&#xc4;" k="-61" />
<hkern u1="&#x2026;" u2="&#xc3;" k="-61" />
<hkern u1="&#x2026;" u2="&#xc2;" k="-61" />
<hkern u1="&#x2026;" u2="&#xc1;" k="-61" />
<hkern u1="&#x2026;" u2="&#xc0;" k="-61" />
<hkern u1="&#x2026;" u2="y" k="61" />
<hkern u1="&#x2026;" u2="w" k="20" />
<hkern u1="&#x2026;" u2="v" k="61" />
<hkern u1="&#x2026;" u2="u" k="41" />
<hkern u1="&#x2026;" u2="t" k="82" />
<hkern u1="&#x2026;" u2="r" k="41" />
<hkern u1="&#x2026;" u2="q" k="41" />
<hkern u1="&#x2026;" u2="p" k="41" />
<hkern u1="&#x2026;" u2="o" k="41" />
<hkern u1="&#x2026;" u2="n" k="41" />
<hkern u1="&#x2026;" u2="m" k="41" />
<hkern u1="&#x2026;" u2="e" k="41" />
<hkern u1="&#x2026;" u2="d" k="41" />
<hkern u1="&#x2026;" u2="c" k="41" />
<hkern u1="&#x2026;" u2="a" k="41" />
<hkern u1="&#x2026;" u2="Y" k="102" />
<hkern u1="&#x2026;" u2="W" k="41" />
<hkern u1="&#x2026;" u2="V" k="123" />
<hkern u1="&#x2026;" u2="U" k="20" />
<hkern u1="&#x2026;" u2="T" k="123" />
<hkern u1="&#x2026;" u2="Q" k="82" />
<hkern u1="&#x2026;" u2="O" k="82" />
<hkern u1="&#x2026;" u2="G" k="82" />
<hkern u1="&#x2026;" u2="C" k="82" />
<hkern u1="&#x2026;" u2="A" k="-61" />
<hkern u1="&#x2026;" u2="&#x39;" k="20" />
<hkern u1="&#x2026;" u2="&#x38;" k="31" />
<hkern u1="&#x2026;" u2="&#x36;" k="61" />
<hkern u1="&#x2026;" u2="&#x34;" k="102" />
<hkern u1="&#x2026;" u2="&#x31;" k="123" />
<hkern u1="&#x2026;" u2="&#x30;" k="82" />
<hkern u1="&#x2039;" u2="&#x178;" k="82" />
<hkern u1="&#x2039;" u2="&#xdd;" k="82" />
<hkern u1="&#x2039;" u2="Y" k="82" />
<hkern u1="&#x2039;" u2="W" k="41" />
<hkern u1="&#x2039;" u2="V" k="41" />
<hkern u1="&#x2039;" u2="T" k="82" />
<hkern u1="&#x203a;" u2="&#x178;" k="102" />
<hkern u1="&#x203a;" u2="&#xdd;" k="102" />
<hkern u1="&#x203a;" u2="z" k="102" />
<hkern u1="&#x203a;" u2="x" k="102" />
<hkern u1="&#x203a;" u2="Y" k="102" />
<hkern u1="&#x203a;" u2="W" k="82" />
<hkern u1="&#x203a;" u2="V" k="102" />
<hkern u1="&#x203a;" u2="T" k="225" />
</font>
</defs></svg> 