<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="omnessemibold_italic" horiz-adv-x="500" >
<font-face units-per-em="1000" ascent="700" descent="-300" />
<missing-glyph horiz-adv-x="170" />
<glyph unicode="&#xfb01;" horiz-adv-x="643" d="M515 630q0 36 22.5 59t59.5 23q36 0 56.5 -16.5t20.5 -44.5q0 -36 -23 -59t-59 -23t-56.5 16.5t-20.5 44.5zM62 63l67 315h-38q-51 0 -51 37q0 38 20 59q16 14 49 14h43l8 33q38 180 191 180q68 0 103 -33q18 -18 18 -43q0 -17 -9 -32.5t-18 -23.5t-11 -6q-23 30 -68 30 q-56 0 -72 -75l-7 -30h66q54 0 54 -37q0 -38 -21 -59q-16 -14 -49 -14h-67l-67 -315q-7 -37 -22 -52t-43 -15h-15q-75 0 -61 67zM414 125l63 299q8 38 23 53t42 15h15q76 0 61 -68l-64 -303q-9 -40 -1 -58.5t39 -22.5q3 -1 -2.5 -13t-25.5 -24.5t-53 -12.5q-65 0 -88 36 t-9 99z" />
<glyph unicode="&#xfb02;" horiz-adv-x="643" d="M62 63l67 315h-38q-51 0 -51 37q0 38 20 59q16 14 49 14h43l8 33q38 180 191 180q68 0 103 -33q18 -18 18 -43q0 -17 -9 -32.5t-18 -23.5t-11 -6q-23 30 -68 30q-56 0 -72 -75l-7 -30h66q54 0 54 -37q0 -38 -21 -59q-16 -14 -49 -14h-67l-67 -315q-7 -37 -22 -52t-43 -15 h-15q-75 0 -61 67zM414 125l106 502q8 38 23 53t42 15h15q32 0 50.5 -16t10.5 -52l-107 -506q-9 -40 -1 -58.5t39 -22.5q3 -1 -2.5 -13t-25.5 -24.5t-53 -12.5q-126 0 -97 135z" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="333" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="170" />
<glyph unicode=" "  horiz-adv-x="170" />
<glyph unicode="&#x09;" horiz-adv-x="170" />
<glyph unicode="&#xa0;" horiz-adv-x="170" />
<glyph unicode="!" horiz-adv-x="277" d="M136 200q-24 0 -32.5 10.5t-8.5 40.5l28 324q0 81 85 81q58 0 80 -25.5t1 -72.5l-109 -318q-9 -22 -18.5 -31t-25.5 -9zM22 44q0 51 26 77q19 19 63 19q68 0 68 -52t-26 -77q-18 -18 -63 -18q-68 0 -68 51z" />
<glyph unicode="&#x22;" horiz-adv-x="433" d="M341 354q-25 0 -34.5 8.5t-8.5 33.5l14 189q5 70 72 70q103 0 70 -84l-72 -184q-8 -19 -16 -26t-25 -7zM152 354q-25 0 -35 8.5t-9 33.5l9 189q2 70 70 70q104 0 73 -84l-68 -184q-7 -19 -15 -26t-25 -7z" />
<glyph unicode="#" horiz-adv-x="656" d="M98 48q0 13 7 39l19 61h-62q-24 0 -38 9.5t-14 25.5q0 36 21 57q13 13 46 13h80l43 141h-61q-52 0 -52 36t20 56q14 13 47 13h79l47 153q2 6 16 6q24 0 46 -15.5t22 -43.5q0 -13 -7 -39l-19 -61h124l47 153q2 6 15 6q25 0 47 -15.5t22 -43.5q0 -13 -7 -39l-19 -61h62 q51 0 51 -35q0 -37 -20 -56q-13 -14 -47 -14h-79l-44 -141h62q52 0 52 -36q0 -34 -20 -56q-14 -13 -47 -13h-80l-46 -153q-2 -5 -16 -5q-24 0 -46 15.5t-22 42.5q0 18 7 39l19 61h-124l-47 -153q-2 -5 -16 -5q-24 0 -46 15.5t-22 42.5zM257 251h133l44 145h-133z" />
<glyph unicode="$" horiz-adv-x="556" d="M43 70q-39 38 -39 81q0 22 13 38.5t26 23t15 3.5q28 -47 79.5 -77t107.5 -32q57 -2 85.5 19.5t28.5 59.5q0 22 -17.5 39.5t-36 26.5t-59.5 26q-35 15 -56.5 27t-47.5 33.5t-39 52t-13 69.5q0 78 56 130.5t160 58.5l7 34q15 66 62 66h11q25 0 40 -17t8 -49l-10 -44 q70 -16 111 -57q33 -32 33 -72q0 -24 -14 -42.5t-28.5 -25.5t-17.5 -3q-28 46 -74 73.5t-102 28.5q-47 1 -74 -20.5t-27 -54.5q0 -24 18 -43t37 -28.5t62 -27.5q38 -16 61 -28t50.5 -33t41 -49.5t13.5 -64.5q0 -84 -57.5 -136.5t-169.5 -56.5l-8 -37q-13 -65 -61 -65h-11 q-26 0 -40 16.5t-8 48.5l10 46q-80 16 -126 62z" />
<glyph unicode="%" horiz-adv-x="751" d="M31 39q0 23 32 51l634 570q3 3 16 -3t26 -19.5t13 -28.5q0 -23 -32 -51l-634 -570q-3 -3 -16 3t-26 19.5t-13 28.5zM213 342q-67 0 -107.5 38.5t-40.5 99.5q0 75 47 126t127 51q68 0 108 -38.5t40 -99.5q0 -75 -47 -126t-127 -51zM219 418q34 0 56 28t22 68 q0 28 -18.5 47.5t-44.5 19.5q-35 0 -57 -28t-22 -68q0 -29 18.5 -48t45.5 -19zM397 131q0 75 47.5 126t127.5 51q67 0 107.5 -38.5t40.5 -99.5q0 -75 -47 -126t-127 -51q-68 0 -108.5 38.5t-40.5 99.5zM488 136q0 -28 18.5 -47.5t44.5 -19.5q34 0 56 28t22 68q0 29 -18 48 t-45 19q-34 0 -56 -28t-22 -68z" />
<glyph unicode="&#x26;" horiz-adv-x="696" d="M8 164q0 83 49 131.5t138 74.5q-32 45 -32 102q0 77 57 132.5t150 55.5q78 0 128.5 -42t50.5 -109q0 -68 -45 -108.5t-129 -61.5l105 -126q69 75 87 163q1 2 10.5 3t23 -1.5t26.5 -8.5t22.5 -21t9.5 -36q0 -34 -25.5 -82.5t-76.5 -94.5l5 -5q23 -27 43.5 -39.5 t52.5 -14.5q1 0 2 -8.5t-1.5 -21t-9.5 -25t-24.5 -21t-42.5 -8.5q-38 0 -61.5 14.5t-54.5 49.5l-5 6q-97 -74 -224 -74q-109 0 -169 49t-60 127zM317 403q61 14 89 35.5t28 63.5q0 27 -18.5 44.5t-48.5 17.5q-35 0 -56.5 -25t-21.5 -59q0 -42 28 -77zM137 183 q0 -40 29 -66.5t86 -26.5q78 0 138 49l-137 163q-116 -31 -116 -119z" />
<glyph unicode="'" horiz-adv-x="238" d="M151 354q-25 0 -35 8.5t-9 33.5l11 189q3 70 70 70q105 0 72 -84l-69 -184q-7 -18 -15.5 -25.5t-24.5 -7.5z" />
<glyph unicode="(" horiz-adv-x="405" d="M28 140q0 114 44.5 223.5t123.5 189.5q104 102 211 102q32 0 50 -15.5t18 -43.5q0 -12 -5 -24.5t-10.5 -19.5t-7.5 -7q-88 -7 -158 -76q-61 -61 -93 -150.5t-32 -181.5q0 -125 69 -194q28 -26 70 -42q1 -1 4 -6.5t5.5 -15t2.5 -17.5q0 -27 -19 -44.5t-51 -17.5 q-64 0 -122 58q-47 46 -73.5 119.5t-26.5 162.5z" />
<glyph unicode=")" horiz-adv-x="405" d="M-53 -89q89 7 158 76q61 61 93.5 151t32.5 181q0 124 -70 194q-29 27 -70 42q-1 1 -4 6.5t-5.5 15t-2.5 17.5q0 27 19 44.5t51 17.5q65 0 123 -58q46 -46 73 -119.5t27 -162.5q0 -114 -45 -223.5t-124 -189.5q-102 -102 -211 -102q-32 0 -50 15.5t-18 43.5q0 12 5 24.5 t10.5 19.5t7.5 7z" />
<glyph unicode="*" horiz-adv-x="411" d="M142 307l-4 2q-29 17 -9 54l47 80l-80 34q-21 10 -25.5 20.5t1.5 28.5l1 4q5 17 15 23t33 2l88 -20l9 86q3 23 12 30.5t26 7.5h4q17 0 26.5 -8t12.5 -30l7 -87l87 21q23 4 33.5 -2t15.5 -23l1 -4q6 -18 1.5 -28.5t-25.5 -20.5l-81 -35l48 -79q19 -35 -10 -54l-3 -2 q-26 -19 -54 8l-62 71l-62 -71q-15 -15 -27.5 -16t-25.5 8z" />
<glyph unicode="+" horiz-adv-x="536" d="M176 73l28 133h-122q-52 0 -52 38q0 34 18 53q15 15 48 15h130l29 131q11 56 54 56h13q26 0 38 -15.5t6 -44.5l-28 -127h123q52 0 52 -38q0 -34 -18 -53q-15 -15 -48 -15h-131l-29 -137q-13 -56 -55 -56h-13q-26 0 -38 15.5t-5 44.5z" />
<glyph unicode="," horiz-adv-x="243" d="M-51 -80l100 223q1 1 13 1.5t29 -2t33.5 -8t28.5 -19t12 -32.5q0 -34 -39 -76l-112 -127q-13 -13 -43.5 1.5t-21.5 38.5z" />
<glyph unicode="-" horiz-adv-x="428" d="M91 180q-58 0 -58 45q0 14 6.5 35t17.5 32q16 17 53 17h227q58 0 58 -44q0 -14 -6.5 -35t-17.5 -33q-17 -17 -53 -17h-227z" />
<glyph unicode="." horiz-adv-x="239" d="M3 44q0 52 26 77q19 19 63 19q68 0 68 -52q0 -51 -26 -77q-18 -18 -63 -18q-68 0 -68 51z" />
<glyph unicode="/" horiz-adv-x="359" d="M-61 -98l398 752q1 2 10.5 2t23 -2.5t27 -8t23 -18t9.5 -29.5q0 -22 -17 -53l-398 -752q-1 -2 -10.5 -2t-23 2.5t-27 8t-23 18t-9.5 29.5q0 22 17 53z" />
<glyph unicode="0" horiz-adv-x="643" d="M44 223q0 62 24 161q31 136 106.5 206t205.5 70q120 0 186.5 -65.5t66.5 -170.5q0 -55 -24 -161q-31 -136 -106 -206t-206 -70q-120 0 -186.5 65t-66.5 171zM190 226q0 -55 32 -88t90 -33q65 0 97.5 40t51.5 116q26 110 26 160q0 54 -32.5 87.5t-89.5 33.5 q-65 0 -97.5 -40t-51.5 -117q-26 -109 -26 -159z" />
<glyph unicode="1" horiz-adv-x="391" d="M128 67l85 401q-25 -20 -56 -20q-39 0 -61 20q-18 18 -18 50q0 27 12.5 48.5t24 30t13.5 6.5q24 -24 68 -24q31 0 56 20.5t38 54.5h39q70 0 54 -70l-111 -521q-8 -35 -25 -51t-48 -16h-18q-31 0 -45.5 18.5t-7.5 52.5z" />
<glyph unicode="2" horiz-adv-x="554" d="M54 0q-5 0 -16 14t-21.5 39.5t-10.5 49.5q0 89 57 146q34 35 83 55.5t128 42.5q90 29 125 52.5t35 68.5q0 36 -27.5 57t-77.5 21q-102 0 -158 -105q-2 -3 -20.5 1t-37.5 20t-19 43q0 46 40 86q71 69 197 69q113 0 177.5 -51t64.5 -135q0 -99 -58 -148t-191 -86 q-77 -21 -110 -35t-54 -35q-22 -22 -28 -54h291q74 0 74 -48q0 -15 -6 -31.5t-12.5 -26.5t-8.5 -10h-416z" />
<glyph unicode="3" horiz-adv-x="564" d="M36 52q-38 38 -38 82q0 23 9.5 40t22.5 25t26.5 13.5t22.5 4t10 -2.5q9 -54 49 -83.5t106 -29.5q64 0 98.5 29t34.5 75q0 36 -25.5 56.5t-76.5 20.5h-31q-53 0 -53 41q0 33 18 51q16 16 62 16h39q116 0 116 82q1 34 -30 54.5t-80 20.5q-104 0 -150 -86q-4 -1 -21 -1 t-37 17t-20 45q0 35 31 68q70 70 205 70q114 0 179 -43.5t65 -122.5q0 -61 -37.5 -105.5t-111.5 -57.5q100 -36 100 -135q0 -40 -13.5 -74.5t-43 -66t-85.5 -49.5t-133 -18q-144 0 -208 64z" />
<glyph unicode="4" horiz-adv-x="605" d="M62 161q-4 0 -13 11t-17.5 31.5t-8.5 40.5q0 24 10.5 44.5t39.5 54.5l247 256q30 33 54 44t72 11q61 0 84.5 -23t12.5 -75l-61 -284h50q54 0 54 -38t-21 -59q-13 -14 -50 -14h-52l-21 -100q-8 -38 -22.5 -53t-40.5 -15h-17q-30 0 -46.5 17t-8.5 53l21 98h-266zM151 269 h206l58 278z" />
<glyph unicode="5" horiz-adv-x="551" d="M32 51q-31 31 -31 72q0 23 14.5 42t29 26.5t16.5 4.5q58 -99 184 -99q61 0 98.5 33t37.5 84q0 91 -118 91q-68 0 -126 -32q-42 9 -58.5 32t-7.5 66l42 200q16 76 92 76h353q2 0 4.5 -4.5t4.5 -14t2 -21.5q0 -32 -19.5 -56t-61.5 -24h-260l-30 -137q45 15 100 15 q107 0 163.5 -48t56.5 -130q0 -108 -74.5 -173.5t-199.5 -65.5q-151 0 -212 63z" />
<glyph unicode="6" horiz-adv-x="603" d="M46 235q0 77 30.5 180t90.5 164q81 81 217 81q125 0 189 -62q30 -31 30 -66q0 -29 -19.5 -47t-38.5 -22t-21 -1q-16 37 -52.5 59t-84.5 22q-136 0 -181 -185q71 48 167 48q87 0 142 -45.5t55 -124.5q0 -104 -74.5 -176t-198.5 -72q-116 0 -183.5 66.5t-67.5 180.5z M187 228q-4 -61 27 -96t89 -35t92 35t34 88q0 43 -29 67t-83 24q-69 0 -126 -47q-4 -16 -4 -36z" />
<glyph unicode="7" horiz-adv-x="546" d="M73 56q0 20 9.5 36.5t34.5 45.5l311 385h-263q-78 0 -78 52q0 15 6.5 33t13 28.5t8.5 10.5h418q5 0 16 -11t21 -33t10 -44q0 -47 -49 -105l-365 -461q-2 -3 -24.5 -1t-45.5 18t-23 46z" />
<glyph unicode="8" horiz-adv-x="600" d="M17 165q0 61 43.5 108t123.5 66q-84 36 -84 123q0 85 73.5 141.5t184.5 56.5t176.5 -47t65.5 -122q0 -62 -37 -106t-107 -61q100 -41 100 -136q0 -89 -74.5 -144.5t-199.5 -55.5q-124 0 -194.5 47t-70.5 130zM344 374q53 0 85.5 27.5t32.5 70.5q0 37 -30.5 59t-79.5 22 q-51 0 -84 -28t-33 -71q0 -36 30 -58t79 -22zM164 182q0 -40 33.5 -64.5t89.5 -24.5q58 0 93 30t35 76q0 39 -33.5 62.5t-91.5 23.5q-54 0 -90 -28.5t-36 -74.5z" />
<glyph unicode="9" horiz-adv-x="603" d="M63 47q-29 30 -29 66q0 27 18 45t35.5 23t19.5 2q19 -36 56 -57t83 -21q135 0 180 173q-73 -46 -162 -46q-90 0 -144 45.5t-54 128.5q0 108 74.5 181t198.5 73q118 0 185.5 -66.5t67.5 -180.5q0 -77 -30.5 -180t-90.5 -164q-81 -81 -217 -81q-132 0 -191 59zM318 327 q71 0 126 47q5 25 5 43q4 63 -26.5 98.5t-87.5 35.5q-58 0 -93 -36.5t-35 -91.5q0 -45 28 -70.5t83 -25.5z" />
<glyph unicode=":" horiz-adv-x="255" d="M144 298q-69 0 -69 51t26 77q19 19 63 19q69 0 69 -52q0 -51 -26 -77q-18 -18 -63 -18zM11 44q0 51 26 77q19 19 63 19q68 0 68 -52q0 -51 -26 -77q-18 -18 -63 -18q-68 0 -68 51z" />
<glyph unicode=";" horiz-adv-x="260" d="M148 298q-68 0 -68 51t26 77q19 19 63 19q68 0 68 -52q0 -51 -26 -77q-18 -18 -63 -18zM-43 -80l101 223q1 1 13 1.5t29 -2t33.5 -8t28.5 -19t12 -32.5q0 -33 -40 -76l-112 -127q-13 -13 -43.5 1.5t-21.5 38.5z" />
<glyph unicode="&#x3c;" horiz-adv-x="459" d="M81 188q-43 27 -42 70q1 49 56 76l332 160q3 1 11 -7.5t15 -25.5t7 -34q0 -47 -62 -74l-214 -97l150 -87q60 -33 56 -80q-1 -17 -10 -33t-18 -24t-12 -6z" />
<glyph unicode="=" horiz-adv-x="536" d="M127 318q-58 0 -58 41q0 42 22 64q16 16 54 16h321q58 0 58 -41q0 -42 -22 -64q-15 -16 -54 -16h-321zM76 76q-59 0 -59 41q0 42 23 65q15 15 53 15h321q59 0 59 -41q0 -42 -23 -64q-15 -16 -54 -16h-320z" />
<glyph unicode="&#x3e;" horiz-adv-x="459" d="M38 26q-3 -1 -11 7.5t-15 25.5t-7 34q0 46 63 74l213 98l-150 86q-60 33 -56 80q1 17 10 33t18 24t12 6l270 -162q42 -26 41 -69q-1 -50 -56 -76z" />
<glyph unicode="?" horiz-adv-x="499" d="M244 194q-40 0 -64.5 22t-24.5 57q0 43 28.5 68.5t91.5 45.5q58 18 80.5 36t22.5 51t-25 53.5t-66 20.5q-49 0 -84 -26t-52 -71q-1 -2 -19.5 1.5t-37.5 19t-19 40.5q0 44 39 83q65 65 182 65q103 0 166 -50t63 -129q0 -43 -14.5 -75t-43 -51.5t-50 -29.5t-55.5 -20 q-46 -13 -65.5 -25.5t-19.5 -29.5q0 -22 19 -31q2 -1 -2 -7t-17 -12t-33 -6zM112 44q0 51 26 77q19 19 63 19q68 0 68 -52t-26 -77q-18 -18 -63 -18q-68 0 -68 51z" />
<glyph unicode="@" horiz-adv-x="740" d="M54 281q0 161 106.5 270t265.5 109q136 0 220 -73.5t84 -193.5q0 -105 -51 -167.5t-135 -62.5q-97 0 -113 71q-34 -66 -110 -66q-48 0 -78.5 31.5t-30.5 84.5q0 73 45 129.5t112 56.5t91 -52q18 47 67 47q9 0 17 -2t12 -4.5t4 -4.5l-34 -157q-14 -64 40 -64q47 0 74.5 41 t27.5 116q0 94 -69.5 153.5t-178.5 59.5q-129 0 -216 -91t-87 -227q0 -107 71 -173.5t190 -66.5q93 0 158 51q20 13 35 13q11 0 20.5 -9t13 -18t1.5 -11q-82 -84 -233 -84q-148 0 -233.5 79.5t-85.5 214.5zM304 299q0 -26 15 -41.5t38 -15.5q28 0 50 20.5t29 51.5l7 37 q-15 45 -59 45q-35 0 -57.5 -29.5t-22.5 -67.5z" />
<glyph unicode="A" horiz-adv-x="659" d="M-8 59q0 30 32 81l283 471q14 23 34 32.5t61 9.5q47 0 66.5 -11.5t23.5 -34.5l109 -566q2 -11 -26 -29t-58 -18q-58 0 -68 69l-19 102h-236l-97 -172q-1 -1 -11.5 -1.5t-26 2.5t-31 9.5t-26 21t-10.5 34.5zM245 275h177l-42 234z" />
<glyph unicode="B" horiz-adv-x="637" d="M26 94l101 477q15 76 92 76h198q102 0 155 -37t53 -108q0 -129 -138 -167q57 -15 86 -49.5t29 -82.5q0 -203 -281 -203h-221q-95 0 -74 94zM230 375h121q57 0 89.5 26t32.5 71q-1 65 -101 65h-107zM175 110h143q71 0 103 24.5t32 77.5q0 69 -109 69h-133z" />
<glyph unicode="C" horiz-adv-x="648" d="M48 273q0 158 105.5 272.5t261.5 114.5q135 0 206 -71q41 -41 41 -82q0 -35 -23.5 -55.5t-46.5 -25t-27 -0.5q-39 102 -149 102q-91 0 -153 -70t-62 -168q0 -76 45.5 -123t118.5 -47q105 0 161 83q2 3 19.5 -3t35.5 -24t18 -44q0 -33 -35 -68q-33 -33 -88 -54.5 t-120 -21.5q-141 0 -224.5 77t-83.5 208z" />
<glyph unicode="D" horiz-adv-x="673" d="M26 94l101 477q15 76 92 76h125q147 0 230.5 -72.5t83.5 -196.5q0 -104 -43.5 -188t-136.5 -137t-224 -53h-154q-95 0 -74 94zM179 125h95q114 0 175 67t61 167q0 77 -47.5 120t-132.5 43h-67z" />
<glyph unicode="E" horiz-adv-x="600" d="M26 94l101 477q16 76 92 76h357q63 0 63 -44q0 -14 -7 -35.5t-18 -32.5q-15 -16 -57 -16h-293l-28 -129h219q58 0 58 -41t-22 -64q-16 -16 -53 -16h-228l-30 -141h292q62 0 62 -43t-24 -69q-16 -16 -57 -16h-353q-95 0 -74 94z" />
<glyph unicode="F" horiz-adv-x="556" d="M20 67l107 504q15 76 92 76h334q61 0 61 -43q0 -14 -6.5 -35t-17.5 -32q-15 -16 -56 -16h-272l-34 -161h194q59 0 59 -40q0 -42 -23 -65q-15 -15 -54 -15h-198l-37 -177q-15 -67 -74 -67h-22q-31 0 -45.5 18.5t-7.5 52.5z" />
<glyph unicode="G" horiz-adv-x="717" d="M48 267q0 161 107 277t269 116q145 0 220 -75q37 -37 37 -77q0 -21 -9 -36t-22.5 -22t-27 -10t-23.5 -3t-11 2q-21 42 -63.5 68.5t-97.5 26.5q-97 0 -162.5 -74t-65.5 -181q-1 -83 45 -130.5t123 -47.5q70 0 115 39q22 20 33 39t22 63h-117q-54 0 -54 38t21 60 q14 14 49 14h192q67 0 51 -72l-58 -274q-1 -2 -6 -5t-15 -5t-22 -2q-67 0 -68 67q-64 -75 -181 -75q-129 0 -205 74.5t-76 204.5z" />
<glyph unicode="H" horiz-adv-x="685" d="M20 67l110 517q8 35 25 51t48 16h21q32 0 46.5 -18t7.5 -52l-41 -194h273l41 197q16 67 73 67h21q31 0 46 -18.5t8 -52.5l-110 -517q-8 -35 -25 -51t-49 -16h-21q-31 0 -45.5 18t-7.5 52l42 196h-273l-42 -199q-15 -67 -74 -67h-21q-31 0 -45.5 18.5t-7.5 52.5z" />
<glyph unicode="I" horiz-adv-x="279" d="M28 67l110 517q8 35 25 51t48 16h22q31 0 45.5 -18.5t7.5 -52.5l-110 -517q-8 -35 -25 -51t-48 -16h-21q-31 0 -46 18.5t-8 52.5z" />
<glyph unicode="J" horiz-adv-x="491" d="M27 40q-29 29 -29 61q0 30 16.5 50.5t33 26.5t18.5 3q36 -68 109 -68q49 0 76 29t41 97l74 345q8 35 24.5 51t47.5 16h21q31 0 46 -18.5t8 -51.5l-78 -367q-24 -114 -85.5 -170t-168.5 -56q-100 0 -154 52z" />
<glyph unicode="K" horiz-adv-x="608" d="M20 67l110 517q8 35 25 51t48 16h21q31 0 46 -18.5t8 -52.5l-46 -216l316 296q2 2 22.5 -4t42 -23.5t21.5 -42.5q0 -37 -47 -78l-230 -197l112 -165q28 -43 48 -58.5t49 -17.5q5 0 1.5 -20t-26.5 -40t-65 -20q-47 0 -76.5 26t-65.5 90l-117 185l-49 -232q-8 -35 -25 -51 t-49 -16h-21q-31 0 -45.5 18.5t-7.5 52.5z" />
<glyph unicode="L" horiz-adv-x="528" d="M26 94l104 490q8 35 25 51t48 16h21q32 0 46.5 -18t7.5 -52l-97 -452h237q30 0 46.5 -12t16.5 -32q0 -15 -6.5 -36t-18.5 -33q-15 -16 -57 -16h-299q-95 0 -74 94z" />
<glyph unicode="M" horiz-adv-x="759" d="M20 67l108 508q17 76 87 76h37q33 0 47 -12t20 -43l65 -304l199 309q18 29 36 39.5t48 10.5h45q36 0 51.5 -20.5t7.5 -56.5l-109 -511q-8 -35 -24 -51t-47 -16h-17q-31 0 -44 18t-6 53l81 381l-165 -263q-11 -18 -16.5 -24.5t-21.5 -13.5t-41 -7q-29 0 -44.5 7.5 t-18.5 15.5t-6 26l-53 254l-81 -380q-8 -36 -24 -51.5t-47 -15.5h-17q-30 0 -43.5 18.5t-6.5 52.5z" />
<glyph unicode="N" horiz-adv-x="692" d="M20 67l108 509q16 75 81 75h18q25 0 40 -10.5t30 -36.5l194 -381l76 361q8 35 24 51t47 16h16q31 0 44.5 -18.5t6.5 -52.5l-109 -512q-15 -72 -79 -72h-9q-25 0 -40 10.5t-30 36.5l-201 396l-79 -376q-8 -35 -24 -51t-47 -16h-17q-30 0 -43.5 18.5t-6.5 52.5z" />
<glyph unicode="O" horiz-adv-x="729" d="M49 274q0 161 104 273.5t261 112.5q141 0 220.5 -81.5t79.5 -203.5q0 -161 -104 -274t-261 -113q-141 0 -220.5 81.5t-79.5 204.5zM197 284q0 -72 43 -119.5t118 -47.5q83 0 145.5 71.5t62.5 176.5q0 72 -43 119t-118 47q-84 0 -146 -71.5t-62 -175.5z" />
<glyph unicode="P" horiz-adv-x="589" d="M20 67l107 504q15 76 92 76h149q117 0 179 -47.5t62 -138.5q0 -117 -72.5 -184t-214.5 -67h-123l-31 -147q-15 -67 -74 -67h-21q-31 0 -45.5 18.5t-7.5 52.5zM221 321h113q61 0 95.5 33t34.5 92q0 86 -111 86h-88z" />
<glyph unicode="Q" horiz-adv-x="735" d="M49 274q0 161 104.5 273.5t263.5 112.5q142 0 220 -81t78 -204q0 -152 -100 -265l2 -2q34 -45 63 -58q3 -1 -1.5 -16.5t-23 -31.5t-47.5 -16q-31 0 -50.5 9.5t-41.5 36.5q-79 -42 -172 -42q-141 0 -218 80t-77 204zM197 284q0 -72 43 -119.5t117 -47.5q43 0 80 18l-52 70 q-16 25 -16 49q0 21 19 38t40 17q33 0 57 -36l42 -57q42 66 42 149q0 72 -43 119t-117 47q-86 0 -149 -71t-63 -176z" />
<glyph unicode="R" horiz-adv-x="613" d="M20 67l107 504q15 76 92 76h161q120 0 181.5 -46t61.5 -134q0 -93 -57 -151.5t-158 -68.5l56 -95q25 -42 44.5 -57.5t51.5 -18.5q0 -1 2 -9t-3.5 -20.5t-14 -24t-28.5 -20t-48 -8.5q-44 0 -73.5 23.5t-64.5 89.5l-68 123h-60l-35 -167q-15 -67 -74 -67h-20 q-31 0 -45.5 18.5t-7.5 52.5zM223 338h120q64 0 97 30.5t33 83.5q0 80 -109 80h-100z" />
<glyph unicode="S" horiz-adv-x="563" d="M35 59q-37 37 -37 78q0 22 14.5 41t29 27t16.5 5q29 -48 83 -78t118 -30q106 0 106 80q0 29 -25.5 46t-80.5 38l-9 3q-36 14 -59.5 27t-50.5 34.5t-40.5 52.5t-13.5 71q0 87 66 146.5t187 59.5q144 0 209 -67q33 -31 33 -71q0 -25 -15.5 -44.5t-31 -27.5t-18.5 -5 q-28 46 -75 73t-105 27q-47 0 -75.5 -21.5t-28.5 -54.5q0 -30 25.5 -47.5t81.5 -40.5q5 -2 8.5 -3.5t7.5 -2.5q38 -14 63 -27t52.5 -34t41 -50t13.5 -67q0 -95 -68 -152.5t-199 -57.5q-151 0 -223 72z" />
<glyph unicode="T" horiz-adv-x="605" d="M192 67l96 451h-141q-63 0 -63 44q0 14 6.5 35.5t17.5 33.5q16 16 58 16h436q30 0 46.5 -12t16.5 -32q0 -15 -6.5 -36t-18.5 -33q-16 -16 -57 -16h-147l-97 -455q-15 -67 -74 -67h-20q-31 0 -45.5 18.5t-7.5 52.5z" />
<glyph unicode="U" horiz-adv-x="658" d="M62 298l61 286q8 35 25 51t48 16h21q31 0 45.5 -18t7.5 -52l-62 -294q-35 -170 116 -170q111 0 142 146l68 321q15 67 73 67h19q31 0 46 -18.5t8 -51.5l-71 -330q-55 -263 -300 -263q-151 0 -215.5 79t-31.5 231z" />
<glyph unicode="V" horiz-adv-x="642" d="M197 56l-105 546q-3 11 27.5 30t62.5 19q61 0 69 -71l60 -401l261 474q1 3 28 0t54.5 -21.5t27.5 -51.5q0 -30 -33 -79l-280 -456q-19 -29 -39.5 -40t-51.5 -11q-69 0 -81 62z" />
<glyph unicode="W" horiz-adv-x="949" d="M145 58l-26 556q0 10 27 24t58 14q35 0 53 -22.5t16 -58.5v-393l175 347q28 54 90 54q79 0 82 -65l23 -338l207 476q0 1 12 1.5t29 -2t34 -9t29 -22.5t12 -38q0 -28 -26 -75l-223 -461q-27 -52 -96 -52q-44 0 -64 16t-22 49l-25 342l-185 -354q-16 -31 -38.5 -42 t-58.5 -11q-42 0 -61.5 15.5t-21.5 48.5z" />
<glyph unicode="X" horiz-adv-x="626" d="M-6 61q0 35 43 75l203 201l-128 266q-3 5 7 17t32 23t46 11q51 0 73 -61l79 -183l228 248q2 2 23.5 -4t43.5 -24t22 -44q0 -34 -42 -74l-207 -205l64 -139q23 -52 44 -69.5t52 -18.5q0 -1 1.5 -10t-5 -22.5t-15 -26.5t-28.5 -22.5t-47 -9.5q-41 0 -70.5 30t-53.5 97 l-50 120l-226 -247q-2 -2 -23.5 4t-43.5 24t-22 44z" />
<glyph unicode="Y" horiz-adv-x="599" d="M190 64l42 198l-152 336q-2 5 8 17t32.5 24t48.5 12q44 0 65 -58l89 -217l233 281q2 2 23.5 -2t44 -22t22.5 -47q0 -33 -43 -80l-224 -248l-42 -195q-14 -67 -74 -67h-21q-31 0 -45 17t-7 51z" />
<glyph unicode="Z" horiz-adv-x="580" d="M34 0q-4 0 -13.5 11t-18 30t-8.5 37q0 26 11.5 44.5t41.5 45.5l371 355h-240q-78 0 -78 52q0 15 6 33t12.5 28.5t8.5 10.5h442q4 0 13.5 -10.5t18 -30t8.5 -37.5q0 -26 -11.5 -44.5t-41.5 -45.5l-371 -355h262q78 0 78 -52q0 -15 -6 -33t-13 -28.5t-9 -10.5h-463z" />
<glyph unicode="[" horiz-adv-x="373" d="M-14 -104l145 682q15 76 92 76h155q27 0 41.5 -11t14.5 -29q0 -39 -22 -61q-15 -15 -51 -15h-108l-132 -621h102q56 0 56 -39q0 -41 -21 -62q-14 -14 -52 -14h-146q-95 0 -74 94z" />
<glyph unicode="\" horiz-adv-x="359" d="M165 -195l-78 764q-10 87 57 87q12 0 25 -3t20.5 -6t7.5 -5l79 -764q10 -87 -57 -87q-12 0 -25.5 3t-21 6t-7.5 5z" />
<glyph unicode="]" horiz-adv-x="373" d="M5 -83h108l132 621h-103q-56 0 -56 39q0 40 22 62q15 15 51 15h147q95 0 74 -94l-145 -682q-16 -76 -92 -76h-155q-56 0 -56 39t21 62q14 14 52 14z" />
<glyph unicode="^" d="M193 441q-19 0 -30.5 12t-11.5 29q0 42 66 92q67 52 116 63q32 -11 77 -62q44 -49 44 -82q0 -23 -13.5 -37.5t-35.5 -14.5q-38 0 -70 59q-3 7 -10 21t-9 19q-30 -35 -35 -40q-48 -59 -88 -59z" />
<glyph unicode="_" horiz-adv-x="496" d="M14 0h357q52 0 52 -36q0 -38 -20 -58q-14 -14 -48 -14h-356q-53 0 -53 36t20 58q14 14 48 14z" />
<glyph unicode="`" d="M221 598q-45 29 -45 70q0 26 17.5 43.5t43.5 17.5q29 0 54 -19q29 -19 55.5 -53.5t39.5 -62t9 -34.5q-9 -5 -32 -6.5t-65.5 10.5t-76.5 34z" />
<glyph unicode="a" horiz-adv-x="612" d="M25 188q0 122 77 217t191 95q52 0 93.5 -23t63.5 -65q14 42 39 61t64 19q13 0 25 -3.5t18.5 -7t6.5 -6.5l-76 -354q-9 -41 -1 -58.5t37 -22.5q2 -2 -2.5 -13.5t-24 -24t-51.5 -12.5q-102 0 -99 100q-63 -102 -176 -102q-81 0 -133 54t-52 146zM162 208q0 -49 28 -78.5 t71 -29.5q54 0 96 38.5t54 96.5l14 68q-28 87 -113 87q-66 0 -108 -55.5t-42 -126.5z" />
<glyph unicode="b" horiz-adv-x="603" d="M3 13l130 614q8 38 23 53t42 15h16q32 0 50 -16t10 -52l-44 -208q58 81 155 81q82 0 137 -56t55 -153q0 -121 -72 -212t-183 -91q-54 0 -98.5 25t-66.5 70q-14 -47 -38.5 -67t-66.5 -20q-13 0 -25 3t-18.5 7t-5.5 7zM192 242q-12 -60 18.5 -101.5t91.5 -41.5q64 0 101 52 t37 122q0 52 -29.5 83.5t-74.5 31.5q-78 0 -129 -76z" />
<glyph unicode="c" horiz-adv-x="514" d="M23 212q0 116 81.5 202t197.5 86q115 0 174 -61q37 -37 37 -79q0 -20 -9 -35.5t-22 -22t-26.5 -10t-22.5 -3t-10 3.5q-8 42 -38 68t-80 26q-66 0 -109 -49.5t-43 -114.5q0 -54 33 -88t90 -34q76 0 119 69q2 3 18 -1t32 -19t16 -37q0 -33 -33 -65q-60 -60 -164 -60 q-108 0 -174.5 59.5t-66.5 164.5z" />
<glyph unicode="d" horiz-adv-x="612" d="M28 198q0 120 72 211t183 91q53 0 96.5 -23t68.5 -65l46 215q7 38 22 53t43 15h15q32 0 50.5 -16t10.5 -52l-108 -506q-9 -41 -1 -58.5t37 -22.5q2 -2 -2.5 -13.5t-24 -24t-51.5 -12.5q-50 0 -74.5 25t-24.5 70q-58 -97 -166 -97q-82 0 -137 56.5t-55 153.5zM165 214 q0 -52 29 -83t74 -31q51 0 90 36t51 94l15 73q-16 43 -43 65t-75 22q-65 0 -103 -52.5t-38 -123.5z" />
<glyph unicode="e" horiz-adv-x="528" d="M22 196q0 131 88.5 217.5t218.5 86.5q91 0 142 -39t51 -104q0 -80 -63 -120t-218 -40q-26 0 -100 4v-3q0 -52 31 -81t88 -29q90 0 144 71q2 2 16.5 -1.5t29 -17t14.5 -34.5q0 -32 -27 -60q-60 -58 -179 -58q-116 0 -176 57t-60 151zM159 287q39 -3 70 -3q174 0 174 69 q0 21 -21 36.5t-59 15.5q-54 0 -97.5 -33t-66.5 -85z" />
<glyph unicode="f" horiz-adv-x="381" d="M62 63l67 315h-38q-51 0 -51 37q0 38 20 59q16 14 49 14h43l8 33q38 180 191 180q68 0 103 -33q18 -18 18 -43q0 -17 -9 -32.5t-18 -23.5t-11 -6q-23 30 -68 30q-56 0 -72 -75l-7 -30h66q54 0 54 -37q0 -38 -21 -59q-16 -14 -49 -14h-67l-67 -315q-7 -37 -22 -52t-43 -15 h-15q-75 0 -61 67z" />
<glyph unicode="g" horiz-adv-x="605" d="M-2 -66q0 25 16.5 44.5t33 26.5t18.5 4q42 -93 156 -93q120 0 147 125l11 54q-60 -78 -160 -78q-82 0 -136 55.5t-54 149.5q0 109 74 193.5t188 84.5q51 0 94 -22.5t65 -62.5q27 77 102 77q13 0 25 -3.5t18.5 -7t6.5 -6.5l-94 -438q-25 -121 -94 -179t-198 -58 q-124 0 -186 62q-33 33 -33 72zM165 234q0 -49 30 -78.5t74 -29.5q52 0 92 32t51 82l14 68q-28 81 -114 81q-65 0 -106 -47t-41 -108z" />
<glyph unicode="h" horiz-adv-x="582" d="M14 63l119 564q8 38 23 53t42 15h16q32 0 50 -16t10 -52l-47 -221q28 44 70.5 69t93.5 25q72 0 113.5 -43.5t41.5 -114.5q0 -41 -29 -143q-23 -87 -23 -108q0 -37 40 -43q3 0 -2.5 -14.5t-27 -29t-55.5 -14.5q-48 0 -72.5 23t-24.5 64q0 32 24 120q22 78 22 105 q0 38 -20.5 59t-58.5 21q-66 0 -112 -71l-52 -248q-8 -37 -23 -52t-42 -15h-15q-75 0 -61 67z" />
<glyph unicode="i" horiz-adv-x="262" d="M211 569q-37 0 -57.5 16.5t-20.5 44.5q0 36 22.5 59t59.5 23t57.5 -16.5t20.5 -44.5q0 -36 -23 -59t-59 -23zM32 125l64 299q8 38 23 53t42 15h15q75 0 61 -68l-65 -303q-8 -41 0 -59t39 -22q2 -2 -3 -13.5t-25.5 -24t-53.5 -12.5q-126 0 -97 135z" />
<glyph unicode="j" horiz-adv-x="264" d="M220 569q-36 0 -56.5 16.5t-20.5 44.5q0 36 22.5 59t59.5 23q36 0 56.5 -16.5t20.5 -44.5q0 -36 -23 -59t-59 -23zM9 -26l96 450q8 38 23 53t42 15h15q75 0 61 -68l-99 -466q-17 -82 -58 -121t-111 -39q-68 0 -100 32q-19 19 -19 44q0 16 7.5 31.5t14.5 23.5t8 7 q23 -27 62 -27q23 0 37 14.5t21 50.5z" />
<glyph unicode="k" horiz-adv-x="510" d="M14 63l119 564q8 38 23 53t42 15h16q32 0 50 -16t10 -52l-71 -336l234 209q2 2 20 -3t36.5 -23t18.5 -45q0 -35 -42 -69l-151 -121l54 -89q26 -47 43.5 -61.5t50.5 -16.5q0 -1 1.5 -8.5t-4 -20t-13 -24t-26 -20t-42.5 -8.5q-42 0 -65.5 22t-58.5 89l-69 129l-8 -42 l-18 -84l-9 -42q-8 -37 -23 -52t-42 -15h-15q-75 0 -61 67z" />
<glyph unicode="l" horiz-adv-x="262" d="M32 125l107 502q8 38 23 53t42 15h15q32 0 50.5 -16t10.5 -52l-108 -506q-8 -41 0 -59t39 -22q2 -2 -3 -13.5t-25.5 -24t-53.5 -12.5q-126 0 -97 135z" />
<glyph unicode="m" horiz-adv-x="892" d="M14 63l89 420q3 9 39 9q33 0 55 -20t23 -63q63 91 163 91q53 0 91.5 -28.5t50.5 -77.5q68 106 180 106q68 0 109.5 -44.5t41.5 -113.5q0 -38 -28 -143q-24 -84 -24 -108q0 -37 40 -43q3 0 -2.5 -14.5t-27 -29t-55.5 -14.5q-96 0 -96 87q0 37 24 120q21 81 21 105 q0 37 -19.5 58.5t-52.5 21.5q-61 0 -105 -65l-54 -254q-8 -37 -24 -52t-44 -15h-15q-72 0 -58 67l44 208q10 49 -8.5 80t-57.5 31q-33 0 -57 -15t-48 -49l-54 -255q-8 -38 -24 -52.5t-44 -14.5h-15q-72 0 -58 67z" />
<glyph unicode="n" horiz-adv-x="582" d="M14 63l89 420q3 9 39 9q34 0 56.5 -21t21.5 -66q63 95 168 95q74 0 116 -43.5t42 -114.5q0 -41 -29 -143q-23 -87 -23 -108q0 -37 40 -43q3 0 -2.5 -14.5t-27 -29t-55.5 -14.5q-48 0 -72.5 23t-24.5 64q0 32 24 120q22 78 22 105q0 38 -20.5 59t-58.5 21q-66 0 -112 -71 l-52 -248q-8 -37 -23 -52t-42 -15h-15q-75 0 -61 67z" />
<glyph unicode="o" horiz-adv-x="559" d="M25 212q0 117 76.5 202.5t199.5 85.5q107 0 170.5 -63t63.5 -161q0 -117 -76.5 -202.5t-199.5 -85.5q-107 0 -170.5 63t-63.5 161zM148 217q0 -53 33 -87.5t82 -34.5q64 0 106.5 52.5t42.5 122.5q0 53 -32.5 88t-82.5 35q-64 0 -106.5 -52.5t-42.5 -123.5z" />
<glyph unicode="p" horiz-adv-x="603" d="M-27 -127l130 610q3 9 39 9q34 0 56.5 -21t21.5 -67q60 96 165 96q82 0 137 -56t55 -153q0 -121 -72 -212t-183 -91q-56 0 -98.5 25t-64.5 68l-45 -208q-7 -38 -22 -53t-43 -15h-15q-32 0 -50.5 16t-10.5 52zM192 242q-12 -60 18.5 -101.5t91.5 -41.5q64 0 101 52t37 122 q0 52 -29.5 83.5t-74.5 31.5q-78 0 -129 -76z" />
<glyph unicode="q" horiz-adv-x="605" d="M28 198q0 120 72 211t183 91q54 0 98 -24t68 -66q26 82 104 82q13 0 25 -3.5t18.5 -7t6.5 -6.5l-128 -602q-8 -38 -23 -53t-42 -15h-16q-32 0 -50 16t-10 52l40 193q-60 -78 -154 -78q-82 0 -137 56.5t-55 153.5zM165 214q0 -52 29 -83t74 -31q51 0 90 36t51 94l15 73 q-16 43 -43 65t-75 22q-65 0 -103 -52.5t-38 -123.5z" />
<glyph unicode="r" horiz-adv-x="414" d="M14 63l89 420q3 9 39 9q34 0 56.5 -20.5t21.5 -65.5q48 94 132 94q37 0 62 -19.5t25 -49.5q0 -31 -15.5 -53t-30.5 -30t-17 -6q-27 29 -68 29q-88 0 -125 -173l-28 -135q-8 -37 -23 -52t-42 -15h-15q-75 0 -61 67z" />
<glyph unicode="s" horiz-adv-x="467" d="M22 52q-28 28 -28 57q0 19 11.5 35t23 22.5t13.5 4.5q27 -36 71 -59.5t92 -23.5q36 0 55.5 13.5t19.5 35.5q0 19 -18 30t-61 27q-34 13 -52.5 22t-43 26t-35.5 40.5t-11 55.5q0 72 53 116.5t146 44.5q116 0 180 -62q24 -24 24 -54q0 -21 -12.5 -38t-25 -23t-15.5 -3 q-23 35 -63 58t-83 23q-34 0 -53.5 -14t-19.5 -35q0 -19 19 -31t69 -31q34 -13 55 -23t44.5 -27t35 -40t11.5 -53q0 -72 -54 -114.5t-151 -42.5q-134 0 -197 63z" />
<glyph unicode="t" horiz-adv-x="396" d="M76 172l43 206h-24q-51 0 -51 37q0 38 20 59q16 14 49 14h29l6 28q11 54 35 78t70 24q14 0 26.5 -3t19.5 -7t6 -7l-24 -113h82q25 0 39 -10t14 -27q0 -39 -21 -59q-14 -14 -48 -14h-91l-43 -200q-19 -84 46 -84q33 0 60 25q2 2 7.5 -2.5t10.5 -15.5t5 -25q0 -31 -24 -55 q-34 -34 -104 -34q-84 0 -120.5 48.5t-17.5 136.5z" />
<glyph unicode="u" horiz-adv-x="584" d="M188 -12q-90 0 -129 60.5t-18 162.5l45 213q8 38 23 53t42 15h16q32 0 50 -16t10 -52l-44 -203q-11 -52 8.5 -84t65.5 -32q65 0 112 72l52 247q8 38 22.5 53t42.5 15h15q75 0 61 -68l-64 -303q-9 -41 -1.5 -58.5t36.5 -22.5q3 -1 -2 -13t-24.5 -24.5t-51.5 -12.5 q-97 0 -99 92q-64 -94 -168 -94z" />
<glyph unicode="v" horiz-adv-x="526" d="M136 47l-40 278q-7 54 -24 75.5t-49 23.5q-3 0 2.5 18t27.5 36t58 18q43 0 70 -27q36 -36 44 -138l18 -182l198 346q1 3 25 -1t48 -21.5t24 -46.5q0 -27 -30 -72l-206 -317q-27 -42 -88 -42q-71 0 -78 52z" />
<glyph unicode="w" horiz-adv-x="761" d="M110 51l-9 279q-2 51 -18.5 72t-49.5 24q-4 0 1 17.5t26.5 35t57.5 17.5q44 0 69 -25q36 -36 36 -138l-3 -184l119 244q14 27 31 37t46 10q75 0 78 -62l16 -231l160 347q1 3 24 0.5t46.5 -19t24.5 -45.5q2 -22 -22 -66l-174 -327q-22 -42 -80 -42q-36 0 -52 13t-20 43 l-26 226l-130 -240q-23 -42 -76 -42q-39 0 -56.5 13.5t-18.5 42.5z" />
<glyph unicode="x" horiz-adv-x="519" d="M-8 61q0 37 45 74l133 119l-99 199q-1 3 8.5 12.5t30.5 19t45 9.5q29 0 45 -16t29 -53l48 -111l178 184q2 2 20 -4.5t37 -24.5t19 -42q0 -35 -45 -74l-143 -129l30 -61q22 -49 44 -64t57 -17q5 0 0 -22t-28 -44t-62 -22q-41 0 -70.5 25t-54.5 94l-20 52l-171 -175 q-2 -2 -20 4.5t-37 24.5t-19 42z" />
<glyph unicode="y" horiz-adv-x="529" d="M-31 -70q21 -21 55 -21q29 0 57 22.5t70 83.5l12 18q-33 8 -39 52l-29 240q-6 51 -23.5 74t-48.5 25q-4 0 1.5 18t28 36t60.5 18q98 0 111 -165l15 -182l205 346q1 3 24.5 -1t47 -21.5t23.5 -46.5q0 -26 -29 -72l-243 -386q-58 -92 -104.5 -131t-107.5 -39q-66 0 -95 29 q-19 19 -19 50q0 13 6.5 26.5t13 20.5t8.5 6z" />
<glyph unicode="z" horiz-adv-x="499" d="M34 0q-3 0 -11 9.5t-15.5 25.5t-7.5 31q0 23 10 39t37 37l270 237h-174q-69 0 -69 46q0 13 5.5 28.5t11 25t7.5 9.5h360q4 0 12 -9.5t15 -25.5t7 -31q0 -23 -10 -39t-37 -37l-270 -237h189q68 0 68 -46q0 -13 -5.5 -28.5t-11 -25t-7.5 -9.5h-374z" />
<glyph unicode="{" horiz-adv-x="462" d="M71 130q-32 22 -42.5 37.5t-10.5 40.5q0 65 31 96q15 16 65 32l47 18q36 143 116 225q76 76 172 76q72 0 72 -58q0 -13 -5.5 -25.5t-11 -19t-6.5 -6.5q-81 -2 -133 -55q-54 -53 -88 -203l-131 -58l103 -74q-24 -161 33 -216q23 -23 73 -35q1 0 3.5 -4t5 -12.5t2.5 -17.5 q0 -71 -76 -71q-64 0 -111 47q-83 85 -71 256z" />
<glyph unicode="|" horiz-adv-x="271" d="M-14 -128l152 716q15 68 71 68h20q29 0 43 -18.5t7 -51.5l-152 -716q-14 -68 -72 -68h-19q-29 0 -43 18t-7 52z" />
<glyph unicode="}" horiz-adv-x="462" d="M90 -36q52 53 88 203l130 58l-103 74q24 160 -32 216q-23 23 -73 35q-11 6 -11 35q0 70 76 70q63 0 110 -47q83 -83 71 -255l37 -27q33 -23 43.5 -38t10.5 -40q0 -65 -31 -96q-15 -15 -66 -33l-46 -17q-38 -148 -117 -225q-77 -77 -172 -77q-72 0 -72 58q0 13 5.5 25.5 t11 19t7.5 6.5q79 3 133 55z" />
<glyph unicode="~" d="M121 160q-20 0 -31.5 12t-11.5 29q0 44 31.5 77t69.5 33q67 0 132 -69q29 65 64 65q20 0 31.5 -11.5t11.5 -28.5q0 -45 -31.5 -78t-69.5 -33q-66 0 -132 69q-29 -65 -64 -65z" />
<glyph unicode="&#xa1;" horiz-adv-x="277" d="M200 508q-68 0 -68 51t26 77q19 19 63 19q68 0 68 -52t-25 -77q-18 -18 -64 -18zM23 90l108 318q10 22 19.5 31t24.5 9q24 0 32.5 -10.5t9.5 -40.5l-29 -324q1 -42 -21 -62t-63 -20q-58 0 -80 26t-1 73z" />
<glyph unicode="&#xa2;" horiz-adv-x="540" d="M176 -33l15 70q-71 22 -111 76.5t-40 135.5q0 112 73.5 195.5t183.5 94.5l12 56q8 37 22 52.5t38 15.5h5q28 0 45.5 -16t9.5 -52l-13 -64q57 -14 94 -51q36 -36 36 -81q0 -21 -10 -35.5t-24 -21t-28.5 -9t-24.5 -1.5t-10 4q-9 43 -39.5 67.5t-81.5 24.5q-67 0 -111 -50.5 t-44 -117.5q0 -55 33.5 -89.5t91.5 -34.5q79 0 120 67q2 3 20 -1.5t36 -20.5t18 -40q0 -30 -31 -61q-54 -54 -154 -58l-11 -55q-8 -37 -22.5 -52.5t-37.5 -15.5h-5q-28 0 -45.5 16t-9.5 52z" />
<glyph unicode="&#xa3;" horiz-adv-x="583" d="M-10 39q0 40 22 61q14 15 47 15h31l39 134h-45q-19 0 -30.5 9t-11.5 24q0 38 20 58q14 14 46 14h53l27 91q34 114 90 164.5t148 50.5q91 0 141 -48q32 -34 32 -72q0 -30 -19 -48.5t-38 -23t-20 -0.5q-21 78 -98 78q-42 0 -69.5 -28.5t-48.5 -100.5l-18 -63h127 q51 0 51 -36q0 -34 -20 -56q-14 -13 -47 -13h-144l-39 -134h257q72 0 72 -48q0 -14 -5.5 -30.5t-11.5 -26.5t-8 -10h-474q-56 0 -56 39z" />
<glyph unicode="&#xa4;" horiz-adv-x="631" d="M68 205q-46 0 -46 31q0 28 15 43q14 14 41 14h40q5 30 12 62h-30q-46 0 -46 32q0 28 15 43q14 14 41 14h48q31 82 76 127q90 89 220 89q109 0 170 -59q33 -33 33 -78q0 -31 -19.5 -50.5t-38 -24.5t-20.5 -2q-35 94 -138 94q-65 0 -107 -44q-20 -20 -35 -52h156 q46 0 46 -31q0 -29 -16 -44q-14 -14 -40 -14h-177q-7 -32 -12 -62h166q47 0 47 -31q0 -28 -16 -44q-13 -13 -40 -13h-159q9 -45 41.5 -71t83.5 -26q44 0 76.5 17t65.5 57q3 3 17 -4t27.5 -23.5t13.5 -35.5q0 -35 -32 -67q-28 -28 -76.5 -46t-102.5 -18q-112 0 -179 58 t-75 159h-46z" />
<glyph unicode="&#xa5;" horiz-adv-x="612" d="M77 107q-49 0 -49 34q0 30 16 46q15 15 44 15h145l11 53h-135q-50 0 -50 34q0 29 17 46q14 15 44 15h99l-115 250q-2 5 7 16.5t30.5 23t46.5 11.5q48 0 69 -59l74 -202l209 267q1 2 22.5 -2t43.5 -21.5t22 -46.5q0 -37 -43 -81l-143 -156h88q50 0 50 -34q0 -29 -17 -46 q-13 -15 -44 -15h-144l-11 -53h134q50 0 50 -34q0 -30 -16 -46q-15 -15 -44 -15h-145l-9 -44q-13 -67 -74 -67h-4q-31 0 -45 17t-7 51l9 43h-136z" />
<glyph unicode="&#xa7;" horiz-adv-x="569" d="M31 15q25 -38 78 -64t116 -26q48 0 72.5 17t24.5 47q0 34 -101 70q-7 3 -11 4q-35 12 -51.5 18t-47 21t-45 29t-27 37t-12.5 50q0 58 37 99t97 58q-54 39 -54 99q0 83 65.5 134t172.5 51q137 0 195 -60q27 -25 27 -55q0 -23 -14.5 -41t-29 -25.5t-17.5 -4.5 q-57 84 -162 84q-45 0 -70 -16.5t-25 -43.5q0 -14 7.5 -25t27 -21t33 -15.5t46.5 -17.5q34 -12 50.5 -18t45.5 -21t43 -28.5t26 -35.5t12 -48q0 -59 -35 -99t-96 -59q55 -36 55 -98q0 -83 -65 -136t-185 -53q-150 0 -212 62q-28 28 -28 61q0 21 14 39t27.5 26t15.5 5z M155 253q0 -34 30 -52t95 -39l39 -12q40 5 67 27t27 55q0 23 -19 40t-38.5 25t-63.5 22q-20 5 -46 14q-41 -7 -66 -28t-25 -52z" />
<glyph unicode="&#xa8;" d="M427 544q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5zM224 544q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="751" d="M59 323q0 146 94 241.5t240 95.5q147 0 239.5 -94t92.5 -241t-93.5 -242.5t-239.5 -95.5q-147 0 -240 94.5t-93 241.5zM122 324q0 -120 76.5 -199t194.5 -79q120 0 194.5 77.5t74.5 199.5q0 121 -76.5 199.5t-194.5 78.5q-120 0 -194.5 -77.5t-74.5 -199.5zM218 322 q0 79 52 130.5t131 51.5q75 0 116 -41q19 -19 18 -41q0 -15 -11.5 -27.5t-23 -17t-13.5 -2.5q-30 43 -84 43q-37 0 -63 -27t-26 -68q0 -42 26.5 -69t65.5 -27q55 0 83 46q2 3 14 -2t23.5 -17.5t11.5 -28.5q0 -23 -21 -44q-41 -41 -116 -41q-80 0 -131.5 51t-51.5 131z" />
<glyph unicode="&#xaa;" horiz-adv-x="519" d="M204 250q-64 0 -105.5 43t-41.5 117q0 98 61.5 174.5t152.5 76.5q89 0 125 -71q22 64 85 64q11 0 21 -2.5t15.5 -6t4.5 -5.5l-59 -282q-7 -33 -1.5 -47t28.5 -19q2 -1 -2 -10.5t-20 -19.5t-43 -10q-82 0 -80 80q-22 -38 -59.5 -60t-81.5 -22zM248 344q41 0 73.5 29.5 t42.5 74.5l11 53q-22 68 -87 68q-52 0 -84.5 -43t-32.5 -99q0 -37 22 -60t55 -23zM131 39q0 45 24 69q17 17 57 17q61 0 61 -47q0 -46 -24 -69q-19 -17 -56 -17q-62 0 -62 47z" />
<glyph unicode="&#xab;" horiz-adv-x="535" d="M312 145q-21 31 -36.5 63t-15.5 40q0 6 30 38.5t66 63.5q83 71 126 71q24 0 36.5 -12t12.5 -33q0 -48 -94 -101q-39 -24 -70 -39q27 -17 52 -40q49 -46 49 -75q0 -21 -16 -35.5t-42 -14.5q-47 0 -98 74zM97 148q-24 32 -42.5 66t-18.5 42q0 7 26.5 37t62.5 61 q78 67 120 67q22 0 34.5 -12t12.5 -32q0 -45 -83 -94q-46 -27 -68 -39q27 -18 58 -45q55 -50 55 -79q1 -20 -14 -34t-39 -14q-47 0 -104 76z" />
<glyph unicode="&#xad;" horiz-adv-x="428" d="M91 180q-58 0 -58 45q0 14 6.5 35t17.5 32q16 17 53 17h227q58 0 58 -44q0 -14 -6.5 -35t-17.5 -33q-17 -17 -53 -17h-227z" />
<glyph unicode="&#xae;" horiz-adv-x="751" d="M59 323q0 146 94 241.5t240 95.5q147 0 239.5 -94t92.5 -241t-93.5 -242.5t-239.5 -95.5q-147 0 -240 94.5t-93 241.5zM122 324q0 -120 76.5 -199t194.5 -79q120 0 194.5 77.5t74.5 199.5q0 121 -76.5 199.5t-194.5 78.5q-120 0 -194.5 -77.5t-74.5 -199.5zM250 200v248 q0 52 49 52h106q147 0 147 -113q0 -82 -81 -103l79 -105q3 -3 -4 -11t-21.5 -15.5t-28.5 -7.5q-34 0 -56 35l-63 87h-33v-77q0 -43 -41 -43h-13q-40 0 -40 53zM342 336h59q57 0 57 45q0 46 -60 46h-56v-91z" />
<glyph unicode="&#xaf;" d="M205 555q-52 0 -52 36q0 37 20 57q13 14 48 14h229q53 0 53 -36q0 -35 -20 -57q-14 -14 -49 -14h-229z" />
<glyph unicode="&#xb0;" horiz-adv-x="385" d="M220 341q-66 0 -107.5 37t-41.5 98q0 75 49 129.5t132 54.5q65 0 107 -37t42 -98q0 -74 -49 -129t-132 -55zM228 416q36 0 60 32t24 71q0 29 -19.5 47.5t-48.5 18.5q-36 0 -60 -32.5t-24 -71.5q0 -29 19 -47t49 -18z" />
<glyph unicode="&#xb1;" horiz-adv-x="600" d="M295 221q-25 0 -37.5 15.5t-5.5 44.5l20 95h-111q-53 0 -53 38q0 34 19 53q15 15 47 15h120l24 111q11 56 55 56h12q26 0 38 -15.5t6 -44.5l-23 -107h112q53 0 53 -38q0 -35 -19 -53q-14 -15 -47 -15h-121l-21 -99q-13 -56 -55 -56h-13zM37 103q0 36 19 55q14 15 47 15 h353q53 0 53 -38q0 -36 -19 -54q-14 -16 -47 -16h-353q-53 0 -53 38z" />
<glyph unicode="&#xb2;" horiz-adv-x="336" d="M48 285q-46 0 -36 56q7 56 39 89q28 30 98 53q64 21 77 33q10 10 10 25q0 16 -13.5 26t-37.5 10q-52 0 -75 -56q0 -2 -14.5 -0.5t-29 12.5t-14.5 32q0 28 26 52q38 38 120 38q62 0 99 -28.5t37 -77.5q0 -54 -32 -79.5t-93 -42.5q-78 -22 -98 -42q-11 -12 -13 -22h153 q51 0 51 -33q0 -9 -4.5 -20.5t-9 -18t-5.5 -6.5h-235z" />
<glyph unicode="&#xb3;" horiz-adv-x="336" d="M147 278q-83 0 -121 36q-20 20 -20 46q0 15 6 27t15 17t18 8t15 2t7 -1q10 -59 79 -59q31 0 48 13.5t17 35.5q0 33 -44 33h-14q-36 0 -36 28q0 21 12 33q11 11 44 11h19q46 0 48 35q0 16 -15.5 26t-40.5 10q-57 0 -77 -47q-3 0 -15 -1t-26 10t-14 31q0 24 19 43 q40 40 120 40q64 0 103.5 -26.5t39.5 -68.5q0 -34 -21 -58.5t-62 -32.5q55 -22 55 -74q0 -50 -38 -83.5t-121 -33.5z" />
<glyph unicode="&#xb4;" d="M239 560q-3 7 14 34.5t49 61.5t63 51q25 15 54 15q26 0 43 -17.5t17 -42.5q0 -41 -52 -70q-38 -21 -82.5 -31t-73 -9t-32.5 8z" />
<glyph unicode="&#xb6;" horiz-adv-x="606" d="M419 42l120 565q9 42 45 42h14q20 0 28.5 -12t3.5 -34l-120 -565q-8 -42 -46 -42h-12q-43 0 -33 46zM272 42l50 233h-46q-107 0 -163 39.5t-56 117.5q0 97 63.5 156t191.5 59h124q26 0 38 -14.5t7 -40.5l-117 -554q-5 -23 -16 -32.5t-31 -9.5h-12q-20 0 -28.5 12t-4.5 34 z" />
<glyph unicode="&#xb7;" horiz-adv-x="245" d="M113 172q-68 0 -68 52q0 51 26 77q19 19 63 19q68 0 68 -52q0 -51 -26 -77q-19 -19 -63 -19z" />
<glyph unicode="&#xb8;" d="M158 -42q2 3 26 0.5t48.5 -15.5t24.5 -36q0 -32 -46 -66l-95 -75q-14 -10 -39.5 5t-14.5 32z" />
<glyph unicode="&#xb9;" horiz-adv-x="336" d="M58 285q-19 0 -30 9t-10 23q0 24 13 37q11 15 41 15h49l40 170q-22 -17 -47 -17q-24 0 -37 12.5t-13 34.5q0 18 8 32.5t16 20t9 4.5q18 -18 45 -18q39 0 57 44h37q43 0 33 -48l-55 -235h44q19 0 29.5 -8.5t9.5 -23.5q0 -24 -13 -37q-10 -15 -40 -15h-186z" />
<glyph unicode="&#xba;" horiz-adv-x="452" d="M248 249q-87 0 -137 50.5t-50 130.5q0 94 60.5 162t160.5 68q87 0 137.5 -50t50.5 -129q0 -95 -61 -163.5t-161 -68.5zM252 339q50 0 83 41t33 96q0 40 -25.5 67t-63.5 27q-50 0 -82.5 -40.5t-32.5 -96.5q0 -40 25 -67t63 -27zM110 39q0 46 23 69q17 17 57 17 q62 0 62 -47q0 -45 -24 -69q-19 -17 -57 -17q-61 0 -61 47z" />
<glyph unicode="&#xbb;" horiz-adv-x="535" d="M53 70q-49 0 -49 44q0 48 94 101q39 24 70 39q-26 18 -52 40q-48 45 -48 75q0 21 15.5 35.5t41.5 14.5q47 0 98 -74q21 -31 36.5 -63t15.5 -40q0 -6 -30 -38.5t-66 -63.5q-81 -70 -126 -70zM243 113q0 45 83 94q46 27 68 39q-27 18 -58 45q-55 50 -55 79q-1 20 14 34 t39 14q47 0 104 -76q24 -32 42.5 -66t18.5 -42q0 -7 -26.5 -37t-61.5 -61q-80 -67 -121 -67q-22 0 -34.5 12t-12.5 32z" />
<glyph unicode="&#xbc;" horiz-adv-x="792" d="M58 285q-19 0 -30 9t-10 23q0 24 13 37q11 15 41 15h49l40 170q-22 -17 -47 -17q-24 0 -37 12.5t-13 34.5q0 18 8 32.5t16 20t9 4.5q18 -18 45 -18q39 0 57 44h37q43 0 33 -48l-55 -235h44q19 0 29.5 -8.5t9.5 -23.5q0 -24 -13 -37q-10 -15 -40 -15h-186zM82 15 q5 22 35 51q6 6 46 44.5t103.5 99.5t105.5 102l336 324q3 2 13.5 -2t18.5 -16t4 -28q-5 -22 -34 -51q-105 -98 -256 -247l-334 -323q-4 -3 -14.5 1.5t-19 17t-4.5 27.5zM471 137q0 21 35 67l111 128q17 21 32 28t49 7q41 0 56.5 -15t7.5 -52l-30 -141h19q36 0 36 -26 q0 -25 -14 -39q-10 -10 -33 -10h-25l-8 -41q-5 -26 -15 -36.5t-27 -10.5h-12q-46 0 -36 47l8 41h-125q-3 0 -9.5 6t-13 19t-6.5 28zM551 157h96l30 144z" />
<glyph unicode="&#xbd;" horiz-adv-x="792" d="M58 285q-19 0 -30 9t-10 23q0 24 13 37q11 15 41 15h49l40 170q-22 -17 -47 -17q-24 0 -37 12.5t-13 34.5q0 18 8 32.5t16 20t9 4.5q18 -18 45 -18q39 0 57 44h37q43 0 33 -48l-55 -235h44q19 0 29.5 -8.5t9.5 -23.5q0 -24 -13 -37q-10 -15 -40 -15h-186zM57 15 q5 22 35 51q6 6 46 44.5t103.5 99.5t105.5 102l336 324q3 2 13.5 -2t18.5 -16t4 -28q-5 -22 -34 -51q-105 -98 -256 -247l-334 -323q-4 -3 -14.5 1.5t-19 17t-4.5 27.5zM468 56q7 56 39 89q28 30 98 53q64 21 77 33q10 10 10 25q0 16 -13.5 26t-37.5 10q-52 0 -75 -56 q0 -2 -14.5 -0.5t-29 12.5t-14.5 32q0 28 26 52q38 38 120 38q62 0 99 -28.5t37 -77.5q0 -54 -32 -79.5t-93 -42.5q-78 -22 -98 -42q-11 -12 -13 -22h153q51 0 51 -33q0 -9 -4.5 -20.5t-9 -18t-5.5 -6.5h-235q-46 0 -36 56z" />
<glyph unicode="&#xbe;" horiz-adv-x="792" d="M147 278q-83 0 -121 36q-20 20 -20 46q0 15 6 27t15 17t18 8t15 2t7 -1q10 -59 79 -59q31 0 48 13.5t17 35.5q0 33 -44 33h-14q-36 0 -36 28q0 21 12 33q11 11 44 11h19q46 0 48 35q0 16 -15.5 26t-40.5 10q-57 0 -77 -47q-3 0 -15 -1t-26 10t-14 31q0 24 19 43 q40 40 120 40q64 0 103.5 -26.5t39.5 -68.5q0 -34 -21 -58.5t-62 -32.5q55 -22 55 -74q0 -50 -38 -83.5t-121 -33.5zM73 15q5 22 35 51q6 6 46 44.5t103.5 99.5t105.5 102l336 324q3 2 13.5 -2t18.5 -16t4 -28q-5 -22 -34 -51q-105 -98 -256 -247l-334 -323q-4 -3 -14.5 1.5 t-19 17t-4.5 27.5zM471 137q0 21 35 67l111 128q17 21 32 28t49 7q41 0 56.5 -15t7.5 -52l-30 -141h19q36 0 36 -26q0 -25 -14 -39q-10 -10 -33 -10h-25l-8 -41q-5 -26 -15 -36.5t-27 -10.5h-12q-46 0 -36 47l8 41h-125q-3 0 -9.5 6t-13 19t-6.5 28zM551 157h96l30 144z" />
<glyph unicode="&#xbf;" horiz-adv-x="499" d="M329 507q-68 0 -68 51t26 77q19 19 63 19q68 0 68 -52q0 -51 -26 -77q-18 -18 -63 -18zM5 166q0 43 14.5 74.5t43 51t50 29.5t55.5 20q46 14 65.5 26t19.5 29q0 22 -19 31q-2 2 2 7.5t17.5 11.5t32.5 6q40 0 65 -21.5t25 -56.5q0 -43 -28.5 -68.5t-92.5 -45.5 q-58 -19 -80.5 -37t-22.5 -51t25 -53.5t67 -20.5q48 0 83 26.5t52 70.5q1 3 19.5 -1t37.5 -19t19 -40q0 -45 -39 -84q-65 -65 -182 -65q-103 0 -166 50.5t-63 129.5z" />
<glyph unicode="&#xc0;" horiz-adv-x="659" d="M289 759q-45 29 -45 70q0 26 17.5 43.5t43.5 17.5q29 0 54 -19q29 -19 55.5 -53.5t39.5 -62t9 -34.5q-9 -5 -32 -6.5t-65.5 10.5t-76.5 34zM-8 59q0 30 32 81l283 471q14 23 34 32.5t61 9.5q47 0 66.5 -11.5t23.5 -34.5l109 -566q2 -11 -26 -29t-58 -18q-58 0 -68 69 l-19 102h-236l-97 -172q-1 -1 -11.5 -1.5t-26 2.5t-31 9.5t-26 21t-10.5 34.5zM245 275h177l-42 234z" />
<glyph unicode="&#xc1;" horiz-adv-x="659" d="M344 721q-3 7 14 34.5t49 61.5t63 51q26 15 54 15q26 0 43 -17.5t17 -42.5q0 -41 -52 -70q-38 -21 -82.5 -31t-73 -9t-32.5 8zM-8 59q0 30 32 81l283 471q14 23 34 32.5t61 9.5q47 0 66.5 -11.5t23.5 -34.5l109 -566q2 -11 -26 -29t-58 -18q-58 0 -68 69l-19 102h-236 l-97 -172q-1 -1 -11.5 -1.5t-26 2.5t-31 9.5t-26 21t-10.5 34.5zM245 275h177l-42 234z" />
<glyph unicode="&#xc2;" horiz-adv-x="659" d="M305 705q-19 0 -30.5 12t-11.5 29q0 42 66 92q67 52 116 63q32 -11 77 -62q44 -49 44 -82q0 -23 -13.5 -37.5t-35.5 -14.5q-38 0 -70 59q-3 7 -10 21t-9 19q-30 -35 -35 -40q-48 -59 -88 -59zM-8 59q0 30 32 81l283 471q14 23 34 32.5t61 9.5q47 0 66.5 -11.5t23.5 -34.5 l109 -566q2 -11 -26 -29t-58 -18q-58 0 -68 69l-19 102h-236l-97 -172q-1 -1 -11.5 -1.5t-26 2.5t-31 9.5t-26 21t-10.5 34.5zM245 275h177l-42 234z" />
<glyph unicode="&#xc3;" horiz-adv-x="659" d="M291 708q-20 0 -31.5 12t-11.5 29q0 44 31.5 77t69.5 33q67 0 132 -69q29 65 64 65q20 0 31.5 -11.5t11.5 -28.5q0 -45 -31.5 -78t-69.5 -33q-66 0 -132 69q-29 -65 -64 -65zM-8 59q0 30 32 81l283 471q14 23 34 32.5t61 9.5q47 0 66.5 -11.5t23.5 -34.5l109 -566 q2 -11 -26 -29t-58 -18q-58 0 -68 69l-19 102h-236l-97 -172q-1 -1 -11.5 -1.5t-26 2.5t-31 9.5t-26 21t-10.5 34.5zM245 275h177l-42 234z" />
<glyph unicode="&#xc4;" horiz-adv-x="659" d="M519 705q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5zM316 705q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5zM-8 59q0 30 32 81l283 471q14 23 34 32.5t61 9.5 q47 0 66.5 -11.5t23.5 -34.5l109 -566q2 -11 -26 -29t-58 -18q-58 0 -68 69l-19 102h-236l-97 -172q-1 -1 -11.5 -1.5t-26 2.5t-31 9.5t-26 21t-10.5 34.5zM245 275h177l-42 234z" />
<glyph unicode="&#xc5;" horiz-adv-x="659" d="M-8 59q0 30 32 81l274 455q14 23 38 33q-41 27 -41 80q0 56 38.5 96.5t97.5 40.5q54 0 86.5 -29t32.5 -75q0 -38 -20 -71.5t-54 -49.5q15 -10 20 -30l105 -549q2 -11 -26 -29t-58 -18q-58 0 -68 69l-19 102h-236l-97 -172q-1 -1 -11.5 -1.5t-26 2.5t-31 9.5t-26 21 t-10.5 34.5zM416 664q27 0 44.5 20.5t17.5 46.5q0 21 -13.5 35t-35.5 14q-27 0 -44.5 -20.5t-17.5 -46.5q0 -21 14 -35t35 -14zM245 275h177l-42 234z" />
<glyph unicode="&#xc6;" horiz-adv-x="876" d="M-8 59q0 28 32 81l275 456q15 27 37 39t64 12h446q62 0 62 -44q0 -15 -6.5 -36t-17.5 -32q-15 -16 -57 -16h-321l26 -129h191q59 0 59 -41t-23 -64q-16 -16 -53 -16h-151l27 -141h170q62 0 62 -43t-24 -69q-16 -16 -57 -16h-199q-42 0 -61 15t-26 54l-17 96h-236 l-97 -172q0 -1 -11 -1.5t-26.5 2.5t-31 9.5t-26 21t-10.5 34.5zM245 275h177l-42 234z" />
<glyph unicode="&#xc7;" horiz-adv-x="648" d="M48 273q0 158 105.5 272.5t261.5 114.5q135 0 206 -71q41 -41 41 -82q0 -35 -23.5 -55.5t-46.5 -25t-27 -0.5q-39 102 -149 102q-91 0 -153 -70t-62 -168q0 -76 45.5 -123t118.5 -47q105 0 161 83q2 3 19.5 -3t35.5 -24t18 -44q0 -33 -35 -68q-33 -33 -88 -54.5 t-120 -21.5q-141 0 -224.5 77t-83.5 208zM195 -197l96 155q2 3 26 0.5t48.5 -15.5t24.5 -36q0 -32 -46 -66l-95 -75q-14 -10 -39.5 5t-14.5 32z" />
<glyph unicode="&#xc8;" horiz-adv-x="600" d="M310 759q-45 29 -45 70q0 26 17.5 43.5t43.5 17.5q29 0 54 -19q29 -19 55.5 -53.5t39.5 -62t9 -34.5q-9 -5 -32 -6.5t-65.5 10.5t-76.5 34zM26 94l101 477q16 76 92 76h357q63 0 63 -44q0 -14 -7 -35.5t-18 -32.5q-15 -16 -57 -16h-293l-28 -129h219q58 0 58 -41t-22 -64 q-16 -16 -53 -16h-228l-30 -141h292q62 0 62 -43t-24 -69q-16 -16 -57 -16h-353q-95 0 -74 94z" />
<glyph unicode="&#xc9;" horiz-adv-x="600" d="M326 721q-3 7 14 34.5t49 61.5t63 51q26 15 54 15q26 0 43 -17.5t17 -42.5q0 -41 -52 -70q-38 -21 -82.5 -31t-73 -9t-32.5 8zM26 94l101 477q16 76 92 76h357q63 0 63 -44q0 -14 -7 -35.5t-18 -32.5q-15 -16 -57 -16h-293l-28 -129h219q58 0 58 -41t-22 -64 q-16 -16 -53 -16h-228l-30 -141h292q62 0 62 -43t-24 -69q-16 -16 -57 -16h-353q-95 0 -74 94z" />
<glyph unicode="&#xca;" horiz-adv-x="600" d="M300 705q-19 0 -30.5 12t-11.5 29q0 42 66 92q67 52 116 63q32 -11 77 -62q44 -49 44 -82q0 -23 -13.5 -37.5t-35.5 -14.5q-38 0 -70 59q-3 7 -10 21t-9 19q-30 -35 -35 -40q-48 -59 -88 -59zM26 94l101 477q16 76 92 76h357q63 0 63 -44q0 -14 -7 -35.5t-18 -32.5 q-15 -16 -57 -16h-293l-28 -129h219q58 0 58 -41t-22 -64q-16 -16 -53 -16h-228l-30 -141h292q62 0 62 -43t-24 -69q-16 -16 -57 -16h-353q-95 0 -74 94z" />
<glyph unicode="&#xcb;" horiz-adv-x="600" d="M507 705q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5zM304 705q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5zM26 94l101 477q16 76 92 76h357q63 0 63 -44 q0 -14 -7 -35.5t-18 -32.5q-15 -16 -57 -16h-293l-28 -129h219q58 0 58 -41t-22 -64q-16 -16 -53 -16h-228l-30 -141h292q62 0 62 -43t-24 -69q-16 -16 -57 -16h-353q-95 0 -74 94z" />
<glyph unicode="&#xcc;" horiz-adv-x="279" d="M120 759q-45 29 -45 70q0 26 17.5 43.5t43.5 17.5q29 0 54 -19q29 -19 55.5 -53.5t39.5 -62t9 -34.5q-9 -5 -32 -6.5t-65.5 10.5t-76.5 34zM28 67l110 517q8 35 25 51t48 16h22q31 0 45.5 -18.5t7.5 -52.5l-110 -517q-8 -35 -25 -51t-48 -16h-21q-31 0 -46 18.5t-8 52.5z " />
<glyph unicode="&#xcd;" horiz-adv-x="279" d="M174 721q-3 7 14 34.5t49 61.5t63 51q26 15 54 15q26 0 43 -17.5t17 -42.5q0 -41 -52 -70q-38 -21 -82.5 -31t-73 -9t-32.5 8zM28 67l110 517q8 35 25 51t48 16h22q31 0 45.5 -18.5t7.5 -52.5l-110 -517q-8 -35 -25 -51t-48 -16h-21q-31 0 -46 18.5t-8 52.5z" />
<glyph unicode="&#xce;" horiz-adv-x="279" d="M132 705q-19 0 -30.5 12t-11.5 29q0 42 66 92q67 52 116 63q32 -11 77 -62q44 -49 44 -82q0 -23 -13.5 -37.5t-35.5 -14.5q-38 0 -70 59q-3 7 -10 21t-9 19q-30 -35 -35 -40q-48 -59 -88 -59zM28 67l110 517q8 35 25 51t48 16h22q31 0 45.5 -18.5t7.5 -52.5l-110 -517 q-8 -35 -25 -51t-48 -16h-21q-31 0 -46 18.5t-8 52.5z" />
<glyph unicode="&#xcf;" horiz-adv-x="279" d="M345 705q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5zM142 705q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5zM28 67l110 517q8 35 25 51t48 16h22q31 0 45.5 -18.5 t7.5 -52.5l-110 -517q-8 -35 -25 -51t-48 -16h-21q-31 0 -46 18.5t-8 52.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="715" d="M65 94l38 179h-37q-19 0 -31 9t-12 24q0 38 20 57q13 13 43 13h39l41 195q16 76 92 76h128q147 0 230.5 -72.5t83.5 -196.5q0 -104 -43.5 -188t-136.5 -137t-224 -53h-157q-95 0 -74 94zM218 125h98q114 0 175 67t61 167q0 77 -47.5 120t-132.5 43h-70l-31 -146h117 q49 0 49 -35q0 -36 -19 -55q-13 -13 -46 -13h-123z" />
<glyph unicode="&#xd1;" horiz-adv-x="692" d="M324 708q-20 0 -31.5 12t-11.5 29q0 44 31.5 77t69.5 33q67 0 132 -69q29 65 64 65q20 0 31.5 -11.5t11.5 -28.5q0 -45 -31.5 -78t-69.5 -33q-66 0 -132 69q-29 -65 -64 -65zM20 67l108 509q16 75 81 75h18q25 0 40 -10.5t30 -36.5l194 -381l76 361q8 35 24 51t47 16h16 q31 0 44.5 -18.5t6.5 -52.5l-109 -512q-15 -72 -79 -72h-9q-25 0 -40 10.5t-30 36.5l-201 396l-79 -376q-8 -35 -24 -51t-47 -16h-17q-30 0 -43.5 18.5t-6.5 52.5z" />
<glyph unicode="&#xd2;" horiz-adv-x="729" d="M335 759q-45 29 -45 70q0 26 17.5 43.5t43.5 17.5q29 0 54 -19q29 -19 55.5 -53.5t39.5 -62t9 -34.5q-9 -5 -32 -6.5t-65.5 10.5t-76.5 34zM49 274q0 161 104 273.5t261 112.5q141 0 220.5 -81.5t79.5 -203.5q0 -161 -104 -274t-261 -113q-141 0 -220.5 81.5t-79.5 204.5 zM197 284q0 -72 43 -119.5t118 -47.5q83 0 145.5 71.5t62.5 176.5q0 72 -43 119t-118 47q-84 0 -146 -71.5t-62 -175.5z" />
<glyph unicode="&#xd3;" horiz-adv-x="729" d="M353 721q-3 7 14 34.5t49 61.5t63 51q25 15 54 15q26 0 43 -17.5t17 -42.5q0 -41 -52 -70q-38 -21 -82.5 -31t-73 -9t-32.5 8zM49 274q0 161 104 273.5t261 112.5q141 0 220.5 -81.5t79.5 -203.5q0 -161 -104 -274t-261 -113q-141 0 -220.5 81.5t-79.5 204.5zM197 284 q0 -72 43 -119.5t118 -47.5q83 0 145.5 71.5t62.5 176.5q0 72 -43 119t-118 47q-84 0 -146 -71.5t-62 -175.5z" />
<glyph unicode="&#xd4;" horiz-adv-x="729" d="M333 705q-19 0 -30.5 12t-11.5 29q0 42 66 92q67 52 116 63q32 -11 77 -62q44 -49 44 -82q0 -23 -13.5 -37.5t-35.5 -14.5q-38 0 -70 59q-3 7 -10 21t-9 19q-30 -35 -35 -40q-48 -59 -88 -59zM49 274q0 161 104 273.5t261 112.5q141 0 220.5 -81.5t79.5 -203.5 q0 -161 -104 -274t-261 -113q-141 0 -220.5 81.5t-79.5 204.5zM197 284q0 -72 43 -119.5t118 -47.5q83 0 145.5 71.5t62.5 176.5q0 72 -43 119t-118 47q-84 0 -146 -71.5t-62 -175.5z" />
<glyph unicode="&#xd5;" horiz-adv-x="729" d="M316 708q-20 0 -31.5 12t-11.5 29q0 44 31.5 77t69.5 33q68 0 132 -69q29 65 64 65q20 0 31.5 -11.5t11.5 -28.5q0 -45 -31.5 -78t-69.5 -33q-66 0 -132 69q-29 -65 -64 -65zM49 274q0 161 104 273.5t261 112.5q141 0 220.5 -81.5t79.5 -203.5q0 -161 -104 -274 t-261 -113q-141 0 -220.5 81.5t-79.5 204.5zM197 284q0 -72 43 -119.5t118 -47.5q83 0 145.5 71.5t62.5 176.5q0 72 -43 119t-118 47q-84 0 -146 -71.5t-62 -175.5z" />
<glyph unicode="&#xd6;" horiz-adv-x="729" d="M551 705q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5zM348 705q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5zM49 274q0 161 104 273.5t261 112.5q141 0 220.5 -81.5 t79.5 -203.5q0 -161 -104 -274t-261 -113q-141 0 -220.5 81.5t-79.5 204.5zM197 284q0 -72 43 -119.5t118 -47.5q83 0 145.5 71.5t62.5 176.5q0 72 -43 119t-118 47q-84 0 -146 -71.5t-62 -175.5z" />
<glyph unicode="&#xd7;" horiz-adv-x="554" d="M93 47q-16 0 -33.5 19.5t-17.5 34.5q0 30 25 50l138 115l-86 110q-16 20 -16 39q0 17 20 36t39 19q23 0 44 -25l82 -110l135 113q20 19 44 19q16 0 34 -19.5t18 -33.5q0 -30 -26 -51l-137 -114l86 -111q17 -20 17 -39q0 -17 -20 -36t-39 -19q-25 0 -44 26l-84 110 l-134 -114q-22 -19 -45 -19z" />
<glyph unicode="&#xd8;" horiz-adv-x="729" d="M-9 13q0 25 28 50l69 62q-39 66 -39 149q0 161 104 273.5t261 112.5q120 0 202 -67l96 86q3 3 16 -2.5t25 -19t12 -28.5q0 -25 -28 -50l-62 -56q39 -69 39 -148q0 -161 -104 -274t-261 -113q-124 0 -202 67l-103 -92q-3 -3 -16 2.5t-25 19t-12 28.5zM200 222l311 279 q-42 33 -106 33q-86 0 -149.5 -72.5t-63.5 -178.5q0 -31 8 -61zM250 147q42 -33 107 -33q87 0 150.5 72.5t63.5 178.5q0 33 -9 61z" />
<glyph unicode="&#xd9;" horiz-adv-x="658" d="M317 753q-45 29 -45 70q0 26 17.5 43.5t43.5 17.5q29 0 54 -19q29 -19 55.5 -53.5t39.5 -62t9 -34.5q-9 -5 -32 -6.5t-65.5 10.5t-76.5 34zM62 298l61 286q8 35 25 51t48 16h21q31 0 45.5 -18t7.5 -52l-62 -294q-35 -170 116 -170q111 0 142 146l68 321q15 67 73 67h19 q31 0 46 -18.5t8 -51.5l-71 -330q-55 -263 -300 -263q-151 0 -215.5 79t-31.5 231z" />
<glyph unicode="&#xda;" horiz-adv-x="658" d="M337 715q-3 7 14 34.5t49 61.5t63 51q25 15 54 15q26 0 43 -17.5t17 -42.5q0 -41 -52 -70q-38 -21 -82.5 -31t-73 -9t-32.5 8zM62 298l61 286q8 35 25 51t48 16h21q31 0 45.5 -18t7.5 -52l-62 -294q-35 -170 116 -170q111 0 142 146l68 321q15 67 73 67h19q31 0 46 -18.5 t8 -51.5l-71 -330q-55 -263 -300 -263q-151 0 -215.5 79t-31.5 231z" />
<glyph unicode="&#xdb;" horiz-adv-x="658" d="M316 699q-19 0 -30.5 12t-11.5 29q0 42 66 92q67 52 116 63q32 -11 77 -62q44 -49 44 -82q0 -23 -13.5 -37.5t-35.5 -14.5q-38 0 -70 59q-3 7 -10 21t-9 19q-30 -35 -35 -40q-48 -59 -88 -59zM62 298l61 286q8 35 25 51t48 16h21q31 0 45.5 -18t7.5 -52l-62 -294 q-35 -170 116 -170q111 0 142 146l68 321q15 67 73 67h19q31 0 46 -18.5t8 -51.5l-71 -330q-55 -263 -300 -263q-151 0 -215.5 79t-31.5 231z" />
<glyph unicode="&#xdc;" horiz-adv-x="658" d="M529 699q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5zM326 699q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5zM62 298l61 286q8 35 25 51t48 16h21q31 0 45.5 -18 t7.5 -52l-62 -294q-35 -170 116 -170q111 0 142 146l68 321q15 67 73 67h19q31 0 46 -18.5t8 -51.5l-71 -330q-55 -263 -300 -263q-151 0 -215.5 79t-31.5 231z" />
<glyph unicode="&#xdd;" horiz-adv-x="599" d="M313 715q-3 7 14 34.5t49 61.5t63 51q25 15 54 15q26 0 43 -17.5t17 -42.5q0 -41 -52 -70q-38 -21 -82.5 -31t-73 -9t-32.5 8zM190 64l42 198l-152 336q-2 5 8 17t32.5 24t48.5 12q44 0 65 -58l89 -217l233 281q2 2 23.5 -2t44 -22t22.5 -47q0 -33 -43 -80l-224 -248 l-42 -195q-14 -67 -74 -67h-21q-31 0 -45 17t-7 51z" />
<glyph unicode="&#xde;" horiz-adv-x="597" d="M20 67l110 517q8 35 24.5 51t48.5 16h25q29 0 43.5 -16t7.5 -43l-11 -52h75q119 0 181.5 -46.5t62.5 -135.5q0 -117 -72 -181.5t-212 -64.5h-125l-12 -58q-12 -58 -70 -58h-23q-31 0 -45.5 18.5t-7.5 52.5zM202 224h112q64 0 97 31t33 86q0 84 -110 84h-90z" />
<glyph unicode="&#xdf;" horiz-adv-x="585" d="M14 63l87 407q25 121 87.5 175t172.5 54q108 0 167 -45t59 -116q0 -68 -42.5 -109.5t-124.5 -49.5q62 -23 98 -66.5t36 -101.5q0 -96 -60.5 -159.5t-150.5 -63.5q-86 0 -125 39q-21 22 -21 50q0 21 11.5 37.5t23 23t12.5 4.5q30 -42 82 -42q40 0 67 29t27 73q0 63 -83 92 q-27 12 -39.5 26t-12.5 39q0 23 11 44t22 29q69 0 100 20.5t31 67.5q0 32 -25.5 52.5t-66.5 20.5q-89 0 -116 -126l-85 -404q-8 -35 -23.5 -51t-42.5 -16h-15q-31 0 -49.5 17t-11.5 50z" />
<glyph unicode="&#xe0;" horiz-adv-x="612" d="M255 598q-45 29 -45 70q0 26 17.5 43.5t43.5 17.5q29 0 54 -19q29 -19 55.5 -53.5t39.5 -62t9 -34.5q-9 -5 -32 -6.5t-65.5 10.5t-76.5 34zM25 188q0 122 77 217t191 95q52 0 93.5 -23t63.5 -65q14 42 39 61t64 19q13 0 25 -3.5t18.5 -7t6.5 -6.5l-76 -354 q-9 -41 -1 -58.5t37 -22.5q2 -2 -2.5 -13.5t-24 -24t-51.5 -12.5q-102 0 -99 100q-63 -102 -176 -102q-81 0 -133 54t-52 146zM162 208q0 -49 28 -78.5t71 -29.5q54 0 96 38.5t54 96.5l14 68q-28 87 -113 87q-66 0 -108 -55.5t-42 -126.5z" />
<glyph unicode="&#xe1;" horiz-adv-x="612" d="M285 560q-3 7 14 34.5t49 61.5t63 51q25 15 54 15q26 0 43 -17.5t17 -42.5q0 -41 -52 -70q-38 -21 -82.5 -31t-73 -9t-32.5 8zM25 188q0 122 77 217t191 95q52 0 93.5 -23t63.5 -65q14 42 39 61t64 19q13 0 25 -3.5t18.5 -7t6.5 -6.5l-76 -354q-9 -41 -1 -58.5t37 -22.5 q2 -2 -2.5 -13.5t-24 -24t-51.5 -12.5q-102 0 -99 100q-63 -102 -176 -102q-81 0 -133 54t-52 146zM162 208q0 -49 28 -78.5t71 -29.5q54 0 96 38.5t54 96.5l14 68q-28 87 -113 87q-66 0 -108 -55.5t-42 -126.5z" />
<glyph unicode="&#xe2;" horiz-adv-x="612" d="M264 544q-19 0 -30.5 12t-11.5 29q0 42 66 92q67 52 116 63q32 -11 77 -62q44 -49 44 -82q0 -23 -13.5 -37.5t-35.5 -14.5q-38 0 -70 59q-3 7 -10 21t-9 19q-30 -35 -35 -40q-48 -59 -88 -59zM25 188q0 122 77 217t191 95q52 0 93.5 -23t63.5 -65q14 42 39 61t64 19 q13 0 25 -3.5t18.5 -7t6.5 -6.5l-76 -354q-9 -41 -1 -58.5t37 -22.5q2 -2 -2.5 -13.5t-24 -24t-51.5 -12.5q-102 0 -99 100q-63 -102 -176 -102q-81 0 -133 54t-52 146zM162 208q0 -49 28 -78.5t71 -29.5q54 0 96 38.5t54 96.5l14 68q-28 87 -113 87q-66 0 -108 -55.5 t-42 -126.5z" />
<glyph unicode="&#xe3;" horiz-adv-x="612" d="M248 547q-20 0 -31.5 12t-11.5 29q0 44 31.5 77t69.5 33q67 0 132 -69q29 65 64 65q20 0 31.5 -11.5t11.5 -28.5q0 -45 -31.5 -78t-69.5 -33q-66 0 -132 69q-29 -65 -64 -65zM25 188q0 122 77 217t191 95q52 0 93.5 -23t63.5 -65q14 42 39 61t64 19q13 0 25 -3.5t18.5 -7 t6.5 -6.5l-76 -354q-9 -41 -1 -58.5t37 -22.5q2 -2 -2.5 -13.5t-24 -24t-51.5 -12.5q-102 0 -99 100q-63 -102 -176 -102q-81 0 -133 54t-52 146zM162 208q0 -49 28 -78.5t71 -29.5q54 0 96 38.5t54 96.5l14 68q-28 87 -113 87q-66 0 -108 -55.5t-42 -126.5z" />
<glyph unicode="&#xe4;" horiz-adv-x="612" d="M477 544q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5zM274 544q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5zM25 188q0 122 77 217t191 95q52 0 93.5 -23t63.5 -65 q14 42 39 61t64 19q13 0 25 -3.5t18.5 -7t6.5 -6.5l-76 -354q-9 -41 -1 -58.5t37 -22.5q2 -2 -2.5 -13.5t-24 -24t-51.5 -12.5q-102 0 -99 100q-63 -102 -176 -102q-81 0 -133 54t-52 146zM162 208q0 -49 28 -78.5t71 -29.5q54 0 96 38.5t54 96.5l14 68q-28 87 -113 87 q-66 0 -108 -55.5t-42 -126.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="612" d="M376 545q-54 0 -86.5 29.5t-32.5 76.5q0 56 39 97.5t98 41.5q54 0 86.5 -29.5t32.5 -76.5q0 -57 -39 -98t-98 -41zM378 609q27 0 45 20.5t18 46.5q0 21 -14 35.5t-36 14.5q-27 0 -44.5 -20.5t-17.5 -46.5q0 -21 14 -35.5t35 -14.5zM25 188q0 122 77 217t191 95 q52 0 93.5 -23t63.5 -65q14 42 39 61t64 19q13 0 25 -3.5t18.5 -7t6.5 -6.5l-76 -354q-9 -41 -1 -58.5t37 -22.5q2 -2 -2.5 -13.5t-24 -24t-51.5 -12.5q-102 0 -99 100q-63 -102 -176 -102q-81 0 -133 54t-52 146zM162 208q0 -49 28 -78.5t71 -29.5q54 0 96 38.5t54 96.5 l14 68q-28 87 -113 87q-66 0 -108 -55.5t-42 -126.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="856" d="M4 115q0 93 83 135t266 44h16v3q9 45 -15.5 71t-82.5 26q-50 0 -79 -16t-51 -54q-2 -2 -19.5 0t-36 16t-18.5 40q0 45 54.5 82t155.5 37q137 0 175 -90q77 91 204 91q92 0 142.5 -39t50.5 -104q0 -80 -63 -120t-218 -40q-25 0 -99 4v-3q0 -52 30.5 -81t88.5 -29 q88 0 144 71q1 2 15.5 -1.5t29 -17t14.5 -34.5q0 -32 -27 -60q-58 -58 -177 -58q-149 0 -197 100q-62 -100 -207 -100q-86 0 -132.5 33t-46.5 94zM487 287q26 -3 69 -3q174 0 174 69q0 21 -21 36.5t-59 15.5q-54 0 -97 -33t-66 -85zM140 132q0 -23 20.5 -37.5t55.5 -14.5 q47 0 83 29t47 74l8 38l-27 -1q-105 -3 -146 -24t-41 -64z" />
<glyph unicode="&#xe7;" horiz-adv-x="514" d="M23 212q0 116 81.5 202t197.5 86q115 0 174 -61q37 -37 37 -79q0 -20 -9 -35.5t-22 -22t-26.5 -10t-22.5 -3t-10 3.5q-8 42 -38 68t-80 26q-66 0 -109 -49.5t-43 -114.5q0 -54 33 -88t90 -34q76 0 119 69q2 3 18 -1t32 -19t16 -37q0 -33 -33 -65q-60 -60 -164 -60 q-108 0 -174.5 59.5t-66.5 164.5zM205 -42q2 3 26 0.5t48.5 -15.5t24.5 -36q0 -32 -46 -66l-95 -75q-14 -10 -39.5 5t-14.5 32z" />
<glyph unicode="&#xe8;" horiz-adv-x="528" d="M241 598q-45 29 -45 70q0 26 17.5 43.5t43.5 17.5q29 0 54 -19q29 -19 55.5 -53.5t39.5 -62t9 -34.5q-9 -5 -32 -6.5t-65.5 10.5t-76.5 34zM22 196q0 131 88.5 217.5t218.5 86.5q91 0 142 -39t51 -104q0 -80 -63 -120t-218 -40q-26 0 -100 4v-3q0 -52 31 -81t88 -29 q90 0 144 71q2 2 16.5 -1.5t29 -17t14.5 -34.5q0 -32 -27 -60q-60 -58 -179 -58q-116 0 -176 57t-60 151zM159 287q39 -3 70 -3q174 0 174 69q0 21 -21 36.5t-59 15.5q-54 0 -97.5 -33t-66.5 -85z" />
<glyph unicode="&#xe9;" horiz-adv-x="528" d="M267 560q-3 7 14 34.5t49 61.5t63 51q25 15 54 15q26 0 43 -17.5t17 -42.5q0 -41 -52 -70q-38 -21 -82.5 -31t-73 -9t-32.5 8zM22 196q0 131 88.5 217.5t218.5 86.5q91 0 142 -39t51 -104q0 -80 -63 -120t-218 -40q-26 0 -100 4v-3q0 -52 31 -81t88 -29q90 0 144 71 q2 2 16.5 -1.5t29 -17t14.5 -34.5q0 -32 -27 -60q-60 -58 -179 -58q-116 0 -176 57t-60 151zM159 287q39 -3 70 -3q174 0 174 69q0 21 -21 36.5t-59 15.5q-54 0 -97.5 -33t-66.5 -85z" />
<glyph unicode="&#xea;" horiz-adv-x="528" d="M239 544q-19 0 -30.5 12t-11.5 29q0 42 66 92q67 52 116 63q32 -11 77 -62q44 -49 44 -82q0 -23 -13.5 -37.5t-35.5 -14.5q-38 0 -70 59q-3 7 -10 21t-9 19q-30 -35 -35 -40q-48 -59 -88 -59zM22 196q0 131 88.5 217.5t218.5 86.5q91 0 142 -39t51 -104q0 -80 -63 -120 t-218 -40q-26 0 -100 4v-3q0 -52 31 -81t88 -29q90 0 144 71q2 2 16.5 -1.5t29 -17t14.5 -34.5q0 -32 -27 -60q-60 -58 -179 -58q-116 0 -176 57t-60 151zM159 287q39 -3 70 -3q174 0 174 69q0 21 -21 36.5t-59 15.5q-54 0 -97.5 -33t-66.5 -85z" />
<glyph unicode="&#xeb;" horiz-adv-x="528" d="M452 544q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5zM249 544q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5zM22 196q0 131 88.5 217.5t218.5 86.5q91 0 142 -39 t51 -104q0 -80 -63 -120t-218 -40q-26 0 -100 4v-3q0 -52 31 -81t88 -29q90 0 144 71q2 2 16.5 -1.5t29 -17t14.5 -34.5q0 -32 -27 -60q-60 -58 -179 -58q-116 0 -176 57t-60 151zM159 287q39 -3 70 -3q174 0 174 69q0 21 -21 36.5t-59 15.5q-54 0 -97.5 -33t-66.5 -85z" />
<glyph unicode="&#xec;" horiz-adv-x="255" d="M88 598q-45 29 -45 70q0 26 17.5 43.5t43.5 17.5q29 0 54 -19q29 -19 55.5 -53.5t39.5 -62t9 -34.5q-9 -5 -32 -6.5t-65.5 10.5t-76.5 34zM32 125l64 299q8 38 23 53t42 15h15q75 0 61 -68l-65 -303q-8 -41 0 -59t39 -22q2 -2 -3 -13.5t-25.5 -24t-53.5 -12.5 q-126 0 -97 135z" />
<glyph unicode="&#xed;" horiz-adv-x="255" d="M121 560q-3 7 14 34.5t49 61.5t63 51q25 15 54 15q26 0 43 -17.5t17 -42.5q0 -41 -52 -70q-38 -21 -82.5 -31t-73 -9t-32.5 8zM32 125l64 299q8 38 23 53t42 15h15q75 0 61 -68l-65 -303q-8 -41 0 -59t39 -22q2 -2 -3 -13.5t-25.5 -24t-53.5 -12.5q-126 0 -97 135z" />
<glyph unicode="&#xee;" horiz-adv-x="255" d="M92 544q-19 0 -30.5 12t-11.5 29q0 42 66 92q67 52 116 63q32 -11 77 -62q44 -49 44 -82q0 -23 -13.5 -37.5t-35.5 -14.5q-38 0 -70 59q-3 7 -10 21t-9 19q-30 -35 -35 -40q-48 -59 -88 -59zM32 125l64 299q8 38 23 53t42 15h15q75 0 61 -68l-65 -303q-8 -41 0 -59 t39 -22q2 -2 -3 -13.5t-25.5 -24t-53.5 -12.5q-126 0 -97 135z" />
<glyph unicode="&#xef;" horiz-adv-x="255" d="M305 544q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5zM102 544q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5zM32 125l64 299q8 38 23 53t42 15h15q75 0 61 -68l-65 -303 q-8 -41 0 -59t39 -22q2 -2 -3 -13.5t-25.5 -24t-53.5 -12.5q-126 0 -97 135z" />
<glyph unicode="&#xf0;" horiz-adv-x="588" d="M20 197q0 103 67.5 174t174.5 71q65 0 112 -25t69 -64q4 118 -60 194l-161 -69q-2 -1 -7 4.5t-10 17.5t-5 26q0 43 45 60l58 24q-57 29 -125 34q-5 0 1.5 18t29.5 36t60 18q85 0 164 -59l129 54q2 1 7 -5t10 -18t5 -26q0 -43 -44 -60l-36 -14q89 -120 68 -284 q-11 -150 -102 -241q-75 -75 -193 -75q-114 0 -185.5 55.5t-71.5 153.5zM156 197q0 -48 31 -76t82 -28q58 0 98 40q43 43 58 131q-11 32 -46 56t-81 24q-67 0 -105 -42t-37 -105z" />
<glyph unicode="&#xf1;" horiz-adv-x="582" d="M238 547q-20 0 -31.5 12t-11.5 29q0 44 31.5 77t69.5 33q68 0 132 -69q29 65 64 65q20 0 31.5 -11.5t11.5 -28.5q0 -45 -31.5 -78t-69.5 -33q-66 0 -132 69q-29 -65 -64 -65zM14 63l89 420q3 9 39 9q34 0 56.5 -21t21.5 -66q63 95 168 95q74 0 116 -43.5t42 -114.5 q0 -41 -29 -143q-23 -87 -23 -108q0 -37 40 -43q3 0 -2.5 -14.5t-27 -29t-55.5 -14.5q-48 0 -72.5 23t-24.5 64q0 32 24 120q22 78 22 105q0 38 -20.5 59t-58.5 21q-66 0 -112 -71l-52 -248q-8 -37 -23 -52t-42 -15h-15q-75 0 -61 67z" />
<glyph unicode="&#xf2;" horiz-adv-x="559" d="M221 598q-45 29 -45 70q0 26 17.5 43.5t43.5 17.5q29 0 54 -19q29 -19 55.5 -53.5t39.5 -62t9 -34.5q-9 -5 -32 -6.5t-65.5 10.5t-76.5 34zM25 212q0 117 76.5 202.5t199.5 85.5q107 0 170.5 -63t63.5 -161q0 -117 -76.5 -202.5t-199.5 -85.5q-107 0 -170.5 63t-63.5 161 zM148 217q0 -53 33 -87.5t82 -34.5q64 0 106.5 52.5t42.5 122.5q0 53 -32.5 88t-82.5 35q-64 0 -106.5 -52.5t-42.5 -123.5z" />
<glyph unicode="&#xf3;" horiz-adv-x="559" d="M258 560q-3 7 14 34.5t49 61.5t63 51q26 15 54 15q26 0 43 -17.5t17 -42.5q0 -41 -52 -70q-38 -21 -82.5 -31t-73 -9t-32.5 8zM25 212q0 117 76.5 202.5t199.5 85.5q107 0 170.5 -63t63.5 -161q0 -117 -76.5 -202.5t-199.5 -85.5q-107 0 -170.5 63t-63.5 161zM148 217 q0 -53 33 -87.5t82 -34.5q64 0 106.5 52.5t42.5 122.5q0 53 -32.5 88t-82.5 35q-64 0 -106.5 -52.5t-42.5 -123.5z" />
<glyph unicode="&#xf4;" horiz-adv-x="559" d="M228 544q-19 0 -30.5 12t-11.5 29q0 42 66 92q67 52 116 63q32 -11 77 -62q44 -49 44 -82q0 -23 -13.5 -37.5t-35.5 -14.5q-38 0 -70 59q-3 7 -10 21t-9 19q-30 -35 -35 -40q-48 -59 -88 -59zM25 212q0 117 76.5 202.5t199.5 85.5q107 0 170.5 -63t63.5 -161 q0 -117 -76.5 -202.5t-199.5 -85.5q-107 0 -170.5 63t-63.5 161zM148 217q0 -53 33 -87.5t82 -34.5q64 0 106.5 52.5t42.5 122.5q0 53 -32.5 88t-82.5 35q-64 0 -106.5 -52.5t-42.5 -123.5z" />
<glyph unicode="&#xf5;" horiz-adv-x="559" d="M216 547q-20 0 -31.5 12t-11.5 29q0 44 31.5 77t69.5 33q68 0 132 -69q29 65 64 65q20 0 31.5 -11.5t11.5 -28.5q0 -45 -31.5 -78t-69.5 -33q-66 0 -132 69q-29 -65 -64 -65zM25 212q0 117 76.5 202.5t199.5 85.5q107 0 170.5 -63t63.5 -161q0 -117 -76.5 -202.5 t-199.5 -85.5q-107 0 -170.5 63t-63.5 161zM148 217q0 -53 33 -87.5t82 -34.5q64 0 106.5 52.5t42.5 122.5q0 53 -32.5 88t-82.5 35q-64 0 -106.5 -52.5t-42.5 -123.5z" />
<glyph unicode="&#xf6;" horiz-adv-x="559" d="M441 544q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5zM238 544q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5zM25 212q0 117 76.5 202.5t199.5 85.5q107 0 170.5 -63 t63.5 -161q0 -117 -76.5 -202.5t-199.5 -85.5q-107 0 -170.5 63t-63.5 161zM148 217q0 -53 33 -87.5t82 -34.5q64 0 106.5 52.5t42.5 122.5q0 53 -32.5 88t-82.5 35q-64 0 -106.5 -52.5t-42.5 -123.5z" />
<glyph unicode="&#xf7;" horiz-adv-x="600" d="M333 377q-59 0 -59 46q0 45 22 67q17 17 55 17q59 0 59 -45t-22 -68q-19 -17 -55 -17zM114 206q-28 0 -40.5 11.5t-10.5 31.5q0 25 13 38q15 26 55 26h362q28 0 40 -11.5t10 -32.5q0 -25 -12 -37q-15 -26 -54 -26h-363zM196 58q0 45 23 68q17 17 54 17q60 0 60 -46 q0 -45 -22 -67q-17 -17 -55 -17q-60 0 -60 45z" />
<glyph unicode="&#xf8;" horiz-adv-x="559" d="M-22 3q0 22 26 45l53 48q-32 52 -32 116q0 117 76.5 202.5t199.5 85.5q82 0 145 -45l83 78q3 3 16 -2.5t25 -18t12 -27.5q0 -22 -25 -46l-53 -49q31 -51 31 -114q0 -117 -76.5 -202.5t-199.5 -85.5q-83 0 -143 44l-84 -77q-3 -3 -16 2t-25.5 18t-12.5 28zM151 172 l217 200q-33 25 -72 25q-64 0 -108 -54t-44 -125q0 -26 7 -46zM192 114q32 -24 72 -24q64 0 107.5 54.5t43.5 125.5q0 20 -7 44z" />
<glyph unicode="&#xf9;" horiz-adv-x="584" d="M233 598q-45 29 -45 70q0 26 17.5 43.5t43.5 17.5q29 0 54 -19q29 -19 55.5 -53.5t39.5 -62t9 -34.5q-9 -5 -32 -6.5t-65.5 10.5t-76.5 34zM188 -12q-90 0 -129 60.5t-18 162.5l45 213q8 38 23 53t42 15h16q32 0 50 -16t10 -52l-44 -203q-11 -52 8.5 -84t65.5 -32 q65 0 112 72l52 247q8 38 22.5 53t42.5 15h15q75 0 61 -68l-64 -303q-9 -41 -1.5 -58.5t36.5 -22.5q3 -1 -2 -13t-24.5 -24.5t-51.5 -12.5q-97 0 -99 92q-64 -94 -168 -94z" />
<glyph unicode="&#xfa;" horiz-adv-x="584" d="M271 560q-3 7 14 34.5t49 61.5t63 51q25 15 54 15q26 0 43 -17.5t17 -42.5q0 -41 -52 -70q-38 -21 -82.5 -31t-73 -9t-32.5 8zM188 -12q-90 0 -129 60.5t-18 162.5l45 213q8 38 23 53t42 15h16q32 0 50 -16t10 -52l-44 -203q-11 -52 8.5 -84t65.5 -32q65 0 112 72l52 247 q8 38 22.5 53t42.5 15h15q75 0 61 -68l-64 -303q-9 -41 -1.5 -58.5t36.5 -22.5q3 -1 -2 -13t-24.5 -24.5t-51.5 -12.5q-97 0 -99 92q-64 -94 -168 -94z" />
<glyph unicode="&#xfb;" horiz-adv-x="584" d="M240 544q-19 0 -30.5 12t-11.5 29q0 42 66 92q67 52 116 63q32 -11 77 -62q44 -49 44 -82q0 -23 -13.5 -37.5t-35.5 -14.5q-38 0 -70 59q-3 7 -10 21t-9 19q-30 -35 -35 -40q-48 -59 -88 -59zM188 -12q-90 0 -129 60.5t-18 162.5l45 213q8 38 23 53t42 15h16q32 0 50 -16 t10 -52l-44 -203q-11 -52 8.5 -84t65.5 -32q65 0 112 72l52 247q8 38 22.5 53t42.5 15h15q75 0 61 -68l-64 -303q-9 -41 -1.5 -58.5t36.5 -22.5q3 -1 -2 -13t-24.5 -24.5t-51.5 -12.5q-97 0 -99 92q-64 -94 -168 -94z" />
<glyph unicode="&#xfc;" horiz-adv-x="584" d="M453 544q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5zM250 544q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5zM188 -12q-90 0 -129 60.5t-18 162.5l45 213q8 38 23 53 t42 15h16q32 0 50 -16t10 -52l-44 -203q-11 -52 8.5 -84t65.5 -32q65 0 112 72l52 247q8 38 22.5 53t42.5 15h15q75 0 61 -68l-64 -303q-9 -41 -1.5 -58.5t36.5 -22.5q3 -1 -2 -13t-24.5 -24.5t-51.5 -12.5q-97 0 -99 92q-64 -94 -168 -94z" />
<glyph unicode="&#xfd;" horiz-adv-x="529" d="M249 555q-3 7 14 34.5t49 61.5t63 51q25 15 54 15q26 0 43 -17.5t17 -42.5q0 -41 -52 -70q-38 -21 -82.5 -31t-73 -9t-32.5 8zM-31 -70q21 -21 55 -21q29 0 57 22.5t70 83.5l12 18q-33 8 -39 52l-29 240q-6 51 -23.5 74t-48.5 25q-4 0 1.5 18t28 36t60.5 18 q98 0 111 -165l15 -182l205 346q1 3 24.5 -1t47 -21.5t23.5 -46.5q0 -26 -29 -72l-243 -386q-58 -92 -104.5 -131t-107.5 -39q-66 0 -95 29q-19 19 -19 50q0 13 6.5 26.5t13 20.5t8.5 6z" />
<glyph unicode="&#xfe;" horiz-adv-x="603" d="M-27 -127l160 754q9 38 23.5 53t41.5 15h16q32 0 50 -16t10 -52l-44 -208q58 81 155 81q82 0 137 -56t55 -153q0 -121 -72 -212t-183 -91q-54 0 -98 25t-66 68l-44 -208q-7 -38 -22 -53t-43 -15h-15q-32 0 -50.5 16t-10.5 52zM192 240q-11 -60 18.5 -100.5t89.5 -40.5 q65 0 103 52t38 123q0 52 -29 83t-74 31q-80 0 -131 -76z" />
<glyph unicode="&#xff;" horiz-adv-x="529" d="M425 544q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5zM222 544q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5zM-31 -70q21 -21 55 -21q29 0 57 22.5t70 83.5l12 18 q-33 8 -39 52l-29 240q-6 51 -23.5 74t-48.5 25q-4 0 1.5 18t28 36t60.5 18q98 0 111 -165l15 -182l205 346q1 3 24.5 -1t47 -21.5t23.5 -46.5q0 -26 -29 -72l-243 -386q-58 -92 -104.5 -131t-107.5 -39q-66 0 -95 29q-19 19 -19 50q0 13 6.5 26.5t13 20.5t8.5 6z" />
<glyph unicode="&#x152;" horiz-adv-x="1025" d="M49 274q0 161 104 273.5t261 112.5q49 0 98 -13h489q62 0 62 -44q0 -15 -6.5 -36t-17.5 -32q-15 -16 -57 -16h-304q32 -59 35 -129h160q59 0 59 -41t-23 -64q-16 -16 -53 -16h-156q-20 -78 -70 -141h267q62 0 62 -43t-24 -69q-16 -16 -57 -16h-439q-50 -12 -90 -12 q-141 0 -220.5 81.5t-79.5 204.5zM197 284q0 -72 43 -119.5t118 -47.5q83 0 145.5 71.5t62.5 176.5q0 72 -43 119t-118 47q-84 0 -146 -71.5t-62 -175.5z" />
<glyph unicode="&#x153;" horiz-adv-x="924" d="M25 212q0 117 75.5 202.5t196.5 85.5q70 0 122.5 -31t76.5 -86q84 117 230 117q91 0 141 -39t50 -104q0 -80 -63 -120t-218 -40q-24 0 -100 4v-3q0 -52 31 -81t89 -29q89 0 143 71q2 2 16.5 -1.5t29 -17t14.5 -34.5q0 -32 -27 -60q-58 -58 -179 -58q-69 0 -120 26.5 t-75 73.5q-77 -100 -204 -100q-105 0 -167 62.5t-62 161.5zM554 287q39 -3 70 -3q174 0 174 69q0 21 -21 36.5t-59 15.5q-54 0 -97.5 -33t-66.5 -85zM148 217q0 -53 33 -87.5t82 -34.5q64 0 106.5 52.5t42.5 122.5q0 53 -32.5 88t-82.5 35q-64 0 -106.5 -52.5t-42.5 -123.5z " />
<glyph unicode="&#x178;" horiz-adv-x="599" d="M489 699q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5zM286 699q-34 0 -52.5 16t-18.5 44q0 36 22 59.5t56 23.5t52.5 -16t18.5 -44q0 -36 -22 -59.5t-56 -23.5zM190 64l42 198l-152 336q-2 5 8 17t32.5 24t48.5 12 q44 0 65 -58l89 -217l233 281q2 2 23.5 -2t44 -22t22.5 -47q0 -33 -43 -80l-224 -248l-42 -195q-14 -67 -74 -67h-21q-31 0 -45 17t-7 51z" />
<glyph unicode="&#x2c6;" d="M214 544q-19 0 -30.5 12t-11.5 29q0 42 66 92q67 52 116 63q32 -11 77 -62q44 -49 44 -82q0 -23 -13.5 -37.5t-35.5 -14.5q-38 0 -70 59q-3 7 -10 21t-9 19q-30 -35 -35 -40q-48 -59 -88 -59z" />
<glyph unicode="&#x2dc;" d="M202 547q-20 0 -31.5 12t-11.5 29q0 44 31.5 77t69.5 33q68 0 132 -69q29 65 64 65q20 0 31.5 -11.5t11.5 -28.5q0 -45 -31.5 -78t-69.5 -33q-66 0 -132 69q-29 -65 -64 -65z" />
<glyph unicode="&#x2000;" horiz-adv-x="450" />
<glyph unicode="&#x2001;" horiz-adv-x="901" />
<glyph unicode="&#x2002;" horiz-adv-x="450" />
<glyph unicode="&#x2003;" horiz-adv-x="901" />
<glyph unicode="&#x2004;" horiz-adv-x="300" />
<glyph unicode="&#x2005;" horiz-adv-x="225" />
<glyph unicode="&#x2006;" horiz-adv-x="150" />
<glyph unicode="&#x2007;" horiz-adv-x="150" />
<glyph unicode="&#x2008;" horiz-adv-x="112" />
<glyph unicode="&#x2009;" horiz-adv-x="180" />
<glyph unicode="&#x200a;" horiz-adv-x="50" />
<glyph unicode="&#x2010;" horiz-adv-x="428" d="M91 180q-58 0 -58 45q0 14 6.5 35t17.5 32q16 17 53 17h227q58 0 58 -44q0 -14 -6.5 -35t-17.5 -33q-17 -17 -53 -17h-227z" />
<glyph unicode="&#x2011;" horiz-adv-x="428" d="M91 180q-58 0 -58 45q0 14 6.5 35t17.5 32q16 17 53 17h227q58 0 58 -44q0 -14 -6.5 -35t-17.5 -33q-17 -17 -53 -17h-227z" />
<glyph unicode="&#x2012;" horiz-adv-x="428" d="M91 180q-58 0 -58 45q0 14 6.5 35t17.5 32q16 17 53 17h227q58 0 58 -44q0 -14 -6.5 -35t-17.5 -33q-17 -17 -53 -17h-227z" />
<glyph unicode="&#x2013;" horiz-adv-x="555" d="M91 185q-57 0 -57 41q0 42 22 63q15 15 53 15h356q58 0 58 -40q0 -41 -22 -63q-16 -16 -54 -16h-356z" />
<glyph unicode="&#x2014;" horiz-adv-x="720" d="M91 185q-57 0 -57 41q0 42 22 63q15 15 53 15h521q57 0 57 -40q0 -42 -22 -63q-15 -16 -53 -16h-521z" />
<glyph unicode="&#x2018;" horiz-adv-x="234" d="M190 393q-1 -2 -13 -2.5t-29 2t-33.5 8.5t-28.5 19t-12 32q0 34 39 77l115 132q14 14 44.5 -1t20.5 -39z" />
<glyph unicode="&#x2019;" horiz-adv-x="234" d="M125 392q-13 -13 -43.5 1.5t-20.5 38.5l103 228q1 2 13 2.5t29 -2t33.5 -8.5t28.5 -19t12 -32q0 -35 -40 -76z" />
<glyph unicode="&#x201a;" horiz-adv-x="243" d="M-49 -86l103 229q1 1 13 1.5t29 -2t33.5 -8t28.5 -19t12 -32.5q0 -34 -39 -76l-115 -133q-14 -13 -44.5 1.5t-20.5 38.5z" />
<glyph unicode="&#x201c;" horiz-adv-x="435" d="M185 396q-1 -2 -12.5 -2.5t-28 2t-32.5 8t-27 19t-11 32.5q0 34 41 76l118 135q13 14 43 -1t20 -39zM277 452q0 33 37 74l113 133q13 14 44 -1t21 -39l-101 -228q-1 -2 -12.5 -2.5t-28.5 2.5t-33.5 9t-28 19.5t-11.5 32.5z" />
<glyph unicode="&#x201d;" horiz-adv-x="435" d="M128 395q-13 -14 -44 1t-22 39l102 228q1 2 12.5 2.5t28 -2.5t33 -9t28 -19.5t11.5 -32.5q0 -34 -37 -75zM259 428l111 230q1 2 12.5 2.5t28 -2t32.5 -8t27 -19t11 -32.5q0 -33 -41 -77l-118 -135q-14 -14 -43.5 1.5t-19.5 39.5z" />
<glyph unicode="&#x201e;" horiz-adv-x="439" d="M-52 -85l101 228q1 2 12.5 2.5t28.5 -2t33.5 -8.5t28 -20t11.5 -33q0 -33 -37 -74l-113 -133q-13 -14 -44 1t-21 39zM144 -92l111 230q1 2 12.5 2.5t28 -2t32.5 -8t27 -18.5t11 -32q0 -35 -41 -77l-118 -135q-13 -14 -43 1t-20 39z" />
<glyph unicode="&#x2022;" horiz-adv-x="347" d="M174 116q-54 0 -91 37t-37 92t37 93t91 38t91 -38t37 -93q0 -53 -37.5 -91t-90.5 -38z" />
<glyph unicode="&#x2026;" horiz-adv-x="716" d="M480 44q0 51 26 77q19 19 63 19q68 0 68 -52t-26 -77q-18 -18 -63 -18q-68 0 -68 51zM241 44q0 51 26 77q19 19 63 19q69 0 69 -52q0 -51 -26 -77q-18 -18 -63 -18q-69 0 -69 51zM3 44q0 52 26 77q19 19 63 19q68 0 68 -52q0 -51 -26 -77q-18 -18 -63 -18q-68 0 -68 51z " />
<glyph unicode="&#x202f;" horiz-adv-x="180" />
<glyph unicode="&#x2039;" horiz-adv-x="318" d="M90 145q-23 34 -40 67t-15 40q1 7 30.5 39t65.5 63q83 72 130 72q24 0 37 -12t13 -33q0 -46 -87 -99q-49 -28 -78 -42q33 -22 60 -48q50 -49 50 -77q0 -22 -16 -36.5t-43 -14.5q-49 0 -107 81z" />
<glyph unicode="&#x203a;" horiz-adv-x="318" d="M58 64q-23 0 -36.5 12.5t-13.5 33.5q0 46 88 99q39 24 77 42q-37 25 -59 48q-50 47 -50 76q0 22 16 36.5t43 14.5q49 0 107 -81q22 -32 39 -65.5t16 -40.5t-30.5 -39t-66.5 -63q-82 -73 -130 -73z" />
<glyph unicode="&#x205f;" horiz-adv-x="225" />
<glyph unicode="&#x20ac;" horiz-adv-x="631" d="M68 205q-46 0 -46 31q0 28 15 43q14 14 41 14h40q5 30 12 62h-30q-46 0 -46 32q0 28 15 43q14 14 41 14h48q31 82 76 127q90 89 220 89q109 0 170 -59q33 -33 33 -78q0 -31 -19.5 -50.5t-38 -24.5t-20.5 -2q-35 94 -138 94q-65 0 -107 -44q-20 -20 -35 -52h156 q46 0 46 -31q0 -29 -16 -44q-14 -14 -40 -14h-177q-7 -32 -12 -62h166q47 0 47 -31q0 -28 -16 -44q-13 -13 -40 -13h-159q9 -45 41.5 -71t83.5 -26q44 0 76.5 17t65.5 57q3 3 17 -4t27.5 -23.5t13.5 -35.5q0 -35 -32 -67q-28 -28 -76.5 -46t-102.5 -18q-112 0 -179 58 t-75 159h-46z" />
<glyph unicode="&#x2122;" horiz-adv-x="700" d="M426 331q-33 0 -33 39v240q0 41 36 41h29q28 0 39 -24l55 -136l56 136q11 24 38 24h29q36 0 36 -41v-240q0 -39 -34 -39h-12q-34 0 -34 39v160l-43 -117q-2 -7 -3.5 -10t-9.5 -5.5t-23 -2.5q-6 0 -11 0.5t-8 0.5t-6 1.5l-4 2t-2.5 3t-2 3t-1.5 3.5t-1 4l-44 115v-158 q0 -39 -32 -39h-14zM206 331q-35 0 -35 39v203h-62q-38 0 -38 37v3q0 35 38 35h207q38 0 38 -35v-3q0 -37 -38 -37h-61v-203q0 -39 -37 -39h-12z" />
<glyph unicode="&#x25fc;" d="M0 500h500v-500h-500v500z" />
<hkern u1="&#x26;" u2="&#x178;" k="17" />
<hkern u1="&#x26;" u2="&#xdd;" k="17" />
<hkern u1="&#x26;" u2="&#xc6;" k="-22" />
<hkern u1="&#x26;" u2="&#xc5;" k="-22" />
<hkern u1="&#x26;" u2="&#xc4;" k="-22" />
<hkern u1="&#x26;" u2="&#xc3;" k="-22" />
<hkern u1="&#x26;" u2="&#xc2;" k="-22" />
<hkern u1="&#x26;" u2="&#xc1;" k="-22" />
<hkern u1="&#x26;" u2="&#xc0;" k="-22" />
<hkern u1="&#x26;" u2="Y" k="17" />
<hkern u1="&#x26;" u2="X" k="-11" />
<hkern u1="&#x26;" u2="W" k="11" />
<hkern u1="&#x26;" u2="V" k="11" />
<hkern u1="&#x26;" u2="T" k="22" />
<hkern u1="&#x26;" u2="J" k="-11" />
<hkern u1="&#x26;" u2="A" k="-22" />
<hkern u1="&#x28;" u2="&#x178;" k="-28" />
<hkern u1="&#x28;" u2="&#xdd;" k="-28" />
<hkern u1="&#x28;" u2="j" k="-83" />
<hkern u1="&#x28;" u2="g" k="-11" />
<hkern u1="&#x28;" u2="Y" k="-28" />
<hkern u1="&#x28;" u2="X" k="-22" />
<hkern u1="&#x28;" u2="W" k="-6" />
<hkern u1="&#x28;" u2="V" k="-17" />
<hkern u1="&#x2a;" u2="&#xc6;" k="33" />
<hkern u1="&#x2a;" u2="&#xc5;" k="33" />
<hkern u1="&#x2a;" u2="&#xc4;" k="33" />
<hkern u1="&#x2a;" u2="&#xc3;" k="33" />
<hkern u1="&#x2a;" u2="&#xc2;" k="33" />
<hkern u1="&#x2a;" u2="&#xc1;" k="33" />
<hkern u1="&#x2a;" u2="&#xc0;" k="33" />
<hkern u1="&#x2a;" u2="A" k="33" />
<hkern u1="&#x2c;" u2="&#x178;" k="28" />
<hkern u1="&#x2c;" u2="&#x153;" k="11" />
<hkern u1="&#x2c;" u2="&#x152;" k="22" />
<hkern u1="&#x2c;" u2="&#xe7;" k="11" />
<hkern u1="&#x2c;" u2="&#xe6;" k="11" />
<hkern u1="&#x2c;" u2="&#xdd;" k="28" />
<hkern u1="&#x2c;" u2="&#xdc;" k="6" />
<hkern u1="&#x2c;" u2="&#xdb;" k="6" />
<hkern u1="&#x2c;" u2="&#xda;" k="6" />
<hkern u1="&#x2c;" u2="&#xd9;" k="6" />
<hkern u1="&#x2c;" u2="&#xd8;" k="22" />
<hkern u1="&#x2c;" u2="&#xd6;" k="22" />
<hkern u1="&#x2c;" u2="&#xd5;" k="22" />
<hkern u1="&#x2c;" u2="&#xd4;" k="22" />
<hkern u1="&#x2c;" u2="&#xd3;" k="22" />
<hkern u1="&#x2c;" u2="&#xd2;" k="22" />
<hkern u1="&#x2c;" u2="&#xc6;" k="-17" />
<hkern u1="&#x2c;" u2="&#xc5;" k="-17" />
<hkern u1="&#x2c;" u2="&#xc4;" k="-17" />
<hkern u1="&#x2c;" u2="&#xc3;" k="-17" />
<hkern u1="&#x2c;" u2="&#xc2;" k="-17" />
<hkern u1="&#x2c;" u2="&#xc1;" k="-17" />
<hkern u1="&#x2c;" u2="&#xc0;" k="-17" />
<hkern u1="&#x2c;" u2="y" k="17" />
<hkern u1="&#x2c;" u2="w" k="6" />
<hkern u1="&#x2c;" u2="v" k="17" />
<hkern u1="&#x2c;" u2="u" k="11" />
<hkern u1="&#x2c;" u2="t" k="22" />
<hkern u1="&#x2c;" u2="r" k="11" />
<hkern u1="&#x2c;" u2="q" k="11" />
<hkern u1="&#x2c;" u2="p" k="11" />
<hkern u1="&#x2c;" u2="o" k="11" />
<hkern u1="&#x2c;" u2="n" k="11" />
<hkern u1="&#x2c;" u2="m" k="11" />
<hkern u1="&#x2c;" u2="e" k="11" />
<hkern u1="&#x2c;" u2="d" k="11" />
<hkern u1="&#x2c;" u2="c" k="11" />
<hkern u1="&#x2c;" u2="a" k="11" />
<hkern u1="&#x2c;" u2="Y" k="28" />
<hkern u1="&#x2c;" u2="W" k="11" />
<hkern u1="&#x2c;" u2="V" k="33" />
<hkern u1="&#x2c;" u2="U" k="6" />
<hkern u1="&#x2c;" u2="T" k="33" />
<hkern u1="&#x2c;" u2="Q" k="22" />
<hkern u1="&#x2c;" u2="O" k="22" />
<hkern u1="&#x2c;" u2="G" k="22" />
<hkern u1="&#x2c;" u2="C" k="22" />
<hkern u1="&#x2c;" u2="A" k="-17" />
<hkern u1="&#x2c;" u2="&#x39;" k="6" />
<hkern u1="&#x2c;" u2="&#x38;" k="8" />
<hkern u1="&#x2c;" u2="&#x36;" k="17" />
<hkern u1="&#x2c;" u2="&#x34;" k="28" />
<hkern u1="&#x2c;" u2="&#x31;" k="33" />
<hkern u1="&#x2c;" u2="&#x30;" k="22" />
<hkern u1="&#x2d;" u2="&#x178;" k="28" />
<hkern u1="&#x2d;" u2="&#x153;" k="6" />
<hkern u1="&#x2d;" u2="&#xe7;" k="6" />
<hkern u1="&#x2d;" u2="&#xe6;" k="6" />
<hkern u1="&#x2d;" u2="&#xdd;" k="28" />
<hkern u1="&#x2d;" u2="&#xc6;" k="-6" />
<hkern u1="&#x2d;" u2="&#xc5;" k="-6" />
<hkern u1="&#x2d;" u2="&#xc4;" k="-6" />
<hkern u1="&#x2d;" u2="&#xc3;" k="-6" />
<hkern u1="&#x2d;" u2="&#xc2;" k="-6" />
<hkern u1="&#x2d;" u2="&#xc1;" k="-6" />
<hkern u1="&#x2d;" u2="&#xc0;" k="-6" />
<hkern u1="&#x2d;" u2="z" k="17" />
<hkern u1="&#x2d;" u2="y" k="6" />
<hkern u1="&#x2d;" u2="x" k="17" />
<hkern u1="&#x2d;" u2="v" k="6" />
<hkern u1="&#x2d;" u2="q" k="6" />
<hkern u1="&#x2d;" u2="o" k="6" />
<hkern u1="&#x2d;" u2="e" k="6" />
<hkern u1="&#x2d;" u2="d" k="6" />
<hkern u1="&#x2d;" u2="c" k="6" />
<hkern u1="&#x2d;" u2="a" k="6" />
<hkern u1="&#x2d;" u2="Z" k="6" />
<hkern u1="&#x2d;" u2="Y" k="28" />
<hkern u1="&#x2d;" u2="X" k="17" />
<hkern u1="&#x2d;" u2="W" k="22" />
<hkern u1="&#x2d;" u2="V" k="28" />
<hkern u1="&#x2d;" u2="T" k="39" />
<hkern u1="&#x2d;" u2="A" k="-6" />
<hkern u1="&#x2d;" u2="&#x37;" k="17" />
<hkern u1="&#x2d;" u2="&#x31;" k="11" />
<hkern u1="&#x2e;" u2="&#x178;" k="28" />
<hkern u1="&#x2e;" u2="&#x153;" k="11" />
<hkern u1="&#x2e;" u2="&#x152;" k="22" />
<hkern u1="&#x2e;" u2="&#xe7;" k="11" />
<hkern u1="&#x2e;" u2="&#xe6;" k="11" />
<hkern u1="&#x2e;" u2="&#xdd;" k="28" />
<hkern u1="&#x2e;" u2="&#xdc;" k="6" />
<hkern u1="&#x2e;" u2="&#xdb;" k="6" />
<hkern u1="&#x2e;" u2="&#xda;" k="6" />
<hkern u1="&#x2e;" u2="&#xd9;" k="6" />
<hkern u1="&#x2e;" u2="&#xd8;" k="22" />
<hkern u1="&#x2e;" u2="&#xd6;" k="22" />
<hkern u1="&#x2e;" u2="&#xd5;" k="22" />
<hkern u1="&#x2e;" u2="&#xd4;" k="22" />
<hkern u1="&#x2e;" u2="&#xd3;" k="22" />
<hkern u1="&#x2e;" u2="&#xd2;" k="22" />
<hkern u1="&#x2e;" u2="&#xc6;" k="-17" />
<hkern u1="&#x2e;" u2="&#xc5;" k="-17" />
<hkern u1="&#x2e;" u2="&#xc4;" k="-17" />
<hkern u1="&#x2e;" u2="&#xc3;" k="-17" />
<hkern u1="&#x2e;" u2="&#xc2;" k="-17" />
<hkern u1="&#x2e;" u2="&#xc1;" k="-17" />
<hkern u1="&#x2e;" u2="&#xc0;" k="-17" />
<hkern u1="&#x2e;" u2="y" k="17" />
<hkern u1="&#x2e;" u2="w" k="6" />
<hkern u1="&#x2e;" u2="v" k="17" />
<hkern u1="&#x2e;" u2="u" k="11" />
<hkern u1="&#x2e;" u2="t" k="22" />
<hkern u1="&#x2e;" u2="r" k="11" />
<hkern u1="&#x2e;" u2="q" k="11" />
<hkern u1="&#x2e;" u2="p" k="11" />
<hkern u1="&#x2e;" u2="o" k="11" />
<hkern u1="&#x2e;" u2="n" k="11" />
<hkern u1="&#x2e;" u2="m" k="11" />
<hkern u1="&#x2e;" u2="e" k="11" />
<hkern u1="&#x2e;" u2="d" k="11" />
<hkern u1="&#x2e;" u2="c" k="11" />
<hkern u1="&#x2e;" u2="a" k="11" />
<hkern u1="&#x2e;" u2="Y" k="28" />
<hkern u1="&#x2e;" u2="W" k="11" />
<hkern u1="&#x2e;" u2="V" k="33" />
<hkern u1="&#x2e;" u2="U" k="6" />
<hkern u1="&#x2e;" u2="T" k="33" />
<hkern u1="&#x2e;" u2="Q" k="22" />
<hkern u1="&#x2e;" u2="O" k="22" />
<hkern u1="&#x2e;" u2="G" k="22" />
<hkern u1="&#x2e;" u2="C" k="22" />
<hkern u1="&#x2e;" u2="A" k="-17" />
<hkern u1="&#x2e;" u2="&#x39;" k="6" />
<hkern u1="&#x2e;" u2="&#x38;" k="8" />
<hkern u1="&#x2e;" u2="&#x36;" k="17" />
<hkern u1="&#x2e;" u2="&#x34;" k="28" />
<hkern u1="&#x2e;" u2="&#x31;" k="33" />
<hkern u1="&#x2e;" u2="&#x30;" k="22" />
<hkern u1="&#x2f;" u2="&#x153;" k="6" />
<hkern u1="&#x2f;" u2="&#x152;" k="-6" />
<hkern u1="&#x2f;" u2="&#xe7;" k="6" />
<hkern u1="&#x2f;" u2="&#xe6;" k="6" />
<hkern u1="&#x2f;" u2="&#xd8;" k="-6" />
<hkern u1="&#x2f;" u2="&#xd6;" k="-6" />
<hkern u1="&#x2f;" u2="&#xd5;" k="-6" />
<hkern u1="&#x2f;" u2="&#xd4;" k="-6" />
<hkern u1="&#x2f;" u2="&#xd3;" k="-6" />
<hkern u1="&#x2f;" u2="&#xd2;" k="-6" />
<hkern u1="&#x2f;" u2="q" k="6" />
<hkern u1="&#x2f;" u2="o" k="6" />
<hkern u1="&#x2f;" u2="g" k="6" />
<hkern u1="&#x2f;" u2="e" k="6" />
<hkern u1="&#x2f;" u2="d" k="6" />
<hkern u1="&#x2f;" u2="c" k="6" />
<hkern u1="&#x2f;" u2="a" k="6" />
<hkern u1="&#x2f;" u2="Q" k="-6" />
<hkern u1="&#x2f;" u2="O" k="-6" />
<hkern u1="&#x2f;" u2="G" k="-6" />
<hkern u1="&#x2f;" u2="C" k="-6" />
<hkern u1="&#x2f;" u2="&#x34;" k="11" />
<hkern u1="&#x2f;" u2="&#x31;" k="-11" />
<hkern u1="&#x30;" u2="&#x2026;" k="22" />
<hkern u1="&#x30;" u2="&#x201e;" k="22" />
<hkern u1="&#x30;" u2="&#x201a;" k="22" />
<hkern u1="&#x30;" u2="&#x37;" k="11" />
<hkern u1="&#x30;" u2="&#x2e;" k="22" />
<hkern u1="&#x30;" u2="&#x2c;" k="22" />
<hkern u1="&#x32;" u2="&#x2014;" k="6" />
<hkern u1="&#x32;" u2="&#x2013;" k="6" />
<hkern u1="&#x32;" u2="&#x34;" k="17" />
<hkern u1="&#x32;" u2="&#x2d;" k="6" />
<hkern u1="&#x33;" u2="&#x2026;" k="11" />
<hkern u1="&#x33;" u2="&#x201e;" k="11" />
<hkern u1="&#x33;" u2="&#x201a;" k="11" />
<hkern u1="&#x33;" u2="&#x37;" k="11" />
<hkern u1="&#x33;" u2="&#x2e;" k="11" />
<hkern u1="&#x33;" u2="&#x2c;" k="11" />
<hkern u1="&#x34;" u2="&#x2122;" k="28" />
<hkern u1="&#x34;" u2="&#xb0;" k="22" />
<hkern u1="&#x34;" u2="&#x37;" k="6" />
<hkern u1="&#x35;" u2="&#x2026;" k="6" />
<hkern u1="&#x35;" u2="&#x201e;" k="6" />
<hkern u1="&#x35;" u2="&#x201a;" k="6" />
<hkern u1="&#x35;" u2="&#x37;" k="6" />
<hkern u1="&#x35;" u2="&#x2e;" k="6" />
<hkern u1="&#x35;" u2="&#x2c;" k="6" />
<hkern u1="&#x36;" u2="&#x2026;" k="6" />
<hkern u1="&#x36;" u2="&#x201e;" k="6" />
<hkern u1="&#x36;" u2="&#x201a;" k="6" />
<hkern u1="&#x36;" u2="&#x37;" k="6" />
<hkern u1="&#x36;" u2="&#x2e;" k="6" />
<hkern u1="&#x36;" u2="&#x2c;" k="6" />
<hkern u1="&#x37;" u2="&#x2026;" k="44" />
<hkern u1="&#x37;" u2="&#x201e;" k="44" />
<hkern u1="&#x37;" u2="&#x201a;" k="44" />
<hkern u1="&#x37;" u2="&#x2014;" k="22" />
<hkern u1="&#x37;" u2="&#x2013;" k="22" />
<hkern u1="&#x37;" u2="&#xa2;" k="22" />
<hkern u1="&#x37;" u2="&#x3b;" k="11" />
<hkern u1="&#x37;" u2="&#x3a;" k="11" />
<hkern u1="&#x37;" u2="&#x39;" k="-6" />
<hkern u1="&#x37;" u2="&#x35;" k="-8" />
<hkern u1="&#x37;" u2="&#x34;" k="33" />
<hkern u1="&#x37;" u2="&#x33;" k="-11" />
<hkern u1="&#x37;" u2="&#x31;" k="-11" />
<hkern u1="&#x37;" u2="&#x30;" k="6" />
<hkern u1="&#x37;" u2="&#x2e;" k="44" />
<hkern u1="&#x37;" u2="&#x2d;" k="22" />
<hkern u1="&#x37;" u2="&#x2c;" k="44" />
<hkern u1="&#x38;" u2="&#x2026;" k="8" />
<hkern u1="&#x38;" u2="&#x201e;" k="8" />
<hkern u1="&#x38;" u2="&#x201a;" k="8" />
<hkern u1="&#x38;" u2="&#x37;" k="8" />
<hkern u1="&#x38;" u2="&#x2e;" k="8" />
<hkern u1="&#x38;" u2="&#x2c;" k="8" />
<hkern u1="&#x39;" u2="&#x2026;" k="17" />
<hkern u1="&#x39;" u2="&#x201e;" k="17" />
<hkern u1="&#x39;" u2="&#x201a;" k="17" />
<hkern u1="&#x39;" u2="&#x37;" k="25" />
<hkern u1="&#x39;" u2="&#x2e;" k="17" />
<hkern u1="&#x39;" u2="&#x2c;" k="17" />
<hkern u1="&#x3a;" u2="&#x178;" k="17" />
<hkern u1="&#x3a;" u2="&#x153;" k="6" />
<hkern u1="&#x3a;" u2="&#xe7;" k="6" />
<hkern u1="&#x3a;" u2="&#xe6;" k="6" />
<hkern u1="&#x3a;" u2="&#xdd;" k="17" />
<hkern u1="&#x3a;" u2="q" k="6" />
<hkern u1="&#x3a;" u2="o" k="6" />
<hkern u1="&#x3a;" u2="e" k="6" />
<hkern u1="&#x3a;" u2="d" k="6" />
<hkern u1="&#x3a;" u2="c" k="6" />
<hkern u1="&#x3a;" u2="a" k="6" />
<hkern u1="&#x3a;" u2="Y" k="17" />
<hkern u1="&#x3a;" u2="W" k="11" />
<hkern u1="&#x3a;" u2="V" k="11" />
<hkern u1="&#x3a;" u2="T" k="28" />
<hkern u1="&#x3a;" u2="&#x37;" k="11" />
<hkern u1="&#x3a;" u2="&#x31;" k="17" />
<hkern u1="&#x3b;" u2="&#x178;" k="17" />
<hkern u1="&#x3b;" u2="&#x153;" k="6" />
<hkern u1="&#x3b;" u2="&#xe7;" k="6" />
<hkern u1="&#x3b;" u2="&#xe6;" k="6" />
<hkern u1="&#x3b;" u2="&#xdd;" k="17" />
<hkern u1="&#x3b;" u2="q" k="6" />
<hkern u1="&#x3b;" u2="o" k="6" />
<hkern u1="&#x3b;" u2="e" k="6" />
<hkern u1="&#x3b;" u2="d" k="6" />
<hkern u1="&#x3b;" u2="c" k="6" />
<hkern u1="&#x3b;" u2="a" k="6" />
<hkern u1="&#x3b;" u2="Y" k="17" />
<hkern u1="&#x3b;" u2="W" k="11" />
<hkern u1="&#x3b;" u2="V" k="11" />
<hkern u1="&#x3b;" u2="T" k="28" />
<hkern u1="&#x3b;" u2="&#x37;" k="11" />
<hkern u1="&#x3b;" u2="&#x31;" k="17" />
<hkern u1="&#x3e;" u2="&#x37;" k="33" />
<hkern u1="&#x40;" u2="&#x178;" k="17" />
<hkern u1="&#x40;" u2="&#xdd;" k="17" />
<hkern u1="&#x40;" u2="&#xc6;" k="6" />
<hkern u1="&#x40;" u2="&#xc5;" k="6" />
<hkern u1="&#x40;" u2="&#xc4;" k="6" />
<hkern u1="&#x40;" u2="&#xc3;" k="6" />
<hkern u1="&#x40;" u2="&#xc2;" k="6" />
<hkern u1="&#x40;" u2="&#xc1;" k="6" />
<hkern u1="&#x40;" u2="&#xc0;" k="6" />
<hkern u1="&#x40;" u2="Y" k="17" />
<hkern u1="&#x40;" u2="W" k="11" />
<hkern u1="&#x40;" u2="T" k="17" />
<hkern u1="&#x40;" u2="A" k="6" />
<hkern u1="A" u2="&#x2122;" k="39" />
<hkern u1="A" u2="&#x2026;" k="-17" />
<hkern u1="A" u2="&#x201e;" k="-17" />
<hkern u1="A" u2="&#x201d;" k="17" />
<hkern u1="A" u2="&#x201c;" k="33" />
<hkern u1="A" u2="&#x201a;" k="-17" />
<hkern u1="A" u2="&#x2019;" k="17" />
<hkern u1="A" u2="&#x2018;" k="33" />
<hkern u1="A" u2="&#x2014;" k="-6" />
<hkern u1="A" u2="&#x2013;" k="-6" />
<hkern u1="A" u2="&#x178;" k="39" />
<hkern u1="A" u2="&#x153;" k="3" />
<hkern u1="A" u2="&#x152;" k="22" />
<hkern u1="A" u2="&#xe7;" k="3" />
<hkern u1="A" u2="&#xe6;" k="3" />
<hkern u1="A" u2="&#xdd;" k="39" />
<hkern u1="A" u2="&#xd8;" k="22" />
<hkern u1="A" u2="&#xd6;" k="22" />
<hkern u1="A" u2="&#xd5;" k="22" />
<hkern u1="A" u2="&#xd4;" k="22" />
<hkern u1="A" u2="&#xd3;" k="22" />
<hkern u1="A" u2="&#xd2;" k="22" />
<hkern u1="A" u2="&#xc6;" k="-6" />
<hkern u1="A" u2="&#xc5;" k="-6" />
<hkern u1="A" u2="&#xc4;" k="-6" />
<hkern u1="A" u2="&#xc3;" k="-6" />
<hkern u1="A" u2="&#xc2;" k="-6" />
<hkern u1="A" u2="&#xc1;" k="-6" />
<hkern u1="A" u2="&#xc0;" k="-6" />
<hkern u1="A" u2="&#xae;" k="6" />
<hkern u1="A" u2="&#xa9;" k="6" />
<hkern u1="A" u2="z" k="-6" />
<hkern u1="A" u2="y" k="17" />
<hkern u1="A" u2="x" k="-11" />
<hkern u1="A" u2="w" k="6" />
<hkern u1="A" u2="v" k="17" />
<hkern u1="A" u2="u" k="3" />
<hkern u1="A" u2="t" k="3" />
<hkern u1="A" u2="r" k="3" />
<hkern u1="A" u2="q" k="3" />
<hkern u1="A" u2="p" k="3" />
<hkern u1="A" u2="o" k="3" />
<hkern u1="A" u2="n" k="3" />
<hkern u1="A" u2="m" k="3" />
<hkern u1="A" u2="e" k="3" />
<hkern u1="A" u2="d" k="3" />
<hkern u1="A" u2="c" k="3" />
<hkern u1="A" u2="a" k="3" />
<hkern u1="A" u2="Y" k="75" />
<hkern u1="A" u2="W" k="65" />
<hkern u1="A" u2="V" k="75" />
<hkern u1="A" u2="T" k="95" />
<hkern u1="A" u2="S" k="-3" />
<hkern u1="A" u2="Q" k="22" />
<hkern u1="A" u2="O" k="22" />
<hkern u1="A" u2="J" k="6" />
<hkern u1="A" u2="G" k="22" />
<hkern u1="A" u2="C" k="22" />
<hkern u1="A" u2="A" k="-6" />
<hkern u1="A" u2="&#x40;" k="6" />
<hkern u1="A" u2="&#x2e;" k="-17" />
<hkern u1="A" u2="&#x2d;" k="-6" />
<hkern u1="A" u2="&#x2c;" k="-17" />
<hkern u1="A" u2="&#x2a;" k="33" />
<hkern u1="B" u2="&#x178;" k="11" />
<hkern u1="B" u2="&#xdd;" k="11" />
<hkern u1="B" u2="Y" k="11" />
<hkern u1="B" u2="W" k="11" />
<hkern u1="B" u2="V" k="33" />
<hkern u1="C" u2="&#x153;" k="-3" />
<hkern u1="C" u2="&#xe7;" k="-3" />
<hkern u1="C" u2="&#xe6;" k="-3" />
<hkern u1="C" u2="z" k="6" />
<hkern u1="C" u2="y" k="3" />
<hkern u1="C" u2="w" k="3" />
<hkern u1="C" u2="v" k="3" />
<hkern u1="C" u2="t" k="3" />
<hkern u1="C" u2="q" k="-3" />
<hkern u1="C" u2="o" k="-3" />
<hkern u1="C" u2="e" k="-3" />
<hkern u1="C" u2="d" k="-3" />
<hkern u1="C" u2="c" k="-3" />
<hkern u1="C" u2="a" k="-3" />
<hkern u1="C" u2="Z" k="6" />
<hkern u1="C" u2="X" k="-6" />
<hkern u1="C" u2="V" k="-11" />
<hkern u1="C" u2="T" k="11" />
<hkern u1="D" u2="&#x2026;" k="22" />
<hkern u1="D" u2="&#x201e;" k="22" />
<hkern u1="D" u2="&#x201c;" k="11" />
<hkern u1="D" u2="&#x201a;" k="22" />
<hkern u1="D" u2="&#x2018;" k="11" />
<hkern u1="D" u2="&#x178;" k="25" />
<hkern u1="D" u2="&#x153;" k="3" />
<hkern u1="D" u2="&#xe7;" k="3" />
<hkern u1="D" u2="&#xe6;" k="3" />
<hkern u1="D" u2="&#xdd;" k="25" />
<hkern u1="D" u2="z" k="6" />
<hkern u1="D" u2="x" k="6" />
<hkern u1="D" u2="u" k="3" />
<hkern u1="D" u2="r" k="3" />
<hkern u1="D" u2="q" k="3" />
<hkern u1="D" u2="p" k="3" />
<hkern u1="D" u2="o" k="3" />
<hkern u1="D" u2="n" k="3" />
<hkern u1="D" u2="m" k="3" />
<hkern u1="D" u2="l" k="6" />
<hkern u1="D" u2="k" k="6" />
<hkern u1="D" u2="h" k="6" />
<hkern u1="D" u2="e" k="3" />
<hkern u1="D" u2="d" k="3" />
<hkern u1="D" u2="c" k="3" />
<hkern u1="D" u2="b" k="6" />
<hkern u1="D" u2="a" k="3" />
<hkern u1="D" u2="Z" k="25" />
<hkern u1="D" u2="Y" k="25" />
<hkern u1="D" u2="X" k="19" />
<hkern u1="D" u2="W" k="33" />
<hkern u1="D" u2="V" k="22" />
<hkern u1="D" u2="T" k="28" />
<hkern u1="D" u2="J" k="25" />
<hkern u1="D" u2="&#x3f;" k="11" />
<hkern u1="D" u2="&#x2f;" k="28" />
<hkern u1="D" u2="&#x2e;" k="22" />
<hkern u1="D" u2="&#x2c;" k="22" />
<hkern u1="E" u2="&#x153;" k="14" />
<hkern u1="E" u2="&#xe7;" k="14" />
<hkern u1="E" u2="&#xe6;" k="14" />
<hkern u1="E" u2="&#xae;" k="11" />
<hkern u1="E" u2="&#xa9;" k="11" />
<hkern u1="E" u2="y" k="11" />
<hkern u1="E" u2="v" k="11" />
<hkern u1="E" u2="q" k="14" />
<hkern u1="E" u2="o" k="14" />
<hkern u1="E" u2="g" k="3" />
<hkern u1="E" u2="f" k="6" />
<hkern u1="E" u2="e" k="14" />
<hkern u1="E" u2="d" k="14" />
<hkern u1="E" u2="c" k="14" />
<hkern u1="E" u2="a" k="14" />
<hkern u1="E" u2="T" k="-8" />
<hkern u1="E" u2="&#x40;" k="11" />
<hkern u1="F" u2="&#x2026;" k="50" />
<hkern u1="F" u2="&#x201e;" k="50" />
<hkern u1="F" u2="&#x201a;" k="50" />
<hkern u1="F" u2="&#x2014;" k="28" />
<hkern u1="F" u2="&#x2013;" k="28" />
<hkern u1="F" u2="&#x178;" k="-14" />
<hkern u1="F" u2="&#x153;" k="28" />
<hkern u1="F" u2="&#xe7;" k="28" />
<hkern u1="F" u2="&#xe6;" k="28" />
<hkern u1="F" u2="&#xdd;" k="-14" />
<hkern u1="F" u2="&#xc6;" k="22" />
<hkern u1="F" u2="&#xc5;" k="22" />
<hkern u1="F" u2="&#xc4;" k="22" />
<hkern u1="F" u2="&#xc3;" k="22" />
<hkern u1="F" u2="&#xc2;" k="22" />
<hkern u1="F" u2="&#xc1;" k="22" />
<hkern u1="F" u2="&#xc0;" k="22" />
<hkern u1="F" u2="&#xae;" k="11" />
<hkern u1="F" u2="&#xa9;" k="11" />
<hkern u1="F" u2="z" k="22" />
<hkern u1="F" u2="y" k="11" />
<hkern u1="F" u2="x" k="22" />
<hkern u1="F" u2="w" k="11" />
<hkern u1="F" u2="v" k="11" />
<hkern u1="F" u2="u" k="22" />
<hkern u1="F" u2="t" k="11" />
<hkern u1="F" u2="s" k="22" />
<hkern u1="F" u2="r" k="22" />
<hkern u1="F" u2="q" k="28" />
<hkern u1="F" u2="p" k="22" />
<hkern u1="F" u2="o" k="28" />
<hkern u1="F" u2="n" k="22" />
<hkern u1="F" u2="m" k="22" />
<hkern u1="F" u2="l" k="6" />
<hkern u1="F" u2="k" k="6" />
<hkern u1="F" u2="j" k="6" />
<hkern u1="F" u2="i" k="6" />
<hkern u1="F" u2="h" k="6" />
<hkern u1="F" u2="g" k="17" />
<hkern u1="F" u2="f" k="11" />
<hkern u1="F" u2="e" k="28" />
<hkern u1="F" u2="d" k="28" />
<hkern u1="F" u2="c" k="28" />
<hkern u1="F" u2="b" k="6" />
<hkern u1="F" u2="a" k="28" />
<hkern u1="F" u2="Y" k="-14" />
<hkern u1="F" u2="X" k="-6" />
<hkern u1="F" u2="W" k="-3" />
<hkern u1="F" u2="V" k="-11" />
<hkern u1="F" u2="T" k="-17" />
<hkern u1="F" u2="J" k="50" />
<hkern u1="F" u2="A" k="22" />
<hkern u1="F" u2="&#x40;" k="11" />
<hkern u1="F" u2="&#x3b;" k="17" />
<hkern u1="F" u2="&#x3a;" k="17" />
<hkern u1="F" u2="&#x2f;" k="44" />
<hkern u1="F" u2="&#x2e;" k="50" />
<hkern u1="F" u2="&#x2d;" k="28" />
<hkern u1="F" u2="&#x2c;" k="50" />
<hkern u1="F" u2="&#x26;" k="22" />
<hkern u1="G" u2="&#x2122;" k="17" />
<hkern u1="G" u2="&#x2026;" k="-11" />
<hkern u1="G" u2="&#x201e;" k="-11" />
<hkern u1="G" u2="&#x201a;" k="-11" />
<hkern u1="G" u2="&#x178;" k="8" />
<hkern u1="G" u2="&#xdd;" k="8" />
<hkern u1="G" u2="y" k="6" />
<hkern u1="G" u2="v" k="6" />
<hkern u1="G" u2="t" k="6" />
<hkern u1="G" u2="Y" k="8" />
<hkern u1="G" u2="W" k="8" />
<hkern u1="G" u2="V" k="6" />
<hkern u1="G" u2="T" k="14" />
<hkern u1="G" u2="&#x2e;" k="-11" />
<hkern u1="G" u2="&#x2c;" k="-11" />
<hkern u1="H" u2="y" k="-11" />
<hkern u1="H" u2="v" k="-11" />
<hkern u1="H" u2="&#x2f;" k="11" />
<hkern u1="I" u2="y" k="-11" />
<hkern u1="I" u2="v" k="-11" />
<hkern u1="I" u2="&#x2f;" k="11" />
<hkern u1="J" u2="&#x2026;" k="6" />
<hkern u1="J" u2="&#x201e;" k="6" />
<hkern u1="J" u2="&#x201a;" k="6" />
<hkern u1="J" u2="&#xc6;" k="-11" />
<hkern u1="J" u2="&#xc5;" k="-11" />
<hkern u1="J" u2="&#xc4;" k="-11" />
<hkern u1="J" u2="&#xc3;" k="-11" />
<hkern u1="J" u2="&#xc2;" k="-11" />
<hkern u1="J" u2="&#xc1;" k="-11" />
<hkern u1="J" u2="&#xc0;" k="-11" />
<hkern u1="J" u2="y" k="-11" />
<hkern u1="J" u2="v" k="-11" />
<hkern u1="J" u2="J" k="6" />
<hkern u1="J" u2="A" k="-11" />
<hkern u1="J" u2="&#x2e;" k="6" />
<hkern u1="J" u2="&#x2c;" k="6" />
<hkern u1="K" u2="&#x2014;" k="22" />
<hkern u1="K" u2="&#x2013;" k="22" />
<hkern u1="K" u2="&#x178;" k="-8" />
<hkern u1="K" u2="&#x153;" k="11" />
<hkern u1="K" u2="&#x152;" k="17" />
<hkern u1="K" u2="&#xf0;" k="11" />
<hkern u1="K" u2="&#xe7;" k="11" />
<hkern u1="K" u2="&#xe6;" k="11" />
<hkern u1="K" u2="&#xdd;" k="-8" />
<hkern u1="K" u2="&#xdc;" k="6" />
<hkern u1="K" u2="&#xdb;" k="6" />
<hkern u1="K" u2="&#xda;" k="6" />
<hkern u1="K" u2="&#xd9;" k="6" />
<hkern u1="K" u2="&#xd8;" k="17" />
<hkern u1="K" u2="&#xd6;" k="17" />
<hkern u1="K" u2="&#xd5;" k="17" />
<hkern u1="K" u2="&#xd4;" k="17" />
<hkern u1="K" u2="&#xd3;" k="17" />
<hkern u1="K" u2="&#xd2;" k="17" />
<hkern u1="K" u2="&#xae;" k="17" />
<hkern u1="K" u2="&#xa9;" k="17" />
<hkern u1="K" u2="y" k="11" />
<hkern u1="K" u2="v" k="11" />
<hkern u1="K" u2="t" k="6" />
<hkern u1="K" u2="q" k="11" />
<hkern u1="K" u2="o" k="11" />
<hkern u1="K" u2="g" k="6" />
<hkern u1="K" u2="f" k="3" />
<hkern u1="K" u2="e" k="11" />
<hkern u1="K" u2="d" k="11" />
<hkern u1="K" u2="c" k="11" />
<hkern u1="K" u2="a" k="11" />
<hkern u1="K" u2="Z" k="3" />
<hkern u1="K" u2="Y" k="-8" />
<hkern u1="K" u2="X" k="-6" />
<hkern u1="K" u2="W" k="6" />
<hkern u1="K" u2="V" k="-8" />
<hkern u1="K" u2="U" k="6" />
<hkern u1="K" u2="T" k="-3" />
<hkern u1="K" u2="S" k="3" />
<hkern u1="K" u2="Q" k="17" />
<hkern u1="K" u2="O" k="17" />
<hkern u1="K" u2="G" k="17" />
<hkern u1="K" u2="C" k="17" />
<hkern u1="K" u2="&#x40;" k="17" />
<hkern u1="K" u2="&#x2d;" k="22" />
<hkern u1="K" u2="&#x26;" k="11" />
<hkern u1="L" u2="&#x2122;" k="39" />
<hkern u1="L" u2="&#x201d;" k="33" />
<hkern u1="L" u2="&#x201c;" k="55" />
<hkern u1="L" u2="&#x2019;" k="33" />
<hkern u1="L" u2="&#x2018;" k="55" />
<hkern u1="L" u2="&#x178;" k="44" />
<hkern u1="L" u2="&#x153;" k="11" />
<hkern u1="L" u2="&#x152;" k="28" />
<hkern u1="L" u2="&#xe7;" k="11" />
<hkern u1="L" u2="&#xe6;" k="11" />
<hkern u1="L" u2="&#xdd;" k="44" />
<hkern u1="L" u2="&#xdc;" k="8" />
<hkern u1="L" u2="&#xdb;" k="8" />
<hkern u1="L" u2="&#xda;" k="8" />
<hkern u1="L" u2="&#xd9;" k="8" />
<hkern u1="L" u2="&#xd8;" k="28" />
<hkern u1="L" u2="&#xd6;" k="28" />
<hkern u1="L" u2="&#xd5;" k="28" />
<hkern u1="L" u2="&#xd4;" k="28" />
<hkern u1="L" u2="&#xd3;" k="28" />
<hkern u1="L" u2="&#xd2;" k="28" />
<hkern u1="L" u2="&#xc6;" k="-11" />
<hkern u1="L" u2="&#xc5;" k="-11" />
<hkern u1="L" u2="&#xc4;" k="-11" />
<hkern u1="L" u2="&#xc3;" k="-11" />
<hkern u1="L" u2="&#xc2;" k="-11" />
<hkern u1="L" u2="&#xc1;" k="-11" />
<hkern u1="L" u2="&#xc0;" k="-11" />
<hkern u1="L" u2="&#xae;" k="28" />
<hkern u1="L" u2="&#xa9;" k="28" />
<hkern u1="L" u2="y" k="22" />
<hkern u1="L" u2="w" k="6" />
<hkern u1="L" u2="v" k="22" />
<hkern u1="L" u2="q" k="11" />
<hkern u1="L" u2="o" k="11" />
<hkern u1="L" u2="e" k="11" />
<hkern u1="L" u2="d" k="11" />
<hkern u1="L" u2="c" k="11" />
<hkern u1="L" u2="a" k="11" />
<hkern u1="L" u2="\" k="28" />
<hkern u1="L" u2="Y" k="124" />
<hkern u1="L" u2="W" k="120" />
<hkern u1="L" u2="V" k="150" />
<hkern u1="L" u2="U" k="8" />
<hkern u1="L" u2="T" k="150" />
<hkern u1="L" u2="Q" k="28" />
<hkern u1="L" u2="O" k="28" />
<hkern u1="L" u2="G" k="28" />
<hkern u1="L" u2="C" k="28" />
<hkern u1="L" u2="A" k="-11" />
<hkern u1="L" u2="&#x40;" k="28" />
<hkern u1="M" u2="y" k="-11" />
<hkern u1="M" u2="v" k="-11" />
<hkern u1="M" u2="&#x2f;" k="11" />
<hkern u1="N" u2="y" k="-11" />
<hkern u1="N" u2="v" k="-11" />
<hkern u1="N" u2="&#x2f;" k="11" />
<hkern u1="O" u2="&#x2026;" k="22" />
<hkern u1="O" u2="&#x201e;" k="22" />
<hkern u1="O" u2="&#x201c;" k="11" />
<hkern u1="O" u2="&#x201a;" k="22" />
<hkern u1="O" u2="&#x2018;" k="11" />
<hkern u1="O" u2="&#x178;" k="25" />
<hkern u1="O" u2="&#x153;" k="3" />
<hkern u1="O" u2="&#xe7;" k="3" />
<hkern u1="O" u2="&#xe6;" k="3" />
<hkern u1="O" u2="&#xdd;" k="25" />
<hkern u1="O" u2="z" k="6" />
<hkern u1="O" u2="x" k="6" />
<hkern u1="O" u2="u" k="3" />
<hkern u1="O" u2="r" k="3" />
<hkern u1="O" u2="q" k="3" />
<hkern u1="O" u2="p" k="3" />
<hkern u1="O" u2="o" k="3" />
<hkern u1="O" u2="n" k="3" />
<hkern u1="O" u2="m" k="3" />
<hkern u1="O" u2="l" k="6" />
<hkern u1="O" u2="k" k="6" />
<hkern u1="O" u2="h" k="6" />
<hkern u1="O" u2="e" k="3" />
<hkern u1="O" u2="d" k="3" />
<hkern u1="O" u2="c" k="3" />
<hkern u1="O" u2="b" k="6" />
<hkern u1="O" u2="a" k="3" />
<hkern u1="O" u2="Z" k="25" />
<hkern u1="O" u2="Y" k="25" />
<hkern u1="O" u2="X" k="19" />
<hkern u1="O" u2="W" k="33" />
<hkern u1="O" u2="V" k="22" />
<hkern u1="O" u2="T" k="28" />
<hkern u1="O" u2="J" k="25" />
<hkern u1="O" u2="&#x3f;" k="11" />
<hkern u1="O" u2="&#x2f;" k="28" />
<hkern u1="O" u2="&#x2e;" k="22" />
<hkern u1="O" u2="&#x2c;" k="22" />
<hkern u1="P" u2="&#x2026;" k="33" />
<hkern u1="P" u2="&#x201e;" k="33" />
<hkern u1="P" u2="&#x201a;" k="33" />
<hkern u1="P" u2="&#x178;" k="3" />
<hkern u1="P" u2="&#x153;" k="6" />
<hkern u1="P" u2="&#xe7;" k="6" />
<hkern u1="P" u2="&#xe6;" k="6" />
<hkern u1="P" u2="&#xdd;" k="3" />
<hkern u1="P" u2="&#xc6;" k="19" />
<hkern u1="P" u2="&#xc5;" k="19" />
<hkern u1="P" u2="&#xc4;" k="19" />
<hkern u1="P" u2="&#xc3;" k="19" />
<hkern u1="P" u2="&#xc2;" k="19" />
<hkern u1="P" u2="&#xc1;" k="19" />
<hkern u1="P" u2="&#xc0;" k="19" />
<hkern u1="P" u2="y" k="-8" />
<hkern u1="P" u2="x" k="-3" />
<hkern u1="P" u2="w" k="-6" />
<hkern u1="P" u2="v" k="-8" />
<hkern u1="P" u2="t" k="-6" />
<hkern u1="P" u2="q" k="6" />
<hkern u1="P" u2="o" k="6" />
<hkern u1="P" u2="f" k="-6" />
<hkern u1="P" u2="e" k="6" />
<hkern u1="P" u2="d" k="6" />
<hkern u1="P" u2="c" k="6" />
<hkern u1="P" u2="a" k="6" />
<hkern u1="P" u2="Z" k="14" />
<hkern u1="P" u2="Y" k="3" />
<hkern u1="P" u2="X" k="-3" />
<hkern u1="P" u2="W" k="8" />
<hkern u1="P" u2="V" k="-3" />
<hkern u1="P" u2="J" k="47" />
<hkern u1="P" u2="A" k="19" />
<hkern u1="P" u2="&#x2e;" k="33" />
<hkern u1="P" u2="&#x2c;" k="33" />
<hkern u1="P" u2="&#x26;" k="11" />
<hkern u1="Q" u2="&#x2026;" k="22" />
<hkern u1="Q" u2="&#x201e;" k="22" />
<hkern u1="Q" u2="&#x201c;" k="11" />
<hkern u1="Q" u2="&#x201a;" k="22" />
<hkern u1="Q" u2="&#x2018;" k="11" />
<hkern u1="Q" u2="&#x178;" k="25" />
<hkern u1="Q" u2="&#x153;" k="3" />
<hkern u1="Q" u2="&#xe7;" k="3" />
<hkern u1="Q" u2="&#xe6;" k="3" />
<hkern u1="Q" u2="&#xdd;" k="25" />
<hkern u1="Q" u2="z" k="6" />
<hkern u1="Q" u2="x" k="6" />
<hkern u1="Q" u2="u" k="3" />
<hkern u1="Q" u2="r" k="3" />
<hkern u1="Q" u2="q" k="3" />
<hkern u1="Q" u2="p" k="3" />
<hkern u1="Q" u2="o" k="3" />
<hkern u1="Q" u2="n" k="3" />
<hkern u1="Q" u2="m" k="3" />
<hkern u1="Q" u2="l" k="6" />
<hkern u1="Q" u2="k" k="6" />
<hkern u1="Q" u2="h" k="6" />
<hkern u1="Q" u2="e" k="3" />
<hkern u1="Q" u2="d" k="3" />
<hkern u1="Q" u2="c" k="3" />
<hkern u1="Q" u2="b" k="6" />
<hkern u1="Q" u2="a" k="3" />
<hkern u1="Q" u2="Z" k="25" />
<hkern u1="Q" u2="Y" k="25" />
<hkern u1="Q" u2="X" k="19" />
<hkern u1="Q" u2="W" k="33" />
<hkern u1="Q" u2="V" k="22" />
<hkern u1="Q" u2="T" k="28" />
<hkern u1="Q" u2="J" k="25" />
<hkern u1="Q" u2="&#x3f;" k="11" />
<hkern u1="Q" u2="&#x2f;" k="17" />
<hkern u1="Q" u2="&#x2e;" k="22" />
<hkern u1="Q" u2="&#x2c;" k="22" />
<hkern u1="R" u2="&#x178;" k="-8" />
<hkern u1="R" u2="&#xdd;" k="-8" />
<hkern u1="R" u2="&#xc6;" k="-11" />
<hkern u1="R" u2="&#xc5;" k="-11" />
<hkern u1="R" u2="&#xc4;" k="-11" />
<hkern u1="R" u2="&#xc3;" k="-11" />
<hkern u1="R" u2="&#xc2;" k="-11" />
<hkern u1="R" u2="&#xc1;" k="-11" />
<hkern u1="R" u2="&#xc0;" k="-11" />
<hkern u1="R" u2="y" k="-17" />
<hkern u1="R" u2="w" k="-6" />
<hkern u1="R" u2="v" k="-17" />
<hkern u1="R" u2="Y" k="-8" />
<hkern u1="R" u2="X" k="-6" />
<hkern u1="R" u2="V" k="-11" />
<hkern u1="R" u2="T" k="6" />
<hkern u1="R" u2="J" k="6" />
<hkern u1="R" u2="A" k="-11" />
<hkern u1="R" u2="&#x26;" k="-6" />
<hkern u1="S" u2="&#x178;" k="6" />
<hkern u1="S" u2="&#xdd;" k="6" />
<hkern u1="S" u2="&#xc6;" k="-3" />
<hkern u1="S" u2="&#xc5;" k="-3" />
<hkern u1="S" u2="&#xc4;" k="-3" />
<hkern u1="S" u2="&#xc3;" k="-3" />
<hkern u1="S" u2="&#xc2;" k="-3" />
<hkern u1="S" u2="&#xc1;" k="-3" />
<hkern u1="S" u2="&#xc0;" k="-3" />
<hkern u1="S" u2="Y" k="6" />
<hkern u1="S" u2="X" k="-3" />
<hkern u1="S" u2="W" k="11" />
<hkern u1="S" u2="V" k="6" />
<hkern u1="S" u2="A" k="-3" />
<hkern u1="T" u2="&#x203a;" k="22" />
<hkern u1="T" u2="&#x2039;" k="61" />
<hkern u1="T" u2="&#x2026;" k="33" />
<hkern u1="T" u2="&#x201e;" k="33" />
<hkern u1="T" u2="&#x201a;" k="33" />
<hkern u1="T" u2="&#x2014;" k="39" />
<hkern u1="T" u2="&#x2013;" k="39" />
<hkern u1="T" u2="&#x178;" k="-11" />
<hkern u1="T" u2="&#x153;" k="52" />
<hkern u1="T" u2="&#x152;" k="6" />
<hkern u1="T" u2="&#xe7;" k="52" />
<hkern u1="T" u2="&#xe6;" k="52" />
<hkern u1="T" u2="&#xdd;" k="-11" />
<hkern u1="T" u2="&#xd8;" k="6" />
<hkern u1="T" u2="&#xd6;" k="6" />
<hkern u1="T" u2="&#xd5;" k="6" />
<hkern u1="T" u2="&#xd4;" k="6" />
<hkern u1="T" u2="&#xd3;" k="6" />
<hkern u1="T" u2="&#xd2;" k="6" />
<hkern u1="T" u2="&#xc6;" k="28" />
<hkern u1="T" u2="&#xc5;" k="28" />
<hkern u1="T" u2="&#xc4;" k="28" />
<hkern u1="T" u2="&#xc3;" k="28" />
<hkern u1="T" u2="&#xc2;" k="28" />
<hkern u1="T" u2="&#xc1;" k="28" />
<hkern u1="T" u2="&#xc0;" k="28" />
<hkern u1="T" u2="&#xbf;" k="28" />
<hkern u1="T" u2="&#xbb;" k="22" />
<hkern u1="T" u2="&#xae;" k="17" />
<hkern u1="T" u2="&#xab;" k="61" />
<hkern u1="T" u2="&#xa9;" k="17" />
<hkern u1="T" u2="z" k="44" />
<hkern u1="T" u2="y" k="39" />
<hkern u1="T" u2="x" k="44" />
<hkern u1="T" u2="w" k="33" />
<hkern u1="T" u2="v" k="39" />
<hkern u1="T" u2="u" k="44" />
<hkern u1="T" u2="t" k="11" />
<hkern u1="T" u2="s" k="55" />
<hkern u1="T" u2="r" k="44" />
<hkern u1="T" u2="q" k="52" />
<hkern u1="T" u2="p" k="44" />
<hkern u1="T" u2="o" k="52" />
<hkern u1="T" u2="n" k="44" />
<hkern u1="T" u2="m" k="44" />
<hkern u1="T" u2="g" k="52" />
<hkern u1="T" u2="f" k="6" />
<hkern u1="T" u2="e" k="52" />
<hkern u1="T" u2="d" k="52" />
<hkern u1="T" u2="c" k="52" />
<hkern u1="T" u2="a" k="52" />
<hkern u1="T" u2="\" k="-6" />
<hkern u1="T" u2="Z" k="6" />
<hkern u1="T" u2="Y" k="-11" />
<hkern u1="T" u2="X" k="-6" />
<hkern u1="T" u2="V" k="-11" />
<hkern u1="T" u2="T" k="-6" />
<hkern u1="T" u2="Q" k="6" />
<hkern u1="T" u2="O" k="6" />
<hkern u1="T" u2="J" k="47" />
<hkern u1="T" u2="G" k="6" />
<hkern u1="T" u2="C" k="6" />
<hkern u1="T" u2="A" k="75" />
<hkern u1="T" u2="&#x40;" k="17" />
<hkern u1="T" u2="&#x3b;" k="28" />
<hkern u1="T" u2="&#x3a;" k="28" />
<hkern u1="T" u2="&#x2e;" k="33" />
<hkern u1="T" u2="&#x2d;" k="39" />
<hkern u1="T" u2="&#x2c;" k="33" />
<hkern u1="T" u2="&#x26;" k="22" />
<hkern u1="U" u2="&#x2026;" k="6" />
<hkern u1="U" u2="&#x201e;" k="6" />
<hkern u1="U" u2="&#x201a;" k="6" />
<hkern u1="U" u2="&#xc6;" k="-11" />
<hkern u1="U" u2="&#xc5;" k="-11" />
<hkern u1="U" u2="&#xc4;" k="-11" />
<hkern u1="U" u2="&#xc3;" k="-11" />
<hkern u1="U" u2="&#xc2;" k="-11" />
<hkern u1="U" u2="&#xc1;" k="-11" />
<hkern u1="U" u2="&#xc0;" k="-11" />
<hkern u1="U" u2="y" k="-11" />
<hkern u1="U" u2="v" k="-11" />
<hkern u1="U" u2="J" k="6" />
<hkern u1="U" u2="A" k="-11" />
<hkern u1="U" u2="&#x2e;" k="6" />
<hkern u1="U" u2="&#x2c;" k="6" />
<hkern u1="V" u2="&#x203a;" k="11" />
<hkern u1="V" u2="&#x2039;" k="28" />
<hkern u1="V" u2="&#x2026;" k="33" />
<hkern u1="V" u2="&#x201e;" k="33" />
<hkern u1="V" u2="&#x201a;" k="33" />
<hkern u1="V" u2="&#x2014;" k="33" />
<hkern u1="V" u2="&#x2013;" k="33" />
<hkern u1="V" u2="&#x178;" k="-17" />
<hkern u1="V" u2="&#x153;" k="22" />
<hkern u1="V" u2="&#xe7;" k="22" />
<hkern u1="V" u2="&#xe6;" k="22" />
<hkern u1="V" u2="&#xdd;" k="-17" />
<hkern u1="V" u2="&#xc6;" k="17" />
<hkern u1="V" u2="&#xc5;" k="17" />
<hkern u1="V" u2="&#xc4;" k="17" />
<hkern u1="V" u2="&#xc3;" k="17" />
<hkern u1="V" u2="&#xc2;" k="17" />
<hkern u1="V" u2="&#xc1;" k="17" />
<hkern u1="V" u2="&#xc0;" k="17" />
<hkern u1="V" u2="&#xbb;" k="11" />
<hkern u1="V" u2="&#xab;" k="28" />
<hkern u1="V" u2="&#x7d;" k="-17" />
<hkern u1="V" u2="y" k="6" />
<hkern u1="V" u2="x" k="14" />
<hkern u1="V" u2="w" k="6" />
<hkern u1="V" u2="v" k="6" />
<hkern u1="V" u2="u" k="17" />
<hkern u1="V" u2="t" k="6" />
<hkern u1="V" u2="s" k="22" />
<hkern u1="V" u2="r" k="17" />
<hkern u1="V" u2="q" k="22" />
<hkern u1="V" u2="p" k="17" />
<hkern u1="V" u2="o" k="22" />
<hkern u1="V" u2="n" k="17" />
<hkern u1="V" u2="m" k="17" />
<hkern u1="V" u2="l" k="6" />
<hkern u1="V" u2="k" k="6" />
<hkern u1="V" u2="h" k="6" />
<hkern u1="V" u2="g" k="19" />
<hkern u1="V" u2="f" k="6" />
<hkern u1="V" u2="e" k="22" />
<hkern u1="V" u2="d" k="22" />
<hkern u1="V" u2="c" k="22" />
<hkern u1="V" u2="b" k="6" />
<hkern u1="V" u2="a" k="22" />
<hkern u1="V" u2="]" k="-17" />
<hkern u1="V" u2="Y" k="-17" />
<hkern u1="V" u2="X" k="-11" />
<hkern u1="V" u2="W" k="3" />
<hkern u1="V" u2="V" k="-11" />
<hkern u1="V" u2="T" k="-11" />
<hkern u1="V" u2="S" k="-17" />
<hkern u1="V" u2="J" k="28" />
<hkern u1="V" u2="A" k="45" />
<hkern u1="V" u2="&#x3b;" k="11" />
<hkern u1="V" u2="&#x3a;" k="11" />
<hkern u1="V" u2="&#x2e;" k="33" />
<hkern u1="V" u2="&#x2d;" k="33" />
<hkern u1="V" u2="&#x2c;" k="33" />
<hkern u1="V" u2="&#x29;" k="-17" />
<hkern u1="V" u2="&#x26;" k="22" />
<hkern u1="W" u2="&#x203a;" k="11" />
<hkern u1="W" u2="&#x2039;" k="22" />
<hkern u1="W" u2="&#x2026;" k="11" />
<hkern u1="W" u2="&#x201e;" k="11" />
<hkern u1="W" u2="&#x201a;" k="11" />
<hkern u1="W" u2="&#x2014;" k="22" />
<hkern u1="W" u2="&#x2013;" k="22" />
<hkern u1="W" u2="&#x153;" k="25" />
<hkern u1="W" u2="&#x152;" k="11" />
<hkern u1="W" u2="&#xe7;" k="25" />
<hkern u1="W" u2="&#xe6;" k="25" />
<hkern u1="W" u2="&#xd8;" k="11" />
<hkern u1="W" u2="&#xd6;" k="11" />
<hkern u1="W" u2="&#xd5;" k="11" />
<hkern u1="W" u2="&#xd4;" k="11" />
<hkern u1="W" u2="&#xd3;" k="11" />
<hkern u1="W" u2="&#xd2;" k="11" />
<hkern u1="W" u2="&#xc6;" k="60" />
<hkern u1="W" u2="&#xc5;" k="17" />
<hkern u1="W" u2="&#xc4;" k="17" />
<hkern u1="W" u2="&#xc3;" k="17" />
<hkern u1="W" u2="&#xc2;" k="17" />
<hkern u1="W" u2="&#xc1;" k="17" />
<hkern u1="W" u2="&#xc0;" k="17" />
<hkern u1="W" u2="&#xbb;" k="11" />
<hkern u1="W" u2="&#xae;" k="11" />
<hkern u1="W" u2="&#xab;" k="22" />
<hkern u1="W" u2="&#xa9;" k="11" />
<hkern u1="W" u2="&#x7d;" k="-6" />
<hkern u1="W" u2="z" k="17" />
<hkern u1="W" u2="y" k="17" />
<hkern u1="W" u2="x" k="17" />
<hkern u1="W" u2="w" k="17" />
<hkern u1="W" u2="v" k="17" />
<hkern u1="W" u2="u" k="19" />
<hkern u1="W" u2="t" k="17" />
<hkern u1="W" u2="s" k="28" />
<hkern u1="W" u2="r" k="19" />
<hkern u1="W" u2="q" k="25" />
<hkern u1="W" u2="p" k="19" />
<hkern u1="W" u2="o" k="25" />
<hkern u1="W" u2="n" k="19" />
<hkern u1="W" u2="m" k="19" />
<hkern u1="W" u2="l" k="6" />
<hkern u1="W" u2="k" k="6" />
<hkern u1="W" u2="j" k="11" />
<hkern u1="W" u2="i" k="11" />
<hkern u1="W" u2="h" k="6" />
<hkern u1="W" u2="g" k="28" />
<hkern u1="W" u2="f" k="8" />
<hkern u1="W" u2="e" k="25" />
<hkern u1="W" u2="d" k="25" />
<hkern u1="W" u2="c" k="25" />
<hkern u1="W" u2="b" k="6" />
<hkern u1="W" u2="a" k="25" />
<hkern u1="W" u2="]" k="-6" />
<hkern u1="W" u2="X" k="6" />
<hkern u1="W" u2="V" k="3" />
<hkern u1="W" u2="S" k="-11" />
<hkern u1="W" u2="Q" k="11" />
<hkern u1="W" u2="O" k="11" />
<hkern u1="W" u2="J" k="22" />
<hkern u1="W" u2="G" k="11" />
<hkern u1="W" u2="C" k="11" />
<hkern u1="W" u2="A" k="40" />
<hkern u1="W" u2="&#x40;" k="11" />
<hkern u1="W" u2="&#x3b;" k="11" />
<hkern u1="W" u2="&#x3a;" k="11" />
<hkern u1="W" u2="&#x2e;" k="11" />
<hkern u1="W" u2="&#x2d;" k="22" />
<hkern u1="W" u2="&#x2c;" k="11" />
<hkern u1="W" u2="&#x29;" k="-6" />
<hkern u1="W" u2="&#x26;" k="17" />
<hkern u1="X" u2="&#x2014;" k="17" />
<hkern u1="X" u2="&#x2013;" k="17" />
<hkern u1="X" u2="&#x178;" k="-11" />
<hkern u1="X" u2="&#x153;" k="6" />
<hkern u1="X" u2="&#x152;" k="8" />
<hkern u1="X" u2="&#xe7;" k="6" />
<hkern u1="X" u2="&#xe6;" k="6" />
<hkern u1="X" u2="&#xdd;" k="-11" />
<hkern u1="X" u2="&#xd8;" k="8" />
<hkern u1="X" u2="&#xd6;" k="8" />
<hkern u1="X" u2="&#xd5;" k="8" />
<hkern u1="X" u2="&#xd4;" k="8" />
<hkern u1="X" u2="&#xd3;" k="8" />
<hkern u1="X" u2="&#xd2;" k="8" />
<hkern u1="X" u2="&#xc6;" k="-11" />
<hkern u1="X" u2="&#xc5;" k="-11" />
<hkern u1="X" u2="&#xc4;" k="-11" />
<hkern u1="X" u2="&#xc3;" k="-11" />
<hkern u1="X" u2="&#xc2;" k="-11" />
<hkern u1="X" u2="&#xc1;" k="-11" />
<hkern u1="X" u2="&#xc0;" k="-11" />
<hkern u1="X" u2="&#x7d;" k="-22" />
<hkern u1="X" u2="y" k="-6" />
<hkern u1="X" u2="w" k="6" />
<hkern u1="X" u2="v" k="-6" />
<hkern u1="X" u2="u" k="6" />
<hkern u1="X" u2="t" k="6" />
<hkern u1="X" u2="r" k="6" />
<hkern u1="X" u2="q" k="6" />
<hkern u1="X" u2="p" k="6" />
<hkern u1="X" u2="o" k="6" />
<hkern u1="X" u2="n" k="6" />
<hkern u1="X" u2="m" k="6" />
<hkern u1="X" u2="f" k="6" />
<hkern u1="X" u2="e" k="6" />
<hkern u1="X" u2="d" k="6" />
<hkern u1="X" u2="c" k="6" />
<hkern u1="X" u2="a" k="6" />
<hkern u1="X" u2="]" k="-22" />
<hkern u1="X" u2="Y" k="-11" />
<hkern u1="X" u2="X" k="-6" />
<hkern u1="X" u2="W" k="6" />
<hkern u1="X" u2="V" k="-11" />
<hkern u1="X" u2="T" k="-6" />
<hkern u1="X" u2="S" k="-3" />
<hkern u1="X" u2="Q" k="8" />
<hkern u1="X" u2="O" k="8" />
<hkern u1="X" u2="G" k="8" />
<hkern u1="X" u2="C" k="8" />
<hkern u1="X" u2="A" k="-11" />
<hkern u1="X" u2="&#x2d;" k="17" />
<hkern u1="X" u2="&#x29;" k="-22" />
<hkern u1="X" u2="&#x26;" k="11" />
<hkern u1="Y" u2="&#x203a;" k="22" />
<hkern u1="Y" u2="&#x2039;" k="33" />
<hkern u1="Y" u2="&#x2026;" k="28" />
<hkern u1="Y" u2="&#x201e;" k="28" />
<hkern u1="Y" u2="&#x201a;" k="28" />
<hkern u1="Y" u2="&#x2014;" k="28" />
<hkern u1="Y" u2="&#x2013;" k="28" />
<hkern u1="Y" u2="&#x153;" k="30" />
<hkern u1="Y" u2="&#x152;" k="3" />
<hkern u1="Y" u2="&#xe7;" k="30" />
<hkern u1="Y" u2="&#xe6;" k="30" />
<hkern u1="Y" u2="&#xd8;" k="3" />
<hkern u1="Y" u2="&#xd6;" k="3" />
<hkern u1="Y" u2="&#xd5;" k="3" />
<hkern u1="Y" u2="&#xd4;" k="3" />
<hkern u1="Y" u2="&#xd3;" k="3" />
<hkern u1="Y" u2="&#xd2;" k="3" />
<hkern u1="Y" u2="&#xc6;" k="17" />
<hkern u1="Y" u2="&#xc5;" k="17" />
<hkern u1="Y" u2="&#xc4;" k="17" />
<hkern u1="Y" u2="&#xc3;" k="17" />
<hkern u1="Y" u2="&#xc2;" k="17" />
<hkern u1="Y" u2="&#xc1;" k="17" />
<hkern u1="Y" u2="&#xc0;" k="17" />
<hkern u1="Y" u2="&#xbb;" k="22" />
<hkern u1="Y" u2="&#xae;" k="17" />
<hkern u1="Y" u2="&#xab;" k="33" />
<hkern u1="Y" u2="&#xa9;" k="17" />
<hkern u1="Y" u2="&#x7d;" k="-28" />
<hkern u1="Y" u2="z" k="17" />
<hkern u1="Y" u2="y" k="11" />
<hkern u1="Y" u2="x" k="11" />
<hkern u1="Y" u2="w" k="6" />
<hkern u1="Y" u2="v" k="11" />
<hkern u1="Y" u2="u" k="22" />
<hkern u1="Y" u2="t" k="11" />
<hkern u1="Y" u2="s" k="28" />
<hkern u1="Y" u2="r" k="22" />
<hkern u1="Y" u2="q" k="30" />
<hkern u1="Y" u2="p" k="22" />
<hkern u1="Y" u2="o" k="30" />
<hkern u1="Y" u2="n" k="22" />
<hkern u1="Y" u2="m" k="22" />
<hkern u1="Y" u2="g" k="22" />
<hkern u1="Y" u2="f" k="6" />
<hkern u1="Y" u2="e" k="30" />
<hkern u1="Y" u2="d" k="30" />
<hkern u1="Y" u2="c" k="30" />
<hkern u1="Y" u2="a" k="30" />
<hkern u1="Y" u2="]" k="-28" />
<hkern u1="Y" u2="X" k="-11" />
<hkern u1="Y" u2="V" k="-17" />
<hkern u1="Y" u2="T" k="-11" />
<hkern u1="Y" u2="S" k="-17" />
<hkern u1="Y" u2="Q" k="3" />
<hkern u1="Y" u2="O" k="3" />
<hkern u1="Y" u2="J" k="49" />
<hkern u1="Y" u2="G" k="3" />
<hkern u1="Y" u2="C" k="3" />
<hkern u1="Y" u2="A" k="47" />
<hkern u1="Y" u2="&#x40;" k="17" />
<hkern u1="Y" u2="&#x3b;" k="17" />
<hkern u1="Y" u2="&#x3a;" k="17" />
<hkern u1="Y" u2="&#x2e;" k="28" />
<hkern u1="Y" u2="&#x2d;" k="28" />
<hkern u1="Y" u2="&#x2c;" k="28" />
<hkern u1="Y" u2="&#x29;" k="-28" />
<hkern u1="Y" u2="&#x26;" k="17" />
<hkern u1="Z" u2="&#x2014;" k="28" />
<hkern u1="Z" u2="&#x2013;" k="28" />
<hkern u1="Z" u2="&#x153;" k="22" />
<hkern u1="Z" u2="&#x152;" k="25" />
<hkern u1="Z" u2="&#xf0;" k="17" />
<hkern u1="Z" u2="&#xe7;" k="22" />
<hkern u1="Z" u2="&#xe6;" k="22" />
<hkern u1="Z" u2="&#xd8;" k="25" />
<hkern u1="Z" u2="&#xd6;" k="25" />
<hkern u1="Z" u2="&#xd5;" k="25" />
<hkern u1="Z" u2="&#xd4;" k="25" />
<hkern u1="Z" u2="&#xd3;" k="25" />
<hkern u1="Z" u2="&#xd2;" k="25" />
<hkern u1="Z" u2="&#xc6;" k="6" />
<hkern u1="Z" u2="&#xc5;" k="6" />
<hkern u1="Z" u2="&#xc4;" k="6" />
<hkern u1="Z" u2="&#xc3;" k="6" />
<hkern u1="Z" u2="&#xc2;" k="6" />
<hkern u1="Z" u2="&#xc1;" k="6" />
<hkern u1="Z" u2="&#xc0;" k="6" />
<hkern u1="Z" u2="y" k="17" />
<hkern u1="Z" u2="w" k="6" />
<hkern u1="Z" u2="v" k="17" />
<hkern u1="Z" u2="u" k="11" />
<hkern u1="Z" u2="r" k="11" />
<hkern u1="Z" u2="q" k="22" />
<hkern u1="Z" u2="p" k="11" />
<hkern u1="Z" u2="o" k="22" />
<hkern u1="Z" u2="n" k="11" />
<hkern u1="Z" u2="m" k="11" />
<hkern u1="Z" u2="l" k="6" />
<hkern u1="Z" u2="k" k="6" />
<hkern u1="Z" u2="j" k="8" />
<hkern u1="Z" u2="i" k="11" />
<hkern u1="Z" u2="h" k="6" />
<hkern u1="Z" u2="g" k="11" />
<hkern u1="Z" u2="f" k="11" />
<hkern u1="Z" u2="e" k="22" />
<hkern u1="Z" u2="d" k="22" />
<hkern u1="Z" u2="c" k="22" />
<hkern u1="Z" u2="b" k="6" />
<hkern u1="Z" u2="a" k="22" />
<hkern u1="Z" u2="T" k="6" />
<hkern u1="Z" u2="Q" k="25" />
<hkern u1="Z" u2="O" k="25" />
<hkern u1="Z" u2="J" k="6" />
<hkern u1="Z" u2="G" k="25" />
<hkern u1="Z" u2="C" k="25" />
<hkern u1="Z" u2="A" k="6" />
<hkern u1="Z" u2="&#x2d;" k="28" />
<hkern u1="[" u2="&#x178;" k="-28" />
<hkern u1="[" u2="&#xdd;" k="-28" />
<hkern u1="[" u2="j" k="-83" />
<hkern u1="[" u2="g" k="-11" />
<hkern u1="[" u2="Y" k="-28" />
<hkern u1="[" u2="X" k="-22" />
<hkern u1="[" u2="W" k="-6" />
<hkern u1="[" u2="V" k="-17" />
<hkern u1="\" u2="&#x178;" k="33" />
<hkern u1="\" u2="&#x153;" k="6" />
<hkern u1="\" u2="&#xe7;" k="6" />
<hkern u1="\" u2="&#xe6;" k="6" />
<hkern u1="\" u2="&#xdd;" k="33" />
<hkern u1="\" u2="q" k="6" />
<hkern u1="\" u2="o" k="6" />
<hkern u1="\" u2="j" k="-72" />
<hkern u1="\" u2="e" k="6" />
<hkern u1="\" u2="d" k="6" />
<hkern u1="\" u2="c" k="6" />
<hkern u1="\" u2="a" k="6" />
<hkern u1="\" u2="Y" k="33" />
<hkern u1="\" u2="W" k="28" />
<hkern u1="\" u2="V" k="39" />
<hkern u1="\" u2="T" k="50" />
<hkern u1="a" u2="&#x2122;" k="11" />
<hkern u1="a" u2="&#x201c;" k="17" />
<hkern u1="a" u2="&#x2018;" k="17" />
<hkern u1="a" u2="y" k="3" />
<hkern u1="a" u2="v" k="3" />
<hkern u1="a" u2="t" k="6" />
<hkern u1="a" u2="\" k="6" />
<hkern u1="a" u2="&#x3f;" k="6" />
<hkern u1="b" u2="&#x2122;" k="17" />
<hkern u1="b" u2="&#x2026;" k="11" />
<hkern u1="b" u2="&#x201e;" k="11" />
<hkern u1="b" u2="&#x201c;" k="22" />
<hkern u1="b" u2="&#x201a;" k="11" />
<hkern u1="b" u2="&#x2018;" k="22" />
<hkern u1="b" u2="z" k="8" />
<hkern u1="b" u2="y" k="-3" />
<hkern u1="b" u2="x" k="3" />
<hkern u1="b" u2="v" k="-3" />
<hkern u1="b" u2="t" k="6" />
<hkern u1="b" u2="s" k="-3" />
<hkern u1="b" u2="\" k="6" />
<hkern u1="b" u2="&#x3f;" k="11" />
<hkern u1="b" u2="&#x3b;" k="6" />
<hkern u1="b" u2="&#x3a;" k="6" />
<hkern u1="b" u2="&#x2f;" k="6" />
<hkern u1="b" u2="&#x2e;" k="11" />
<hkern u1="b" u2="&#x2c;" k="11" />
<hkern u1="d" u2="y" k="-6" />
<hkern u1="d" u2="v" k="-6" />
<hkern u1="e" u2="y" k="6" />
<hkern u1="e" u2="x" k="8" />
<hkern u1="e" u2="v" k="6" />
<hkern u1="f" u2="&#x2122;" k="-22" />
<hkern u1="f" u2="&#x203a;" k="11" />
<hkern u1="f" u2="&#x2039;" k="28" />
<hkern u1="f" u2="&#x2026;" k="44" />
<hkern u1="f" u2="&#x201e;" k="44" />
<hkern u1="f" u2="&#x201d;" k="-28" />
<hkern u1="f" u2="&#x201c;" k="-17" />
<hkern u1="f" u2="&#x201a;" k="44" />
<hkern u1="f" u2="&#x2019;" k="-28" />
<hkern u1="f" u2="&#x2018;" k="-17" />
<hkern u1="f" u2="&#x2014;" k="17" />
<hkern u1="f" u2="&#x2013;" k="17" />
<hkern u1="f" u2="&#x153;" k="6" />
<hkern u1="f" u2="&#xf0;" k="11" />
<hkern u1="f" u2="&#xe7;" k="6" />
<hkern u1="f" u2="&#xe6;" k="6" />
<hkern u1="f" u2="&#xbb;" k="11" />
<hkern u1="f" u2="&#xab;" k="28" />
<hkern u1="f" u2="&#x7d;" k="-39" />
<hkern u1="f" u2="y" k="-11" />
<hkern u1="f" u2="v" k="-11" />
<hkern u1="f" u2="u" k="3" />
<hkern u1="f" u2="r" k="3" />
<hkern u1="f" u2="q" k="6" />
<hkern u1="f" u2="p" k="3" />
<hkern u1="f" u2="o" k="6" />
<hkern u1="f" u2="n" k="3" />
<hkern u1="f" u2="m" k="3" />
<hkern u1="f" u2="e" k="6" />
<hkern u1="f" u2="d" k="6" />
<hkern u1="f" u2="c" k="6" />
<hkern u1="f" u2="a" k="6" />
<hkern u1="f" u2="]" k="-39" />
<hkern u1="f" u2="\" k="-22" />
<hkern u1="f" u2="&#x3f;" k="-22" />
<hkern u1="f" u2="&#x3b;" k="6" />
<hkern u1="f" u2="&#x3a;" k="6" />
<hkern u1="f" u2="&#x2f;" k="28" />
<hkern u1="f" u2="&#x2e;" k="44" />
<hkern u1="f" u2="&#x2d;" k="17" />
<hkern u1="f" u2="&#x2c;" k="44" />
<hkern u1="f" u2="&#x2a;" k="-11" />
<hkern u1="f" u2="&#x29;" k="-39" />
<hkern u1="f" u2="&#x26;" k="17" />
<hkern u1="g" u2="&#x201d;" k="-22" />
<hkern u1="g" u2="&#x201c;" k="-11" />
<hkern u1="g" u2="&#x2019;" k="-22" />
<hkern u1="g" u2="&#x2018;" k="-11" />
<hkern u1="g" u2="&#x7d;" k="-11" />
<hkern u1="g" u2="t" k="-6" />
<hkern u1="g" u2="j" k="-28" />
<hkern u1="g" u2="g" k="-6" />
<hkern u1="g" u2="]" k="-11" />
<hkern u1="g" u2="&#x3b;" k="-11" />
<hkern u1="g" u2="&#x2f;" k="-22" />
<hkern u1="g" u2="&#x29;" k="-11" />
<hkern u1="h" u2="&#x2122;" k="11" />
<hkern u1="h" u2="&#x201c;" k="17" />
<hkern u1="h" u2="&#x2018;" k="17" />
<hkern u1="h" u2="y" k="3" />
<hkern u1="h" u2="v" k="3" />
<hkern u1="h" u2="t" k="6" />
<hkern u1="h" u2="\" k="6" />
<hkern u1="h" u2="&#x3f;" k="6" />
<hkern u1="j" u2="y" k="-11" />
<hkern u1="j" u2="v" k="-11" />
<hkern u1="j" u2="j" k="-11" />
<hkern u1="k" u2="&#x153;" k="6" />
<hkern u1="k" u2="&#xe7;" k="6" />
<hkern u1="k" u2="&#xe6;" k="6" />
<hkern u1="k" u2="y" k="-6" />
<hkern u1="k" u2="v" k="-6" />
<hkern u1="k" u2="q" k="6" />
<hkern u1="k" u2="o" k="6" />
<hkern u1="k" u2="g" k="11" />
<hkern u1="k" u2="e" k="6" />
<hkern u1="k" u2="d" k="6" />
<hkern u1="k" u2="c" k="6" />
<hkern u1="k" u2="a" k="6" />
<hkern u1="l" u2="y" k="-6" />
<hkern u1="l" u2="v" k="-6" />
<hkern u1="m" u2="&#x2122;" k="11" />
<hkern u1="m" u2="&#x201c;" k="17" />
<hkern u1="m" u2="&#x2018;" k="17" />
<hkern u1="m" u2="y" k="3" />
<hkern u1="m" u2="v" k="3" />
<hkern u1="m" u2="t" k="6" />
<hkern u1="m" u2="\" k="6" />
<hkern u1="m" u2="&#x3f;" k="6" />
<hkern u1="n" u2="&#x2122;" k="11" />
<hkern u1="n" u2="&#x201c;" k="17" />
<hkern u1="n" u2="&#x2018;" k="17" />
<hkern u1="n" u2="y" k="3" />
<hkern u1="n" u2="v" k="3" />
<hkern u1="n" u2="t" k="6" />
<hkern u1="n" u2="\" k="6" />
<hkern u1="n" u2="&#x3f;" k="6" />
<hkern u1="o" u2="&#x2122;" k="17" />
<hkern u1="o" u2="&#x2026;" k="11" />
<hkern u1="o" u2="&#x201e;" k="11" />
<hkern u1="o" u2="&#x201c;" k="22" />
<hkern u1="o" u2="&#x201a;" k="11" />
<hkern u1="o" u2="&#x2018;" k="22" />
<hkern u1="o" u2="z" k="8" />
<hkern u1="o" u2="y" k="-3" />
<hkern u1="o" u2="x" k="3" />
<hkern u1="o" u2="v" k="-3" />
<hkern u1="o" u2="t" k="6" />
<hkern u1="o" u2="s" k="-3" />
<hkern u1="o" u2="\" k="6" />
<hkern u1="o" u2="&#x3f;" k="11" />
<hkern u1="o" u2="&#x3b;" k="6" />
<hkern u1="o" u2="&#x3a;" k="6" />
<hkern u1="o" u2="&#x2f;" k="6" />
<hkern u1="o" u2="&#x2e;" k="11" />
<hkern u1="o" u2="&#x2c;" k="11" />
<hkern u1="p" u2="&#x2122;" k="17" />
<hkern u1="p" u2="&#x2026;" k="11" />
<hkern u1="p" u2="&#x201e;" k="11" />
<hkern u1="p" u2="&#x201c;" k="22" />
<hkern u1="p" u2="&#x201a;" k="11" />
<hkern u1="p" u2="&#x2018;" k="22" />
<hkern u1="p" u2="z" k="8" />
<hkern u1="p" u2="y" k="-3" />
<hkern u1="p" u2="x" k="3" />
<hkern u1="p" u2="v" k="-3" />
<hkern u1="p" u2="t" k="6" />
<hkern u1="p" u2="s" k="-3" />
<hkern u1="p" u2="\" k="6" />
<hkern u1="p" u2="&#x3f;" k="11" />
<hkern u1="p" u2="&#x3b;" k="6" />
<hkern u1="p" u2="&#x3a;" k="6" />
<hkern u1="p" u2="&#x2f;" k="6" />
<hkern u1="p" u2="&#x2e;" k="11" />
<hkern u1="p" u2="&#x2c;" k="11" />
<hkern u1="q" u2="j" k="-17" />
<hkern u1="r" u2="&#x2026;" k="28" />
<hkern u1="r" u2="&#x201e;" k="28" />
<hkern u1="r" u2="&#x201d;" k="-22" />
<hkern u1="r" u2="&#x201c;" k="-11" />
<hkern u1="r" u2="&#x201a;" k="28" />
<hkern u1="r" u2="&#x2019;" k="-22" />
<hkern u1="r" u2="&#x2018;" k="-11" />
<hkern u1="r" u2="&#x2014;" k="11" />
<hkern u1="r" u2="&#x2013;" k="11" />
<hkern u1="r" u2="y" k="-22" />
<hkern u1="r" u2="x" k="-6" />
<hkern u1="r" u2="w" k="-17" />
<hkern u1="r" u2="v" k="-22" />
<hkern u1="r" u2="t" k="-11" />
<hkern u1="r" u2="g" k="6" />
<hkern u1="r" u2="f" k="-8" />
<hkern u1="r" u2="&#x3f;" k="-11" />
<hkern u1="r" u2="&#x2f;" k="33" />
<hkern u1="r" u2="&#x2e;" k="28" />
<hkern u1="r" u2="&#x2d;" k="11" />
<hkern u1="r" u2="&#x2c;" k="28" />
<hkern u1="t" u2="&#x2026;" k="-6" />
<hkern u1="t" u2="&#x201e;" k="-6" />
<hkern u1="t" u2="&#x201c;" k="-6" />
<hkern u1="t" u2="&#x201a;" k="-6" />
<hkern u1="t" u2="&#x2018;" k="-6" />
<hkern u1="t" u2="&#x153;" k="3" />
<hkern u1="t" u2="&#xe7;" k="3" />
<hkern u1="t" u2="&#xe6;" k="3" />
<hkern u1="t" u2="y" k="-8" />
<hkern u1="t" u2="x" k="-3" />
<hkern u1="t" u2="v" k="-8" />
<hkern u1="t" u2="q" k="3" />
<hkern u1="t" u2="o" k="3" />
<hkern u1="t" u2="e" k="3" />
<hkern u1="t" u2="d" k="3" />
<hkern u1="t" u2="c" k="3" />
<hkern u1="t" u2="a" k="3" />
<hkern u1="t" u2="&#x2e;" k="-6" />
<hkern u1="t" u2="&#x2c;" k="-6" />
<hkern u1="u" u2="y" k="-6" />
<hkern u1="u" u2="v" k="-6" />
<hkern u1="v" u2="&#x2026;" k="28" />
<hkern u1="v" u2="&#x201e;" k="28" />
<hkern u1="v" u2="&#x201d;" k="-17" />
<hkern u1="v" u2="&#x201c;" k="-17" />
<hkern u1="v" u2="&#x201a;" k="28" />
<hkern u1="v" u2="&#x2019;" k="-17" />
<hkern u1="v" u2="&#x2018;" k="-17" />
<hkern u1="v" u2="&#x2014;" k="6" />
<hkern u1="v" u2="&#x2013;" k="6" />
<hkern u1="v" u2="&#x153;" k="6" />
<hkern u1="v" u2="&#xe7;" k="6" />
<hkern u1="v" u2="&#xe6;" k="6" />
<hkern u1="v" u2="y" k="-17" />
<hkern u1="v" u2="w" k="-6" />
<hkern u1="v" u2="v" k="-17" />
<hkern u1="v" u2="q" k="6" />
<hkern u1="v" u2="o" k="6" />
<hkern u1="v" u2="e" k="6" />
<hkern u1="v" u2="d" k="6" />
<hkern u1="v" u2="c" k="6" />
<hkern u1="v" u2="a" k="6" />
<hkern u1="v" u2="&#x2e;" k="28" />
<hkern u1="v" u2="&#x2d;" k="6" />
<hkern u1="v" u2="&#x2c;" k="28" />
<hkern u1="w" u2="&#x2026;" k="17" />
<hkern u1="w" u2="&#x201e;" k="17" />
<hkern u1="w" u2="&#x201d;" k="-11" />
<hkern u1="w" u2="&#x201c;" k="-11" />
<hkern u1="w" u2="&#x201a;" k="17" />
<hkern u1="w" u2="&#x2019;" k="-11" />
<hkern u1="w" u2="&#x2018;" k="-11" />
<hkern u1="w" u2="y" k="-6" />
<hkern u1="w" u2="v" k="-6" />
<hkern u1="w" u2="&#x2e;" k="17" />
<hkern u1="w" u2="&#x2c;" k="17" />
<hkern u1="x" u2="&#x2039;" k="28" />
<hkern u1="x" u2="&#x201d;" k="-11" />
<hkern u1="x" u2="&#x201c;" k="-6" />
<hkern u1="x" u2="&#x2019;" k="-11" />
<hkern u1="x" u2="&#x2018;" k="-6" />
<hkern u1="x" u2="&#x2014;" k="17" />
<hkern u1="x" u2="&#x2013;" k="17" />
<hkern u1="x" u2="&#x153;" k="8" />
<hkern u1="x" u2="&#xe7;" k="8" />
<hkern u1="x" u2="&#xe6;" k="8" />
<hkern u1="x" u2="&#xab;" k="28" />
<hkern u1="x" u2="y" k="-11" />
<hkern u1="x" u2="v" k="-11" />
<hkern u1="x" u2="q" k="8" />
<hkern u1="x" u2="o" k="8" />
<hkern u1="x" u2="e" k="8" />
<hkern u1="x" u2="d" k="8" />
<hkern u1="x" u2="c" k="8" />
<hkern u1="x" u2="a" k="8" />
<hkern u1="x" u2="&#x2d;" k="17" />
<hkern u1="y" u2="&#x2026;" k="28" />
<hkern u1="y" u2="&#x201e;" k="28" />
<hkern u1="y" u2="&#x201d;" k="-17" />
<hkern u1="y" u2="&#x201c;" k="-17" />
<hkern u1="y" u2="&#x201a;" k="28" />
<hkern u1="y" u2="&#x2019;" k="-17" />
<hkern u1="y" u2="&#x2018;" k="-17" />
<hkern u1="y" u2="&#x2014;" k="6" />
<hkern u1="y" u2="&#x2013;" k="6" />
<hkern u1="y" u2="&#x153;" k="6" />
<hkern u1="y" u2="&#xe7;" k="6" />
<hkern u1="y" u2="&#xe6;" k="6" />
<hkern u1="y" u2="y" k="-17" />
<hkern u1="y" u2="w" k="-6" />
<hkern u1="y" u2="v" k="-17" />
<hkern u1="y" u2="q" k="6" />
<hkern u1="y" u2="o" k="6" />
<hkern u1="y" u2="e" k="6" />
<hkern u1="y" u2="d" k="6" />
<hkern u1="y" u2="c" k="6" />
<hkern u1="y" u2="a" k="6" />
<hkern u1="y" u2="&#x2e;" k="28" />
<hkern u1="y" u2="&#x2d;" k="6" />
<hkern u1="y" u2="&#x2c;" k="28" />
<hkern u1="z" u2="&#x2039;" k="33" />
<hkern u1="z" u2="&#x2014;" k="17" />
<hkern u1="z" u2="&#x2013;" k="17" />
<hkern u1="z" u2="&#x153;" k="11" />
<hkern u1="z" u2="&#xe7;" k="11" />
<hkern u1="z" u2="&#xe6;" k="11" />
<hkern u1="z" u2="&#xab;" k="33" />
<hkern u1="z" u2="y" k="-6" />
<hkern u1="z" u2="v" k="-6" />
<hkern u1="z" u2="u" k="8" />
<hkern u1="z" u2="r" k="8" />
<hkern u1="z" u2="q" k="11" />
<hkern u1="z" u2="p" k="8" />
<hkern u1="z" u2="o" k="11" />
<hkern u1="z" u2="n" k="8" />
<hkern u1="z" u2="m" k="8" />
<hkern u1="z" u2="g" k="6" />
<hkern u1="z" u2="e" k="11" />
<hkern u1="z" u2="d" k="11" />
<hkern u1="z" u2="c" k="11" />
<hkern u1="z" u2="a" k="11" />
<hkern u1="z" u2="&#x2d;" k="17" />
<hkern u1="&#x7b;" u2="&#x178;" k="-28" />
<hkern u1="&#x7b;" u2="&#xdd;" k="-28" />
<hkern u1="&#x7b;" u2="j" k="-83" />
<hkern u1="&#x7b;" u2="g" k="-11" />
<hkern u1="&#x7b;" u2="Y" k="-28" />
<hkern u1="&#x7b;" u2="X" k="-22" />
<hkern u1="&#x7b;" u2="W" k="-6" />
<hkern u1="&#x7b;" u2="V" k="-17" />
<hkern u1="&#xa3;" u2="&#x34;" k="11" />
<hkern u1="&#xa3;" u2="&#x31;" k="-6" />
<hkern u1="&#xa4;" u2="&#x34;" k="11" />
<hkern u1="&#xa9;" u2="&#x178;" k="17" />
<hkern u1="&#xa9;" u2="&#xdd;" k="17" />
<hkern u1="&#xa9;" u2="&#xc6;" k="6" />
<hkern u1="&#xa9;" u2="&#xc5;" k="6" />
<hkern u1="&#xa9;" u2="&#xc4;" k="6" />
<hkern u1="&#xa9;" u2="&#xc3;" k="6" />
<hkern u1="&#xa9;" u2="&#xc2;" k="6" />
<hkern u1="&#xa9;" u2="&#xc1;" k="6" />
<hkern u1="&#xa9;" u2="&#xc0;" k="6" />
<hkern u1="&#xa9;" u2="Y" k="17" />
<hkern u1="&#xa9;" u2="W" k="11" />
<hkern u1="&#xa9;" u2="T" k="17" />
<hkern u1="&#xa9;" u2="A" k="6" />
<hkern u1="&#xab;" u2="&#x178;" k="22" />
<hkern u1="&#xab;" u2="&#xdd;" k="22" />
<hkern u1="&#xab;" u2="Y" k="22" />
<hkern u1="&#xab;" u2="W" k="11" />
<hkern u1="&#xab;" u2="V" k="11" />
<hkern u1="&#xab;" u2="T" k="22" />
<hkern u1="&#xae;" u2="&#x178;" k="17" />
<hkern u1="&#xae;" u2="&#xdd;" k="17" />
<hkern u1="&#xae;" u2="&#xc6;" k="6" />
<hkern u1="&#xae;" u2="&#xc5;" k="6" />
<hkern u1="&#xae;" u2="&#xc4;" k="6" />
<hkern u1="&#xae;" u2="&#xc3;" k="6" />
<hkern u1="&#xae;" u2="&#xc2;" k="6" />
<hkern u1="&#xae;" u2="&#xc1;" k="6" />
<hkern u1="&#xae;" u2="&#xc0;" k="6" />
<hkern u1="&#xae;" u2="Y" k="17" />
<hkern u1="&#xae;" u2="W" k="11" />
<hkern u1="&#xae;" u2="T" k="17" />
<hkern u1="&#xae;" u2="A" k="6" />
<hkern u1="&#xb0;" u2="&#x34;" k="36" />
<hkern u1="&#xbb;" u2="&#x178;" k="28" />
<hkern u1="&#xbb;" u2="&#xdd;" k="28" />
<hkern u1="&#xbb;" u2="z" k="28" />
<hkern u1="&#xbb;" u2="x" k="28" />
<hkern u1="&#xbb;" u2="Y" k="28" />
<hkern u1="&#xbb;" u2="W" k="22" />
<hkern u1="&#xbb;" u2="V" k="28" />
<hkern u1="&#xbb;" u2="T" k="61" />
<hkern u1="&#xbf;" u2="&#x178;" k="22" />
<hkern u1="&#xbf;" u2="&#x152;" k="11" />
<hkern u1="&#xbf;" u2="&#xdd;" k="22" />
<hkern u1="&#xbf;" u2="&#xd8;" k="11" />
<hkern u1="&#xbf;" u2="&#xd6;" k="11" />
<hkern u1="&#xbf;" u2="&#xd5;" k="11" />
<hkern u1="&#xbf;" u2="&#xd4;" k="11" />
<hkern u1="&#xbf;" u2="&#xd3;" k="11" />
<hkern u1="&#xbf;" u2="&#xd2;" k="11" />
<hkern u1="&#xbf;" u2="y" k="17" />
<hkern u1="&#xbf;" u2="x" k="6" />
<hkern u1="&#xbf;" u2="w" k="11" />
<hkern u1="&#xbf;" u2="v" k="17" />
<hkern u1="&#xbf;" u2="Y" k="22" />
<hkern u1="&#xbf;" u2="W" k="17" />
<hkern u1="&#xbf;" u2="V" k="28" />
<hkern u1="&#xbf;" u2="T" k="33" />
<hkern u1="&#xbf;" u2="Q" k="11" />
<hkern u1="&#xbf;" u2="O" k="11" />
<hkern u1="&#xbf;" u2="G" k="11" />
<hkern u1="&#xbf;" u2="C" k="11" />
<hkern u1="&#xbf;" u2="&#x37;" k="17" />
<hkern u1="&#xbf;" u2="&#x33;" k="-6" />
<hkern u1="&#xbf;" u2="&#x31;" k="22" />
<hkern u1="&#xc0;" u2="&#x2122;" k="39" />
<hkern u1="&#xc0;" u2="&#x2026;" k="-17" />
<hkern u1="&#xc0;" u2="&#x201e;" k="-17" />
<hkern u1="&#xc0;" u2="&#x201d;" k="17" />
<hkern u1="&#xc0;" u2="&#x201c;" k="33" />
<hkern u1="&#xc0;" u2="&#x201a;" k="-17" />
<hkern u1="&#xc0;" u2="&#x2019;" k="17" />
<hkern u1="&#xc0;" u2="&#x2018;" k="33" />
<hkern u1="&#xc0;" u2="&#x2014;" k="-6" />
<hkern u1="&#xc0;" u2="&#x2013;" k="-6" />
<hkern u1="&#xc0;" u2="&#x178;" k="39" />
<hkern u1="&#xc0;" u2="&#x153;" k="3" />
<hkern u1="&#xc0;" u2="&#x152;" k="22" />
<hkern u1="&#xc0;" u2="&#xe7;" k="3" />
<hkern u1="&#xc0;" u2="&#xe6;" k="3" />
<hkern u1="&#xc0;" u2="&#xdd;" k="39" />
<hkern u1="&#xc0;" u2="&#xd8;" k="22" />
<hkern u1="&#xc0;" u2="&#xd6;" k="22" />
<hkern u1="&#xc0;" u2="&#xd5;" k="22" />
<hkern u1="&#xc0;" u2="&#xd4;" k="22" />
<hkern u1="&#xc0;" u2="&#xd3;" k="22" />
<hkern u1="&#xc0;" u2="&#xd2;" k="22" />
<hkern u1="&#xc0;" u2="&#xc6;" k="-6" />
<hkern u1="&#xc0;" u2="&#xc5;" k="-6" />
<hkern u1="&#xc0;" u2="&#xc4;" k="-6" />
<hkern u1="&#xc0;" u2="&#xc3;" k="-6" />
<hkern u1="&#xc0;" u2="&#xc2;" k="-6" />
<hkern u1="&#xc0;" u2="&#xc1;" k="-6" />
<hkern u1="&#xc0;" u2="&#xc0;" k="-6" />
<hkern u1="&#xc0;" u2="&#xae;" k="6" />
<hkern u1="&#xc0;" u2="&#xa9;" k="6" />
<hkern u1="&#xc0;" u2="z" k="-6" />
<hkern u1="&#xc0;" u2="y" k="17" />
<hkern u1="&#xc0;" u2="x" k="-11" />
<hkern u1="&#xc0;" u2="w" k="6" />
<hkern u1="&#xc0;" u2="v" k="17" />
<hkern u1="&#xc0;" u2="u" k="3" />
<hkern u1="&#xc0;" u2="t" k="3" />
<hkern u1="&#xc0;" u2="r" k="3" />
<hkern u1="&#xc0;" u2="q" k="3" />
<hkern u1="&#xc0;" u2="p" k="3" />
<hkern u1="&#xc0;" u2="o" k="3" />
<hkern u1="&#xc0;" u2="n" k="3" />
<hkern u1="&#xc0;" u2="m" k="3" />
<hkern u1="&#xc0;" u2="e" k="3" />
<hkern u1="&#xc0;" u2="d" k="3" />
<hkern u1="&#xc0;" u2="c" k="3" />
<hkern u1="&#xc0;" u2="a" k="3" />
<hkern u1="&#xc0;" u2="Y" k="39" />
<hkern u1="&#xc0;" u2="W" k="39" />
<hkern u1="&#xc0;" u2="V" k="39" />
<hkern u1="&#xc0;" u2="T" k="50" />
<hkern u1="&#xc0;" u2="S" k="-3" />
<hkern u1="&#xc0;" u2="Q" k="22" />
<hkern u1="&#xc0;" u2="O" k="22" />
<hkern u1="&#xc0;" u2="J" k="6" />
<hkern u1="&#xc0;" u2="G" k="22" />
<hkern u1="&#xc0;" u2="C" k="22" />
<hkern u1="&#xc0;" u2="A" k="-6" />
<hkern u1="&#xc0;" u2="&#x40;" k="6" />
<hkern u1="&#xc0;" u2="&#x2e;" k="-17" />
<hkern u1="&#xc0;" u2="&#x2d;" k="-6" />
<hkern u1="&#xc0;" u2="&#x2c;" k="-17" />
<hkern u1="&#xc0;" u2="&#x2a;" k="33" />
<hkern u1="&#xc1;" u2="&#x2122;" k="39" />
<hkern u1="&#xc1;" u2="&#x2026;" k="-17" />
<hkern u1="&#xc1;" u2="&#x201e;" k="-17" />
<hkern u1="&#xc1;" u2="&#x201d;" k="17" />
<hkern u1="&#xc1;" u2="&#x201c;" k="33" />
<hkern u1="&#xc1;" u2="&#x201a;" k="-17" />
<hkern u1="&#xc1;" u2="&#x2019;" k="17" />
<hkern u1="&#xc1;" u2="&#x2018;" k="33" />
<hkern u1="&#xc1;" u2="&#x2014;" k="-6" />
<hkern u1="&#xc1;" u2="&#x2013;" k="-6" />
<hkern u1="&#xc1;" u2="&#x178;" k="39" />
<hkern u1="&#xc1;" u2="&#x153;" k="3" />
<hkern u1="&#xc1;" u2="&#x152;" k="22" />
<hkern u1="&#xc1;" u2="&#xe7;" k="3" />
<hkern u1="&#xc1;" u2="&#xe6;" k="3" />
<hkern u1="&#xc1;" u2="&#xdd;" k="39" />
<hkern u1="&#xc1;" u2="&#xd8;" k="22" />
<hkern u1="&#xc1;" u2="&#xd6;" k="22" />
<hkern u1="&#xc1;" u2="&#xd5;" k="22" />
<hkern u1="&#xc1;" u2="&#xd4;" k="22" />
<hkern u1="&#xc1;" u2="&#xd3;" k="22" />
<hkern u1="&#xc1;" u2="&#xd2;" k="22" />
<hkern u1="&#xc1;" u2="&#xc6;" k="-6" />
<hkern u1="&#xc1;" u2="&#xc5;" k="-6" />
<hkern u1="&#xc1;" u2="&#xc4;" k="-6" />
<hkern u1="&#xc1;" u2="&#xc3;" k="-6" />
<hkern u1="&#xc1;" u2="&#xc2;" k="-6" />
<hkern u1="&#xc1;" u2="&#xc1;" k="-6" />
<hkern u1="&#xc1;" u2="&#xc0;" k="-6" />
<hkern u1="&#xc1;" u2="&#xae;" k="6" />
<hkern u1="&#xc1;" u2="&#xa9;" k="6" />
<hkern u1="&#xc1;" u2="z" k="-6" />
<hkern u1="&#xc1;" u2="y" k="17" />
<hkern u1="&#xc1;" u2="x" k="-11" />
<hkern u1="&#xc1;" u2="w" k="6" />
<hkern u1="&#xc1;" u2="v" k="17" />
<hkern u1="&#xc1;" u2="u" k="3" />
<hkern u1="&#xc1;" u2="t" k="3" />
<hkern u1="&#xc1;" u2="r" k="3" />
<hkern u1="&#xc1;" u2="q" k="3" />
<hkern u1="&#xc1;" u2="p" k="3" />
<hkern u1="&#xc1;" u2="o" k="3" />
<hkern u1="&#xc1;" u2="n" k="3" />
<hkern u1="&#xc1;" u2="m" k="3" />
<hkern u1="&#xc1;" u2="e" k="3" />
<hkern u1="&#xc1;" u2="d" k="3" />
<hkern u1="&#xc1;" u2="c" k="3" />
<hkern u1="&#xc1;" u2="a" k="3" />
<hkern u1="&#xc1;" u2="Y" k="39" />
<hkern u1="&#xc1;" u2="W" k="39" />
<hkern u1="&#xc1;" u2="V" k="39" />
<hkern u1="&#xc1;" u2="T" k="50" />
<hkern u1="&#xc1;" u2="S" k="-3" />
<hkern u1="&#xc1;" u2="Q" k="22" />
<hkern u1="&#xc1;" u2="O" k="22" />
<hkern u1="&#xc1;" u2="J" k="6" />
<hkern u1="&#xc1;" u2="G" k="22" />
<hkern u1="&#xc1;" u2="C" k="22" />
<hkern u1="&#xc1;" u2="A" k="-6" />
<hkern u1="&#xc1;" u2="&#x40;" k="6" />
<hkern u1="&#xc1;" u2="&#x2e;" k="-17" />
<hkern u1="&#xc1;" u2="&#x2d;" k="-6" />
<hkern u1="&#xc1;" u2="&#x2c;" k="-17" />
<hkern u1="&#xc1;" u2="&#x2a;" k="33" />
<hkern u1="&#xc2;" u2="&#x2122;" k="39" />
<hkern u1="&#xc2;" u2="&#x2026;" k="-17" />
<hkern u1="&#xc2;" u2="&#x201e;" k="-17" />
<hkern u1="&#xc2;" u2="&#x201d;" k="17" />
<hkern u1="&#xc2;" u2="&#x201c;" k="33" />
<hkern u1="&#xc2;" u2="&#x201a;" k="-17" />
<hkern u1="&#xc2;" u2="&#x2019;" k="17" />
<hkern u1="&#xc2;" u2="&#x2018;" k="33" />
<hkern u1="&#xc2;" u2="&#x2014;" k="-6" />
<hkern u1="&#xc2;" u2="&#x2013;" k="-6" />
<hkern u1="&#xc2;" u2="&#x178;" k="39" />
<hkern u1="&#xc2;" u2="&#x153;" k="3" />
<hkern u1="&#xc2;" u2="&#x152;" k="22" />
<hkern u1="&#xc2;" u2="&#xe7;" k="3" />
<hkern u1="&#xc2;" u2="&#xe6;" k="3" />
<hkern u1="&#xc2;" u2="&#xdd;" k="39" />
<hkern u1="&#xc2;" u2="&#xd8;" k="22" />
<hkern u1="&#xc2;" u2="&#xd6;" k="22" />
<hkern u1="&#xc2;" u2="&#xd5;" k="22" />
<hkern u1="&#xc2;" u2="&#xd4;" k="22" />
<hkern u1="&#xc2;" u2="&#xd3;" k="22" />
<hkern u1="&#xc2;" u2="&#xd2;" k="22" />
<hkern u1="&#xc2;" u2="&#xc6;" k="-6" />
<hkern u1="&#xc2;" u2="&#xc5;" k="-6" />
<hkern u1="&#xc2;" u2="&#xc4;" k="-6" />
<hkern u1="&#xc2;" u2="&#xc3;" k="-6" />
<hkern u1="&#xc2;" u2="&#xc2;" k="-6" />
<hkern u1="&#xc2;" u2="&#xc1;" k="-6" />
<hkern u1="&#xc2;" u2="&#xc0;" k="-6" />
<hkern u1="&#xc2;" u2="&#xae;" k="6" />
<hkern u1="&#xc2;" u2="&#xa9;" k="6" />
<hkern u1="&#xc2;" u2="z" k="-6" />
<hkern u1="&#xc2;" u2="y" k="17" />
<hkern u1="&#xc2;" u2="x" k="-11" />
<hkern u1="&#xc2;" u2="w" k="6" />
<hkern u1="&#xc2;" u2="v" k="17" />
<hkern u1="&#xc2;" u2="u" k="3" />
<hkern u1="&#xc2;" u2="t" k="3" />
<hkern u1="&#xc2;" u2="r" k="3" />
<hkern u1="&#xc2;" u2="q" k="3" />
<hkern u1="&#xc2;" u2="p" k="3" />
<hkern u1="&#xc2;" u2="o" k="3" />
<hkern u1="&#xc2;" u2="n" k="3" />
<hkern u1="&#xc2;" u2="m" k="3" />
<hkern u1="&#xc2;" u2="e" k="3" />
<hkern u1="&#xc2;" u2="d" k="3" />
<hkern u1="&#xc2;" u2="c" k="3" />
<hkern u1="&#xc2;" u2="a" k="3" />
<hkern u1="&#xc2;" u2="Y" k="39" />
<hkern u1="&#xc2;" u2="W" k="39" />
<hkern u1="&#xc2;" u2="V" k="39" />
<hkern u1="&#xc2;" u2="T" k="50" />
<hkern u1="&#xc2;" u2="S" k="-3" />
<hkern u1="&#xc2;" u2="Q" k="22" />
<hkern u1="&#xc2;" u2="O" k="22" />
<hkern u1="&#xc2;" u2="J" k="6" />
<hkern u1="&#xc2;" u2="G" k="22" />
<hkern u1="&#xc2;" u2="C" k="22" />
<hkern u1="&#xc2;" u2="A" k="-6" />
<hkern u1="&#xc2;" u2="&#x40;" k="6" />
<hkern u1="&#xc2;" u2="&#x2e;" k="-17" />
<hkern u1="&#xc2;" u2="&#x2d;" k="-6" />
<hkern u1="&#xc2;" u2="&#x2c;" k="-17" />
<hkern u1="&#xc2;" u2="&#x2a;" k="33" />
<hkern u1="&#xc3;" u2="&#x2122;" k="39" />
<hkern u1="&#xc3;" u2="&#x2026;" k="-17" />
<hkern u1="&#xc3;" u2="&#x201e;" k="-17" />
<hkern u1="&#xc3;" u2="&#x201d;" k="17" />
<hkern u1="&#xc3;" u2="&#x201c;" k="33" />
<hkern u1="&#xc3;" u2="&#x201a;" k="-17" />
<hkern u1="&#xc3;" u2="&#x2019;" k="17" />
<hkern u1="&#xc3;" u2="&#x2018;" k="33" />
<hkern u1="&#xc3;" u2="&#x2014;" k="-6" />
<hkern u1="&#xc3;" u2="&#x2013;" k="-6" />
<hkern u1="&#xc3;" u2="&#x178;" k="39" />
<hkern u1="&#xc3;" u2="&#x153;" k="3" />
<hkern u1="&#xc3;" u2="&#x152;" k="22" />
<hkern u1="&#xc3;" u2="&#xe7;" k="3" />
<hkern u1="&#xc3;" u2="&#xe6;" k="3" />
<hkern u1="&#xc3;" u2="&#xdd;" k="39" />
<hkern u1="&#xc3;" u2="&#xd8;" k="22" />
<hkern u1="&#xc3;" u2="&#xd6;" k="22" />
<hkern u1="&#xc3;" u2="&#xd5;" k="22" />
<hkern u1="&#xc3;" u2="&#xd4;" k="22" />
<hkern u1="&#xc3;" u2="&#xd3;" k="22" />
<hkern u1="&#xc3;" u2="&#xd2;" k="22" />
<hkern u1="&#xc3;" u2="&#xc6;" k="-6" />
<hkern u1="&#xc3;" u2="&#xc5;" k="-6" />
<hkern u1="&#xc3;" u2="&#xc4;" k="-6" />
<hkern u1="&#xc3;" u2="&#xc3;" k="-6" />
<hkern u1="&#xc3;" u2="&#xc2;" k="-6" />
<hkern u1="&#xc3;" u2="&#xc1;" k="-6" />
<hkern u1="&#xc3;" u2="&#xc0;" k="-6" />
<hkern u1="&#xc3;" u2="&#xae;" k="6" />
<hkern u1="&#xc3;" u2="&#xa9;" k="6" />
<hkern u1="&#xc3;" u2="z" k="-6" />
<hkern u1="&#xc3;" u2="y" k="17" />
<hkern u1="&#xc3;" u2="x" k="-11" />
<hkern u1="&#xc3;" u2="w" k="6" />
<hkern u1="&#xc3;" u2="v" k="17" />
<hkern u1="&#xc3;" u2="u" k="3" />
<hkern u1="&#xc3;" u2="t" k="3" />
<hkern u1="&#xc3;" u2="r" k="3" />
<hkern u1="&#xc3;" u2="q" k="3" />
<hkern u1="&#xc3;" u2="p" k="3" />
<hkern u1="&#xc3;" u2="o" k="3" />
<hkern u1="&#xc3;" u2="n" k="3" />
<hkern u1="&#xc3;" u2="m" k="3" />
<hkern u1="&#xc3;" u2="e" k="3" />
<hkern u1="&#xc3;" u2="d" k="3" />
<hkern u1="&#xc3;" u2="c" k="3" />
<hkern u1="&#xc3;" u2="a" k="3" />
<hkern u1="&#xc3;" u2="Y" k="39" />
<hkern u1="&#xc3;" u2="W" k="39" />
<hkern u1="&#xc3;" u2="V" k="39" />
<hkern u1="&#xc3;" u2="T" k="50" />
<hkern u1="&#xc3;" u2="S" k="-3" />
<hkern u1="&#xc3;" u2="Q" k="22" />
<hkern u1="&#xc3;" u2="O" k="22" />
<hkern u1="&#xc3;" u2="J" k="6" />
<hkern u1="&#xc3;" u2="G" k="22" />
<hkern u1="&#xc3;" u2="C" k="22" />
<hkern u1="&#xc3;" u2="A" k="-6" />
<hkern u1="&#xc3;" u2="&#x40;" k="6" />
<hkern u1="&#xc3;" u2="&#x2e;" k="-17" />
<hkern u1="&#xc3;" u2="&#x2d;" k="-6" />
<hkern u1="&#xc3;" u2="&#x2c;" k="-17" />
<hkern u1="&#xc3;" u2="&#x2a;" k="33" />
<hkern u1="&#xc4;" u2="&#x2122;" k="39" />
<hkern u1="&#xc4;" u2="&#x2026;" k="-17" />
<hkern u1="&#xc4;" u2="&#x201e;" k="-17" />
<hkern u1="&#xc4;" u2="&#x201d;" k="17" />
<hkern u1="&#xc4;" u2="&#x201c;" k="33" />
<hkern u1="&#xc4;" u2="&#x201a;" k="-17" />
<hkern u1="&#xc4;" u2="&#x2019;" k="17" />
<hkern u1="&#xc4;" u2="&#x2018;" k="33" />
<hkern u1="&#xc4;" u2="&#x2014;" k="-6" />
<hkern u1="&#xc4;" u2="&#x2013;" k="-6" />
<hkern u1="&#xc4;" u2="&#x178;" k="39" />
<hkern u1="&#xc4;" u2="&#x153;" k="3" />
<hkern u1="&#xc4;" u2="&#x152;" k="22" />
<hkern u1="&#xc4;" u2="&#xe7;" k="3" />
<hkern u1="&#xc4;" u2="&#xe6;" k="3" />
<hkern u1="&#xc4;" u2="&#xdd;" k="39" />
<hkern u1="&#xc4;" u2="&#xd8;" k="22" />
<hkern u1="&#xc4;" u2="&#xd6;" k="22" />
<hkern u1="&#xc4;" u2="&#xd5;" k="22" />
<hkern u1="&#xc4;" u2="&#xd4;" k="22" />
<hkern u1="&#xc4;" u2="&#xd3;" k="22" />
<hkern u1="&#xc4;" u2="&#xd2;" k="22" />
<hkern u1="&#xc4;" u2="&#xc6;" k="-6" />
<hkern u1="&#xc4;" u2="&#xc5;" k="-6" />
<hkern u1="&#xc4;" u2="&#xc4;" k="-6" />
<hkern u1="&#xc4;" u2="&#xc3;" k="-6" />
<hkern u1="&#xc4;" u2="&#xc2;" k="-6" />
<hkern u1="&#xc4;" u2="&#xc1;" k="-6" />
<hkern u1="&#xc4;" u2="&#xc0;" k="-6" />
<hkern u1="&#xc4;" u2="&#xae;" k="6" />
<hkern u1="&#xc4;" u2="&#xa9;" k="6" />
<hkern u1="&#xc4;" u2="z" k="-6" />
<hkern u1="&#xc4;" u2="y" k="17" />
<hkern u1="&#xc4;" u2="x" k="-11" />
<hkern u1="&#xc4;" u2="w" k="6" />
<hkern u1="&#xc4;" u2="v" k="17" />
<hkern u1="&#xc4;" u2="u" k="3" />
<hkern u1="&#xc4;" u2="t" k="3" />
<hkern u1="&#xc4;" u2="r" k="3" />
<hkern u1="&#xc4;" u2="q" k="3" />
<hkern u1="&#xc4;" u2="p" k="3" />
<hkern u1="&#xc4;" u2="o" k="3" />
<hkern u1="&#xc4;" u2="n" k="3" />
<hkern u1="&#xc4;" u2="m" k="3" />
<hkern u1="&#xc4;" u2="e" k="3" />
<hkern u1="&#xc4;" u2="d" k="3" />
<hkern u1="&#xc4;" u2="c" k="3" />
<hkern u1="&#xc4;" u2="a" k="3" />
<hkern u1="&#xc4;" u2="Y" k="39" />
<hkern u1="&#xc4;" u2="W" k="39" />
<hkern u1="&#xc4;" u2="V" k="39" />
<hkern u1="&#xc4;" u2="T" k="50" />
<hkern u1="&#xc4;" u2="S" k="-3" />
<hkern u1="&#xc4;" u2="Q" k="22" />
<hkern u1="&#xc4;" u2="O" k="22" />
<hkern u1="&#xc4;" u2="J" k="6" />
<hkern u1="&#xc4;" u2="G" k="22" />
<hkern u1="&#xc4;" u2="C" k="22" />
<hkern u1="&#xc4;" u2="A" k="-6" />
<hkern u1="&#xc4;" u2="&#x40;" k="6" />
<hkern u1="&#xc4;" u2="&#x2e;" k="-17" />
<hkern u1="&#xc4;" u2="&#x2d;" k="-6" />
<hkern u1="&#xc4;" u2="&#x2c;" k="-17" />
<hkern u1="&#xc4;" u2="&#x2a;" k="33" />
<hkern u1="&#xc5;" u2="&#x2122;" k="39" />
<hkern u1="&#xc5;" u2="&#x2026;" k="-17" />
<hkern u1="&#xc5;" u2="&#x201e;" k="-17" />
<hkern u1="&#xc5;" u2="&#x201d;" k="17" />
<hkern u1="&#xc5;" u2="&#x201c;" k="33" />
<hkern u1="&#xc5;" u2="&#x201a;" k="-17" />
<hkern u1="&#xc5;" u2="&#x2019;" k="17" />
<hkern u1="&#xc5;" u2="&#x2018;" k="33" />
<hkern u1="&#xc5;" u2="&#x2014;" k="-6" />
<hkern u1="&#xc5;" u2="&#x2013;" k="-6" />
<hkern u1="&#xc5;" u2="&#x178;" k="39" />
<hkern u1="&#xc5;" u2="&#x153;" k="3" />
<hkern u1="&#xc5;" u2="&#x152;" k="22" />
<hkern u1="&#xc5;" u2="&#xe7;" k="3" />
<hkern u1="&#xc5;" u2="&#xe6;" k="3" />
<hkern u1="&#xc5;" u2="&#xdd;" k="39" />
<hkern u1="&#xc5;" u2="&#xd8;" k="22" />
<hkern u1="&#xc5;" u2="&#xd6;" k="22" />
<hkern u1="&#xc5;" u2="&#xd5;" k="22" />
<hkern u1="&#xc5;" u2="&#xd4;" k="22" />
<hkern u1="&#xc5;" u2="&#xd3;" k="22" />
<hkern u1="&#xc5;" u2="&#xd2;" k="22" />
<hkern u1="&#xc5;" u2="&#xc6;" k="-6" />
<hkern u1="&#xc5;" u2="&#xc5;" k="-6" />
<hkern u1="&#xc5;" u2="&#xc4;" k="-6" />
<hkern u1="&#xc5;" u2="&#xc3;" k="-6" />
<hkern u1="&#xc5;" u2="&#xc2;" k="-6" />
<hkern u1="&#xc5;" u2="&#xc1;" k="-6" />
<hkern u1="&#xc5;" u2="&#xc0;" k="-6" />
<hkern u1="&#xc5;" u2="&#xae;" k="6" />
<hkern u1="&#xc5;" u2="&#xa9;" k="6" />
<hkern u1="&#xc5;" u2="z" k="-6" />
<hkern u1="&#xc5;" u2="y" k="17" />
<hkern u1="&#xc5;" u2="x" k="-11" />
<hkern u1="&#xc5;" u2="w" k="6" />
<hkern u1="&#xc5;" u2="v" k="17" />
<hkern u1="&#xc5;" u2="u" k="3" />
<hkern u1="&#xc5;" u2="t" k="3" />
<hkern u1="&#xc5;" u2="r" k="3" />
<hkern u1="&#xc5;" u2="q" k="3" />
<hkern u1="&#xc5;" u2="p" k="3" />
<hkern u1="&#xc5;" u2="o" k="3" />
<hkern u1="&#xc5;" u2="n" k="3" />
<hkern u1="&#xc5;" u2="m" k="3" />
<hkern u1="&#xc5;" u2="e" k="3" />
<hkern u1="&#xc5;" u2="d" k="3" />
<hkern u1="&#xc5;" u2="c" k="3" />
<hkern u1="&#xc5;" u2="a" k="3" />
<hkern u1="&#xc5;" u2="Y" k="39" />
<hkern u1="&#xc5;" u2="W" k="39" />
<hkern u1="&#xc5;" u2="V" k="39" />
<hkern u1="&#xc5;" u2="T" k="50" />
<hkern u1="&#xc5;" u2="S" k="-3" />
<hkern u1="&#xc5;" u2="Q" k="22" />
<hkern u1="&#xc5;" u2="O" k="22" />
<hkern u1="&#xc5;" u2="J" k="6" />
<hkern u1="&#xc5;" u2="G" k="22" />
<hkern u1="&#xc5;" u2="C" k="22" />
<hkern u1="&#xc5;" u2="A" k="-6" />
<hkern u1="&#xc5;" u2="&#x40;" k="6" />
<hkern u1="&#xc5;" u2="&#x2e;" k="-17" />
<hkern u1="&#xc5;" u2="&#x2d;" k="-6" />
<hkern u1="&#xc5;" u2="&#x2c;" k="-17" />
<hkern u1="&#xc5;" u2="&#x2a;" k="33" />
<hkern u1="&#xc6;" u2="&#x153;" k="14" />
<hkern u1="&#xc6;" u2="&#xe7;" k="14" />
<hkern u1="&#xc6;" u2="&#xe6;" k="14" />
<hkern u1="&#xc6;" u2="&#xae;" k="11" />
<hkern u1="&#xc6;" u2="&#xa9;" k="11" />
<hkern u1="&#xc6;" u2="y" k="11" />
<hkern u1="&#xc6;" u2="v" k="11" />
<hkern u1="&#xc6;" u2="q" k="14" />
<hkern u1="&#xc6;" u2="o" k="14" />
<hkern u1="&#xc6;" u2="g" k="3" />
<hkern u1="&#xc6;" u2="f" k="6" />
<hkern u1="&#xc6;" u2="e" k="14" />
<hkern u1="&#xc6;" u2="d" k="14" />
<hkern u1="&#xc6;" u2="c" k="14" />
<hkern u1="&#xc6;" u2="a" k="14" />
<hkern u1="&#xc6;" u2="T" k="-8" />
<hkern u1="&#xc6;" u2="&#x40;" k="11" />
<hkern u1="&#xc8;" u2="&#x153;" k="14" />
<hkern u1="&#xc8;" u2="&#xe7;" k="14" />
<hkern u1="&#xc8;" u2="&#xe6;" k="14" />
<hkern u1="&#xc8;" u2="&#xae;" k="11" />
<hkern u1="&#xc8;" u2="&#xa9;" k="11" />
<hkern u1="&#xc8;" u2="y" k="11" />
<hkern u1="&#xc8;" u2="v" k="11" />
<hkern u1="&#xc8;" u2="q" k="14" />
<hkern u1="&#xc8;" u2="o" k="14" />
<hkern u1="&#xc8;" u2="g" k="3" />
<hkern u1="&#xc8;" u2="f" k="6" />
<hkern u1="&#xc8;" u2="e" k="14" />
<hkern u1="&#xc8;" u2="d" k="14" />
<hkern u1="&#xc8;" u2="c" k="14" />
<hkern u1="&#xc8;" u2="a" k="14" />
<hkern u1="&#xc8;" u2="T" k="-8" />
<hkern u1="&#xc8;" u2="&#x40;" k="11" />
<hkern u1="&#xc9;" u2="&#x153;" k="14" />
<hkern u1="&#xc9;" u2="&#xe7;" k="14" />
<hkern u1="&#xc9;" u2="&#xe6;" k="14" />
<hkern u1="&#xc9;" u2="&#xae;" k="11" />
<hkern u1="&#xc9;" u2="&#xa9;" k="11" />
<hkern u1="&#xc9;" u2="y" k="11" />
<hkern u1="&#xc9;" u2="v" k="11" />
<hkern u1="&#xc9;" u2="q" k="14" />
<hkern u1="&#xc9;" u2="o" k="14" />
<hkern u1="&#xc9;" u2="g" k="3" />
<hkern u1="&#xc9;" u2="f" k="6" />
<hkern u1="&#xc9;" u2="e" k="14" />
<hkern u1="&#xc9;" u2="d" k="14" />
<hkern u1="&#xc9;" u2="c" k="14" />
<hkern u1="&#xc9;" u2="a" k="14" />
<hkern u1="&#xc9;" u2="T" k="-8" />
<hkern u1="&#xc9;" u2="&#x40;" k="11" />
<hkern u1="&#xca;" u2="&#x153;" k="14" />
<hkern u1="&#xca;" u2="&#xe7;" k="14" />
<hkern u1="&#xca;" u2="&#xe6;" k="14" />
<hkern u1="&#xca;" u2="&#xae;" k="11" />
<hkern u1="&#xca;" u2="&#xa9;" k="11" />
<hkern u1="&#xca;" u2="y" k="11" />
<hkern u1="&#xca;" u2="v" k="11" />
<hkern u1="&#xca;" u2="q" k="14" />
<hkern u1="&#xca;" u2="o" k="14" />
<hkern u1="&#xca;" u2="g" k="3" />
<hkern u1="&#xca;" u2="f" k="6" />
<hkern u1="&#xca;" u2="e" k="14" />
<hkern u1="&#xca;" u2="d" k="14" />
<hkern u1="&#xca;" u2="c" k="14" />
<hkern u1="&#xca;" u2="a" k="14" />
<hkern u1="&#xca;" u2="T" k="-8" />
<hkern u1="&#xca;" u2="&#x40;" k="11" />
<hkern u1="&#xcb;" u2="&#x153;" k="14" />
<hkern u1="&#xcb;" u2="&#xe7;" k="14" />
<hkern u1="&#xcb;" u2="&#xe6;" k="14" />
<hkern u1="&#xcb;" u2="&#xae;" k="11" />
<hkern u1="&#xcb;" u2="&#xa9;" k="11" />
<hkern u1="&#xcb;" u2="y" k="11" />
<hkern u1="&#xcb;" u2="v" k="11" />
<hkern u1="&#xcb;" u2="q" k="14" />
<hkern u1="&#xcb;" u2="o" k="14" />
<hkern u1="&#xcb;" u2="g" k="3" />
<hkern u1="&#xcb;" u2="f" k="6" />
<hkern u1="&#xcb;" u2="e" k="14" />
<hkern u1="&#xcb;" u2="d" k="14" />
<hkern u1="&#xcb;" u2="c" k="14" />
<hkern u1="&#xcb;" u2="a" k="14" />
<hkern u1="&#xcb;" u2="T" k="-8" />
<hkern u1="&#xcb;" u2="&#x40;" k="11" />
<hkern u1="&#xcc;" u2="y" k="-11" />
<hkern u1="&#xcc;" u2="v" k="-11" />
<hkern u1="&#xcc;" u2="&#x2f;" k="11" />
<hkern u1="&#xcd;" u2="y" k="-11" />
<hkern u1="&#xcd;" u2="v" k="-11" />
<hkern u1="&#xcd;" u2="&#x2f;" k="11" />
<hkern u1="&#xce;" u2="y" k="-11" />
<hkern u1="&#xce;" u2="v" k="-11" />
<hkern u1="&#xce;" u2="&#x2f;" k="11" />
<hkern u1="&#xcf;" u2="y" k="-11" />
<hkern u1="&#xcf;" u2="v" k="-11" />
<hkern u1="&#xcf;" u2="&#x2f;" k="11" />
<hkern u1="&#xd0;" u2="&#x2026;" k="22" />
<hkern u1="&#xd0;" u2="&#x201e;" k="22" />
<hkern u1="&#xd0;" u2="&#x201c;" k="11" />
<hkern u1="&#xd0;" u2="&#x201a;" k="22" />
<hkern u1="&#xd0;" u2="&#x2018;" k="11" />
<hkern u1="&#xd0;" u2="&#x178;" k="25" />
<hkern u1="&#xd0;" u2="&#x153;" k="3" />
<hkern u1="&#xd0;" u2="&#xe7;" k="3" />
<hkern u1="&#xd0;" u2="&#xe6;" k="3" />
<hkern u1="&#xd0;" u2="&#xdd;" k="25" />
<hkern u1="&#xd0;" u2="z" k="6" />
<hkern u1="&#xd0;" u2="x" k="6" />
<hkern u1="&#xd0;" u2="u" k="3" />
<hkern u1="&#xd0;" u2="r" k="3" />
<hkern u1="&#xd0;" u2="q" k="3" />
<hkern u1="&#xd0;" u2="p" k="3" />
<hkern u1="&#xd0;" u2="o" k="3" />
<hkern u1="&#xd0;" u2="n" k="3" />
<hkern u1="&#xd0;" u2="m" k="3" />
<hkern u1="&#xd0;" u2="l" k="6" />
<hkern u1="&#xd0;" u2="k" k="6" />
<hkern u1="&#xd0;" u2="h" k="6" />
<hkern u1="&#xd0;" u2="e" k="3" />
<hkern u1="&#xd0;" u2="d" k="3" />
<hkern u1="&#xd0;" u2="c" k="3" />
<hkern u1="&#xd0;" u2="b" k="6" />
<hkern u1="&#xd0;" u2="a" k="3" />
<hkern u1="&#xd0;" u2="Z" k="25" />
<hkern u1="&#xd0;" u2="Y" k="25" />
<hkern u1="&#xd0;" u2="X" k="19" />
<hkern u1="&#xd0;" u2="W" k="33" />
<hkern u1="&#xd0;" u2="V" k="22" />
<hkern u1="&#xd0;" u2="T" k="28" />
<hkern u1="&#xd0;" u2="J" k="25" />
<hkern u1="&#xd0;" u2="&#x3f;" k="11" />
<hkern u1="&#xd0;" u2="&#x2f;" k="28" />
<hkern u1="&#xd0;" u2="&#x2e;" k="22" />
<hkern u1="&#xd0;" u2="&#x2c;" k="22" />
<hkern u1="&#xd1;" u2="y" k="-11" />
<hkern u1="&#xd1;" u2="v" k="-11" />
<hkern u1="&#xd1;" u2="&#x2f;" k="11" />
<hkern u1="&#xd2;" u2="&#x2026;" k="22" />
<hkern u1="&#xd2;" u2="&#x201e;" k="22" />
<hkern u1="&#xd2;" u2="&#x201c;" k="11" />
<hkern u1="&#xd2;" u2="&#x201a;" k="22" />
<hkern u1="&#xd2;" u2="&#x2018;" k="11" />
<hkern u1="&#xd2;" u2="&#x178;" k="25" />
<hkern u1="&#xd2;" u2="&#x153;" k="3" />
<hkern u1="&#xd2;" u2="&#xe7;" k="3" />
<hkern u1="&#xd2;" u2="&#xe6;" k="3" />
<hkern u1="&#xd2;" u2="&#xdd;" k="25" />
<hkern u1="&#xd2;" u2="z" k="6" />
<hkern u1="&#xd2;" u2="x" k="6" />
<hkern u1="&#xd2;" u2="u" k="3" />
<hkern u1="&#xd2;" u2="r" k="3" />
<hkern u1="&#xd2;" u2="q" k="3" />
<hkern u1="&#xd2;" u2="p" k="3" />
<hkern u1="&#xd2;" u2="o" k="3" />
<hkern u1="&#xd2;" u2="n" k="3" />
<hkern u1="&#xd2;" u2="m" k="3" />
<hkern u1="&#xd2;" u2="l" k="6" />
<hkern u1="&#xd2;" u2="k" k="6" />
<hkern u1="&#xd2;" u2="h" k="6" />
<hkern u1="&#xd2;" u2="e" k="3" />
<hkern u1="&#xd2;" u2="d" k="3" />
<hkern u1="&#xd2;" u2="c" k="3" />
<hkern u1="&#xd2;" u2="b" k="6" />
<hkern u1="&#xd2;" u2="a" k="3" />
<hkern u1="&#xd2;" u2="Z" k="25" />
<hkern u1="&#xd2;" u2="Y" k="25" />
<hkern u1="&#xd2;" u2="X" k="19" />
<hkern u1="&#xd2;" u2="W" k="33" />
<hkern u1="&#xd2;" u2="V" k="22" />
<hkern u1="&#xd2;" u2="T" k="28" />
<hkern u1="&#xd2;" u2="J" k="25" />
<hkern u1="&#xd2;" u2="&#x3f;" k="11" />
<hkern u1="&#xd2;" u2="&#x2f;" k="28" />
<hkern u1="&#xd2;" u2="&#x2e;" k="22" />
<hkern u1="&#xd2;" u2="&#x2c;" k="22" />
<hkern u1="&#xd3;" u2="&#x2026;" k="22" />
<hkern u1="&#xd3;" u2="&#x201e;" k="22" />
<hkern u1="&#xd3;" u2="&#x201c;" k="11" />
<hkern u1="&#xd3;" u2="&#x201a;" k="22" />
<hkern u1="&#xd3;" u2="&#x2018;" k="11" />
<hkern u1="&#xd3;" u2="&#x178;" k="25" />
<hkern u1="&#xd3;" u2="&#x153;" k="3" />
<hkern u1="&#xd3;" u2="&#xe7;" k="3" />
<hkern u1="&#xd3;" u2="&#xe6;" k="3" />
<hkern u1="&#xd3;" u2="&#xdd;" k="25" />
<hkern u1="&#xd3;" u2="z" k="6" />
<hkern u1="&#xd3;" u2="x" k="6" />
<hkern u1="&#xd3;" u2="u" k="3" />
<hkern u1="&#xd3;" u2="r" k="3" />
<hkern u1="&#xd3;" u2="q" k="3" />
<hkern u1="&#xd3;" u2="p" k="3" />
<hkern u1="&#xd3;" u2="o" k="3" />
<hkern u1="&#xd3;" u2="n" k="3" />
<hkern u1="&#xd3;" u2="m" k="3" />
<hkern u1="&#xd3;" u2="l" k="6" />
<hkern u1="&#xd3;" u2="k" k="6" />
<hkern u1="&#xd3;" u2="h" k="6" />
<hkern u1="&#xd3;" u2="e" k="3" />
<hkern u1="&#xd3;" u2="d" k="3" />
<hkern u1="&#xd3;" u2="c" k="3" />
<hkern u1="&#xd3;" u2="b" k="6" />
<hkern u1="&#xd3;" u2="a" k="3" />
<hkern u1="&#xd3;" u2="Z" k="25" />
<hkern u1="&#xd3;" u2="Y" k="25" />
<hkern u1="&#xd3;" u2="X" k="19" />
<hkern u1="&#xd3;" u2="W" k="33" />
<hkern u1="&#xd3;" u2="V" k="22" />
<hkern u1="&#xd3;" u2="T" k="28" />
<hkern u1="&#xd3;" u2="J" k="25" />
<hkern u1="&#xd3;" u2="&#x3f;" k="11" />
<hkern u1="&#xd3;" u2="&#x2f;" k="28" />
<hkern u1="&#xd3;" u2="&#x2e;" k="22" />
<hkern u1="&#xd3;" u2="&#x2c;" k="22" />
<hkern u1="&#xd4;" u2="&#x2026;" k="22" />
<hkern u1="&#xd4;" u2="&#x201e;" k="22" />
<hkern u1="&#xd4;" u2="&#x201c;" k="11" />
<hkern u1="&#xd4;" u2="&#x201a;" k="22" />
<hkern u1="&#xd4;" u2="&#x2018;" k="11" />
<hkern u1="&#xd4;" u2="&#x178;" k="25" />
<hkern u1="&#xd4;" u2="&#x153;" k="3" />
<hkern u1="&#xd4;" u2="&#xe7;" k="3" />
<hkern u1="&#xd4;" u2="&#xe6;" k="3" />
<hkern u1="&#xd4;" u2="&#xdd;" k="25" />
<hkern u1="&#xd4;" u2="z" k="6" />
<hkern u1="&#xd4;" u2="x" k="6" />
<hkern u1="&#xd4;" u2="u" k="3" />
<hkern u1="&#xd4;" u2="r" k="3" />
<hkern u1="&#xd4;" u2="q" k="3" />
<hkern u1="&#xd4;" u2="p" k="3" />
<hkern u1="&#xd4;" u2="o" k="3" />
<hkern u1="&#xd4;" u2="n" k="3" />
<hkern u1="&#xd4;" u2="m" k="3" />
<hkern u1="&#xd4;" u2="l" k="6" />
<hkern u1="&#xd4;" u2="k" k="6" />
<hkern u1="&#xd4;" u2="h" k="6" />
<hkern u1="&#xd4;" u2="e" k="3" />
<hkern u1="&#xd4;" u2="d" k="3" />
<hkern u1="&#xd4;" u2="c" k="3" />
<hkern u1="&#xd4;" u2="b" k="6" />
<hkern u1="&#xd4;" u2="a" k="3" />
<hkern u1="&#xd4;" u2="Z" k="25" />
<hkern u1="&#xd4;" u2="Y" k="25" />
<hkern u1="&#xd4;" u2="X" k="19" />
<hkern u1="&#xd4;" u2="W" k="33" />
<hkern u1="&#xd4;" u2="V" k="22" />
<hkern u1="&#xd4;" u2="T" k="28" />
<hkern u1="&#xd4;" u2="J" k="25" />
<hkern u1="&#xd4;" u2="&#x3f;" k="11" />
<hkern u1="&#xd4;" u2="&#x2f;" k="28" />
<hkern u1="&#xd4;" u2="&#x2e;" k="22" />
<hkern u1="&#xd4;" u2="&#x2c;" k="22" />
<hkern u1="&#xd5;" u2="&#x2026;" k="22" />
<hkern u1="&#xd5;" u2="&#x201e;" k="22" />
<hkern u1="&#xd5;" u2="&#x201c;" k="11" />
<hkern u1="&#xd5;" u2="&#x201a;" k="22" />
<hkern u1="&#xd5;" u2="&#x2018;" k="11" />
<hkern u1="&#xd5;" u2="&#x178;" k="25" />
<hkern u1="&#xd5;" u2="&#x153;" k="3" />
<hkern u1="&#xd5;" u2="&#xe7;" k="3" />
<hkern u1="&#xd5;" u2="&#xe6;" k="3" />
<hkern u1="&#xd5;" u2="&#xdd;" k="25" />
<hkern u1="&#xd5;" u2="z" k="6" />
<hkern u1="&#xd5;" u2="x" k="6" />
<hkern u1="&#xd5;" u2="u" k="3" />
<hkern u1="&#xd5;" u2="r" k="3" />
<hkern u1="&#xd5;" u2="q" k="3" />
<hkern u1="&#xd5;" u2="p" k="3" />
<hkern u1="&#xd5;" u2="o" k="3" />
<hkern u1="&#xd5;" u2="n" k="3" />
<hkern u1="&#xd5;" u2="m" k="3" />
<hkern u1="&#xd5;" u2="l" k="6" />
<hkern u1="&#xd5;" u2="k" k="6" />
<hkern u1="&#xd5;" u2="h" k="6" />
<hkern u1="&#xd5;" u2="e" k="3" />
<hkern u1="&#xd5;" u2="d" k="3" />
<hkern u1="&#xd5;" u2="c" k="3" />
<hkern u1="&#xd5;" u2="b" k="6" />
<hkern u1="&#xd5;" u2="a" k="3" />
<hkern u1="&#xd5;" u2="Z" k="25" />
<hkern u1="&#xd5;" u2="Y" k="25" />
<hkern u1="&#xd5;" u2="X" k="19" />
<hkern u1="&#xd5;" u2="W" k="33" />
<hkern u1="&#xd5;" u2="V" k="22" />
<hkern u1="&#xd5;" u2="T" k="28" />
<hkern u1="&#xd5;" u2="J" k="25" />
<hkern u1="&#xd5;" u2="&#x3f;" k="11" />
<hkern u1="&#xd5;" u2="&#x2f;" k="28" />
<hkern u1="&#xd5;" u2="&#x2e;" k="22" />
<hkern u1="&#xd5;" u2="&#x2c;" k="22" />
<hkern u1="&#xd6;" u2="&#x2026;" k="22" />
<hkern u1="&#xd6;" u2="&#x201e;" k="22" />
<hkern u1="&#xd6;" u2="&#x201c;" k="11" />
<hkern u1="&#xd6;" u2="&#x201a;" k="22" />
<hkern u1="&#xd6;" u2="&#x2018;" k="11" />
<hkern u1="&#xd6;" u2="&#x178;" k="25" />
<hkern u1="&#xd6;" u2="&#x153;" k="3" />
<hkern u1="&#xd6;" u2="&#xe7;" k="3" />
<hkern u1="&#xd6;" u2="&#xe6;" k="3" />
<hkern u1="&#xd6;" u2="&#xdd;" k="25" />
<hkern u1="&#xd6;" u2="z" k="6" />
<hkern u1="&#xd6;" u2="x" k="6" />
<hkern u1="&#xd6;" u2="u" k="3" />
<hkern u1="&#xd6;" u2="r" k="3" />
<hkern u1="&#xd6;" u2="q" k="3" />
<hkern u1="&#xd6;" u2="p" k="3" />
<hkern u1="&#xd6;" u2="o" k="3" />
<hkern u1="&#xd6;" u2="n" k="3" />
<hkern u1="&#xd6;" u2="m" k="3" />
<hkern u1="&#xd6;" u2="l" k="6" />
<hkern u1="&#xd6;" u2="k" k="6" />
<hkern u1="&#xd6;" u2="h" k="6" />
<hkern u1="&#xd6;" u2="e" k="3" />
<hkern u1="&#xd6;" u2="d" k="3" />
<hkern u1="&#xd6;" u2="c" k="3" />
<hkern u1="&#xd6;" u2="b" k="6" />
<hkern u1="&#xd6;" u2="a" k="3" />
<hkern u1="&#xd6;" u2="Z" k="25" />
<hkern u1="&#xd6;" u2="Y" k="25" />
<hkern u1="&#xd6;" u2="X" k="19" />
<hkern u1="&#xd6;" u2="W" k="33" />
<hkern u1="&#xd6;" u2="V" k="22" />
<hkern u1="&#xd6;" u2="T" k="28" />
<hkern u1="&#xd6;" u2="J" k="25" />
<hkern u1="&#xd6;" u2="&#x3f;" k="11" />
<hkern u1="&#xd6;" u2="&#x2f;" k="28" />
<hkern u1="&#xd6;" u2="&#x2e;" k="22" />
<hkern u1="&#xd6;" u2="&#x2c;" k="22" />
<hkern u1="&#xd8;" u2="&#x2026;" k="22" />
<hkern u1="&#xd8;" u2="&#x201e;" k="22" />
<hkern u1="&#xd8;" u2="&#x201c;" k="11" />
<hkern u1="&#xd8;" u2="&#x201a;" k="22" />
<hkern u1="&#xd8;" u2="&#x2018;" k="11" />
<hkern u1="&#xd8;" u2="&#x178;" k="25" />
<hkern u1="&#xd8;" u2="&#x153;" k="3" />
<hkern u1="&#xd8;" u2="&#xe7;" k="3" />
<hkern u1="&#xd8;" u2="&#xe6;" k="3" />
<hkern u1="&#xd8;" u2="&#xdd;" k="25" />
<hkern u1="&#xd8;" u2="z" k="6" />
<hkern u1="&#xd8;" u2="x" k="6" />
<hkern u1="&#xd8;" u2="u" k="3" />
<hkern u1="&#xd8;" u2="r" k="3" />
<hkern u1="&#xd8;" u2="q" k="3" />
<hkern u1="&#xd8;" u2="p" k="3" />
<hkern u1="&#xd8;" u2="o" k="3" />
<hkern u1="&#xd8;" u2="n" k="3" />
<hkern u1="&#xd8;" u2="m" k="3" />
<hkern u1="&#xd8;" u2="l" k="6" />
<hkern u1="&#xd8;" u2="k" k="6" />
<hkern u1="&#xd8;" u2="h" k="6" />
<hkern u1="&#xd8;" u2="e" k="3" />
<hkern u1="&#xd8;" u2="d" k="3" />
<hkern u1="&#xd8;" u2="c" k="3" />
<hkern u1="&#xd8;" u2="b" k="6" />
<hkern u1="&#xd8;" u2="a" k="3" />
<hkern u1="&#xd8;" u2="Z" k="25" />
<hkern u1="&#xd8;" u2="Y" k="25" />
<hkern u1="&#xd8;" u2="X" k="19" />
<hkern u1="&#xd8;" u2="W" k="33" />
<hkern u1="&#xd8;" u2="V" k="22" />
<hkern u1="&#xd8;" u2="T" k="28" />
<hkern u1="&#xd8;" u2="J" k="25" />
<hkern u1="&#xd8;" u2="&#x3f;" k="11" />
<hkern u1="&#xd8;" u2="&#x2f;" k="28" />
<hkern u1="&#xd8;" u2="&#x2e;" k="22" />
<hkern u1="&#xd8;" u2="&#x2c;" k="22" />
<hkern u1="&#xd9;" u2="&#x2026;" k="6" />
<hkern u1="&#xd9;" u2="&#x201e;" k="6" />
<hkern u1="&#xd9;" u2="&#x201a;" k="6" />
<hkern u1="&#xd9;" u2="&#xc6;" k="-11" />
<hkern u1="&#xd9;" u2="&#xc5;" k="-11" />
<hkern u1="&#xd9;" u2="&#xc4;" k="-11" />
<hkern u1="&#xd9;" u2="&#xc3;" k="-11" />
<hkern u1="&#xd9;" u2="&#xc2;" k="-11" />
<hkern u1="&#xd9;" u2="&#xc1;" k="-11" />
<hkern u1="&#xd9;" u2="&#xc0;" k="-11" />
<hkern u1="&#xd9;" u2="y" k="-11" />
<hkern u1="&#xd9;" u2="v" k="-11" />
<hkern u1="&#xd9;" u2="J" k="6" />
<hkern u1="&#xd9;" u2="A" k="-11" />
<hkern u1="&#xd9;" u2="&#x2e;" k="6" />
<hkern u1="&#xd9;" u2="&#x2c;" k="6" />
<hkern u1="&#xda;" u2="&#x2026;" k="6" />
<hkern u1="&#xda;" u2="&#x201e;" k="6" />
<hkern u1="&#xda;" u2="&#x201a;" k="6" />
<hkern u1="&#xda;" u2="&#xc6;" k="-11" />
<hkern u1="&#xda;" u2="&#xc5;" k="-11" />
<hkern u1="&#xda;" u2="&#xc4;" k="-11" />
<hkern u1="&#xda;" u2="&#xc3;" k="-11" />
<hkern u1="&#xda;" u2="&#xc2;" k="-11" />
<hkern u1="&#xda;" u2="&#xc1;" k="-11" />
<hkern u1="&#xda;" u2="&#xc0;" k="-11" />
<hkern u1="&#xda;" u2="y" k="-11" />
<hkern u1="&#xda;" u2="v" k="-11" />
<hkern u1="&#xda;" u2="J" k="6" />
<hkern u1="&#xda;" u2="A" k="-11" />
<hkern u1="&#xda;" u2="&#x2e;" k="6" />
<hkern u1="&#xda;" u2="&#x2c;" k="6" />
<hkern u1="&#xdb;" u2="&#x2026;" k="6" />
<hkern u1="&#xdb;" u2="&#x201e;" k="6" />
<hkern u1="&#xdb;" u2="&#x201a;" k="6" />
<hkern u1="&#xdb;" u2="&#xc6;" k="-11" />
<hkern u1="&#xdb;" u2="&#xc5;" k="-11" />
<hkern u1="&#xdb;" u2="&#xc4;" k="-11" />
<hkern u1="&#xdb;" u2="&#xc3;" k="-11" />
<hkern u1="&#xdb;" u2="&#xc2;" k="-11" />
<hkern u1="&#xdb;" u2="&#xc1;" k="-11" />
<hkern u1="&#xdb;" u2="&#xc0;" k="-11" />
<hkern u1="&#xdb;" u2="y" k="-11" />
<hkern u1="&#xdb;" u2="v" k="-11" />
<hkern u1="&#xdb;" u2="J" k="6" />
<hkern u1="&#xdb;" u2="A" k="-11" />
<hkern u1="&#xdb;" u2="&#x2e;" k="6" />
<hkern u1="&#xdb;" u2="&#x2c;" k="6" />
<hkern u1="&#xdc;" u2="&#x2026;" k="6" />
<hkern u1="&#xdc;" u2="&#x201e;" k="6" />
<hkern u1="&#xdc;" u2="&#x201a;" k="6" />
<hkern u1="&#xdc;" u2="&#xc6;" k="-11" />
<hkern u1="&#xdc;" u2="&#xc5;" k="-11" />
<hkern u1="&#xdc;" u2="&#xc4;" k="-11" />
<hkern u1="&#xdc;" u2="&#xc3;" k="-11" />
<hkern u1="&#xdc;" u2="&#xc2;" k="-11" />
<hkern u1="&#xdc;" u2="&#xc1;" k="-11" />
<hkern u1="&#xdc;" u2="&#xc0;" k="-11" />
<hkern u1="&#xdc;" u2="y" k="-11" />
<hkern u1="&#xdc;" u2="v" k="-11" />
<hkern u1="&#xdc;" u2="J" k="6" />
<hkern u1="&#xdc;" u2="A" k="-11" />
<hkern u1="&#xdc;" u2="&#x2e;" k="6" />
<hkern u1="&#xdc;" u2="&#x2c;" k="6" />
<hkern u1="&#xdd;" u2="&#x203a;" k="22" />
<hkern u1="&#xdd;" u2="&#x2039;" k="33" />
<hkern u1="&#xdd;" u2="&#x2026;" k="28" />
<hkern u1="&#xdd;" u2="&#x201e;" k="28" />
<hkern u1="&#xdd;" u2="&#x201a;" k="28" />
<hkern u1="&#xdd;" u2="&#x2014;" k="28" />
<hkern u1="&#xdd;" u2="&#x2013;" k="28" />
<hkern u1="&#xdd;" u2="&#x153;" k="30" />
<hkern u1="&#xdd;" u2="&#x152;" k="3" />
<hkern u1="&#xdd;" u2="&#xe7;" k="30" />
<hkern u1="&#xdd;" u2="&#xe6;" k="30" />
<hkern u1="&#xdd;" u2="&#xd8;" k="3" />
<hkern u1="&#xdd;" u2="&#xd6;" k="3" />
<hkern u1="&#xdd;" u2="&#xd5;" k="3" />
<hkern u1="&#xdd;" u2="&#xd4;" k="3" />
<hkern u1="&#xdd;" u2="&#xd3;" k="3" />
<hkern u1="&#xdd;" u2="&#xd2;" k="3" />
<hkern u1="&#xdd;" u2="&#xc6;" k="17" />
<hkern u1="&#xdd;" u2="&#xc5;" k="17" />
<hkern u1="&#xdd;" u2="&#xc4;" k="17" />
<hkern u1="&#xdd;" u2="&#xc3;" k="17" />
<hkern u1="&#xdd;" u2="&#xc2;" k="17" />
<hkern u1="&#xdd;" u2="&#xc1;" k="17" />
<hkern u1="&#xdd;" u2="&#xc0;" k="17" />
<hkern u1="&#xdd;" u2="&#xbb;" k="22" />
<hkern u1="&#xdd;" u2="&#xae;" k="17" />
<hkern u1="&#xdd;" u2="&#xab;" k="33" />
<hkern u1="&#xdd;" u2="&#xa9;" k="17" />
<hkern u1="&#xdd;" u2="&#x7d;" k="-28" />
<hkern u1="&#xdd;" u2="z" k="17" />
<hkern u1="&#xdd;" u2="y" k="11" />
<hkern u1="&#xdd;" u2="x" k="11" />
<hkern u1="&#xdd;" u2="w" k="6" />
<hkern u1="&#xdd;" u2="v" k="11" />
<hkern u1="&#xdd;" u2="u" k="22" />
<hkern u1="&#xdd;" u2="t" k="11" />
<hkern u1="&#xdd;" u2="s" k="28" />
<hkern u1="&#xdd;" u2="r" k="22" />
<hkern u1="&#xdd;" u2="q" k="30" />
<hkern u1="&#xdd;" u2="p" k="22" />
<hkern u1="&#xdd;" u2="o" k="30" />
<hkern u1="&#xdd;" u2="n" k="22" />
<hkern u1="&#xdd;" u2="m" k="22" />
<hkern u1="&#xdd;" u2="g" k="22" />
<hkern u1="&#xdd;" u2="f" k="6" />
<hkern u1="&#xdd;" u2="e" k="30" />
<hkern u1="&#xdd;" u2="d" k="30" />
<hkern u1="&#xdd;" u2="c" k="30" />
<hkern u1="&#xdd;" u2="a" k="30" />
<hkern u1="&#xdd;" u2="]" k="-28" />
<hkern u1="&#xdd;" u2="X" k="-11" />
<hkern u1="&#xdd;" u2="V" k="-17" />
<hkern u1="&#xdd;" u2="T" k="-11" />
<hkern u1="&#xdd;" u2="S" k="-17" />
<hkern u1="&#xdd;" u2="Q" k="3" />
<hkern u1="&#xdd;" u2="O" k="3" />
<hkern u1="&#xdd;" u2="J" k="39" />
<hkern u1="&#xdd;" u2="G" k="3" />
<hkern u1="&#xdd;" u2="C" k="3" />
<hkern u1="&#xdd;" u2="A" k="17" />
<hkern u1="&#xdd;" u2="&#x40;" k="17" />
<hkern u1="&#xdd;" u2="&#x3b;" k="17" />
<hkern u1="&#xdd;" u2="&#x3a;" k="17" />
<hkern u1="&#xdd;" u2="&#x2e;" k="28" />
<hkern u1="&#xdd;" u2="&#x2d;" k="28" />
<hkern u1="&#xdd;" u2="&#x2c;" k="28" />
<hkern u1="&#xdd;" u2="&#x29;" k="-28" />
<hkern u1="&#xdd;" u2="&#x26;" k="17" />
<hkern u1="&#xde;" u2="&#x2026;" k="22" />
<hkern u1="&#xde;" u2="&#x201e;" k="22" />
<hkern u1="&#xde;" u2="&#x201c;" k="11" />
<hkern u1="&#xde;" u2="&#x201a;" k="22" />
<hkern u1="&#xde;" u2="&#x2018;" k="11" />
<hkern u1="&#xde;" u2="&#x178;" k="25" />
<hkern u1="&#xde;" u2="&#x153;" k="3" />
<hkern u1="&#xde;" u2="&#xe7;" k="3" />
<hkern u1="&#xde;" u2="&#xe6;" k="3" />
<hkern u1="&#xde;" u2="&#xdd;" k="25" />
<hkern u1="&#xde;" u2="z" k="6" />
<hkern u1="&#xde;" u2="x" k="6" />
<hkern u1="&#xde;" u2="u" k="3" />
<hkern u1="&#xde;" u2="r" k="3" />
<hkern u1="&#xde;" u2="q" k="3" />
<hkern u1="&#xde;" u2="p" k="3" />
<hkern u1="&#xde;" u2="o" k="3" />
<hkern u1="&#xde;" u2="n" k="3" />
<hkern u1="&#xde;" u2="m" k="3" />
<hkern u1="&#xde;" u2="l" k="6" />
<hkern u1="&#xde;" u2="k" k="6" />
<hkern u1="&#xde;" u2="h" k="6" />
<hkern u1="&#xde;" u2="e" k="3" />
<hkern u1="&#xde;" u2="d" k="3" />
<hkern u1="&#xde;" u2="c" k="3" />
<hkern u1="&#xde;" u2="b" k="6" />
<hkern u1="&#xde;" u2="a" k="3" />
<hkern u1="&#xde;" u2="Z" k="25" />
<hkern u1="&#xde;" u2="Y" k="25" />
<hkern u1="&#xde;" u2="X" k="19" />
<hkern u1="&#xde;" u2="W" k="33" />
<hkern u1="&#xde;" u2="V" k="22" />
<hkern u1="&#xde;" u2="T" k="28" />
<hkern u1="&#xde;" u2="J" k="25" />
<hkern u1="&#xde;" u2="&#x3f;" k="11" />
<hkern u1="&#xde;" u2="&#x2f;" k="28" />
<hkern u1="&#xde;" u2="&#x2e;" k="22" />
<hkern u1="&#xde;" u2="&#x2c;" k="22" />
<hkern u1="&#xdf;" u2="&#x2122;" k="17" />
<hkern u1="&#xdf;" u2="&#x2026;" k="11" />
<hkern u1="&#xdf;" u2="&#x201e;" k="11" />
<hkern u1="&#xdf;" u2="&#x201c;" k="22" />
<hkern u1="&#xdf;" u2="&#x201a;" k="11" />
<hkern u1="&#xdf;" u2="&#x2018;" k="22" />
<hkern u1="&#xdf;" u2="z" k="8" />
<hkern u1="&#xdf;" u2="y" k="-3" />
<hkern u1="&#xdf;" u2="x" k="3" />
<hkern u1="&#xdf;" u2="v" k="-3" />
<hkern u1="&#xdf;" u2="t" k="6" />
<hkern u1="&#xdf;" u2="s" k="-3" />
<hkern u1="&#xdf;" u2="g" k="6" />
<hkern u1="&#xdf;" u2="\" k="6" />
<hkern u1="&#xdf;" u2="&#x3f;" k="11" />
<hkern u1="&#xdf;" u2="&#x3b;" k="6" />
<hkern u1="&#xdf;" u2="&#x3a;" k="6" />
<hkern u1="&#xdf;" u2="&#x2f;" k="6" />
<hkern u1="&#xdf;" u2="&#x2e;" k="11" />
<hkern u1="&#xdf;" u2="&#x2c;" k="11" />
<hkern u1="&#xe0;" u2="&#x2122;" k="11" />
<hkern u1="&#xe0;" u2="&#x201c;" k="17" />
<hkern u1="&#xe0;" u2="&#x2018;" k="17" />
<hkern u1="&#xe0;" u2="y" k="3" />
<hkern u1="&#xe0;" u2="v" k="3" />
<hkern u1="&#xe0;" u2="t" k="6" />
<hkern u1="&#xe0;" u2="\" k="6" />
<hkern u1="&#xe0;" u2="&#x3f;" k="6" />
<hkern u1="&#xe1;" u2="&#x2122;" k="11" />
<hkern u1="&#xe1;" u2="&#x201c;" k="17" />
<hkern u1="&#xe1;" u2="&#x2018;" k="17" />
<hkern u1="&#xe1;" u2="y" k="3" />
<hkern u1="&#xe1;" u2="v" k="3" />
<hkern u1="&#xe1;" u2="t" k="6" />
<hkern u1="&#xe1;" u2="\" k="6" />
<hkern u1="&#xe1;" u2="&#x3f;" k="6" />
<hkern u1="&#xe2;" u2="&#x2122;" k="11" />
<hkern u1="&#xe2;" u2="&#x201c;" k="17" />
<hkern u1="&#xe2;" u2="&#x2018;" k="17" />
<hkern u1="&#xe2;" u2="y" k="3" />
<hkern u1="&#xe2;" u2="v" k="3" />
<hkern u1="&#xe2;" u2="t" k="6" />
<hkern u1="&#xe2;" u2="\" k="6" />
<hkern u1="&#xe2;" u2="&#x3f;" k="6" />
<hkern u1="&#xe4;" u2="&#x2122;" k="11" />
<hkern u1="&#xe4;" u2="&#x201c;" k="17" />
<hkern u1="&#xe4;" u2="&#x2018;" k="17" />
<hkern u1="&#xe4;" u2="y" k="3" />
<hkern u1="&#xe4;" u2="v" k="3" />
<hkern u1="&#xe4;" u2="t" k="6" />
<hkern u1="&#xe4;" u2="\" k="6" />
<hkern u1="&#xe4;" u2="&#x3f;" k="6" />
<hkern u1="&#xe6;" u2="y" k="6" />
<hkern u1="&#xe6;" u2="x" k="8" />
<hkern u1="&#xe6;" u2="v" k="6" />
<hkern u1="&#xe8;" u2="y" k="6" />
<hkern u1="&#xe8;" u2="x" k="8" />
<hkern u1="&#xe8;" u2="v" k="6" />
<hkern u1="&#xe9;" u2="y" k="6" />
<hkern u1="&#xe9;" u2="x" k="8" />
<hkern u1="&#xe9;" u2="v" k="6" />
<hkern u1="&#xea;" u2="y" k="6" />
<hkern u1="&#xea;" u2="x" k="8" />
<hkern u1="&#xea;" u2="v" k="6" />
<hkern u1="&#xeb;" u2="y" k="6" />
<hkern u1="&#xeb;" u2="x" k="8" />
<hkern u1="&#xeb;" u2="v" k="6" />
<hkern u1="&#xf1;" u2="&#x2122;" k="11" />
<hkern u1="&#xf1;" u2="&#x201c;" k="17" />
<hkern u1="&#xf1;" u2="&#x2018;" k="17" />
<hkern u1="&#xf1;" u2="y" k="3" />
<hkern u1="&#xf1;" u2="v" k="3" />
<hkern u1="&#xf1;" u2="t" k="6" />
<hkern u1="&#xf1;" u2="\" k="6" />
<hkern u1="&#xf1;" u2="&#x3f;" k="6" />
<hkern u1="&#xf3;" u2="&#x2122;" k="17" />
<hkern u1="&#xf3;" u2="&#x2026;" k="11" />
<hkern u1="&#xf3;" u2="&#x201e;" k="11" />
<hkern u1="&#xf3;" u2="&#x201c;" k="22" />
<hkern u1="&#xf3;" u2="&#x201a;" k="11" />
<hkern u1="&#xf3;" u2="&#x2018;" k="22" />
<hkern u1="&#xf3;" u2="z" k="8" />
<hkern u1="&#xf3;" u2="y" k="-3" />
<hkern u1="&#xf3;" u2="x" k="3" />
<hkern u1="&#xf3;" u2="v" k="-3" />
<hkern u1="&#xf3;" u2="t" k="6" />
<hkern u1="&#xf3;" u2="s" k="-3" />
<hkern u1="&#xf3;" u2="\" k="6" />
<hkern u1="&#xf3;" u2="&#x3f;" k="11" />
<hkern u1="&#xf3;" u2="&#x3b;" k="6" />
<hkern u1="&#xf3;" u2="&#x3a;" k="6" />
<hkern u1="&#xf3;" u2="&#x2f;" k="6" />
<hkern u1="&#xf3;" u2="&#x2e;" k="11" />
<hkern u1="&#xf3;" u2="&#x2c;" k="11" />
<hkern u1="&#xf4;" u2="&#x2122;" k="17" />
<hkern u1="&#xf4;" u2="&#x2026;" k="11" />
<hkern u1="&#xf4;" u2="&#x201e;" k="11" />
<hkern u1="&#xf4;" u2="&#x201c;" k="22" />
<hkern u1="&#xf4;" u2="&#x201a;" k="11" />
<hkern u1="&#xf4;" u2="&#x2018;" k="22" />
<hkern u1="&#xf4;" u2="z" k="8" />
<hkern u1="&#xf4;" u2="y" k="-3" />
<hkern u1="&#xf4;" u2="x" k="3" />
<hkern u1="&#xf4;" u2="v" k="-3" />
<hkern u1="&#xf4;" u2="t" k="6" />
<hkern u1="&#xf4;" u2="s" k="-3" />
<hkern u1="&#xf4;" u2="\" k="6" />
<hkern u1="&#xf4;" u2="&#x3f;" k="11" />
<hkern u1="&#xf4;" u2="&#x3b;" k="6" />
<hkern u1="&#xf4;" u2="&#x3a;" k="6" />
<hkern u1="&#xf4;" u2="&#x2f;" k="6" />
<hkern u1="&#xf4;" u2="&#x2e;" k="11" />
<hkern u1="&#xf4;" u2="&#x2c;" k="11" />
<hkern u1="&#xf5;" u2="&#x2122;" k="17" />
<hkern u1="&#xf5;" u2="&#x2026;" k="11" />
<hkern u1="&#xf5;" u2="&#x201e;" k="11" />
<hkern u1="&#xf5;" u2="&#x201c;" k="22" />
<hkern u1="&#xf5;" u2="&#x201a;" k="11" />
<hkern u1="&#xf5;" u2="&#x2018;" k="22" />
<hkern u1="&#xf5;" u2="z" k="8" />
<hkern u1="&#xf5;" u2="y" k="-3" />
<hkern u1="&#xf5;" u2="x" k="3" />
<hkern u1="&#xf5;" u2="v" k="-3" />
<hkern u1="&#xf5;" u2="t" k="6" />
<hkern u1="&#xf5;" u2="s" k="-3" />
<hkern u1="&#xf5;" u2="\" k="6" />
<hkern u1="&#xf5;" u2="&#x3f;" k="11" />
<hkern u1="&#xf5;" u2="&#x3b;" k="6" />
<hkern u1="&#xf5;" u2="&#x3a;" k="6" />
<hkern u1="&#xf5;" u2="&#x2f;" k="6" />
<hkern u1="&#xf5;" u2="&#x2e;" k="11" />
<hkern u1="&#xf5;" u2="&#x2c;" k="11" />
<hkern u1="&#xf6;" u2="&#x2122;" k="17" />
<hkern u1="&#xf6;" u2="&#x2026;" k="11" />
<hkern u1="&#xf6;" u2="&#x201e;" k="11" />
<hkern u1="&#xf6;" u2="&#x201c;" k="22" />
<hkern u1="&#xf6;" u2="&#x201a;" k="11" />
<hkern u1="&#xf6;" u2="&#x2018;" k="22" />
<hkern u1="&#xf6;" u2="z" k="8" />
<hkern u1="&#xf6;" u2="y" k="-3" />
<hkern u1="&#xf6;" u2="x" k="3" />
<hkern u1="&#xf6;" u2="v" k="-3" />
<hkern u1="&#xf6;" u2="t" k="6" />
<hkern u1="&#xf6;" u2="s" k="-3" />
<hkern u1="&#xf6;" u2="\" k="6" />
<hkern u1="&#xf6;" u2="&#x3f;" k="11" />
<hkern u1="&#xf6;" u2="&#x3b;" k="6" />
<hkern u1="&#xf6;" u2="&#x3a;" k="6" />
<hkern u1="&#xf6;" u2="&#x2f;" k="6" />
<hkern u1="&#xf6;" u2="&#x2e;" k="11" />
<hkern u1="&#xf6;" u2="&#x2c;" k="11" />
<hkern u1="&#xf8;" u2="&#x2122;" k="17" />
<hkern u1="&#xf8;" u2="&#x2026;" k="11" />
<hkern u1="&#xf8;" u2="&#x201e;" k="11" />
<hkern u1="&#xf8;" u2="&#x201c;" k="22" />
<hkern u1="&#xf8;" u2="&#x201a;" k="11" />
<hkern u1="&#xf8;" u2="&#x2018;" k="22" />
<hkern u1="&#xf8;" u2="z" k="8" />
<hkern u1="&#xf8;" u2="y" k="-3" />
<hkern u1="&#xf8;" u2="x" k="3" />
<hkern u1="&#xf8;" u2="v" k="-3" />
<hkern u1="&#xf8;" u2="t" k="6" />
<hkern u1="&#xf8;" u2="s" k="-3" />
<hkern u1="&#xf8;" u2="\" k="6" />
<hkern u1="&#xf8;" u2="&#x3f;" k="11" />
<hkern u1="&#xf8;" u2="&#x3b;" k="6" />
<hkern u1="&#xf8;" u2="&#x3a;" k="6" />
<hkern u1="&#xf8;" u2="&#x2f;" k="6" />
<hkern u1="&#xf8;" u2="&#x2e;" k="11" />
<hkern u1="&#xf8;" u2="&#x2c;" k="11" />
<hkern u1="&#x152;" u2="&#x153;" k="14" />
<hkern u1="&#x152;" u2="&#xe7;" k="14" />
<hkern u1="&#x152;" u2="&#xe6;" k="14" />
<hkern u1="&#x152;" u2="&#xae;" k="11" />
<hkern u1="&#x152;" u2="&#xa9;" k="11" />
<hkern u1="&#x152;" u2="y" k="11" />
<hkern u1="&#x152;" u2="v" k="11" />
<hkern u1="&#x152;" u2="q" k="14" />
<hkern u1="&#x152;" u2="o" k="14" />
<hkern u1="&#x152;" u2="g" k="3" />
<hkern u1="&#x152;" u2="f" k="6" />
<hkern u1="&#x152;" u2="e" k="14" />
<hkern u1="&#x152;" u2="d" k="14" />
<hkern u1="&#x152;" u2="c" k="14" />
<hkern u1="&#x152;" u2="a" k="14" />
<hkern u1="&#x152;" u2="T" k="-8" />
<hkern u1="&#x152;" u2="&#x40;" k="11" />
<hkern u1="&#x153;" u2="y" k="6" />
<hkern u1="&#x153;" u2="x" k="8" />
<hkern u1="&#x153;" u2="v" k="6" />
<hkern u1="&#x178;" u2="&#x203a;" k="22" />
<hkern u1="&#x178;" u2="&#x2039;" k="33" />
<hkern u1="&#x178;" u2="&#x2026;" k="28" />
<hkern u1="&#x178;" u2="&#x201e;" k="28" />
<hkern u1="&#x178;" u2="&#x201a;" k="28" />
<hkern u1="&#x178;" u2="&#x2014;" k="28" />
<hkern u1="&#x178;" u2="&#x2013;" k="28" />
<hkern u1="&#x178;" u2="&#x153;" k="30" />
<hkern u1="&#x178;" u2="&#x152;" k="3" />
<hkern u1="&#x178;" u2="&#xe7;" k="30" />
<hkern u1="&#x178;" u2="&#xe6;" k="30" />
<hkern u1="&#x178;" u2="&#xd8;" k="3" />
<hkern u1="&#x178;" u2="&#xd6;" k="3" />
<hkern u1="&#x178;" u2="&#xd5;" k="3" />
<hkern u1="&#x178;" u2="&#xd4;" k="3" />
<hkern u1="&#x178;" u2="&#xd3;" k="3" />
<hkern u1="&#x178;" u2="&#xd2;" k="3" />
<hkern u1="&#x178;" u2="&#xc6;" k="17" />
<hkern u1="&#x178;" u2="&#xc5;" k="17" />
<hkern u1="&#x178;" u2="&#xc4;" k="17" />
<hkern u1="&#x178;" u2="&#xc3;" k="17" />
<hkern u1="&#x178;" u2="&#xc2;" k="17" />
<hkern u1="&#x178;" u2="&#xc1;" k="17" />
<hkern u1="&#x178;" u2="&#xc0;" k="17" />
<hkern u1="&#x178;" u2="&#xbb;" k="22" />
<hkern u1="&#x178;" u2="&#xae;" k="17" />
<hkern u1="&#x178;" u2="&#xab;" k="33" />
<hkern u1="&#x178;" u2="&#xa9;" k="17" />
<hkern u1="&#x178;" u2="&#x7d;" k="-28" />
<hkern u1="&#x178;" u2="z" k="17" />
<hkern u1="&#x178;" u2="y" k="11" />
<hkern u1="&#x178;" u2="x" k="11" />
<hkern u1="&#x178;" u2="w" k="6" />
<hkern u1="&#x178;" u2="v" k="11" />
<hkern u1="&#x178;" u2="u" k="22" />
<hkern u1="&#x178;" u2="t" k="11" />
<hkern u1="&#x178;" u2="s" k="28" />
<hkern u1="&#x178;" u2="r" k="22" />
<hkern u1="&#x178;" u2="q" k="30" />
<hkern u1="&#x178;" u2="p" k="22" />
<hkern u1="&#x178;" u2="o" k="30" />
<hkern u1="&#x178;" u2="n" k="22" />
<hkern u1="&#x178;" u2="m" k="22" />
<hkern u1="&#x178;" u2="g" k="22" />
<hkern u1="&#x178;" u2="f" k="6" />
<hkern u1="&#x178;" u2="e" k="30" />
<hkern u1="&#x178;" u2="d" k="30" />
<hkern u1="&#x178;" u2="c" k="30" />
<hkern u1="&#x178;" u2="a" k="30" />
<hkern u1="&#x178;" u2="]" k="-28" />
<hkern u1="&#x178;" u2="X" k="-11" />
<hkern u1="&#x178;" u2="V" k="-17" />
<hkern u1="&#x178;" u2="T" k="-11" />
<hkern u1="&#x178;" u2="S" k="-17" />
<hkern u1="&#x178;" u2="Q" k="3" />
<hkern u1="&#x178;" u2="O" k="3" />
<hkern u1="&#x178;" u2="J" k="39" />
<hkern u1="&#x178;" u2="G" k="3" />
<hkern u1="&#x178;" u2="C" k="3" />
<hkern u1="&#x178;" u2="A" k="17" />
<hkern u1="&#x178;" u2="&#x40;" k="17" />
<hkern u1="&#x178;" u2="&#x3b;" k="17" />
<hkern u1="&#x178;" u2="&#x3a;" k="17" />
<hkern u1="&#x178;" u2="&#x2e;" k="28" />
<hkern u1="&#x178;" u2="&#x2d;" k="28" />
<hkern u1="&#x178;" u2="&#x2c;" k="28" />
<hkern u1="&#x178;" u2="&#x29;" k="-28" />
<hkern u1="&#x178;" u2="&#x26;" k="17" />
<hkern u1="&#x2013;" u2="&#x178;" k="28" />
<hkern u1="&#x2013;" u2="&#x153;" k="6" />
<hkern u1="&#x2013;" u2="&#xe7;" k="6" />
<hkern u1="&#x2013;" u2="&#xe6;" k="6" />
<hkern u1="&#x2013;" u2="&#xdd;" k="28" />
<hkern u1="&#x2013;" u2="&#xc6;" k="-6" />
<hkern u1="&#x2013;" u2="&#xc5;" k="-6" />
<hkern u1="&#x2013;" u2="&#xc4;" k="-6" />
<hkern u1="&#x2013;" u2="&#xc3;" k="-6" />
<hkern u1="&#x2013;" u2="&#xc2;" k="-6" />
<hkern u1="&#x2013;" u2="&#xc1;" k="-6" />
<hkern u1="&#x2013;" u2="&#xc0;" k="-6" />
<hkern u1="&#x2013;" u2="z" k="17" />
<hkern u1="&#x2013;" u2="y" k="6" />
<hkern u1="&#x2013;" u2="x" k="17" />
<hkern u1="&#x2013;" u2="v" k="6" />
<hkern u1="&#x2013;" u2="q" k="6" />
<hkern u1="&#x2013;" u2="o" k="6" />
<hkern u1="&#x2013;" u2="e" k="6" />
<hkern u1="&#x2013;" u2="d" k="6" />
<hkern u1="&#x2013;" u2="c" k="6" />
<hkern u1="&#x2013;" u2="a" k="6" />
<hkern u1="&#x2013;" u2="Z" k="6" />
<hkern u1="&#x2013;" u2="Y" k="28" />
<hkern u1="&#x2013;" u2="X" k="17" />
<hkern u1="&#x2013;" u2="W" k="22" />
<hkern u1="&#x2013;" u2="V" k="28" />
<hkern u1="&#x2013;" u2="T" k="39" />
<hkern u1="&#x2013;" u2="A" k="-6" />
<hkern u1="&#x2013;" u2="&#x37;" k="17" />
<hkern u1="&#x2013;" u2="&#x31;" k="11" />
<hkern u1="&#x2014;" u2="&#x178;" k="28" />
<hkern u1="&#x2014;" u2="&#x153;" k="6" />
<hkern u1="&#x2014;" u2="&#xe7;" k="6" />
<hkern u1="&#x2014;" u2="&#xe6;" k="6" />
<hkern u1="&#x2014;" u2="&#xdd;" k="28" />
<hkern u1="&#x2014;" u2="&#xc6;" k="-6" />
<hkern u1="&#x2014;" u2="&#xc5;" k="-6" />
<hkern u1="&#x2014;" u2="&#xc4;" k="-6" />
<hkern u1="&#x2014;" u2="&#xc3;" k="-6" />
<hkern u1="&#x2014;" u2="&#xc2;" k="-6" />
<hkern u1="&#x2014;" u2="&#xc1;" k="-6" />
<hkern u1="&#x2014;" u2="&#xc0;" k="-6" />
<hkern u1="&#x2014;" u2="z" k="17" />
<hkern u1="&#x2014;" u2="y" k="6" />
<hkern u1="&#x2014;" u2="x" k="17" />
<hkern u1="&#x2014;" u2="v" k="6" />
<hkern u1="&#x2014;" u2="q" k="6" />
<hkern u1="&#x2014;" u2="o" k="6" />
<hkern u1="&#x2014;" u2="e" k="6" />
<hkern u1="&#x2014;" u2="d" k="6" />
<hkern u1="&#x2014;" u2="c" k="6" />
<hkern u1="&#x2014;" u2="a" k="6" />
<hkern u1="&#x2014;" u2="Z" k="6" />
<hkern u1="&#x2014;" u2="Y" k="28" />
<hkern u1="&#x2014;" u2="X" k="17" />
<hkern u1="&#x2014;" u2="W" k="22" />
<hkern u1="&#x2014;" u2="V" k="28" />
<hkern u1="&#x2014;" u2="T" k="39" />
<hkern u1="&#x2014;" u2="A" k="-6" />
<hkern u1="&#x2014;" u2="&#x37;" k="17" />
<hkern u1="&#x2014;" u2="&#x31;" k="11" />
<hkern u1="&#x2018;" u2="&#x178;" k="-17" />
<hkern u1="&#x2018;" u2="&#x153;" k="17" />
<hkern u1="&#x2018;" u2="&#xe7;" k="17" />
<hkern u1="&#x2018;" u2="&#xe6;" k="17" />
<hkern u1="&#x2018;" u2="&#xdd;" k="-17" />
<hkern u1="&#x2018;" u2="&#xc6;" k="22" />
<hkern u1="&#x2018;" u2="&#xc5;" k="22" />
<hkern u1="&#x2018;" u2="&#xc4;" k="22" />
<hkern u1="&#x2018;" u2="&#xc3;" k="22" />
<hkern u1="&#x2018;" u2="&#xc2;" k="22" />
<hkern u1="&#x2018;" u2="&#xc1;" k="22" />
<hkern u1="&#x2018;" u2="&#xc0;" k="22" />
<hkern u1="&#x2018;" u2="u" k="6" />
<hkern u1="&#x2018;" u2="s" k="17" />
<hkern u1="&#x2018;" u2="r" k="6" />
<hkern u1="&#x2018;" u2="q" k="17" />
<hkern u1="&#x2018;" u2="p" k="6" />
<hkern u1="&#x2018;" u2="o" k="17" />
<hkern u1="&#x2018;" u2="n" k="6" />
<hkern u1="&#x2018;" u2="m" k="6" />
<hkern u1="&#x2018;" u2="g" k="22" />
<hkern u1="&#x2018;" u2="e" k="17" />
<hkern u1="&#x2018;" u2="d" k="17" />
<hkern u1="&#x2018;" u2="c" k="17" />
<hkern u1="&#x2018;" u2="a" k="17" />
<hkern u1="&#x2018;" u2="Y" k="-17" />
<hkern u1="&#x2018;" u2="X" k="-11" />
<hkern u1="&#x2018;" u2="W" k="-11" />
<hkern u1="&#x2018;" u2="V" k="-11" />
<hkern u1="&#x2018;" u2="T" k="-6" />
<hkern u1="&#x2018;" u2="J" k="61" />
<hkern u1="&#x2018;" u2="A" k="22" />
<hkern u1="&#x2019;" u2="&#x153;" k="28" />
<hkern u1="&#x2019;" u2="&#x152;" k="22" />
<hkern u1="&#x2019;" u2="&#xe7;" k="28" />
<hkern u1="&#x2019;" u2="&#xe6;" k="28" />
<hkern u1="&#x2019;" u2="&#xd8;" k="22" />
<hkern u1="&#x2019;" u2="&#xd6;" k="22" />
<hkern u1="&#x2019;" u2="&#xd5;" k="22" />
<hkern u1="&#x2019;" u2="&#xd4;" k="22" />
<hkern u1="&#x2019;" u2="&#xd3;" k="22" />
<hkern u1="&#x2019;" u2="&#xd2;" k="22" />
<hkern u1="&#x2019;" u2="q" k="28" />
<hkern u1="&#x2019;" u2="o" k="28" />
<hkern u1="&#x2019;" u2="e" k="28" />
<hkern u1="&#x2019;" u2="d" k="28" />
<hkern u1="&#x2019;" u2="c" k="28" />
<hkern u1="&#x2019;" u2="a" k="28" />
<hkern u1="&#x2019;" u2="S" k="6" />
<hkern u1="&#x2019;" u2="Q" k="22" />
<hkern u1="&#x2019;" u2="O" k="22" />
<hkern u1="&#x2019;" u2="J" k="66" />
<hkern u1="&#x2019;" u2="G" k="22" />
<hkern u1="&#x2019;" u2="C" k="22" />
<hkern u1="&#x201a;" u2="&#x178;" k="28" />
<hkern u1="&#x201a;" u2="&#x153;" k="11" />
<hkern u1="&#x201a;" u2="&#x152;" k="22" />
<hkern u1="&#x201a;" u2="&#xe7;" k="11" />
<hkern u1="&#x201a;" u2="&#xe6;" k="11" />
<hkern u1="&#x201a;" u2="&#xdd;" k="28" />
<hkern u1="&#x201a;" u2="&#xdc;" k="6" />
<hkern u1="&#x201a;" u2="&#xdb;" k="6" />
<hkern u1="&#x201a;" u2="&#xda;" k="6" />
<hkern u1="&#x201a;" u2="&#xd9;" k="6" />
<hkern u1="&#x201a;" u2="&#xd8;" k="22" />
<hkern u1="&#x201a;" u2="&#xd6;" k="22" />
<hkern u1="&#x201a;" u2="&#xd5;" k="22" />
<hkern u1="&#x201a;" u2="&#xd4;" k="22" />
<hkern u1="&#x201a;" u2="&#xd3;" k="22" />
<hkern u1="&#x201a;" u2="&#xd2;" k="22" />
<hkern u1="&#x201a;" u2="&#xc6;" k="-17" />
<hkern u1="&#x201a;" u2="&#xc5;" k="-17" />
<hkern u1="&#x201a;" u2="&#xc4;" k="-17" />
<hkern u1="&#x201a;" u2="&#xc3;" k="-17" />
<hkern u1="&#x201a;" u2="&#xc2;" k="-17" />
<hkern u1="&#x201a;" u2="&#xc1;" k="-17" />
<hkern u1="&#x201a;" u2="&#xc0;" k="-17" />
<hkern u1="&#x201a;" u2="y" k="17" />
<hkern u1="&#x201a;" u2="w" k="6" />
<hkern u1="&#x201a;" u2="v" k="17" />
<hkern u1="&#x201a;" u2="u" k="11" />
<hkern u1="&#x201a;" u2="t" k="22" />
<hkern u1="&#x201a;" u2="r" k="11" />
<hkern u1="&#x201a;" u2="q" k="11" />
<hkern u1="&#x201a;" u2="p" k="11" />
<hkern u1="&#x201a;" u2="o" k="11" />
<hkern u1="&#x201a;" u2="n" k="11" />
<hkern u1="&#x201a;" u2="m" k="11" />
<hkern u1="&#x201a;" u2="j" k="-17" />
<hkern u1="&#x201a;" u2="e" k="11" />
<hkern u1="&#x201a;" u2="d" k="11" />
<hkern u1="&#x201a;" u2="c" k="11" />
<hkern u1="&#x201a;" u2="a" k="11" />
<hkern u1="&#x201a;" u2="Y" k="28" />
<hkern u1="&#x201a;" u2="W" k="11" />
<hkern u1="&#x201a;" u2="V" k="33" />
<hkern u1="&#x201a;" u2="U" k="6" />
<hkern u1="&#x201a;" u2="T" k="33" />
<hkern u1="&#x201a;" u2="Q" k="22" />
<hkern u1="&#x201a;" u2="O" k="22" />
<hkern u1="&#x201a;" u2="G" k="22" />
<hkern u1="&#x201a;" u2="C" k="22" />
<hkern u1="&#x201a;" u2="A" k="-17" />
<hkern u1="&#x201a;" u2="&#x39;" k="6" />
<hkern u1="&#x201a;" u2="&#x38;" k="8" />
<hkern u1="&#x201a;" u2="&#x36;" k="17" />
<hkern u1="&#x201a;" u2="&#x34;" k="28" />
<hkern u1="&#x201a;" u2="&#x31;" k="33" />
<hkern u1="&#x201a;" u2="&#x30;" k="22" />
<hkern u1="&#x201c;" u2="&#x178;" k="-17" />
<hkern u1="&#x201c;" u2="&#x153;" k="17" />
<hkern u1="&#x201c;" u2="&#xe7;" k="17" />
<hkern u1="&#x201c;" u2="&#xe6;" k="17" />
<hkern u1="&#x201c;" u2="&#xdd;" k="-17" />
<hkern u1="&#x201c;" u2="&#xc6;" k="22" />
<hkern u1="&#x201c;" u2="&#xc5;" k="22" />
<hkern u1="&#x201c;" u2="&#xc4;" k="22" />
<hkern u1="&#x201c;" u2="&#xc3;" k="22" />
<hkern u1="&#x201c;" u2="&#xc2;" k="22" />
<hkern u1="&#x201c;" u2="&#xc1;" k="22" />
<hkern u1="&#x201c;" u2="&#xc0;" k="22" />
<hkern u1="&#x201c;" u2="u" k="6" />
<hkern u1="&#x201c;" u2="s" k="17" />
<hkern u1="&#x201c;" u2="r" k="6" />
<hkern u1="&#x201c;" u2="q" k="17" />
<hkern u1="&#x201c;" u2="p" k="6" />
<hkern u1="&#x201c;" u2="o" k="17" />
<hkern u1="&#x201c;" u2="n" k="6" />
<hkern u1="&#x201c;" u2="m" k="6" />
<hkern u1="&#x201c;" u2="g" k="22" />
<hkern u1="&#x201c;" u2="e" k="17" />
<hkern u1="&#x201c;" u2="d" k="17" />
<hkern u1="&#x201c;" u2="c" k="17" />
<hkern u1="&#x201c;" u2="a" k="17" />
<hkern u1="&#x201c;" u2="Y" k="-17" />
<hkern u1="&#x201c;" u2="X" k="-11" />
<hkern u1="&#x201c;" u2="W" k="-11" />
<hkern u1="&#x201c;" u2="V" k="-11" />
<hkern u1="&#x201c;" u2="T" k="-6" />
<hkern u1="&#x201c;" u2="J" k="61" />
<hkern u1="&#x201c;" u2="A" k="22" />
<hkern u1="&#x201d;" u2="&#x153;" k="28" />
<hkern u1="&#x201d;" u2="&#x152;" k="22" />
<hkern u1="&#x201d;" u2="&#xe7;" k="28" />
<hkern u1="&#x201d;" u2="&#xe6;" k="28" />
<hkern u1="&#x201d;" u2="&#xd8;" k="22" />
<hkern u1="&#x201d;" u2="&#xd6;" k="22" />
<hkern u1="&#x201d;" u2="&#xd5;" k="22" />
<hkern u1="&#x201d;" u2="&#xd4;" k="22" />
<hkern u1="&#x201d;" u2="&#xd3;" k="22" />
<hkern u1="&#x201d;" u2="&#xd2;" k="22" />
<hkern u1="&#x201d;" u2="q" k="28" />
<hkern u1="&#x201d;" u2="o" k="28" />
<hkern u1="&#x201d;" u2="e" k="28" />
<hkern u1="&#x201d;" u2="d" k="28" />
<hkern u1="&#x201d;" u2="c" k="28" />
<hkern u1="&#x201d;" u2="a" k="28" />
<hkern u1="&#x201d;" u2="S" k="6" />
<hkern u1="&#x201d;" u2="Q" k="22" />
<hkern u1="&#x201d;" u2="O" k="22" />
<hkern u1="&#x201d;" u2="J" k="66" />
<hkern u1="&#x201d;" u2="G" k="22" />
<hkern u1="&#x201d;" u2="C" k="22" />
<hkern u1="&#x201e;" u2="&#x178;" k="28" />
<hkern u1="&#x201e;" u2="&#x153;" k="11" />
<hkern u1="&#x201e;" u2="&#x152;" k="22" />
<hkern u1="&#x201e;" u2="&#xe7;" k="11" />
<hkern u1="&#x201e;" u2="&#xe6;" k="11" />
<hkern u1="&#x201e;" u2="&#xdd;" k="28" />
<hkern u1="&#x201e;" u2="&#xdc;" k="6" />
<hkern u1="&#x201e;" u2="&#xdb;" k="6" />
<hkern u1="&#x201e;" u2="&#xda;" k="6" />
<hkern u1="&#x201e;" u2="&#xd9;" k="6" />
<hkern u1="&#x201e;" u2="&#xd8;" k="22" />
<hkern u1="&#x201e;" u2="&#xd6;" k="22" />
<hkern u1="&#x201e;" u2="&#xd5;" k="22" />
<hkern u1="&#x201e;" u2="&#xd4;" k="22" />
<hkern u1="&#x201e;" u2="&#xd3;" k="22" />
<hkern u1="&#x201e;" u2="&#xd2;" k="22" />
<hkern u1="&#x201e;" u2="&#xc6;" k="-17" />
<hkern u1="&#x201e;" u2="&#xc5;" k="-17" />
<hkern u1="&#x201e;" u2="&#xc4;" k="-17" />
<hkern u1="&#x201e;" u2="&#xc3;" k="-17" />
<hkern u1="&#x201e;" u2="&#xc2;" k="-17" />
<hkern u1="&#x201e;" u2="&#xc1;" k="-17" />
<hkern u1="&#x201e;" u2="&#xc0;" k="-17" />
<hkern u1="&#x201e;" u2="y" k="17" />
<hkern u1="&#x201e;" u2="w" k="6" />
<hkern u1="&#x201e;" u2="v" k="17" />
<hkern u1="&#x201e;" u2="u" k="11" />
<hkern u1="&#x201e;" u2="t" k="22" />
<hkern u1="&#x201e;" u2="r" k="11" />
<hkern u1="&#x201e;" u2="q" k="11" />
<hkern u1="&#x201e;" u2="p" k="11" />
<hkern u1="&#x201e;" u2="o" k="11" />
<hkern u1="&#x201e;" u2="n" k="11" />
<hkern u1="&#x201e;" u2="m" k="11" />
<hkern u1="&#x201e;" u2="j" k="-17" />
<hkern u1="&#x201e;" u2="e" k="11" />
<hkern u1="&#x201e;" u2="d" k="11" />
<hkern u1="&#x201e;" u2="c" k="11" />
<hkern u1="&#x201e;" u2="a" k="11" />
<hkern u1="&#x201e;" u2="Y" k="28" />
<hkern u1="&#x201e;" u2="W" k="11" />
<hkern u1="&#x201e;" u2="V" k="33" />
<hkern u1="&#x201e;" u2="U" k="6" />
<hkern u1="&#x201e;" u2="T" k="33" />
<hkern u1="&#x201e;" u2="Q" k="22" />
<hkern u1="&#x201e;" u2="O" k="22" />
<hkern u1="&#x201e;" u2="G" k="22" />
<hkern u1="&#x201e;" u2="C" k="22" />
<hkern u1="&#x201e;" u2="A" k="-17" />
<hkern u1="&#x201e;" u2="&#x39;" k="6" />
<hkern u1="&#x201e;" u2="&#x38;" k="8" />
<hkern u1="&#x201e;" u2="&#x36;" k="17" />
<hkern u1="&#x201e;" u2="&#x34;" k="28" />
<hkern u1="&#x201e;" u2="&#x31;" k="33" />
<hkern u1="&#x201e;" u2="&#x30;" k="22" />
<hkern u1="&#x2026;" u2="&#x178;" k="28" />
<hkern u1="&#x2026;" u2="&#x153;" k="11" />
<hkern u1="&#x2026;" u2="&#x152;" k="22" />
<hkern u1="&#x2026;" u2="&#xe7;" k="11" />
<hkern u1="&#x2026;" u2="&#xe6;" k="11" />
<hkern u1="&#x2026;" u2="&#xdd;" k="28" />
<hkern u1="&#x2026;" u2="&#xdc;" k="6" />
<hkern u1="&#x2026;" u2="&#xdb;" k="6" />
<hkern u1="&#x2026;" u2="&#xda;" k="6" />
<hkern u1="&#x2026;" u2="&#xd9;" k="6" />
<hkern u1="&#x2026;" u2="&#xd8;" k="22" />
<hkern u1="&#x2026;" u2="&#xd6;" k="22" />
<hkern u1="&#x2026;" u2="&#xd5;" k="22" />
<hkern u1="&#x2026;" u2="&#xd4;" k="22" />
<hkern u1="&#x2026;" u2="&#xd3;" k="22" />
<hkern u1="&#x2026;" u2="&#xd2;" k="22" />
<hkern u1="&#x2026;" u2="&#xc6;" k="-17" />
<hkern u1="&#x2026;" u2="&#xc5;" k="-17" />
<hkern u1="&#x2026;" u2="&#xc4;" k="-17" />
<hkern u1="&#x2026;" u2="&#xc3;" k="-17" />
<hkern u1="&#x2026;" u2="&#xc2;" k="-17" />
<hkern u1="&#x2026;" u2="&#xc1;" k="-17" />
<hkern u1="&#x2026;" u2="&#xc0;" k="-17" />
<hkern u1="&#x2026;" u2="y" k="17" />
<hkern u1="&#x2026;" u2="w" k="6" />
<hkern u1="&#x2026;" u2="v" k="17" />
<hkern u1="&#x2026;" u2="u" k="11" />
<hkern u1="&#x2026;" u2="t" k="22" />
<hkern u1="&#x2026;" u2="r" k="11" />
<hkern u1="&#x2026;" u2="q" k="11" />
<hkern u1="&#x2026;" u2="p" k="11" />
<hkern u1="&#x2026;" u2="o" k="11" />
<hkern u1="&#x2026;" u2="n" k="11" />
<hkern u1="&#x2026;" u2="m" k="11" />
<hkern u1="&#x2026;" u2="e" k="11" />
<hkern u1="&#x2026;" u2="d" k="11" />
<hkern u1="&#x2026;" u2="c" k="11" />
<hkern u1="&#x2026;" u2="a" k="11" />
<hkern u1="&#x2026;" u2="Y" k="28" />
<hkern u1="&#x2026;" u2="W" k="11" />
<hkern u1="&#x2026;" u2="V" k="33" />
<hkern u1="&#x2026;" u2="U" k="6" />
<hkern u1="&#x2026;" u2="T" k="33" />
<hkern u1="&#x2026;" u2="Q" k="22" />
<hkern u1="&#x2026;" u2="O" k="22" />
<hkern u1="&#x2026;" u2="G" k="22" />
<hkern u1="&#x2026;" u2="C" k="22" />
<hkern u1="&#x2026;" u2="A" k="-17" />
<hkern u1="&#x2026;" u2="&#x39;" k="6" />
<hkern u1="&#x2026;" u2="&#x38;" k="8" />
<hkern u1="&#x2026;" u2="&#x36;" k="17" />
<hkern u1="&#x2026;" u2="&#x34;" k="28" />
<hkern u1="&#x2026;" u2="&#x31;" k="33" />
<hkern u1="&#x2026;" u2="&#x30;" k="22" />
<hkern u1="&#x2039;" u2="&#x178;" k="22" />
<hkern u1="&#x2039;" u2="&#xdd;" k="22" />
<hkern u1="&#x2039;" u2="Y" k="22" />
<hkern u1="&#x2039;" u2="W" k="11" />
<hkern u1="&#x2039;" u2="V" k="11" />
<hkern u1="&#x2039;" u2="T" k="22" />
<hkern u1="&#x203a;" u2="&#x178;" k="28" />
<hkern u1="&#x203a;" u2="&#xdd;" k="28" />
<hkern u1="&#x203a;" u2="z" k="28" />
<hkern u1="&#x203a;" u2="x" k="28" />
<hkern u1="&#x203a;" u2="Y" k="28" />
<hkern u1="&#x203a;" u2="W" k="22" />
<hkern u1="&#x203a;" u2="V" k="28" />
<hkern u1="&#x203a;" u2="T" k="61" />
</font>
</defs></svg> 