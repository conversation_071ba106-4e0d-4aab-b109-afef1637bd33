<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="omnesbold" horiz-adv-x="733" >
<font-face units-per-em="1000" ascent="700" descent="-300" />
<missing-glyph horiz-adv-x="170" />
<glyph unicode="&#xfb01;" horiz-adv-x="702" d="M560 552q-48 0 -75.5 24.5t-27.5 66.5q0 41 27.5 65.5t73.5 24.5q48 0 75.5 -24t27.5 -66t-27 -66.5t-74 -24.5zM101 88v263h-20q-78 0 -78 69v11q0 65 78 65h20v14q0 109 50 154t139 45q71 0 106.5 -26t35.5 -65q0 -19 -8.5 -38t-17.5 -28.5t-11 -7.5q-25 23 -61 23 q-25 0 -37 -12.5t-12 -42.5v-16h42q78 0 78 -69v-11q0 -65 -78 -65h-34v-263q0 -51 -22 -72t-64 -21h-20q-86 0 -86 93zM463 88v321q0 92 86 92h20q41 0 63.5 -21t22.5 -71v-321q0 -93 -85 -93h-20q-87 0 -87 93z" />
<glyph unicode="&#xfb02;" horiz-adv-x="702" d="M101 88v263h-20q-78 0 -78 69v11q0 65 78 65h20v14q0 109 50 154t139 45q71 0 106.5 -26t35.5 -65q0 -19 -8.5 -38t-17.5 -28.5t-11 -7.5q-25 23 -61 23q-25 0 -37 -12.5t-12 -42.5v-16h42q78 0 78 -69v-11q0 -65 -78 -65h-34v-263q0 -51 -22 -72t-64 -21h-20 q-86 0 -86 93zM463 88v594q0 4 10 9t28 9t36 4q50 0 84 -33t34 -113v-470q0 -93 -85 -93h-20q-87 0 -87 93z" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="333" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="170" />
<glyph unicode=" "  horiz-adv-x="170" />
<glyph unicode="&#x09;" horiz-adv-x="170" />
<glyph unicode="&#xa0;" horiz-adv-x="170" />
<glyph unicode="!" horiz-adv-x="297" d="M138 216q-18 0 -28.5 14t-19.5 46l-51 261q-14 55 13 88.5t82 33.5h25q58 0 86 -33.5t14 -88.5l-51 -261q-9 -32 -20 -46t-31 -14h-19zM55 73v16q0 81 81 81h23q83 0 83 -81v-16q0 -81 -83 -81h-23q-81 0 -81 81z" />
<glyph unicode="&#x22;" horiz-adv-x="479" d="M339 330q-36 0 -45 44l-36 192q-8 44 14 67t77 23q56 0 78 -23t14 -67l-36 -192q-9 -44 -45 -44h-21zM118 330q-36 0 -45 44l-36 192q-8 44 14 67t77 23q56 0 78 -23t14 -67l-36 -192q-9 -44 -45 -44h-21z" />
<glyph unicode="#" horiz-adv-x="698" d="M95 65q0 15 6 36l9 28h-28q-76 0 -76 58v23q0 57 76 57h71l36 113h-28q-76 0 -76 59v23q0 56 76 56h71l43 137q1 7 18 7q26 0 53 -21.5t27 -57.5q0 -15 -6 -36l-9 -29h103l43 137q3 7 18 7q27 0 54 -21.5t27 -57.5q0 -16 -7 -36l-9 -29h28q76 0 76 -56v-23q0 -59 -76 -59 h-71l-36 -113h29q76 0 76 -57v-23q0 -58 -76 -58h-72l-42 -136q-2 -8 -18 -8q-26 0 -53.5 22t-27.5 58q0 11 7 36l8 28h-103l-42 -136q-3 -8 -18 -8q-26 0 -53.5 22t-27.5 58zM278 266h105l36 115h-105z" />
<glyph unicode="$" horiz-adv-x="574" d="M28 132q0 23 14.5 46t29 34.5t16.5 8.5q32 -28 84 -48.5t100 -20.5q83 0 83 47q0 23 -24 34t-76 22q-4 1 -6 1q-40 8 -68.5 18t-64 30.5t-55 56t-19.5 84.5q0 75 44.5 124.5t125.5 66.5v22q0 93 78 93h5q42 0 60 -22.5t18 -70.5v-17q79 -10 120.5 -40.5t41.5 -76.5 q0 -25 -15.5 -49t-30.5 -36t-18 -9q-74 67 -165 67q-80 0 -80 -47q0 -27 50 -42q14 -4 48 -11l12 -3q31 -6 55.5 -13t55.5 -22t52 -33.5t35.5 -49t14.5 -68.5q0 -79 -45.5 -128.5t-131.5 -66.5v-25q0 -93 -78 -93h-5q-42 0 -60 22.5t-18 70.5v20q-88 10 -135.5 42.5 t-47.5 81.5z" />
<glyph unicode="%" horiz-adv-x="743" d="M64 50q0 21 25 49l504 559q4 4 24.5 -2.5t40 -23t19.5 -36.5q0 -23 -24 -50l-505 -559q-3 -4 -23.5 2.5t-40.5 24t-20 36.5zM180 335q-80 0 -127 42t-47 115v7q0 72 47.5 115t126.5 43t126 -42.5t47 -114.5v-7q0 -73 -47 -115.5t-126 -42.5zM180 430q22 0 36 14.5 t14 43.5v16q0 29 -14 44t-36 15q-50 0 -50 -59v-16q0 -28 13.5 -43t36.5 -15zM389 145v7q0 72 48 115t127 43t126 -42.5t47 -114.5v-7q0 -73 -47 -115.5t-126 -42.5q-80 0 -127.5 42.5t-47.5 114.5zM514 141q0 -58 50 -58q22 0 36 14.5t14 43.5v16q0 29 -14 44t-36 15 q-50 0 -50 -59v-16z" />
<glyph unicode="&#x26;" horiz-adv-x="711" d="M16 181q0 126 138 178q-60 55 -60 125q0 80 61.5 128.5t163.5 48.5q91 0 146 -44.5t55 -117.5q0 -51 -35 -91t-96 -62l88 -74q9 16 34 78q19 44 39 61.5t52 17.5q25 0 46.5 -12t30.5 -23.5t8 -14.5q-25 -103 -99 -202l123 -103q3 -2 -8 -22t-37 -40.5t-58 -20.5 q-60 0 -107 55l-17 17q-87 -77 -214 -77q-121 0 -187.5 50.5t-66.5 144.5zM301 407q71 23 71 75q0 25 -15 41t-39 16q-25 0 -40.5 -17t-15.5 -43q0 -38 39 -72zM182 203q0 -40 28 -63t75 -23q52 0 98 41l-146 126q-55 -29 -55 -81z" />
<glyph unicode="'" horiz-adv-x="258" d="M118 330q-36 0 -45 44l-36 192q-8 44 14 67t77 23q56 0 78 -23t14 -67l-36 -192q-9 -44 -45 -44h-21z" />
<glyph unicode="(" horiz-adv-x="441" d="M31 231q0 174 95.5 301t215.5 127q44 0 68 -22t24 -61q0 -23 -13 -40.5t-21 -20.5q-87 -23 -132.5 -98.5t-45.5 -185.5q0 -111 45.5 -187t132.5 -99q4 -1 11.5 -8t15 -21.5t7.5 -30.5q0 -39 -24 -61t-68 -22q-121 0 -216 127t-95 302z" />
<glyph unicode=")" horiz-adv-x="441" d="M41 -55q87 23 132.5 99t45.5 187q0 110 -45.5 185.5t-132.5 98.5q-4 2 -11 9t-15 21.5t-8 30.5q0 38 24.5 60.5t67.5 22.5q120 0 215.5 -127t95.5 -301q0 -175 -95 -302t-216 -127q-43 0 -67.5 22t-24.5 61q0 16 7.5 30.5t15.5 21.5t11 8z" />
<glyph unicode="*" horiz-adv-x="433" d="M93 282l-4 2q-38 20 -13 68l37 70l-65 33q-25 14 -30.5 26.5t1.5 35.5l1 4q6 23 19 30.5t41 2.5l76 -13l11 72q5 28 16.5 37.5t33.5 9.5h4q23 0 35 -9.5t16 -37.5l9 -72l75 13q29 5 42 -3t19 -30l1 -4q7 -21 2 -35t-31 -27l-66 -34l38 -69q23 -44 -14 -68l-3 -2 q-13 -12 -30.5 -11.5t-37.5 19.5l-58 58l-57 -58q-20 -19 -36.5 -19.5t-31.5 11.5z" />
<glyph unicode="+" horiz-adv-x="535" d="M92 184q-37 0 -56.5 17.5t-19.5 47.5v9q0 28 19.5 46t56.5 18h102v107q0 37 18 56.5t48 19.5h15q29 0 47 -19.5t18 -56.5v-107h103q37 0 56.5 -18t19.5 -46v-9q0 -30 -19 -47.5t-57 -17.5h-103v-110q0 -37 -18 -56.5t-47 -19.5h-15q-30 0 -48 19t-18 57v110h-102z" />
<glyph unicode="," horiz-adv-x="259" d="M9 -53l35 146q9 43 26 61t60 18h21q74 0 74 -79v-8q0 -48 -43 -92l-76 -90q-26 -27 -65.5 -11t-31.5 55z" />
<glyph unicode="-" horiz-adv-x="469" d="M106 159q-36 0 -56 21t-20 51v31q0 30 20 51t56 21h257q36 0 56 -21t20 -51v-31q0 -30 -20 -51t-56 -21h-257z" />
<glyph unicode="." horiz-adv-x="259" d="M35 73v16q0 81 81 81h23q43 0 63 -19.5t20 -61.5v-16q0 -81 -83 -81h-23q-81 0 -81 81z" />
<glyph unicode="/" horiz-adv-x="382" d="M-3 -104l244 759q3 7 42 7q92 0 92 -69q0 -20 -8 -44l-244 -760q0 -6 -42 -6q-92 0 -92 68q0 20 8 45z" />
<glyph unicode="0" horiz-adv-x="653" d="M27 293v62q0 141 80 223.5t220 82.5t219.5 -82t79.5 -224v-62q0 -142 -79.5 -224.5t-219.5 -82.5q-141 0 -220.5 82t-79.5 225zM228 276q0 -129 99 -129q98 0 98 128v98q0 130 -98 130q-99 0 -99 -130v-97z" />
<glyph unicode="1" horiz-adv-x="420" d="M164 84v338q-24 -11 -47 -11q-44 0 -72.5 28.5t-28.5 71.5q0 30 12 53.5t23.5 32t16.5 6.5q31 -20 63 -20q33 0 57.5 19.5t32.5 53.5h52q88 0 88 -93v-479q0 -93 -88 -93h-25q-84 0 -84 93z" />
<glyph unicode="2" horiz-adv-x="577" d="M29 97v38q0 40 17.5 75t49.5 61.5t62 44.5t69 35q13 6 41.5 18.5t39 18t26 15.5t21 19t5.5 21q0 62 -83 62q-47 0 -78 -29.5t-39 -71.5q0 -1 -7 -1t-18.5 1t-25 4t-27.5 9.5t-25.5 15.5t-18.5 24.5t-7 35.5q0 74 69 121.5t190 47.5q127 0 195.5 -55t68.5 -154 q0 -44 -13.5 -77.5t-41.5 -56t-54.5 -36t-67.5 -28.5q-50 -19 -73 -29t-49.5 -28t-37.5 -38h248q50 0 72 -18t22 -55v-8q0 -39 -21.5 -58.5t-72.5 -19.5h-316q-72 0 -96 18t-24 78z" />
<glyph unicode="3" horiz-adv-x="585" d="M13 137q0 28 12 48.5t29 29t34.5 13t29.5 3.5t13 -3q26 -92 140 -92q47 0 74 18t27 49q0 59 -76 59h-20q-92 0 -92 65v10q0 66 90 66h35q26 0 41.5 13t15.5 36q0 27 -26 43t-72 16q-104 0 -128 -86q0 -3 -12 -4t-29 3t-33.5 13t-28.5 29.5t-12 48.5q0 63 67.5 104.5 t179.5 41.5q136 0 207.5 -46.5t71.5 -134.5q0 -97 -118 -131q128 -42 128 -162q0 -96 -75 -148.5t-213 -52.5q-117 0 -188.5 43.5t-71.5 107.5z" />
<glyph unicode="4" horiz-adv-x="630" d="M120 141q-49 0 -77.5 22.5t-28.5 58.5v28q1 40 32 92l160 248q25 42 55 54t106 12q77 0 110 -29t33 -101v-240h24q46 0 65.5 -15.5t19.5 -52.5v-5q0 -40 -19 -56t-66 -16h-24v-57q0 -93 -84 -93h-17q-81 0 -81 93v57h-208zM184 282h152v240z" />
<glyph unicode="5" horiz-adv-x="566" d="M14 112q0 24 17.5 47.5t35 35t21.5 8.5q72 -75 175 -75q52 0 80.5 20.5t28.5 57.5q0 77 -109 77q-66 0 -113 -34q-58 1 -87 27t-29 85v175q0 56 22 83.5t72 27.5h299q96 0 96 -77v-5q0 -40 -23 -58t-73 -18h-230v-96q48 16 100 16q114 0 181 -54.5t67 -151.5 q0 -99 -74.5 -158t-202.5 -59q-118 0 -186 37.5t-68 88.5z" />
<glyph unicode="6" horiz-adv-x="622" d="M27 297v48q0 147 83.5 231.5t229.5 84.5q112 0 174.5 -38t62.5 -101q0 -35 -23.5 -56t-46.5 -25.5t-25 -1.5q-39 66 -125 66q-67 0 -99 -34.5t-34 -108.5q61 54 164 54q99 0 156.5 -49.5t57.5 -142.5v-5q0 -104 -76.5 -168.5t-199.5 -64.5q-139 0 -219 84t-80 227z M228 247q3 -55 28 -84.5t68 -29.5q38 0 62.5 20t24.5 54v3q0 35 -23 54t-65 19q-51 0 -95 -36z" />
<glyph unicode="7" horiz-adv-x="562" d="M86 60l244 425h-231q-44 0 -66 18t-22 59v11q0 40 22 57t66 17h310q81 0 108 -27.5t27 -75.5v-11q0 -13 -3.5 -28t-7.5 -26.5t-12.5 -28.5t-13 -24.5l-15 -25t-12.5 -20.5l-180 -319q-40 -70 -102 -70q-30 0 -59 15t-43 31.5t-10 22.5z" />
<glyph unicode="8" horiz-adv-x="621" d="M23 179q0 113 125 153q-106 38 -106 143q0 85 74.5 137t195.5 52t192 -51t71 -137q0 -103 -104 -143q127 -38 127 -154q0 -89 -77 -140t-212 -51q-136 0 -211 50t-75 141zM311 387q37 0 61 19t24 49t-23.5 49.5t-62.5 19.5q-38 0 -61.5 -19t-23.5 -50q0 -30 24 -49 t62 -19zM217 199q0 -33 25.5 -53t68.5 -20t68 20t25 53q0 34 -25.5 54.5t-68.5 20.5t-68 -20.5t-25 -54.5z" />
<glyph unicode="9" horiz-adv-x="622" d="M44 104q0 35 14.5 58.5t28 30.5t17.5 5q66 -57 156 -57q71 0 104 34.5t34 110.5q-62 -55 -164 -55q-99 0 -156.5 49.5t-57.5 142.5v3q0 104 76.5 169.5t199.5 65.5q137 0 218 -82.5t81 -227.5v-55q0 -150 -82.5 -230.5t-235.5 -80.5q-114 0 -173.5 32t-59.5 87zM299 364 q50 0 94 36q-2 54 -27.5 84t-68.5 30q-37 0 -62 -21.5t-25 -54.5v-1q0 -35 23.5 -54t65.5 -19z" />
<glyph unicode=":" horiz-adv-x="266" d="M120 274q-81 0 -81 81v17q0 81 81 81h23q83 0 83 -81v-17q0 -81 -83 -81h-23zM39 73v16q0 81 81 81h23q83 0 83 -81v-16q0 -81 -83 -81h-23q-81 0 -81 81z" />
<glyph unicode=";" horiz-adv-x="266" d="M120 274q-81 0 -81 81v17q0 81 81 81h23q83 0 83 -81v-17q0 -81 -83 -81h-23zM13 -53l34 146q10 43 27 61t60 18h20q75 0 75 -79v-8q0 -48 -43 -92l-76 -90q-27 -27 -66 -11t-31 55z" />
<glyph unicode="&#x3c;" horiz-adv-x="453" d="M87 166q-56 31 -56 87q0 58 56 89l289 158q4 2 15 -9.5t20.5 -34t9.5 -44.5q2 -62 -71 -96l-125 -61l125 -62q72 -35 72 -95q0 -22 -10 -44.5t-21 -34t-15 -9.5z" />
<glyph unicode="=" horiz-adv-x="535" d="M109 300q-36 0 -56 21t-20 51v16q0 30 20 51t56 21h317q36 0 56 -21t20 -51v-16q0 -30 -20 -51t-56 -21h-317zM109 48q-36 0 -56 21t-20 52v16q0 29 20 50.5t56 21.5h317q36 0 56 -21.5t20 -50.5v-16q0 -31 -20 -52t-56 -21h-317z" />
<glyph unicode="&#x3e;" horiz-adv-x="453" d="M77 10q-4 -2 -15 9.5t-21 34t-10 44.5q0 60 72 95l125 62l-125 61q-73 34 -71 96q0 22 9.5 44.5t20.5 34t15 9.5l289 -158q56 -31 56 -89q0 -56 -56 -87z" />
<glyph unicode="?" horiz-adv-x="522" d="M162 275q1 40 18 68t41 41.5t48 23.5t40.5 21.5t16.5 28.5q0 23 -18.5 36.5t-50.5 13.5q-50 0 -80 -29.5t-36 -71.5q0 -1 -13 -1.5t-32 3t-37.5 12t-31.5 28t-13 47.5q0 68 66 116.5t179 48.5q116 0 182 -51t66 -139q0 -46 -18 -78.5t-44 -50t-53 -31t-48 -30.5t-25 -40 q-10 -33 -76 -33q-37 0 -59 17t-22 50zM149 73v16q0 81 81 81h23q83 0 83 -81v-16q0 -81 -83 -81h-23q-81 0 -81 81z" />
<glyph unicode="@" horiz-adv-x="736" d="M32 324q0 147 95.5 242t245.5 95q157 0 244.5 -84t87.5 -210q0 -97 -46 -148t-133 -51q-83 0 -111 60q-11 -28 -36 -45t-56 -17q-62 0 -96.5 42.5t-34.5 107.5q0 67 36.5 109t93.5 42q55 0 88 -48q15 46 67 46q11 0 21 -2.5t15.5 -6t5.5 -6.5v-146q0 -35 11.5 -47.5 t34.5 -12.5q29 0 50 29t21 92q0 98 -66 164.5t-197 66.5q-119 0 -195 -77t-76 -195q0 -119 75 -196t195 -77q109 0 180 66q16 15 33 15q13 0 23.5 -10t15 -20.5t2.5 -11.5q-96 -104 -254 -104q-152 0 -246 94t-94 244zM303 316q0 -27 15 -43t38 -16q21 0 34.5 12t13.5 33v46 q-16 28 -47 28q-25 0 -39.5 -16.5t-14.5 -43.5z" />
<glyph unicode="A" horiz-adv-x="688" d="M6 53l210 544q8 27 36 41t91 14t89 -14t35 -41l218 -542q4 -9 -10.5 -24t-41.5 -27.5t-55 -12.5q-78 0 -115 95l-18 48h-209l-18 -50q-32 -93 -109 -93q-39 0 -74 23.5t-29 38.5zM270 278h139l-70 188z" />
<glyph unicode="B" horiz-adv-x="660" d="M46 112v424q0 54 26 82.5t76 28.5h232q114 0 174 -42t60 -120q0 -107 -118 -135q141 -33 141 -163q0 -98 -66.5 -142.5t-214.5 -44.5h-208q-50 0 -76 28.5t-26 83.5zM243 386h77q88 0 88 62q0 57 -85 57h-80v-119zM243 143h89q53 0 76 15.5t23 50.5q0 32 -24 46.5 t-77 14.5h-87v-127z" />
<glyph unicode="C" horiz-adv-x="650" d="M29 324q0 148 94.5 242.5t245.5 94.5q124 0 189 -50.5t65 -107.5q0 -38 -28.5 -63t-56.5 -32.5t-30 -2.5q-39 73 -128 73q-63 0 -104 -42.5t-41 -111.5t41.5 -112t103.5 -43q95 0 130 78q2 5 30.5 -2.5t57 -32.5t28.5 -63q0 -58 -66 -110.5t-192 -52.5q-151 0 -245 94.5 t-94 243.5z" />
<glyph unicode="D" horiz-adv-x="690" d="M46 110v426q0 53 29 82t75 29h171q160 0 252 -86t92 -234q0 -150 -94 -238.5t-254 -88.5h-160q-46 0 -78.5 32t-32.5 78zM244 170h68q71 0 113 41.5t42 114.5q0 72 -40.5 111.5t-111.5 39.5h-71v-307z" />
<glyph unicode="E" horiz-adv-x="616" d="M46 110v426q0 54 26 82.5t76 28.5h344q93 0 93 -84v-5q0 -84 -93 -84h-246v-65h164q93 0 93 -77v-3q0 -82 -93 -82h-164v-75h251q92 0 92 -79v-5q0 -88 -92 -88h-349q-102 0 -102 110z" />
<glyph unicode="F" horiz-adv-x="571" d="M46 112v424q0 54 26 82.5t76 28.5h316q92 0 92 -80v-6q0 -83 -92 -83h-221v-105h151q92 0 92 -76v-4q0 -81 -92 -81h-144v-123q0 -93 -88 -93h-32q-41 0 -62.5 30.5t-21.5 85.5z" />
<glyph unicode="G" horiz-adv-x="728" d="M29 324q0 148 98 242.5t256 94.5q119 0 187.5 -42t68.5 -107q0 -33 -24 -56.5t-48 -31.5t-28 -3q-53 69 -144 69q-70 0 -117 -49t-47 -127q0 -79 43.5 -127t111.5 -48q52 0 85.5 22.5t41.5 62.5h-67q-49 0 -71.5 15t-22.5 54v9q0 41 22 55t72 14h176q42 0 56 -19.5 t13 -65.5v-271q0 -3 -9 -7.5t-25 -8.5t-33 -4q-79 0 -99 76q-57 -82 -188 -82q-139 0 -223.5 94t-84.5 241z" />
<glyph unicode="H" horiz-adv-x="700" d="M46 88v471q0 93 84 93h29q88 0 88 -93v-150h207v150q0 93 84 93h30q87 0 87 -93v-471q0 -93 -87 -93h-30q-84 0 -84 93v153h-207v-153q0 -93 -88 -93h-29q-84 0 -84 93z" />
<glyph unicode="I" horiz-adv-x="306" d="M53 88v471q0 93 84 93h29q87 0 87 -93v-471q0 -93 -87 -93h-29q-84 0 -84 93z" />
<glyph unicode="J" horiz-adv-x="538" d="M15 115q0 21 6.5 39t16 29.5t19 19t16.5 10.5t8 2q48 -59 118 -59q47 0 67 23.5t20 80.5v299q0 93 85 93h29q87 0 87 -93v-302q0 -271 -258 -271q-103 0 -158.5 34t-55.5 95z" />
<glyph unicode="K" horiz-adv-x="652" d="M46 88v471q0 93 84 93h29q87 0 87 -93v-192l156 217q47 68 110 68q31 0 60 -16.5t43 -34t11 -24.5l-181 -248l190 -260q4 -7 -11 -24.5t-46 -33.5t-64 -16q-64 0 -110 67l-158 219v-193q0 -93 -87 -93h-29q-84 0 -84 93z" />
<glyph unicode="L" horiz-adv-x="552" d="M46 110v449q0 93 84 93h29q87 0 87 -93v-385h197q92 0 92 -83v-3q0 -88 -92 -88h-295q-102 0 -102 110z" />
<glyph unicode="M" horiz-adv-x="772" d="M46 88v468q0 96 89 96h57q39 0 57 -11.5t32 -42.5l105 -259l106 259q14 32 32 43t54 11h56q92 0 92 -96v-468q0 -47 -18 -70t-60 -23h-32q-78 0 -78 93v277l-68 -190q-1 -2 -3 -8.5t-3 -8t-3.5 -7t-5 -7.5t-7 -6t-10 -5t-13.5 -3.5t-18 -3t-22 -0.5q-18 0 -31.5 2.5 t-21 4t-13.5 8.5t-7.5 9t-5.5 12.5t-5 12.5l-67 182v-269q0 -47 -18 -70t-60 -23h-32q-78 0 -78 93z" />
<glyph unicode="N" horiz-adv-x="703" d="M46 88v471q0 46 21.5 69.5t60.5 23.5h19q33 0 50 -10t40 -37l232 -302v256q0 93 87 93h17q84 0 84 -93v-471q0 -46 -21.5 -69.5t-59.5 -23.5h-16q-34 0 -50.5 9t-38.5 35l-237 307v-258q0 -93 -87 -93h-17q-84 0 -84 93z" />
<glyph unicode="O" d="M25 324q0 147 96.5 242t245.5 95t245 -94.5t96 -242.5t-96 -243t-245 -95q-150 0 -246 95t-96 243zM223 324q0 -72 39.5 -115.5t104.5 -43.5t104 43.5t39 115.5q0 71 -39.5 115.5t-103.5 44.5q-66 0 -105 -43.5t-39 -116.5z" />
<glyph unicode="P" horiz-adv-x="614" d="M46 88v448q0 54 26 82.5t76 28.5h171q139 0 212.5 -59.5t73.5 -175.5q0 -230 -280 -230h-78v-94q0 -45 -22.5 -69t-65.5 -24h-29q-40 0 -62 24t-22 69zM244 328h73q93 0 93 84t-96 84h-70v-168z" />
<glyph unicode="Q" d="M25 324q0 147 97 242t247 95q149 0 244 -94.5t95 -242.5q0 -100 -57 -184l80 -88q3 -3 -10 -19t-41.5 -31.5t-60.5 -15.5q-48 0 -95 39q-73 -39 -156 -39q-150 0 -246.5 95t-96.5 243zM223 323q0 -70 41 -114.5t105 -44.5q17 0 33 3l-12 13q-26 28 -29.5 63t23.5 60l6 5 q31 22 61.5 14t57.5 -39l4 -5q5 23 5 46q0 70 -40 115.5t-106 45.5q-68 0 -108.5 -45.5t-40.5 -116.5z" />
<glyph unicode="R" horiz-adv-x="650" d="M46 111v425q0 54 26 82.5t76 28.5h200q270 0 270 -212q0 -71 -36 -121t-104 -69l146 -181q4 -5 -7 -21t-40 -32t-68 -16q-44 0 -72.5 19.5t-62.5 65.5l-92 122h-36v-114q0 -93 -88 -93h-28q-41 0 -62.5 30.5t-21.5 85.5zM243 344h84q90 0 90 75q0 76 -92 76h-82v-151z " />
<glyph unicode="S" horiz-adv-x="588" d="M19 119q0 22 15.5 46.5t31.5 38t19 10.5q33 -30 88 -52.5t104 -22.5q90 0 90 51q0 12 -5 19.5t-18 13t-23 8t-34 6.5t-36 7q-45 9 -80 22.5t-68 36t-51 59t-18 84.5q0 101 72 158t204 57q120 0 182 -35.5t62 -87.5q0 -25 -17 -50.5t-34 -39t-20 -10.5q-33 31 -79.5 50.5 t-91.5 19.5q-87 0 -87 -50q0 -11 5.5 -18t18 -11.5t24 -7t34.5 -6.5t38 -7q224 -49 224 -202q0 -107 -73.5 -163.5t-218.5 -56.5q-133 0 -195.5 38t-62.5 95z" />
<glyph unicode="T" horiz-adv-x="619" d="M211 88v385h-109q-92 0 -92 88v3q0 83 92 83h415q93 0 93 -83v-3q0 -88 -93 -88h-105v-385q0 -93 -90 -93h-28q-83 0 -83 93z" />
<glyph unicode="U" horiz-adv-x="679" d="M43 284v275q0 46 23 69.5t65 23.5h25q88 0 88 -93v-276q0 -120 96 -120q49 0 72 29.5t23 91.5v275q0 93 88 93h26q87 0 87 -93v-275q0 -146 -75 -220.5t-222 -74.5q-296 0 -296 295z" />
<glyph unicode="V" horiz-adv-x="674" d="M225 70l-219 513q-4 10 13 26t48.5 29.5t62.5 13.5q38 0 67.5 -25t43.5 -68l101 -322l100 322q14 46 43 69.5t64 23.5q30 0 60.5 -13.5t46.5 -29.5t12 -26l-213 -513q-17 -40 -44 -57.5t-72 -17.5t-70.5 16.5t-43.5 58.5z" />
<glyph unicode="W" horiz-adv-x="983" d="M183 71l-153 511q-1 6 11.5 21.5t44 31t69.5 15.5q37 0 68 -24.5t42 -82.5l54 -279l68 253q8 36 38 54.5t72 18.5q39 0 69 -19.5t38 -53.5l68 -253l54 282q10 54 40 79t69 25q36 0 65.5 -15.5t41.5 -31t10 -21.5l-147 -511q-20 -77 -118 -77q-48 0 -76.5 17.5t-41.5 59.5 l-76 267l-75 -267q-21 -77 -119 -77q-49 0 -76.5 17t-39.5 60z" />
<glyph unicode="X" horiz-adv-x="671" d="M14 65l183 256l-181 255q-4 7 14 25t51 33.5t66 15.5q66 0 107 -72l84 -143l86 143q39 72 107 72q32 0 63.5 -15.5t48.5 -33t13 -24.5l-183 -256l183 -255q4 -8 -13 -25.5t-49.5 -33t-65.5 -15.5q-69 0 -110 73l-86 143l-86 -144q-40 -73 -107 -73q-32 0 -63.5 16 t-48.5 33.5t-13 24.5z" />
<glyph unicode="Y" horiz-adv-x="639" d="M221 88v165l-225 322q-2 3 1.5 11.5t13 19.5t24.5 20.5t39 16.5t53 7q56 0 94 -69l99 -171l104 171q41 70 95 70q26 0 48.5 -7t36.5 -17t24 -21t13.5 -19.5t1.5 -11.5l-222 -322v-165q0 -93 -87 -93h-29q-84 0 -84 93z" />
<glyph unicode="Z" horiz-adv-x="621" d="M31 79v9q0 28 7.5 49t16 32t29.5 34l246 279h-203q-45 0 -66.5 18t-21.5 60v9q0 78 88 78h362q51 0 76.5 -22.5t25.5 -55.5v-10q0 -39 -12 -61t-41 -53l-251 -279h222q45 0 66.5 -18.5t21.5 -60.5v-8q0 -79 -88 -79h-377q-50 0 -75.5 23t-25.5 56z" />
<glyph unicode="[" horiz-adv-x="406" d="M46 -89v636q0 54 26.5 82.5t76.5 28.5h151q48 0 70 -16.5t22 -55.5v-6q0 -40 -22 -57.5t-70 -17.5h-77v-552h80q48 0 70 -16.5t22 -54.5v-6q0 -40 -22 -57.5t-70 -17.5h-154q-103 0 -103 110z" />
<glyph unicode="\" horiz-adv-x="382" d="M258 -211l-244 760q-8 29 -8 44q0 38 28 53.5t65 15.5q39 0 42 -7l244 -759q8 -30 8 -45q0 -68 -93 -68q-39 0 -42 6z" />
<glyph unicode="]" horiz-adv-x="406" d="M104 -47h80v552h-77q-49 0 -71 17.5t-22 57.5v6q0 39 22 55.5t71 16.5h151q102 0 102 -111v-636q0 -110 -102 -110h-154q-49 0 -71 17.5t-22 57.5v6q0 38 22 54.5t71 16.5z" />
<glyph unicode="^" horiz-adv-x="500" d="M146 438q-27 0 -44 16.5t-17 41.5q0 48 55.5 94.5t109.5 60.5q54 -14 109.5 -60.5t55.5 -94.5q0 -25 -17 -41.5t-44 -16.5q-30 0 -57.5 24.5t-46.5 67.5q-42 -92 -104 -92z" />
<glyph unicode="_" horiz-adv-x="516" d="M8 -65q0 29 20 47t56 18h349q36 0 56 -18t20 -47v-8q0 -30 -20 -48t-56 -18h-349q-36 0 -56 18t-20 48v8z" />
<glyph unicode="`" horiz-adv-x="500" d="M141 577q-27 15 -42.5 40.5t-15.5 52.5q0 31 25 52.5t62 21.5q26 0 53 -16q46 -27 81.5 -86.5t27.5 -74.5q-6 -10 -34 -16.5t-73 -1t-84 27.5z" />
<glyph unicode="a" horiz-adv-x="574" d="M21 136q0 85 73.5 126t230.5 42h19q-6 66 -77 66t-100 -66q0 -1 -13.5 -1.5t-32 2.5t-37 10t-31.5 23.5t-13 40.5q0 60 59 93.5t179 33.5q132 0 194 -52t62 -159v-282q0 -8 -23.5 -15t-50.5 -7q-85 0 -108 80q-25 -38 -68.5 -59.5t-97.5 -21.5q-75 0 -120 40.5t-45 105.5 zM203 153q0 -22 15.5 -34t42.5 -12q38 0 61 23.5t23 61.5v17h-24q-118 -3 -118 -56z" />
<glyph unicode="b" horiz-adv-x="629" d="M44 88v594q0 4 10 9t28 9t36 4q50 0 84 -33t34 -113v-123q48 71 144 71q103 0 165 -70t62 -189q0 -116 -62.5 -188.5t-164.5 -72.5q-50 0 -88.5 24t-57.5 63q-5 -78 -83 -78h-20q-87 0 -87 93zM235 223q0 -40 25.5 -64t66.5 -24q44 0 71 30.5t27 80.5q0 51 -26.5 81 t-70.5 30q-67 0 -93 -59v-75z" />
<glyph unicode="c" horiz-adv-x="512" d="M22 247q0 113 74 185.5t190 72.5q106 0 158.5 -42.5t52.5 -89.5q0 -34 -25 -55.5t-50 -26.5t-27 0q-26 65 -98 65q-45 0 -74 -31t-29 -78q0 -49 28.5 -80.5t74.5 -31.5q73 0 98 66q2 5 27 0.5t50 -26.5t25 -58q0 -45 -54.5 -88t-156.5 -43q-118 0 -191 72.5t-73 188.5z " />
<glyph unicode="d" horiz-adv-x="629" d="M22 247q0 119 62 189t166 70q45 0 83.5 -18.5t60.5 -49.5v244q0 4 10 9t27.5 9t35.5 4q50 0 84 -33t34 -113v-470q0 -93 -84 -93h-20q-82 0 -87 82q-19 -42 -57.5 -66.5t-88.5 -24.5q-102 0 -164 72.5t-62 188.5zM204 246q0 -49 27 -80t71 -31q40 0 66 23.5t26 63.5v78 q-26 57 -90 57q-45 0 -72.5 -30t-27.5 -81z" />
<glyph unicode="e" horiz-adv-x="563" d="M22 242q0 114 74.5 189t189.5 75q114 0 186 -69.5t72 -177.5v-12q-2 -50 -39 -50h-313q18 -78 115 -78q85 0 120 63q0 3 22 -2.5t44 -25t22 -52.5q0 -51 -57.5 -83.5t-159.5 -32.5q-127 0 -201.5 68.5t-74.5 187.5zM189 296h182q-1 39 -24.5 62t-62.5 23q-37 0 -63.5 -24 t-31.5 -61z" />
<glyph unicode="f" horiz-adv-x="416" d="M101 88v263h-20q-78 0 -78 69v11q0 65 78 65h20v14q0 109 50 154t139 45q71 0 106.5 -26t35.5 -65q0 -19 -8.5 -38t-17.5 -28.5t-11 -7.5q-25 23 -61 23q-25 0 -37 -12.5t-12 -42.5v-16h42q78 0 78 -69v-11q0 -65 -78 -65h-34v-263q0 -51 -22 -72t-64 -21h-20 q-86 0 -86 93z" />
<glyph unicode="g" horiz-adv-x="540" d="M-9 -71q0 49 41 79.5t111 38.5q-16 16 -16 45q0 40 42 64q-69 17 -107 59.5t-38 102.5q0 82 68.5 134.5t174.5 52.5q45 0 91 -13l17 34q31 61 86 61q30 0 53 -19.5t32 -38.5t5 -22l-86 -78q46 -49 46 -110q0 -80 -62.5 -127t-172.5 -48v-1q0 -7 5 -12t17.5 -9t24.5 -7 t34.5 -8t40.5 -10q141 -39 141 -147q0 -73 -73 -117t-203 -44q-142 0 -207 34t-65 106zM267 249q31 0 50 19.5t19 51.5t-19 51t-51 19q-30 0 -49.5 -19.5t-19.5 -50.5q0 -32 19.5 -51.5t50.5 -19.5zM172 -57q0 -41 93 -41q92 0 92 42q0 17 -21 25t-69 17q-8 1 -12 2 q-83 -8 -83 -45z" />
<glyph unicode="h" horiz-adv-x="606" d="M44 88v594q0 4 10 9t28 9t36 4q50 0 84 -33t34 -113v-141q51 89 155 89q90 0 133 -57.5t43 -145.5v-215q0 -93 -85 -93h-19q-88 0 -88 93v185q0 36 -17 56.5t-46 20.5q-37 0 -56.5 -21.5t-19.5 -59.5v-181q0 -93 -85 -93h-20q-87 0 -87 93z" />
<glyph unicode="i" horiz-adv-x="286" d="M145 552q-48 0 -76 24.5t-28 66.5q0 41 27.5 65.5t73.5 24.5q48 0 76 -24t28 -66t-27 -66.5t-74 -24.5zM47 88v321q0 92 86 92h20q42 0 64 -21t22 -71v-321q0 -93 -84 -93h-20q-88 0 -88 93z" />
<glyph unicode="j" horiz-adv-x="286" d="M145 552q-48 0 -76 24.5t-28 66.5q0 41 27.5 65.5t73.5 24.5q48 0 76 -24t28 -66t-27 -66.5t-74 -24.5zM47 -7v416q0 92 86 92h20q42 0 64 -21t22 -71v-426q0 -88 -47 -135.5t-134 -47.5q-136 0 -136 98q0 18 9 36t18 27t10 8q16 -21 48 -21q40 0 40 45z" />
<glyph unicode="k" horiz-adv-x="561" d="M44 88v594q0 4 10 9t28 9t36 4q50 0 84 -33t34 -113v-262l99 149q36 58 101 58q27 0 53 -12.5t40 -26.5t11 -20l-133 -189l146 -205q3 -5 -11.5 -19t-41.5 -26.5t-53 -12.5q-66 0 -104 58l-107 155v-117q0 -93 -85 -93h-20q-87 0 -87 93z" />
<glyph unicode="l" horiz-adv-x="286" d="M47 88v594q0 4 10 9t28 9t36 4q50 0 84 -33t34 -113v-470q0 -93 -84 -93h-20q-88 0 -88 93z" />
<glyph unicode="m" horiz-adv-x="909" d="M44 88v391q0 4 10 9t28 9t36 4q87 0 110 -86q60 91 155 91q119 0 154 -99q30 47 67.5 73t95.5 26q90 0 130 -52t40 -138v-228q0 -93 -84 -93h-20q-88 0 -88 93v194q0 68 -59 68q-42 0 -66 -36v-226q0 -93 -85 -93h-19q-88 0 -88 93v192q0 70 -59 70q-42 0 -66 -36v-226 q0 -93 -85 -93h-20q-87 0 -87 93z" />
<glyph unicode="n" horiz-adv-x="606" d="M44 88v391q0 4 10 9t28 9t36 4q87 0 110 -86q60 91 163 91q91 0 133.5 -55.5t42.5 -140.5v-222q0 -93 -85 -93h-19q-88 0 -88 93v189q0 34 -17 53.5t-47 19.5q-50 0 -75 -40v-222q0 -93 -85 -93h-20q-87 0 -87 93z" />
<glyph unicode="o" horiz-adv-x="586" d="M22 247q0 116 75 187.5t198 71.5q121 0 195 -71.5t74 -188.5q0 -118 -74.5 -189t-197.5 -71t-196.5 71t-73.5 190zM185 247q0 -50 30 -82t78 -32q47 0 77.5 32t30.5 81q0 50 -30.5 82.5t-77.5 32.5t-77.5 -32t-30.5 -82z" />
<glyph unicode="p" horiz-adv-x="629" d="M44 -101v580q0 4 10 9t28 9t36 4q89 0 111 -91q18 44 57.5 70t93.5 26q103 0 165 -70t62 -189q0 -116 -62.5 -188.5t-164.5 -72.5q-49 0 -87 23.5t-57 62.5v-173q0 -92 -85 -92h-20q-87 0 -87 92zM235 223q0 -40 25.5 -64t66.5 -24q44 0 71 30.5t27 80.5q0 51 -26.5 81 t-70.5 30q-67 0 -93 -59v-75z" />
<glyph unicode="q" horiz-adv-x="629" d="M22 247q0 119 62 189t166 70q51 0 92.5 -23t60.5 -61q25 79 109 79q18 0 35.5 -4t27.5 -9t10 -9v-580q0 -92 -87 -92h-20q-84 0 -84 92v177q-20 -42 -58 -66t-88 -24q-102 0 -164 72.5t-62 188.5zM204 246q0 -49 27 -80t71 -31q39 0 65 23t27 61v81q-27 57 -90 57 q-45 0 -72.5 -30t-27.5 -81z" />
<glyph unicode="r" horiz-adv-x="467" d="M44 88v391q0 4 10 9t28 9t36 4q88 0 110 -88q32 93 120 93q48 0 76.5 -28t28.5 -75q0 -22 -6.5 -40.5t-15.5 -29.5t-18 -18.5t-15.5 -10.5t-7.5 -2q-28 26 -69 26q-85 0 -85 -122v-118q0 -93 -85 -93h-20q-87 0 -87 93z" />
<glyph unicode="s" horiz-adv-x="492" d="M13 102q0 19 11.5 38t23.5 28.5t14 7.5q62 -58 158 -58q62 0 62 25q0 5 -1.5 9t-6 7t-8.5 5t-12.5 4.5t-14.5 3.5t-18 3.5t-20 4.5q-27 6 -43 10t-44.5 16.5t-45 27.5t-29.5 42t-13 61q0 77 59 122.5t159 45.5q105 0 158.5 -29.5t53.5 -83.5q0 -20 -12.5 -39.5t-25 -29.5 t-15.5 -8q-27 27 -66.5 44t-77.5 17q-59 0 -59 -27q0 -7 3.5 -11.5t12.5 -7.5t17 -5t25 -5t28 -6q29 -6 44.5 -10.5t45.5 -17t47 -27.5t31 -41.5t14 -60.5q0 -79 -61.5 -123.5t-171.5 -44.5q-222 0 -222 113z" />
<glyph unicode="t" horiz-adv-x="439" d="M98 164v187h-11q-78 0 -78 65v11q0 69 78 69h11v48q0 93 86 93h16q86 0 86 -93v-48h57q75 0 75 -65v-11q0 -69 -77 -69h-59v-166q0 -50 42 -50q32 0 55 30q2 3 12 -4.5t20 -25.5t10 -40q0 -48 -40.5 -78.5t-108.5 -30.5q-174 0 -174 178z" />
<glyph unicode="u" horiz-adv-x="606" d="M39 182v227q0 92 85 92h19q42 0 65 -21t23 -71v-193q0 -34 17 -53.5t48 -19.5q49 0 74 39v227q0 92 85 92h20q87 0 87 -92v-392q0 -3 -10 -8t-28 -9.5t-36 -4.5q-87 0 -110 83q-61 -92 -163 -92q-90 0 -133 55.5t-43 140.5z" />
<glyph unicode="v" horiz-adv-x="571" d="M185 38l-177 409q-2 5 12.5 18.5t41.5 25.5t52 12q74 0 101 -88l71 -231l74 231q27 88 98 88q26 0 52 -12t40.5 -25.5t11.5 -18.5l-174 -409q-20 -44 -101 -44q-83 0 -102 44z" />
<glyph unicode="w" horiz-adv-x="809" d="M144 45l-134 402q-2 6 16 19t47.5 25t53.5 12q74 0 87 -73l42 -236l63 208q8 29 33.5 44.5t60.5 15.5q34 0 59 -15.5t32 -44.5l55 -198l42 226q7 39 30.5 56t52.5 17q25 0 54 -12t46 -25t15 -19l-131 -402q-14 -51 -94 -51q-48 0 -71 12.5t-35 50.5l-63 193l-61 -193 q-11 -38 -34.5 -50.5t-70.5 -12.5q-77 0 -95 51z" />
<glyph unicode="x" horiz-adv-x="556" d="M15 53l137 195l-136 196q-4 5 10.5 19t42.5 27t55 13q70 0 98 -62l55 -98l56 98q30 62 96 62q28 0 56.5 -13t43 -27t11.5 -19l-138 -196l137 -195q4 -5 -10.5 -19.5t-43.5 -27.5t-59 -13q-64 0 -94 62l-56 97l-54 -96q-31 -63 -97 -63q-28 0 -56 13t-42.5 27.5 t-11.5 19.5z" />
<glyph unicode="y" horiz-adv-x="570" d="M173 72l-165 375q-2 5 12.5 18.5t41.5 25.5t53 12q74 0 100 -87l75 -217l70 216q26 88 98 88q25 0 51.5 -12t41 -25t12.5 -19l-218 -550q-33 -85 -102 -85q-26 0 -52 11.5t-40 24t-11 17.5l82 171q-38 8 -49 36z" />
<glyph unicode="z" horiz-adv-x="508" d="M23 64v8q0 31 7 46t28 36l179 198h-136q-73 0 -73 68v7q0 69 73 69h306q34 0 52 -19t18 -45v-7q0 -31 -7 -45.5t-28 -35.5l-185 -199h158q72 0 72 -68v-8q0 -68 -72 -68h-324q-34 0 -51 18.5t-17 44.5z" />
<glyph unicode="{" horiz-adv-x="497" d="M112 100q-59 29 -76.5 53.5t-17.5 76.5q0 50 19.5 76t74.5 54l27 14q17 148 86 216.5t159 68.5q44 0 73 -23t29 -61q0 -18 -7.5 -32.5t-15 -21t-10.5 -6.5q-76 -5 -107 -54.5t-36 -162.5l-118 -68l118 -68q5 -113 36 -162.5t106 -54.5q4 -1 11.5 -7.5t15 -21t7.5 -32.5 q0 -38 -29 -61t-73 -23q-43 0 -81 15.5t-72.5 47.5t-59 89.5t-33.5 133.5z" />
<glyph unicode="|" horiz-adv-x="290" d="M49 -107v674q0 92 86 92h20q42 0 64 -21t22 -71v-674q0 -92 -84 -92h-20q-42 0 -65 21t-23 71z" />
<glyph unicode="}" horiz-adv-x="497" d="M45 -55q75 5 106 54.5t36 162.5l118 68l-118 68q-5 113 -36 162.5t-107 54.5q-3 0 -10.5 6.5t-15 21t-7.5 32.5q0 38 29 61t73 23q90 0 159 -68.5t86 -216.5l27 -14q54 -27 74 -53.5t20 -76.5q0 -51 -18.5 -76t-75.5 -54l-26 -14q-9 -76 -33.5 -133.5t-59 -89.5 t-72.5 -47.5t-81 -15.5q-44 0 -73 23t-29 61q0 18 7.5 32.5t15 21t11.5 7.5z" />
<glyph unicode="~" horiz-adv-x="499" d="M130 155q-27 0 -44.5 18.5t-17.5 44.5q0 50 30.5 80.5t72.5 30.5q72 0 133 -61q21 59 65 59q27 0 44.5 -18.5t17.5 -44.5q0 -50 -30.5 -80t-72.5 -30q-73 0 -134 61q-19 -60 -64 -60z" />
<glyph unicode="&#xa1;" horiz-adv-x="297" d="M138 478q-43 0 -63 19.5t-20 61.5v16q0 81 83 81h23q81 0 81 -81v-16q0 -81 -81 -81h-23zM38 111l51 261q9 32 20 46t31 14h19q18 0 28.5 -14t19.5 -46l51 -261q14 -54 -13.5 -88.5t-82.5 -34.5h-25q-57 0 -85 34.5t-14 88.5z" />
<glyph unicode="&#xa2;" horiz-adv-x="523" d="M210 -11v36q-93 22 -145.5 89.5t-52.5 168.5q0 97 52.5 164.5t142.5 90.5v36q0 93 78 93h5q42 0 60 -22.5t18 -70.5v-30q72 -10 108.5 -42t36.5 -79q0 -32 -24.5 -53.5t-48.5 -27.5t-27 -1q-21 32 -45 46t-63 14q-50 0 -81 -32.5t-31 -84.5q0 -54 32 -86.5t83 -32.5 q69 0 107 61q3 5 27 -0.5t48.5 -27t24.5 -53.5q0 -46 -37.5 -79t-107.5 -44v-33q0 -92 -78 -92h-4q-43 0 -60.5 22t-17.5 70z" />
<glyph unicode="&#xa3;" horiz-adv-x="622" d="M17 72v9q0 37 20 53.5t66 16.5h3l15 88h-15q-28 0 -45.5 17t-17.5 43v16q0 26 18 42.5t45 16.5h39l15 80q18 103 79.5 155t158.5 52q94 0 149 -37.5t55 -101.5q0 -44 -26.5 -66t-53 -25t-26.5 1q-14 77 -89 77q-36 0 -56 -18.5t-28 -61.5l-10 -55h109q28 0 46 -16.5 t18 -42.5v-16q0 -27 -17.5 -43.5t-46.5 -16.5h-133l-16 -88h241q46 0 66 -16.5t20 -53.5v-9q0 -38 -20 -55t-66 -17h-411q-46 0 -66 17t-20 55z" />
<glyph unicode="&#xa4;" horiz-adv-x="634" d="M78 199q-31 0 -47 14t-16 34v14q0 20 16 33.5t47 13.5h17v30h-17q-31 0 -47 13.5t-16 34.5v14q0 20 16.5 33.5t46.5 13.5h26q24 100 100.5 157t187.5 57q103 0 163.5 -37.5t60.5 -97.5q0 -29 -21.5 -53t-43 -33.5t-25.5 -5.5q-28 34 -56.5 49.5t-73.5 15.5 q-70 0 -100 -52h119q31 0 47 -13.5t16 -33.5v-14q0 -21 -16 -34.5t-47 -13.5h-135v-30h135q31 0 47 -13.5t16 -33.5v-14q0 -20 -16 -34t-47 -14h-117q30 -51 99 -51q45 0 73.5 15.5t56.5 51.5q4 4 25.5 -5.5t43 -33.5t21.5 -53q0 -54 -56 -95.5t-167 -41.5q-113 0 -189 55.5 t-100 157.5h-27z" />
<glyph unicode="&#xa5;" horiz-adv-x="644" d="M108 101q-34 0 -51.5 15t-17.5 38v14q0 22 18 37t51 15h127v35h-127q-33 0 -51 15t-18 38v14q0 23 18 38t51 15h68l-134 196q-4 7 3.5 25.5t35.5 37.5t72 19q64 0 96 -69l74 -151l79 151q37 71 99 71q23 0 42.5 -8t30.5 -19.5t18.5 -23.5t10 -21t0.5 -12l-133 -196h67 q33 0 50.5 -15t17.5 -38v-14q0 -23 -17.5 -38t-50.5 -15h-125v-35h125q33 0 50.5 -15t17.5 -37v-14q0 -23 -17 -38t-51 -15h-125v-17q0 -47 -19 -70t-61 -23h-20q-77 0 -77 93v17h-127z" />
<glyph unicode="&#xa7;" horiz-adv-x="601" d="M36 -71q0 20 13 39.5t27 29t17 6.5q30 -28 87 -47t103 -19q78 0 78 41q0 11 -6.5 18.5t-25.5 14.5t-31 10t-46 11q-38 9 -62 15.5t-59.5 21t-56 31.5t-36 45t-15.5 62q0 96 104 138q-75 41 -75 124q0 87 67.5 139t185.5 52q122 0 175.5 -35.5t53.5 -82.5q0 -22 -14.5 -43 t-29 -31t-17.5 -7q-74 67 -156 67q-75 0 -75 -42q0 -20 29.5 -31t99.5 -27q44 -11 73.5 -21.5t64 -30t52.5 -49.5t18 -70q0 -95 -102 -138q74 -40 74 -124q0 -87 -72 -139.5t-199 -52.5q-121 0 -182.5 33.5t-61.5 91.5zM186 239q0 -9 2.5 -16t8.5 -13t12 -11t17.5 -9 t19.5 -7t24 -6t25 -5t27.5 -5.5t28.5 -5.5q31 7 47.5 24t16.5 39q0 23 -20.5 38t-48 23t-70.5 17q-8 1 -12 2q-36 -7 -57 -25t-21 -40z" />
<glyph unicode="&#xa8;" horiz-adv-x="500" d="M365 541q-42 0 -66 23t-24 65q0 41 23.5 64.5t63.5 23.5q41 0 65 -23t24 -65q0 -41 -23.5 -64.5t-62.5 -23.5zM139 541q-42 0 -66 23t-24 65q0 41 23.5 64.5t62.5 23.5q42 0 66.5 -23t24.5 -65q0 -41 -23.5 -64.5t-63.5 -23.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="739" d="M33 324q0 147 94 242t243 95t242.5 -94.5t93.5 -242.5t-93.5 -243t-242.5 -95q-150 0 -243.5 94.5t-93.5 243.5zM103 324q0 -118 74.5 -194.5t192.5 -76.5q119 0 192.5 76t73.5 195q0 118 -74.5 194t-191.5 76q-119 0 -193 -75.5t-74 -194.5zM192 323q0 80 52.5 131.5 t134.5 51.5q78 0 118 -40q18 -18 18 -43q0 -18 -14 -33t-27.5 -21.5t-16.5 -3.5q-29 35 -73 35q-30 0 -51 -22t-21 -55q0 -34 21 -56t52 -22q44 0 73 36q3 3 16.5 -3.5t27.5 -21.5t14 -33q1 -23 -19 -45q-40 -40 -117 -40q-83 0 -135.5 52t-52.5 133z" />
<glyph unicode="&#xaa;" horiz-adv-x="456" d="M149 248q-60 0 -96.5 32.5t-36.5 83.5q0 66 54.5 96.5t200.5 32.5q-3 57 -62 57q-51 0 -77 -55q0 -1 -10.5 -1.5t-25 2t-29.5 8t-25.5 19t-10.5 32.5q0 49 46.5 77.5t141.5 28.5q106 0 154.5 -42t48.5 -129v-224q0 -6 -18.5 -11.5t-40.5 -5.5q-67 0 -84 65 q-38 -66 -130 -66zM209 343q28 0 45 18t17 45v18q-60 -2 -85 -12t-25 -33q0 -17 13.5 -26.5t34.5 -9.5zM141 64v15q0 73 73 73h20q75 0 75 -73v-15q0 -73 -75 -73h-20q-73 0 -73 73z" />
<glyph unicode="&#xab;" horiz-adv-x="551" d="M261 247q0 12 29 57.5t76.5 90t85.5 44.5h2q34 0 52 -19t18 -50v-4q0 -31 -39 -64t-84 -55q123 -66 123 -119v-5q0 -30 -18.5 -49t-51.5 -19h-2q-38 0 -85.5 44.5t-76.5 90t-29 57.5zM204 74q-34 0 -78 40t-72 81t-28 52t28 52t72 81t78 40h2q31 0 47 -17t16 -46v-3 q0 -28 -35 -57.5t-75 -49.5q110 -59 110 -108v-3q0 -28 -16 -45t-47 -17h-2z" />
<glyph unicode="&#xad;" horiz-adv-x="469" d="M106 159q-36 0 -56 21t-20 51v31q0 30 20 51t56 21h257q36 0 56 -21t20 -51v-31q0 -30 -20 -51t-56 -21h-257z" />
<glyph unicode="&#xae;" horiz-adv-x="739" d="M33 324q0 147 94 242t243 95t242.5 -94.5t93.5 -242.5t-93.5 -243t-242.5 -95q-150 0 -243.5 94.5t-93.5 243.5zM103 324q0 -118 74.5 -194.5t192.5 -76.5q119 0 192.5 76t73.5 195q0 118 -74.5 194t-191.5 76q-119 0 -193 -75.5t-74 -194.5zM266 144q-24 0 -37 18 t-13 50v226q0 64 60 64h108q154 0 154 -118q0 -75 -69 -101l71 -102q3 -4 -6.5 -13.5t-27 -18t-34.5 -8.5q-41 0 -65 44l-46 72h-28v-59q0 -54 -51 -54h-16zM331 338h45q46 0 46 38t-49 38h-42v-76z" />
<glyph unicode="&#xaf;" horiz-adv-x="500" d="M133 546q-36 0 -57 17t-21 48v8q0 30 21.5 47.5t56.5 17.5h234q36 0 57 -17.5t21 -47.5v-8q0 -31 -20.5 -48t-57.5 -17h-234z" />
<glyph unicode="&#xb0;" horiz-adv-x="375" d="M188 335q-75 0 -121.5 42.5t-46.5 115.5v7q0 73 47 115.5t122 42.5t120.5 -42.5t45.5 -115.5v-7q0 -73 -46 -115.5t-121 -42.5zM189 425q24 0 39 16t15 48v15q0 32 -15.5 48.5t-39.5 16.5q-25 0 -40 -16t-15 -48v-16q0 -31 15.5 -47.5t40.5 -16.5z" />
<glyph unicode="&#xb1;" horiz-adv-x="600" d="M292 209q-30 0 -48 19t-18 57v68h-97q-37 0 -56.5 17.5t-19.5 47.5v8q0 28 19.5 46t56.5 18h97v87q0 37 18 56.5t48 19.5h15q29 0 47 -19.5t18 -56.5v-87h98q37 0 56.5 -18t19.5 -46v-8q0 -30 -19 -47.5t-57 -17.5h-98v-68q0 -37 -18 -56.5t-47 -19.5h-15zM43 95v9 q0 28 19.5 46t56.5 18h361q37 0 56.5 -18t19.5 -46v-9q0 -30 -19 -47.5t-57 -17.5h-361q-37 0 -56.5 17.5t-19.5 47.5z" />
<glyph unicode="&#xb2;" horiz-adv-x="336" d="M96 286q-45 0 -60 11.5t-15 47.5v24q0 46 25 69t85 49q10 4 24 9.5t21 8.5t14.5 7.5t10.5 8.5t3 10q0 15 -10.5 24t-28.5 9q-25 0 -41 -17.5t-20 -41.5q0 -3 -20 -1t-40.5 16t-20.5 41q0 43 39.5 69t109.5 26q69 0 108.5 -32t39.5 -88q0 -25 -7.5 -43.5t-23 -31 t-29.5 -19.5t-37 -15q-11 -4 -30 -10t-29.5 -9.5t-22 -10t-17.5 -14.5h138q59 0 59 -46v-2q0 -49 -59 -49h-166z" />
<glyph unicode="&#xb3;" horiz-adv-x="336" d="M160 277q-67 0 -108.5 24t-41.5 61q0 28 19.5 43.5t36 14.5t20.5 -1q12 -50 70 -50q23 0 36.5 9t13.5 23q0 25 -32 25h-8q-56 0 -56 40v7q0 41 55 41h16q23 0 23 20q0 13 -13.5 21.5t-36.5 8.5q-53 0 -65 -49q0 -2 -7.5 -2.5t-18 2t-21 7.5t-18 18t-7.5 31q0 39 38.5 62 t103.5 23q158 0 158 -103q0 -55 -62 -74q66 -23 66 -89q0 -55 -41 -84t-120 -29z" />
<glyph unicode="&#xb4;" horiz-adv-x="500" d="M167 567q-8 15 27.5 74.5t81.5 86.5q27 16 53 16q36 0 61 -21.5t25 -52.5q0 -27 -15.5 -52.5t-41.5 -40.5q-39 -22 -84 -27.5t-73 1t-34 16.5z" />
<glyph unicode="&#xb6;" horiz-adv-x="626" d="M472 42v564q0 50 46 50h16q47 0 47 -50v-564q0 -51 -47 -51h-16q-46 0 -46 51zM312 42v230h-62q-115 0 -176 48t-61 141q0 194 235 194h109q30 0 46.5 -16t16.5 -45v-552q0 -51 -46 -51h-15q-47 0 -47 51z" />
<glyph unicode="&#xb7;" horiz-adv-x="262" d="M118 158q-82 0 -82 81v16q0 81 82 81h23q83 0 83 -81v-16q0 -81 -83 -81h-23z" />
<glyph unicode="&#xb8;" horiz-adv-x="500" d="M171 -96q10 29 27 41t60 12h20q76 0 76 -52v-5q0 -29 -46 -62l-75 -59q-20 -11 -43.5 -11.5t-41 11t-11.5 28.5z" />
<glyph unicode="&#xb9;" horiz-adv-x="336" d="M85 285q-58 0 -58 50v1q0 53 58 53h40v124q-20 -10 -38 -10q-26 0 -45 17.5t-19 43.5q0 19 8.5 34t16.5 20.5t11 4.5q19 -11 35 -11q18 0 32.5 11t18.5 30h39q56 0 56 -59v-205h23q58 0 58 -53v-1q0 -50 -58 -50h-178z" />
<glyph unicode="&#xba;" horiz-adv-x="476" d="M237 244q-96 0 -155.5 59t-59.5 150q0 89 61.5 148.5t155.5 59.5q96 0 156 -59t60 -149t-61.5 -149.5t-156.5 -59.5zM239 367q34 0 57.5 25t23.5 61q0 37 -23.5 62t-57.5 25q-35 0 -58.5 -25t-23.5 -62q0 -36 23.5 -61t58.5 -25zM153 64v15q0 73 73 73h21q74 0 74 -73 v-15q0 -73 -74 -73h-21q-73 0 -73 73z" />
<glyph unicode="&#xbb;" horiz-adv-x="551" d="M97 55q-34 0 -52 19t-18 50v4q0 53 123 119q-44 22 -83.5 55t-39.5 64v4q0 32 18 50.5t52 18.5h2q38 0 85 -44.5t76 -90t29 -57.5t-29 -57.5t-76 -90t-85 -44.5h-2zM282 136v4q0 48 110 107q-40 20 -75 49.5t-35 57.5v4q0 28 16 45t46 17h3q34 0 78 -40t72 -81t28 -52 t-28 -52t-72 -81t-78 -40h-3q-30 0 -46 17.5t-16 44.5z" />
<glyph unicode="&#xbc;" horiz-adv-x="792" d="M85 285q-58 0 -58 50v1q0 53 58 53h40v124q-20 -10 -38 -10q-26 0 -45 17.5t-19 43.5q0 19 8.5 34t16.5 20.5t11 4.5q19 -11 35 -11q18 0 32.5 11t18.5 30h39q56 0 56 -59v-205h23q58 0 58 -53v-1q0 -50 -58 -50h-178zM132 12q1 22 23 50l15 17q14 17 35 42t46 54.5 t53 64t51 62.5l267 336q5 2 17.5 1.5t25 -13.5t12.5 -32q-2 -25 -23 -50q-178 -213 -201 -242l-265 -336q-6 -2 -18.5 -0.5t-25 14.5t-12.5 32zM456 125v16q1 19 19 55l72 132q13 26 32.5 33t69.5 7q47 0 66.5 -18t19.5 -64v-122h2q30 0 42 -9.5t12 -32.5v-3 q0 -25 -12 -34.5t-42 -9.5h-2v-22q0 -58 -53 -58h-7q-51 0 -51 58v22h-102q-30 0 -48 14t-18 36zM555 162h76v133z" />
<glyph unicode="&#xbd;" horiz-adv-x="792" d="M85 285q-58 0 -58 50v1q0 53 58 53h40v124q-20 -10 -38 -10q-26 0 -45 17.5t-19 43.5q0 19 8.5 34t16.5 20.5t11 4.5q19 -11 35 -11q18 0 32.5 11t18.5 30h39q56 0 56 -59v-205h23q58 0 58 -53v-1q0 -50 -58 -50h-178zM110 12q1 22 23 50l200 240l267 336q5 2 17.5 1.5 t25 -13.5t12.5 -32q-2 -25 -23 -50q-178 -213 -201 -242l-265 -336q-6 -2 -18.5 -0.5t-25 14.5t-12.5 32zM477 60v24q0 46 25 69t85 49q10 4 24 9.5t21 8.5t14.5 7.5t10.5 8.5t3 10q0 15 -10.5 24t-28.5 9q-25 0 -41 -17.5t-20 -41.5q0 -3 -20 -1t-40.5 16t-20.5 41 q0 43 39.5 69t109.5 26q69 0 108.5 -32t39.5 -88q0 -25 -7.5 -43.5t-23 -31t-29.5 -19.5t-37 -15q-11 -4 -30 -10t-29.5 -9.5t-22 -10t-17.5 -14.5h138q59 0 59 -46v-2q0 -49 -59 -49h-166q-45 0 -60 11.5t-15 47.5z" />
<glyph unicode="&#xbe;" horiz-adv-x="792" d="M160 277q-67 0 -108.5 24t-41.5 61q0 28 19.5 43.5t36 14.5t20.5 -1q12 -50 70 -50q23 0 36.5 9t13.5 23q0 25 -32 25h-8q-56 0 -56 40v7q0 41 55 41h16q23 0 23 20q0 13 -13.5 21.5t-36.5 8.5q-53 0 -65 -49q0 -2 -7.5 -2.5t-18 2t-21 7.5t-18 18t-7.5 31q0 39 38.5 62 t103.5 23q158 0 158 -103q0 -55 -62 -74q66 -23 66 -89q0 -55 -41 -84t-120 -29zM125 12q1 22 23 50l14 17q15 17 36 42t46 54.5t53 64t51 62.5l267 336q5 2 17.5 1.5t25 -13.5t12.5 -32q-2 -25 -23 -50q-178 -213 -201 -242l-265 -336q-6 -2 -18.5 -0.5t-25 14.5t-12.5 32z M456 125v16q1 19 19 55l72 132q13 26 32.5 33t69.5 7q47 0 66.5 -18t19.5 -64v-122h2q30 0 42 -9.5t12 -32.5v-3q0 -25 -12 -34.5t-42 -9.5h-2v-22q0 -58 -53 -58h-7q-51 0 -51 58v22h-102q-30 0 -48 14t-18 36zM555 162h76v133z" />
<glyph unicode="&#xbf;" horiz-adv-x="522" d="M270 478q-84 0 -84 81v16q0 81 84 81h22q82 0 82 -81v-16q0 -81 -82 -81h-22zM16 177q0 46 18 78.5t44 50t52.5 31t47.5 30.5t25 40q11 33 77 33q37 0 59 -17t21 -50q0 -40 -17 -68t-41 -41.5t-48 -23.5t-40.5 -21.5t-16.5 -28.5q0 -23 18 -36.5t51 -13.5q49 0 79 29.5 t36 71.5q0 1 13 1.5t31.5 -3t37.5 -12t32 -28t13 -47.5q0 -68 -66 -116.5t-179 -48.5q-116 0 -181.5 51t-65.5 139z" />
<glyph unicode="&#xc0;" horiz-adv-x="688" d="M227 732q-27 15 -42.5 40.5t-15.5 52.5q0 31 25 52.5t62 21.5q26 0 53 -16q46 -27 81.5 -86.5t27.5 -74.5q-6 -10 -34 -16.5t-73 -1t-84 27.5zM6 53l210 544q8 27 36 41t91 14t89 -14t35 -41l218 -542q4 -9 -10.5 -24t-41.5 -27.5t-55 -12.5q-78 0 -115 95l-18 48h-209 l-18 -50q-32 -93 -109 -93q-39 0 -74 23.5t-29 38.5zM270 278h139l-70 188z" />
<glyph unicode="&#xc1;" horiz-adv-x="688" d="M265 722q-8 15 27.5 74.5t81.5 86.5q27 16 53 16q36 0 61 -21.5t25 -52.5q0 -27 -15.5 -52.5t-41.5 -40.5q-39 -22 -84 -27.5t-73 1t-34 16.5zM6 53l210 544q8 27 36 41t91 14t89 -14t35 -41l218 -542q4 -9 -10.5 -24t-41.5 -27.5t-55 -12.5q-78 0 -115 95l-18 48h-209 l-18 -50q-32 -93 -109 -93q-39 0 -74 23.5t-29 38.5zM270 278h139l-70 188z" />
<glyph unicode="&#xc2;" horiz-adv-x="688" d="M239 696q-27 0 -44 16.5t-17 41.5q0 48 55.5 94.5t109.5 60.5q54 -14 109.5 -60.5t55.5 -94.5q0 -25 -17 -41.5t-44 -16.5q-30 0 -57.5 24.5t-46.5 67.5q-42 -92 -104 -92zM6 53l210 544q8 27 36 41t91 14t89 -14t35 -41l218 -542q4 -9 -10.5 -24t-41.5 -27.5t-55 -12.5 q-78 0 -115 95l-18 48h-209l-18 -50q-32 -93 -109 -93q-39 0 -74 23.5t-29 38.5zM270 278h139l-70 188z" />
<glyph unicode="&#xc3;" horiz-adv-x="688" d="M226 697q-27 0 -44.5 18.5t-17.5 44.5q0 50 30.5 80.5t72.5 30.5q72 0 133 -61q21 59 65 59q27 0 44.5 -18.5t17.5 -44.5q0 -50 -30.5 -80t-72.5 -30q-73 0 -134 61q-19 -60 -64 -60zM6 53l210 544q8 27 36 41t91 14t89 -14t35 -41l218 -542q4 -9 -10.5 -24t-41.5 -27.5 t-55 -12.5q-78 0 -115 95l-18 48h-209l-18 -50q-32 -93 -109 -93q-39 0 -74 23.5t-29 38.5zM270 278h139l-70 188z" />
<glyph unicode="&#xc4;" horiz-adv-x="688" d="M458 696q-42 0 -66 23t-24 65q0 41 23.5 64.5t63.5 23.5q41 0 65 -23t24 -65q0 -41 -23.5 -64.5t-62.5 -23.5zM232 696q-42 0 -66 23t-24 65q0 41 23.5 64.5t62.5 23.5q42 0 66.5 -23t24.5 -65q0 -41 -23.5 -64.5t-63.5 -23.5zM6 53l210 544q8 27 36 41t91 14t89 -14 t35 -41l218 -542q4 -9 -10.5 -24t-41.5 -27.5t-55 -12.5q-78 0 -115 95l-18 48h-209l-18 -50q-32 -93 -109 -93q-39 0 -74 23.5t-29 38.5zM270 278h139l-70 188z" />
<glyph unicode="&#xc5;" horiz-adv-x="688" d="M6 53l210 544q9 22 41 35q-44 31 -44 92q0 55 37.5 90.5t94.5 35.5t93.5 -35.5t36.5 -90.5q0 -61 -47 -93q30 -13 39 -34l218 -542q4 -9 -10.5 -24t-41.5 -27.5t-55 -12.5q-78 0 -115 95l-18 48h-209l-18 -50q-32 -93 -109 -93q-39 0 -74 23.5t-29 38.5zM344 676 q19 0 32.5 14t13.5 34q0 21 -13.5 35t-32.5 14q-20 0 -33 -14t-13 -35q0 -20 13.5 -34t32.5 -14zM270 278h139l-69 188z" />
<glyph unicode="&#xc6;" horiz-adv-x="895" d="M6 53l204 530q11 32 39.5 48t90.5 16h432q92 0 92 -84v-5q0 -84 -92 -84h-263l25 -65h155q93 0 93 -77v-4q0 -81 -93 -81h-88l30 -75h145q93 0 93 -79v-5q0 -88 -93 -88h-178q-56 0 -85.5 17.5t-50.5 71.5l-17 45h-209l-18 -50q-33 -93 -108 -93q-27 0 -53.5 12 t-40.5 26.5t-10 23.5zM270 278h139l-70 188z" />
<glyph unicode="&#xc7;" horiz-adv-x="650" d="M29 324q0 148 94.5 242.5t245.5 94.5q124 0 189 -50.5t65 -107.5q0 -38 -28.5 -63t-56.5 -32.5t-30 -2.5q-39 73 -128 73q-63 0 -104 -42.5t-41 -111.5t41.5 -112t103.5 -43q95 0 130 78q2 5 30.5 -2.5t57 -32.5t28.5 -63q0 -58 -66 -110.5t-192 -52.5q-151 0 -245 94.5 t-94 243.5zM272 -96q10 29 27 41t60 12h20q76 0 76 -52v-5q0 -29 -46 -62l-75 -59q-20 -11 -43.5 -11.5t-41 11t-11.5 28.5z" />
<glyph unicode="&#xc8;" horiz-adv-x="616" d="M227 732q-27 15 -42.5 40.5t-15.5 52.5q0 31 25 52.5t62 21.5q26 0 53 -16q46 -27 81.5 -86.5t27.5 -74.5q-6 -10 -34 -16.5t-73 -1t-84 27.5zM46 110v426q0 54 26 82.5t76 28.5h344q93 0 93 -84v-5q0 -84 -93 -84h-246v-65h164q93 0 93 -77v-3q0 -82 -93 -82h-164v-75 h251q92 0 92 -79v-5q0 -88 -92 -88h-349q-102 0 -102 110z" />
<glyph unicode="&#xc9;" horiz-adv-x="616" d="M221 722q-8 15 27.5 74.5t81.5 86.5q27 16 53 16q36 0 61 -21.5t25 -52.5q0 -27 -15.5 -52.5t-41.5 -40.5q-39 -22 -84 -27.5t-73 1t-34 16.5zM46 110v426q0 54 26 82.5t76 28.5h344q93 0 93 -84v-5q0 -84 -93 -84h-246v-65h164q93 0 93 -77v-3q0 -82 -93 -82h-164v-75 h251q92 0 92 -79v-5q0 -88 -92 -88h-349q-102 0 -102 110z" />
<glyph unicode="&#xca;" horiz-adv-x="616" d="M217 696q-27 0 -44 16.5t-17 41.5q0 48 55.5 94.5t109.5 60.5q54 -14 109.5 -60.5t55.5 -94.5q0 -25 -17 -41.5t-44 -16.5q-30 0 -57.5 24.5t-46.5 67.5q-42 -92 -104 -92zM46 110v426q0 54 26 82.5t76 28.5h344q93 0 93 -84v-5q0 -84 -93 -84h-246v-65h164q93 0 93 -77 v-3q0 -82 -93 -82h-164v-75h251q92 0 92 -79v-5q0 -88 -92 -88h-349q-102 0 -102 110z" />
<glyph unicode="&#xcb;" horiz-adv-x="616" d="M436 696q-42 0 -66 23t-24 65q0 41 23.5 64.5t63.5 23.5q41 0 65 -23t24 -65q0 -41 -23.5 -64.5t-62.5 -23.5zM210 696q-42 0 -66 23t-24 65q0 41 23.5 64.5t62.5 23.5q42 0 66.5 -23t24.5 -65q0 -41 -23.5 -64.5t-63.5 -23.5zM46 110v426q0 54 26 82.5t76 28.5h344 q93 0 93 -84v-5q0 -84 -93 -84h-246v-65h164q93 0 93 -77v-3q0 -82 -93 -82h-164v-75h251q92 0 92 -79v-5q0 -88 -92 -88h-349q-102 0 -102 110z" />
<glyph unicode="&#xcc;" horiz-adv-x="306" d="M51 732q-27 15 -42.5 40.5t-15.5 52.5q0 31 25 52.5t62 21.5q26 0 53 -16q46 -27 81.5 -86.5t27.5 -74.5q-6 -10 -34 -16.5t-73 -1t-84 27.5zM53 88v471q0 93 84 93h29q87 0 87 -93v-471q0 -93 -87 -93h-29q-84 0 -84 93z" />
<glyph unicode="&#xcd;" horiz-adv-x="306" d="M57 722q-8 15 27.5 74.5t81.5 86.5q27 16 53 16q36 0 61 -21.5t25 -52.5q0 -27 -15.5 -52.5t-41.5 -40.5q-39 -22 -84 -27.5t-73 1t-34 16.5zM53 88v471q0 93 84 93h29q87 0 87 -93v-471q0 -93 -87 -93h-29q-84 0 -84 93z" />
<glyph unicode="&#xce;" horiz-adv-x="306" d="M46 696q-27 0 -44 16.5t-17 41.5q0 48 55.5 94.5t109.5 60.5q54 -14 109.5 -60.5t55.5 -94.5q0 -25 -17 -41.5t-44 -16.5q-30 0 -57.5 24.5t-46.5 67.5q-42 -92 -104 -92zM53 88v471q0 93 84 93h29q87 0 87 -93v-471q0 -93 -87 -93h-29q-84 0 -84 93z" />
<glyph unicode="&#xcf;" horiz-adv-x="306" d="M265 696q-42 0 -66 23t-24 65q0 41 23.5 64.5t63.5 23.5q41 0 65 -23t24 -65q0 -41 -23.5 -64.5t-62.5 -23.5zM39 696q-42 0 -66 23t-24 65q0 41 23.5 64.5t62.5 23.5q42 0 66.5 -23t24.5 -65q0 -41 -23.5 -64.5t-63.5 -23.5zM53 88v471q0 93 84 93h29q87 0 87 -93v-471 q0 -93 -87 -93h-29q-84 0 -84 93z" />
<glyph unicode="&#xd0;" horiz-adv-x="731" d="M84 110v147h-17q-31 0 -47.5 17t-16.5 43v16q0 25 16.5 42t47.5 17h17v144q0 53 29 82t75 29h173q159 0 252 -86t93 -234q0 -149 -94.5 -238t-254.5 -89h-161q-47 0 -79.5 32t-32.5 78zM283 171h70q71 0 113.5 41t42.5 114q0 72 -41 111t-112 39h-73v-84h66q31 0 47 -17 t16 -42v-16q0 -26 -16 -43t-47 -17h-66v-86z" />
<glyph unicode="&#xd1;" horiz-adv-x="703" d="M232 697q-27 0 -44.5 18.5t-17.5 44.5q0 50 30.5 80.5t72.5 30.5q72 0 133 -61q21 59 65 59q27 0 44.5 -18.5t17.5 -44.5q0 -50 -30.5 -80t-72.5 -30q-73 0 -134 61q-19 -60 -64 -60zM46 88v471q0 46 21.5 69.5t60.5 23.5h19q33 0 50 -10t40 -37l232 -302v256q0 93 87 93 h17q84 0 84 -93v-471q0 -46 -21.5 -69.5t-59.5 -23.5h-16q-34 0 -50.5 9t-38.5 35l-237 307v-258q0 -93 -87 -93h-17q-84 0 -84 93z" />
<glyph unicode="&#xd2;" d="M253 732q-27 15 -42.5 40.5t-15.5 52.5q0 31 25 52.5t62 21.5q26 0 53 -16q46 -27 81.5 -86.5t27.5 -74.5q-6 -10 -34 -16.5t-73 -1t-84 27.5zM25 324q0 147 96.5 242t245.5 95t245 -94.5t96 -242.5t-96 -243t-245 -95q-150 0 -246 95t-96 243zM223 324 q0 -72 39.5 -115.5t104.5 -43.5t104 43.5t39 115.5q0 71 -39.5 115.5t-103.5 44.5q-66 0 -105 -43.5t-39 -116.5z" />
<glyph unicode="&#xd3;" d="M274 722q-8 15 27.5 74.5t81.5 86.5q27 16 53 16q36 0 61 -21.5t25 -52.5q0 -27 -15.5 -52.5t-41.5 -40.5q-39 -22 -84 -27.5t-73 1t-34 16.5zM25 324q0 147 96.5 242t245.5 95t245 -94.5t96 -242.5t-96 -243t-245 -95q-150 0 -246 95t-96 243zM223 324 q0 -72 39.5 -115.5t104.5 -43.5t104 43.5t39 115.5q0 71 -39.5 115.5t-103.5 44.5q-66 0 -105 -43.5t-39 -116.5z" />
<glyph unicode="&#xd4;" d="M263 696q-27 0 -44 16.5t-17 41.5q0 48 55.5 94.5t109.5 60.5q54 -14 109.5 -60.5t55.5 -94.5q0 -25 -17 -41.5t-44 -16.5q-30 0 -57.5 24.5t-46.5 67.5q-42 -92 -104 -92zM25 324q0 147 96.5 242t245.5 95t245 -94.5t96 -242.5t-96 -243t-245 -95q-150 0 -246 95 t-96 243zM223 324q0 -72 39.5 -115.5t104.5 -43.5t104 43.5t39 115.5q0 71 -39.5 115.5t-103.5 44.5q-66 0 -105 -43.5t-39 -116.5z" />
<glyph unicode="&#xd5;" d="M248 697q-27 0 -44.5 18.5t-17.5 44.5q0 50 30.5 80.5t72.5 30.5q72 0 133 -61q21 59 65 59q27 0 44.5 -18.5t17.5 -44.5q0 -50 -30.5 -80t-72.5 -30q-73 0 -134 61q-19 -60 -64 -60zM25 324q0 147 96.5 242t245.5 95t245 -94.5t96 -242.5t-96 -243t-245 -95 q-150 0 -246 95t-96 243zM223 324q0 -72 39.5 -115.5t104.5 -43.5t104 43.5t39 115.5q0 71 -39.5 115.5t-103.5 44.5q-66 0 -105 -43.5t-39 -116.5z" />
<glyph unicode="&#xd6;" d="M482 696q-42 0 -66 23t-24 65q0 41 23.5 64.5t63.5 23.5q41 0 65 -23t24 -65q0 -41 -23.5 -64.5t-62.5 -23.5zM256 696q-42 0 -66 23t-24 65q0 41 23.5 64.5t62.5 23.5q42 0 66.5 -23t24.5 -65q0 -41 -23.5 -64.5t-63.5 -23.5zM25 324q0 147 96.5 242t245.5 95t245 -94.5 t96 -242.5t-96 -243t-245 -95q-150 0 -246 95t-96 243zM223 324q0 -72 39.5 -115.5t104.5 -43.5t104 43.5t39 115.5q0 71 -39.5 115.5t-103.5 44.5q-66 0 -105 -43.5t-39 -116.5z" />
<glyph unicode="&#xd7;" horiz-adv-x="565" d="M75 53q-48 48 7 100l103 99l-103 99q-27 25 -27.5 53t19.5 46l11 12q19 22 46.5 20.5t53.5 -29.5l98 -103l97 103q26 28 54 29.5t47 -20.5l10 -12q20 -18 19.5 -46t-27.5 -53l-102 -99l102 -99q55 -53 8 -100l-12 -12q-16 -20 -45 -19.5t-54 28.5l-97 103l-98 -103 q-25 -28 -53.5 -28.5t-45.5 19.5z" />
<glyph unicode="&#xd8;" d="M33 14q0 21 23 47l42 47q-71 90 -71 216q0 146 96 241.5t246 95.5q102 0 184 -49l68 74q4 5 22.5 -0.5t36 -21.5t17.5 -36q0 -22 -22 -47l-38 -42q72 -89 72 -215q0 -147 -96 -242.5t-245 -95.5q-103 0 -187 49l-71 -79q-4 -5 -22.5 1t-36.5 21.5t-18 35.5zM224 247 l210 233q-31 13 -66 13q-73 0 -115.5 -46.5t-42.5 -122.5q0 -43 14 -77zM302 169q29 -13 67 -13q70 0 114 47t44 121q0 43 -15 77z" />
<glyph unicode="&#xd9;" horiz-adv-x="679" d="M227 729q-27 15 -42.5 40.5t-15.5 52.5q0 31 25 52.5t62 21.5q26 0 53 -16q46 -27 81.5 -86.5t27.5 -74.5q-6 -10 -34 -16.5t-73 -1t-84 27.5zM43 284v275q0 46 23 69.5t65 23.5h25q88 0 88 -93v-276q0 -120 96 -120q49 0 72 29.5t23 91.5v275q0 93 88 93h26q87 0 87 -93 v-275q0 -146 -75 -220.5t-222 -74.5q-296 0 -296 295z" />
<glyph unicode="&#xda;" horiz-adv-x="679" d="M261 719q-8 15 27.5 74.5t81.5 86.5q27 16 53 16q36 0 61 -21.5t25 -52.5q0 -27 -15.5 -52.5t-41.5 -40.5q-39 -22 -84 -27.5t-73 1t-34 16.5zM43 284v275q0 46 23 69.5t65 23.5h25q88 0 88 -93v-276q0 -120 96 -120q49 0 72 29.5t23 91.5v275q0 93 88 93h26q87 0 87 -93 v-275q0 -146 -75 -220.5t-222 -74.5q-296 0 -296 295z" />
<glyph unicode="&#xdb;" horiz-adv-x="679" d="M235 696q-27 0 -44 16.5t-17 41.5q0 48 55.5 94.5t109.5 60.5q54 -14 109.5 -60.5t55.5 -94.5q0 -25 -17 -41.5t-44 -16.5q-30 0 -57.5 24.5t-46.5 67.5q-42 -92 -104 -92zM43 284v275q0 46 23 69.5t65 23.5h25q88 0 88 -93v-276q0 -120 96 -120q49 0 72 29.5t23 91.5 v275q0 93 88 93h26q87 0 87 -93v-275q0 -146 -75 -220.5t-222 -74.5q-296 0 -296 295z" />
<glyph unicode="&#xdc;" horiz-adv-x="679" d="M454 696q-42 0 -66 23t-24 65q0 41 23.5 64.5t63.5 23.5q41 0 65 -23t24 -65q0 -41 -23.5 -64.5t-62.5 -23.5zM228 696q-42 0 -66 23t-24 65q0 41 23.5 64.5t62.5 23.5q42 0 66.5 -23t24.5 -65q0 -41 -23.5 -64.5t-63.5 -23.5zM43 284v275q0 46 23 69.5t65 23.5h25 q88 0 88 -93v-276q0 -120 96 -120q49 0 72 29.5t23 91.5v275q0 93 88 93h26q87 0 87 -93v-275q0 -146 -75 -220.5t-222 -74.5q-296 0 -296 295z" />
<glyph unicode="&#xdd;" horiz-adv-x="639" d="M238 719q-8 15 27.5 74.5t81.5 86.5q27 16 53 16q36 0 61 -21.5t25 -52.5q0 -27 -15.5 -52.5t-41.5 -40.5q-39 -22 -84 -27.5t-73 1t-34 16.5zM221 88v165l-225 322q-2 3 1.5 11.5t13 19.5t24.5 20.5t39 16.5t53 7q56 0 94 -69l99 -171l104 171q41 70 95 70q26 0 48.5 -7 t36.5 -17t24 -21t13.5 -19.5t1.5 -11.5l-222 -322v-165q0 -93 -87 -93h-29q-84 0 -84 93z" />
<glyph unicode="&#xde;" horiz-adv-x="623" d="M46 88v471q0 93 84 93h29q87 0 87 -93v-8h69q141 0 213.5 -57.5t72.5 -169.5q0 -223 -280 -223h-75v-13q0 -93 -87 -93h-29q-84 0 -84 93zM246 247h71q93 0 93 76q0 77 -93 77h-71v-153z" />
<glyph unicode="&#xdf;" horiz-adv-x="625" d="M44 84v363q0 138 65.5 199.5t210.5 61.5q119 0 186 -47.5t67 -128.5q0 -110 -103 -135q69 -28 106.5 -81t37.5 -122q0 -93 -54.5 -150.5t-142.5 -57.5q-75 0 -113.5 26t-38.5 73q0 18 9.5 36t19 27.5t11.5 8.5q28 -24 65 -24q26 0 45 17.5t19 42.5q0 31 -20.5 51 t-66.5 34q-33 13 -50 31.5t-17 57.5q0 19 8 37t15.5 26.5t8.5 8.5q79 0 79 65q0 30 -20.5 47t-53.5 17q-81 0 -81 -102v-382q0 -45 -23 -69t-63 -24h-20q-39 0 -62.5 25t-23.5 68z" />
<glyph unicode="&#xe0;" horiz-adv-x="574" d="M186 577q-27 15 -42.5 40.5t-15.5 52.5q0 31 25 52.5t62 21.5q26 0 53 -16q46 -27 81.5 -86.5t27.5 -74.5q-6 -10 -34 -16.5t-73 -1t-84 27.5zM21 136q0 85 73.5 126t230.5 42h19q-6 66 -77 66t-100 -66q0 -1 -13.5 -1.5t-32 2.5t-37 10t-31.5 23.5t-13 40.5 q0 60 59 93.5t179 33.5q132 0 194 -52t62 -159v-282q0 -8 -23.5 -15t-50.5 -7q-85 0 -108 80q-25 -38 -68.5 -59.5t-97.5 -21.5q-75 0 -120 40.5t-45 105.5zM203 153q0 -22 15.5 -34t42.5 -12q38 0 61 23.5t23 61.5v17h-24q-118 -3 -118 -56z" />
<glyph unicode="&#xe1;" horiz-adv-x="574" d="M197 567q-8 15 27.5 74.5t81.5 86.5q27 16 53 16q36 0 61 -21.5t25 -52.5q0 -27 -15.5 -52.5t-41.5 -40.5q-39 -22 -84 -27.5t-73 1t-34 16.5zM21 136q0 85 73.5 126t230.5 42h19q-6 66 -77 66t-100 -66q0 -1 -13.5 -1.5t-32 2.5t-37 10t-31.5 23.5t-13 40.5 q0 60 59 93.5t179 33.5q132 0 194 -52t62 -159v-282q0 -8 -23.5 -15t-50.5 -7q-85 0 -108 80q-25 -38 -68.5 -59.5t-97.5 -21.5q-75 0 -120 40.5t-45 105.5zM203 153q0 -22 15.5 -34t42.5 -12q38 0 61 23.5t23 61.5v17h-24q-118 -3 -118 -56z" />
<glyph unicode="&#xe2;" horiz-adv-x="574" d="M184 541q-27 0 -44 16.5t-17 41.5q0 48 55.5 94.5t109.5 60.5q54 -14 109.5 -60.5t55.5 -94.5q0 -25 -17 -41.5t-44 -16.5q-30 0 -57.5 24.5t-46.5 67.5q-42 -92 -104 -92zM21 136q0 85 73.5 126t230.5 42h19q-6 66 -77 66t-100 -66q0 -1 -13.5 -1.5t-32 2.5t-37 10 t-31.5 23.5t-13 40.5q0 60 59 93.5t179 33.5q132 0 194 -52t62 -159v-282q0 -8 -23.5 -15t-50.5 -7q-85 0 -108 80q-25 -38 -68.5 -59.5t-97.5 -21.5q-75 0 -120 40.5t-45 105.5zM203 153q0 -22 15.5 -34t42.5 -12q38 0 61 23.5t23 61.5v17h-24q-118 -3 -118 -56z" />
<glyph unicode="&#xe3;" horiz-adv-x="574" d="M161 542q-27 0 -44.5 18.5t-17.5 44.5q0 50 30.5 80.5t72.5 30.5q72 0 133 -61q21 59 65 59q27 0 44.5 -18.5t17.5 -44.5q0 -50 -30.5 -80t-72.5 -30q-73 0 -134 61q-19 -60 -64 -60zM21 136q0 85 73.5 126t230.5 42h19q-6 66 -77 66t-100 -66q0 -1 -13.5 -1.5t-32 2.5 t-37 10t-31.5 23.5t-13 40.5q0 60 59 93.5t179 33.5q132 0 194 -52t62 -159v-282q0 -8 -23.5 -15t-50.5 -7q-85 0 -108 80q-25 -38 -68.5 -59.5t-97.5 -21.5q-75 0 -120 40.5t-45 105.5zM203 153q0 -22 15.5 -34t42.5 -12q38 0 61 23.5t23 61.5v17h-24q-118 -3 -118 -56z " />
<glyph unicode="&#xe4;" horiz-adv-x="574" d="M395 541q-42 0 -66 23t-24 65q0 41 23.5 64.5t63.5 23.5q41 0 65 -23t24 -65q0 -41 -23.5 -64.5t-62.5 -23.5zM169 541q-42 0 -66 23t-24 65q0 41 23.5 64.5t62.5 23.5q42 0 66.5 -23t24.5 -65q0 -41 -23.5 -64.5t-63.5 -23.5zM21 136q0 85 73.5 126t230.5 42h19 q-6 66 -77 66t-100 -66q0 -1 -13.5 -1.5t-32 2.5t-37 10t-31.5 23.5t-13 40.5q0 60 59 93.5t179 33.5q132 0 194 -52t62 -159v-282q0 -8 -23.5 -15t-50.5 -7q-85 0 -108 80q-25 -38 -68.5 -59.5t-97.5 -21.5q-75 0 -120 40.5t-45 105.5zM203 153q0 -22 15.5 -34t42.5 -12 q38 0 61 23.5t23 61.5v17h-24q-118 -3 -118 -56z" />
<glyph unicode="&#xe5;" horiz-adv-x="574" d="M279 541q-59 0 -94.5 35.5t-35.5 90.5q0 54 37 90t95 36t94 -36t36 -90q0 -55 -36.5 -90.5t-95.5 -35.5zM280 619q19 0 32.5 14t13.5 34t-13.5 34.5t-32.5 14.5t-32.5 -14.5t-13.5 -34.5q0 -21 13 -34.5t33 -13.5zM21 136q0 85 73.5 126t230.5 42h19q-6 66 -77 66 t-100 -66q0 -1 -13.5 -1.5t-32 2.5t-37 10t-31.5 23.5t-13 40.5q0 60 59 93.5t179 33.5q132 0 194 -52t62 -159v-282q0 -8 -23.5 -15t-50.5 -7q-85 0 -108 80q-25 -38 -68.5 -59.5t-97.5 -21.5q-75 0 -120 40.5t-45 105.5zM203 153q0 -22 15.5 -34t42.5 -12q38 0 61 23.5 t23 61.5v17h-24q-118 -3 -118 -56z" />
<glyph unicode="&#xe6;" horiz-adv-x="872" d="M21 136q0 85 73.5 126t230.5 42h19q-6 66 -77 66t-100 -66q0 -1 -13.5 -1.5t-32 2.5t-37 10t-31.5 23.5t-13 40.5q0 60 55.5 93.5t164.5 33.5q117 0 174 -55q63 55 161 55q113 0 185 -69.5t72 -177.5v-12q-2 -50 -38 -50h-314q18 -78 115 -78q85 0 120 63q1 3 23 -2.5 t44 -25t22 -52.5q0 -51 -57.5 -83.5t-159.5 -32.5q-159 0 -218 103q-27 -45 -80.5 -72t-120.5 -27q-75 0 -121 40.5t-46 105.5zM497 296h183q-2 39 -25 62t-62 23q-38 0 -64.5 -24t-31.5 -61zM203 153q0 -22 15.5 -34t42.5 -12q38 0 61 23.5t23 61.5v17h-24 q-118 -3 -118 -56z" />
<glyph unicode="&#xe7;" horiz-adv-x="512" d="M22 247q0 113 74 185.5t190 72.5q106 0 158.5 -42.5t52.5 -89.5q0 -34 -25 -55.5t-50 -26.5t-27 0q-26 65 -98 65q-45 0 -74 -31t-29 -78q0 -49 28.5 -80.5t74.5 -31.5q73 0 98 66q2 5 27 0.5t50 -26.5t25 -58q0 -45 -54.5 -88t-156.5 -43q-118 0 -191 72.5t-73 188.5z M196 -96q10 29 27 41t60 12h20q76 0 76 -52v-5q0 -29 -46 -62l-75 -59q-20 -11 -43.5 -11.5t-41 11t-11.5 28.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="563" d="M172 577q-27 15 -42.5 40.5t-15.5 52.5q0 31 25 52.5t62 21.5q26 0 53 -16q46 -27 81.5 -86.5t27.5 -74.5q-6 -10 -34 -16.5t-73 -1t-84 27.5zM22 242q0 114 74.5 189t189.5 75q114 0 186 -69.5t72 -177.5v-12q-2 -50 -39 -50h-313q18 -78 115 -78q85 0 120 63 q0 3 22 -2.5t44 -25t22 -52.5q0 -51 -57.5 -83.5t-159.5 -32.5q-127 0 -201.5 68.5t-74.5 187.5zM189 296h182q-1 39 -24.5 62t-62.5 23q-37 0 -63.5 -24t-31.5 -61z" />
<glyph unicode="&#xe9;" horiz-adv-x="563" d="M195 567q-8 15 27.5 74.5t81.5 86.5q27 16 53 16q36 0 61 -21.5t25 -52.5q0 -27 -15.5 -52.5t-41.5 -40.5q-39 -22 -84 -27.5t-73 1t-34 16.5zM22 242q0 114 74.5 189t189.5 75q114 0 186 -69.5t72 -177.5v-12q-2 -50 -39 -50h-313q18 -78 115 -78q85 0 120 63 q0 3 22 -2.5t44 -25t22 -52.5q0 -51 -57.5 -83.5t-159.5 -32.5q-127 0 -201.5 68.5t-74.5 187.5zM189 296h182q-1 39 -24.5 62t-62.5 23q-37 0 -63.5 -24t-31.5 -61z" />
<glyph unicode="&#xea;" horiz-adv-x="563" d="M181 541q-27 0 -44 16.5t-17 41.5q0 48 55.5 94.5t109.5 60.5q54 -14 109.5 -60.5t55.5 -94.5q0 -25 -17 -41.5t-44 -16.5q-30 0 -57.5 24.5t-46.5 67.5q-42 -92 -104 -92zM22 242q0 114 74.5 189t189.5 75q114 0 186 -69.5t72 -177.5v-12q-2 -50 -39 -50h-313 q18 -78 115 -78q85 0 120 63q0 3 22 -2.5t44 -25t22 -52.5q0 -51 -57.5 -83.5t-159.5 -32.5q-127 0 -201.5 68.5t-74.5 187.5zM189 296h182q-1 39 -24.5 62t-62.5 23q-37 0 -63.5 -24t-31.5 -61z" />
<glyph unicode="&#xeb;" horiz-adv-x="563" d="M400 541q-42 0 -66 23t-24 65q0 41 23.5 64.5t63.5 23.5q41 0 65 -23t24 -65q0 -41 -23.5 -64.5t-62.5 -23.5zM174 541q-42 0 -66 23t-24 65q0 41 23.5 64.5t62.5 23.5q42 0 66.5 -23t24.5 -65q0 -41 -23.5 -64.5t-63.5 -23.5zM22 242q0 114 74.5 189t189.5 75 q114 0 186 -69.5t72 -177.5v-12q-2 -50 -39 -50h-313q18 -78 115 -78q85 0 120 63q0 3 22 -2.5t44 -25t22 -52.5q0 -51 -57.5 -83.5t-159.5 -32.5q-127 0 -201.5 68.5t-74.5 187.5zM189 296h182q-1 39 -24.5 62t-62.5 23q-37 0 -63.5 -24t-31.5 -61z" />
<glyph unicode="&#xec;" horiz-adv-x="286" d="M43 577q-27 15 -42.5 40.5t-15.5 52.5q0 31 25 52.5t62 21.5q26 0 53 -16q46 -27 81.5 -86.5t27.5 -74.5q-6 -10 -34 -16.5t-73 -1t-84 27.5zM47 88v321q0 92 86 92h20q42 0 64 -21t22 -71v-321q0 -93 -84 -93h-20q-88 0 -88 93z" />
<glyph unicode="&#xed;" horiz-adv-x="286" d="M49 567q-8 15 27.5 74.5t81.5 86.5q27 16 53 16q36 0 61 -21.5t25 -52.5q0 -27 -15.5 -52.5t-41.5 -40.5q-39 -22 -84 -27.5t-73 1t-34 16.5zM47 88v321q0 92 86 92h20q42 0 64 -21t22 -71v-321q0 -93 -84 -93h-20q-88 0 -88 93z" />
<glyph unicode="&#xee;" horiz-adv-x="286" d="M38 541q-27 0 -44 16.5t-17 41.5q0 48 55.5 94.5t109.5 60.5q54 -14 109.5 -60.5t55.5 -94.5q0 -25 -17 -41.5t-44 -16.5q-30 0 -57.5 24.5t-46.5 67.5q-42 -92 -104 -92zM47 88v321q0 92 86 92h20q42 0 64 -21t22 -71v-321q0 -93 -84 -93h-20q-88 0 -88 93z" />
<glyph unicode="&#xef;" horiz-adv-x="286" d="M257 541q-42 0 -66 23t-24 65q0 41 23.5 64.5t63.5 23.5q41 0 65 -23t24 -65q0 -41 -23.5 -64.5t-62.5 -23.5zM31 541q-42 0 -66 23t-24 65q0 41 23.5 64.5t62.5 23.5q42 0 66.5 -23t24.5 -65q0 -41 -23.5 -64.5t-63.5 -23.5zM47 88v321q0 92 86 92h20q42 0 64 -21 t22 -71v-321q0 -93 -84 -93h-20q-88 0 -88 93z" />
<glyph unicode="&#xf0;" horiz-adv-x="622" d="M24 234q0 105 64.5 171.5t167.5 66.5q98 0 148 -75q-27 88 -101 149l-82 -41q-31 -15 -54.5 -8t-33.5 33l-2 5q-19 55 37 83h1q-35 12 -73 21q-1 1 -1.5 11t10 24.5t24.5 28.5t42.5 24t63.5 10q62 0 122 -26l99 49q49 24 86 -25l4 -5q16 -19 5 -43t-42 -40l-38 -18 q60 -61 94 -145.5t34 -175.5q0 -147 -82 -234.5t-216 -87.5q-126 0 -201.5 65.5t-75.5 182.5zM205 238q0 -45 27.5 -75t70.5 -30q46 0 73 28t27 75v53q-13 23 -39.5 38.5t-56.5 15.5q-46 0 -74 -29.5t-28 -75.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="606" d="M189 542q-27 0 -44.5 18.5t-17.5 44.5q0 50 30.5 80.5t72.5 30.5q72 0 133 -61q21 59 65 59q27 0 44.5 -18.5t17.5 -44.5q0 -50 -30.5 -80t-72.5 -30q-73 0 -134 61q-19 -60 -64 -60zM44 88v391q0 4 10 9t28 9t36 4q87 0 110 -86q60 91 163 91q91 0 133.5 -55.5 t42.5 -140.5v-222q0 -93 -85 -93h-19q-88 0 -88 93v189q0 34 -17 53.5t-47 19.5q-50 0 -75 -40v-222q0 -93 -85 -93h-20q-87 0 -87 93z" />
<glyph unicode="&#xf2;" horiz-adv-x="586" d="M179 577q-27 15 -42.5 40.5t-15.5 52.5q0 31 25 52.5t62 21.5q26 0 53 -16q46 -27 81.5 -86.5t27.5 -74.5q-6 -10 -34 -16.5t-73 -1t-84 27.5zM22 247q0 116 75 187.5t198 71.5q121 0 195 -71.5t74 -188.5q0 -118 -74.5 -189t-197.5 -71t-196.5 71t-73.5 190zM185 247 q0 -50 30 -82t78 -32q47 0 77.5 32t30.5 81q0 50 -30.5 82.5t-77.5 32.5t-77.5 -32t-30.5 -82z" />
<glyph unicode="&#xf3;" horiz-adv-x="586" d="M215 567q-8 15 27.5 74.5t81.5 86.5q27 16 53 16q36 0 61 -21.5t25 -52.5q0 -27 -15.5 -52.5t-41.5 -40.5q-39 -22 -84 -27.5t-73 1t-34 16.5zM22 247q0 116 75 187.5t198 71.5q121 0 195 -71.5t74 -188.5q0 -118 -74.5 -189t-197.5 -71t-196.5 71t-73.5 190zM185 247 q0 -50 30 -82t78 -32q47 0 77.5 32t30.5 81q0 50 -30.5 82.5t-77.5 32.5t-77.5 -32t-30.5 -82z" />
<glyph unicode="&#xf4;" horiz-adv-x="586" d="M189 541q-27 0 -44 16.5t-17 41.5q0 48 55.5 94.5t109.5 60.5q54 -14 109.5 -60.5t55.5 -94.5q0 -25 -17 -41.5t-44 -16.5q-30 0 -57.5 24.5t-46.5 67.5q-42 -92 -104 -92zM22 247q0 116 75 187.5t198 71.5q121 0 195 -71.5t74 -188.5q0 -118 -74.5 -189t-197.5 -71 t-196.5 71t-73.5 190zM185 247q0 -50 30 -82t78 -32q47 0 77.5 32t30.5 81q0 50 -30.5 82.5t-77.5 32.5t-77.5 -32t-30.5 -82z" />
<glyph unicode="&#xf5;" horiz-adv-x="586" d="M174 542q-27 0 -44.5 18.5t-17.5 44.5q0 50 30.5 80.5t72.5 30.5q72 0 133 -61q21 59 65 59q27 0 44.5 -18.5t17.5 -44.5q0 -50 -30.5 -80t-72.5 -30q-73 0 -134 61q-19 -60 -64 -60zM22 247q0 116 75 187.5t198 71.5q121 0 195 -71.5t74 -188.5q0 -118 -74.5 -189 t-197.5 -71t-196.5 71t-73.5 190zM185 247q0 -50 30 -82t78 -32q47 0 77.5 32t30.5 81q0 50 -30.5 82.5t-77.5 32.5t-77.5 -32t-30.5 -82z" />
<glyph unicode="&#xf6;" horiz-adv-x="586" d="M408 541q-42 0 -66 23t-24 65q0 41 23.5 64.5t63.5 23.5q41 0 65 -23t24 -65q0 -41 -23.5 -64.5t-62.5 -23.5zM182 541q-42 0 -66 23t-24 65q0 41 23.5 64.5t62.5 23.5q42 0 66.5 -23t24.5 -65q0 -41 -23.5 -64.5t-63.5 -23.5zM22 247q0 116 75 187.5t198 71.5 q121 0 195 -71.5t74 -188.5q0 -118 -74.5 -189t-197.5 -71t-196.5 71t-73.5 190zM185 247q0 -50 30 -82t78 -32q47 0 77.5 32t30.5 81q0 50 -30.5 82.5t-77.5 32.5t-77.5 -32t-30.5 -82z" />
<glyph unicode="&#xf7;" horiz-adv-x="600" d="M289 377q-67 0 -67 68v14q0 68 67 68h20q69 0 69 -68v-14q0 -68 -69 -68h-20zM119 182q-76 0 -76 67v9q0 31 19.5 49t56.5 18h361q38 0 57 -18t19 -49v-9q0 -67 -76 -67h-361zM222 50v14q0 68 67 68h20q69 0 69 -68v-14q0 -68 -69 -68h-20q-67 0 -67 68z" />
<glyph unicode="&#xf8;" horiz-adv-x="586" d="M5 -16q0 20 23 46l49 54q-53 69 -53 162q0 111 76.5 185.5t195.5 74.5q72 0 133 -32l85 94q4 4 22.5 -1t36 -20t17.5 -34q0 -22 -22 -47l-55 -62q51 -66 51 -157q0 -113 -76 -187t-196 -74q-74 0 -130 30l-80 -89q-4 -4 -22.5 1.5t-36.5 21t-18 34.5zM191 193l148 162 q-19 12 -45 12q-50 0 -82 -34t-32 -87q0 -28 11 -53zM252 138q19 -11 42 -11q49 0 81.5 34t32.5 85q0 30 -12 51l-36 -39l-72 -80z" />
<glyph unicode="&#xf9;" horiz-adv-x="606" d="M188 577q-27 15 -42.5 40.5t-15.5 52.5q0 31 25 52.5t62 21.5q26 0 53 -16q46 -27 81.5 -86.5t27.5 -74.5q-6 -10 -34 -16.5t-73 -1t-84 27.5zM39 182v227q0 92 85 92h19q42 0 65 -21t23 -71v-193q0 -34 17 -53.5t48 -19.5q49 0 74 39v227q0 92 85 92h20q87 0 87 -92 v-392q0 -3 -10 -8t-28 -9.5t-36 -4.5q-87 0 -110 83q-61 -92 -163 -92q-90 0 -133 55.5t-43 140.5z" />
<glyph unicode="&#xfa;" horiz-adv-x="606" d="M227 567q-8 15 27.5 74.5t81.5 86.5q27 16 53 16q36 0 61 -21.5t25 -52.5q0 -27 -15.5 -52.5t-41.5 -40.5q-39 -22 -84 -27.5t-73 1t-34 16.5zM39 182v227q0 92 85 92h19q42 0 65 -21t23 -71v-193q0 -34 17 -53.5t48 -19.5q49 0 74 39v227q0 92 85 92h20q87 0 87 -92 v-392q0 -3 -10 -8t-28 -9.5t-36 -4.5q-87 0 -110 83q-61 -92 -163 -92q-90 0 -133 55.5t-43 140.5z" />
<glyph unicode="&#xfb;" horiz-adv-x="606" d="M199 541q-27 0 -44 16.5t-17 41.5q0 48 55.5 94.5t109.5 60.5q54 -14 109.5 -60.5t55.5 -94.5q0 -25 -17 -41.5t-44 -16.5q-30 0 -57.5 24.5t-46.5 67.5q-42 -92 -104 -92zM39 182v227q0 92 85 92h19q42 0 65 -21t23 -71v-193q0 -34 17 -53.5t48 -19.5q49 0 74 39v227 q0 92 85 92h20q87 0 87 -92v-392q0 -3 -10 -8t-28 -9.5t-36 -4.5q-87 0 -110 83q-61 -92 -163 -92q-90 0 -133 55.5t-43 140.5z" />
<glyph unicode="&#xfc;" horiz-adv-x="606" d="M418 541q-42 0 -66 23t-24 65q0 41 23.5 64.5t63.5 23.5q41 0 65 -23t24 -65q0 -41 -23.5 -64.5t-62.5 -23.5zM192 541q-42 0 -66 23t-24 65q0 41 23.5 64.5t62.5 23.5q42 0 66.5 -23t24.5 -65q0 -41 -23.5 -64.5t-63.5 -23.5zM39 182v227q0 92 85 92h19q42 0 65 -21 t23 -71v-193q0 -34 17 -53.5t48 -19.5q49 0 74 39v227q0 92 85 92h20q87 0 87 -92v-392q0 -3 -10 -8t-28 -9.5t-36 -4.5q-87 0 -110 83q-61 -92 -163 -92q-90 0 -133 55.5t-43 140.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="570" d="M209 565q-8 15 27.5 74.5t81.5 86.5q27 16 53 16q36 0 61 -21.5t25 -52.5q0 -27 -15.5 -52.5t-41.5 -40.5q-39 -22 -84 -27.5t-73 1t-34 16.5zM173 72l-165 375q-2 5 12.5 18.5t41.5 25.5t53 12q74 0 100 -87l75 -217l70 216q26 88 98 88q25 0 51.5 -12t41 -25t12.5 -19 l-218 -550q-33 -85 -102 -85q-26 0 -52 11.5t-40 24t-11 17.5l82 171q-38 8 -49 36z" />
<glyph unicode="&#xfe;" horiz-adv-x="632" d="M44 -101v783q0 4 10 9t28 9t36 4q50 0 84 -33t34 -113v-128q58 76 145 76q102 0 164 -71t62 -190t-61.5 -189t-165.5 -70q-89 0 -144 65v-152q0 -92 -85 -92h-20q-87 0 -87 92zM236 207q5 -34 29.5 -53t61.5 -19q44 0 71 30.5t27 80.5q0 51 -26.5 81t-70.5 30 q-66 0 -92 -56v-94z" />
<glyph unicode="&#xff;" horiz-adv-x="570" d="M403 541q-42 0 -66 23t-24 65q0 41 23.5 64.5t63.5 23.5q41 0 65 -23t24 -65q0 -41 -23.5 -64.5t-62.5 -23.5zM177 541q-42 0 -66 23t-24 65q0 41 23.5 64.5t62.5 23.5q42 0 66.5 -23t24.5 -65q0 -41 -23.5 -64.5t-63.5 -23.5zM173 72l-165 375q-2 5 12.5 18.5t41.5 25.5 t53 12q74 0 100 -87l75 -217l70 216q26 88 98 88q25 0 51.5 -12t41 -25t12.5 -19l-218 -550q-33 -85 -102 -85q-26 0 -52 11.5t-40 24t-11 17.5l82 171q-38 8 -49 36z" />
<glyph unicode="&#x152;" horiz-adv-x="1030" d="M25 324q0 147 96.5 242t245.5 95q59 0 105 -14h434q93 0 93 -84v-5q0 -84 -93 -84h-239q27 -27 30 -65h127q92 0 92 -77v-3q0 -82 -92 -82h-126q-3 -44 -32 -75h245q92 0 92 -79v-5q0 -88 -92 -88h-439q-46 -14 -105 -14q-150 0 -246 95t-96 243zM223 324 q0 -72 39.5 -115.5t104.5 -43.5t104 43.5t39 115.5q0 71 -39.5 115.5t-103.5 44.5q-66 0 -105 -43.5t-39 -116.5z" />
<glyph unicode="&#x153;" horiz-adv-x="942" d="M22 247q0 116 75 187.5t198 71.5q116 0 184 -77q68 77 186 77q114 0 186 -69.5t72 -177.5v-12q-2 -50 -39 -50h-313q18 -78 115 -78q85 0 120 63q0 3 22 -2.5t44 -25t22 -52.5q0 -51 -57.5 -83.5t-159.5 -32.5q-136 0 -202 74q-68 -74 -183 -74q-123 0 -196.5 71 t-73.5 190zM568 296h182q-1 39 -24.5 62t-62.5 23q-37 0 -63.5 -24t-31.5 -61zM185 247q0 -50 30 -82t78 -32q47 0 77.5 32t30.5 81q0 50 -30.5 82.5t-77.5 32.5t-77.5 -32t-30.5 -82z" />
<glyph unicode="&#x178;" horiz-adv-x="639" d="M434 696q-42 0 -66 23t-24 65q0 41 23.5 64.5t63.5 23.5q41 0 65 -23t24 -65q0 -41 -23.5 -64.5t-62.5 -23.5zM208 696q-42 0 -66 23t-24 65q0 41 23.5 64.5t62.5 23.5q42 0 66.5 -23t24.5 -65q0 -41 -23.5 -64.5t-63.5 -23.5zM221 88v165l-225 322q-2 3 1.5 11.5 t13 19.5t24.5 20.5t39 16.5t53 7q56 0 94 -69l99 -171l104 171q41 70 95 70q26 0 48.5 -7t36.5 -17t24 -21t13.5 -19.5t1.5 -11.5l-222 -322v-165q0 -93 -87 -93h-29q-84 0 -84 93z" />
<glyph unicode="&#x2c6;" horiz-adv-x="500" d="M146 541q-27 0 -44 16.5t-17 41.5q0 48 55.5 94.5t109.5 60.5q54 -14 109.5 -60.5t55.5 -94.5q0 -25 -17 -41.5t-44 -16.5q-30 0 -57.5 24.5t-46.5 67.5q-42 -92 -104 -92z" />
<glyph unicode="&#x2dc;" horiz-adv-x="499" d="M130 542q-27 0 -44.5 18.5t-17.5 44.5q0 50 30.5 80.5t72.5 30.5q72 0 133 -61q21 59 65 59q27 0 44.5 -18.5t17.5 -44.5q0 -50 -30.5 -80t-72.5 -30q-73 0 -134 61q-19 -60 -64 -60z" />
<glyph unicode="&#x2000;" horiz-adv-x="454" />
<glyph unicode="&#x2001;" horiz-adv-x="909" />
<glyph unicode="&#x2002;" horiz-adv-x="454" />
<glyph unicode="&#x2003;" horiz-adv-x="909" />
<glyph unicode="&#x2004;" horiz-adv-x="303" />
<glyph unicode="&#x2005;" horiz-adv-x="227" />
<glyph unicode="&#x2006;" horiz-adv-x="151" />
<glyph unicode="&#x2007;" horiz-adv-x="151" />
<glyph unicode="&#x2008;" horiz-adv-x="113" />
<glyph unicode="&#x2009;" horiz-adv-x="181" />
<glyph unicode="&#x200a;" horiz-adv-x="50" />
<glyph unicode="&#x2010;" horiz-adv-x="469" d="M106 159q-36 0 -56 21t-20 51v31q0 30 20 51t56 21h257q36 0 56 -21t20 -51v-31q0 -30 -20 -51t-56 -21h-257z" />
<glyph unicode="&#x2011;" horiz-adv-x="469" d="M106 159q-36 0 -56 21t-20 51v31q0 30 20 51t56 21h257q36 0 56 -21t20 -51v-31q0 -30 -20 -51t-56 -21h-257z" />
<glyph unicode="&#x2012;" horiz-adv-x="469" d="M106 159q-36 0 -56 21t-20 51v31q0 30 20 51t56 21h257q36 0 56 -21t20 -51v-31q0 -30 -20 -51t-56 -21h-257z" />
<glyph unicode="&#x2013;" horiz-adv-x="576" d="M106 167q-36 0 -56 21t-20 51v16q0 29 20 50.5t56 21.5h357q36 0 56 -21.5t20 -50.5v-16q0 -30 -20 -51t-56 -21h-357z" />
<glyph unicode="&#x2014;" horiz-adv-x="738" d="M106 167q-36 0 -56 21t-20 51v16q0 29 20 50.5t56 21.5h526q37 0 56.5 -21t19.5 -51v-16q0 -31 -19.5 -51.5t-56.5 -20.5h-526z" />
<glyph unicode="&#x2018;" horiz-adv-x="256" d="M95 373q-74 0 -74 79v7q0 46 43 93l75 89q27 28 67 12t32 -56l-36 -145q-10 -44 -26 -61.5t-59 -17.5h-22z" />
<glyph unicode="&#x2019;" horiz-adv-x="256" d="M116 390q-26 -28 -65.5 -12t-31.5 56l35 145q9 44 26 61.5t60 17.5h21q74 0 74 -79v-7q0 -49 -43 -93z" />
<glyph unicode="&#x201a;" horiz-adv-x="256" d="M17 -53l34 146q10 44 27 61.5t60 17.5h20q75 0 75 -79v-7q0 -47 -43 -93l-76 -89q-27 -28 -66 -12t-31 55z" />
<glyph unicode="&#x201c;" horiz-adv-x="489" d="M328 373q-74 0 -74 79v7q0 49 43 93l76 89q26 28 65 12t32 -56l-35 -145q-9 -44 -26 -61.5t-60 -17.5h-21zM95 373q-74 0 -74 79v7q0 47 43 93l75 89q27 28 67 12t32 -56l-36 -145q-10 -44 -26 -61.5t-59 -17.5h-22z" />
<glyph unicode="&#x201d;" horiz-adv-x="489" d="M251 434l36 145q10 44 26 61.5t59 17.5h22q74 0 74 -79v-7q0 -45 -43 -93l-76 -89q-20 -16 -44 -16.5t-42 17t-12 43.5zM116 390q-26 -28 -65.5 -12t-31.5 56l35 145q9 44 26 61.5t60 17.5h21q74 0 74 -79v-7q0 -49 -43 -93z" />
<glyph unicode="&#x201e;" horiz-adv-x="489" d="M249 -53l36 146q10 45 25.5 62t58.5 17h23q73 0 73 -79v-7q0 -47 -43 -93l-75 -89q-20 -16 -44 -17t-42 17t-12 43zM17 -53l34 146q9 44 26 61.5t60 17.5h21q74 0 74 -79v-7q0 -49 -43 -93l-76 -89q-26 -28 -65 -12t-31 55z" />
<glyph unicode="&#x2022;" horiz-adv-x="363" d="M182 101q-63 0 -104.5 41.5t-41.5 104.5t42 106t104 43q61 0 103 -43t42 -106q0 -61 -42.5 -103.5t-102.5 -42.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="776" d="M552 73v16q0 81 81 81h23q43 0 63 -19.5t20 -61.5v-16q0 -81 -83 -81h-23q-81 0 -81 81zM293 73v16q0 81 82 81h23q83 0 83 -81v-16q0 -81 -83 -81h-23q-82 0 -82 81zM35 73v16q0 81 81 81h23q43 0 63 -19.5t20 -61.5v-16q0 -81 -83 -81h-23q-81 0 -81 81z" />
<glyph unicode="&#x202f;" horiz-adv-x="181" />
<glyph unicode="&#x2039;" horiz-adv-x="323" d="M224 55q-38 0 -87 44.5t-80 90t-31 57.5t31 57.5t80 90t87 44.5h2q34 0 52 -19t18 -50v-4q0 -31 -39 -64t-84 -55q123 -67 123 -119v-5q0 -30 -18.5 -49t-51.5 -19h-2z" />
<glyph unicode="&#x203a;" horiz-adv-x="323" d="M97 55q-33 0 -51.5 19t-18.5 49v5q0 54 122 119q-44 22 -83 55t-39 64v4q0 31 18 50t52 19h2q38 0 87 -44.5t80 -90t31 -57.5t-31 -57.5t-80 -90t-87 -44.5h-2z" />
<glyph unicode="&#x205f;" horiz-adv-x="227" />
<glyph unicode="&#x20ac;" horiz-adv-x="634" d="M78 199q-31 0 -47 14t-16 34v14q0 20 16 33.5t47 13.5h17v30h-17q-31 0 -47 13.5t-16 34.5v14q0 20 16.5 33.5t46.5 13.5h26q24 100 100.5 157t187.5 57q103 0 163.5 -37.5t60.5 -97.5q0 -29 -21.5 -53t-43 -33.5t-25.5 -5.5q-28 34 -56.5 49.5t-73.5 15.5 q-70 0 -100 -52h119q31 0 47 -13.5t16 -33.5v-14q0 -21 -16 -34.5t-47 -13.5h-135v-30h135q31 0 47 -13.5t16 -33.5v-14q0 -20 -16 -34t-47 -14h-117q30 -51 99 -51q45 0 73.5 15.5t56.5 51.5q4 4 25.5 -5.5t43 -33.5t21.5 -53q0 -54 -56 -95.5t-167 -41.5q-113 0 -189 55.5 t-100 157.5h-27z" />
<glyph unicode="&#x2122;" horiz-adv-x="700" d="M375 324q-41 0 -41 49v229q0 50 42 50h32q20 0 29 -6t16 -21l51 -121l51 121q7 16 16 21.5t27 5.5h32q43 0 43 -50v-229q0 -49 -41 -49h-17q-41 0 -41 49v125l-28 -89q-2 -8 -4 -11t-11.5 -6.5t-27.5 -3.5q-8 0 -14 0.5t-10.5 2t-7 2t-4 3.5t-2 3.5t-2 4.5t-1.5 5l-29 85 v-121q0 -49 -40 -49h-18zM148 324q-43 0 -43 49v183h-49q-48 0 -48 46v2q0 43 48 43h202q48 0 48 -43v-2q0 -46 -48 -46h-48v-183q0 -49 -47 -49h-15z" />
<glyph unicode="&#x25fc;" horiz-adv-x="505" d="M0 505h505v-505h-505v505z" />
<hkern u1="&#x21;" u2="&#x153;" k="8" />
<hkern u1="&#x21;" u2="&#xe7;" k="8" />
<hkern u1="&#x21;" u2="&#xe6;" k="8" />
<hkern u1="&#x21;" u2="q" k="8" />
<hkern u1="&#x21;" u2="o" k="8" />
<hkern u1="&#x21;" u2="e" k="8" />
<hkern u1="&#x21;" u2="d" k="8" />
<hkern u1="&#x21;" u2="c" k="8" />
<hkern u1="&#x21;" u2="a" k="8" />
<hkern u1="&#x26;" u2="&#x178;" k="38" />
<hkern u1="&#x26;" u2="&#xdd;" k="38" />
<hkern u1="&#x26;" u2="&#xc6;" k="-21" />
<hkern u1="&#x26;" u2="&#xc5;" k="-21" />
<hkern u1="&#x26;" u2="&#xc4;" k="-21" />
<hkern u1="&#x26;" u2="&#xc3;" k="-21" />
<hkern u1="&#x26;" u2="&#xc2;" k="-21" />
<hkern u1="&#x26;" u2="&#xc1;" k="-21" />
<hkern u1="&#x26;" u2="&#xc0;" k="-21" />
<hkern u1="&#x26;" u2="Y" k="38" />
<hkern u1="&#x26;" u2="X" k="-20" />
<hkern u1="&#x26;" u2="W" k="28" />
<hkern u1="&#x26;" u2="V" k="43" />
<hkern u1="&#x26;" u2="T" k="40" />
<hkern u1="&#x26;" u2="J" k="-20" />
<hkern u1="&#x26;" u2="A" k="-20" />
<hkern u1="&#x28;" u2="&#x178;" k="-50" />
<hkern u1="&#x28;" u2="&#xdd;" k="-50" />
<hkern u1="&#x28;" u2="j" k="-150" />
<hkern u1="&#x28;" u2="g" k="-20" />
<hkern u1="&#x28;" u2="Y" k="-50" />
<hkern u1="&#x28;" u2="X" k="-40" />
<hkern u1="&#x28;" u2="W" k="-10" />
<hkern u1="&#x28;" u2="V" k="-30" />
<hkern u1="&#x28;" u2="J" k="15" />
<hkern u1="&#x28;" u2="&#x37;" k="-15" />
<hkern u1="&#x2a;" u2="&#xc6;" k="75" />
<hkern u1="&#x2a;" u2="&#xc5;" k="75" />
<hkern u1="&#x2a;" u2="&#xc4;" k="75" />
<hkern u1="&#x2a;" u2="&#xc3;" k="75" />
<hkern u1="&#x2a;" u2="&#xc2;" k="75" />
<hkern u1="&#x2a;" u2="&#xc1;" k="75" />
<hkern u1="&#x2a;" u2="&#xc0;" k="75" />
<hkern u1="&#x2a;" u2="T" k="-11" />
<hkern u1="&#x2a;" u2="J" k="53" />
<hkern u1="&#x2a;" u2="A" k="75" />
<hkern u1="&#x2c;" u2="&#x178;" k="50" />
<hkern u1="&#x2c;" u2="&#x153;" k="20" />
<hkern u1="&#x2c;" u2="&#x152;" k="33" />
<hkern u1="&#x2c;" u2="&#xe7;" k="20" />
<hkern u1="&#x2c;" u2="&#xe6;" k="20" />
<hkern u1="&#x2c;" u2="&#xdd;" k="50" />
<hkern u1="&#x2c;" u2="&#xdc;" k="10" />
<hkern u1="&#x2c;" u2="&#xdb;" k="10" />
<hkern u1="&#x2c;" u2="&#xda;" k="10" />
<hkern u1="&#x2c;" u2="&#xd9;" k="10" />
<hkern u1="&#x2c;" u2="&#xd8;" k="33" />
<hkern u1="&#x2c;" u2="&#xd6;" k="33" />
<hkern u1="&#x2c;" u2="&#xd5;" k="33" />
<hkern u1="&#x2c;" u2="&#xd4;" k="33" />
<hkern u1="&#x2c;" u2="&#xd3;" k="33" />
<hkern u1="&#x2c;" u2="&#xd2;" k="33" />
<hkern u1="&#x2c;" u2="&#xc6;" k="-30" />
<hkern u1="&#x2c;" u2="&#xc5;" k="-30" />
<hkern u1="&#x2c;" u2="&#xc4;" k="-30" />
<hkern u1="&#x2c;" u2="&#xc3;" k="-30" />
<hkern u1="&#x2c;" u2="&#xc2;" k="-30" />
<hkern u1="&#x2c;" u2="&#xc1;" k="-30" />
<hkern u1="&#x2c;" u2="&#xc0;" k="-30" />
<hkern u1="&#x2c;" u2="y" k="30" />
<hkern u1="&#x2c;" u2="w" k="10" />
<hkern u1="&#x2c;" u2="v" k="30" />
<hkern u1="&#x2c;" u2="u" k="20" />
<hkern u1="&#x2c;" u2="t" k="40" />
<hkern u1="&#x2c;" u2="r" k="20" />
<hkern u1="&#x2c;" u2="q" k="20" />
<hkern u1="&#x2c;" u2="p" k="20" />
<hkern u1="&#x2c;" u2="o" k="20" />
<hkern u1="&#x2c;" u2="n" k="20" />
<hkern u1="&#x2c;" u2="m" k="20" />
<hkern u1="&#x2c;" u2="f" k="30" />
<hkern u1="&#x2c;" u2="e" k="20" />
<hkern u1="&#x2c;" u2="d" k="20" />
<hkern u1="&#x2c;" u2="c" k="20" />
<hkern u1="&#x2c;" u2="a" k="20" />
<hkern u1="&#x2c;" u2="Y" k="50" />
<hkern u1="&#x2c;" u2="W" k="28" />
<hkern u1="&#x2c;" u2="V" k="68" />
<hkern u1="&#x2c;" u2="U" k="10" />
<hkern u1="&#x2c;" u2="T" k="83" />
<hkern u1="&#x2c;" u2="Q" k="33" />
<hkern u1="&#x2c;" u2="O" k="33" />
<hkern u1="&#x2c;" u2="G" k="33" />
<hkern u1="&#x2c;" u2="C" k="33" />
<hkern u1="&#x2c;" u2="A" k="-30" />
<hkern u1="&#x2c;" u2="&#x39;" k="3" />
<hkern u1="&#x2c;" u2="&#x38;" k="15" />
<hkern u1="&#x2c;" u2="&#x37;" k="8" />
<hkern u1="&#x2c;" u2="&#x36;" k="23" />
<hkern u1="&#x2c;" u2="&#x35;" k="-15" />
<hkern u1="&#x2c;" u2="&#x34;" k="13" />
<hkern u1="&#x2c;" u2="&#x33;" k="-8" />
<hkern u1="&#x2c;" u2="&#x32;" k="-8" />
<hkern u1="&#x2c;" u2="&#x31;" k="86" />
<hkern u1="&#x2c;" u2="&#x30;" k="18" />
<hkern u1="&#x2d;" u2="&#x178;" k="50" />
<hkern u1="&#x2d;" u2="&#x153;" k="10" />
<hkern u1="&#x2d;" u2="&#xe7;" k="10" />
<hkern u1="&#x2d;" u2="&#xe6;" k="10" />
<hkern u1="&#x2d;" u2="&#xdd;" k="50" />
<hkern u1="&#x2d;" u2="&#xc6;" k="-10" />
<hkern u1="&#x2d;" u2="&#xc5;" k="-10" />
<hkern u1="&#x2d;" u2="&#xc4;" k="-10" />
<hkern u1="&#x2d;" u2="&#xc3;" k="-10" />
<hkern u1="&#x2d;" u2="&#xc2;" k="-10" />
<hkern u1="&#x2d;" u2="&#xc1;" k="-10" />
<hkern u1="&#x2d;" u2="&#xc0;" k="-10" />
<hkern u1="&#x2d;" u2="z" k="15" />
<hkern u1="&#x2d;" u2="y" k="10" />
<hkern u1="&#x2d;" u2="x" k="30" />
<hkern u1="&#x2d;" u2="v" k="10" />
<hkern u1="&#x2d;" u2="q" k="10" />
<hkern u1="&#x2d;" u2="o" k="10" />
<hkern u1="&#x2d;" u2="e" k="10" />
<hkern u1="&#x2d;" u2="d" k="10" />
<hkern u1="&#x2d;" u2="c" k="10" />
<hkern u1="&#x2d;" u2="a" k="10" />
<hkern u1="&#x2d;" u2="Z" k="10" />
<hkern u1="&#x2d;" u2="Y" k="50" />
<hkern u1="&#x2d;" u2="X" k="30" />
<hkern u1="&#x2d;" u2="W" k="40" />
<hkern u1="&#x2d;" u2="V" k="50" />
<hkern u1="&#x2d;" u2="T" k="55" />
<hkern u1="&#x2d;" u2="A" k="-10" />
<hkern u1="&#x2d;" u2="&#x37;" k="30" />
<hkern u1="&#x2d;" u2="&#x31;" k="20" />
<hkern u1="&#x2e;" u2="&#x178;" k="50" />
<hkern u1="&#x2e;" u2="&#x153;" k="20" />
<hkern u1="&#x2e;" u2="&#x152;" k="33" />
<hkern u1="&#x2e;" u2="&#xe7;" k="20" />
<hkern u1="&#x2e;" u2="&#xe6;" k="20" />
<hkern u1="&#x2e;" u2="&#xdd;" k="50" />
<hkern u1="&#x2e;" u2="&#xdc;" k="10" />
<hkern u1="&#x2e;" u2="&#xdb;" k="10" />
<hkern u1="&#x2e;" u2="&#xda;" k="10" />
<hkern u1="&#x2e;" u2="&#xd9;" k="10" />
<hkern u1="&#x2e;" u2="&#xd8;" k="33" />
<hkern u1="&#x2e;" u2="&#xd6;" k="33" />
<hkern u1="&#x2e;" u2="&#xd5;" k="33" />
<hkern u1="&#x2e;" u2="&#xd4;" k="33" />
<hkern u1="&#x2e;" u2="&#xd3;" k="33" />
<hkern u1="&#x2e;" u2="&#xd2;" k="33" />
<hkern u1="&#x2e;" u2="&#xc6;" k="-30" />
<hkern u1="&#x2e;" u2="&#xc5;" k="-30" />
<hkern u1="&#x2e;" u2="&#xc4;" k="-30" />
<hkern u1="&#x2e;" u2="&#xc3;" k="-30" />
<hkern u1="&#x2e;" u2="&#xc2;" k="-30" />
<hkern u1="&#x2e;" u2="&#xc1;" k="-30" />
<hkern u1="&#x2e;" u2="&#xc0;" k="-30" />
<hkern u1="&#x2e;" u2="y" k="30" />
<hkern u1="&#x2e;" u2="w" k="10" />
<hkern u1="&#x2e;" u2="v" k="30" />
<hkern u1="&#x2e;" u2="u" k="20" />
<hkern u1="&#x2e;" u2="t" k="40" />
<hkern u1="&#x2e;" u2="r" k="20" />
<hkern u1="&#x2e;" u2="q" k="20" />
<hkern u1="&#x2e;" u2="p" k="20" />
<hkern u1="&#x2e;" u2="o" k="20" />
<hkern u1="&#x2e;" u2="n" k="20" />
<hkern u1="&#x2e;" u2="m" k="20" />
<hkern u1="&#x2e;" u2="f" k="30" />
<hkern u1="&#x2e;" u2="e" k="20" />
<hkern u1="&#x2e;" u2="d" k="20" />
<hkern u1="&#x2e;" u2="c" k="20" />
<hkern u1="&#x2e;" u2="a" k="20" />
<hkern u1="&#x2e;" u2="Y" k="50" />
<hkern u1="&#x2e;" u2="W" k="28" />
<hkern u1="&#x2e;" u2="V" k="68" />
<hkern u1="&#x2e;" u2="U" k="10" />
<hkern u1="&#x2e;" u2="T" k="83" />
<hkern u1="&#x2e;" u2="Q" k="33" />
<hkern u1="&#x2e;" u2="O" k="33" />
<hkern u1="&#x2e;" u2="G" k="33" />
<hkern u1="&#x2e;" u2="C" k="33" />
<hkern u1="&#x2e;" u2="A" k="-30" />
<hkern u1="&#x2e;" u2="&#x39;" k="3" />
<hkern u1="&#x2e;" u2="&#x38;" k="15" />
<hkern u1="&#x2e;" u2="&#x37;" k="8" />
<hkern u1="&#x2e;" u2="&#x36;" k="23" />
<hkern u1="&#x2e;" u2="&#x35;" k="-15" />
<hkern u1="&#x2e;" u2="&#x34;" k="13" />
<hkern u1="&#x2e;" u2="&#x33;" k="-8" />
<hkern u1="&#x2e;" u2="&#x32;" k="-8" />
<hkern u1="&#x2e;" u2="&#x31;" k="86" />
<hkern u1="&#x2e;" u2="&#x30;" k="18" />
<hkern u1="&#x2f;" u2="&#x153;" k="10" />
<hkern u1="&#x2f;" u2="&#x152;" k="-3" />
<hkern u1="&#x2f;" u2="&#xe7;" k="10" />
<hkern u1="&#x2f;" u2="&#xe6;" k="10" />
<hkern u1="&#x2f;" u2="&#xde;" k="-8" />
<hkern u1="&#x2f;" u2="&#xd8;" k="-3" />
<hkern u1="&#x2f;" u2="&#xd6;" k="-3" />
<hkern u1="&#x2f;" u2="&#xd5;" k="-3" />
<hkern u1="&#x2f;" u2="&#xd4;" k="-3" />
<hkern u1="&#x2f;" u2="&#xd3;" k="-3" />
<hkern u1="&#x2f;" u2="&#xd2;" k="-3" />
<hkern u1="&#x2f;" u2="&#xd1;" k="-8" />
<hkern u1="&#x2f;" u2="&#xd0;" k="-8" />
<hkern u1="&#x2f;" u2="&#xcf;" k="-8" />
<hkern u1="&#x2f;" u2="&#xce;" k="-8" />
<hkern u1="&#x2f;" u2="&#xcd;" k="-8" />
<hkern u1="&#x2f;" u2="&#xcc;" k="-8" />
<hkern u1="&#x2f;" u2="&#xcb;" k="-8" />
<hkern u1="&#x2f;" u2="&#xca;" k="-8" />
<hkern u1="&#x2f;" u2="&#xc9;" k="-8" />
<hkern u1="&#x2f;" u2="&#xc8;" k="-8" />
<hkern u1="&#x2f;" u2="q" k="10" />
<hkern u1="&#x2f;" u2="o" k="10" />
<hkern u1="&#x2f;" u2="g" k="18" />
<hkern u1="&#x2f;" u2="e" k="10" />
<hkern u1="&#x2f;" u2="d" k="10" />
<hkern u1="&#x2f;" u2="c" k="10" />
<hkern u1="&#x2f;" u2="a" k="10" />
<hkern u1="&#x2f;" u2="R" k="-8" />
<hkern u1="&#x2f;" u2="Q" k="-3" />
<hkern u1="&#x2f;" u2="P" k="-8" />
<hkern u1="&#x2f;" u2="O" k="-3" />
<hkern u1="&#x2f;" u2="N" k="-8" />
<hkern u1="&#x2f;" u2="M" k="-8" />
<hkern u1="&#x2f;" u2="L" k="-8" />
<hkern u1="&#x2f;" u2="K" k="-8" />
<hkern u1="&#x2f;" u2="J" k="38" />
<hkern u1="&#x2f;" u2="I" k="-8" />
<hkern u1="&#x2f;" u2="H" k="-8" />
<hkern u1="&#x2f;" u2="G" k="-3" />
<hkern u1="&#x2f;" u2="F" k="-8" />
<hkern u1="&#x2f;" u2="E" k="-8" />
<hkern u1="&#x2f;" u2="D" k="-8" />
<hkern u1="&#x2f;" u2="C" k="-3" />
<hkern u1="&#x2f;" u2="B" k="-8" />
<hkern u1="&#x2f;" u2="&#x37;" k="-30" />
<hkern u1="&#x2f;" u2="&#x35;" k="-15" />
<hkern u1="&#x2f;" u2="&#x34;" k="5" />
<hkern u1="&#x2f;" u2="&#x33;" k="-15" />
<hkern u1="&#x2f;" u2="&#x32;" k="-11" />
<hkern u1="&#x2f;" u2="&#x31;" k="-20" />
<hkern u1="&#x30;" u2="&#x2026;" k="18" />
<hkern u1="&#x30;" u2="&#x201e;" k="18" />
<hkern u1="&#x30;" u2="&#x201a;" k="18" />
<hkern u1="&#x30;" u2="&#x37;" k="20" />
<hkern u1="&#x30;" u2="&#x2e;" k="18" />
<hkern u1="&#x30;" u2="&#x2c;" k="18" />
<hkern u1="&#x31;" u2="&#x2026;" k="-8" />
<hkern u1="&#x31;" u2="&#x201e;" k="-8" />
<hkern u1="&#x31;" u2="&#x201a;" k="-8" />
<hkern u1="&#x31;" u2="&#x37;" k="-8" />
<hkern u1="&#x31;" u2="&#x2e;" k="-8" />
<hkern u1="&#x31;" u2="&#x2c;" k="-8" />
<hkern u1="&#x32;" u2="&#x2014;" k="10" />
<hkern u1="&#x32;" u2="&#x2013;" k="10" />
<hkern u1="&#x32;" u2="&#x2d;" k="10" />
<hkern u1="&#x33;" u2="&#x2026;" k="5" />
<hkern u1="&#x33;" u2="&#x201e;" k="5" />
<hkern u1="&#x33;" u2="&#x201a;" k="5" />
<hkern u1="&#x33;" u2="&#x37;" k="1" />
<hkern u1="&#x33;" u2="&#x2f;" k="15" />
<hkern u1="&#x33;" u2="&#x2e;" k="5" />
<hkern u1="&#x33;" u2="&#x2c;" k="5" />
<hkern u1="&#x34;" u2="&#x2122;" k="43" />
<hkern u1="&#x34;" u2="&#x2026;" k="-15" />
<hkern u1="&#x34;" u2="&#x201e;" k="-15" />
<hkern u1="&#x34;" u2="&#x201d;" k="23" />
<hkern u1="&#x34;" u2="&#x201c;" k="30" />
<hkern u1="&#x34;" u2="&#x201a;" k="-15" />
<hkern u1="&#x34;" u2="&#x2019;" k="23" />
<hkern u1="&#x34;" u2="&#x2018;" k="30" />
<hkern u1="&#x34;" u2="&#xb0;" k="10" />
<hkern u1="&#x34;" u2="&#x37;" k="18" />
<hkern u1="&#x34;" u2="&#x31;" k="23" />
<hkern u1="&#x34;" u2="&#x2f;" k="8" />
<hkern u1="&#x34;" u2="&#x2e;" k="-15" />
<hkern u1="&#x34;" u2="&#x2c;" k="-15" />
<hkern u1="&#x35;" u2="&#x2026;" k="10" />
<hkern u1="&#x35;" u2="&#x201e;" k="10" />
<hkern u1="&#x35;" u2="&#x201a;" k="10" />
<hkern u1="&#x35;" u2="&#x37;" k="-5" />
<hkern u1="&#x35;" u2="&#x2f;" k="8" />
<hkern u1="&#x35;" u2="&#x2e;" k="10" />
<hkern u1="&#x35;" u2="&#x2c;" k="10" />
<hkern u1="&#x36;" u2="&#x2026;" k="3" />
<hkern u1="&#x36;" u2="&#x201e;" k="3" />
<hkern u1="&#x36;" u2="&#x201a;" k="3" />
<hkern u1="&#x36;" u2="&#x37;" k="10" />
<hkern u1="&#x36;" u2="&#x2e;" k="3" />
<hkern u1="&#x36;" u2="&#x2c;" k="3" />
<hkern u1="&#x37;" u2="&#x2026;" k="80" />
<hkern u1="&#x37;" u2="&#x201e;" k="80" />
<hkern u1="&#x37;" u2="&#x201a;" k="80" />
<hkern u1="&#x37;" u2="&#x2014;" k="40" />
<hkern u1="&#x37;" u2="&#x2013;" k="40" />
<hkern u1="&#x37;" u2="&#xa2;" k="40" />
<hkern u1="&#x37;" u2="&#x7d;" k="-8" />
<hkern u1="&#x37;" u2="]" k="-8" />
<hkern u1="&#x37;" u2="&#x3f;" k="-8" />
<hkern u1="&#x37;" u2="&#x3b;" k="20" />
<hkern u1="&#x37;" u2="&#x3a;" k="20" />
<hkern u1="&#x37;" u2="&#x39;" k="-10" />
<hkern u1="&#x37;" u2="&#x38;" k="4" />
<hkern u1="&#x37;" u2="&#x36;" k="4" />
<hkern u1="&#x37;" u2="&#x35;" k="-15" />
<hkern u1="&#x37;" u2="&#x34;" k="23" />
<hkern u1="&#x37;" u2="&#x33;" k="-16" />
<hkern u1="&#x37;" u2="&#x31;" k="-20" />
<hkern u1="&#x37;" u2="&#x30;" k="6" />
<hkern u1="&#x37;" u2="&#x2f;" k="68" />
<hkern u1="&#x37;" u2="&#x2e;" k="80" />
<hkern u1="&#x37;" u2="&#x2d;" k="40" />
<hkern u1="&#x37;" u2="&#x2c;" k="80" />
<hkern u1="&#x37;" u2="&#x29;" k="-8" />
<hkern u1="&#x38;" u2="&#x2026;" k="15" />
<hkern u1="&#x38;" u2="&#x201e;" k="15" />
<hkern u1="&#x38;" u2="&#x201a;" k="15" />
<hkern u1="&#x38;" u2="&#x37;" k="15" />
<hkern u1="&#x38;" u2="&#x2e;" k="15" />
<hkern u1="&#x38;" u2="&#x2c;" k="15" />
<hkern u1="&#x39;" u2="&#x2026;" k="23" />
<hkern u1="&#x39;" u2="&#x201e;" k="23" />
<hkern u1="&#x39;" u2="&#x201a;" k="23" />
<hkern u1="&#x39;" u2="&#x37;" k="19" />
<hkern u1="&#x39;" u2="&#x2e;" k="23" />
<hkern u1="&#x39;" u2="&#x2c;" k="23" />
<hkern u1="&#x3a;" u2="&#x178;" k="30" />
<hkern u1="&#x3a;" u2="&#x153;" k="10" />
<hkern u1="&#x3a;" u2="&#xe7;" k="10" />
<hkern u1="&#x3a;" u2="&#xe6;" k="10" />
<hkern u1="&#x3a;" u2="&#xdd;" k="30" />
<hkern u1="&#x3a;" u2="q" k="10" />
<hkern u1="&#x3a;" u2="o" k="10" />
<hkern u1="&#x3a;" u2="e" k="10" />
<hkern u1="&#x3a;" u2="d" k="10" />
<hkern u1="&#x3a;" u2="c" k="10" />
<hkern u1="&#x3a;" u2="a" k="10" />
<hkern u1="&#x3a;" u2="Y" k="30" />
<hkern u1="&#x3a;" u2="W" k="20" />
<hkern u1="&#x3a;" u2="V" k="20" />
<hkern u1="&#x3a;" u2="T" k="50" />
<hkern u1="&#x3a;" u2="&#x37;" k="20" />
<hkern u1="&#x3a;" u2="&#x31;" k="30" />
<hkern u1="&#x3b;" u2="&#x178;" k="30" />
<hkern u1="&#x3b;" u2="&#x153;" k="10" />
<hkern u1="&#x3b;" u2="&#xe7;" k="10" />
<hkern u1="&#x3b;" u2="&#xe6;" k="10" />
<hkern u1="&#x3b;" u2="&#xdd;" k="30" />
<hkern u1="&#x3b;" u2="q" k="10" />
<hkern u1="&#x3b;" u2="o" k="10" />
<hkern u1="&#x3b;" u2="e" k="10" />
<hkern u1="&#x3b;" u2="d" k="10" />
<hkern u1="&#x3b;" u2="c" k="10" />
<hkern u1="&#x3b;" u2="a" k="10" />
<hkern u1="&#x3b;" u2="Y" k="30" />
<hkern u1="&#x3b;" u2="W" k="20" />
<hkern u1="&#x3b;" u2="V" k="20" />
<hkern u1="&#x3b;" u2="T" k="50" />
<hkern u1="&#x3b;" u2="&#x37;" k="20" />
<hkern u1="&#x3b;" u2="&#x31;" k="30" />
<hkern u1="&#x3e;" u2="&#x37;" k="15" />
<hkern u1="&#x40;" u2="&#x178;" k="30" />
<hkern u1="&#x40;" u2="&#xdd;" k="30" />
<hkern u1="&#x40;" u2="&#xc6;" k="10" />
<hkern u1="&#x40;" u2="&#xc5;" k="10" />
<hkern u1="&#x40;" u2="&#xc4;" k="10" />
<hkern u1="&#x40;" u2="&#xc3;" k="10" />
<hkern u1="&#x40;" u2="&#xc2;" k="10" />
<hkern u1="&#x40;" u2="&#xc1;" k="10" />
<hkern u1="&#x40;" u2="&#xc0;" k="10" />
<hkern u1="&#x40;" u2="g" k="-8" />
<hkern u1="&#x40;" u2="Y" k="30" />
<hkern u1="&#x40;" u2="X" k="23" />
<hkern u1="&#x40;" u2="W" k="20" />
<hkern u1="&#x40;" u2="V" k="8" />
<hkern u1="&#x40;" u2="T" k="30" />
<hkern u1="&#x40;" u2="A" k="10" />
<hkern u1="&#x40;" u2="&#x33;" k="8" />
<hkern u1="A" u2="&#x2122;" k="53" />
<hkern u1="A" u2="&#x2026;" k="-23" />
<hkern u1="A" u2="&#x201e;" k="-23" />
<hkern u1="A" u2="&#x201d;" k="23" />
<hkern u1="A" u2="&#x201c;" k="45" />
<hkern u1="A" u2="&#x201a;" k="-23" />
<hkern u1="A" u2="&#x2019;" k="23" />
<hkern u1="A" u2="&#x2018;" k="45" />
<hkern u1="A" u2="&#x2014;" k="-8" />
<hkern u1="A" u2="&#x2013;" k="-8" />
<hkern u1="A" u2="&#x178;" k="41" />
<hkern u1="A" u2="&#x153;" k="4" />
<hkern u1="A" u2="&#x152;" k="15" />
<hkern u1="A" u2="&#xe7;" k="4" />
<hkern u1="A" u2="&#xe6;" k="4" />
<hkern u1="A" u2="&#xdd;" k="41" />
<hkern u1="A" u2="&#xd8;" k="15" />
<hkern u1="A" u2="&#xd6;" k="15" />
<hkern u1="A" u2="&#xd5;" k="15" />
<hkern u1="A" u2="&#xd4;" k="15" />
<hkern u1="A" u2="&#xd3;" k="15" />
<hkern u1="A" u2="&#xd2;" k="15" />
<hkern u1="A" u2="&#xc6;" k="-8" />
<hkern u1="A" u2="&#xc5;" k="-8" />
<hkern u1="A" u2="&#xc4;" k="-8" />
<hkern u1="A" u2="&#xc3;" k="-8" />
<hkern u1="A" u2="&#xc2;" k="-8" />
<hkern u1="A" u2="&#xc1;" k="-8" />
<hkern u1="A" u2="&#xc0;" k="-8" />
<hkern u1="A" u2="&#xae;" k="8" />
<hkern u1="A" u2="&#xa9;" k="8" />
<hkern u1="A" u2="z" k="-8" />
<hkern u1="A" u2="y" k="53" />
<hkern u1="A" u2="x" k="-8" />
<hkern u1="A" u2="w" k="38" />
<hkern u1="A" u2="v" k="53" />
<hkern u1="A" u2="u" k="4" />
<hkern u1="A" u2="t" k="4" />
<hkern u1="A" u2="s" k="-11" />
<hkern u1="A" u2="r" k="4" />
<hkern u1="A" u2="q" k="4" />
<hkern u1="A" u2="p" k="4" />
<hkern u1="A" u2="o" k="4" />
<hkern u1="A" u2="n" k="4" />
<hkern u1="A" u2="m" k="4" />
<hkern u1="A" u2="g" k="11" />
<hkern u1="A" u2="f" k="30" />
<hkern u1="A" u2="e" k="4" />
<hkern u1="A" u2="d" k="4" />
<hkern u1="A" u2="c" k="4" />
<hkern u1="A" u2="a" k="-8" />
<hkern u1="A" u2="Y" k="60" />
<hkern u1="A" u2="X" k="-8" />
<hkern u1="A" u2="W" k="70" />
<hkern u1="A" u2="V" k="80" />
<hkern u1="A" u2="T" k="41" />
<hkern u1="A" u2="S" k="-4" />
<hkern u1="A" u2="Q" k="15" />
<hkern u1="A" u2="O" k="15" />
<hkern u1="A" u2="J" k="-8" />
<hkern u1="A" u2="G" k="15" />
<hkern u1="A" u2="C" k="15" />
<hkern u1="A" u2="A" k="-8" />
<hkern u1="A" u2="&#x40;" k="8" />
<hkern u1="A" u2="&#x3f;" k="23" />
<hkern u1="A" u2="&#x2e;" k="-23" />
<hkern u1="A" u2="&#x2d;" k="-8" />
<hkern u1="A" u2="&#x2c;" k="-23" />
<hkern u1="A" u2="&#x2a;" k="60" />
<hkern u1="B" u2="&#x153;" k="-8" />
<hkern u1="B" u2="&#xe7;" k="-8" />
<hkern u1="B" u2="&#xe6;" k="-8" />
<hkern u1="B" u2="y" k="11" />
<hkern u1="B" u2="w" k="8" />
<hkern u1="B" u2="v" k="11" />
<hkern u1="B" u2="q" k="-8" />
<hkern u1="B" u2="o" k="-8" />
<hkern u1="B" u2="e" k="-8" />
<hkern u1="B" u2="d" k="-8" />
<hkern u1="B" u2="c" k="-8" />
<hkern u1="B" u2="a" k="-8" />
<hkern u1="B" u2="W" k="20" />
<hkern u1="B" u2="V" k="5" />
<hkern u1="C" u2="&#x178;" k="-20" />
<hkern u1="C" u2="&#x153;" k="-1" />
<hkern u1="C" u2="&#x152;" k="8" />
<hkern u1="C" u2="&#xe7;" k="-1" />
<hkern u1="C" u2="&#xe6;" k="-1" />
<hkern u1="C" u2="&#xdd;" k="-20" />
<hkern u1="C" u2="&#xd8;" k="8" />
<hkern u1="C" u2="&#xd6;" k="8" />
<hkern u1="C" u2="&#xd5;" k="8" />
<hkern u1="C" u2="&#xd4;" k="8" />
<hkern u1="C" u2="&#xd3;" k="8" />
<hkern u1="C" u2="&#xd2;" k="8" />
<hkern u1="C" u2="z" k="10" />
<hkern u1="C" u2="y" k="5" />
<hkern u1="C" u2="w" k="5" />
<hkern u1="C" u2="v" k="5" />
<hkern u1="C" u2="t" k="5" />
<hkern u1="C" u2="q" k="-1" />
<hkern u1="C" u2="o" k="-1" />
<hkern u1="C" u2="e" k="-1" />
<hkern u1="C" u2="d" k="-1" />
<hkern u1="C" u2="c" k="-1" />
<hkern u1="C" u2="a" k="-1" />
<hkern u1="C" u2="Y" k="-20" />
<hkern u1="C" u2="X" k="-10" />
<hkern u1="C" u2="V" k="-20" />
<hkern u1="C" u2="Q" k="8" />
<hkern u1="C" u2="O" k="8" />
<hkern u1="C" u2="G" k="8" />
<hkern u1="C" u2="C" k="8" />
<hkern u1="D" u2="&#x2026;" k="33" />
<hkern u1="D" u2="&#x201e;" k="33" />
<hkern u1="D" u2="&#x201c;" k="20" />
<hkern u1="D" u2="&#x201a;" k="33" />
<hkern u1="D" u2="&#x2018;" k="20" />
<hkern u1="D" u2="&#x178;" k="18" />
<hkern u1="D" u2="&#x153;" k="1" />
<hkern u1="D" u2="&#xe7;" k="1" />
<hkern u1="D" u2="&#xe6;" k="1" />
<hkern u1="D" u2="&#xdd;" k="18" />
<hkern u1="D" u2="&#xc6;" k="20" />
<hkern u1="D" u2="&#xc5;" k="20" />
<hkern u1="D" u2="&#xc4;" k="20" />
<hkern u1="D" u2="&#xc3;" k="20" />
<hkern u1="D" u2="&#xc2;" k="20" />
<hkern u1="D" u2="&#xc1;" k="20" />
<hkern u1="D" u2="&#xc0;" k="20" />
<hkern u1="D" u2="z" k="10" />
<hkern u1="D" u2="x" k="10" />
<hkern u1="D" u2="u" k="1" />
<hkern u1="D" u2="r" k="1" />
<hkern u1="D" u2="q" k="1" />
<hkern u1="D" u2="p" k="1" />
<hkern u1="D" u2="o" k="1" />
<hkern u1="D" u2="n" k="1" />
<hkern u1="D" u2="m" k="1" />
<hkern u1="D" u2="l" k="3" />
<hkern u1="D" u2="k" k="3" />
<hkern u1="D" u2="h" k="3" />
<hkern u1="D" u2="e" k="1" />
<hkern u1="D" u2="d" k="1" />
<hkern u1="D" u2="c" k="1" />
<hkern u1="D" u2="b" k="3" />
<hkern u1="D" u2="a" k="1" />
<hkern u1="D" u2="Z" k="19" />
<hkern u1="D" u2="Y" k="18" />
<hkern u1="D" u2="X" k="29" />
<hkern u1="D" u2="W" k="33" />
<hkern u1="D" u2="V" k="20" />
<hkern u1="D" u2="T" k="19" />
<hkern u1="D" u2="J" k="23" />
<hkern u1="D" u2="A" k="20" />
<hkern u1="D" u2="&#x3f;" k="20" />
<hkern u1="D" u2="&#x2f;" k="50" />
<hkern u1="D" u2="&#x2e;" k="33" />
<hkern u1="D" u2="&#x2c;" k="33" />
<hkern u1="E" u2="&#x2039;" k="15" />
<hkern u1="E" u2="&#x153;" k="21" />
<hkern u1="E" u2="&#x152;" k="19" />
<hkern u1="E" u2="&#xfe;" k="4" />
<hkern u1="E" u2="&#xe7;" k="21" />
<hkern u1="E" u2="&#xe6;" k="21" />
<hkern u1="E" u2="&#xd8;" k="19" />
<hkern u1="E" u2="&#xd6;" k="19" />
<hkern u1="E" u2="&#xd5;" k="19" />
<hkern u1="E" u2="&#xd4;" k="19" />
<hkern u1="E" u2="&#xd3;" k="19" />
<hkern u1="E" u2="&#xd2;" k="19" />
<hkern u1="E" u2="&#xae;" k="20" />
<hkern u1="E" u2="&#xab;" k="15" />
<hkern u1="E" u2="&#xa9;" k="20" />
<hkern u1="E" u2="y" k="20" />
<hkern u1="E" u2="v" k="20" />
<hkern u1="E" u2="u" k="8" />
<hkern u1="E" u2="r" k="8" />
<hkern u1="E" u2="q" k="21" />
<hkern u1="E" u2="p" k="8" />
<hkern u1="E" u2="o" k="21" />
<hkern u1="E" u2="n" k="8" />
<hkern u1="E" u2="m" k="8" />
<hkern u1="E" u2="l" k="4" />
<hkern u1="E" u2="k" k="4" />
<hkern u1="E" u2="h" k="4" />
<hkern u1="E" u2="g" k="16" />
<hkern u1="E" u2="f" k="10" />
<hkern u1="E" u2="e" k="21" />
<hkern u1="E" u2="d" k="21" />
<hkern u1="E" u2="c" k="21" />
<hkern u1="E" u2="b" k="4" />
<hkern u1="E" u2="a" k="6" />
<hkern u1="E" u2="W" k="4" />
<hkern u1="E" u2="V" k="-8" />
<hkern u1="E" u2="T" k="-8" />
<hkern u1="E" u2="Q" k="19" />
<hkern u1="E" u2="O" k="19" />
<hkern u1="E" u2="J" k="-4" />
<hkern u1="E" u2="G" k="19" />
<hkern u1="E" u2="C" k="19" />
<hkern u1="E" u2="&#x40;" k="20" />
<hkern u1="F" u2="&#x2122;" k="-15" />
<hkern u1="F" u2="&#x2039;" k="26" />
<hkern u1="F" u2="&#x2026;" k="90" />
<hkern u1="F" u2="&#x201e;" k="90" />
<hkern u1="F" u2="&#x201a;" k="90" />
<hkern u1="F" u2="&#x2014;" k="35" />
<hkern u1="F" u2="&#x2013;" k="35" />
<hkern u1="F" u2="&#x178;" k="-25" />
<hkern u1="F" u2="&#x153;" k="50" />
<hkern u1="F" u2="&#x152;" k="15" />
<hkern u1="F" u2="&#xfe;" k="8" />
<hkern u1="F" u2="&#xe7;" k="50" />
<hkern u1="F" u2="&#xe6;" k="50" />
<hkern u1="F" u2="&#xdd;" k="-25" />
<hkern u1="F" u2="&#xd8;" k="15" />
<hkern u1="F" u2="&#xd6;" k="15" />
<hkern u1="F" u2="&#xd5;" k="15" />
<hkern u1="F" u2="&#xd4;" k="15" />
<hkern u1="F" u2="&#xd3;" k="15" />
<hkern u1="F" u2="&#xd2;" k="15" />
<hkern u1="F" u2="&#xc6;" k="50" />
<hkern u1="F" u2="&#xc5;" k="50" />
<hkern u1="F" u2="&#xc4;" k="50" />
<hkern u1="F" u2="&#xc3;" k="50" />
<hkern u1="F" u2="&#xc2;" k="50" />
<hkern u1="F" u2="&#xc1;" k="50" />
<hkern u1="F" u2="&#xc0;" k="50" />
<hkern u1="F" u2="&#xae;" k="20" />
<hkern u1="F" u2="&#xab;" k="26" />
<hkern u1="F" u2="&#xa9;" k="20" />
<hkern u1="F" u2="z" k="40" />
<hkern u1="F" u2="y" k="20" />
<hkern u1="F" u2="x" k="40" />
<hkern u1="F" u2="w" k="20" />
<hkern u1="F" u2="v" k="20" />
<hkern u1="F" u2="u" k="40" />
<hkern u1="F" u2="t" k="20" />
<hkern u1="F" u2="s" k="40" />
<hkern u1="F" u2="r" k="40" />
<hkern u1="F" u2="q" k="50" />
<hkern u1="F" u2="p" k="40" />
<hkern u1="F" u2="o" k="50" />
<hkern u1="F" u2="n" k="40" />
<hkern u1="F" u2="m" k="40" />
<hkern u1="F" u2="l" k="10" />
<hkern u1="F" u2="k" k="10" />
<hkern u1="F" u2="j" k="10" />
<hkern u1="F" u2="i" k="10" />
<hkern u1="F" u2="h" k="10" />
<hkern u1="F" u2="g" k="30" />
<hkern u1="F" u2="f" k="20" />
<hkern u1="F" u2="e" k="50" />
<hkern u1="F" u2="d" k="50" />
<hkern u1="F" u2="c" k="50" />
<hkern u1="F" u2="b" k="10" />
<hkern u1="F" u2="a" k="50" />
<hkern u1="F" u2="Y" k="-25" />
<hkern u1="F" u2="X" k="-10" />
<hkern u1="F" u2="W" k="-5" />
<hkern u1="F" u2="V" k="-20" />
<hkern u1="F" u2="T" k="-15" />
<hkern u1="F" u2="Q" k="15" />
<hkern u1="F" u2="O" k="15" />
<hkern u1="F" u2="J" k="90" />
<hkern u1="F" u2="G" k="15" />
<hkern u1="F" u2="C" k="15" />
<hkern u1="F" u2="A" k="50" />
<hkern u1="F" u2="&#x40;" k="20" />
<hkern u1="F" u2="&#x3b;" k="30" />
<hkern u1="F" u2="&#x3a;" k="30" />
<hkern u1="F" u2="&#x2f;" k="80" />
<hkern u1="F" u2="&#x2e;" k="90" />
<hkern u1="F" u2="&#x2d;" k="35" />
<hkern u1="F" u2="&#x2c;" k="90" />
<hkern u1="F" u2="&#x26;" k="33" />
<hkern u1="G" u2="&#x2122;" k="30" />
<hkern u1="G" u2="&#x2026;" k="-20" />
<hkern u1="G" u2="&#x201e;" k="-20" />
<hkern u1="G" u2="&#x201a;" k="-20" />
<hkern u1="G" u2="&#x178;" k="23" />
<hkern u1="G" u2="&#xdd;" k="23" />
<hkern u1="G" u2="y" k="18" />
<hkern u1="G" u2="w" k="11" />
<hkern u1="G" u2="v" k="18" />
<hkern u1="G" u2="t" k="10" />
<hkern u1="G" u2="Y" k="23" />
<hkern u1="G" u2="W" k="4" />
<hkern u1="G" u2="V" k="25" />
<hkern u1="G" u2="T" k="25" />
<hkern u1="G" u2="&#x2e;" k="-20" />
<hkern u1="G" u2="&#x2c;" k="-20" />
<hkern u1="H" u2="y" k="10" />
<hkern u1="H" u2="v" k="10" />
<hkern u1="H" u2="&#x2f;" k="20" />
<hkern u1="I" u2="y" k="10" />
<hkern u1="I" u2="v" k="10" />
<hkern u1="I" u2="&#x2f;" k="20" />
<hkern u1="J" u2="&#x2026;" k="10" />
<hkern u1="J" u2="&#x201e;" k="10" />
<hkern u1="J" u2="&#x201a;" k="10" />
<hkern u1="J" u2="&#xc6;" k="15" />
<hkern u1="J" u2="&#xc5;" k="15" />
<hkern u1="J" u2="&#xc4;" k="15" />
<hkern u1="J" u2="&#xc3;" k="15" />
<hkern u1="J" u2="&#xc2;" k="15" />
<hkern u1="J" u2="&#xc1;" k="15" />
<hkern u1="J" u2="&#xc0;" k="15" />
<hkern u1="J" u2="J" k="10" />
<hkern u1="J" u2="A" k="16" />
<hkern u1="J" u2="&#x2e;" k="10" />
<hkern u1="J" u2="&#x2c;" k="10" />
<hkern u1="K" u2="&#x2014;" k="33" />
<hkern u1="K" u2="&#x2013;" k="33" />
<hkern u1="K" u2="&#x178;" k="-4" />
<hkern u1="K" u2="&#x153;" k="20" />
<hkern u1="K" u2="&#x152;" k="8" />
<hkern u1="K" u2="&#xf0;" k="20" />
<hkern u1="K" u2="&#xe7;" k="20" />
<hkern u1="K" u2="&#xe6;" k="20" />
<hkern u1="K" u2="&#xdd;" k="-4" />
<hkern u1="K" u2="&#xdc;" k="10" />
<hkern u1="K" u2="&#xdb;" k="10" />
<hkern u1="K" u2="&#xda;" k="10" />
<hkern u1="K" u2="&#xd9;" k="10" />
<hkern u1="K" u2="&#xd8;" k="8" />
<hkern u1="K" u2="&#xd6;" k="8" />
<hkern u1="K" u2="&#xd5;" k="8" />
<hkern u1="K" u2="&#xd4;" k="8" />
<hkern u1="K" u2="&#xd3;" k="8" />
<hkern u1="K" u2="&#xd2;" k="8" />
<hkern u1="K" u2="&#xae;" k="30" />
<hkern u1="K" u2="&#xa9;" k="30" />
<hkern u1="K" u2="y" k="31" />
<hkern u1="K" u2="x" k="8" />
<hkern u1="K" u2="w" k="19" />
<hkern u1="K" u2="v" k="31" />
<hkern u1="K" u2="t" k="10" />
<hkern u1="K" u2="q" k="20" />
<hkern u1="K" u2="o" k="20" />
<hkern u1="K" u2="g" k="10" />
<hkern u1="K" u2="f" k="5" />
<hkern u1="K" u2="e" k="20" />
<hkern u1="K" u2="d" k="20" />
<hkern u1="K" u2="c" k="20" />
<hkern u1="K" u2="a" k="15" />
<hkern u1="K" u2="Z" k="5" />
<hkern u1="K" u2="Y" k="-4" />
<hkern u1="K" u2="X" k="-6" />
<hkern u1="K" u2="W" k="21" />
<hkern u1="K" u2="V" k="4" />
<hkern u1="K" u2="U" k="10" />
<hkern u1="K" u2="T" k="-5" />
<hkern u1="K" u2="S" k="5" />
<hkern u1="K" u2="Q" k="8" />
<hkern u1="K" u2="O" k="8" />
<hkern u1="K" u2="G" k="8" />
<hkern u1="K" u2="C" k="8" />
<hkern u1="K" u2="&#x40;" k="30" />
<hkern u1="K" u2="&#x2d;" k="33" />
<hkern u1="K" u2="&#x26;" k="20" />
<hkern u1="L" u2="&#x2122;" k="70" />
<hkern u1="L" u2="&#x2039;" k="15" />
<hkern u1="L" u2="&#x201d;" k="68" />
<hkern u1="L" u2="&#x201c;" k="100" />
<hkern u1="L" u2="&#x2019;" k="68" />
<hkern u1="L" u2="&#x2018;" k="100" />
<hkern u1="L" u2="&#x178;" k="91" />
<hkern u1="L" u2="&#x153;" k="13" />
<hkern u1="L" u2="&#x152;" k="31" />
<hkern u1="L" u2="&#xe7;" k="13" />
<hkern u1="L" u2="&#xe6;" k="1" />
<hkern u1="L" u2="&#xdd;" k="91" />
<hkern u1="L" u2="&#xdc;" k="15" />
<hkern u1="L" u2="&#xdb;" k="15" />
<hkern u1="L" u2="&#xda;" k="15" />
<hkern u1="L" u2="&#xd9;" k="15" />
<hkern u1="L" u2="&#xd8;" k="31" />
<hkern u1="L" u2="&#xd6;" k="31" />
<hkern u1="L" u2="&#xd5;" k="31" />
<hkern u1="L" u2="&#xd4;" k="31" />
<hkern u1="L" u2="&#xd3;" k="31" />
<hkern u1="L" u2="&#xd2;" k="31" />
<hkern u1="L" u2="&#xc6;" k="-20" />
<hkern u1="L" u2="&#xc5;" k="-20" />
<hkern u1="L" u2="&#xc4;" k="-20" />
<hkern u1="L" u2="&#xc3;" k="-20" />
<hkern u1="L" u2="&#xc2;" k="-20" />
<hkern u1="L" u2="&#xc1;" k="-20" />
<hkern u1="L" u2="&#xc0;" k="-20" />
<hkern u1="L" u2="&#xae;" k="50" />
<hkern u1="L" u2="&#xab;" k="15" />
<hkern u1="L" u2="&#xa9;" k="50" />
<hkern u1="L" u2="y" k="55" />
<hkern u1="L" u2="w" k="25" />
<hkern u1="L" u2="v" k="55" />
<hkern u1="L" u2="q" k="13" />
<hkern u1="L" u2="o" k="13" />
<hkern u1="L" u2="e" k="13" />
<hkern u1="L" u2="d" k="13" />
<hkern u1="L" u2="c" k="13" />
<hkern u1="L" u2="a" k="1" />
<hkern u1="L" u2="\" k="50" />
<hkern u1="L" u2="Z" k="-4" />
<hkern u1="L" u2="Y" k="91" />
<hkern u1="L" u2="W" k="70" />
<hkern u1="L" u2="V" k="90" />
<hkern u1="L" u2="U" k="15" />
<hkern u1="L" u2="T" k="96" />
<hkern u1="L" u2="Q" k="31" />
<hkern u1="L" u2="O" k="31" />
<hkern u1="L" u2="J" k="-11" />
<hkern u1="L" u2="G" k="31" />
<hkern u1="L" u2="C" k="31" />
<hkern u1="L" u2="A" k="-20" />
<hkern u1="L" u2="&#x40;" k="50" />
<hkern u1="L" u2="&#x3f;" k="53" />
<hkern u1="L" u2="&#x2a;" k="45" />
<hkern u1="M" u2="y" k="10" />
<hkern u1="M" u2="v" k="10" />
<hkern u1="M" u2="&#x2f;" k="20" />
<hkern u1="N" u2="y" k="10" />
<hkern u1="N" u2="v" k="10" />
<hkern u1="N" u2="&#x2f;" k="20" />
<hkern u1="O" u2="&#x2026;" k="33" />
<hkern u1="O" u2="&#x201e;" k="33" />
<hkern u1="O" u2="&#x201c;" k="20" />
<hkern u1="O" u2="&#x201a;" k="33" />
<hkern u1="O" u2="&#x2018;" k="20" />
<hkern u1="O" u2="&#x178;" k="18" />
<hkern u1="O" u2="&#x153;" k="1" />
<hkern u1="O" u2="&#xe7;" k="1" />
<hkern u1="O" u2="&#xe6;" k="1" />
<hkern u1="O" u2="&#xdd;" k="18" />
<hkern u1="O" u2="&#xc6;" k="20" />
<hkern u1="O" u2="&#xc5;" k="20" />
<hkern u1="O" u2="&#xc4;" k="20" />
<hkern u1="O" u2="&#xc3;" k="20" />
<hkern u1="O" u2="&#xc2;" k="20" />
<hkern u1="O" u2="&#xc1;" k="20" />
<hkern u1="O" u2="&#xc0;" k="20" />
<hkern u1="O" u2="z" k="10" />
<hkern u1="O" u2="x" k="10" />
<hkern u1="O" u2="u" k="1" />
<hkern u1="O" u2="r" k="1" />
<hkern u1="O" u2="q" k="1" />
<hkern u1="O" u2="p" k="1" />
<hkern u1="O" u2="o" k="1" />
<hkern u1="O" u2="n" k="1" />
<hkern u1="O" u2="m" k="1" />
<hkern u1="O" u2="l" k="3" />
<hkern u1="O" u2="k" k="3" />
<hkern u1="O" u2="h" k="3" />
<hkern u1="O" u2="e" k="1" />
<hkern u1="O" u2="d" k="1" />
<hkern u1="O" u2="c" k="1" />
<hkern u1="O" u2="b" k="3" />
<hkern u1="O" u2="a" k="1" />
<hkern u1="O" u2="Z" k="19" />
<hkern u1="O" u2="Y" k="18" />
<hkern u1="O" u2="X" k="29" />
<hkern u1="O" u2="W" k="33" />
<hkern u1="O" u2="V" k="20" />
<hkern u1="O" u2="T" k="19" />
<hkern u1="O" u2="J" k="23" />
<hkern u1="O" u2="A" k="20" />
<hkern u1="O" u2="&#x3f;" k="20" />
<hkern u1="O" u2="&#x2f;" k="50" />
<hkern u1="O" u2="&#x2e;" k="33" />
<hkern u1="O" u2="&#x2c;" k="33" />
<hkern u1="P" u2="&#x2026;" k="60" />
<hkern u1="P" u2="&#x201e;" k="60" />
<hkern u1="P" u2="&#x201a;" k="60" />
<hkern u1="P" u2="&#x178;" k="-8" />
<hkern u1="P" u2="&#x153;" k="10" />
<hkern u1="P" u2="&#xe7;" k="10" />
<hkern u1="P" u2="&#xe6;" k="10" />
<hkern u1="P" u2="&#xdd;" k="-8" />
<hkern u1="P" u2="&#xc6;" k="39" />
<hkern u1="P" u2="&#xc5;" k="39" />
<hkern u1="P" u2="&#xc4;" k="39" />
<hkern u1="P" u2="&#xc3;" k="39" />
<hkern u1="P" u2="&#xc2;" k="39" />
<hkern u1="P" u2="&#xc1;" k="39" />
<hkern u1="P" u2="&#xc0;" k="39" />
<hkern u1="P" u2="y" k="-15" />
<hkern u1="P" u2="x" k="-5" />
<hkern u1="P" u2="w" k="-10" />
<hkern u1="P" u2="v" k="-15" />
<hkern u1="P" u2="t" k="-3" />
<hkern u1="P" u2="q" k="10" />
<hkern u1="P" u2="o" k="10" />
<hkern u1="P" u2="f" k="-3" />
<hkern u1="P" u2="e" k="10" />
<hkern u1="P" u2="d" k="10" />
<hkern u1="P" u2="c" k="10" />
<hkern u1="P" u2="a" k="10" />
<hkern u1="P" u2="Z" k="25" />
<hkern u1="P" u2="Y" k="-8" />
<hkern u1="P" u2="X" k="6" />
<hkern u1="P" u2="W" k="6" />
<hkern u1="P" u2="V" k="-3" />
<hkern u1="P" u2="T" k="-8" />
<hkern u1="P" u2="J" k="59" />
<hkern u1="P" u2="A" k="39" />
<hkern u1="P" u2="&#x2e;" k="60" />
<hkern u1="P" u2="&#x2c;" k="60" />
<hkern u1="P" u2="&#x2a;" k="-8" />
<hkern u1="P" u2="&#x26;" k="13" />
<hkern u1="Q" u2="&#x2026;" k="33" />
<hkern u1="Q" u2="&#x201e;" k="-13" />
<hkern u1="Q" u2="&#x201c;" k="20" />
<hkern u1="Q" u2="&#x201a;" k="-13" />
<hkern u1="Q" u2="&#x2018;" k="20" />
<hkern u1="Q" u2="&#x178;" k="18" />
<hkern u1="Q" u2="&#x153;" k="1" />
<hkern u1="Q" u2="&#xe7;" k="1" />
<hkern u1="Q" u2="&#xe6;" k="1" />
<hkern u1="Q" u2="&#xdd;" k="18" />
<hkern u1="Q" u2="&#xc6;" k="20" />
<hkern u1="Q" u2="&#xc5;" k="20" />
<hkern u1="Q" u2="&#xc4;" k="20" />
<hkern u1="Q" u2="&#xc3;" k="20" />
<hkern u1="Q" u2="&#xc2;" k="20" />
<hkern u1="Q" u2="&#xc1;" k="20" />
<hkern u1="Q" u2="&#xc0;" k="20" />
<hkern u1="Q" u2="z" k="10" />
<hkern u1="Q" u2="x" k="10" />
<hkern u1="Q" u2="u" k="1" />
<hkern u1="Q" u2="r" k="1" />
<hkern u1="Q" u2="q" k="1" />
<hkern u1="Q" u2="p" k="1" />
<hkern u1="Q" u2="o" k="1" />
<hkern u1="Q" u2="n" k="1" />
<hkern u1="Q" u2="m" k="1" />
<hkern u1="Q" u2="l" k="3" />
<hkern u1="Q" u2="k" k="3" />
<hkern u1="Q" u2="h" k="3" />
<hkern u1="Q" u2="e" k="1" />
<hkern u1="Q" u2="d" k="1" />
<hkern u1="Q" u2="c" k="1" />
<hkern u1="Q" u2="b" k="3" />
<hkern u1="Q" u2="a" k="1" />
<hkern u1="Q" u2="Z" k="19" />
<hkern u1="Q" u2="Y" k="18" />
<hkern u1="Q" u2="X" k="-9" />
<hkern u1="Q" u2="W" k="33" />
<hkern u1="Q" u2="V" k="20" />
<hkern u1="Q" u2="T" k="19" />
<hkern u1="Q" u2="J" k="4" />
<hkern u1="Q" u2="A" k="20" />
<hkern u1="Q" u2="&#x3f;" k="20" />
<hkern u1="Q" u2="&#x2f;" k="30" />
<hkern u1="Q" u2="&#x2e;" k="-15" />
<hkern u1="Q" u2="&#x2c;" k="-13" />
<hkern u1="R" u2="&#x178;" k="-8" />
<hkern u1="R" u2="&#x152;" k="4" />
<hkern u1="R" u2="&#xdd;" k="-8" />
<hkern u1="R" u2="&#xd8;" k="4" />
<hkern u1="R" u2="&#xd6;" k="4" />
<hkern u1="R" u2="&#xd5;" k="4" />
<hkern u1="R" u2="&#xd4;" k="4" />
<hkern u1="R" u2="&#xd3;" k="4" />
<hkern u1="R" u2="&#xd2;" k="4" />
<hkern u1="R" u2="Y" k="-8" />
<hkern u1="R" u2="X" k="-10" />
<hkern u1="R" u2="W" k="11" />
<hkern u1="R" u2="V" k="-1" />
<hkern u1="R" u2="T" k="3" />
<hkern u1="R" u2="Q" k="4" />
<hkern u1="R" u2="O" k="4" />
<hkern u1="R" u2="J" k="6" />
<hkern u1="R" u2="G" k="4" />
<hkern u1="R" u2="C" k="4" />
<hkern u1="R" u2="&#x26;" k="-10" />
<hkern u1="S" u2="&#x178;" k="-10" />
<hkern u1="S" u2="&#xdd;" k="-10" />
<hkern u1="S" u2="&#xc6;" k="-5" />
<hkern u1="S" u2="&#xc5;" k="-5" />
<hkern u1="S" u2="&#xc4;" k="-5" />
<hkern u1="S" u2="&#xc3;" k="-5" />
<hkern u1="S" u2="&#xc2;" k="-5" />
<hkern u1="S" u2="&#xc1;" k="-5" />
<hkern u1="S" u2="&#xc0;" k="-5" />
<hkern u1="S" u2="Y" k="-10" />
<hkern u1="S" u2="X" k="-5" />
<hkern u1="S" u2="W" k="8" />
<hkern u1="S" u2="V" k="-6" />
<hkern u1="S" u2="A" k="-5" />
<hkern u1="T" u2="&#x203a;" k="40" />
<hkern u1="T" u2="&#x2039;" k="110" />
<hkern u1="T" u2="&#x2026;" k="83" />
<hkern u1="T" u2="&#x201e;" k="83" />
<hkern u1="T" u2="&#x201a;" k="83" />
<hkern u1="T" u2="&#x2014;" k="55" />
<hkern u1="T" u2="&#x2013;" k="55" />
<hkern u1="T" u2="&#x178;" k="-28" />
<hkern u1="T" u2="&#x153;" k="76" />
<hkern u1="T" u2="&#x152;" k="19" />
<hkern u1="T" u2="&#xe7;" k="76" />
<hkern u1="T" u2="&#xe6;" k="58" />
<hkern u1="T" u2="&#xdd;" k="-28" />
<hkern u1="T" u2="&#xd8;" k="19" />
<hkern u1="T" u2="&#xd6;" k="19" />
<hkern u1="T" u2="&#xd5;" k="19" />
<hkern u1="T" u2="&#xd4;" k="19" />
<hkern u1="T" u2="&#xd3;" k="19" />
<hkern u1="T" u2="&#xd2;" k="19" />
<hkern u1="T" u2="&#xc6;" k="59" />
<hkern u1="T" u2="&#xc5;" k="59" />
<hkern u1="T" u2="&#xc4;" k="59" />
<hkern u1="T" u2="&#xc3;" k="59" />
<hkern u1="T" u2="&#xc2;" k="59" />
<hkern u1="T" u2="&#xc1;" k="59" />
<hkern u1="T" u2="&#xc0;" k="59" />
<hkern u1="T" u2="&#xbf;" k="50" />
<hkern u1="T" u2="&#xbb;" k="40" />
<hkern u1="T" u2="&#xae;" k="30" />
<hkern u1="T" u2="&#xab;" k="110" />
<hkern u1="T" u2="&#xa9;" k="30" />
<hkern u1="T" u2="z" k="35" />
<hkern u1="T" u2="y" k="25" />
<hkern u1="T" u2="x" k="35" />
<hkern u1="T" u2="w" k="23" />
<hkern u1="T" u2="v" k="25" />
<hkern u1="T" u2="u" k="35" />
<hkern u1="T" u2="t" k="20" />
<hkern u1="T" u2="s" k="63" />
<hkern u1="T" u2="r" k="35" />
<hkern u1="T" u2="q" k="76" />
<hkern u1="T" u2="p" k="35" />
<hkern u1="T" u2="o" k="76" />
<hkern u1="T" u2="n" k="35" />
<hkern u1="T" u2="m" k="35" />
<hkern u1="T" u2="g" k="95" />
<hkern u1="T" u2="f" k="10" />
<hkern u1="T" u2="e" k="76" />
<hkern u1="T" u2="d" k="76" />
<hkern u1="T" u2="c" k="76" />
<hkern u1="T" u2="a" k="58" />
<hkern u1="T" u2="\" k="-10" />
<hkern u1="T" u2="Z" k="6" />
<hkern u1="T" u2="Y" k="-28" />
<hkern u1="T" u2="X" k="-10" />
<hkern u1="T" u2="V" k="-20" />
<hkern u1="T" u2="T" k="-10" />
<hkern u1="T" u2="Q" k="19" />
<hkern u1="T" u2="O" k="19" />
<hkern u1="T" u2="J" k="96" />
<hkern u1="T" u2="G" k="19" />
<hkern u1="T" u2="C" k="19" />
<hkern u1="T" u2="A" k="58" />
<hkern u1="T" u2="&#x40;" k="30" />
<hkern u1="T" u2="&#x3b;" k="50" />
<hkern u1="T" u2="&#x3a;" k="50" />
<hkern u1="T" u2="&#x2f;" k="38" />
<hkern u1="T" u2="&#x2e;" k="83" />
<hkern u1="T" u2="&#x2d;" k="55" />
<hkern u1="T" u2="&#x2c;" k="83" />
<hkern u1="T" u2="&#x2a;" k="-11" />
<hkern u1="T" u2="&#x26;" k="40" />
<hkern u1="U" u2="&#x2026;" k="10" />
<hkern u1="U" u2="&#x201e;" k="10" />
<hkern u1="U" u2="&#x201a;" k="10" />
<hkern u1="U" u2="&#xc6;" k="15" />
<hkern u1="U" u2="&#xc5;" k="15" />
<hkern u1="U" u2="&#xc4;" k="15" />
<hkern u1="U" u2="&#xc3;" k="15" />
<hkern u1="U" u2="&#xc2;" k="15" />
<hkern u1="U" u2="&#xc1;" k="15" />
<hkern u1="U" u2="&#xc0;" k="15" />
<hkern u1="U" u2="J" k="10" />
<hkern u1="U" u2="A" k="16" />
<hkern u1="U" u2="&#x2e;" k="10" />
<hkern u1="U" u2="&#x2c;" k="10" />
<hkern u1="V" u2="&#x203a;" k="20" />
<hkern u1="V" u2="&#x2039;" k="50" />
<hkern u1="V" u2="&#x2026;" k="68" />
<hkern u1="V" u2="&#x201e;" k="68" />
<hkern u1="V" u2="&#x201a;" k="68" />
<hkern u1="V" u2="&#x2014;" k="60" />
<hkern u1="V" u2="&#x2013;" k="60" />
<hkern u1="V" u2="&#x178;" k="-23" />
<hkern u1="V" u2="&#x153;" k="55" />
<hkern u1="V" u2="&#x152;" k="20" />
<hkern u1="V" u2="&#xfe;" k="8" />
<hkern u1="V" u2="&#xe7;" k="55" />
<hkern u1="V" u2="&#xe6;" k="48" />
<hkern u1="V" u2="&#xdd;" k="-23" />
<hkern u1="V" u2="&#xd8;" k="20" />
<hkern u1="V" u2="&#xd6;" k="20" />
<hkern u1="V" u2="&#xd5;" k="20" />
<hkern u1="V" u2="&#xd4;" k="20" />
<hkern u1="V" u2="&#xd3;" k="20" />
<hkern u1="V" u2="&#xd2;" k="20" />
<hkern u1="V" u2="&#xc6;" k="73" />
<hkern u1="V" u2="&#xc5;" k="73" />
<hkern u1="V" u2="&#xc4;" k="73" />
<hkern u1="V" u2="&#xc3;" k="73" />
<hkern u1="V" u2="&#xc2;" k="73" />
<hkern u1="V" u2="&#xc1;" k="73" />
<hkern u1="V" u2="&#xc0;" k="73" />
<hkern u1="V" u2="&#xbb;" k="20" />
<hkern u1="V" u2="&#xae;" k="8" />
<hkern u1="V" u2="&#xab;" k="50" />
<hkern u1="V" u2="&#xa9;" k="8" />
<hkern u1="V" u2="&#x7d;" k="-30" />
<hkern u1="V" u2="y" k="10" />
<hkern u1="V" u2="x" k="25" />
<hkern u1="V" u2="w" k="10" />
<hkern u1="V" u2="v" k="10" />
<hkern u1="V" u2="u" k="30" />
<hkern u1="V" u2="t" k="10" />
<hkern u1="V" u2="s" k="40" />
<hkern u1="V" u2="r" k="30" />
<hkern u1="V" u2="q" k="55" />
<hkern u1="V" u2="p" k="30" />
<hkern u1="V" u2="o" k="55" />
<hkern u1="V" u2="n" k="30" />
<hkern u1="V" u2="m" k="30" />
<hkern u1="V" u2="l" k="10" />
<hkern u1="V" u2="k" k="10" />
<hkern u1="V" u2="h" k="10" />
<hkern u1="V" u2="g" k="35" />
<hkern u1="V" u2="f" k="10" />
<hkern u1="V" u2="e" k="55" />
<hkern u1="V" u2="d" k="55" />
<hkern u1="V" u2="c" k="55" />
<hkern u1="V" u2="b" k="10" />
<hkern u1="V" u2="a" k="48" />
<hkern u1="V" u2="]" k="-30" />
<hkern u1="V" u2="Y" k="-23" />
<hkern u1="V" u2="X" k="-5" />
<hkern u1="V" u2="W" k="9" />
<hkern u1="V" u2="V" k="-9" />
<hkern u1="V" u2="T" k="-20" />
<hkern u1="V" u2="S" k="-3" />
<hkern u1="V" u2="Q" k="20" />
<hkern u1="V" u2="O" k="20" />
<hkern u1="V" u2="J" k="80" />
<hkern u1="V" u2="G" k="20" />
<hkern u1="V" u2="C" k="20" />
<hkern u1="V" u2="A" k="74" />
<hkern u1="V" u2="&#x40;" k="8" />
<hkern u1="V" u2="&#x3b;" k="20" />
<hkern u1="V" u2="&#x3a;" k="20" />
<hkern u1="V" u2="&#x2e;" k="68" />
<hkern u1="V" u2="&#x2d;" k="60" />
<hkern u1="V" u2="&#x2c;" k="68" />
<hkern u1="V" u2="&#x29;" k="-30" />
<hkern u1="V" u2="&#x26;" k="33" />
<hkern u1="W" u2="&#x203a;" k="20" />
<hkern u1="W" u2="&#x2039;" k="40" />
<hkern u1="W" u2="&#x2026;" k="28" />
<hkern u1="W" u2="&#x201e;" k="28" />
<hkern u1="W" u2="&#x201a;" k="28" />
<hkern u1="W" u2="&#x2014;" k="40" />
<hkern u1="W" u2="&#x2013;" k="40" />
<hkern u1="W" u2="&#x153;" k="60" />
<hkern u1="W" u2="&#x152;" k="33" />
<hkern u1="W" u2="&#xfe;" k="8" />
<hkern u1="W" u2="&#xe7;" k="60" />
<hkern u1="W" u2="&#xe6;" k="53" />
<hkern u1="W" u2="&#xd8;" k="33" />
<hkern u1="W" u2="&#xd6;" k="33" />
<hkern u1="W" u2="&#xd5;" k="33" />
<hkern u1="W" u2="&#xd4;" k="33" />
<hkern u1="W" u2="&#xd3;" k="33" />
<hkern u1="W" u2="&#xd2;" k="33" />
<hkern u1="W" u2="&#xc6;" k="61" />
<hkern u1="W" u2="&#xc5;" k="61" />
<hkern u1="W" u2="&#xc4;" k="61" />
<hkern u1="W" u2="&#xc3;" k="61" />
<hkern u1="W" u2="&#xc2;" k="61" />
<hkern u1="W" u2="&#xc1;" k="61" />
<hkern u1="W" u2="&#xc0;" k="61" />
<hkern u1="W" u2="&#xbb;" k="20" />
<hkern u1="W" u2="&#xae;" k="20" />
<hkern u1="W" u2="&#xab;" k="40" />
<hkern u1="W" u2="&#xa9;" k="20" />
<hkern u1="W" u2="&#x7d;" k="-10" />
<hkern u1="W" u2="z" k="30" />
<hkern u1="W" u2="y" k="30" />
<hkern u1="W" u2="x" k="30" />
<hkern u1="W" u2="w" k="30" />
<hkern u1="W" u2="v" k="30" />
<hkern u1="W" u2="u" k="35" />
<hkern u1="W" u2="t" k="30" />
<hkern u1="W" u2="s" k="50" />
<hkern u1="W" u2="r" k="35" />
<hkern u1="W" u2="q" k="60" />
<hkern u1="W" u2="p" k="35" />
<hkern u1="W" u2="o" k="60" />
<hkern u1="W" u2="n" k="35" />
<hkern u1="W" u2="m" k="35" />
<hkern u1="W" u2="l" k="10" />
<hkern u1="W" u2="k" k="10" />
<hkern u1="W" u2="j" k="20" />
<hkern u1="W" u2="i" k="20" />
<hkern u1="W" u2="h" k="10" />
<hkern u1="W" u2="g" k="50" />
<hkern u1="W" u2="f" k="15" />
<hkern u1="W" u2="e" k="60" />
<hkern u1="W" u2="d" k="60" />
<hkern u1="W" u2="c" k="60" />
<hkern u1="W" u2="b" k="10" />
<hkern u1="W" u2="a" k="53" />
<hkern u1="W" u2="]" k="-10" />
<hkern u1="W" u2="X" k="18" />
<hkern u1="W" u2="W" k="23" />
<hkern u1="W" u2="V" k="9" />
<hkern u1="W" u2="S" k="11" />
<hkern u1="W" u2="Q" k="33" />
<hkern u1="W" u2="O" k="33" />
<hkern u1="W" u2="J" k="74" />
<hkern u1="W" u2="G" k="33" />
<hkern u1="W" u2="C" k="33" />
<hkern u1="W" u2="A" k="62" />
<hkern u1="W" u2="&#x40;" k="20" />
<hkern u1="W" u2="&#x3b;" k="20" />
<hkern u1="W" u2="&#x3a;" k="20" />
<hkern u1="W" u2="&#x2e;" k="28" />
<hkern u1="W" u2="&#x2d;" k="40" />
<hkern u1="W" u2="&#x2c;" k="28" />
<hkern u1="W" u2="&#x29;" k="-10" />
<hkern u1="W" u2="&#x26;" k="41" />
<hkern u1="X" u2="&#x2014;" k="30" />
<hkern u1="X" u2="&#x2013;" k="30" />
<hkern u1="X" u2="&#x178;" k="-13" />
<hkern u1="X" u2="&#x153;" k="10" />
<hkern u1="X" u2="&#x152;" k="29" />
<hkern u1="X" u2="&#xe7;" k="10" />
<hkern u1="X" u2="&#xe6;" k="10" />
<hkern u1="X" u2="&#xdd;" k="-13" />
<hkern u1="X" u2="&#xd8;" k="29" />
<hkern u1="X" u2="&#xd6;" k="29" />
<hkern u1="X" u2="&#xd5;" k="29" />
<hkern u1="X" u2="&#xd4;" k="29" />
<hkern u1="X" u2="&#xd3;" k="29" />
<hkern u1="X" u2="&#xd2;" k="29" />
<hkern u1="X" u2="&#xc6;" k="-10" />
<hkern u1="X" u2="&#xc5;" k="-10" />
<hkern u1="X" u2="&#xc4;" k="-10" />
<hkern u1="X" u2="&#xc3;" k="-10" />
<hkern u1="X" u2="&#xc2;" k="-10" />
<hkern u1="X" u2="&#xc1;" k="-10" />
<hkern u1="X" u2="&#xc0;" k="-10" />
<hkern u1="X" u2="&#xae;" k="23" />
<hkern u1="X" u2="&#xa9;" k="23" />
<hkern u1="X" u2="&#x7d;" k="-40" />
<hkern u1="X" u2="y" k="20" />
<hkern u1="X" u2="w" k="10" />
<hkern u1="X" u2="v" k="20" />
<hkern u1="X" u2="u" k="10" />
<hkern u1="X" u2="t" k="10" />
<hkern u1="X" u2="r" k="10" />
<hkern u1="X" u2="q" k="10" />
<hkern u1="X" u2="p" k="10" />
<hkern u1="X" u2="o" k="10" />
<hkern u1="X" u2="n" k="10" />
<hkern u1="X" u2="m" k="10" />
<hkern u1="X" u2="f" k="10" />
<hkern u1="X" u2="e" k="10" />
<hkern u1="X" u2="d" k="10" />
<hkern u1="X" u2="c" k="10" />
<hkern u1="X" u2="a" k="10" />
<hkern u1="X" u2="]" k="-40" />
<hkern u1="X" u2="Y" k="-13" />
<hkern u1="X" u2="X" k="-3" />
<hkern u1="X" u2="W" k="18" />
<hkern u1="X" u2="V" k="-5" />
<hkern u1="X" u2="T" k="-10" />
<hkern u1="X" u2="S" k="-9" />
<hkern u1="X" u2="Q" k="29" />
<hkern u1="X" u2="O" k="29" />
<hkern u1="X" u2="G" k="29" />
<hkern u1="X" u2="C" k="29" />
<hkern u1="X" u2="A" k="-10" />
<hkern u1="X" u2="&#x40;" k="23" />
<hkern u1="X" u2="&#x2d;" k="30" />
<hkern u1="X" u2="&#x29;" k="-40" />
<hkern u1="X" u2="&#x26;" k="5" />
<hkern u1="Y" u2="&#x203a;" k="40" />
<hkern u1="Y" u2="&#x2039;" k="60" />
<hkern u1="Y" u2="&#x2026;" k="50" />
<hkern u1="Y" u2="&#x201e;" k="50" />
<hkern u1="Y" u2="&#x201a;" k="50" />
<hkern u1="Y" u2="&#x2014;" k="50" />
<hkern u1="Y" u2="&#x2013;" k="50" />
<hkern u1="Y" u2="&#x153;" k="70" />
<hkern u1="Y" u2="&#x152;" k="18" />
<hkern u1="Y" u2="&#xe7;" k="70" />
<hkern u1="Y" u2="&#xe6;" k="63" />
<hkern u1="Y" u2="&#xd8;" k="18" />
<hkern u1="Y" u2="&#xd6;" k="18" />
<hkern u1="Y" u2="&#xd5;" k="18" />
<hkern u1="Y" u2="&#xd4;" k="18" />
<hkern u1="Y" u2="&#xd3;" k="18" />
<hkern u1="Y" u2="&#xd2;" k="18" />
<hkern u1="Y" u2="&#xc6;" k="54" />
<hkern u1="Y" u2="&#xc5;" k="54" />
<hkern u1="Y" u2="&#xc4;" k="54" />
<hkern u1="Y" u2="&#xc3;" k="54" />
<hkern u1="Y" u2="&#xc2;" k="54" />
<hkern u1="Y" u2="&#xc1;" k="54" />
<hkern u1="Y" u2="&#xc0;" k="54" />
<hkern u1="Y" u2="&#xbb;" k="40" />
<hkern u1="Y" u2="&#xae;" k="30" />
<hkern u1="Y" u2="&#xab;" k="60" />
<hkern u1="Y" u2="&#xa9;" k="30" />
<hkern u1="Y" u2="&#x7d;" k="-50" />
<hkern u1="Y" u2="z" k="30" />
<hkern u1="Y" u2="y" k="20" />
<hkern u1="Y" u2="x" k="20" />
<hkern u1="Y" u2="w" k="10" />
<hkern u1="Y" u2="v" k="20" />
<hkern u1="Y" u2="u" k="40" />
<hkern u1="Y" u2="t" k="20" />
<hkern u1="Y" u2="s" k="54" />
<hkern u1="Y" u2="r" k="40" />
<hkern u1="Y" u2="q" k="70" />
<hkern u1="Y" u2="p" k="40" />
<hkern u1="Y" u2="o" k="70" />
<hkern u1="Y" u2="n" k="40" />
<hkern u1="Y" u2="m" k="40" />
<hkern u1="Y" u2="g" k="40" />
<hkern u1="Y" u2="f" k="10" />
<hkern u1="Y" u2="e" k="70" />
<hkern u1="Y" u2="d" k="70" />
<hkern u1="Y" u2="c" k="70" />
<hkern u1="Y" u2="a" k="63" />
<hkern u1="Y" u2="]" k="-50" />
<hkern u1="Y" u2="X" k="-13" />
<hkern u1="Y" u2="V" k="-23" />
<hkern u1="Y" u2="T" k="-28" />
<hkern u1="Y" u2="S" k="-10" />
<hkern u1="Y" u2="Q" k="18" />
<hkern u1="Y" u2="O" k="18" />
<hkern u1="Y" u2="J" k="85" />
<hkern u1="Y" u2="G" k="18" />
<hkern u1="Y" u2="C" k="18" />
<hkern u1="Y" u2="A" k="54" />
<hkern u1="Y" u2="&#x40;" k="30" />
<hkern u1="Y" u2="&#x3b;" k="30" />
<hkern u1="Y" u2="&#x3a;" k="30" />
<hkern u1="Y" u2="&#x2f;" k="45" />
<hkern u1="Y" u2="&#x2e;" k="50" />
<hkern u1="Y" u2="&#x2d;" k="50" />
<hkern u1="Y" u2="&#x2c;" k="50" />
<hkern u1="Y" u2="&#x29;" k="-50" />
<hkern u1="Y" u2="&#x26;" k="30" />
<hkern u1="Z" u2="&#x2014;" k="20" />
<hkern u1="Z" u2="&#x2013;" k="20" />
<hkern u1="Z" u2="&#x153;" k="18" />
<hkern u1="Z" u2="&#x152;" k="15" />
<hkern u1="Z" u2="&#xfe;" k="8" />
<hkern u1="Z" u2="&#xf0;" k="30" />
<hkern u1="Z" u2="&#xe7;" k="18" />
<hkern u1="Z" u2="&#xe6;" k="8" />
<hkern u1="Z" u2="&#xd8;" k="15" />
<hkern u1="Z" u2="&#xd6;" k="15" />
<hkern u1="Z" u2="&#xd5;" k="15" />
<hkern u1="Z" u2="&#xd4;" k="15" />
<hkern u1="Z" u2="&#xd3;" k="15" />
<hkern u1="Z" u2="&#xd2;" k="15" />
<hkern u1="Z" u2="&#xc6;" k="10" />
<hkern u1="Z" u2="&#xc5;" k="10" />
<hkern u1="Z" u2="&#xc4;" k="10" />
<hkern u1="Z" u2="&#xc3;" k="10" />
<hkern u1="Z" u2="&#xc2;" k="10" />
<hkern u1="Z" u2="&#xc1;" k="10" />
<hkern u1="Z" u2="&#xc0;" k="10" />
<hkern u1="Z" u2="y" k="30" />
<hkern u1="Z" u2="w" k="10" />
<hkern u1="Z" u2="v" k="30" />
<hkern u1="Z" u2="u" k="20" />
<hkern u1="Z" u2="r" k="20" />
<hkern u1="Z" u2="q" k="18" />
<hkern u1="Z" u2="p" k="20" />
<hkern u1="Z" u2="o" k="18" />
<hkern u1="Z" u2="n" k="20" />
<hkern u1="Z" u2="m" k="20" />
<hkern u1="Z" u2="l" k="10" />
<hkern u1="Z" u2="k" k="10" />
<hkern u1="Z" u2="j" k="15" />
<hkern u1="Z" u2="i" k="20" />
<hkern u1="Z" u2="h" k="10" />
<hkern u1="Z" u2="g" k="20" />
<hkern u1="Z" u2="f" k="20" />
<hkern u1="Z" u2="e" k="18" />
<hkern u1="Z" u2="d" k="18" />
<hkern u1="Z" u2="c" k="18" />
<hkern u1="Z" u2="b" k="10" />
<hkern u1="Z" u2="a" k="8" />
<hkern u1="Z" u2="T" k="-1" />
<hkern u1="Z" u2="Q" k="15" />
<hkern u1="Z" u2="O" k="15" />
<hkern u1="Z" u2="J" k="-5" />
<hkern u1="Z" u2="G" k="15" />
<hkern u1="Z" u2="C" k="15" />
<hkern u1="Z" u2="A" k="10" />
<hkern u1="Z" u2="&#x2d;" k="20" />
<hkern u1="[" u2="&#x178;" k="-50" />
<hkern u1="[" u2="&#xdd;" k="-50" />
<hkern u1="[" u2="j" k="-150" />
<hkern u1="[" u2="g" k="-20" />
<hkern u1="[" u2="Y" k="-50" />
<hkern u1="[" u2="X" k="-40" />
<hkern u1="[" u2="W" k="-10" />
<hkern u1="[" u2="V" k="-30" />
<hkern u1="[" u2="J" k="15" />
<hkern u1="[" u2="&#x37;" k="-15" />
<hkern u1="\" u2="&#x178;" k="60" />
<hkern u1="\" u2="&#x153;" k="10" />
<hkern u1="\" u2="&#xe7;" k="10" />
<hkern u1="\" u2="&#xe6;" k="10" />
<hkern u1="\" u2="&#xdd;" k="60" />
<hkern u1="\" u2="q" k="10" />
<hkern u1="\" u2="o" k="10" />
<hkern u1="\" u2="j" k="-130" />
<hkern u1="\" u2="e" k="10" />
<hkern u1="\" u2="d" k="10" />
<hkern u1="\" u2="c" k="10" />
<hkern u1="\" u2="a" k="10" />
<hkern u1="\" u2="Y" k="60" />
<hkern u1="\" u2="W" k="50" />
<hkern u1="\" u2="V" k="70" />
<hkern u1="\" u2="T" k="90" />
<hkern u1="a" u2="&#x2122;" k="20" />
<hkern u1="a" u2="&#x201c;" k="30" />
<hkern u1="a" u2="&#x2018;" k="30" />
<hkern u1="a" u2="y" k="9" />
<hkern u1="a" u2="v" k="9" />
<hkern u1="a" u2="t" k="10" />
<hkern u1="a" u2="f" k="4" />
<hkern u1="a" u2="\" k="10" />
<hkern u1="a" u2="&#x3f;" k="18" />
<hkern u1="b" u2="&#x2122;" k="30" />
<hkern u1="b" u2="&#x2026;" k="20" />
<hkern u1="b" u2="&#x201e;" k="20" />
<hkern u1="b" u2="&#x201c;" k="25" />
<hkern u1="b" u2="&#x201a;" k="20" />
<hkern u1="b" u2="&#x2018;" k="25" />
<hkern u1="b" u2="z" k="19" />
<hkern u1="b" u2="y" k="21" />
<hkern u1="b" u2="x" k="23" />
<hkern u1="b" u2="w" k="11" />
<hkern u1="b" u2="v" k="21" />
<hkern u1="b" u2="t" k="11" />
<hkern u1="b" u2="f" k="8" />
<hkern u1="b" u2="\" k="10" />
<hkern u1="b" u2="&#x3f;" k="31" />
<hkern u1="b" u2="&#x3b;" k="10" />
<hkern u1="b" u2="&#x3a;" k="10" />
<hkern u1="b" u2="&#x2f;" k="10" />
<hkern u1="b" u2="&#x2e;" k="20" />
<hkern u1="b" u2="&#x2c;" k="20" />
<hkern u1="b" u2="&#x21;" k="15" />
<hkern u1="c" u2="&#x2039;" k="15" />
<hkern u1="c" u2="&#xab;" k="15" />
<hkern u1="e" u2="y" k="18" />
<hkern u1="e" u2="x" k="19" />
<hkern u1="e" u2="w" k="8" />
<hkern u1="e" u2="v" k="18" />
<hkern u1="e" u2="t" k="4" />
<hkern u1="e" u2="f" k="8" />
<hkern u1="f" u2="&#x2122;" k="-48" />
<hkern u1="f" u2="&#x203a;" k="-3" />
<hkern u1="f" u2="&#x2039;" k="13" />
<hkern u1="f" u2="&#x2026;" k="58" />
<hkern u1="f" u2="&#x201e;" k="58" />
<hkern u1="f" u2="&#x201d;" k="-50" />
<hkern u1="f" u2="&#x201c;" k="-30" />
<hkern u1="f" u2="&#x201a;" k="58" />
<hkern u1="f" u2="&#x2019;" k="-50" />
<hkern u1="f" u2="&#x2018;" k="-30" />
<hkern u1="f" u2="&#x2014;" k="15" />
<hkern u1="f" u2="&#x2013;" k="15" />
<hkern u1="f" u2="&#x153;" k="16" />
<hkern u1="f" u2="&#xfe;" k="-11" />
<hkern u1="f" u2="&#xf0;" k="20" />
<hkern u1="f" u2="&#xe7;" k="16" />
<hkern u1="f" u2="&#xe6;" k="16" />
<hkern u1="f" u2="&#xbb;" k="-3" />
<hkern u1="f" u2="&#xae;" k="-15" />
<hkern u1="f" u2="&#xab;" k="13" />
<hkern u1="f" u2="&#xa9;" k="-15" />
<hkern u1="f" u2="&#x7d;" k="-70" />
<hkern u1="f" u2="u" k="1" />
<hkern u1="f" u2="t" k="-8" />
<hkern u1="f" u2="r" k="1" />
<hkern u1="f" u2="q" k="16" />
<hkern u1="f" u2="p" k="1" />
<hkern u1="f" u2="o" k="16" />
<hkern u1="f" u2="n" k="1" />
<hkern u1="f" u2="m" k="1" />
<hkern u1="f" u2="l" k="-11" />
<hkern u1="f" u2="k" k="-11" />
<hkern u1="f" u2="h" k="-11" />
<hkern u1="f" u2="g" k="8" />
<hkern u1="f" u2="f" k="-8" />
<hkern u1="f" u2="e" k="16" />
<hkern u1="f" u2="d" k="16" />
<hkern u1="f" u2="c" k="16" />
<hkern u1="f" u2="b" k="-11" />
<hkern u1="f" u2="a" k="16" />
<hkern u1="f" u2="]" k="-70" />
<hkern u1="f" u2="\" k="-40" />
<hkern u1="f" u2="&#x40;" k="-15" />
<hkern u1="f" u2="&#x3f;" k="-40" />
<hkern u1="f" u2="&#x3b;" k="-5" />
<hkern u1="f" u2="&#x3a;" k="-5" />
<hkern u1="f" u2="&#x2f;" k="65" />
<hkern u1="f" u2="&#x2e;" k="58" />
<hkern u1="f" u2="&#x2d;" k="15" />
<hkern u1="f" u2="&#x2c;" k="58" />
<hkern u1="f" u2="&#x2a;" k="-28" />
<hkern u1="f" u2="&#x29;" k="-70" />
<hkern u1="f" u2="&#x26;" k="15" />
<hkern u1="f" u2="&#x21;" k="-30" />
<hkern u1="g" u2="&#x201d;" k="-40" />
<hkern u1="g" u2="&#x201c;" k="-20" />
<hkern u1="g" u2="&#x2019;" k="-40" />
<hkern u1="g" u2="&#x2018;" k="-20" />
<hkern u1="g" u2="&#xae;" k="-15" />
<hkern u1="g" u2="&#xa9;" k="-15" />
<hkern u1="g" u2="&#x7d;" k="-20" />
<hkern u1="g" u2="t" k="-10" />
<hkern u1="g" u2="j" k="-50" />
<hkern u1="g" u2="g" k="-10" />
<hkern u1="g" u2="]" k="-20" />
<hkern u1="g" u2="&#x40;" k="-15" />
<hkern u1="g" u2="&#x3f;" k="-23" />
<hkern u1="g" u2="&#x3b;" k="-20" />
<hkern u1="g" u2="&#x2f;" k="-48" />
<hkern u1="g" u2="&#x2c;" k="-23" />
<hkern u1="g" u2="&#x2a;" k="-30" />
<hkern u1="g" u2="&#x29;" k="-20" />
<hkern u1="h" u2="&#x2122;" k="20" />
<hkern u1="h" u2="&#x201c;" k="30" />
<hkern u1="h" u2="&#x2018;" k="30" />
<hkern u1="h" u2="y" k="9" />
<hkern u1="h" u2="v" k="9" />
<hkern u1="h" u2="t" k="10" />
<hkern u1="h" u2="f" k="4" />
<hkern u1="h" u2="\" k="10" />
<hkern u1="h" u2="&#x3f;" k="18" />
<hkern u1="k" u2="&#x2039;" k="30" />
<hkern u1="k" u2="&#x153;" k="14" />
<hkern u1="k" u2="&#xe7;" k="14" />
<hkern u1="k" u2="&#xe6;" k="14" />
<hkern u1="k" u2="&#xab;" k="30" />
<hkern u1="k" u2="y" k="4" />
<hkern u1="k" u2="v" k="4" />
<hkern u1="k" u2="q" k="14" />
<hkern u1="k" u2="o" k="14" />
<hkern u1="k" u2="g" k="20" />
<hkern u1="k" u2="e" k="14" />
<hkern u1="k" u2="d" k="14" />
<hkern u1="k" u2="c" k="14" />
<hkern u1="k" u2="a" k="14" />
<hkern u1="m" u2="&#x2122;" k="20" />
<hkern u1="m" u2="&#x201c;" k="30" />
<hkern u1="m" u2="&#x2018;" k="30" />
<hkern u1="m" u2="y" k="9" />
<hkern u1="m" u2="v" k="9" />
<hkern u1="m" u2="t" k="10" />
<hkern u1="m" u2="f" k="4" />
<hkern u1="m" u2="\" k="10" />
<hkern u1="m" u2="&#x3f;" k="18" />
<hkern u1="n" u2="&#x2122;" k="20" />
<hkern u1="n" u2="&#x201c;" k="30" />
<hkern u1="n" u2="&#x2018;" k="30" />
<hkern u1="n" u2="y" k="9" />
<hkern u1="n" u2="v" k="9" />
<hkern u1="n" u2="t" k="10" />
<hkern u1="n" u2="f" k="4" />
<hkern u1="n" u2="\" k="10" />
<hkern u1="n" u2="&#x3f;" k="18" />
<hkern u1="o" u2="&#x2122;" k="30" />
<hkern u1="o" u2="&#x2026;" k="20" />
<hkern u1="o" u2="&#x201e;" k="20" />
<hkern u1="o" u2="&#x201c;" k="25" />
<hkern u1="o" u2="&#x201a;" k="20" />
<hkern u1="o" u2="&#x2018;" k="25" />
<hkern u1="o" u2="z" k="19" />
<hkern u1="o" u2="y" k="21" />
<hkern u1="o" u2="x" k="23" />
<hkern u1="o" u2="w" k="11" />
<hkern u1="o" u2="v" k="21" />
<hkern u1="o" u2="t" k="11" />
<hkern u1="o" u2="f" k="8" />
<hkern u1="o" u2="\" k="10" />
<hkern u1="o" u2="&#x3f;" k="31" />
<hkern u1="o" u2="&#x3b;" k="10" />
<hkern u1="o" u2="&#x3a;" k="10" />
<hkern u1="o" u2="&#x2f;" k="10" />
<hkern u1="o" u2="&#x2e;" k="20" />
<hkern u1="o" u2="&#x2c;" k="20" />
<hkern u1="o" u2="&#x21;" k="15" />
<hkern u1="p" u2="&#x2122;" k="30" />
<hkern u1="p" u2="&#x2026;" k="20" />
<hkern u1="p" u2="&#x201e;" k="20" />
<hkern u1="p" u2="&#x201c;" k="25" />
<hkern u1="p" u2="&#x201a;" k="20" />
<hkern u1="p" u2="&#x2018;" k="25" />
<hkern u1="p" u2="z" k="19" />
<hkern u1="p" u2="y" k="21" />
<hkern u1="p" u2="x" k="23" />
<hkern u1="p" u2="w" k="11" />
<hkern u1="p" u2="v" k="21" />
<hkern u1="p" u2="t" k="11" />
<hkern u1="p" u2="f" k="8" />
<hkern u1="p" u2="\" k="10" />
<hkern u1="p" u2="&#x3f;" k="31" />
<hkern u1="p" u2="&#x3b;" k="10" />
<hkern u1="p" u2="&#x3a;" k="10" />
<hkern u1="p" u2="&#x2f;" k="10" />
<hkern u1="p" u2="&#x2e;" k="20" />
<hkern u1="p" u2="&#x2c;" k="20" />
<hkern u1="p" u2="&#x21;" k="15" />
<hkern u1="r" u2="&#x2026;" k="80" />
<hkern u1="r" u2="&#x201e;" k="80" />
<hkern u1="r" u2="&#x201d;" k="-40" />
<hkern u1="r" u2="&#x201c;" k="-20" />
<hkern u1="r" u2="&#x201a;" k="80" />
<hkern u1="r" u2="&#x2019;" k="-40" />
<hkern u1="r" u2="&#x2018;" k="-20" />
<hkern u1="r" u2="&#x2014;" k="13" />
<hkern u1="r" u2="&#x2013;" k="13" />
<hkern u1="r" u2="&#x153;" k="9" />
<hkern u1="r" u2="&#xe7;" k="9" />
<hkern u1="r" u2="&#xe6;" k="9" />
<hkern u1="r" u2="x" k="4" />
<hkern u1="r" u2="t" k="-5" />
<hkern u1="r" u2="q" k="9" />
<hkern u1="r" u2="o" k="9" />
<hkern u1="r" u2="g" k="10" />
<hkern u1="r" u2="f" k="-4" />
<hkern u1="r" u2="e" k="9" />
<hkern u1="r" u2="d" k="9" />
<hkern u1="r" u2="c" k="9" />
<hkern u1="r" u2="a" k="9" />
<hkern u1="r" u2="&#x3f;" k="-5" />
<hkern u1="r" u2="&#x2f;" k="60" />
<hkern u1="r" u2="&#x2e;" k="80" />
<hkern u1="r" u2="&#x2d;" k="13" />
<hkern u1="r" u2="&#x2c;" k="80" />
<hkern u1="r" u2="&#x2a;" k="-8" />
<hkern u1="r" u2="&#x26;" k="23" />
<hkern u1="s" u2="s" k="-4" />
<hkern u1="s" u2="g" k="8" />
<hkern u1="s" u2="&#x3f;" k="15" />
<hkern u1="t" u2="&#x2026;" k="-10" />
<hkern u1="t" u2="&#x201e;" k="-10" />
<hkern u1="t" u2="&#x201c;" k="-10" />
<hkern u1="t" u2="&#x201a;" k="-10" />
<hkern u1="t" u2="&#x2018;" k="-10" />
<hkern u1="t" u2="&#x153;" k="13" />
<hkern u1="t" u2="&#xe7;" k="13" />
<hkern u1="t" u2="&#xe6;" k="13" />
<hkern u1="t" u2="y" k="11" />
<hkern u1="t" u2="v" k="11" />
<hkern u1="t" u2="t" k="4" />
<hkern u1="t" u2="q" k="13" />
<hkern u1="t" u2="o" k="13" />
<hkern u1="t" u2="e" k="13" />
<hkern u1="t" u2="d" k="13" />
<hkern u1="t" u2="c" k="13" />
<hkern u1="t" u2="a" k="13" />
<hkern u1="t" u2="&#x2e;" k="-10" />
<hkern u1="t" u2="&#x2c;" k="-10" />
<hkern u1="u" u2="&#x3f;" k="8" />
<hkern u1="v" u2="&#x2039;" k="23" />
<hkern u1="v" u2="&#x2026;" k="50" />
<hkern u1="v" u2="&#x201e;" k="50" />
<hkern u1="v" u2="&#x201d;" k="-30" />
<hkern u1="v" u2="&#x201c;" k="-30" />
<hkern u1="v" u2="&#x201a;" k="50" />
<hkern u1="v" u2="&#x2019;" k="-30" />
<hkern u1="v" u2="&#x2018;" k="-30" />
<hkern u1="v" u2="&#x2014;" k="10" />
<hkern u1="v" u2="&#x2013;" k="10" />
<hkern u1="v" u2="&#x153;" k="21" />
<hkern u1="v" u2="&#xe7;" k="21" />
<hkern u1="v" u2="&#xe6;" k="21" />
<hkern u1="v" u2="&#xab;" k="23" />
<hkern u1="v" u2="y" k="11" />
<hkern u1="v" u2="x" k="15" />
<hkern u1="v" u2="w" k="11" />
<hkern u1="v" u2="v" k="11" />
<hkern u1="v" u2="q" k="21" />
<hkern u1="v" u2="o" k="21" />
<hkern u1="v" u2="e" k="21" />
<hkern u1="v" u2="d" k="21" />
<hkern u1="v" u2="c" k="21" />
<hkern u1="v" u2="a" k="21" />
<hkern u1="v" u2="&#x2e;" k="50" />
<hkern u1="v" u2="&#x2d;" k="10" />
<hkern u1="v" u2="&#x2c;" k="50" />
<hkern u1="w" u2="&#x2039;" k="15" />
<hkern u1="w" u2="&#x2026;" k="30" />
<hkern u1="w" u2="&#x201e;" k="30" />
<hkern u1="w" u2="&#x201d;" k="-20" />
<hkern u1="w" u2="&#x201c;" k="-20" />
<hkern u1="w" u2="&#x201a;" k="30" />
<hkern u1="w" u2="&#x2019;" k="-20" />
<hkern u1="w" u2="&#x2018;" k="-20" />
<hkern u1="w" u2="&#x153;" k="11" />
<hkern u1="w" u2="&#xe7;" k="11" />
<hkern u1="w" u2="&#xe6;" k="11" />
<hkern u1="w" u2="&#xab;" k="15" />
<hkern u1="w" u2="y" k="11" />
<hkern u1="w" u2="x" k="11" />
<hkern u1="w" u2="w" k="11" />
<hkern u1="w" u2="v" k="11" />
<hkern u1="w" u2="q" k="11" />
<hkern u1="w" u2="o" k="11" />
<hkern u1="w" u2="e" k="11" />
<hkern u1="w" u2="d" k="11" />
<hkern u1="w" u2="c" k="11" />
<hkern u1="w" u2="a" k="11" />
<hkern u1="w" u2="&#x2e;" k="30" />
<hkern u1="w" u2="&#x2c;" k="30" />
<hkern u1="x" u2="&#x2039;" k="50" />
<hkern u1="x" u2="&#x201d;" k="-20" />
<hkern u1="x" u2="&#x201c;" k="-10" />
<hkern u1="x" u2="&#x2019;" k="-20" />
<hkern u1="x" u2="&#x2018;" k="-10" />
<hkern u1="x" u2="&#x2014;" k="30" />
<hkern u1="x" u2="&#x2013;" k="30" />
<hkern u1="x" u2="&#x153;" k="23" />
<hkern u1="x" u2="&#xe7;" k="23" />
<hkern u1="x" u2="&#xe6;" k="23" />
<hkern u1="x" u2="&#xab;" k="50" />
<hkern u1="x" u2="y" k="15" />
<hkern u1="x" u2="w" k="11" />
<hkern u1="x" u2="v" k="15" />
<hkern u1="x" u2="q" k="23" />
<hkern u1="x" u2="o" k="23" />
<hkern u1="x" u2="e" k="23" />
<hkern u1="x" u2="d" k="23" />
<hkern u1="x" u2="c" k="23" />
<hkern u1="x" u2="a" k="23" />
<hkern u1="x" u2="&#x2d;" k="30" />
<hkern u1="y" u2="&#x2039;" k="23" />
<hkern u1="y" u2="&#x2026;" k="50" />
<hkern u1="y" u2="&#x201e;" k="50" />
<hkern u1="y" u2="&#x201d;" k="-30" />
<hkern u1="y" u2="&#x201c;" k="-30" />
<hkern u1="y" u2="&#x201a;" k="50" />
<hkern u1="y" u2="&#x2019;" k="-30" />
<hkern u1="y" u2="&#x2018;" k="-30" />
<hkern u1="y" u2="&#x2014;" k="10" />
<hkern u1="y" u2="&#x2013;" k="10" />
<hkern u1="y" u2="&#x153;" k="21" />
<hkern u1="y" u2="&#xe7;" k="21" />
<hkern u1="y" u2="&#xe6;" k="21" />
<hkern u1="y" u2="&#xab;" k="23" />
<hkern u1="y" u2="y" k="11" />
<hkern u1="y" u2="x" k="15" />
<hkern u1="y" u2="w" k="11" />
<hkern u1="y" u2="v" k="11" />
<hkern u1="y" u2="q" k="21" />
<hkern u1="y" u2="o" k="21" />
<hkern u1="y" u2="e" k="21" />
<hkern u1="y" u2="d" k="21" />
<hkern u1="y" u2="c" k="21" />
<hkern u1="y" u2="a" k="21" />
<hkern u1="y" u2="&#x2e;" k="50" />
<hkern u1="y" u2="&#x2d;" k="10" />
<hkern u1="y" u2="&#x2c;" k="50" />
<hkern u1="z" u2="&#x2039;" k="45" />
<hkern u1="z" u2="&#x2014;" k="15" />
<hkern u1="z" u2="&#x2013;" k="15" />
<hkern u1="z" u2="&#x153;" k="20" />
<hkern u1="z" u2="&#xe7;" k="20" />
<hkern u1="z" u2="&#xe6;" k="20" />
<hkern u1="z" u2="&#xab;" k="45" />
<hkern u1="z" u2="q" k="20" />
<hkern u1="z" u2="o" k="20" />
<hkern u1="z" u2="g" k="3" />
<hkern u1="z" u2="e" k="20" />
<hkern u1="z" u2="d" k="20" />
<hkern u1="z" u2="c" k="20" />
<hkern u1="z" u2="a" k="20" />
<hkern u1="z" u2="&#x2d;" k="15" />
<hkern u1="&#x7b;" u2="&#x178;" k="-50" />
<hkern u1="&#x7b;" u2="&#xdd;" k="-50" />
<hkern u1="&#x7b;" u2="j" k="-150" />
<hkern u1="&#x7b;" u2="g" k="-20" />
<hkern u1="&#x7b;" u2="Y" k="-50" />
<hkern u1="&#x7b;" u2="X" k="-40" />
<hkern u1="&#x7b;" u2="W" k="-10" />
<hkern u1="&#x7b;" u2="V" k="-30" />
<hkern u1="&#x7b;" u2="J" k="15" />
<hkern u1="&#x7b;" u2="&#x37;" k="-15" />
<hkern u1="&#xa1;" u2="&#x178;" k="15" />
<hkern u1="&#xa1;" u2="&#xdd;" k="15" />
<hkern u1="&#xa1;" u2="y" k="30" />
<hkern u1="&#xa1;" u2="w" k="19" />
<hkern u1="&#xa1;" u2="v" k="30" />
<hkern u1="&#xa1;" u2="f" k="15" />
<hkern u1="&#xa1;" u2="Y" k="15" />
<hkern u1="&#xa1;" u2="W" k="15" />
<hkern u1="&#xa1;" u2="V" k="15" />
<hkern u1="&#xa3;" u2="&#x34;" k="20" />
<hkern u1="&#xa3;" u2="&#x31;" k="-10" />
<hkern u1="&#xa4;" u2="&#x34;" k="20" />
<hkern u1="&#xa9;" u2="&#x178;" k="30" />
<hkern u1="&#xa9;" u2="&#xdd;" k="30" />
<hkern u1="&#xa9;" u2="&#xc6;" k="10" />
<hkern u1="&#xa9;" u2="&#xc5;" k="10" />
<hkern u1="&#xa9;" u2="&#xc4;" k="10" />
<hkern u1="&#xa9;" u2="&#xc3;" k="10" />
<hkern u1="&#xa9;" u2="&#xc2;" k="10" />
<hkern u1="&#xa9;" u2="&#xc1;" k="10" />
<hkern u1="&#xa9;" u2="&#xc0;" k="10" />
<hkern u1="&#xa9;" u2="g" k="-8" />
<hkern u1="&#xa9;" u2="Y" k="30" />
<hkern u1="&#xa9;" u2="X" k="23" />
<hkern u1="&#xa9;" u2="W" k="20" />
<hkern u1="&#xa9;" u2="V" k="8" />
<hkern u1="&#xa9;" u2="T" k="30" />
<hkern u1="&#xa9;" u2="A" k="10" />
<hkern u1="&#xa9;" u2="&#x33;" k="8" />
<hkern u1="&#xab;" u2="&#x178;" k="40" />
<hkern u1="&#xab;" u2="&#xdd;" k="40" />
<hkern u1="&#xab;" u2="f" k="-15" />
<hkern u1="&#xab;" u2="Y" k="40" />
<hkern u1="&#xab;" u2="W" k="20" />
<hkern u1="&#xab;" u2="V" k="20" />
<hkern u1="&#xab;" u2="T" k="40" />
<hkern u1="&#xae;" u2="&#x178;" k="30" />
<hkern u1="&#xae;" u2="&#xdd;" k="30" />
<hkern u1="&#xae;" u2="&#xc6;" k="10" />
<hkern u1="&#xae;" u2="&#xc5;" k="10" />
<hkern u1="&#xae;" u2="&#xc4;" k="10" />
<hkern u1="&#xae;" u2="&#xc3;" k="10" />
<hkern u1="&#xae;" u2="&#xc2;" k="10" />
<hkern u1="&#xae;" u2="&#xc1;" k="10" />
<hkern u1="&#xae;" u2="&#xc0;" k="10" />
<hkern u1="&#xae;" u2="g" k="-8" />
<hkern u1="&#xae;" u2="Y" k="30" />
<hkern u1="&#xae;" u2="X" k="23" />
<hkern u1="&#xae;" u2="W" k="20" />
<hkern u1="&#xae;" u2="V" k="8" />
<hkern u1="&#xae;" u2="T" k="30" />
<hkern u1="&#xae;" u2="A" k="10" />
<hkern u1="&#xae;" u2="&#x33;" k="8" />
<hkern u1="&#xb0;" u2="&#x34;" k="16" />
<hkern u1="&#xbb;" u2="&#x178;" k="50" />
<hkern u1="&#xbb;" u2="&#xdd;" k="50" />
<hkern u1="&#xbb;" u2="z" k="35" />
<hkern u1="&#xbb;" u2="y" k="23" />
<hkern u1="&#xbb;" u2="x" k="50" />
<hkern u1="&#xbb;" u2="w" k="15" />
<hkern u1="&#xbb;" u2="v" k="23" />
<hkern u1="&#xbb;" u2="f" k="8" />
<hkern u1="&#xbb;" u2="Y" k="50" />
<hkern u1="&#xbb;" u2="W" k="40" />
<hkern u1="&#xbb;" u2="V" k="50" />
<hkern u1="&#xbb;" u2="T" k="110" />
<hkern u1="&#xbf;" u2="&#x178;" k="55" />
<hkern u1="&#xbf;" u2="&#x153;" k="-8" />
<hkern u1="&#xbf;" u2="&#x152;" k="20" />
<hkern u1="&#xbf;" u2="&#xe7;" k="-8" />
<hkern u1="&#xbf;" u2="&#xe6;" k="-8" />
<hkern u1="&#xbf;" u2="&#xdd;" k="55" />
<hkern u1="&#xbf;" u2="&#xd8;" k="20" />
<hkern u1="&#xbf;" u2="&#xd6;" k="20" />
<hkern u1="&#xbf;" u2="&#xd5;" k="20" />
<hkern u1="&#xbf;" u2="&#xd4;" k="20" />
<hkern u1="&#xbf;" u2="&#xd3;" k="20" />
<hkern u1="&#xbf;" u2="&#xd2;" k="20" />
<hkern u1="&#xbf;" u2="y" k="45" />
<hkern u1="&#xbf;" u2="x" k="10" />
<hkern u1="&#xbf;" u2="w" k="35" />
<hkern u1="&#xbf;" u2="v" k="45" />
<hkern u1="&#xbf;" u2="t" k="30" />
<hkern u1="&#xbf;" u2="s" k="-8" />
<hkern u1="&#xbf;" u2="q" k="-8" />
<hkern u1="&#xbf;" u2="o" k="-8" />
<hkern u1="&#xbf;" u2="e" k="-8" />
<hkern u1="&#xbf;" u2="d" k="-8" />
<hkern u1="&#xbf;" u2="c" k="-8" />
<hkern u1="&#xbf;" u2="a" k="-8" />
<hkern u1="&#xbf;" u2="Y" k="55" />
<hkern u1="&#xbf;" u2="W" k="45" />
<hkern u1="&#xbf;" u2="V" k="65" />
<hkern u1="&#xbf;" u2="T" k="68" />
<hkern u1="&#xbf;" u2="Q" k="20" />
<hkern u1="&#xbf;" u2="O" k="20" />
<hkern u1="&#xbf;" u2="G" k="20" />
<hkern u1="&#xbf;" u2="C" k="20" />
<hkern u1="&#xbf;" u2="&#x37;" k="38" />
<hkern u1="&#xbf;" u2="&#x34;" k="-8" />
<hkern u1="&#xbf;" u2="&#x33;" k="-3" />
<hkern u1="&#xbf;" u2="&#x32;" k="-15" />
<hkern u1="&#xbf;" u2="&#x31;" k="55" />
<hkern u1="&#xc0;" u2="&#x2122;" k="70" />
<hkern u1="&#xc0;" u2="&#x2026;" k="-30" />
<hkern u1="&#xc0;" u2="&#x201e;" k="-30" />
<hkern u1="&#xc0;" u2="&#x201d;" k="30" />
<hkern u1="&#xc0;" u2="&#x201c;" k="60" />
<hkern u1="&#xc0;" u2="&#x201a;" k="-30" />
<hkern u1="&#xc0;" u2="&#x2019;" k="30" />
<hkern u1="&#xc0;" u2="&#x2018;" k="60" />
<hkern u1="&#xc0;" u2="&#x2014;" k="-10" />
<hkern u1="&#xc0;" u2="&#x2013;" k="-10" />
<hkern u1="&#xc0;" u2="&#x178;" k="54" />
<hkern u1="&#xc0;" u2="&#x153;" k="5" />
<hkern u1="&#xc0;" u2="&#x152;" k="20" />
<hkern u1="&#xc0;" u2="&#xe7;" k="5" />
<hkern u1="&#xc0;" u2="&#xe6;" k="5" />
<hkern u1="&#xc0;" u2="&#xdd;" k="54" />
<hkern u1="&#xc0;" u2="&#xd8;" k="20" />
<hkern u1="&#xc0;" u2="&#xd6;" k="20" />
<hkern u1="&#xc0;" u2="&#xd5;" k="20" />
<hkern u1="&#xc0;" u2="&#xd4;" k="20" />
<hkern u1="&#xc0;" u2="&#xd3;" k="20" />
<hkern u1="&#xc0;" u2="&#xd2;" k="20" />
<hkern u1="&#xc0;" u2="&#xc6;" k="-10" />
<hkern u1="&#xc0;" u2="&#xc5;" k="-10" />
<hkern u1="&#xc0;" u2="&#xc4;" k="-10" />
<hkern u1="&#xc0;" u2="&#xc3;" k="-10" />
<hkern u1="&#xc0;" u2="&#xc2;" k="-10" />
<hkern u1="&#xc0;" u2="&#xc1;" k="-10" />
<hkern u1="&#xc0;" u2="&#xc0;" k="-10" />
<hkern u1="&#xc0;" u2="&#xae;" k="10" />
<hkern u1="&#xc0;" u2="&#xa9;" k="10" />
<hkern u1="&#xc0;" u2="z" k="-10" />
<hkern u1="&#xc0;" u2="y" k="60" />
<hkern u1="&#xc0;" u2="x" k="-13" />
<hkern u1="&#xc0;" u2="w" k="40" />
<hkern u1="&#xc0;" u2="v" k="60" />
<hkern u1="&#xc0;" u2="u" k="5" />
<hkern u1="&#xc0;" u2="t" k="5" />
<hkern u1="&#xc0;" u2="s" k="-11" />
<hkern u1="&#xc0;" u2="r" k="5" />
<hkern u1="&#xc0;" u2="q" k="5" />
<hkern u1="&#xc0;" u2="p" k="5" />
<hkern u1="&#xc0;" u2="o" k="5" />
<hkern u1="&#xc0;" u2="n" k="5" />
<hkern u1="&#xc0;" u2="m" k="5" />
<hkern u1="&#xc0;" u2="g" k="11" />
<hkern u1="&#xc0;" u2="f" k="30" />
<hkern u1="&#xc0;" u2="e" k="5" />
<hkern u1="&#xc0;" u2="d" k="5" />
<hkern u1="&#xc0;" u2="c" k="5" />
<hkern u1="&#xc0;" u2="a" k="5" />
<hkern u1="&#xc0;" u2="Y" k="54" />
<hkern u1="&#xc0;" u2="X" k="-10" />
<hkern u1="&#xc0;" u2="W" k="61" />
<hkern u1="&#xc0;" u2="V" k="73" />
<hkern u1="&#xc0;" u2="T" k="59" />
<hkern u1="&#xc0;" u2="S" k="-5" />
<hkern u1="&#xc0;" u2="Q" k="20" />
<hkern u1="&#xc0;" u2="O" k="20" />
<hkern u1="&#xc0;" u2="J" k="-10" />
<hkern u1="&#xc0;" u2="G" k="20" />
<hkern u1="&#xc0;" u2="C" k="20" />
<hkern u1="&#xc0;" u2="A" k="-10" />
<hkern u1="&#xc0;" u2="&#x40;" k="10" />
<hkern u1="&#xc0;" u2="&#x3f;" k="23" />
<hkern u1="&#xc0;" u2="&#x2e;" k="-30" />
<hkern u1="&#xc0;" u2="&#x2d;" k="-10" />
<hkern u1="&#xc0;" u2="&#x2c;" k="-30" />
<hkern u1="&#xc0;" u2="&#x2a;" k="75" />
<hkern u1="&#xc1;" u2="&#x2122;" k="70" />
<hkern u1="&#xc1;" u2="&#x2026;" k="-30" />
<hkern u1="&#xc1;" u2="&#x201e;" k="-30" />
<hkern u1="&#xc1;" u2="&#x201d;" k="30" />
<hkern u1="&#xc1;" u2="&#x201c;" k="60" />
<hkern u1="&#xc1;" u2="&#x201a;" k="-30" />
<hkern u1="&#xc1;" u2="&#x2019;" k="30" />
<hkern u1="&#xc1;" u2="&#x2018;" k="60" />
<hkern u1="&#xc1;" u2="&#x2014;" k="-10" />
<hkern u1="&#xc1;" u2="&#x2013;" k="-10" />
<hkern u1="&#xc1;" u2="&#x178;" k="54" />
<hkern u1="&#xc1;" u2="&#x153;" k="5" />
<hkern u1="&#xc1;" u2="&#x152;" k="20" />
<hkern u1="&#xc1;" u2="&#xe7;" k="5" />
<hkern u1="&#xc1;" u2="&#xe6;" k="5" />
<hkern u1="&#xc1;" u2="&#xdd;" k="54" />
<hkern u1="&#xc1;" u2="&#xd8;" k="20" />
<hkern u1="&#xc1;" u2="&#xd6;" k="20" />
<hkern u1="&#xc1;" u2="&#xd5;" k="20" />
<hkern u1="&#xc1;" u2="&#xd4;" k="20" />
<hkern u1="&#xc1;" u2="&#xd3;" k="20" />
<hkern u1="&#xc1;" u2="&#xd2;" k="20" />
<hkern u1="&#xc1;" u2="&#xc6;" k="-10" />
<hkern u1="&#xc1;" u2="&#xc5;" k="-10" />
<hkern u1="&#xc1;" u2="&#xc4;" k="-10" />
<hkern u1="&#xc1;" u2="&#xc3;" k="-10" />
<hkern u1="&#xc1;" u2="&#xc2;" k="-10" />
<hkern u1="&#xc1;" u2="&#xc1;" k="-10" />
<hkern u1="&#xc1;" u2="&#xc0;" k="-10" />
<hkern u1="&#xc1;" u2="&#xae;" k="10" />
<hkern u1="&#xc1;" u2="&#xa9;" k="10" />
<hkern u1="&#xc1;" u2="z" k="-10" />
<hkern u1="&#xc1;" u2="y" k="60" />
<hkern u1="&#xc1;" u2="x" k="-13" />
<hkern u1="&#xc1;" u2="w" k="40" />
<hkern u1="&#xc1;" u2="v" k="60" />
<hkern u1="&#xc1;" u2="u" k="5" />
<hkern u1="&#xc1;" u2="t" k="5" />
<hkern u1="&#xc1;" u2="s" k="-11" />
<hkern u1="&#xc1;" u2="r" k="5" />
<hkern u1="&#xc1;" u2="q" k="5" />
<hkern u1="&#xc1;" u2="p" k="5" />
<hkern u1="&#xc1;" u2="o" k="5" />
<hkern u1="&#xc1;" u2="n" k="5" />
<hkern u1="&#xc1;" u2="m" k="5" />
<hkern u1="&#xc1;" u2="g" k="11" />
<hkern u1="&#xc1;" u2="f" k="30" />
<hkern u1="&#xc1;" u2="e" k="5" />
<hkern u1="&#xc1;" u2="d" k="5" />
<hkern u1="&#xc1;" u2="c" k="5" />
<hkern u1="&#xc1;" u2="a" k="5" />
<hkern u1="&#xc1;" u2="Y" k="54" />
<hkern u1="&#xc1;" u2="X" k="-10" />
<hkern u1="&#xc1;" u2="W" k="61" />
<hkern u1="&#xc1;" u2="V" k="73" />
<hkern u1="&#xc1;" u2="T" k="59" />
<hkern u1="&#xc1;" u2="S" k="-5" />
<hkern u1="&#xc1;" u2="Q" k="20" />
<hkern u1="&#xc1;" u2="O" k="20" />
<hkern u1="&#xc1;" u2="J" k="-10" />
<hkern u1="&#xc1;" u2="G" k="20" />
<hkern u1="&#xc1;" u2="C" k="20" />
<hkern u1="&#xc1;" u2="A" k="-10" />
<hkern u1="&#xc1;" u2="&#x40;" k="10" />
<hkern u1="&#xc1;" u2="&#x3f;" k="23" />
<hkern u1="&#xc1;" u2="&#x2e;" k="-30" />
<hkern u1="&#xc1;" u2="&#x2d;" k="-10" />
<hkern u1="&#xc1;" u2="&#x2c;" k="-30" />
<hkern u1="&#xc1;" u2="&#x2a;" k="75" />
<hkern u1="&#xc2;" u2="&#x2122;" k="70" />
<hkern u1="&#xc2;" u2="&#x2026;" k="-30" />
<hkern u1="&#xc2;" u2="&#x201e;" k="-30" />
<hkern u1="&#xc2;" u2="&#x201d;" k="30" />
<hkern u1="&#xc2;" u2="&#x201c;" k="60" />
<hkern u1="&#xc2;" u2="&#x201a;" k="-30" />
<hkern u1="&#xc2;" u2="&#x2019;" k="30" />
<hkern u1="&#xc2;" u2="&#x2018;" k="60" />
<hkern u1="&#xc2;" u2="&#x2014;" k="-10" />
<hkern u1="&#xc2;" u2="&#x2013;" k="-10" />
<hkern u1="&#xc2;" u2="&#x178;" k="54" />
<hkern u1="&#xc2;" u2="&#x153;" k="5" />
<hkern u1="&#xc2;" u2="&#x152;" k="20" />
<hkern u1="&#xc2;" u2="&#xe7;" k="5" />
<hkern u1="&#xc2;" u2="&#xe6;" k="5" />
<hkern u1="&#xc2;" u2="&#xdd;" k="54" />
<hkern u1="&#xc2;" u2="&#xd8;" k="20" />
<hkern u1="&#xc2;" u2="&#xd6;" k="20" />
<hkern u1="&#xc2;" u2="&#xd5;" k="20" />
<hkern u1="&#xc2;" u2="&#xd4;" k="20" />
<hkern u1="&#xc2;" u2="&#xd3;" k="20" />
<hkern u1="&#xc2;" u2="&#xd2;" k="20" />
<hkern u1="&#xc2;" u2="&#xc6;" k="-10" />
<hkern u1="&#xc2;" u2="&#xc5;" k="-10" />
<hkern u1="&#xc2;" u2="&#xc4;" k="-10" />
<hkern u1="&#xc2;" u2="&#xc3;" k="-10" />
<hkern u1="&#xc2;" u2="&#xc2;" k="-10" />
<hkern u1="&#xc2;" u2="&#xc1;" k="-10" />
<hkern u1="&#xc2;" u2="&#xc0;" k="-10" />
<hkern u1="&#xc2;" u2="&#xae;" k="10" />
<hkern u1="&#xc2;" u2="&#xa9;" k="10" />
<hkern u1="&#xc2;" u2="z" k="-10" />
<hkern u1="&#xc2;" u2="y" k="60" />
<hkern u1="&#xc2;" u2="x" k="-13" />
<hkern u1="&#xc2;" u2="w" k="40" />
<hkern u1="&#xc2;" u2="v" k="60" />
<hkern u1="&#xc2;" u2="u" k="5" />
<hkern u1="&#xc2;" u2="t" k="5" />
<hkern u1="&#xc2;" u2="s" k="-11" />
<hkern u1="&#xc2;" u2="r" k="5" />
<hkern u1="&#xc2;" u2="q" k="5" />
<hkern u1="&#xc2;" u2="p" k="5" />
<hkern u1="&#xc2;" u2="o" k="5" />
<hkern u1="&#xc2;" u2="n" k="5" />
<hkern u1="&#xc2;" u2="m" k="5" />
<hkern u1="&#xc2;" u2="g" k="11" />
<hkern u1="&#xc2;" u2="f" k="30" />
<hkern u1="&#xc2;" u2="e" k="5" />
<hkern u1="&#xc2;" u2="d" k="5" />
<hkern u1="&#xc2;" u2="c" k="5" />
<hkern u1="&#xc2;" u2="a" k="5" />
<hkern u1="&#xc2;" u2="Y" k="54" />
<hkern u1="&#xc2;" u2="X" k="-10" />
<hkern u1="&#xc2;" u2="W" k="61" />
<hkern u1="&#xc2;" u2="V" k="73" />
<hkern u1="&#xc2;" u2="T" k="59" />
<hkern u1="&#xc2;" u2="S" k="-5" />
<hkern u1="&#xc2;" u2="Q" k="20" />
<hkern u1="&#xc2;" u2="O" k="20" />
<hkern u1="&#xc2;" u2="J" k="-10" />
<hkern u1="&#xc2;" u2="G" k="20" />
<hkern u1="&#xc2;" u2="C" k="20" />
<hkern u1="&#xc2;" u2="A" k="-10" />
<hkern u1="&#xc2;" u2="&#x40;" k="10" />
<hkern u1="&#xc2;" u2="&#x3f;" k="23" />
<hkern u1="&#xc2;" u2="&#x2e;" k="-30" />
<hkern u1="&#xc2;" u2="&#x2d;" k="-10" />
<hkern u1="&#xc2;" u2="&#x2c;" k="-30" />
<hkern u1="&#xc2;" u2="&#x2a;" k="75" />
<hkern u1="&#xc3;" u2="&#x2122;" k="70" />
<hkern u1="&#xc3;" u2="&#x2026;" k="-30" />
<hkern u1="&#xc3;" u2="&#x201e;" k="-30" />
<hkern u1="&#xc3;" u2="&#x201d;" k="30" />
<hkern u1="&#xc3;" u2="&#x201c;" k="60" />
<hkern u1="&#xc3;" u2="&#x201a;" k="-30" />
<hkern u1="&#xc3;" u2="&#x2019;" k="30" />
<hkern u1="&#xc3;" u2="&#x2018;" k="60" />
<hkern u1="&#xc3;" u2="&#x2014;" k="-10" />
<hkern u1="&#xc3;" u2="&#x2013;" k="-10" />
<hkern u1="&#xc3;" u2="&#x178;" k="54" />
<hkern u1="&#xc3;" u2="&#x153;" k="5" />
<hkern u1="&#xc3;" u2="&#x152;" k="20" />
<hkern u1="&#xc3;" u2="&#xe7;" k="5" />
<hkern u1="&#xc3;" u2="&#xe6;" k="5" />
<hkern u1="&#xc3;" u2="&#xdd;" k="54" />
<hkern u1="&#xc3;" u2="&#xd8;" k="20" />
<hkern u1="&#xc3;" u2="&#xd6;" k="20" />
<hkern u1="&#xc3;" u2="&#xd5;" k="20" />
<hkern u1="&#xc3;" u2="&#xd4;" k="20" />
<hkern u1="&#xc3;" u2="&#xd3;" k="20" />
<hkern u1="&#xc3;" u2="&#xd2;" k="20" />
<hkern u1="&#xc3;" u2="&#xc6;" k="-10" />
<hkern u1="&#xc3;" u2="&#xc5;" k="-10" />
<hkern u1="&#xc3;" u2="&#xc4;" k="-10" />
<hkern u1="&#xc3;" u2="&#xc3;" k="-10" />
<hkern u1="&#xc3;" u2="&#xc2;" k="-10" />
<hkern u1="&#xc3;" u2="&#xc1;" k="-10" />
<hkern u1="&#xc3;" u2="&#xc0;" k="-10" />
<hkern u1="&#xc3;" u2="&#xae;" k="10" />
<hkern u1="&#xc3;" u2="&#xa9;" k="10" />
<hkern u1="&#xc3;" u2="z" k="-10" />
<hkern u1="&#xc3;" u2="y" k="60" />
<hkern u1="&#xc3;" u2="x" k="-13" />
<hkern u1="&#xc3;" u2="w" k="40" />
<hkern u1="&#xc3;" u2="v" k="60" />
<hkern u1="&#xc3;" u2="u" k="5" />
<hkern u1="&#xc3;" u2="t" k="5" />
<hkern u1="&#xc3;" u2="s" k="-11" />
<hkern u1="&#xc3;" u2="r" k="5" />
<hkern u1="&#xc3;" u2="q" k="5" />
<hkern u1="&#xc3;" u2="p" k="5" />
<hkern u1="&#xc3;" u2="o" k="5" />
<hkern u1="&#xc3;" u2="n" k="5" />
<hkern u1="&#xc3;" u2="m" k="5" />
<hkern u1="&#xc3;" u2="g" k="11" />
<hkern u1="&#xc3;" u2="f" k="30" />
<hkern u1="&#xc3;" u2="e" k="5" />
<hkern u1="&#xc3;" u2="d" k="5" />
<hkern u1="&#xc3;" u2="c" k="5" />
<hkern u1="&#xc3;" u2="a" k="5" />
<hkern u1="&#xc3;" u2="Y" k="54" />
<hkern u1="&#xc3;" u2="X" k="-10" />
<hkern u1="&#xc3;" u2="W" k="61" />
<hkern u1="&#xc3;" u2="V" k="73" />
<hkern u1="&#xc3;" u2="T" k="59" />
<hkern u1="&#xc3;" u2="S" k="-5" />
<hkern u1="&#xc3;" u2="Q" k="20" />
<hkern u1="&#xc3;" u2="O" k="20" />
<hkern u1="&#xc3;" u2="J" k="-10" />
<hkern u1="&#xc3;" u2="G" k="20" />
<hkern u1="&#xc3;" u2="C" k="20" />
<hkern u1="&#xc3;" u2="A" k="-10" />
<hkern u1="&#xc3;" u2="&#x40;" k="10" />
<hkern u1="&#xc3;" u2="&#x3f;" k="23" />
<hkern u1="&#xc3;" u2="&#x2e;" k="-30" />
<hkern u1="&#xc3;" u2="&#x2d;" k="-10" />
<hkern u1="&#xc3;" u2="&#x2c;" k="-30" />
<hkern u1="&#xc3;" u2="&#x2a;" k="75" />
<hkern u1="&#xc4;" u2="&#x2122;" k="70" />
<hkern u1="&#xc4;" u2="&#x2026;" k="-30" />
<hkern u1="&#xc4;" u2="&#x201e;" k="-30" />
<hkern u1="&#xc4;" u2="&#x201d;" k="30" />
<hkern u1="&#xc4;" u2="&#x201c;" k="60" />
<hkern u1="&#xc4;" u2="&#x201a;" k="-30" />
<hkern u1="&#xc4;" u2="&#x2019;" k="30" />
<hkern u1="&#xc4;" u2="&#x2018;" k="60" />
<hkern u1="&#xc4;" u2="&#x2014;" k="-10" />
<hkern u1="&#xc4;" u2="&#x2013;" k="-10" />
<hkern u1="&#xc4;" u2="&#x178;" k="54" />
<hkern u1="&#xc4;" u2="&#x153;" k="5" />
<hkern u1="&#xc4;" u2="&#x152;" k="20" />
<hkern u1="&#xc4;" u2="&#xe7;" k="5" />
<hkern u1="&#xc4;" u2="&#xe6;" k="5" />
<hkern u1="&#xc4;" u2="&#xdd;" k="54" />
<hkern u1="&#xc4;" u2="&#xd8;" k="20" />
<hkern u1="&#xc4;" u2="&#xd6;" k="20" />
<hkern u1="&#xc4;" u2="&#xd5;" k="20" />
<hkern u1="&#xc4;" u2="&#xd4;" k="20" />
<hkern u1="&#xc4;" u2="&#xd3;" k="20" />
<hkern u1="&#xc4;" u2="&#xd2;" k="20" />
<hkern u1="&#xc4;" u2="&#xc6;" k="-10" />
<hkern u1="&#xc4;" u2="&#xc5;" k="-10" />
<hkern u1="&#xc4;" u2="&#xc4;" k="-10" />
<hkern u1="&#xc4;" u2="&#xc3;" k="-10" />
<hkern u1="&#xc4;" u2="&#xc2;" k="-10" />
<hkern u1="&#xc4;" u2="&#xc1;" k="-10" />
<hkern u1="&#xc4;" u2="&#xc0;" k="-10" />
<hkern u1="&#xc4;" u2="&#xae;" k="10" />
<hkern u1="&#xc4;" u2="&#xa9;" k="10" />
<hkern u1="&#xc4;" u2="z" k="-10" />
<hkern u1="&#xc4;" u2="y" k="60" />
<hkern u1="&#xc4;" u2="x" k="-13" />
<hkern u1="&#xc4;" u2="w" k="40" />
<hkern u1="&#xc4;" u2="v" k="60" />
<hkern u1="&#xc4;" u2="u" k="5" />
<hkern u1="&#xc4;" u2="t" k="5" />
<hkern u1="&#xc4;" u2="s" k="-11" />
<hkern u1="&#xc4;" u2="r" k="5" />
<hkern u1="&#xc4;" u2="q" k="5" />
<hkern u1="&#xc4;" u2="p" k="5" />
<hkern u1="&#xc4;" u2="o" k="5" />
<hkern u1="&#xc4;" u2="n" k="5" />
<hkern u1="&#xc4;" u2="m" k="5" />
<hkern u1="&#xc4;" u2="g" k="11" />
<hkern u1="&#xc4;" u2="f" k="30" />
<hkern u1="&#xc4;" u2="e" k="5" />
<hkern u1="&#xc4;" u2="d" k="5" />
<hkern u1="&#xc4;" u2="c" k="5" />
<hkern u1="&#xc4;" u2="a" k="5" />
<hkern u1="&#xc4;" u2="Y" k="54" />
<hkern u1="&#xc4;" u2="X" k="-10" />
<hkern u1="&#xc4;" u2="W" k="61" />
<hkern u1="&#xc4;" u2="V" k="73" />
<hkern u1="&#xc4;" u2="T" k="59" />
<hkern u1="&#xc4;" u2="S" k="-5" />
<hkern u1="&#xc4;" u2="Q" k="20" />
<hkern u1="&#xc4;" u2="O" k="20" />
<hkern u1="&#xc4;" u2="J" k="-10" />
<hkern u1="&#xc4;" u2="G" k="20" />
<hkern u1="&#xc4;" u2="C" k="20" />
<hkern u1="&#xc4;" u2="A" k="-10" />
<hkern u1="&#xc4;" u2="&#x40;" k="10" />
<hkern u1="&#xc4;" u2="&#x3f;" k="23" />
<hkern u1="&#xc4;" u2="&#x2e;" k="-30" />
<hkern u1="&#xc4;" u2="&#x2d;" k="-10" />
<hkern u1="&#xc4;" u2="&#x2c;" k="-30" />
<hkern u1="&#xc4;" u2="&#x2a;" k="75" />
<hkern u1="&#xc5;" u2="&#x2122;" k="70" />
<hkern u1="&#xc5;" u2="&#x2026;" k="-30" />
<hkern u1="&#xc5;" u2="&#x201e;" k="-30" />
<hkern u1="&#xc5;" u2="&#x201d;" k="30" />
<hkern u1="&#xc5;" u2="&#x201c;" k="60" />
<hkern u1="&#xc5;" u2="&#x201a;" k="-30" />
<hkern u1="&#xc5;" u2="&#x2019;" k="30" />
<hkern u1="&#xc5;" u2="&#x2018;" k="60" />
<hkern u1="&#xc5;" u2="&#x2014;" k="-10" />
<hkern u1="&#xc5;" u2="&#x2013;" k="-10" />
<hkern u1="&#xc5;" u2="&#x178;" k="54" />
<hkern u1="&#xc5;" u2="&#x153;" k="5" />
<hkern u1="&#xc5;" u2="&#x152;" k="20" />
<hkern u1="&#xc5;" u2="&#xe7;" k="5" />
<hkern u1="&#xc5;" u2="&#xe6;" k="5" />
<hkern u1="&#xc5;" u2="&#xdd;" k="54" />
<hkern u1="&#xc5;" u2="&#xd8;" k="20" />
<hkern u1="&#xc5;" u2="&#xd6;" k="20" />
<hkern u1="&#xc5;" u2="&#xd5;" k="20" />
<hkern u1="&#xc5;" u2="&#xd4;" k="20" />
<hkern u1="&#xc5;" u2="&#xd3;" k="20" />
<hkern u1="&#xc5;" u2="&#xd2;" k="20" />
<hkern u1="&#xc5;" u2="&#xc6;" k="-10" />
<hkern u1="&#xc5;" u2="&#xc5;" k="-10" />
<hkern u1="&#xc5;" u2="&#xc4;" k="-10" />
<hkern u1="&#xc5;" u2="&#xc3;" k="-10" />
<hkern u1="&#xc5;" u2="&#xc2;" k="-10" />
<hkern u1="&#xc5;" u2="&#xc1;" k="-10" />
<hkern u1="&#xc5;" u2="&#xc0;" k="-10" />
<hkern u1="&#xc5;" u2="&#xae;" k="10" />
<hkern u1="&#xc5;" u2="&#xa9;" k="10" />
<hkern u1="&#xc5;" u2="z" k="-10" />
<hkern u1="&#xc5;" u2="y" k="60" />
<hkern u1="&#xc5;" u2="x" k="-13" />
<hkern u1="&#xc5;" u2="w" k="40" />
<hkern u1="&#xc5;" u2="v" k="60" />
<hkern u1="&#xc5;" u2="u" k="5" />
<hkern u1="&#xc5;" u2="t" k="5" />
<hkern u1="&#xc5;" u2="s" k="-11" />
<hkern u1="&#xc5;" u2="r" k="5" />
<hkern u1="&#xc5;" u2="q" k="5" />
<hkern u1="&#xc5;" u2="p" k="5" />
<hkern u1="&#xc5;" u2="o" k="5" />
<hkern u1="&#xc5;" u2="n" k="5" />
<hkern u1="&#xc5;" u2="m" k="5" />
<hkern u1="&#xc5;" u2="g" k="11" />
<hkern u1="&#xc5;" u2="f" k="30" />
<hkern u1="&#xc5;" u2="e" k="5" />
<hkern u1="&#xc5;" u2="d" k="5" />
<hkern u1="&#xc5;" u2="c" k="5" />
<hkern u1="&#xc5;" u2="a" k="5" />
<hkern u1="&#xc5;" u2="Y" k="54" />
<hkern u1="&#xc5;" u2="X" k="-10" />
<hkern u1="&#xc5;" u2="W" k="61" />
<hkern u1="&#xc5;" u2="V" k="73" />
<hkern u1="&#xc5;" u2="T" k="59" />
<hkern u1="&#xc5;" u2="S" k="-5" />
<hkern u1="&#xc5;" u2="Q" k="20" />
<hkern u1="&#xc5;" u2="O" k="20" />
<hkern u1="&#xc5;" u2="J" k="-10" />
<hkern u1="&#xc5;" u2="G" k="20" />
<hkern u1="&#xc5;" u2="C" k="20" />
<hkern u1="&#xc5;" u2="A" k="-10" />
<hkern u1="&#xc5;" u2="&#x40;" k="10" />
<hkern u1="&#xc5;" u2="&#x3f;" k="23" />
<hkern u1="&#xc5;" u2="&#x2e;" k="-30" />
<hkern u1="&#xc5;" u2="&#x2d;" k="-10" />
<hkern u1="&#xc5;" u2="&#x2c;" k="-30" />
<hkern u1="&#xc5;" u2="&#x2a;" k="75" />
<hkern u1="&#xc6;" u2="&#x2039;" k="15" />
<hkern u1="&#xc6;" u2="&#x153;" k="21" />
<hkern u1="&#xc6;" u2="&#x152;" k="19" />
<hkern u1="&#xc6;" u2="&#xfe;" k="4" />
<hkern u1="&#xc6;" u2="&#xe7;" k="21" />
<hkern u1="&#xc6;" u2="&#xe6;" k="21" />
<hkern u1="&#xc6;" u2="&#xd8;" k="19" />
<hkern u1="&#xc6;" u2="&#xd6;" k="19" />
<hkern u1="&#xc6;" u2="&#xd5;" k="19" />
<hkern u1="&#xc6;" u2="&#xd4;" k="19" />
<hkern u1="&#xc6;" u2="&#xd3;" k="19" />
<hkern u1="&#xc6;" u2="&#xd2;" k="19" />
<hkern u1="&#xc6;" u2="&#xae;" k="20" />
<hkern u1="&#xc6;" u2="&#xab;" k="15" />
<hkern u1="&#xc6;" u2="&#xa9;" k="20" />
<hkern u1="&#xc6;" u2="y" k="20" />
<hkern u1="&#xc6;" u2="v" k="20" />
<hkern u1="&#xc6;" u2="u" k="8" />
<hkern u1="&#xc6;" u2="r" k="8" />
<hkern u1="&#xc6;" u2="q" k="21" />
<hkern u1="&#xc6;" u2="p" k="8" />
<hkern u1="&#xc6;" u2="o" k="21" />
<hkern u1="&#xc6;" u2="n" k="8" />
<hkern u1="&#xc6;" u2="m" k="8" />
<hkern u1="&#xc6;" u2="l" k="4" />
<hkern u1="&#xc6;" u2="k" k="4" />
<hkern u1="&#xc6;" u2="h" k="4" />
<hkern u1="&#xc6;" u2="g" k="16" />
<hkern u1="&#xc6;" u2="f" k="10" />
<hkern u1="&#xc6;" u2="e" k="21" />
<hkern u1="&#xc6;" u2="d" k="21" />
<hkern u1="&#xc6;" u2="c" k="21" />
<hkern u1="&#xc6;" u2="b" k="4" />
<hkern u1="&#xc6;" u2="a" k="21" />
<hkern u1="&#xc6;" u2="W" k="4" />
<hkern u1="&#xc6;" u2="V" k="-8" />
<hkern u1="&#xc6;" u2="T" k="-8" />
<hkern u1="&#xc6;" u2="Q" k="19" />
<hkern u1="&#xc6;" u2="O" k="19" />
<hkern u1="&#xc6;" u2="J" k="-4" />
<hkern u1="&#xc6;" u2="G" k="19" />
<hkern u1="&#xc6;" u2="C" k="19" />
<hkern u1="&#xc6;" u2="&#x40;" k="20" />
<hkern u1="&#xc8;" u2="&#x2039;" k="15" />
<hkern u1="&#xc8;" u2="&#x153;" k="21" />
<hkern u1="&#xc8;" u2="&#x152;" k="19" />
<hkern u1="&#xc8;" u2="&#xfe;" k="4" />
<hkern u1="&#xc8;" u2="&#xe7;" k="21" />
<hkern u1="&#xc8;" u2="&#xe6;" k="21" />
<hkern u1="&#xc8;" u2="&#xd8;" k="19" />
<hkern u1="&#xc8;" u2="&#xd6;" k="19" />
<hkern u1="&#xc8;" u2="&#xd5;" k="19" />
<hkern u1="&#xc8;" u2="&#xd4;" k="19" />
<hkern u1="&#xc8;" u2="&#xd3;" k="19" />
<hkern u1="&#xc8;" u2="&#xd2;" k="19" />
<hkern u1="&#xc8;" u2="&#xae;" k="20" />
<hkern u1="&#xc8;" u2="&#xab;" k="15" />
<hkern u1="&#xc8;" u2="&#xa9;" k="20" />
<hkern u1="&#xc8;" u2="y" k="20" />
<hkern u1="&#xc8;" u2="v" k="20" />
<hkern u1="&#xc8;" u2="u" k="8" />
<hkern u1="&#xc8;" u2="r" k="8" />
<hkern u1="&#xc8;" u2="q" k="21" />
<hkern u1="&#xc8;" u2="p" k="8" />
<hkern u1="&#xc8;" u2="o" k="21" />
<hkern u1="&#xc8;" u2="n" k="8" />
<hkern u1="&#xc8;" u2="m" k="8" />
<hkern u1="&#xc8;" u2="l" k="4" />
<hkern u1="&#xc8;" u2="k" k="4" />
<hkern u1="&#xc8;" u2="h" k="4" />
<hkern u1="&#xc8;" u2="g" k="16" />
<hkern u1="&#xc8;" u2="f" k="10" />
<hkern u1="&#xc8;" u2="e" k="21" />
<hkern u1="&#xc8;" u2="d" k="21" />
<hkern u1="&#xc8;" u2="c" k="21" />
<hkern u1="&#xc8;" u2="b" k="4" />
<hkern u1="&#xc8;" u2="a" k="21" />
<hkern u1="&#xc8;" u2="W" k="4" />
<hkern u1="&#xc8;" u2="V" k="-8" />
<hkern u1="&#xc8;" u2="T" k="-8" />
<hkern u1="&#xc8;" u2="Q" k="19" />
<hkern u1="&#xc8;" u2="O" k="19" />
<hkern u1="&#xc8;" u2="J" k="-4" />
<hkern u1="&#xc8;" u2="G" k="19" />
<hkern u1="&#xc8;" u2="C" k="19" />
<hkern u1="&#xc8;" u2="&#x40;" k="20" />
<hkern u1="&#xc9;" u2="&#x2039;" k="15" />
<hkern u1="&#xc9;" u2="&#x153;" k="21" />
<hkern u1="&#xc9;" u2="&#x152;" k="19" />
<hkern u1="&#xc9;" u2="&#xfe;" k="4" />
<hkern u1="&#xc9;" u2="&#xe7;" k="21" />
<hkern u1="&#xc9;" u2="&#xe6;" k="21" />
<hkern u1="&#xc9;" u2="&#xd8;" k="19" />
<hkern u1="&#xc9;" u2="&#xd6;" k="19" />
<hkern u1="&#xc9;" u2="&#xd5;" k="19" />
<hkern u1="&#xc9;" u2="&#xd4;" k="19" />
<hkern u1="&#xc9;" u2="&#xd3;" k="19" />
<hkern u1="&#xc9;" u2="&#xd2;" k="19" />
<hkern u1="&#xc9;" u2="&#xae;" k="20" />
<hkern u1="&#xc9;" u2="&#xab;" k="15" />
<hkern u1="&#xc9;" u2="&#xa9;" k="20" />
<hkern u1="&#xc9;" u2="y" k="20" />
<hkern u1="&#xc9;" u2="v" k="20" />
<hkern u1="&#xc9;" u2="u" k="8" />
<hkern u1="&#xc9;" u2="r" k="8" />
<hkern u1="&#xc9;" u2="q" k="21" />
<hkern u1="&#xc9;" u2="p" k="8" />
<hkern u1="&#xc9;" u2="o" k="21" />
<hkern u1="&#xc9;" u2="n" k="8" />
<hkern u1="&#xc9;" u2="m" k="8" />
<hkern u1="&#xc9;" u2="l" k="4" />
<hkern u1="&#xc9;" u2="k" k="4" />
<hkern u1="&#xc9;" u2="h" k="4" />
<hkern u1="&#xc9;" u2="g" k="16" />
<hkern u1="&#xc9;" u2="f" k="10" />
<hkern u1="&#xc9;" u2="e" k="21" />
<hkern u1="&#xc9;" u2="d" k="21" />
<hkern u1="&#xc9;" u2="c" k="21" />
<hkern u1="&#xc9;" u2="b" k="4" />
<hkern u1="&#xc9;" u2="a" k="21" />
<hkern u1="&#xc9;" u2="W" k="4" />
<hkern u1="&#xc9;" u2="V" k="-8" />
<hkern u1="&#xc9;" u2="T" k="-8" />
<hkern u1="&#xc9;" u2="Q" k="19" />
<hkern u1="&#xc9;" u2="O" k="19" />
<hkern u1="&#xc9;" u2="J" k="-4" />
<hkern u1="&#xc9;" u2="G" k="19" />
<hkern u1="&#xc9;" u2="C" k="19" />
<hkern u1="&#xc9;" u2="&#x40;" k="20" />
<hkern u1="&#xca;" u2="&#x2039;" k="15" />
<hkern u1="&#xca;" u2="&#x153;" k="21" />
<hkern u1="&#xca;" u2="&#x152;" k="19" />
<hkern u1="&#xca;" u2="&#xfe;" k="4" />
<hkern u1="&#xca;" u2="&#xe7;" k="21" />
<hkern u1="&#xca;" u2="&#xe6;" k="21" />
<hkern u1="&#xca;" u2="&#xd8;" k="19" />
<hkern u1="&#xca;" u2="&#xd6;" k="19" />
<hkern u1="&#xca;" u2="&#xd5;" k="19" />
<hkern u1="&#xca;" u2="&#xd4;" k="19" />
<hkern u1="&#xca;" u2="&#xd3;" k="19" />
<hkern u1="&#xca;" u2="&#xd2;" k="19" />
<hkern u1="&#xca;" u2="&#xae;" k="20" />
<hkern u1="&#xca;" u2="&#xab;" k="15" />
<hkern u1="&#xca;" u2="&#xa9;" k="20" />
<hkern u1="&#xca;" u2="y" k="20" />
<hkern u1="&#xca;" u2="v" k="20" />
<hkern u1="&#xca;" u2="u" k="8" />
<hkern u1="&#xca;" u2="r" k="8" />
<hkern u1="&#xca;" u2="q" k="21" />
<hkern u1="&#xca;" u2="p" k="8" />
<hkern u1="&#xca;" u2="o" k="21" />
<hkern u1="&#xca;" u2="n" k="8" />
<hkern u1="&#xca;" u2="m" k="8" />
<hkern u1="&#xca;" u2="l" k="4" />
<hkern u1="&#xca;" u2="k" k="4" />
<hkern u1="&#xca;" u2="h" k="4" />
<hkern u1="&#xca;" u2="g" k="16" />
<hkern u1="&#xca;" u2="f" k="10" />
<hkern u1="&#xca;" u2="e" k="21" />
<hkern u1="&#xca;" u2="d" k="21" />
<hkern u1="&#xca;" u2="c" k="21" />
<hkern u1="&#xca;" u2="b" k="4" />
<hkern u1="&#xca;" u2="a" k="21" />
<hkern u1="&#xca;" u2="W" k="4" />
<hkern u1="&#xca;" u2="V" k="-8" />
<hkern u1="&#xca;" u2="T" k="-8" />
<hkern u1="&#xca;" u2="Q" k="19" />
<hkern u1="&#xca;" u2="O" k="19" />
<hkern u1="&#xca;" u2="J" k="-4" />
<hkern u1="&#xca;" u2="G" k="19" />
<hkern u1="&#xca;" u2="C" k="19" />
<hkern u1="&#xca;" u2="&#x40;" k="20" />
<hkern u1="&#xcb;" u2="&#x2039;" k="15" />
<hkern u1="&#xcb;" u2="&#x153;" k="21" />
<hkern u1="&#xcb;" u2="&#x152;" k="19" />
<hkern u1="&#xcb;" u2="&#xfe;" k="4" />
<hkern u1="&#xcb;" u2="&#xe7;" k="21" />
<hkern u1="&#xcb;" u2="&#xe6;" k="21" />
<hkern u1="&#xcb;" u2="&#xd8;" k="19" />
<hkern u1="&#xcb;" u2="&#xd6;" k="19" />
<hkern u1="&#xcb;" u2="&#xd5;" k="19" />
<hkern u1="&#xcb;" u2="&#xd4;" k="19" />
<hkern u1="&#xcb;" u2="&#xd3;" k="19" />
<hkern u1="&#xcb;" u2="&#xd2;" k="19" />
<hkern u1="&#xcb;" u2="&#xae;" k="20" />
<hkern u1="&#xcb;" u2="&#xab;" k="15" />
<hkern u1="&#xcb;" u2="&#xa9;" k="20" />
<hkern u1="&#xcb;" u2="y" k="20" />
<hkern u1="&#xcb;" u2="v" k="20" />
<hkern u1="&#xcb;" u2="u" k="8" />
<hkern u1="&#xcb;" u2="r" k="8" />
<hkern u1="&#xcb;" u2="q" k="21" />
<hkern u1="&#xcb;" u2="p" k="8" />
<hkern u1="&#xcb;" u2="o" k="21" />
<hkern u1="&#xcb;" u2="n" k="8" />
<hkern u1="&#xcb;" u2="m" k="8" />
<hkern u1="&#xcb;" u2="l" k="4" />
<hkern u1="&#xcb;" u2="k" k="4" />
<hkern u1="&#xcb;" u2="h" k="4" />
<hkern u1="&#xcb;" u2="g" k="16" />
<hkern u1="&#xcb;" u2="f" k="10" />
<hkern u1="&#xcb;" u2="e" k="21" />
<hkern u1="&#xcb;" u2="d" k="21" />
<hkern u1="&#xcb;" u2="c" k="21" />
<hkern u1="&#xcb;" u2="b" k="4" />
<hkern u1="&#xcb;" u2="a" k="21" />
<hkern u1="&#xcb;" u2="W" k="4" />
<hkern u1="&#xcb;" u2="V" k="-8" />
<hkern u1="&#xcb;" u2="T" k="-8" />
<hkern u1="&#xcb;" u2="Q" k="19" />
<hkern u1="&#xcb;" u2="O" k="19" />
<hkern u1="&#xcb;" u2="J" k="-4" />
<hkern u1="&#xcb;" u2="G" k="19" />
<hkern u1="&#xcb;" u2="C" k="19" />
<hkern u1="&#xcb;" u2="&#x40;" k="20" />
<hkern u1="&#xcc;" u2="y" k="10" />
<hkern u1="&#xcc;" u2="v" k="10" />
<hkern u1="&#xcc;" u2="&#x2f;" k="20" />
<hkern u1="&#xcd;" u2="y" k="10" />
<hkern u1="&#xcd;" u2="v" k="10" />
<hkern u1="&#xcd;" u2="&#x2f;" k="20" />
<hkern u1="&#xce;" u2="y" k="10" />
<hkern u1="&#xce;" u2="v" k="10" />
<hkern u1="&#xce;" u2="&#x2f;" k="20" />
<hkern u1="&#xcf;" u2="y" k="10" />
<hkern u1="&#xcf;" u2="v" k="10" />
<hkern u1="&#xcf;" u2="&#x2f;" k="20" />
<hkern u1="&#xd0;" u2="&#x2026;" k="33" />
<hkern u1="&#xd0;" u2="&#x201e;" k="33" />
<hkern u1="&#xd0;" u2="&#x201c;" k="20" />
<hkern u1="&#xd0;" u2="&#x201a;" k="33" />
<hkern u1="&#xd0;" u2="&#x2018;" k="20" />
<hkern u1="&#xd0;" u2="&#x178;" k="18" />
<hkern u1="&#xd0;" u2="&#x153;" k="1" />
<hkern u1="&#xd0;" u2="&#xe7;" k="1" />
<hkern u1="&#xd0;" u2="&#xe6;" k="1" />
<hkern u1="&#xd0;" u2="&#xdd;" k="18" />
<hkern u1="&#xd0;" u2="&#xc6;" k="20" />
<hkern u1="&#xd0;" u2="&#xc5;" k="20" />
<hkern u1="&#xd0;" u2="&#xc4;" k="20" />
<hkern u1="&#xd0;" u2="&#xc3;" k="20" />
<hkern u1="&#xd0;" u2="&#xc2;" k="20" />
<hkern u1="&#xd0;" u2="&#xc1;" k="20" />
<hkern u1="&#xd0;" u2="&#xc0;" k="20" />
<hkern u1="&#xd0;" u2="z" k="10" />
<hkern u1="&#xd0;" u2="x" k="10" />
<hkern u1="&#xd0;" u2="u" k="1" />
<hkern u1="&#xd0;" u2="r" k="1" />
<hkern u1="&#xd0;" u2="q" k="1" />
<hkern u1="&#xd0;" u2="p" k="1" />
<hkern u1="&#xd0;" u2="o" k="1" />
<hkern u1="&#xd0;" u2="n" k="1" />
<hkern u1="&#xd0;" u2="m" k="1" />
<hkern u1="&#xd0;" u2="l" k="3" />
<hkern u1="&#xd0;" u2="k" k="3" />
<hkern u1="&#xd0;" u2="h" k="3" />
<hkern u1="&#xd0;" u2="e" k="1" />
<hkern u1="&#xd0;" u2="d" k="1" />
<hkern u1="&#xd0;" u2="c" k="1" />
<hkern u1="&#xd0;" u2="b" k="3" />
<hkern u1="&#xd0;" u2="a" k="1" />
<hkern u1="&#xd0;" u2="Z" k="19" />
<hkern u1="&#xd0;" u2="Y" k="18" />
<hkern u1="&#xd0;" u2="X" k="29" />
<hkern u1="&#xd0;" u2="W" k="33" />
<hkern u1="&#xd0;" u2="V" k="20" />
<hkern u1="&#xd0;" u2="T" k="19" />
<hkern u1="&#xd0;" u2="J" k="23" />
<hkern u1="&#xd0;" u2="A" k="20" />
<hkern u1="&#xd0;" u2="&#x3f;" k="20" />
<hkern u1="&#xd0;" u2="&#x2f;" k="50" />
<hkern u1="&#xd0;" u2="&#x2e;" k="33" />
<hkern u1="&#xd0;" u2="&#x2c;" k="33" />
<hkern u1="&#xd1;" u2="y" k="10" />
<hkern u1="&#xd1;" u2="v" k="10" />
<hkern u1="&#xd1;" u2="&#x2f;" k="20" />
<hkern u1="&#xd2;" u2="&#x2026;" k="33" />
<hkern u1="&#xd2;" u2="&#x201e;" k="33" />
<hkern u1="&#xd2;" u2="&#x201c;" k="20" />
<hkern u1="&#xd2;" u2="&#x201a;" k="33" />
<hkern u1="&#xd2;" u2="&#x2018;" k="20" />
<hkern u1="&#xd2;" u2="&#x178;" k="18" />
<hkern u1="&#xd2;" u2="&#x153;" k="1" />
<hkern u1="&#xd2;" u2="&#xe7;" k="1" />
<hkern u1="&#xd2;" u2="&#xe6;" k="1" />
<hkern u1="&#xd2;" u2="&#xdd;" k="18" />
<hkern u1="&#xd2;" u2="&#xc6;" k="20" />
<hkern u1="&#xd2;" u2="&#xc5;" k="20" />
<hkern u1="&#xd2;" u2="&#xc4;" k="20" />
<hkern u1="&#xd2;" u2="&#xc3;" k="20" />
<hkern u1="&#xd2;" u2="&#xc2;" k="20" />
<hkern u1="&#xd2;" u2="&#xc1;" k="20" />
<hkern u1="&#xd2;" u2="&#xc0;" k="20" />
<hkern u1="&#xd2;" u2="z" k="10" />
<hkern u1="&#xd2;" u2="x" k="10" />
<hkern u1="&#xd2;" u2="u" k="1" />
<hkern u1="&#xd2;" u2="r" k="1" />
<hkern u1="&#xd2;" u2="q" k="1" />
<hkern u1="&#xd2;" u2="p" k="1" />
<hkern u1="&#xd2;" u2="o" k="1" />
<hkern u1="&#xd2;" u2="n" k="1" />
<hkern u1="&#xd2;" u2="m" k="1" />
<hkern u1="&#xd2;" u2="l" k="3" />
<hkern u1="&#xd2;" u2="k" k="3" />
<hkern u1="&#xd2;" u2="h" k="3" />
<hkern u1="&#xd2;" u2="e" k="1" />
<hkern u1="&#xd2;" u2="d" k="1" />
<hkern u1="&#xd2;" u2="c" k="1" />
<hkern u1="&#xd2;" u2="b" k="3" />
<hkern u1="&#xd2;" u2="a" k="1" />
<hkern u1="&#xd2;" u2="Z" k="19" />
<hkern u1="&#xd2;" u2="Y" k="18" />
<hkern u1="&#xd2;" u2="X" k="29" />
<hkern u1="&#xd2;" u2="W" k="33" />
<hkern u1="&#xd2;" u2="V" k="20" />
<hkern u1="&#xd2;" u2="T" k="19" />
<hkern u1="&#xd2;" u2="J" k="23" />
<hkern u1="&#xd2;" u2="A" k="20" />
<hkern u1="&#xd2;" u2="&#x3f;" k="20" />
<hkern u1="&#xd2;" u2="&#x2f;" k="50" />
<hkern u1="&#xd2;" u2="&#x2e;" k="33" />
<hkern u1="&#xd2;" u2="&#x2c;" k="33" />
<hkern u1="&#xd3;" u2="&#x2026;" k="33" />
<hkern u1="&#xd3;" u2="&#x201e;" k="33" />
<hkern u1="&#xd3;" u2="&#x201c;" k="20" />
<hkern u1="&#xd3;" u2="&#x201a;" k="33" />
<hkern u1="&#xd3;" u2="&#x2018;" k="20" />
<hkern u1="&#xd3;" u2="&#x178;" k="18" />
<hkern u1="&#xd3;" u2="&#x153;" k="1" />
<hkern u1="&#xd3;" u2="&#xe7;" k="1" />
<hkern u1="&#xd3;" u2="&#xe6;" k="1" />
<hkern u1="&#xd3;" u2="&#xdd;" k="18" />
<hkern u1="&#xd3;" u2="&#xc6;" k="20" />
<hkern u1="&#xd3;" u2="&#xc5;" k="20" />
<hkern u1="&#xd3;" u2="&#xc4;" k="20" />
<hkern u1="&#xd3;" u2="&#xc3;" k="20" />
<hkern u1="&#xd3;" u2="&#xc2;" k="20" />
<hkern u1="&#xd3;" u2="&#xc1;" k="20" />
<hkern u1="&#xd3;" u2="&#xc0;" k="20" />
<hkern u1="&#xd3;" u2="z" k="10" />
<hkern u1="&#xd3;" u2="x" k="10" />
<hkern u1="&#xd3;" u2="u" k="1" />
<hkern u1="&#xd3;" u2="r" k="1" />
<hkern u1="&#xd3;" u2="q" k="1" />
<hkern u1="&#xd3;" u2="p" k="1" />
<hkern u1="&#xd3;" u2="o" k="1" />
<hkern u1="&#xd3;" u2="n" k="1" />
<hkern u1="&#xd3;" u2="m" k="1" />
<hkern u1="&#xd3;" u2="l" k="3" />
<hkern u1="&#xd3;" u2="k" k="3" />
<hkern u1="&#xd3;" u2="h" k="3" />
<hkern u1="&#xd3;" u2="e" k="1" />
<hkern u1="&#xd3;" u2="d" k="1" />
<hkern u1="&#xd3;" u2="c" k="1" />
<hkern u1="&#xd3;" u2="b" k="3" />
<hkern u1="&#xd3;" u2="a" k="1" />
<hkern u1="&#xd3;" u2="Z" k="19" />
<hkern u1="&#xd3;" u2="Y" k="18" />
<hkern u1="&#xd3;" u2="X" k="29" />
<hkern u1="&#xd3;" u2="W" k="33" />
<hkern u1="&#xd3;" u2="V" k="20" />
<hkern u1="&#xd3;" u2="T" k="19" />
<hkern u1="&#xd3;" u2="J" k="23" />
<hkern u1="&#xd3;" u2="A" k="20" />
<hkern u1="&#xd3;" u2="&#x3f;" k="20" />
<hkern u1="&#xd3;" u2="&#x2f;" k="50" />
<hkern u1="&#xd3;" u2="&#x2e;" k="33" />
<hkern u1="&#xd3;" u2="&#x2c;" k="33" />
<hkern u1="&#xd4;" u2="&#x2026;" k="33" />
<hkern u1="&#xd4;" u2="&#x201e;" k="33" />
<hkern u1="&#xd4;" u2="&#x201c;" k="20" />
<hkern u1="&#xd4;" u2="&#x201a;" k="33" />
<hkern u1="&#xd4;" u2="&#x2018;" k="20" />
<hkern u1="&#xd4;" u2="&#x178;" k="18" />
<hkern u1="&#xd4;" u2="&#x153;" k="1" />
<hkern u1="&#xd4;" u2="&#xe7;" k="1" />
<hkern u1="&#xd4;" u2="&#xe6;" k="1" />
<hkern u1="&#xd4;" u2="&#xdd;" k="18" />
<hkern u1="&#xd4;" u2="&#xc6;" k="20" />
<hkern u1="&#xd4;" u2="&#xc5;" k="20" />
<hkern u1="&#xd4;" u2="&#xc4;" k="20" />
<hkern u1="&#xd4;" u2="&#xc3;" k="20" />
<hkern u1="&#xd4;" u2="&#xc2;" k="20" />
<hkern u1="&#xd4;" u2="&#xc1;" k="20" />
<hkern u1="&#xd4;" u2="&#xc0;" k="20" />
<hkern u1="&#xd4;" u2="z" k="10" />
<hkern u1="&#xd4;" u2="x" k="10" />
<hkern u1="&#xd4;" u2="u" k="1" />
<hkern u1="&#xd4;" u2="r" k="1" />
<hkern u1="&#xd4;" u2="q" k="1" />
<hkern u1="&#xd4;" u2="p" k="1" />
<hkern u1="&#xd4;" u2="o" k="1" />
<hkern u1="&#xd4;" u2="n" k="1" />
<hkern u1="&#xd4;" u2="m" k="1" />
<hkern u1="&#xd4;" u2="l" k="3" />
<hkern u1="&#xd4;" u2="k" k="3" />
<hkern u1="&#xd4;" u2="h" k="3" />
<hkern u1="&#xd4;" u2="e" k="1" />
<hkern u1="&#xd4;" u2="d" k="1" />
<hkern u1="&#xd4;" u2="c" k="1" />
<hkern u1="&#xd4;" u2="b" k="3" />
<hkern u1="&#xd4;" u2="a" k="1" />
<hkern u1="&#xd4;" u2="Z" k="19" />
<hkern u1="&#xd4;" u2="Y" k="18" />
<hkern u1="&#xd4;" u2="X" k="29" />
<hkern u1="&#xd4;" u2="W" k="33" />
<hkern u1="&#xd4;" u2="V" k="20" />
<hkern u1="&#xd4;" u2="T" k="19" />
<hkern u1="&#xd4;" u2="J" k="23" />
<hkern u1="&#xd4;" u2="A" k="20" />
<hkern u1="&#xd4;" u2="&#x3f;" k="20" />
<hkern u1="&#xd4;" u2="&#x2f;" k="50" />
<hkern u1="&#xd4;" u2="&#x2e;" k="33" />
<hkern u1="&#xd4;" u2="&#x2c;" k="33" />
<hkern u1="&#xd5;" u2="&#x2026;" k="33" />
<hkern u1="&#xd5;" u2="&#x201e;" k="33" />
<hkern u1="&#xd5;" u2="&#x201c;" k="20" />
<hkern u1="&#xd5;" u2="&#x201a;" k="33" />
<hkern u1="&#xd5;" u2="&#x2018;" k="20" />
<hkern u1="&#xd5;" u2="&#x178;" k="18" />
<hkern u1="&#xd5;" u2="&#x153;" k="1" />
<hkern u1="&#xd5;" u2="&#xe7;" k="1" />
<hkern u1="&#xd5;" u2="&#xe6;" k="1" />
<hkern u1="&#xd5;" u2="&#xdd;" k="18" />
<hkern u1="&#xd5;" u2="&#xc6;" k="20" />
<hkern u1="&#xd5;" u2="&#xc5;" k="20" />
<hkern u1="&#xd5;" u2="&#xc4;" k="20" />
<hkern u1="&#xd5;" u2="&#xc3;" k="20" />
<hkern u1="&#xd5;" u2="&#xc2;" k="20" />
<hkern u1="&#xd5;" u2="&#xc1;" k="20" />
<hkern u1="&#xd5;" u2="&#xc0;" k="20" />
<hkern u1="&#xd5;" u2="z" k="10" />
<hkern u1="&#xd5;" u2="x" k="10" />
<hkern u1="&#xd5;" u2="u" k="1" />
<hkern u1="&#xd5;" u2="r" k="1" />
<hkern u1="&#xd5;" u2="q" k="1" />
<hkern u1="&#xd5;" u2="p" k="1" />
<hkern u1="&#xd5;" u2="o" k="1" />
<hkern u1="&#xd5;" u2="n" k="1" />
<hkern u1="&#xd5;" u2="m" k="1" />
<hkern u1="&#xd5;" u2="l" k="3" />
<hkern u1="&#xd5;" u2="k" k="3" />
<hkern u1="&#xd5;" u2="h" k="3" />
<hkern u1="&#xd5;" u2="e" k="1" />
<hkern u1="&#xd5;" u2="d" k="1" />
<hkern u1="&#xd5;" u2="c" k="1" />
<hkern u1="&#xd5;" u2="b" k="3" />
<hkern u1="&#xd5;" u2="a" k="1" />
<hkern u1="&#xd5;" u2="Z" k="19" />
<hkern u1="&#xd5;" u2="Y" k="18" />
<hkern u1="&#xd5;" u2="X" k="29" />
<hkern u1="&#xd5;" u2="W" k="33" />
<hkern u1="&#xd5;" u2="V" k="20" />
<hkern u1="&#xd5;" u2="T" k="19" />
<hkern u1="&#xd5;" u2="J" k="23" />
<hkern u1="&#xd5;" u2="A" k="20" />
<hkern u1="&#xd5;" u2="&#x3f;" k="20" />
<hkern u1="&#xd5;" u2="&#x2f;" k="50" />
<hkern u1="&#xd5;" u2="&#x2e;" k="33" />
<hkern u1="&#xd5;" u2="&#x2c;" k="33" />
<hkern u1="&#xd6;" u2="&#x2026;" k="33" />
<hkern u1="&#xd6;" u2="&#x201e;" k="33" />
<hkern u1="&#xd6;" u2="&#x201c;" k="20" />
<hkern u1="&#xd6;" u2="&#x201a;" k="33" />
<hkern u1="&#xd6;" u2="&#x2018;" k="20" />
<hkern u1="&#xd6;" u2="&#x178;" k="18" />
<hkern u1="&#xd6;" u2="&#x153;" k="1" />
<hkern u1="&#xd6;" u2="&#xe7;" k="1" />
<hkern u1="&#xd6;" u2="&#xe6;" k="1" />
<hkern u1="&#xd6;" u2="&#xdd;" k="18" />
<hkern u1="&#xd6;" u2="&#xc6;" k="20" />
<hkern u1="&#xd6;" u2="&#xc5;" k="20" />
<hkern u1="&#xd6;" u2="&#xc4;" k="20" />
<hkern u1="&#xd6;" u2="&#xc3;" k="20" />
<hkern u1="&#xd6;" u2="&#xc2;" k="20" />
<hkern u1="&#xd6;" u2="&#xc1;" k="20" />
<hkern u1="&#xd6;" u2="&#xc0;" k="20" />
<hkern u1="&#xd6;" u2="z" k="10" />
<hkern u1="&#xd6;" u2="x" k="10" />
<hkern u1="&#xd6;" u2="u" k="1" />
<hkern u1="&#xd6;" u2="r" k="1" />
<hkern u1="&#xd6;" u2="q" k="1" />
<hkern u1="&#xd6;" u2="p" k="1" />
<hkern u1="&#xd6;" u2="o" k="1" />
<hkern u1="&#xd6;" u2="n" k="1" />
<hkern u1="&#xd6;" u2="m" k="1" />
<hkern u1="&#xd6;" u2="l" k="3" />
<hkern u1="&#xd6;" u2="k" k="3" />
<hkern u1="&#xd6;" u2="h" k="3" />
<hkern u1="&#xd6;" u2="e" k="1" />
<hkern u1="&#xd6;" u2="d" k="1" />
<hkern u1="&#xd6;" u2="c" k="1" />
<hkern u1="&#xd6;" u2="b" k="3" />
<hkern u1="&#xd6;" u2="a" k="1" />
<hkern u1="&#xd6;" u2="Z" k="19" />
<hkern u1="&#xd6;" u2="Y" k="18" />
<hkern u1="&#xd6;" u2="X" k="29" />
<hkern u1="&#xd6;" u2="W" k="33" />
<hkern u1="&#xd6;" u2="V" k="20" />
<hkern u1="&#xd6;" u2="T" k="19" />
<hkern u1="&#xd6;" u2="J" k="23" />
<hkern u1="&#xd6;" u2="A" k="20" />
<hkern u1="&#xd6;" u2="&#x3f;" k="20" />
<hkern u1="&#xd6;" u2="&#x2f;" k="50" />
<hkern u1="&#xd6;" u2="&#x2e;" k="33" />
<hkern u1="&#xd6;" u2="&#x2c;" k="33" />
<hkern u1="&#xd8;" u2="&#x2026;" k="33" />
<hkern u1="&#xd8;" u2="&#x201e;" k="33" />
<hkern u1="&#xd8;" u2="&#x201c;" k="20" />
<hkern u1="&#xd8;" u2="&#x201a;" k="33" />
<hkern u1="&#xd8;" u2="&#x2018;" k="20" />
<hkern u1="&#xd8;" u2="&#x178;" k="18" />
<hkern u1="&#xd8;" u2="&#x153;" k="1" />
<hkern u1="&#xd8;" u2="&#xe7;" k="1" />
<hkern u1="&#xd8;" u2="&#xe6;" k="1" />
<hkern u1="&#xd8;" u2="&#xdd;" k="18" />
<hkern u1="&#xd8;" u2="&#xc6;" k="20" />
<hkern u1="&#xd8;" u2="&#xc5;" k="20" />
<hkern u1="&#xd8;" u2="&#xc4;" k="20" />
<hkern u1="&#xd8;" u2="&#xc3;" k="20" />
<hkern u1="&#xd8;" u2="&#xc2;" k="20" />
<hkern u1="&#xd8;" u2="&#xc1;" k="20" />
<hkern u1="&#xd8;" u2="&#xc0;" k="20" />
<hkern u1="&#xd8;" u2="z" k="10" />
<hkern u1="&#xd8;" u2="x" k="10" />
<hkern u1="&#xd8;" u2="u" k="1" />
<hkern u1="&#xd8;" u2="r" k="1" />
<hkern u1="&#xd8;" u2="q" k="1" />
<hkern u1="&#xd8;" u2="p" k="1" />
<hkern u1="&#xd8;" u2="o" k="1" />
<hkern u1="&#xd8;" u2="n" k="1" />
<hkern u1="&#xd8;" u2="m" k="1" />
<hkern u1="&#xd8;" u2="l" k="3" />
<hkern u1="&#xd8;" u2="k" k="3" />
<hkern u1="&#xd8;" u2="h" k="3" />
<hkern u1="&#xd8;" u2="e" k="1" />
<hkern u1="&#xd8;" u2="d" k="1" />
<hkern u1="&#xd8;" u2="c" k="1" />
<hkern u1="&#xd8;" u2="b" k="3" />
<hkern u1="&#xd8;" u2="a" k="1" />
<hkern u1="&#xd8;" u2="Z" k="19" />
<hkern u1="&#xd8;" u2="Y" k="18" />
<hkern u1="&#xd8;" u2="X" k="29" />
<hkern u1="&#xd8;" u2="W" k="33" />
<hkern u1="&#xd8;" u2="V" k="20" />
<hkern u1="&#xd8;" u2="T" k="19" />
<hkern u1="&#xd8;" u2="J" k="23" />
<hkern u1="&#xd8;" u2="A" k="20" />
<hkern u1="&#xd8;" u2="&#x3f;" k="20" />
<hkern u1="&#xd8;" u2="&#x2f;" k="50" />
<hkern u1="&#xd8;" u2="&#x2e;" k="33" />
<hkern u1="&#xd8;" u2="&#x2c;" k="33" />
<hkern u1="&#xd9;" u2="&#x2026;" k="10" />
<hkern u1="&#xd9;" u2="&#x201e;" k="10" />
<hkern u1="&#xd9;" u2="&#x201a;" k="10" />
<hkern u1="&#xd9;" u2="&#xc6;" k="15" />
<hkern u1="&#xd9;" u2="&#xc5;" k="15" />
<hkern u1="&#xd9;" u2="&#xc4;" k="15" />
<hkern u1="&#xd9;" u2="&#xc3;" k="15" />
<hkern u1="&#xd9;" u2="&#xc2;" k="15" />
<hkern u1="&#xd9;" u2="&#xc1;" k="15" />
<hkern u1="&#xd9;" u2="&#xc0;" k="15" />
<hkern u1="&#xd9;" u2="J" k="10" />
<hkern u1="&#xd9;" u2="A" k="15" />
<hkern u1="&#xd9;" u2="&#x2e;" k="10" />
<hkern u1="&#xd9;" u2="&#x2c;" k="10" />
<hkern u1="&#xda;" u2="&#x2026;" k="10" />
<hkern u1="&#xda;" u2="&#x201e;" k="10" />
<hkern u1="&#xda;" u2="&#x201a;" k="10" />
<hkern u1="&#xda;" u2="&#xc6;" k="15" />
<hkern u1="&#xda;" u2="&#xc5;" k="15" />
<hkern u1="&#xda;" u2="&#xc4;" k="15" />
<hkern u1="&#xda;" u2="&#xc3;" k="15" />
<hkern u1="&#xda;" u2="&#xc2;" k="15" />
<hkern u1="&#xda;" u2="&#xc1;" k="15" />
<hkern u1="&#xda;" u2="&#xc0;" k="15" />
<hkern u1="&#xda;" u2="J" k="10" />
<hkern u1="&#xda;" u2="A" k="15" />
<hkern u1="&#xda;" u2="&#x2e;" k="10" />
<hkern u1="&#xda;" u2="&#x2c;" k="10" />
<hkern u1="&#xdb;" u2="&#x2026;" k="10" />
<hkern u1="&#xdb;" u2="&#x201e;" k="10" />
<hkern u1="&#xdb;" u2="&#x201a;" k="10" />
<hkern u1="&#xdb;" u2="&#xc6;" k="15" />
<hkern u1="&#xdb;" u2="&#xc5;" k="15" />
<hkern u1="&#xdb;" u2="&#xc4;" k="15" />
<hkern u1="&#xdb;" u2="&#xc3;" k="15" />
<hkern u1="&#xdb;" u2="&#xc2;" k="15" />
<hkern u1="&#xdb;" u2="&#xc1;" k="15" />
<hkern u1="&#xdb;" u2="&#xc0;" k="15" />
<hkern u1="&#xdb;" u2="J" k="10" />
<hkern u1="&#xdb;" u2="A" k="15" />
<hkern u1="&#xdb;" u2="&#x2e;" k="10" />
<hkern u1="&#xdb;" u2="&#x2c;" k="10" />
<hkern u1="&#xdc;" u2="&#x2026;" k="10" />
<hkern u1="&#xdc;" u2="&#x201e;" k="10" />
<hkern u1="&#xdc;" u2="&#x201a;" k="10" />
<hkern u1="&#xdc;" u2="&#xc6;" k="15" />
<hkern u1="&#xdc;" u2="&#xc5;" k="15" />
<hkern u1="&#xdc;" u2="&#xc4;" k="15" />
<hkern u1="&#xdc;" u2="&#xc3;" k="15" />
<hkern u1="&#xdc;" u2="&#xc2;" k="15" />
<hkern u1="&#xdc;" u2="&#xc1;" k="15" />
<hkern u1="&#xdc;" u2="&#xc0;" k="15" />
<hkern u1="&#xdc;" u2="J" k="10" />
<hkern u1="&#xdc;" u2="A" k="15" />
<hkern u1="&#xdc;" u2="&#x2e;" k="10" />
<hkern u1="&#xdc;" u2="&#x2c;" k="10" />
<hkern u1="&#xdd;" u2="&#x203a;" k="40" />
<hkern u1="&#xdd;" u2="&#x2039;" k="60" />
<hkern u1="&#xdd;" u2="&#x2026;" k="50" />
<hkern u1="&#xdd;" u2="&#x201e;" k="50" />
<hkern u1="&#xdd;" u2="&#x201a;" k="50" />
<hkern u1="&#xdd;" u2="&#x2014;" k="50" />
<hkern u1="&#xdd;" u2="&#x2013;" k="50" />
<hkern u1="&#xdd;" u2="&#x153;" k="70" />
<hkern u1="&#xdd;" u2="&#x152;" k="18" />
<hkern u1="&#xdd;" u2="&#xe7;" k="70" />
<hkern u1="&#xdd;" u2="&#xe6;" k="70" />
<hkern u1="&#xdd;" u2="&#xd8;" k="18" />
<hkern u1="&#xdd;" u2="&#xd6;" k="18" />
<hkern u1="&#xdd;" u2="&#xd5;" k="18" />
<hkern u1="&#xdd;" u2="&#xd4;" k="18" />
<hkern u1="&#xdd;" u2="&#xd3;" k="18" />
<hkern u1="&#xdd;" u2="&#xd2;" k="18" />
<hkern u1="&#xdd;" u2="&#xc6;" k="54" />
<hkern u1="&#xdd;" u2="&#xc5;" k="54" />
<hkern u1="&#xdd;" u2="&#xc4;" k="54" />
<hkern u1="&#xdd;" u2="&#xc3;" k="54" />
<hkern u1="&#xdd;" u2="&#xc2;" k="54" />
<hkern u1="&#xdd;" u2="&#xc1;" k="54" />
<hkern u1="&#xdd;" u2="&#xc0;" k="54" />
<hkern u1="&#xdd;" u2="&#xbb;" k="40" />
<hkern u1="&#xdd;" u2="&#xae;" k="30" />
<hkern u1="&#xdd;" u2="&#xab;" k="60" />
<hkern u1="&#xdd;" u2="&#xa9;" k="30" />
<hkern u1="&#xdd;" u2="&#x7d;" k="-50" />
<hkern u1="&#xdd;" u2="z" k="30" />
<hkern u1="&#xdd;" u2="y" k="20" />
<hkern u1="&#xdd;" u2="x" k="20" />
<hkern u1="&#xdd;" u2="w" k="10" />
<hkern u1="&#xdd;" u2="v" k="20" />
<hkern u1="&#xdd;" u2="u" k="40" />
<hkern u1="&#xdd;" u2="t" k="20" />
<hkern u1="&#xdd;" u2="s" k="54" />
<hkern u1="&#xdd;" u2="r" k="40" />
<hkern u1="&#xdd;" u2="q" k="70" />
<hkern u1="&#xdd;" u2="p" k="40" />
<hkern u1="&#xdd;" u2="o" k="70" />
<hkern u1="&#xdd;" u2="n" k="40" />
<hkern u1="&#xdd;" u2="m" k="40" />
<hkern u1="&#xdd;" u2="g" k="40" />
<hkern u1="&#xdd;" u2="f" k="10" />
<hkern u1="&#xdd;" u2="e" k="70" />
<hkern u1="&#xdd;" u2="d" k="70" />
<hkern u1="&#xdd;" u2="c" k="70" />
<hkern u1="&#xdd;" u2="a" k="70" />
<hkern u1="&#xdd;" u2="]" k="-50" />
<hkern u1="&#xdd;" u2="X" k="-13" />
<hkern u1="&#xdd;" u2="V" k="-23" />
<hkern u1="&#xdd;" u2="T" k="-28" />
<hkern u1="&#xdd;" u2="S" k="-10" />
<hkern u1="&#xdd;" u2="Q" k="18" />
<hkern u1="&#xdd;" u2="O" k="18" />
<hkern u1="&#xdd;" u2="J" k="85" />
<hkern u1="&#xdd;" u2="G" k="18" />
<hkern u1="&#xdd;" u2="C" k="18" />
<hkern u1="&#xdd;" u2="A" k="54" />
<hkern u1="&#xdd;" u2="&#x40;" k="30" />
<hkern u1="&#xdd;" u2="&#x3b;" k="30" />
<hkern u1="&#xdd;" u2="&#x3a;" k="30" />
<hkern u1="&#xdd;" u2="&#x2f;" k="45" />
<hkern u1="&#xdd;" u2="&#x2e;" k="50" />
<hkern u1="&#xdd;" u2="&#x2d;" k="50" />
<hkern u1="&#xdd;" u2="&#x2c;" k="50" />
<hkern u1="&#xdd;" u2="&#x29;" k="-50" />
<hkern u1="&#xdd;" u2="&#x26;" k="30" />
<hkern u1="&#xde;" u2="&#x2026;" k="33" />
<hkern u1="&#xde;" u2="&#x201e;" k="33" />
<hkern u1="&#xde;" u2="&#x201c;" k="20" />
<hkern u1="&#xde;" u2="&#x201a;" k="33" />
<hkern u1="&#xde;" u2="&#x2018;" k="20" />
<hkern u1="&#xde;" u2="&#x178;" k="18" />
<hkern u1="&#xde;" u2="&#x153;" k="1" />
<hkern u1="&#xde;" u2="&#xe7;" k="1" />
<hkern u1="&#xde;" u2="&#xe6;" k="1" />
<hkern u1="&#xde;" u2="&#xdd;" k="18" />
<hkern u1="&#xde;" u2="&#xc6;" k="20" />
<hkern u1="&#xde;" u2="&#xc5;" k="20" />
<hkern u1="&#xde;" u2="&#xc4;" k="20" />
<hkern u1="&#xde;" u2="&#xc3;" k="20" />
<hkern u1="&#xde;" u2="&#xc2;" k="20" />
<hkern u1="&#xde;" u2="&#xc1;" k="20" />
<hkern u1="&#xde;" u2="&#xc0;" k="20" />
<hkern u1="&#xde;" u2="z" k="10" />
<hkern u1="&#xde;" u2="x" k="10" />
<hkern u1="&#xde;" u2="u" k="1" />
<hkern u1="&#xde;" u2="r" k="1" />
<hkern u1="&#xde;" u2="q" k="1" />
<hkern u1="&#xde;" u2="p" k="1" />
<hkern u1="&#xde;" u2="o" k="1" />
<hkern u1="&#xde;" u2="n" k="1" />
<hkern u1="&#xde;" u2="m" k="1" />
<hkern u1="&#xde;" u2="l" k="3" />
<hkern u1="&#xde;" u2="k" k="3" />
<hkern u1="&#xde;" u2="h" k="3" />
<hkern u1="&#xde;" u2="e" k="1" />
<hkern u1="&#xde;" u2="d" k="1" />
<hkern u1="&#xde;" u2="c" k="1" />
<hkern u1="&#xde;" u2="b" k="3" />
<hkern u1="&#xde;" u2="a" k="1" />
<hkern u1="&#xde;" u2="Z" k="19" />
<hkern u1="&#xde;" u2="Y" k="18" />
<hkern u1="&#xde;" u2="X" k="29" />
<hkern u1="&#xde;" u2="W" k="33" />
<hkern u1="&#xde;" u2="V" k="20" />
<hkern u1="&#xde;" u2="T" k="19" />
<hkern u1="&#xde;" u2="J" k="23" />
<hkern u1="&#xde;" u2="A" k="20" />
<hkern u1="&#xde;" u2="&#x3f;" k="20" />
<hkern u1="&#xde;" u2="&#x2f;" k="50" />
<hkern u1="&#xde;" u2="&#x2e;" k="33" />
<hkern u1="&#xde;" u2="&#x2c;" k="33" />
<hkern u1="&#xdf;" u2="&#x2122;" k="30" />
<hkern u1="&#xdf;" u2="&#x2026;" k="20" />
<hkern u1="&#xdf;" u2="&#x201e;" k="20" />
<hkern u1="&#xdf;" u2="&#x201c;" k="25" />
<hkern u1="&#xdf;" u2="&#x201a;" k="20" />
<hkern u1="&#xdf;" u2="&#x2018;" k="25" />
<hkern u1="&#xdf;" u2="z" k="19" />
<hkern u1="&#xdf;" u2="y" k="21" />
<hkern u1="&#xdf;" u2="x" k="23" />
<hkern u1="&#xdf;" u2="w" k="11" />
<hkern u1="&#xdf;" u2="v" k="21" />
<hkern u1="&#xdf;" u2="t" k="10" />
<hkern u1="&#xdf;" u2="g" k="10" />
<hkern u1="&#xdf;" u2="f" k="8" />
<hkern u1="&#xdf;" u2="\" k="10" />
<hkern u1="&#xdf;" u2="&#x3f;" k="31" />
<hkern u1="&#xdf;" u2="&#x3b;" k="10" />
<hkern u1="&#xdf;" u2="&#x3a;" k="10" />
<hkern u1="&#xdf;" u2="&#x2f;" k="10" />
<hkern u1="&#xdf;" u2="&#x2e;" k="20" />
<hkern u1="&#xdf;" u2="&#x2c;" k="20" />
<hkern u1="&#xdf;" u2="&#x21;" k="15" />
<hkern u1="&#xe0;" u2="&#x2122;" k="20" />
<hkern u1="&#xe0;" u2="&#x201c;" k="30" />
<hkern u1="&#xe0;" u2="&#x2018;" k="30" />
<hkern u1="&#xe0;" u2="y" k="9" />
<hkern u1="&#xe0;" u2="v" k="9" />
<hkern u1="&#xe0;" u2="t" k="10" />
<hkern u1="&#xe0;" u2="f" k="4" />
<hkern u1="&#xe0;" u2="\" k="10" />
<hkern u1="&#xe0;" u2="&#x3f;" k="18" />
<hkern u1="&#xe1;" u2="&#x2122;" k="20" />
<hkern u1="&#xe1;" u2="&#x201c;" k="30" />
<hkern u1="&#xe1;" u2="&#x2018;" k="30" />
<hkern u1="&#xe1;" u2="y" k="9" />
<hkern u1="&#xe1;" u2="v" k="9" />
<hkern u1="&#xe1;" u2="t" k="10" />
<hkern u1="&#xe1;" u2="f" k="4" />
<hkern u1="&#xe1;" u2="\" k="10" />
<hkern u1="&#xe1;" u2="&#x3f;" k="18" />
<hkern u1="&#xe2;" u2="&#x2122;" k="20" />
<hkern u1="&#xe2;" u2="&#x201c;" k="30" />
<hkern u1="&#xe2;" u2="&#x2018;" k="30" />
<hkern u1="&#xe2;" u2="y" k="9" />
<hkern u1="&#xe2;" u2="v" k="9" />
<hkern u1="&#xe2;" u2="t" k="10" />
<hkern u1="&#xe2;" u2="f" k="4" />
<hkern u1="&#xe2;" u2="\" k="10" />
<hkern u1="&#xe2;" u2="&#x3f;" k="18" />
<hkern u1="&#xe4;" u2="&#x2122;" k="20" />
<hkern u1="&#xe4;" u2="&#x201c;" k="30" />
<hkern u1="&#xe4;" u2="&#x2018;" k="30" />
<hkern u1="&#xe4;" u2="y" k="9" />
<hkern u1="&#xe4;" u2="v" k="9" />
<hkern u1="&#xe4;" u2="t" k="10" />
<hkern u1="&#xe4;" u2="f" k="4" />
<hkern u1="&#xe4;" u2="\" k="10" />
<hkern u1="&#xe4;" u2="&#x3f;" k="18" />
<hkern u1="&#xe6;" u2="y" k="18" />
<hkern u1="&#xe6;" u2="x" k="19" />
<hkern u1="&#xe6;" u2="w" k="8" />
<hkern u1="&#xe6;" u2="v" k="18" />
<hkern u1="&#xe6;" u2="t" k="4" />
<hkern u1="&#xe6;" u2="f" k="8" />
<hkern u1="&#xe7;" u2="&#x2039;" k="15" />
<hkern u1="&#xe7;" u2="&#xab;" k="15" />
<hkern u1="&#xe8;" u2="y" k="18" />
<hkern u1="&#xe8;" u2="x" k="19" />
<hkern u1="&#xe8;" u2="w" k="8" />
<hkern u1="&#xe8;" u2="v" k="18" />
<hkern u1="&#xe8;" u2="t" k="4" />
<hkern u1="&#xe8;" u2="f" k="8" />
<hkern u1="&#xe9;" u2="y" k="18" />
<hkern u1="&#xe9;" u2="x" k="19" />
<hkern u1="&#xe9;" u2="w" k="8" />
<hkern u1="&#xe9;" u2="v" k="18" />
<hkern u1="&#xe9;" u2="t" k="4" />
<hkern u1="&#xe9;" u2="f" k="8" />
<hkern u1="&#xea;" u2="y" k="18" />
<hkern u1="&#xea;" u2="x" k="19" />
<hkern u1="&#xea;" u2="w" k="8" />
<hkern u1="&#xea;" u2="v" k="18" />
<hkern u1="&#xea;" u2="t" k="4" />
<hkern u1="&#xea;" u2="f" k="8" />
<hkern u1="&#xeb;" u2="y" k="18" />
<hkern u1="&#xeb;" u2="x" k="19" />
<hkern u1="&#xeb;" u2="w" k="8" />
<hkern u1="&#xeb;" u2="v" k="18" />
<hkern u1="&#xeb;" u2="t" k="4" />
<hkern u1="&#xeb;" u2="f" k="8" />
<hkern u1="&#xf1;" u2="&#x2122;" k="20" />
<hkern u1="&#xf1;" u2="&#x201c;" k="30" />
<hkern u1="&#xf1;" u2="&#x2018;" k="30" />
<hkern u1="&#xf1;" u2="y" k="9" />
<hkern u1="&#xf1;" u2="v" k="9" />
<hkern u1="&#xf1;" u2="t" k="10" />
<hkern u1="&#xf1;" u2="f" k="4" />
<hkern u1="&#xf1;" u2="\" k="10" />
<hkern u1="&#xf1;" u2="&#x3f;" k="18" />
<hkern u1="&#xf3;" u2="&#x2122;" k="30" />
<hkern u1="&#xf3;" u2="&#x2026;" k="20" />
<hkern u1="&#xf3;" u2="&#x201e;" k="20" />
<hkern u1="&#xf3;" u2="&#x201c;" k="25" />
<hkern u1="&#xf3;" u2="&#x201a;" k="20" />
<hkern u1="&#xf3;" u2="&#x2018;" k="25" />
<hkern u1="&#xf3;" u2="z" k="19" />
<hkern u1="&#xf3;" u2="y" k="21" />
<hkern u1="&#xf3;" u2="x" k="23" />
<hkern u1="&#xf3;" u2="w" k="11" />
<hkern u1="&#xf3;" u2="v" k="21" />
<hkern u1="&#xf3;" u2="t" k="11" />
<hkern u1="&#xf3;" u2="f" k="8" />
<hkern u1="&#xf3;" u2="\" k="10" />
<hkern u1="&#xf3;" u2="&#x3f;" k="31" />
<hkern u1="&#xf3;" u2="&#x3b;" k="10" />
<hkern u1="&#xf3;" u2="&#x3a;" k="10" />
<hkern u1="&#xf3;" u2="&#x2f;" k="10" />
<hkern u1="&#xf3;" u2="&#x2e;" k="20" />
<hkern u1="&#xf3;" u2="&#x2c;" k="20" />
<hkern u1="&#xf3;" u2="&#x21;" k="15" />
<hkern u1="&#xf4;" u2="&#x2122;" k="30" />
<hkern u1="&#xf4;" u2="&#x2026;" k="20" />
<hkern u1="&#xf4;" u2="&#x201e;" k="20" />
<hkern u1="&#xf4;" u2="&#x201c;" k="25" />
<hkern u1="&#xf4;" u2="&#x201a;" k="20" />
<hkern u1="&#xf4;" u2="&#x2018;" k="25" />
<hkern u1="&#xf4;" u2="z" k="19" />
<hkern u1="&#xf4;" u2="y" k="21" />
<hkern u1="&#xf4;" u2="x" k="23" />
<hkern u1="&#xf4;" u2="w" k="11" />
<hkern u1="&#xf4;" u2="v" k="21" />
<hkern u1="&#xf4;" u2="t" k="11" />
<hkern u1="&#xf4;" u2="f" k="8" />
<hkern u1="&#xf4;" u2="\" k="10" />
<hkern u1="&#xf4;" u2="&#x3f;" k="31" />
<hkern u1="&#xf4;" u2="&#x3b;" k="10" />
<hkern u1="&#xf4;" u2="&#x3a;" k="10" />
<hkern u1="&#xf4;" u2="&#x2f;" k="10" />
<hkern u1="&#xf4;" u2="&#x2e;" k="20" />
<hkern u1="&#xf4;" u2="&#x2c;" k="20" />
<hkern u1="&#xf4;" u2="&#x21;" k="15" />
<hkern u1="&#xf5;" u2="&#x2122;" k="30" />
<hkern u1="&#xf5;" u2="&#x2026;" k="20" />
<hkern u1="&#xf5;" u2="&#x201e;" k="20" />
<hkern u1="&#xf5;" u2="&#x201c;" k="25" />
<hkern u1="&#xf5;" u2="&#x201a;" k="20" />
<hkern u1="&#xf5;" u2="&#x2018;" k="25" />
<hkern u1="&#xf5;" u2="z" k="19" />
<hkern u1="&#xf5;" u2="y" k="21" />
<hkern u1="&#xf5;" u2="x" k="23" />
<hkern u1="&#xf5;" u2="w" k="11" />
<hkern u1="&#xf5;" u2="v" k="21" />
<hkern u1="&#xf5;" u2="t" k="11" />
<hkern u1="&#xf5;" u2="f" k="8" />
<hkern u1="&#xf5;" u2="\" k="10" />
<hkern u1="&#xf5;" u2="&#x3f;" k="31" />
<hkern u1="&#xf5;" u2="&#x3b;" k="10" />
<hkern u1="&#xf5;" u2="&#x3a;" k="10" />
<hkern u1="&#xf5;" u2="&#x2f;" k="10" />
<hkern u1="&#xf5;" u2="&#x2e;" k="20" />
<hkern u1="&#xf5;" u2="&#x2c;" k="20" />
<hkern u1="&#xf5;" u2="&#x21;" k="15" />
<hkern u1="&#xf6;" u2="&#x2122;" k="30" />
<hkern u1="&#xf6;" u2="&#x2026;" k="20" />
<hkern u1="&#xf6;" u2="&#x201e;" k="20" />
<hkern u1="&#xf6;" u2="&#x201c;" k="25" />
<hkern u1="&#xf6;" u2="&#x201a;" k="20" />
<hkern u1="&#xf6;" u2="&#x2018;" k="25" />
<hkern u1="&#xf6;" u2="z" k="19" />
<hkern u1="&#xf6;" u2="y" k="21" />
<hkern u1="&#xf6;" u2="x" k="23" />
<hkern u1="&#xf6;" u2="w" k="11" />
<hkern u1="&#xf6;" u2="v" k="21" />
<hkern u1="&#xf6;" u2="t" k="11" />
<hkern u1="&#xf6;" u2="f" k="8" />
<hkern u1="&#xf6;" u2="\" k="10" />
<hkern u1="&#xf6;" u2="&#x3f;" k="31" />
<hkern u1="&#xf6;" u2="&#x3b;" k="10" />
<hkern u1="&#xf6;" u2="&#x3a;" k="10" />
<hkern u1="&#xf6;" u2="&#x2f;" k="10" />
<hkern u1="&#xf6;" u2="&#x2e;" k="20" />
<hkern u1="&#xf6;" u2="&#x2c;" k="20" />
<hkern u1="&#xf6;" u2="&#x21;" k="15" />
<hkern u1="&#xf8;" u2="&#x2122;" k="30" />
<hkern u1="&#xf8;" u2="&#x2026;" k="20" />
<hkern u1="&#xf8;" u2="&#x201e;" k="20" />
<hkern u1="&#xf8;" u2="&#x201c;" k="25" />
<hkern u1="&#xf8;" u2="&#x201a;" k="20" />
<hkern u1="&#xf8;" u2="&#x2018;" k="25" />
<hkern u1="&#xf8;" u2="z" k="19" />
<hkern u1="&#xf8;" u2="y" k="21" />
<hkern u1="&#xf8;" u2="x" k="23" />
<hkern u1="&#xf8;" u2="w" k="11" />
<hkern u1="&#xf8;" u2="v" k="21" />
<hkern u1="&#xf8;" u2="t" k="11" />
<hkern u1="&#xf8;" u2="f" k="8" />
<hkern u1="&#xf8;" u2="\" k="10" />
<hkern u1="&#xf8;" u2="&#x3f;" k="31" />
<hkern u1="&#xf8;" u2="&#x3b;" k="10" />
<hkern u1="&#xf8;" u2="&#x3a;" k="10" />
<hkern u1="&#xf8;" u2="&#x2f;" k="10" />
<hkern u1="&#xf8;" u2="&#x2e;" k="20" />
<hkern u1="&#xf8;" u2="&#x2c;" k="20" />
<hkern u1="&#xf8;" u2="&#x21;" k="15" />
<hkern u1="&#x152;" u2="&#x2039;" k="15" />
<hkern u1="&#x152;" u2="&#x153;" k="21" />
<hkern u1="&#x152;" u2="&#x152;" k="19" />
<hkern u1="&#x152;" u2="&#xfe;" k="4" />
<hkern u1="&#x152;" u2="&#xe7;" k="21" />
<hkern u1="&#x152;" u2="&#xe6;" k="21" />
<hkern u1="&#x152;" u2="&#xd8;" k="19" />
<hkern u1="&#x152;" u2="&#xd6;" k="19" />
<hkern u1="&#x152;" u2="&#xd5;" k="19" />
<hkern u1="&#x152;" u2="&#xd4;" k="19" />
<hkern u1="&#x152;" u2="&#xd3;" k="19" />
<hkern u1="&#x152;" u2="&#xd2;" k="19" />
<hkern u1="&#x152;" u2="&#xae;" k="20" />
<hkern u1="&#x152;" u2="&#xab;" k="15" />
<hkern u1="&#x152;" u2="&#xa9;" k="20" />
<hkern u1="&#x152;" u2="y" k="20" />
<hkern u1="&#x152;" u2="v" k="20" />
<hkern u1="&#x152;" u2="u" k="8" />
<hkern u1="&#x152;" u2="r" k="8" />
<hkern u1="&#x152;" u2="q" k="21" />
<hkern u1="&#x152;" u2="p" k="8" />
<hkern u1="&#x152;" u2="o" k="21" />
<hkern u1="&#x152;" u2="n" k="8" />
<hkern u1="&#x152;" u2="m" k="8" />
<hkern u1="&#x152;" u2="l" k="4" />
<hkern u1="&#x152;" u2="k" k="4" />
<hkern u1="&#x152;" u2="h" k="4" />
<hkern u1="&#x152;" u2="g" k="16" />
<hkern u1="&#x152;" u2="f" k="10" />
<hkern u1="&#x152;" u2="e" k="21" />
<hkern u1="&#x152;" u2="d" k="21" />
<hkern u1="&#x152;" u2="c" k="21" />
<hkern u1="&#x152;" u2="b" k="4" />
<hkern u1="&#x152;" u2="a" k="21" />
<hkern u1="&#x152;" u2="W" k="4" />
<hkern u1="&#x152;" u2="V" k="-8" />
<hkern u1="&#x152;" u2="T" k="-8" />
<hkern u1="&#x152;" u2="Q" k="19" />
<hkern u1="&#x152;" u2="O" k="19" />
<hkern u1="&#x152;" u2="J" k="-4" />
<hkern u1="&#x152;" u2="G" k="19" />
<hkern u1="&#x152;" u2="C" k="19" />
<hkern u1="&#x152;" u2="&#x40;" k="20" />
<hkern u1="&#x153;" u2="y" k="18" />
<hkern u1="&#x153;" u2="x" k="19" />
<hkern u1="&#x153;" u2="w" k="8" />
<hkern u1="&#x153;" u2="v" k="18" />
<hkern u1="&#x153;" u2="t" k="4" />
<hkern u1="&#x153;" u2="f" k="8" />
<hkern u1="&#x178;" u2="&#x203a;" k="40" />
<hkern u1="&#x178;" u2="&#x2039;" k="60" />
<hkern u1="&#x178;" u2="&#x2026;" k="50" />
<hkern u1="&#x178;" u2="&#x201e;" k="50" />
<hkern u1="&#x178;" u2="&#x201a;" k="50" />
<hkern u1="&#x178;" u2="&#x2014;" k="50" />
<hkern u1="&#x178;" u2="&#x2013;" k="50" />
<hkern u1="&#x178;" u2="&#x153;" k="70" />
<hkern u1="&#x178;" u2="&#x152;" k="18" />
<hkern u1="&#x178;" u2="&#xe7;" k="70" />
<hkern u1="&#x178;" u2="&#xe6;" k="70" />
<hkern u1="&#x178;" u2="&#xd8;" k="18" />
<hkern u1="&#x178;" u2="&#xd6;" k="18" />
<hkern u1="&#x178;" u2="&#xd5;" k="18" />
<hkern u1="&#x178;" u2="&#xd4;" k="18" />
<hkern u1="&#x178;" u2="&#xd3;" k="18" />
<hkern u1="&#x178;" u2="&#xd2;" k="18" />
<hkern u1="&#x178;" u2="&#xc6;" k="54" />
<hkern u1="&#x178;" u2="&#xc5;" k="54" />
<hkern u1="&#x178;" u2="&#xc4;" k="54" />
<hkern u1="&#x178;" u2="&#xc3;" k="54" />
<hkern u1="&#x178;" u2="&#xc2;" k="54" />
<hkern u1="&#x178;" u2="&#xc1;" k="54" />
<hkern u1="&#x178;" u2="&#xc0;" k="54" />
<hkern u1="&#x178;" u2="&#xbb;" k="40" />
<hkern u1="&#x178;" u2="&#xae;" k="30" />
<hkern u1="&#x178;" u2="&#xab;" k="60" />
<hkern u1="&#x178;" u2="&#xa9;" k="30" />
<hkern u1="&#x178;" u2="&#x7d;" k="-50" />
<hkern u1="&#x178;" u2="z" k="30" />
<hkern u1="&#x178;" u2="y" k="20" />
<hkern u1="&#x178;" u2="x" k="20" />
<hkern u1="&#x178;" u2="w" k="10" />
<hkern u1="&#x178;" u2="v" k="20" />
<hkern u1="&#x178;" u2="u" k="40" />
<hkern u1="&#x178;" u2="t" k="20" />
<hkern u1="&#x178;" u2="s" k="54" />
<hkern u1="&#x178;" u2="r" k="40" />
<hkern u1="&#x178;" u2="q" k="70" />
<hkern u1="&#x178;" u2="p" k="40" />
<hkern u1="&#x178;" u2="o" k="70" />
<hkern u1="&#x178;" u2="n" k="40" />
<hkern u1="&#x178;" u2="m" k="40" />
<hkern u1="&#x178;" u2="g" k="40" />
<hkern u1="&#x178;" u2="f" k="10" />
<hkern u1="&#x178;" u2="e" k="70" />
<hkern u1="&#x178;" u2="d" k="70" />
<hkern u1="&#x178;" u2="c" k="70" />
<hkern u1="&#x178;" u2="a" k="70" />
<hkern u1="&#x178;" u2="]" k="-50" />
<hkern u1="&#x178;" u2="X" k="-13" />
<hkern u1="&#x178;" u2="V" k="-23" />
<hkern u1="&#x178;" u2="T" k="-28" />
<hkern u1="&#x178;" u2="S" k="-10" />
<hkern u1="&#x178;" u2="Q" k="18" />
<hkern u1="&#x178;" u2="O" k="18" />
<hkern u1="&#x178;" u2="J" k="85" />
<hkern u1="&#x178;" u2="G" k="18" />
<hkern u1="&#x178;" u2="C" k="18" />
<hkern u1="&#x178;" u2="A" k="54" />
<hkern u1="&#x178;" u2="&#x40;" k="30" />
<hkern u1="&#x178;" u2="&#x3b;" k="30" />
<hkern u1="&#x178;" u2="&#x3a;" k="30" />
<hkern u1="&#x178;" u2="&#x2f;" k="45" />
<hkern u1="&#x178;" u2="&#x2e;" k="50" />
<hkern u1="&#x178;" u2="&#x2d;" k="50" />
<hkern u1="&#x178;" u2="&#x2c;" k="50" />
<hkern u1="&#x178;" u2="&#x29;" k="-50" />
<hkern u1="&#x178;" u2="&#x26;" k="30" />
<hkern u1="&#x2013;" u2="&#x178;" k="50" />
<hkern u1="&#x2013;" u2="&#x153;" k="10" />
<hkern u1="&#x2013;" u2="&#xe7;" k="10" />
<hkern u1="&#x2013;" u2="&#xe6;" k="10" />
<hkern u1="&#x2013;" u2="&#xdd;" k="50" />
<hkern u1="&#x2013;" u2="&#xc6;" k="-10" />
<hkern u1="&#x2013;" u2="&#xc5;" k="-10" />
<hkern u1="&#x2013;" u2="&#xc4;" k="-10" />
<hkern u1="&#x2013;" u2="&#xc3;" k="-10" />
<hkern u1="&#x2013;" u2="&#xc2;" k="-10" />
<hkern u1="&#x2013;" u2="&#xc1;" k="-10" />
<hkern u1="&#x2013;" u2="&#xc0;" k="-10" />
<hkern u1="&#x2013;" u2="z" k="15" />
<hkern u1="&#x2013;" u2="y" k="10" />
<hkern u1="&#x2013;" u2="x" k="30" />
<hkern u1="&#x2013;" u2="v" k="10" />
<hkern u1="&#x2013;" u2="q" k="10" />
<hkern u1="&#x2013;" u2="o" k="10" />
<hkern u1="&#x2013;" u2="e" k="10" />
<hkern u1="&#x2013;" u2="d" k="10" />
<hkern u1="&#x2013;" u2="c" k="10" />
<hkern u1="&#x2013;" u2="a" k="10" />
<hkern u1="&#x2013;" u2="Z" k="10" />
<hkern u1="&#x2013;" u2="Y" k="50" />
<hkern u1="&#x2013;" u2="X" k="30" />
<hkern u1="&#x2013;" u2="W" k="40" />
<hkern u1="&#x2013;" u2="V" k="50" />
<hkern u1="&#x2013;" u2="T" k="55" />
<hkern u1="&#x2013;" u2="A" k="-10" />
<hkern u1="&#x2013;" u2="&#x37;" k="30" />
<hkern u1="&#x2013;" u2="&#x31;" k="20" />
<hkern u1="&#x2014;" u2="&#x178;" k="50" />
<hkern u1="&#x2014;" u2="&#x153;" k="10" />
<hkern u1="&#x2014;" u2="&#xe7;" k="10" />
<hkern u1="&#x2014;" u2="&#xe6;" k="10" />
<hkern u1="&#x2014;" u2="&#xdd;" k="50" />
<hkern u1="&#x2014;" u2="&#xc6;" k="-10" />
<hkern u1="&#x2014;" u2="&#xc5;" k="-10" />
<hkern u1="&#x2014;" u2="&#xc4;" k="-10" />
<hkern u1="&#x2014;" u2="&#xc3;" k="-10" />
<hkern u1="&#x2014;" u2="&#xc2;" k="-10" />
<hkern u1="&#x2014;" u2="&#xc1;" k="-10" />
<hkern u1="&#x2014;" u2="&#xc0;" k="-10" />
<hkern u1="&#x2014;" u2="z" k="15" />
<hkern u1="&#x2014;" u2="y" k="10" />
<hkern u1="&#x2014;" u2="x" k="30" />
<hkern u1="&#x2014;" u2="v" k="10" />
<hkern u1="&#x2014;" u2="q" k="10" />
<hkern u1="&#x2014;" u2="o" k="10" />
<hkern u1="&#x2014;" u2="e" k="10" />
<hkern u1="&#x2014;" u2="d" k="10" />
<hkern u1="&#x2014;" u2="c" k="10" />
<hkern u1="&#x2014;" u2="a" k="10" />
<hkern u1="&#x2014;" u2="Z" k="10" />
<hkern u1="&#x2014;" u2="Y" k="50" />
<hkern u1="&#x2014;" u2="X" k="30" />
<hkern u1="&#x2014;" u2="W" k="40" />
<hkern u1="&#x2014;" u2="V" k="50" />
<hkern u1="&#x2014;" u2="T" k="55" />
<hkern u1="&#x2014;" u2="A" k="-10" />
<hkern u1="&#x2014;" u2="&#x37;" k="30" />
<hkern u1="&#x2014;" u2="&#x31;" k="20" />
<hkern u1="&#x2018;" u2="&#x178;" k="-30" />
<hkern u1="&#x2018;" u2="&#x153;" k="30" />
<hkern u1="&#x2018;" u2="&#xe7;" k="30" />
<hkern u1="&#x2018;" u2="&#xe6;" k="30" />
<hkern u1="&#x2018;" u2="&#xdd;" k="-30" />
<hkern u1="&#x2018;" u2="&#xc6;" k="40" />
<hkern u1="&#x2018;" u2="&#xc5;" k="40" />
<hkern u1="&#x2018;" u2="&#xc4;" k="40" />
<hkern u1="&#x2018;" u2="&#xc3;" k="40" />
<hkern u1="&#x2018;" u2="&#xc2;" k="40" />
<hkern u1="&#x2018;" u2="&#xc1;" k="40" />
<hkern u1="&#x2018;" u2="&#xc0;" k="40" />
<hkern u1="&#x2018;" u2="u" k="10" />
<hkern u1="&#x2018;" u2="s" k="30" />
<hkern u1="&#x2018;" u2="r" k="10" />
<hkern u1="&#x2018;" u2="q" k="30" />
<hkern u1="&#x2018;" u2="p" k="10" />
<hkern u1="&#x2018;" u2="o" k="30" />
<hkern u1="&#x2018;" u2="n" k="10" />
<hkern u1="&#x2018;" u2="m" k="10" />
<hkern u1="&#x2018;" u2="g" k="40" />
<hkern u1="&#x2018;" u2="e" k="30" />
<hkern u1="&#x2018;" u2="d" k="30" />
<hkern u1="&#x2018;" u2="c" k="30" />
<hkern u1="&#x2018;" u2="a" k="30" />
<hkern u1="&#x2018;" u2="Y" k="-30" />
<hkern u1="&#x2018;" u2="X" k="-20" />
<hkern u1="&#x2018;" u2="W" k="-20" />
<hkern u1="&#x2018;" u2="V" k="-20" />
<hkern u1="&#x2018;" u2="T" k="-10" />
<hkern u1="&#x2018;" u2="J" k="118" />
<hkern u1="&#x2018;" u2="A" k="40" />
<hkern u1="&#x2019;" u2="&#x153;" k="50" />
<hkern u1="&#x2019;" u2="&#x152;" k="40" />
<hkern u1="&#x2019;" u2="&#xe7;" k="50" />
<hkern u1="&#x2019;" u2="&#xe6;" k="50" />
<hkern u1="&#x2019;" u2="&#xd8;" k="40" />
<hkern u1="&#x2019;" u2="&#xd6;" k="40" />
<hkern u1="&#x2019;" u2="&#xd5;" k="40" />
<hkern u1="&#x2019;" u2="&#xd4;" k="40" />
<hkern u1="&#x2019;" u2="&#xd3;" k="40" />
<hkern u1="&#x2019;" u2="&#xd2;" k="40" />
<hkern u1="&#x2019;" u2="&#xc6;" k="38" />
<hkern u1="&#x2019;" u2="&#xc5;" k="38" />
<hkern u1="&#x2019;" u2="&#xc4;" k="38" />
<hkern u1="&#x2019;" u2="&#xc3;" k="38" />
<hkern u1="&#x2019;" u2="&#xc2;" k="38" />
<hkern u1="&#x2019;" u2="&#xc1;" k="38" />
<hkern u1="&#x2019;" u2="&#xc0;" k="38" />
<hkern u1="&#x2019;" u2="q" k="50" />
<hkern u1="&#x2019;" u2="o" k="50" />
<hkern u1="&#x2019;" u2="e" k="50" />
<hkern u1="&#x2019;" u2="d" k="50" />
<hkern u1="&#x2019;" u2="c" k="50" />
<hkern u1="&#x2019;" u2="a" k="50" />
<hkern u1="&#x2019;" u2="S" k="10" />
<hkern u1="&#x2019;" u2="Q" k="40" />
<hkern u1="&#x2019;" u2="O" k="40" />
<hkern u1="&#x2019;" u2="J" k="120" />
<hkern u1="&#x2019;" u2="G" k="40" />
<hkern u1="&#x2019;" u2="C" k="40" />
<hkern u1="&#x2019;" u2="A" k="38" />
<hkern u1="&#x201a;" u2="&#x178;" k="50" />
<hkern u1="&#x201a;" u2="&#x153;" k="20" />
<hkern u1="&#x201a;" u2="&#x152;" k="33" />
<hkern u1="&#x201a;" u2="&#xe7;" k="20" />
<hkern u1="&#x201a;" u2="&#xe6;" k="20" />
<hkern u1="&#x201a;" u2="&#xdd;" k="50" />
<hkern u1="&#x201a;" u2="&#xdc;" k="10" />
<hkern u1="&#x201a;" u2="&#xdb;" k="10" />
<hkern u1="&#x201a;" u2="&#xda;" k="10" />
<hkern u1="&#x201a;" u2="&#xd9;" k="10" />
<hkern u1="&#x201a;" u2="&#xd8;" k="33" />
<hkern u1="&#x201a;" u2="&#xd6;" k="33" />
<hkern u1="&#x201a;" u2="&#xd5;" k="33" />
<hkern u1="&#x201a;" u2="&#xd4;" k="33" />
<hkern u1="&#x201a;" u2="&#xd3;" k="33" />
<hkern u1="&#x201a;" u2="&#xd2;" k="33" />
<hkern u1="&#x201a;" u2="&#xc6;" k="-30" />
<hkern u1="&#x201a;" u2="&#xc5;" k="-30" />
<hkern u1="&#x201a;" u2="&#xc4;" k="-30" />
<hkern u1="&#x201a;" u2="&#xc3;" k="-30" />
<hkern u1="&#x201a;" u2="&#xc2;" k="-30" />
<hkern u1="&#x201a;" u2="&#xc1;" k="-30" />
<hkern u1="&#x201a;" u2="&#xc0;" k="-30" />
<hkern u1="&#x201a;" u2="y" k="30" />
<hkern u1="&#x201a;" u2="w" k="10" />
<hkern u1="&#x201a;" u2="v" k="30" />
<hkern u1="&#x201a;" u2="u" k="20" />
<hkern u1="&#x201a;" u2="t" k="40" />
<hkern u1="&#x201a;" u2="r" k="20" />
<hkern u1="&#x201a;" u2="q" k="20" />
<hkern u1="&#x201a;" u2="p" k="20" />
<hkern u1="&#x201a;" u2="o" k="20" />
<hkern u1="&#x201a;" u2="n" k="20" />
<hkern u1="&#x201a;" u2="m" k="20" />
<hkern u1="&#x201a;" u2="j" k="-30" />
<hkern u1="&#x201a;" u2="f" k="30" />
<hkern u1="&#x201a;" u2="e" k="20" />
<hkern u1="&#x201a;" u2="d" k="20" />
<hkern u1="&#x201a;" u2="c" k="20" />
<hkern u1="&#x201a;" u2="a" k="20" />
<hkern u1="&#x201a;" u2="Y" k="50" />
<hkern u1="&#x201a;" u2="W" k="28" />
<hkern u1="&#x201a;" u2="V" k="68" />
<hkern u1="&#x201a;" u2="U" k="10" />
<hkern u1="&#x201a;" u2="T" k="83" />
<hkern u1="&#x201a;" u2="Q" k="33" />
<hkern u1="&#x201a;" u2="O" k="33" />
<hkern u1="&#x201a;" u2="G" k="33" />
<hkern u1="&#x201a;" u2="C" k="33" />
<hkern u1="&#x201a;" u2="A" k="-30" />
<hkern u1="&#x201a;" u2="&#x39;" k="3" />
<hkern u1="&#x201a;" u2="&#x38;" k="15" />
<hkern u1="&#x201a;" u2="&#x37;" k="8" />
<hkern u1="&#x201a;" u2="&#x36;" k="23" />
<hkern u1="&#x201a;" u2="&#x35;" k="-15" />
<hkern u1="&#x201a;" u2="&#x34;" k="13" />
<hkern u1="&#x201a;" u2="&#x33;" k="-8" />
<hkern u1="&#x201a;" u2="&#x32;" k="-8" />
<hkern u1="&#x201a;" u2="&#x31;" k="86" />
<hkern u1="&#x201a;" u2="&#x30;" k="18" />
<hkern u1="&#x201c;" u2="&#x178;" k="-30" />
<hkern u1="&#x201c;" u2="&#x153;" k="30" />
<hkern u1="&#x201c;" u2="&#xe7;" k="30" />
<hkern u1="&#x201c;" u2="&#xe6;" k="30" />
<hkern u1="&#x201c;" u2="&#xdd;" k="-30" />
<hkern u1="&#x201c;" u2="&#xc6;" k="40" />
<hkern u1="&#x201c;" u2="&#xc5;" k="40" />
<hkern u1="&#x201c;" u2="&#xc4;" k="40" />
<hkern u1="&#x201c;" u2="&#xc3;" k="40" />
<hkern u1="&#x201c;" u2="&#xc2;" k="40" />
<hkern u1="&#x201c;" u2="&#xc1;" k="40" />
<hkern u1="&#x201c;" u2="&#xc0;" k="40" />
<hkern u1="&#x201c;" u2="u" k="10" />
<hkern u1="&#x201c;" u2="s" k="30" />
<hkern u1="&#x201c;" u2="r" k="10" />
<hkern u1="&#x201c;" u2="q" k="30" />
<hkern u1="&#x201c;" u2="p" k="10" />
<hkern u1="&#x201c;" u2="o" k="30" />
<hkern u1="&#x201c;" u2="n" k="10" />
<hkern u1="&#x201c;" u2="m" k="10" />
<hkern u1="&#x201c;" u2="g" k="40" />
<hkern u1="&#x201c;" u2="e" k="30" />
<hkern u1="&#x201c;" u2="d" k="30" />
<hkern u1="&#x201c;" u2="c" k="30" />
<hkern u1="&#x201c;" u2="a" k="30" />
<hkern u1="&#x201c;" u2="Y" k="-30" />
<hkern u1="&#x201c;" u2="X" k="-20" />
<hkern u1="&#x201c;" u2="W" k="-20" />
<hkern u1="&#x201c;" u2="V" k="-20" />
<hkern u1="&#x201c;" u2="T" k="-10" />
<hkern u1="&#x201c;" u2="J" k="118" />
<hkern u1="&#x201c;" u2="A" k="40" />
<hkern u1="&#x201d;" u2="&#x153;" k="50" />
<hkern u1="&#x201d;" u2="&#x152;" k="40" />
<hkern u1="&#x201d;" u2="&#xe7;" k="50" />
<hkern u1="&#x201d;" u2="&#xe6;" k="50" />
<hkern u1="&#x201d;" u2="&#xd8;" k="40" />
<hkern u1="&#x201d;" u2="&#xd6;" k="40" />
<hkern u1="&#x201d;" u2="&#xd5;" k="40" />
<hkern u1="&#x201d;" u2="&#xd4;" k="40" />
<hkern u1="&#x201d;" u2="&#xd3;" k="40" />
<hkern u1="&#x201d;" u2="&#xd2;" k="40" />
<hkern u1="&#x201d;" u2="&#xc6;" k="38" />
<hkern u1="&#x201d;" u2="&#xc5;" k="38" />
<hkern u1="&#x201d;" u2="&#xc4;" k="38" />
<hkern u1="&#x201d;" u2="&#xc3;" k="38" />
<hkern u1="&#x201d;" u2="&#xc2;" k="38" />
<hkern u1="&#x201d;" u2="&#xc1;" k="38" />
<hkern u1="&#x201d;" u2="&#xc0;" k="38" />
<hkern u1="&#x201d;" u2="q" k="50" />
<hkern u1="&#x201d;" u2="o" k="50" />
<hkern u1="&#x201d;" u2="e" k="50" />
<hkern u1="&#x201d;" u2="d" k="50" />
<hkern u1="&#x201d;" u2="c" k="50" />
<hkern u1="&#x201d;" u2="a" k="50" />
<hkern u1="&#x201d;" u2="S" k="10" />
<hkern u1="&#x201d;" u2="Q" k="40" />
<hkern u1="&#x201d;" u2="O" k="40" />
<hkern u1="&#x201d;" u2="J" k="120" />
<hkern u1="&#x201d;" u2="G" k="40" />
<hkern u1="&#x201d;" u2="C" k="40" />
<hkern u1="&#x201d;" u2="A" k="38" />
<hkern u1="&#x201e;" u2="&#x178;" k="50" />
<hkern u1="&#x201e;" u2="&#x153;" k="20" />
<hkern u1="&#x201e;" u2="&#x152;" k="33" />
<hkern u1="&#x201e;" u2="&#xe7;" k="20" />
<hkern u1="&#x201e;" u2="&#xe6;" k="20" />
<hkern u1="&#x201e;" u2="&#xdd;" k="50" />
<hkern u1="&#x201e;" u2="&#xdc;" k="10" />
<hkern u1="&#x201e;" u2="&#xdb;" k="10" />
<hkern u1="&#x201e;" u2="&#xda;" k="10" />
<hkern u1="&#x201e;" u2="&#xd9;" k="10" />
<hkern u1="&#x201e;" u2="&#xd8;" k="33" />
<hkern u1="&#x201e;" u2="&#xd6;" k="33" />
<hkern u1="&#x201e;" u2="&#xd5;" k="33" />
<hkern u1="&#x201e;" u2="&#xd4;" k="33" />
<hkern u1="&#x201e;" u2="&#xd3;" k="33" />
<hkern u1="&#x201e;" u2="&#xd2;" k="33" />
<hkern u1="&#x201e;" u2="&#xc6;" k="-30" />
<hkern u1="&#x201e;" u2="&#xc5;" k="-30" />
<hkern u1="&#x201e;" u2="&#xc4;" k="-30" />
<hkern u1="&#x201e;" u2="&#xc3;" k="-30" />
<hkern u1="&#x201e;" u2="&#xc2;" k="-30" />
<hkern u1="&#x201e;" u2="&#xc1;" k="-30" />
<hkern u1="&#x201e;" u2="&#xc0;" k="-30" />
<hkern u1="&#x201e;" u2="y" k="30" />
<hkern u1="&#x201e;" u2="w" k="10" />
<hkern u1="&#x201e;" u2="v" k="30" />
<hkern u1="&#x201e;" u2="u" k="20" />
<hkern u1="&#x201e;" u2="t" k="40" />
<hkern u1="&#x201e;" u2="r" k="20" />
<hkern u1="&#x201e;" u2="q" k="20" />
<hkern u1="&#x201e;" u2="p" k="20" />
<hkern u1="&#x201e;" u2="o" k="20" />
<hkern u1="&#x201e;" u2="n" k="20" />
<hkern u1="&#x201e;" u2="m" k="20" />
<hkern u1="&#x201e;" u2="j" k="-30" />
<hkern u1="&#x201e;" u2="f" k="30" />
<hkern u1="&#x201e;" u2="e" k="20" />
<hkern u1="&#x201e;" u2="d" k="20" />
<hkern u1="&#x201e;" u2="c" k="20" />
<hkern u1="&#x201e;" u2="a" k="20" />
<hkern u1="&#x201e;" u2="Y" k="50" />
<hkern u1="&#x201e;" u2="W" k="28" />
<hkern u1="&#x201e;" u2="V" k="68" />
<hkern u1="&#x201e;" u2="U" k="10" />
<hkern u1="&#x201e;" u2="T" k="83" />
<hkern u1="&#x201e;" u2="Q" k="33" />
<hkern u1="&#x201e;" u2="O" k="33" />
<hkern u1="&#x201e;" u2="G" k="33" />
<hkern u1="&#x201e;" u2="C" k="33" />
<hkern u1="&#x201e;" u2="A" k="-30" />
<hkern u1="&#x201e;" u2="&#x39;" k="3" />
<hkern u1="&#x201e;" u2="&#x38;" k="15" />
<hkern u1="&#x201e;" u2="&#x37;" k="8" />
<hkern u1="&#x201e;" u2="&#x36;" k="23" />
<hkern u1="&#x201e;" u2="&#x35;" k="-15" />
<hkern u1="&#x201e;" u2="&#x34;" k="13" />
<hkern u1="&#x201e;" u2="&#x33;" k="-8" />
<hkern u1="&#x201e;" u2="&#x32;" k="-8" />
<hkern u1="&#x201e;" u2="&#x31;" k="86" />
<hkern u1="&#x201e;" u2="&#x30;" k="18" />
<hkern u1="&#x2026;" u2="&#x178;" k="50" />
<hkern u1="&#x2026;" u2="&#x153;" k="20" />
<hkern u1="&#x2026;" u2="&#x152;" k="33" />
<hkern u1="&#x2026;" u2="&#xe7;" k="20" />
<hkern u1="&#x2026;" u2="&#xe6;" k="20" />
<hkern u1="&#x2026;" u2="&#xdd;" k="50" />
<hkern u1="&#x2026;" u2="&#xdc;" k="10" />
<hkern u1="&#x2026;" u2="&#xdb;" k="10" />
<hkern u1="&#x2026;" u2="&#xda;" k="10" />
<hkern u1="&#x2026;" u2="&#xd9;" k="10" />
<hkern u1="&#x2026;" u2="&#xd8;" k="33" />
<hkern u1="&#x2026;" u2="&#xd6;" k="33" />
<hkern u1="&#x2026;" u2="&#xd5;" k="33" />
<hkern u1="&#x2026;" u2="&#xd4;" k="33" />
<hkern u1="&#x2026;" u2="&#xd3;" k="33" />
<hkern u1="&#x2026;" u2="&#xd2;" k="33" />
<hkern u1="&#x2026;" u2="&#xc6;" k="-30" />
<hkern u1="&#x2026;" u2="&#xc5;" k="-30" />
<hkern u1="&#x2026;" u2="&#xc4;" k="-30" />
<hkern u1="&#x2026;" u2="&#xc3;" k="-30" />
<hkern u1="&#x2026;" u2="&#xc2;" k="-30" />
<hkern u1="&#x2026;" u2="&#xc1;" k="-30" />
<hkern u1="&#x2026;" u2="&#xc0;" k="-30" />
<hkern u1="&#x2026;" u2="y" k="30" />
<hkern u1="&#x2026;" u2="w" k="10" />
<hkern u1="&#x2026;" u2="v" k="30" />
<hkern u1="&#x2026;" u2="u" k="20" />
<hkern u1="&#x2026;" u2="t" k="40" />
<hkern u1="&#x2026;" u2="r" k="20" />
<hkern u1="&#x2026;" u2="q" k="20" />
<hkern u1="&#x2026;" u2="p" k="20" />
<hkern u1="&#x2026;" u2="o" k="20" />
<hkern u1="&#x2026;" u2="n" k="20" />
<hkern u1="&#x2026;" u2="m" k="20" />
<hkern u1="&#x2026;" u2="f" k="30" />
<hkern u1="&#x2026;" u2="e" k="20" />
<hkern u1="&#x2026;" u2="d" k="20" />
<hkern u1="&#x2026;" u2="c" k="20" />
<hkern u1="&#x2026;" u2="a" k="20" />
<hkern u1="&#x2026;" u2="Y" k="50" />
<hkern u1="&#x2026;" u2="W" k="28" />
<hkern u1="&#x2026;" u2="V" k="68" />
<hkern u1="&#x2026;" u2="U" k="10" />
<hkern u1="&#x2026;" u2="T" k="83" />
<hkern u1="&#x2026;" u2="Q" k="33" />
<hkern u1="&#x2026;" u2="O" k="33" />
<hkern u1="&#x2026;" u2="G" k="33" />
<hkern u1="&#x2026;" u2="C" k="33" />
<hkern u1="&#x2026;" u2="A" k="-30" />
<hkern u1="&#x2026;" u2="&#x39;" k="3" />
<hkern u1="&#x2026;" u2="&#x38;" k="15" />
<hkern u1="&#x2026;" u2="&#x37;" k="8" />
<hkern u1="&#x2026;" u2="&#x36;" k="23" />
<hkern u1="&#x2026;" u2="&#x35;" k="-15" />
<hkern u1="&#x2026;" u2="&#x34;" k="13" />
<hkern u1="&#x2026;" u2="&#x33;" k="-8" />
<hkern u1="&#x2026;" u2="&#x32;" k="-8" />
<hkern u1="&#x2026;" u2="&#x31;" k="86" />
<hkern u1="&#x2026;" u2="&#x30;" k="18" />
<hkern u1="&#x2039;" u2="&#x178;" k="40" />
<hkern u1="&#x2039;" u2="&#xdd;" k="40" />
<hkern u1="&#x2039;" u2="f" k="-15" />
<hkern u1="&#x2039;" u2="Y" k="40" />
<hkern u1="&#x2039;" u2="W" k="20" />
<hkern u1="&#x2039;" u2="V" k="20" />
<hkern u1="&#x2039;" u2="T" k="40" />
<hkern u1="&#x203a;" u2="&#x178;" k="50" />
<hkern u1="&#x203a;" u2="&#xdd;" k="50" />
<hkern u1="&#x203a;" u2="z" k="35" />
<hkern u1="&#x203a;" u2="y" k="23" />
<hkern u1="&#x203a;" u2="x" k="50" />
<hkern u1="&#x203a;" u2="w" k="15" />
<hkern u1="&#x203a;" u2="v" k="23" />
<hkern u1="&#x203a;" u2="f" k="8" />
<hkern u1="&#x203a;" u2="Y" k="50" />
<hkern u1="&#x203a;" u2="W" k="40" />
<hkern u1="&#x203a;" u2="V" k="50" />
<hkern u1="&#x203a;" u2="T" k="110" />
</font>
</defs></svg> 