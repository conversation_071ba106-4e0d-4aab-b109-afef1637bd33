<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="omnesregular" horiz-adv-x="428" >
<font-face units-per-em="2048" ascent="1434" descent="-614" />
<missing-glyph horiz-adv-x="348" />
<glyph unicode="&#xfb01;" horiz-adv-x="1103" d="M20 909v13q0 27 14.5 40t49.5 13h143v90q0 168 73.5 257t213.5 89q93 0 144 -33.5t51 -81.5q0 -13 -3.5 -24.5t-9 -19t-11 -12.5t-10 -6.5t-5.5 -0.5q-24 24 -67 41.5t-89 17.5q-83 0 -120.5 -55.5t-37.5 -177.5v-84h197q35 0 49 -12.5t14 -40.5v-13q0 -28 -14 -40.5 t-49 -12.5h-195v-801q0 -35 -14.5 -49t-42.5 -14h-14q-60 0 -60 63v801h-143q-35 0 -49.5 13t-14.5 40zM803 1307q0 88 90 88q86 0 86 -88t-90 -88q-41 0 -63.5 22.5t-22.5 65.5zM823 55v865q0 63 60 63h14q59 0 59 -63v-865q0 -63 -59 -63h-14q-60 0 -60 63z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1103" d="M20 909v13q0 27 14.5 40t49.5 13h143v90q0 168 73.5 257t213.5 89q93 0 144 -33.5t51 -81.5q0 -13 -3.5 -24.5t-9 -19t-11 -12.5t-10 -6.5t-5.5 -0.5q-24 24 -67 41.5t-89 17.5q-83 0 -120.5 -55.5t-37.5 -177.5v-84h197q35 0 49 -12.5t14 -40.5v-13q0 -28 -14 -40.5 t-49 -12.5h-195v-801q0 -35 -14.5 -49t-42.5 -14h-14q-60 0 -60 63v801h-143q-35 0 -49.5 13t-14.5 40zM823 55v1325q0 2 6 5.5t16.5 6.5t22.5 3q88 0 88 -129v-1211q0 -63 -59 -63h-14q-60 0 -60 63z" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="348" />
<glyph unicode=" "  horiz-adv-x="348" />
<glyph unicode="&#x09;" horiz-adv-x="348" />
<glyph unicode="&#xa0;" horiz-adv-x="348" />
<glyph unicode="!" horiz-adv-x="505" d="M152 78v20q0 43 23.5 67.5t66.5 24.5h20q44 0 68 -24.5t24 -67.5v-20q0 -43 -24 -66.5t-68 -23.5h-20q-43 0 -66.5 23.5t-23.5 66.5zM168 1253q-3 80 59 80h52q62 0 59 -80l-45 -839q-2 -26 -8 -37.5t-25 -11.5h-16q-12 0 -18.5 6.5t-9 15.5t-3.5 27z" />
<glyph unicode="&#x22;" horiz-adv-x="731" d="M129 1221q-6 63 15 87.5t69 24.5t69 -24.5t15 -87.5l-39 -367q-3 -29 -10 -41t-25 -12h-20q-18 0 -25 12t-10 41zM432 1221q-6 63 15 87.5t69 24.5t69 -24.5t15 -87.5l-39 -367q-3 -29 -10 -41t-25 -12h-20q-18 0 -25 12t-10 41z" />
<glyph unicode="#" horiz-adv-x="1333" d="M20 412v10q0 55 64 55h227l113 371h-188q-64 0 -64 53v10q0 56 64 56h225l110 360q0 6 25 6q29 0 50.5 -20.5t21.5 -67.5q0 -24 -17 -80l-61 -198h338l110 360q0 6 25 6q29 0 50.5 -20.5t21.5 -67.5q0 -24 -17 -80l-61 -198h192q64 0 64 -54v-10q0 -55 -64 -55h-229 l-113 -371h191q63 0 63 -53v-10q0 -56 -63 -56h-228l-108 -360q0 -6 -25 -6q-29 0 -50 20.5t-21 67.5q0 27 16 80l59 198h-335l-111 -360q0 -6 -25 -6q-29 0 -50 20.5t-21 67.5q0 27 16 80l59 198h-190q-64 0 -64 54zM434 473h348l115 379h-346z" />
<glyph unicode="$" horiz-adv-x="1107" d="M76 236q0 27 20 48.5t39.5 30t22.5 4.5q48 -84 134 -141.5t200 -71.5v510q-65 17 -114.5 34.5t-102 47.5t-86.5 67.5t-56 93.5t-22 126q0 137 108 239t270 122v118q0 35 14 49.5t42 14.5h14q27 0 40 -14.5t13 -49.5v-116q165 -13 274 -86.5t109 -141.5q0 -27 -20 -48.5 t-39.5 -30.5t-22.5 -5q-42 74 -123.5 124.5t-183.5 60.5v-490q70 -17 123 -34.5t111 -48.5t95.5 -69.5t62 -98t24.5 -132.5q0 -149 -117.5 -253t-296.5 -118v-122q0 -35 -13.5 -49.5t-41.5 -14.5h-14q-28 0 -41 14.5t-13 49.5v125q-89 9 -168.5 38t-130.5 66.5t-80.5 77.5 t-29.5 74zM248 987q0 -96 61.5 -145.5t188.5 -83.5v463q-109 -15 -179.5 -80t-70.5 -154zM600 104q126 9 205.5 75t79.5 163q0 54 -21.5 95.5t-63.5 70t-88.5 47t-111.5 35.5v-486z" />
<glyph unicode="%" horiz-adv-x="1499" d="M76 1016v20q0 127 90 218.5t217 91.5q128 0 216.5 -89.5t88.5 -216.5v-20q0 -127 -90 -218t-217 -91q-128 0 -216.5 89t-88.5 216zM166 41q0 29 12.5 51.5t48.5 61.5l1030 1200q4 4 19.5 0t31 -19.5t15.5 -38.5q0 -42 -61 -112l-1031 -1200q-4 -4 -19.5 1t-30.5 20 t-15 36zM197 1014q0 -83 53.5 -140t132.5 -57t131.5 56t52.5 141v28q0 85 -53.5 142t-132.5 57q-80 0 -132 -56.5t-52 -142.5v-28zM811 297v20q0 127 90 218.5t217 91.5q128 0 216.5 -89t88.5 -216v-21q0 -127 -90 -218t-217 -91q-128 0 -216.5 89t-88.5 216zM932 295 q0 -83 53.5 -140t132.5 -57q80 0 132.5 56t52.5 141v29q0 85 -53.5 141.5t-133.5 56.5t-132 -56t-52 -142v-29z" />
<glyph unicode="&#x26;" horiz-adv-x="1333" d="M47 373q0 288 350 387q-116 130 -116 274q0 132 95 224t242 92q138 0 230 -86t92 -215q0 -236 -324 -330l355 -328q72 125 114 311q21 99 93 99q20 0 34.5 -9t20 -18t4.5 -14q-53 -263 -174 -451l285 -260q4 -4 -1 -17.5t-24.5 -26.5t-50.5 -13q-58 0 -103 43l-182 172 q-204 -232 -477 -232q-207 0 -335 105.5t-128 292.5zM190 379q0 -132 91 -208.5t237 -76.5q220 0 383 195l-428 391q-283 -81 -283 -301zM416 1036q0 -69 28.5 -124.5t87.5 -116.5q144 40 211.5 100t67.5 152q0 80 -55 134t-140 54q-95 0 -147.5 -56.5t-52.5 -142.5z" />
<glyph unicode="'" d="M129 1221q-6 63 15 87.5t69 24.5t69 -24.5t15 -87.5l-39 -367q-3 -29 -10 -41t-25 -12h-20q-18 0 -25 12t-10 41z" />
<glyph unicode="(" horiz-adv-x="718" d="M98 463q0 150 35.5 289t93.5 241.5t130 179.5t148 116.5t144 39.5q62 0 62 -49q0 -13 -7 -31t-12 -20q-138 -30 -243 -143t-158 -274.5t-53 -346.5q0 -192 53.5 -354.5t157.5 -274t243 -141.5q5 -2 12 -20t7 -31q0 -50 -62 -50q-69 0 -144.5 37.5t-147.5 113.5 t-130 177.5t-93.5 243t-35.5 297.5z" />
<glyph unicode=")" horiz-adv-x="718" d="M8 -356q0 13 7 31t12 20q139 30 243 141.5t157.5 274t53.5 354.5q0 185 -53 346.5t-158 274.5t-243 143q-5 2 -12 20t-7 31q0 49 62 49q68 0 144 -39.5t148 -116.5t130 -179.5t93.5 -241.5t35.5 -289q0 -156 -35.5 -297.5t-93.5 -243t-130 -177.5t-147.5 -113.5 t-144.5 -37.5q-62 0 -62 50z" />
<glyph unicode="*" horiz-adv-x="772" d="M50 1069.5q-7 12.5 1 34.5l2 8q9 36 62 25l215 -60l12 221q1 30 11.5 41t31.5 11h8q20 0 31 -11.5t12 -40.5l8 -221l218 60q51 10 63 -25l2 -8q7 -21 0 -34t-35 -23l-211 -80l125 -187q26 -45 -6 -67l-6 -4q-32 -23 -66 16l-141 176l-139 -176q-19 -22 -34.5 -25t-31.5 9 l-6 4q-32 22 -6 67l121 187l-207 80q-27 10 -34 22.5z" />
<glyph unicode="+" horiz-adv-x="1103" d="M96 543v12q0 27 14.5 40t49.5 13h329v334q0 64 58 64h10q57 0 57 -64v-334h330q35 0 49.5 -13t14.5 -40v-12q0 -28 -14.5 -41t-49.5 -13h-330v-356q0 -35 -14.5 -49t-42.5 -14h-10q-28 0 -43 14t-15 49v356h-329q-35 0 -49.5 13t-14.5 41z" />
<glyph unicode="," d="M31 -238l123 367q8 22 13 32t14 21t21 15t31 4h11q26 0 43.5 -19t17.5 -53v-8q0 -48 -33 -98l-174 -291q-6 -11 -26 -12t-34 10.5t-7 31.5z" />
<glyph unicode="-" horiz-adv-x="749" d="M92 487v15q0 55 64 55h438q63 0 63 -55v-15q0 -53 -63 -53h-438q-64 0 -64 53z" />
<glyph unicode="." d="M113 78v20q0 43 23.5 67.5t66.5 24.5h20q44 0 68 -24.5t24 -67.5v-20q0 -43 -24 -66.5t-68 -23.5h-20q-43 0 -66.5 23.5t-23.5 66.5z" />
<glyph unicode="/" horiz-adv-x="661" d="M20 -326q0 24 17 80l481 1573q0 6 25 6q86 0 86 -76q0 -23 -17 -79l-481 -1575q0 -6 -25 -6q-86 0 -86 77z" />
<glyph unicode="0" horiz-adv-x="1288" d="M115 592v137q0 287 142 454t386 167q246 0 388.5 -165.5t142.5 -451.5v-137q0 -286 -143.5 -453.5t-389.5 -167.5t-386 164.5t-140 452.5zM254 600q0 -236 103 -370t286 -134q186 0 288.5 131.5t102.5 368.5v129q0 237 -104.5 371.5t-288.5 134.5q-183 0 -285 -132.5 t-102 -369.5v-129z" />
<glyph unicode="1" horiz-adv-x="710" d="M43 1149q0 21 10.5 40t20.5 28.5t12 7.5q44 -51 117 -51q65 0 110.5 46t48.5 113h60q61 0 61 -63v-1215q0 -63 -61 -63h-19q-59 0 -59 63v1053q-53 -76 -151 -76q-66 0 -108 32.5t-42 84.5z" />
<glyph unicode="2" horiz-adv-x="1064" d="M86 84v41q0 60 12.5 113.5t32 95t52.5 80.5t64.5 67t77.5 58t82.5 50t88.5 46q157 80 205 112q127 85 143 189q2 16 2 33q0 118 -82 186t-225 68q-272 0 -353 -273q-1 -3 -10.5 -3.5t-23.5 3t-27.5 11t-23 24.5t-9.5 39q0 36 18 78.5t56 86.5t90.5 80.5t129 59.5 t163.5 23q201 0 317.5 -103.5t116.5 -281.5q0 -48 -12 -91.5t-30 -77t-50.5 -66.5t-61.5 -56t-76.5 -50.5t-80.5 -45t-88 -43.5q-50 -24 -81.5 -40.5t-74 -41.5t-69 -46t-55.5 -50.5t-45.5 -59.5t-27 -68t-10.5 -81v-23h723q66 0 66 -57v-11q0 -59 -66 -59h-770 q-41 0 -64.5 22t-23.5 62z" />
<glyph unicode="3" horiz-adv-x="1089" d="M35 266q0 33 19.5 54t39.5 26t21 0q42 -120 146.5 -185t246.5 -65q157 0 250.5 73t93.5 196q0 135 -91.5 205.5t-264.5 70.5h-115q-68 0 -68 55v9q0 55 64 55h141q144 0 219.5 61.5t75.5 173.5q0 110 -81.5 173t-223.5 63q-122 0 -212 -56.5t-136 -152.5q-2 -5 -21.5 2 t-39 27t-19.5 47q0 35 31 78t84.5 82t137 65.5t175.5 26.5q211 0 328.5 -93t117.5 -260q0 -221 -221 -286q126 -43 193 -131.5t67 -212.5q0 -182 -130 -287t-357 -105q-112 0 -206 31t-149.5 77.5t-85.5 94.5t-30 88z" />
<glyph unicode="4" horiz-adv-x="1189" d="M82 451v41q0 27 13 50.5t44 61.5l527 649q39 49 60.5 64.5t64.5 15.5q59 0 83.5 -22t24.5 -68v-739h180q64 0 64 -60v-10q0 -57 -64 -57h-180v-322q0 -63 -59 -63h-19q-24 0 -40.5 19t-16.5 44v322h-584q-45 0 -71.5 21t-26.5 53zM207 502h561v696z" />
<glyph unicode="5" horiz-adv-x="1081" d="M63 238q0 27 20 46.5t40 26.5t22 2q49 -99 145.5 -156t231.5 -57q160 0 256 81.5t96 217.5q0 135 -88 214t-239 79q-202 0 -338 -98q-88 11 -88 96v557q0 32 23 55t55 23h684q63 0 63 -59v-11q0 -57 -63 -57h-629v-446q138 69 295 69q210 0 337.5 -113.5t127.5 -301.5 q0 -198 -133.5 -314.5t-362.5 -116.5q-97 0 -184.5 26t-145 65.5t-91.5 85.5t-34 86z" />
<glyph unicode="6" horiz-adv-x="1177" d="M115 580v149q0 289 141.5 455t384.5 166q76 0 143 -14.5t113 -38t79 -52.5t48.5 -58t15.5 -54q0 -22 -18 -42t-36.5 -29t-20.5 -5q-117 174 -324 174q-183 0 -286 -129.5t-103 -364.5v-67q72 69 176.5 114t214.5 45q206 0 329.5 -109t123.5 -292v-10q0 -191 -132 -317 t-333 -126q-167 0 -284.5 77t-174.5 212t-57 316zM254 528q11 -205 106 -319.5t263 -114.5q149 0 243.5 87t94.5 225v12q0 141 -85.5 218t-244.5 77q-109 0 -211.5 -52.5t-165.5 -132.5z" />
<glyph unicode="7" horiz-adv-x="1060" d="M53 1249v11q0 30 17 47.5t47 17.5h823q80 0 80 -61v-13q0 -26 -37 -96l-594 -1114q-25 -49 -74 -49q-22 0 -41 12.5t-28 26t-6 18.5l618 1139h-741q-30 0 -47 16.5t-17 44.5z" />
<glyph unicode="8" horiz-adv-x="1165" d="M86 346q0 117 70 205.5t200 130.5q-231 75 -231 303q0 162 125.5 263.5t333.5 101.5q212 0 333 -97.5t121 -263.5q0 -108 -59 -187.5t-172 -117.5q133 -40 201.5 -128.5t68.5 -207.5q0 -163 -137.5 -268t-357.5 -105q-227 0 -361.5 102t-134.5 269zM225 354 q0 -116 96 -188t263 -72q174 0 264 68t90 192q0 122 -94.5 194.5t-261.5 72.5q-170 0 -263.5 -72t-93.5 -195zM264 981q0 -109 85 -174.5t235 -65.5q152 0 233.5 63t81.5 177q0 115 -83 183.5t-234 68.5q-157 0 -237.5 -66.5t-80.5 -185.5z" />
<glyph unicode="9" horiz-adv-x="1177" d="M82 877v10q0 126 59.5 232t167 168.5t238.5 62.5q243 0 379.5 -160.5t136.5 -444.5v-149q0 -307 -137 -464t-400 -157q-94 0 -173.5 22.5t-126.5 56t-73 70.5t-26 69q0 22 18.5 41.5t37 28.5t20.5 5q117 -174 323 -174q201 0 300.5 121t99.5 373v45 q-72 -69 -176.5 -113.5t-214.5 -44.5q-206 0 -329.5 109.5t-123.5 292.5zM217 887q0 -141 85.5 -218t244.5 -77q107 0 211 52.5t166 131.5q-8 215 -103 335t-266 120q-147 0 -242.5 -96t-95.5 -236v-12z" />
<glyph unicode=":" horiz-adv-x="489" d="M143 78v20q0 43 23.5 67.5t66.5 24.5h21q44 0 68 -24.5t24 -67.5v-20q0 -43 -24 -66.5t-68 -23.5h-21q-43 0 -66.5 23.5t-23.5 66.5zM143 774v21q0 43 23.5 67.5t66.5 24.5h21q44 0 68 -24.5t24 -67.5v-21q0 -43 -24 -66.5t-68 -23.5h-21q-43 0 -66.5 23.5t-23.5 66.5z " />
<glyph unicode=";" horiz-adv-x="489" d="M61 -238l123 367q8 21 13 31.5t14 21.5t21.5 15t31.5 4h10q26 0 44 -19t18 -53v-8q0 -48 -33 -98l-174 -291q-6 -11 -26 -12t-34.5 10.5t-7.5 31.5zM143 774v21q0 43 23.5 67.5t66.5 24.5h21q44 0 68 -24.5t24 -67.5v-21q0 -43 -24 -66.5t-68 -23.5h-21q-43 0 -66.5 23.5 t-23.5 66.5z" />
<glyph unicode="&#x3c;" horiz-adv-x="958" d="M100 549q0 41 9 57.5t32 28.5l670 360q4 1 13.5 -6.5t19.5 -23t12 -33.5q7 -53 -84 -101l-541 -282l541 -281q88 -46 84 -102q-2 -20 -11.5 -36t-19.5 -23t-14 -5l-670 359q-23 12 -32 28.5t-9 59.5z" />
<glyph unicode="=" horiz-adv-x="1103" d="M117 299v14q0 28 14 42t49 14h744q35 0 49 -14t14 -42v-14q0 -28 -14 -41.5t-49 -13.5h-744q-35 0 -49 13.5t-14 41.5zM117 764v14q0 28 14 42t49 14h744q35 0 49 -14t14 -42v-14q0 -28 -14 -41.5t-49 -13.5h-744q-35 0 -49 13.5t-14 41.5z" />
<glyph unicode="&#x3e;" horiz-adv-x="956" d="M100 166q-4 56 84 102l539 281l-539 282q-91 48 -84 101q2 18 12 33.5t19.5 23t13.5 6.5l670 -360q23 -12 32 -28.5t9 -57.5q0 -43 -9 -59.5t-32 -28.5l-670 -359q-4 -2 -14 5t-19.5 23t-11.5 36z" />
<glyph unicode="?" horiz-adv-x="948" d="M74 1030q0 33 14 72.5t47 84t79 80.5t118 59.5t157 23.5q181 0 289.5 -95t108.5 -241q0 -68 -22.5 -121.5t-59 -89t-80 -66.5t-87 -62.5t-80 -67t-59 -89t-22.5 -121.5q0 -35 -57 -35q-28 0 -43.5 11t-14.5 33q3 83 26 148.5t57.5 106.5t75 74t81 59t74 53.5t54 66 t20.5 87.5q0 104 -70 165t-195 61q-76 0 -135 -24t-94 -65.5t-54.5 -88t-27.5 -101.5q-1 -6 -26 -0.5t-49.5 27t-24.5 55.5zM326 78v20q0 43 23.5 67.5t66.5 24.5h20q44 0 68 -24.5t24 -67.5v-20q0 -43 -24 -66.5t-68 -23.5h-20q-43 0 -66.5 23.5t-23.5 66.5z" />
<glyph unicode="@" horiz-adv-x="1527" d="M98 662q0 302 189 495t485 193q305 0 485.5 -167t180.5 -452q0 -191 -75 -294t-216 -103q-86 0 -144 36t-73 99q-72 -131 -217 -131q-120 0 -198.5 87t-78.5 224t78.5 223t202.5 86q61 0 113.5 -28.5t84.5 -71.5q8 96 84 96q13 0 25 -3.5t17.5 -8t5.5 -6.5v-330 q0 -84 29 -128t98 -44q83 0 122 81t39 216q0 237 -153.5 378.5t-409.5 141.5q-252 0 -411.5 -164.5t-159.5 -424.5q0 -255 167.5 -421.5t426.5 -166.5q93 0 179 24.5t146 63.5q39 24 70 24q17 0 32.5 -15t21.5 -31.5t1 -20.5q-73 -60 -194.5 -102t-255.5 -42 q-313 0 -505 189t-192 498zM573 649q0 -87 47.5 -141.5t122.5 -54.5q71 0 121.5 46.5t50.5 118.5v107q-23 54 -66.5 86.5t-103.5 32.5q-78 0 -125 -53.5t-47 -141.5z" />
<glyph unicode="A" horiz-adv-x="1247" d="M33 33l485 1253q11 27 34 39t71 12t70.5 -12t33.5 -39l485 -1253q2 -5 -8.5 -15t-30.5 -18t-40 -8q-61 0 -84 63l-146 377h-569l-144 -377q-23 -63 -79 -63q-19 0 -38.5 8t-30.5 18t-9 15zM379 555h479l-240 623z" />
<glyph unicode="B" horiz-adv-x="1234" d="M158 78v1169q0 32 23 55t55 23h423q211 0 322 -89.5t111 -258.5q0 -104 -59 -178t-171 -111q143 -31 215 -116t72 -205q0 -367 -477 -367h-436q-32 0 -55 23t-23 55zM297 125h373q179 0 257.5 58t78.5 190q0 237 -328 237h-381v-485zM297 733h369q127 0 204.5 65.5 t77.5 172.5q0 229 -289 229h-362v-467z" />
<glyph unicode="C" horiz-adv-x="1351" d="M102 659q0 201 84 358.5t235.5 245t344.5 87.5q90 0 172.5 -20t139 -50.5t99 -66.5t63 -69t20.5 -57q0 -32 -20 -53.5t-39.5 -27.5t-22.5 -2q-61 105 -176.5 165t-239.5 60q-227 0 -371.5 -158t-144.5 -407q0 -247 147 -407.5t373 -160.5q149 0 266 70.5t162 189.5 q1 5 23.5 -1t44.5 -27.5t22 -53.5q0 -27 -21 -64t-65.5 -78.5t-104.5 -76t-148 -57.5t-185 -23q-290 0 -474 191t-184 493z" />
<glyph unicode="D" horiz-adv-x="1376" d="M158 78v1169q0 32 23 55t55 23h313q343 0 534 -173.5t191 -485.5q0 -313 -194.5 -489.5t-540.5 -176.5h-303q-32 0 -55 23t-23 55zM297 123h250q279 0 431 141t152 398q0 254 -153.5 397t-427.5 143h-252v-1079z" />
<glyph unicode="E" horiz-adv-x="1179" d="M158 78v1169q0 32 23 55t55 23h792q64 0 64 -57v-13q0 -57 -64 -57h-731v-457h586q63 0 63 -57v-8q0 -58 -63 -58h-586v-491h743q64 0 64 -55v-13q0 -59 -64 -59h-804q-32 0 -55 23t-23 55z" />
<glyph unicode="F" horiz-adv-x="1091" d="M158 55v1192q0 32 23 55t55 23h741q63 0 63 -57v-13q0 -57 -63 -57h-680v-500h514q63 0 63 -57v-8q0 -58 -63 -58h-514v-520q0 -63 -61 -63h-19q-59 0 -59 63z" />
<glyph unicode="G" horiz-adv-x="1445" d="M102 659q0 201 84 358.5t235.5 245t344.5 87.5q90 0 172.5 -20t139 -50.5t99 -66.5t63 -69t20.5 -57q0 -35 -20.5 -55.5t-40.5 -24.5t-23 1q-61 105 -175 163t-239 58q-229 0 -372.5 -157.5t-143.5 -407.5q0 -165 58.5 -292.5t172.5 -201.5t268 -74q179 0 301.5 93 t145.5 245v117h-373q-76 0 -76 57v8q0 60 76 60h393q44 0 73.5 -30t29.5 -73v-567q0 -2 -5.5 -5.5t-16.5 -6t-23 -2.5q-39 0 -58.5 27.5t-19.5 105.5v84q-64 -110 -185 -172t-280 -62q-188 0 -331 88.5t-218.5 243.5t-75.5 352z" />
<glyph unicode="H" horiz-adv-x="1390" d="M158 55v1215q0 63 59 63h19q61 0 61 -63v-543h797v543q0 63 59 63h18q62 0 62 -63v-1215q0 -63 -62 -63h-18q-59 0 -59 63v545h-797v-545q0 -63 -61 -63h-19q-59 0 -59 63z" />
<glyph unicode="I" horiz-adv-x="495" d="M178 55v1215q0 63 60 63h18q61 0 61 -63v-1215q0 -63 -61 -63h-18q-60 0 -60 63z" />
<glyph unicode="J" horiz-adv-x="894" d="M53 168q0 25 20 47t40 32t22 5q32 -70 94.5 -113t143.5 -43q132 0 188.5 74.5t56.5 247.5v852q0 63 62 63h18q60 0 60 -63v-852q0 -226 -93.5 -334.5t-291.5 -108.5q-129 0 -224.5 64.5t-95.5 128.5z" />
<glyph unicode="K" horiz-adv-x="1138" d="M158 55v1215q0 63 59 63h19q61 0 61 -63v-576l606 596q26 26 43 34.5t37 8.5q32 0 49.5 -18t20.5 -35t0 -20l-602 -580l616 -617q1 -1 1 -5q0 -5 -2 -15q-4 -17 -23.5 -34t-51.5 -17q-43 0 -76 33l-618 637v-607q0 -63 -61 -63h-19q-59 0 -59 63z" />
<glyph unicode="L" horiz-adv-x="1064" d="M158 78v1192q0 63 59 63h19q61 0 61 -63v-1141h639q63 0 63 -59v-11q0 -59 -63 -59h-700q-32 0 -55 23t-23 55z" />
<glyph unicode="M" horiz-adv-x="1517" d="M158 55v1190q0 38 28 63t72 25h33q50 0 72.5 -22t48.5 -82l348 -772l350 772q26 60 48.5 82t72.5 22h29q44 0 72 -25t28 -63v-1190q0 -63 -60 -63h-16q-57 0 -57 63v1119l-348 -756q-21 -45 -47 -66.5t-74 -21.5q-49 0 -74.5 21t-46.5 67l-346 756v-1119q0 -63 -58 -63 h-20q-55 0 -55 63z" />
<glyph unicode="N" horiz-adv-x="1382" d="M158 55v1184q0 40 20 67t55 27h35q35 0 57 -16.5t50 -56.5l719 -1043v1053q0 33 13.5 48t43.5 15h18q56 0 56 -63v-1184q0 -42 -19.5 -68t-52.5 -26h-10q-35 0 -51 10.5t-39 42.5l-764 1104v-1094q0 -63 -58 -63h-18q-55 0 -55 63z" />
<glyph unicode="O" horiz-adv-x="1523" d="M102 659q0 200 84.5 358t235.5 245.5t342 87.5q192 0 342 -86.5t232.5 -242.5t82.5 -357t-83.5 -358t-234.5 -244t-343 -87q-292 0 -475 190t-183 494zM246 664q0 -249 146 -408.5t372 -159.5t370 157t144 406q0 163 -66.5 293.5t-184.5 203.5t-267 73q-226 0 -370 -158 t-144 -407z" />
<glyph unicode="P" horiz-adv-x="1132" d="M158 55v1192q0 32 23 55t55 23h339q232 0 360 -104.5t128 -296.5q0 -199 -128 -303.5t-376 -104.5h-262v-461q0 -28 -15.5 -45.5t-45.5 -17.5h-19q-59 0 -59 63zM297 639h274q172 0 260.5 72t88.5 209q0 138 -86.5 209t-255.5 71h-281v-561z" />
<glyph unicode="Q" horiz-adv-x="1523" d="M102 659q0 200 84.5 358t235.5 245.5t342 87.5q192 0 342 -86.5t232.5 -242.5t82.5 -357q0 -274 -166 -467l158 -158q4 -4 -4.5 -19t-29 -30t-46.5 -15q-34 0 -69 35l-99 99q-177 -134 -405 -134q-292 0 -475 190t-183 494zM246 664q0 -249 146 -408.5t372 -159.5 q181 0 313 99l-272 276q-21 21 -25 45.5t13 42.5l14 14q20 18 42.5 14.5t43.5 -24.5l270 -278q115 146 115 374q0 163 -66.5 293.5t-184.5 203.5t-267 73q-226 0 -370 -158t-144 -407z" />
<glyph unicode="R" horiz-adv-x="1161" d="M158 55v1192q0 32 23 55t55 23h364q233 0 355 -97t122 -286q0 -181 -113.5 -281.5t-326.5 -103.5l424 -500q4 -6 -5 -21t-31 -29.5t-48 -14.5q-45 0 -86 49l-430 516h-166v-502q0 -27 -16 -45t-46 -18h-16q-59 0 -59 63zM295 676h317q160 0 241 70t81 192 q0 129 -83 195.5t-247 66.5h-309v-524z" />
<glyph unicode="S" horiz-adv-x="1107" d="M76 236q0 27 20 48.5t39.5 30t22.5 4.5q59 -100 160.5 -161.5t240.5 -61.5q154 0 240 65t86 181q0 36 -11 67t-27.5 54t-46 43t-56 33t-69.5 27t-74 22t-81 20t-86.5 22t-81.5 27t-77 36t-64.5 47.5t-52.5 62.5t-32.5 80.5t-12.5 101.5q0 157 126.5 261t317.5 104 q97 0 183 -23.5t139.5 -59t84.5 -75t31 -72.5q0 -27 -20 -48.5t-39.5 -30.5t-22.5 -5q-48 85 -141.5 139t-212.5 54q-138 0 -224.5 -66.5t-86.5 -173.5q0 -31 5.5 -57.5t20 -47.5t28.5 -37t41 -30.5t48 -24t59.5 -20.5t64.5 -17.5t73 -17.5q73 -17 127 -34t115.5 -48.5 t100.5 -70.5t65 -99.5t26 -136.5q0 -169 -126 -271t-335 -102q-105 0 -199.5 27t-154.5 67.5t-95.5 85t-35.5 81.5z" />
<glyph unicode="T" horiz-adv-x="1196" d="M49 1255v11q0 59 64 59h970q64 0 64 -59v-11q0 -57 -64 -57h-415v-1143q0 -63 -62 -63h-18q-60 0 -60 63v1143h-415q-64 0 -64 57z" />
<glyph unicode="U" horiz-adv-x="1308" d="M137 522v748q0 63 60 63h18q61 0 61 -63v-738q0 -432 379 -432q192 0 285.5 108t93.5 327v735q0 63 58 63h24q55 0 55 -63v-742q0 -268 -134 -410.5t-386 -142.5q-251 0 -382.5 140t-131.5 407z" />
<glyph unicode="V" horiz-adv-x="1228" d="M37 1292q-2 5 9 15t31 18t40 8q61 0 84 -63l415 -1125l418 1125q23 63 80 63q19 0 38.5 -8t30.5 -18t9 -15l-485 -1253q-11 -28 -30.5 -39.5t-62.5 -11.5q-42 0 -61.5 11.5t-30.5 39.5z" />
<glyph unicode="W" horiz-adv-x="1851" d="M113 1303q-1 4 7.5 11t27 13t40.5 6q35 0 55 -20.5t31 -65.5l254 -1122l291 948q13 40 36.5 59t74.5 19q50 0 71.5 -17.5t36.5 -60.5l287 -944l260 1118q11 47 27.5 66.5t50.5 19.5q22 0 40.5 -6t27.5 -13t8 -11l-307 -1260q-6 -32 -29 -44.5t-80 -12.5q-49 0 -70 13.5 t-30 45.5l-291 969l-309 -969q-10 -33 -28.5 -46t-66.5 -13q-56 0 -79 12t-29 45z" />
<glyph unicode="X" horiz-adv-x="1191" d="M53 57l461 611l-444 600q-4 6 3 21t26.5 29.5t43.5 14.5q51 0 78 -41l379 -526l395 530q33 43 68 43q21 0 40 -14.5t27 -30.5q5 -10 4 -15q0 -3 -1 -5l-457 -606l457 -605q1 -2 1 -5q0 -6 -4 -16q-8 -15 -27.5 -29.5t-43.5 -14.5q-48 0 -78 41l-391 534l-400 -538 q-33 -43 -67 -43q-21 0 -40 14t-27 30q-5 10 -4 16q0 3 1 5z" />
<glyph unicode="Y" horiz-adv-x="1124" d="M20 1274q-1 2 -1 4q0 6 6 16q8 13 29.5 26t49.5 13q44 0 70 -47l389 -608l387 606q32 49 70 49q26 0 47 -13t29 -26q7 -10 7 -16q0 -2 -1 -4l-471 -723v-496q0 -63 -62 -63h-18q-59 0 -59 63v496z" />
<glyph unicode="Z" horiz-adv-x="1236" d="M111 78v18q0 32 15.5 61.5t66.5 92.5l761 948h-778q-63 0 -63 57v11q0 59 63 59h821q57 0 84 -20.5t27 -55.5v-18q0 -48 -80 -148l-768 -956h821q64 0 64 -57v-11q0 -59 -64 -59h-858q-57 0 -84.5 21.5t-27.5 56.5z" />
<glyph unicode="[" horiz-adv-x="661" d="M162 -326v1573q0 32 23 55t55 23h315q35 0 49 -12.5t14 -40.5v-15q0 -28 -14 -40.5t-49 -12.5h-266v-1487h266q35 0 49 -12.5t14 -40.5v-14q0 -28 -14 -40.5t-49 -12.5h-315q-32 0 -55 22.5t-23 54.5z" />
<glyph unicode="\" horiz-adv-x="661" d="M33 1257q0 76 86 76q24 0 24 -6l482 -1573q16 -53 16 -80q0 -77 -86 -77q-25 0 -25 6l-481 1575q-16 53 -16 79z" />
<glyph unicode="]" horiz-adv-x="661" d="M43 -336q0 28 14 40.5t49 12.5h267v1487h-267q-35 0 -49 12.5t-14 40.5v15q0 28 14 40.5t49 12.5h316q32 0 55 -23t23 -55v-1573q0 -32 -23 -54.5t-55 -22.5h-316q-35 0 -49 12.5t-14 40.5v14z" />
<glyph unicode="^" horiz-adv-x="1024" d="M244 961q0 34 40.5 88t90 100t91 79.5t46.5 33.5t46 -33.5t90.5 -79.5t89.5 -100t40 -88q0 -22 -11.5 -35t-31.5 -13q-33 0 -81.5 50.5t-143.5 179.5q-97 -132 -143 -181t-78 -49q-20 0 -32.5 13t-12.5 35z" />
<glyph unicode="_" horiz-adv-x="952" d="M31 -59q0 59 63 59h764q64 0 64 -59v-11q0 -57 -64 -57h-764q-63 0 -63 57v11z" />
<glyph unicode="`" horiz-adv-x="1024" d="M246 1372q0 24 18 39.5t43 15.5q46 0 94 -36q60 -42 124 -105t100 -108t32 -50q-5 -6 -58.5 9.5t-133.5 52.5t-143 80q-76 52 -76 102z" />
<glyph unicode="a" horiz-adv-x="1034" d="M76 250q0 167 166.5 245.5t511.5 86.5h20v34q0 126 -71 193.5t-203 67.5q-112 0 -190.5 -50t-121.5 -131q0 -3 -19 2t-37.5 24.5t-18.5 51.5q0 28 26.5 65t74 73.5t126.5 61.5t170 25q186 0 290.5 -100.5t104.5 -277.5v-615q0 -2 -6 -5.5t-16.5 -6t-22.5 -2.5 q-39 0 -61.5 28t-22.5 101v37q-59 -86 -153.5 -133.5t-210.5 -47.5q-158 0 -247 74t-89 199zM211 250q0 -74 57 -118t156 -44q151 0 250.5 84.5t99.5 214.5v90l-57 -2q-259 -9 -382.5 -61t-123.5 -164z" />
<glyph unicode="b" horiz-adv-x="1165" d="M137 55v1325q0 2 6 5.5t16.5 6.5t22.5 3q88 0 88 -129v-451q138 184 363 184q197 0 329 -141t132 -369t-132.5 -371t-336.5 -143q-125 0 -218.5 50t-138.5 125v-95q0 -35 -14.5 -49t-42.5 -14h-14q-60 0 -60 63zM268 422q0 -138 103 -234t243 -96q155 0 249.5 109 t94.5 286q0 172 -96 284t-244 112q-124 0 -213.5 -61.5t-136.5 -170.5v-229z" />
<glyph unicode="c" horiz-adv-x="1060" d="M72 489q0 221 142.5 365.5t358.5 144.5q101 0 185 -29.5t133 -73t75.5 -88t26.5 -79.5q0 -27 -22 -44.5t-41.5 -19t-24.5 0.5q-91 215 -332 215q-157 0 -261.5 -111t-104.5 -281t104 -282.5t262 -112.5q241 0 332 215q5 2 24.5 0.5t41.5 -19t22 -44.5q0 -35 -26.5 -79.5 t-75.5 -88.5t-133 -73.5t-185 -29.5q-216 0 -358.5 146t-142.5 368z" />
<glyph unicode="d" horiz-adv-x="1165" d="M72 485q0 228 132.5 371t336.5 143q122 0 215 -49.5t139 -122.5v553q0 2 6 5.5t16.5 6.5t22.5 3q88 0 88 -129v-1211q0 -63 -59 -63h-15q-28 0 -42.5 14t-14.5 49v107q-138 -187 -365 -187q-197 0 -328.5 141t-131.5 369zM207 487q0 -172 96 -283.5t244 -111.5 q136 0 240 90.5t110 223.5v247q-100 230 -346 230q-155 0 -249.5 -109.5t-94.5 -286.5z" />
<glyph unicode="e" horiz-adv-x="1087" d="M72 483q0 223 139.5 369.5t351.5 146.5q206 0 330.5 -134t124.5 -357v-12q0 -27 -11.5 -36t-43.5 -9h-758q8 -157 111 -257t259 -100q131 0 207.5 46.5t126.5 127.5q3 4 21.5 -2t37.5 -25.5t19 -47.5q0 -27 -28 -63t-78 -71t-131.5 -59.5t-174.5 -24.5q-223 0 -363 142 t-140 366zM209 565h668q-9 145 -93.5 231.5t-220.5 86.5q-137 0 -234.5 -88.5t-119.5 -229.5z" />
<glyph unicode="f" horiz-adv-x="675" d="M20 909v13q0 27 14.5 40t49.5 13h143v90q0 168 73.5 257t213.5 89q93 0 144 -33.5t51 -81.5q0 -13 -3.5 -24.5t-9 -19t-11 -12.5t-10 -6.5t-5.5 -0.5q-24 24 -67 41.5t-89 17.5q-83 0 -120.5 -55.5t-37.5 -177.5v-84h197q35 0 49 -12.5t14 -40.5v-13q0 -28 -14 -40.5 t-49 -12.5h-195v-801q0 -35 -14.5 -49t-42.5 -14h-14q-60 0 -60 63v801h-143q-35 0 -49.5 13t-14.5 40z" />
<glyph unicode="g" horiz-adv-x="964" d="M2 -166q0 106 87.5 180t227.5 101q-43 34 -43 94q0 50 43 96q-111 38 -177 126t-66 200q0 154 113 261t282 107q137 0 246 -75l67 108q23 33 43.5 47.5t51.5 14.5q25 0 44 -14.5t26.5 -28.5t4.5 -17l-161 -180q79 -95 79 -215q0 -151 -115 -254.5t-286 -103.5 q-37 0 -53 2q-15 -23 -15 -50q0 -14 5 -25t18 -20t27 -15.5t40.5 -14t48.5 -12.5t61.5 -14t70.5 -17q46 -12 83.5 -25.5t76.5 -35t65.5 -48t43.5 -64.5t17 -83q0 -137 -131.5 -214t-365.5 -77q-233 0 -346 65.5t-113 200.5zM131 -166q0 -77 85 -117.5t249 -40.5 q169 0 264.5 46.5t95.5 132.5q0 22 -9 40.5t-22 32.5t-36.5 27t-43.5 21t-53.5 17.5t-56 15t-60.5 14.5t-59 14q-96 -5 -174.5 -29t-129 -69.5t-50.5 -104.5zM209 635q0 -110 73 -180t189 -70q117 0 189.5 69.5t72.5 180.5t-73.5 182.5t-192.5 71.5q-114 0 -186 -70 t-72 -184z" />
<glyph unicode="h" horiz-adv-x="1116" d="M137 55v1325q0 2 6 5.5t16.5 6.5t22.5 3q88 0 88 -129v-447q118 180 330 180q189 0 288 -115.5t99 -314.5v-514q0 -63 -59 -63h-15q-59 0 -59 63v506q0 148 -69.5 230.5t-192.5 82.5q-145 0 -233.5 -90.5t-88.5 -242.5v-486q0 -63 -59 -63h-14q-60 0 -60 63z" />
<glyph unicode="i" d="M127 1307q0 88 90 88q86 0 86 -88t-90 -88q-41 0 -63.5 22.5t-22.5 65.5zM147 55v865q0 63 60 63h14q60 0 60 -63v-865q0 -63 -60 -63h-14q-60 0 -60 63z" />
<glyph unicode="j" d="M-172 -303q0 19 9 38t18.5 28.5t11.5 7.5q62 -62 143 -62q73 0 105 42.5t32 139.5v1029q0 63 60 63h14q60 0 60 -63v-1039q0 -140 -69 -215.5t-200 -75.5q-82 0 -133 33t-51 74zM127 1307q0 88 90 88q86 0 86 -88t-90 -88q-41 0 -63.5 22.5t-22.5 65.5z" />
<glyph unicode="k" horiz-adv-x="907" d="M137 55v1325q0 2 6 5.5t16.5 6.5t22.5 3q88 0 88 -129v-738l394 402q50 53 90 53q15 0 27.5 -6t20 -15t12.5 -18.5t5.5 -16.5t-0.5 -9l-403 -402l467 -455q1 -1 1 -5q0 -6 -4 -16q-7 -16 -25.5 -32t-43.5 -16q-35 0 -86 51l-455 453v-441q0 -63 -59 -63h-14q-60 0 -60 63 z" />
<glyph unicode="l" d="M147 55v1325q0 2 6 5.5t17 6.5t23 3q88 0 88 -129v-1211q0 -63 -60 -63h-14q-60 0 -60 63z" />
<glyph unicode="m" horiz-adv-x="1751" d="M137 55v914q0 2 6 5.5t16.5 6t22.5 2.5q86 0 86 -129v-25q54 77 133.5 123.5t173.5 46.5q117 0 201 -53t123 -155q53 94 141.5 151t204.5 57q185 0 281 -104.5t96 -306.5v-533q0 -63 -59 -63h-15q-59 0 -59 63v525q0 294 -252 294q-90 0 -169 -53.5t-122 -142.5v-623 q0 -63 -59 -63h-15q-59 0 -59 63v514q0 305 -252 305q-90 0 -169 -53.5t-122 -142.5v-623q0 -63 -59 -63h-14q-60 0 -60 63z" />
<glyph unicode="n" horiz-adv-x="1116" d="M137 55v914q0 2 6 5.5t16.5 6t22.5 2.5q86 0 86 -129v-33q54 82 138.5 130t193.5 48q189 0 288 -115.5t99 -314.5v-514q0 -63 -59 -63h-15q-59 0 -59 63v506q0 147 -69 230t-193 83q-111 0 -194.5 -55.5t-127.5 -150.5v-613q0 -63 -59 -63h-14q-60 0 -60 63z" />
<glyph unicode="o" horiz-adv-x="1150" d="M72 489q0 222 142 366t361 144q218 0 360 -145.5t142 -368.5q0 -221 -142 -365.5t-360 -144.5q-219 0 -361 145.5t-142 368.5zM207 489q0 -170 105 -283.5t263 -113.5q157 0 262 111.5t105 281.5q0 171 -104.5 284.5t-262.5 113.5t-263 -112t-105 -282z" />
<glyph unicode="p" horiz-adv-x="1165" d="M137 -340v1309q0 2 6 5.5t16.5 6t22.5 2.5q86 0 86 -129v-43q137 188 365 188q197 0 329 -141t132 -369t-132.5 -371t-336.5 -143q-122 0 -215.5 54.5t-139.5 132.5v-502q0 -63 -59 -63h-14q-60 0 -60 63zM268 422q0 -138 103 -234t243 -96q155 0 249.5 109t94.5 286 q0 172 -96 284t-244 112q-124 0 -213.5 -61.5t-136.5 -170.5v-229z" />
<glyph unicode="q" horiz-adv-x="1165" d="M72 485q0 228 132.5 371t336.5 143q123 0 217 -50.5t139 -123.5v29q0 129 86 129q12 0 22.5 -2.5t16.5 -6t6 -5.5v-1309q0 -63 -59 -63h-15q-59 0 -59 63v500q-137 -185 -363 -185q-197 0 -328.5 141t-131.5 369zM207 487q0 -172 96 -283.5t244 -111.5q139 0 244.5 97.5 t105.5 234.5v225q-48 110 -134.5 172t-211.5 62q-155 0 -249.5 -109.5t-94.5 -286.5z" />
<glyph unicode="r" horiz-adv-x="692" d="M137 55v914q0 2 6 5.5t16.5 6t22.5 2.5q86 0 86 -129v-57q62 202 258 202q67 0 104.5 -26.5t37.5 -73.5q0 -22 -8.5 -40t-17.5 -25t-11 -5q-48 41 -119 41q-84 0 -139.5 -57t-79 -151t-23.5 -224v-383q0 -63 -59 -63h-14q-60 0 -60 63z" />
<glyph unicode="s" horiz-adv-x="929" d="M63 174q0 25 17.5 43.5t34.5 25t20 3.5q116 -158 334 -158q119 0 185.5 44.5t66.5 119.5q0 25 -7 45t-17 35t-32 28t-40 21.5t-54 18.5t-62.5 16t-74.5 16q-43 9 -67.5 15t-66 19t-66 25.5t-55 33.5t-47 45.5t-28.5 59t-12 75.5q0 131 102.5 212.5t270.5 81.5 q164 0 262 -60.5t98 -125.5q0 -23 -17.5 -41.5t-35 -25.5t-20.5 -3q-43 66 -117 105t-170 39q-117 0 -181.5 -45t-64.5 -119q0 -72 60.5 -105.5t212.5 -66.5q37 -8 54.5 -12t53.5 -14t55.5 -17.5t50 -21t48 -27t38.5 -33.5t32 -42t18.5 -50.5t7.5 -61.5 q0 -136 -103.5 -216.5t-281.5 -80.5q-92 0 -171.5 20.5t-128 51.5t-75.5 64.5t-27 62.5z" />
<glyph unicode="t" horiz-adv-x="708" d="M45 909v13q0 27 14.5 40t49.5 13h90v170q0 63 59 63h14q28 0 43 -14t15 -49v-170h260q35 0 49 -12.5t14 -40.5v-13q0 -28 -14 -40.5t-49 -12.5h-260v-555q0 -110 32.5 -160.5t112.5 -50.5q93 0 152 72q1 1 6 -0.5t11.5 -6.5t12.5 -12.5t10.5 -20t4.5 -26.5q0 -45 -55 -83 t-146 -38q-140 0 -206 78.5t-66 233.5v569h-90q-35 0 -49.5 13t-14.5 40z" />
<glyph unicode="u" horiz-adv-x="1116" d="M129 406v514q0 63 59 63h15q59 0 59 -63v-506q0 -148 71.5 -231t201.5 -83q103 0 185 56t126 151v613q0 63 59 63h15q59 0 59 -63v-914q0 -2 -6 -5.5t-16.5 -6t-22.5 -2.5q-86 0 -86 129v31q-55 -82 -139.5 -129.5t-192.5 -47.5q-189 0 -288 116t-99 315z" />
<glyph unicode="v" horiz-adv-x="968" d="M31 944q-1 5 5 13.5t22.5 17t37.5 8.5q36 0 57 -19.5t44 -78.5l286 -766l293 766q22 58 43 78t53 20q21 0 37.5 -8.5t23 -17.5t5.5 -13l-358 -903q-12 -29 -30.5 -41t-66.5 -12q-49 0 -63.5 9.5t-26.5 39.5z" />
<glyph unicode="w" horiz-adv-x="1380" d="M49 944q-1 4 9 13.5t29.5 17.5t39.5 8q27 0 48.5 -22t31.5 -72l178 -766l209 657q9 31 30 43.5t70 12.5t70.5 -12.5t30.5 -43.5l206 -657l177 766q8 50 30 72t49 22q19 0 37.5 -8.5t28 -18t8.5 -12.5l-233 -905q-6 -28 -25.5 -39.5t-73.5 -11.5q-48 0 -64 11t-26 40 l-215 668l-221 -668q-10 -29 -26 -40t-64 -11q-54 0 -72.5 11.5t-25.5 39.5z" />
<glyph unicode="x" horiz-adv-x="952" d="M49 49l346 436l-340 441q-3 4 3 17.5t23.5 26.5t41.5 13q32 0 52 -15.5t48 -52.5l258 -348l252 346q51 70 98 70q24 0 42 -13q19 -14 25 -28q4 -9 4 -13q0 -2 -1 -3l-344 -441l338 -436q3 -4 -3 -17.5t-23.5 -26.5t-41.5 -13q-32 0 -52.5 15.5t-47.5 51.5l-258 347 l-250 -345q-26 -36 -49 -52.5t-51 -16.5q-24 0 -42 14q-19 12 -25 26q-4 9 -4 14q0 2 1 3z" />
<glyph unicode="y" horiz-adv-x="966" d="M31 944q-1 5 5 13.5t22.5 17t37.5 8.5q36 0 58 -19.5t43 -72.5l309 -764l268 760q18 52 41.5 74t56.5 22q17 0 34 -7.5t26 -16t8 -11.5l-471 -1263q-33 -88 -94 -88q-17 0 -34.5 7.5t-27 15.5t-8.5 11l164 418q-34 0 -51.5 10.5t-30.5 40.5z" />
<glyph unicode="z" horiz-adv-x="968" d="M82 57v19q0 18 10 34.5t41 53.5l569 696h-552q-36 0 -52 12t-16 39v13q0 27 16 39t52 12h667q64 0 64 -57v-11q0 -14 -10.5 -30t-41.5 -54l-579 -708h577q68 0 68 -52v-12q0 -27 -16 -39t-52 -12h-682q-63 0 -63 57z" />
<glyph unicode="{" horiz-adv-x="839" d="M53 463q0 53 17 74t75 53l136 72q5 132 37 244.5t80.5 189.5t108.5 131.5t121 80t120 25.5q53 0 53 -57q0 -13 -7 -31t-12 -20q-100 -21 -170.5 -71.5t-112 -133t-61.5 -185.5t-24 -243l-244 -129l242 -129q4 -140 24 -243t61.5 -185.5t112 -133t170.5 -71.5q5 -2 12 -20 t7 -31q0 -58 -54 -58q-58 0 -119.5 25.5t-121.5 80t-108 131.5t-80 190t-37 245l-134 72q-58 32 -75 53t-17 74z" />
<glyph unicode="|" horiz-adv-x="497" d="M182 -340v1610q0 63 60 63h14q59 0 59 -63v-1610q0 -63 -59 -63h-14q-60 0 -60 63z" />
<glyph unicode="}" horiz-adv-x="839" d="M37 1276q0 57 53 57q59 0 120 -25.5t121 -80t108.5 -131.5t80.5 -189.5t37 -244.5l135 -72q58 -32 75 -53t17 -74t-17 -74t-75 -53l-133 -72q-5 -132 -37 -245t-80.5 -190t-108.5 -131.5t-121.5 -80t-119.5 -25.5q-53 0 -53 58q0 13 6.5 31t11.5 20q81 17 142 54 t102.5 90t68.5 128.5t40 162.5t16 198l242 129l-244 129q-3 111 -16 198t-40 162.5t-68.5 128.5t-102.5 90t-142 54q-5 2 -11.5 20t-6.5 31z" />
<glyph unicode="~" horiz-adv-x="1021" d="M217 397q0 71 41 128t100 57q51 0 135 -44t169 -120q48 149 94 149q51 0 51 -53q0 -70 -41 -127t-100 -57q-51 0 -135 44t-169 120q-48 -150 -94 -150q-51 0 -51 53z" />
<glyph unicode="&#xa1;" horiz-adv-x="505" d="M152 1227v20q0 43 24 66.5t68 23.5h20q43 0 66.5 -23.5t23.5 -66.5v-20q0 -43 -23.5 -67.5t-66.5 -24.5h-20q-44 0 -68 24.5t-24 67.5zM168 72l45 839q2 27 8 38.5t25 11.5h16q12 0 18.5 -6.5t9 -16t3.5 -27.5l45 -839q5 -80 -59 -80h-52q-64 0 -59 80z" />
<glyph unicode="&#xa2;" horiz-adv-x="1103" d="M96 575q0 208 124 351t319 168v188q0 35 13.5 49.5t41.5 14.5h10q28 0 41.5 -14.5t13.5 -49.5v-184q85 -6 157.5 -33.5t116 -63.5t67.5 -72t24 -63t-18 -48t-36 -29t-20 -3q-49 89 -119.5 136.5t-177.5 56.5v-803q224 12 312 213q2 5 21 -0.5t37.5 -24t18.5 -45.5 q0 -77 -107 -164.5t-276 -97.5v-194q0 -35 -13.5 -49.5t-41.5 -14.5h-10q-28 0 -41.5 14.5t-13.5 49.5v198q-207 25 -325 160t-118 354zM231 578q0 -164 85 -268t229 -128v795q-145 -18 -229.5 -121.5t-84.5 -277.5z" />
<glyph unicode="&#xa3;" horiz-adv-x="1155" d="M33 57v13q0 57 63 57h115l76 412h-162q-64 0 -64 53v14q0 56 64 56h184l58 313q68 375 391 375q82 0 149 -26t106 -66.5t60 -85t21 -85.5q0 -22 -9.5 -38.5t-23.5 -24t-28 -11t-23.5 -3t-10.5 3.5q-22 104 -82.5 160.5t-158.5 56.5q-115 0 -172.5 -62.5t-83.5 -201.5 l-56 -305h340q64 0 64 -56v-14q0 -53 -64 -53h-362l-76 -412h656q63 0 63 -59v-11q0 -57 -63 -57h-908q-63 0 -63 57z" />
<glyph unicode="&#xa4;" horiz-adv-x="1282" d="M74 500v12q0 28 14 40.5t49 12.5h127v146q0 39 2 59h-129q-35 0 -49 12.5t-14 40.5v13q0 28 14 40.5t49 12.5h144q40 214 174 337.5t331 123.5q92 0 172 -26t130.5 -65.5t79 -82t28.5 -78.5q0 -28 -19.5 -48t-38.5 -26t-22 -2q-57 104 -137.5 157.5t-200.5 53.5 q-135 0 -230.5 -92t-131.5 -252h442q35 0 49.5 -13t14.5 -40v-13q0 -27 -14.5 -40t-49.5 -13h-459q-2 -18 -2 -57v-148h461q35 0 49.5 -13t14.5 -40v-12q0 -28 -14.5 -41t-49.5 -13h-444q36 -162 136 -257t241 -95q121 0 200 58.5t131 171.5q3 5 22.5 -0.5t39.5 -26 t20 -51.5q0 -38 -29.5 -84t-80 -88t-130 -70.5t-167.5 -28.5q-210 0 -345.5 124t-172.5 347h-142q-35 0 -49 13t-14 41z" />
<glyph unicode="&#xa5;" horiz-adv-x="1165" d="M86 1274q-3 6 8.5 19.5t35.5 26.5t50 13q42 0 68 -47l334 -600l342 598q32 49 69 49q22 0 44 -13t32.5 -27t7.5 -19l-389 -633h287q35 0 49 -12.5t14 -40.5v-13q0 -28 -14 -40.5t-49 -12.5h-330v-164h330q35 0 49 -12.5t14 -40.5v-12q0 -28 -14 -40.5t-49 -12.5h-330 v-185q0 -33 -13.5 -48t-43.5 -15h-15q-55 0 -55 63v185h-330q-35 0 -49 12.5t-14 40.5v12q0 28 14 40.5t49 12.5h330v164h-330q-35 0 -49 12.5t-14 40.5v13q0 28 14 40.5t49 12.5h285z" />
<glyph unicode="&#xa7;" horiz-adv-x="1064" d="M61 -78q0 26 18.5 47t37.5 30t20 5q57 -102 148 -155.5t233 -53.5q143 0 225 48t82 132q0 40 -16.5 70t-34 48t-69.5 36.5t-81 26t-110 26.5q-17 4 -25 6q-53 12 -85 20.5t-80.5 24t-78.5 31t-64.5 40t-54 52.5t-33 67t-13.5 85q0 96 59.5 162t165.5 98q-90 36 -138 97 t-48 161q0 142 122 233t300 91q81 0 150.5 -16.5t115.5 -41.5t79 -56t47.5 -60t14.5 -54t-18 -45.5t-35.5 -29.5t-20.5 -5q-58 91 -138 138t-195 47q-121 0 -205 -50t-84 -135q0 -86 77.5 -132t248.5 -85q42 -10 66 -15.5t64 -17t63.5 -20.5t56.5 -24.5t53 -31t42.5 -37.5 t35 -46.5t20.5 -55.5t8 -67q0 -95 -58.5 -162t-160.5 -100q90 -34 140 -95.5t50 -166.5q0 -141 -121.5 -227.5t-316.5 -86.5q-114 0 -206.5 26.5t-145 66.5t-80 81t-27.5 76zM209 522q0 -30 10 -54.5t23.5 -42.5t41.5 -34.5t51 -27t64.5 -22.5t69 -18.5t78.5 -17.5l69 -16 q100 9 171 71.5t71 135.5q0 33 -11 60t-36.5 47.5t-50 34.5t-71.5 28t-78.5 22t-90.5 21q-47 8 -74 14q-102 -12 -169.5 -72.5t-67.5 -128.5z" />
<glyph unicode="&#xa8;" horiz-adv-x="1024" d="M240 1223q0 46 27 73t71 27q47 0 74.5 -26.5t27.5 -73.5t-26.5 -74t-71.5 -27q-46 0 -74 27t-28 74zM584 1223q0 47 26.5 73.5t71.5 26.5q46 0 74 -26.5t28 -73.5t-27 -74t-71 -27q-47 0 -74.5 27t-27.5 74z" />
<glyph unicode="&#xa9;" horiz-adv-x="1550" d="M102 659q0 195 87.5 353t242 248t344.5 90q194 0 347.5 -87.5t239 -243.5t85.5 -353q0 -195 -87.5 -353t-242 -248t-344.5 -90q-194 0 -347.5 87.5t-239 243.5t-85.5 353zM213 664q0 -125 43 -234.5t117.5 -187.5t179.5 -123t225 -45q248 0 403.5 163.5t155.5 424.5 q0 253 -161 421t-404 168q-248 0 -403.5 -163t-155.5 -424zM430 659q0 156 104.5 261.5t256.5 105.5q75 0 136.5 -25.5t96.5 -60.5q39 -39 39 -78q0 -21 -16.5 -38t-34 -23.5t-19.5 -1.5q-61 112 -205 112q-95 0 -164 -72.5t-69 -176.5q0 -109 70 -180.5t172 -71.5 q73 0 125.5 33t78.5 89q2 5 19.5 -1t35 -23t17.5 -39q0 -38 -49 -90q-34 -36 -94.5 -60t-136.5 -24q-159 0 -261 101.5t-102 262.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="874" d="M74 743q0 62 24.5 106t85.5 80.5t171 58.5t274 32q0 219 -217 219q-152 0 -238 -139q0 -3 -17 2t-34 20.5t-17 38.5q0 24 19.5 55t56 61.5t100.5 51.5t140 21q165 0 248.5 -86.5t83.5 -239.5v-471q0 -2 -6 -5t-16 -5t-21 -2q-36 0 -58 24t-22 90q-42 -59 -113 -93 t-149 -34q-138 0 -216.5 57t-78.5 158zM199 750q0 -56 49 -87.5t137 -31.5q101 0 173.5 58t72.5 132v111q-134 -9 -222.5 -25.5t-132.5 -41.5t-60.5 -51.5t-16.5 -63.5zM328 70v16q0 40 21 62t61 22h16q41 0 62.5 -22t21.5 -62v-16q0 -82 -84 -82h-16q-82 0 -82 82z" />
<glyph unicode="&#xab;" horiz-adv-x="1038" d="M92 498q0 10 41 58t96 103t117.5 98.5t99.5 43.5h3q18 0 30.5 -12.5t12.5 -30.5v-2q0 -60 -283 -260q283 -196 283 -258v-2q0 -18 -12.5 -30.5t-30.5 -12.5h-3q-37 0 -99.5 44t-117.5 98.5t-96 103t-41 59.5zM494 498q0 11 45 64.5t106 114.5t130 110t110 49h2 q21 0 35 -15t14 -33v-4q0 -37 -85 -109.5t-235 -178.5q320 -219 320 -285v-6q0 -18 -14 -31.5t-35 -13.5h-2q-41 0 -110 48.5t-130 109.5t-106 115t-45 65z" />
<glyph unicode="&#xad;" horiz-adv-x="749" d="M92 487v15q0 55 64 55h438q63 0 63 -55v-15q0 -53 -63 -53h-438q-64 0 -64 53z" />
<glyph unicode="&#xae;" horiz-adv-x="1550" d="M102 659q0 195 87.5 353t242 248t344.5 90q194 0 347.5 -87.5t239 -243.5t85.5 -353q0 -195 -87.5 -353t-242 -248t-344.5 -90q-194 0 -347.5 87.5t-239 243.5t-85.5 353zM213 664q0 -125 43 -234.5t117.5 -187.5t179.5 -123t225 -45q248 0 403.5 163.5t155.5 424.5 q0 253 -161 421t-404 168q-248 0 -403.5 -163t-155.5 -424zM518 375v575q0 32 18 51t48 19h217q133 0 203.5 -55t70.5 -164q0 -92 -56 -148t-157 -65l199 -228q3 -4 -4.5 -16.5t-25 -23.5t-38.5 -11q-49 0 -84 45l-180 226h-88v-213q0 -27 -13.5 -42.5t-39.5 -15.5h-17 q-53 0 -53 66zM639 682h164q71 0 110 31t39 86q0 116 -155 116h-158v-233z" />
<glyph unicode="&#xaf;" horiz-adv-x="1024" d="M219 1223v10q0 57 80 57h426q80 0 80 -57v-10q0 -60 -80 -60h-426q-80 0 -80 60z" />
<glyph unicode="&#xb0;" horiz-adv-x="835" d="M104 1022v18q0 133 92 224.5t226 91.5q133 0 221 -89.5t88 -224.5v-18q0 -133 -91.5 -224t-225.5 -91t-222 89t-88 224zM217 1024q0 -86 59.5 -146.5t143.5 -60.5q85 0 141.5 58.5t56.5 146.5v18q0 86 -59.5 146.5t-142.5 60.5q-85 0 -142 -58.5t-57 -146.5v-18z" />
<glyph unicode="&#xb1;" horiz-adv-x="1228" d="M178 299v14q0 28 14.5 42t49.5 14h743q35 0 49.5 -14t14.5 -42v-14q0 -28 -14.5 -41.5t-49.5 -13.5h-743q-35 0 -49.5 13.5t-14.5 41.5zM199 895v12q0 28 14 41t49 13h289v292q0 64 57 64h10q58 0 58 -64v-292h289q35 0 49 -13t14 -41v-12q0 -28 -14 -40.5t-49 -12.5 h-289v-316q0 -35 -15 -49t-43 -14h-10q-28 0 -42.5 14t-14.5 49v316h-289q-35 0 -49 12.5t-14 40.5z" />
<glyph unicode="&#xb2;" horiz-adv-x="688" d="M80 653v31q0 55 17 98t52.5 75.5t69.5 52.5t88 46q101 50 126 70q51 39 52 88q0 54 -38 85.5t-105 31.5q-72 0 -114.5 -39t-57.5 -100q-4 0 -24 -1.5t-43 14.5t-23 46q0 68 75 129t197 61q124 0 193 -59.5t69 -165.5q0 -39 -12.5 -71t-30 -54t-50 -44t-57.5 -34.5 t-67 -31.5q-108 -49 -143 -75q-61 -46 -64 -112h373q62 0 62 -51v-6q0 -53 -62 -53h-403q-44 0 -62 16t-18 53z" />
<glyph unicode="&#xb3;" horiz-adv-x="688" d="M47 735q0 19 8 33.5t19 21t22.5 10.5t19.5 3t9 -2q20 -61 72 -94t125 -33q77 0 126.5 34.5t49.5 90.5q0 59 -45.5 91t-124.5 32h-49q-62 0 -62 47v8q0 45 59 45h66q63 0 101 28.5t38 75.5q0 50 -44.5 80.5t-114.5 30.5q-68 0 -112 -31.5t-65 -81.5q-2 -3 -20 0.5 t-36.5 20.5t-18.5 45q0 55 72 103t182 48q132 0 202 -52.5t70 -149.5q0 -120 -125 -158q143 -48 143 -188q0 -105 -76.5 -164.5t-213.5 -59.5q-125 0 -201 53t-76 113z" />
<glyph unicode="&#xb4;" horiz-adv-x="1024" d="M367 1128q-4 5 32 50t100 108t124 105q48 36 94 36q25 0 43 -15.5t18 -39.5q0 -50 -76 -102q-63 -43 -143 -80t-133.5 -52.5t-58.5 -9.5z" />
<glyph unicode="&#xb6;" horiz-adv-x="1265" d="M68 954q0 182 119.5 277.5t351.5 95.5h233q32 0 55 -23t23 -55v-1194q0 -63 -59 -63h-19q-30 0 -45.5 17.5t-15.5 45.5v539h-162q-235 0 -358 90.5t-123 269.5zM969 55v1215q0 63 59 63h19q61 0 61 -63v-1215q0 -63 -61 -63h-19q-59 0 -59 63z" />
<glyph unicode="&#xb7;" horiz-adv-x="452" d="M125 485v21q0 43 23.5 67.5t66.5 24.5h21q44 0 68 -24.5t24 -67.5v-21q0 -43 -24 -66.5t-68 -23.5h-21q-43 0 -66.5 23.5t-23.5 66.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="1024" d="M326 -428l123 256q11 23 14.5 30t13.5 19.5t22 16.5t31 4h11q29 0 50 -16.5t21 -43.5v-8q0 -22 -47 -77q-4 -5 -6 -7l-166 -207q-13 -16 -46 -5.5t-21 38.5z" />
<glyph unicode="&#xb9;" horiz-adv-x="688" d="M92 1206q0 19 9 36t18 24t12 6q30 -35 82 -35q42 0 74.5 27t36.5 69h47q59 0 59 -63v-576h123q61 0 61 -55v-4q0 -51 -61 -51h-393q-60 0 -60 51v4q0 55 60 55h147v467q-40 -43 -102 -43q-45 0 -79 24.5t-34 63.5z" />
<glyph unicode="&#xba;" horiz-adv-x="980" d="M90 936q0 172 117.5 293t284.5 121q170 0 284.5 -118t114.5 -292q0 -173 -116.5 -293.5t-282.5 -120.5q-171 0 -286.5 118t-115.5 292zM213 938q0 -126 81 -213.5t198 -87.5q120 0 198 86t78 215q0 128 -79.5 214.5t-196.5 86.5q-120 0 -199.5 -86t-79.5 -215zM397 70v16 q0 40 21 62t61 22h17q41 0 62.5 -22t21.5 -62v-16q0 -82 -84 -82h-17q-82 0 -82 82z" />
<glyph unicode="&#xbb;" horiz-adv-x="1038" d="M102 207v4q0 38 85.5 111t234.5 178q-320 219 -320 284v7q0 18 14.5 31.5t35.5 13.5h2q41 0 110 -48.5t130 -109.5t106 -115t45 -65t-45 -64.5t-106 -114.5t-130 -110t-110 -49h-2q-21 0 -35.5 14.5t-14.5 32.5zM547 238v2q0 60 282 260q-282 196 -282 258v2 q0 18 12.5 30.5t30.5 12.5h2q37 0 99.5 -44t117.5 -98.5t96 -103t41 -59.5q0 -10 -41 -58t-96 -103t-117.5 -98.5t-99.5 -43.5h-2q-18 0 -30.5 12.5t-12.5 30.5z" />
<glyph unicode="&#xbc;" horiz-adv-x="1622" d="M92 1206q0 19 9 36t18 24t12 6q30 -35 82 -35q42 0 74.5 27t36.5 69h47q59 0 59 -63v-576h123q61 0 61 -55v-4q0 -51 -61 -51h-393q-60 0 -60 51v4q0 55 60 55h147v467q-40 -43 -102 -43q-45 0 -79 24.5t-34 63.5zM305 -10q0 29 9 48.5t40 59.5q17 22 87 106.5 t170.5 205.5t170.5 208l539 676q0 -1 6 0t15 -1.5t17.5 -6.5t14.5 -13.5t6 -23.5q0 -41 -51 -108q-15 -20 -86.5 -106t-171.5 -208t-170 -209l-534 -673q-5 -1 -19 -1t-28.5 13t-14.5 33zM969 254v27q2 27 35 77l253 334q23 35 44 46.5t69 11.5q52 0 73 -20t21 -66v-365h78 q60 0 60 -49v-6q0 -49 -60 -49h-78v-140q0 -61 -55 -61h-8q-26 0 -39.5 16.5t-13.5 44.5v140h-299q-36 0 -58 16.5t-22 42.5zM1083 297h267l-2 350z" />
<glyph unicode="&#xbd;" horiz-adv-x="1622" d="M92 1206q0 19 9 36t18 24t12 6q30 -35 82 -35q42 0 74.5 27t36.5 69h47q59 0 59 -63v-576h123q61 0 61 -55v-4q0 -51 -61 -51h-393q-60 0 -60 51v4q0 55 60 55h147v467q-40 -43 -102 -43q-45 0 -79 24.5t-34 63.5zM244 -10q0 29 9 48.5t40 59.5q17 22 87 106.5 t170.5 205.5t170.5 208l539 676q0 -1 6 0t15 -1.5t17.5 -6.5t14.5 -13.5t6 -23.5q0 -41 -51 -108q-15 -20 -86.5 -106t-171.5 -208t-170 -209l-535 -673q-5 -1 -18.5 -1t-28 13t-14.5 33zM1014 70v30q0 55 17 98.5t52.5 75.5t69.5 53t88 46q7 3 30.5 15t30.5 15.5t26.5 14 t26 15.5t20.5 15t19.5 18t12.5 18.5t9.5 22t2.5 23.5q0 54 -38 85.5t-105 31.5q-72 0 -114.5 -39t-57.5 -100q-4 0 -24 -1.5t-43 14.5t-23 46q0 68 75 129.5t197 61.5q124 0 193 -60t69 -166q0 -39 -12.5 -71t-30 -54t-50 -44t-57.5 -34.5t-67 -31.5q-108 -49 -142 -74 q-62 -46 -65 -112h373q62 0 62 -52v-6q0 -53 -62 -53h-403q-44 0 -62 16.5t-18 53.5z" />
<glyph unicode="&#xbe;" horiz-adv-x="1622" d="M47 735q0 19 8 33.5t19 21t22.5 10.5t19.5 3t9 -2q20 -61 72 -94t125 -33q77 0 126.5 34.5t49.5 90.5q0 59 -45.5 91t-124.5 32h-49q-62 0 -62 47v8q0 45 59 45h66q63 0 101 28.5t38 75.5q0 50 -44.5 80.5t-114.5 30.5q-68 0 -112 -31.5t-65 -81.5q-2 -3 -20 0.5 t-36.5 20.5t-18.5 45q0 55 72 103t182 48q132 0 202 -52.5t70 -149.5q0 -120 -125 -158q143 -48 143 -188q0 -105 -76.5 -164.5t-213.5 -59.5q-125 0 -201 53t-76 113zM305 -10q0 29 9 48.5t40 59.5q17 22 87 106.5t170.5 205.5t170.5 208l539 676q0 -1 6 0t15 -1.5 t17.5 -6.5t14.5 -13.5t6 -23.5q0 -41 -51 -108q-15 -20 -86.5 -106t-171.5 -208t-170 -209l-534 -673q-5 -1 -19 -1t-28.5 13t-14.5 33zM969 254v27q2 27 35 77l253 334q23 35 44 46.5t69 11.5q52 0 73 -20t21 -66v-365h78q60 0 60 -49v-6q0 -49 -60 -49h-78v-140 q0 -61 -55 -61h-8q-26 0 -39.5 16.5t-13.5 44.5v140h-299q-36 0 -58 16.5t-22 42.5zM1083 297h267l-2 350z" />
<glyph unicode="&#xbf;" horiz-adv-x="948" d="M61 311q0 68 22.5 121.5t59 89t80 67t87 62.5t80 66.5t59 89t22.5 121.5q0 35 57 35q61 0 58 -43q-3 -83 -26 -148.5t-57 -106.5t-75 -74t-81 -59.5t-73.5 -54t-54 -66t-20.5 -87.5q0 -104 69.5 -165t194.5 -61q76 0 135 24t94 65.5t54.5 88t27.5 101.5q1 6 26 0.5 t49.5 -27t24.5 -55.5q0 -33 -14 -72.5t-47 -84t-79 -80.5t-118 -59.5t-157 -23.5q-181 0 -289.5 95t-108.5 241zM420 1227v20q0 43 24 66.5t68 23.5h20q43 0 67 -23.5t24 -66.5v-20q0 -43 -24 -67.5t-67 -24.5h-20q-44 0 -68 24.5t-24 67.5z" />
<glyph unicode="&#xc0;" horiz-adv-x="1247" d="M33 33l485 1253q11 27 34 39t71 12t70.5 -12t33.5 -39l485 -1253q2 -5 -8.5 -15t-30.5 -18t-40 -8q-61 0 -84 63l-146 377h-569l-144 -377q-23 -63 -79 -63q-19 0 -38.5 8t-30.5 18t-9 15zM295 1720q0 24 18 40t43 16q46 0 95 -37q60 -42 124 -104.5t100 -107.5t32 -50 q-5 -7 -58.5 9t-134 52.5t-143.5 79.5q-76 52 -76 102zM379 555h479l-240 623z" />
<glyph unicode="&#xc1;" horiz-adv-x="1247" d="M33 33l485 1253q11 27 34 39t71 12t70.5 -12t33.5 -39l485 -1253q2 -5 -8.5 -15t-30.5 -18t-40 -8q-61 0 -84 63l-146 377h-569l-144 -377q-23 -63 -79 -63q-19 0 -38.5 8t-30.5 18t-9 15zM379 555h479l-240 623zM518 1477q-4 5 32 50t100 107.5t124 104.5q49 37 94 37 q25 0 43.5 -16t18.5 -40q0 -50 -76 -102q-63 -43 -143.5 -79.5t-134 -52.5t-58.5 -9z" />
<glyph unicode="&#xc2;" horiz-adv-x="1247" d="M33 33l485 1253q11 27 34 39t71 12t70.5 -12t33.5 -39l485 -1253q2 -5 -8.5 -15t-30.5 -18t-40 -8q-61 0 -84 63l-146 377h-569l-144 -377q-23 -63 -79 -63q-19 0 -38.5 8t-30.5 18t-9 15zM354 1520q0 44 66 119t130.5 128.5t72.5 53.5q5 0 46 -33.5t90.5 -79.5 t89.5 -100t40 -88q0 -22 -11.5 -34.5t-31.5 -12.5q-33 0 -82 50.5t-143 178.5q-96 -131 -142.5 -180t-79.5 -49q-20 0 -32.5 12.5t-12.5 34.5zM379 555h479l-240 623z" />
<glyph unicode="&#xc3;" horiz-adv-x="1247" d="M33 33l485 1253q11 27 34 39t71 12t70.5 -12t33.5 -39l485 -1253q2 -5 -8.5 -15t-30.5 -18t-40 -8q-61 0 -84 63l-146 377h-569l-144 -377q-23 -63 -79 -63q-19 0 -38.5 8t-30.5 18t-9 15zM340 1538q0 70 41 127t100 57q51 0 134.5 -43.5t168.5 -119.5q48 149 95 149 q51 0 51 -53q0 -70 -41.5 -127.5t-100.5 -57.5q-51 0 -134.5 44t-168.5 120q-48 -149 -94 -149q-51 0 -51 53zM379 555h479l-240 623z" />
<glyph unicode="&#xc4;" horiz-adv-x="1247" d="M33 33l485 1253q11 27 34 39t71 12t70.5 -12t33.5 -39l485 -1253q2 -5 -8.5 -15t-30.5 -18t-40 -8q-61 0 -84 63l-146 377h-569l-144 -377q-23 -63 -79 -63q-19 0 -38.5 8t-30.5 18t-9 15zM350 1571q0 46 27.5 73t71.5 27q47 0 74.5 -26.5t27.5 -73.5t-26.5 -74 t-71.5 -27q-46 0 -74.5 27t-28.5 74zM379 555h479l-240 623zM694 1571q0 47 27 73.5t72 26.5q46 0 74 -26.5t28 -73.5t-27.5 -74t-70.5 -27q-47 0 -75 27t-28 74z" />
<glyph unicode="&#xc5;" horiz-adv-x="1247" d="M33 33l475 1229q-59 28 -92 83.5t-33 127.5q0 101 68.5 170t171.5 69q104 0 170.5 -68t66.5 -171q0 -69 -34.5 -125t-92.5 -84l479 -1231q2 -5 -8.5 -15t-30.5 -18t-40 -8q-61 0 -84 63l-146 377h-569l-144 -377q-23 -63 -79 -63q-19 0 -38.5 8t-30.5 18t-9 15zM379 555 h479l-240 627zM483 1473q0 -61 40.5 -104.5t97.5 -43.5q60 0 98.5 42.5t38.5 105.5t-39.5 106t-97.5 43t-98 -43t-40 -106z" />
<glyph unicode="&#xc6;" horiz-adv-x="1728" d="M33 33l477 1231q24 61 88 61h979q63 0 63 -57v-13q0 -57 -63 -57h-821l176 -455h500q63 0 63 -57v-12q0 -58 -63 -58h-449l188 -489h418q64 0 64 -55v-13q0 -59 -64 -59h-438q-50 0 -74.5 18t-40.5 60l-133 354h-569l-144 -377q-26 -63 -79 -63q-19 0 -38.5 8t-30.5 18 t-9 15zM379 555h479l-240 623z" />
<glyph unicode="&#xc7;" horiz-adv-x="1351" d="M102 659q0 201 84 358.5t235.5 245t344.5 87.5q90 0 172.5 -20t139 -50.5t99 -66.5t63 -69t20.5 -57q0 -32 -20 -53.5t-39.5 -27.5t-22.5 -2q-61 105 -176.5 165t-239.5 60q-227 0 -371.5 -158t-144.5 -407q0 -247 147 -407.5t373 -160.5q149 0 266 70.5t162 189.5 q1 5 23.5 -1t44.5 -27.5t22 -53.5q0 -27 -21 -64t-65.5 -78.5t-104.5 -76t-148 -57.5t-185 -23q-290 0 -474 191t-184 493zM541 -428l123 256q11 23 14.5 30t13.5 19.5t22 16.5t31 4h11q29 0 50 -16.5t21 -43.5v-8q0 -22 -47 -77q-4 -5 -6 -7l-166 -207q-13 -16 -46 -5.5 t-21 38.5z" />
<glyph unicode="&#xc8;" horiz-adv-x="1179" d="M158 78v1169q0 32 23 55t55 23h792q64 0 64 -57v-13q0 -57 -64 -57h-731v-457h586q63 0 63 -57v-8q0 -58 -63 -58h-586v-491h743q64 0 64 -55v-13q0 -59 -64 -59h-804q-32 0 -55 23t-23 55zM358 1720q0 24 18.5 40t43.5 16q45 0 94 -37q60 -42 124 -104.5t100 -107.5 t32 -50q-5 -7 -58.5 9t-134 52.5t-143.5 79.5q-76 52 -76 102z" />
<glyph unicode="&#xc9;" horiz-adv-x="1179" d="M158 78v1169q0 32 23 55t55 23h792q64 0 64 -57v-13q0 -57 -64 -57h-731v-457h586q63 0 63 -57v-8q0 -58 -63 -58h-586v-491h743q64 0 64 -55v-13q0 -59 -64 -59h-804q-32 0 -55 23t-23 55zM465 1477q-4 5 32 50t100 107.5t124 104.5q49 37 94 37q25 0 43.5 -16t18.5 -40 q0 -50 -76 -102q-63 -43 -143.5 -79.5t-134 -52.5t-58.5 -9z" />
<glyph unicode="&#xca;" horiz-adv-x="1179" d="M158 78v1169q0 32 23 55t55 23h792q64 0 64 -57v-13q0 -57 -64 -57h-731v-457h586q63 0 63 -57v-8q0 -58 -63 -58h-586v-491h743q64 0 64 -55v-13q0 -59 -64 -59h-804q-32 0 -55 23t-23 55zM356 1520q0 44 66 119t130.5 128.5t72.5 53.5q5 0 46 -33.5t90.5 -79.5 t89.5 -100t40 -88q0 -22 -11.5 -34.5t-31.5 -12.5q-33 0 -82 50.5t-143 178.5q-96 -131 -142.5 -180t-79.5 -49q-20 0 -32.5 12.5t-12.5 34.5z" />
<glyph unicode="&#xcb;" horiz-adv-x="1179" d="M158 78v1169q0 32 23 55t55 23h792q64 0 64 -57v-13q0 -57 -64 -57h-731v-457h586q63 0 63 -57v-8q0 -58 -63 -58h-586v-491h743q64 0 64 -55v-13q0 -59 -64 -59h-804q-32 0 -55 23t-23 55zM352 1571q0 46 27.5 73t71.5 27q47 0 74.5 -26.5t27.5 -73.5t-26.5 -74 t-71.5 -27q-46 0 -74.5 27t-28.5 74zM696 1571q0 47 27 73.5t72 26.5q46 0 74 -26.5t28 -73.5t-27.5 -74t-70.5 -27q-47 0 -75 27t-28 74z" />
<glyph unicode="&#xcc;" horiz-adv-x="495" d="M-59 1720q0 24 18 40t43 16q45 0 94 -37q60 -42 124 -104.5t100 -107.5t32 -50q-5 -7 -58.5 9t-134 52.5t-143.5 79.5q-75 51 -75 102zM178 55v1215q0 63 60 63h18q61 0 61 -63v-1215q0 -63 -61 -63h-18q-60 0 -60 63z" />
<glyph unicode="&#xcd;" horiz-adv-x="495" d="M143 1477q-4 5 32 50t100 107.5t124 104.5q49 37 95 37q25 0 43 -16t18 -40q0 -50 -76 -102q-63 -43 -143.5 -79.5t-134 -52.5t-58.5 -9zM178 55v1215q0 63 60 63h18q61 0 61 -63v-1215q0 -63 -61 -63h-18q-60 0 -60 63z" />
<glyph unicode="&#xce;" horiz-adv-x="495" d="M-20 1520q0 34 40.5 88t90 100t91 79.5t46.5 33.5t46 -33.5t90.5 -79.5t89.5 -100t40 -88q0 -22 -11.5 -34.5t-31.5 -12.5q-33 0 -82 50.5t-143 178.5q-96 -131 -142 -180t-79 -49q-20 0 -32.5 12.5t-12.5 34.5zM178 55v1215q0 63 60 63h18q61 0 61 -63v-1215 q0 -63 -61 -63h-18q-60 0 -60 63z" />
<glyph unicode="&#xcf;" horiz-adv-x="495" d="M-25 1571q0 46 27.5 73t71.5 27q47 0 74.5 -26.5t27.5 -73.5t-26.5 -74t-71.5 -27q-46 0 -74.5 27t-28.5 74zM178 55v1215q0 63 60 63h18q61 0 61 -63v-1215q0 -63 -61 -63h-18q-60 0 -60 63zM319 1571q0 47 27 73.5t72 26.5q46 0 74 -26.5t28 -73.5t-27 -74t-71 -27 q-47 0 -75 27t-28 74z" />
<glyph unicode="&#xd0;" horiz-adv-x="1468" d="M33 657v13q0 28 14 41.5t49 13.5h133v522q0 32 23 55t55 23h324q341 0 538 -175.5t197 -483.5q0 -310 -200.5 -488t-544.5 -178h-314q-32 0 -55 23t-23 55v524h-133q-35 0 -49 13.5t-14 41.5zM369 127h270q282 0 434 139.5t152 395.5q0 253 -153.5 394.5t-430.5 141.5 h-272v-473h438q35 0 49 -13.5t14 -41.5v-13q0 -28 -14 -41.5t-49 -13.5h-438v-475z" />
<glyph unicode="&#xd1;" horiz-adv-x="1382" d="M158 55v1184q0 40 20 67t55 27h35q35 0 57 -16.5t50 -56.5l719 -1043v1053q0 33 13.5 48t43.5 15h18q56 0 56 -63v-1184q0 -42 -19.5 -68t-52.5 -26h-10q-35 0 -51 10.5t-39 42.5l-764 1104v-1094q0 -63 -58 -63h-18q-55 0 -55 63zM397 1538q0 70 41.5 127t100.5 57 q51 0 134.5 -43.5t168.5 -119.5q48 149 94 149q51 0 51 -53q0 -71 -41 -128t-100 -57q-51 0 -134.5 44t-168.5 120q-48 -149 -94 -149q-52 0 -52 53z" />
<glyph unicode="&#xd2;" horiz-adv-x="1523" d="M102 659q0 200 84.5 358t235.5 245.5t342 87.5q192 0 342 -86.5t232.5 -242.5t82.5 -357t-83.5 -358t-234.5 -244t-343 -87q-292 0 -475 190t-183 494zM246 664q0 -249 146 -408.5t372 -159.5t370 157t144 406q0 163 -66.5 293.5t-184.5 203.5t-267 73q-226 0 -370 -158 t-144 -407zM455 1720q0 24 18 40t43 16q45 0 94 -37q60 -42 124 -104.5t100 -107.5t32 -50q-5 -7 -58.5 9t-134 52.5t-143.5 79.5q-75 51 -75 102z" />
<glyph unicode="&#xd3;" horiz-adv-x="1523" d="M102 659q0 200 84.5 358t235.5 245.5t342 87.5q192 0 342 -86.5t232.5 -242.5t82.5 -357t-83.5 -358t-234.5 -244t-343 -87q-292 0 -475 190t-183 494zM246 664q0 -249 146 -408.5t372 -159.5t370 157t144 406q0 163 -66.5 293.5t-184.5 203.5t-267 73q-226 0 -370 -158 t-144 -407zM657 1477q-4 5 32 50t100 107.5t124 104.5q49 37 95 37q25 0 43 -16t18 -40q0 -50 -76 -102q-63 -43 -143.5 -79.5t-134 -52.5t-58.5 -9z" />
<glyph unicode="&#xd4;" horiz-adv-x="1523" d="M102 659q0 200 84.5 358t235.5 245.5t342 87.5q192 0 342 -86.5t232.5 -242.5t82.5 -357t-83.5 -358t-234.5 -244t-343 -87q-292 0 -475 190t-183 494zM246 664q0 -249 146 -408.5t372 -159.5t370 157t144 406q0 163 -66.5 293.5t-184.5 203.5t-267 73q-226 0 -370 -158 t-144 -407zM494 1520q0 34 40.5 88t90 100t91 79.5t46.5 33.5t46 -33.5t90.5 -79.5t89.5 -100t40 -88q0 -22 -11.5 -34.5t-31.5 -12.5q-33 0 -82 50.5t-143 178.5q-96 -131 -142 -180t-79 -49q-20 0 -32.5 12.5t-12.5 34.5z" />
<glyph unicode="&#xd5;" horiz-adv-x="1523" d="M102 659q0 200 84.5 358t235.5 245.5t342 87.5q192 0 342 -86.5t232.5 -242.5t82.5 -357t-83.5 -358t-234.5 -244t-343 -87q-292 0 -475 190t-183 494zM246 664q0 -249 146 -408.5t372 -159.5t370 157t144 406q0 163 -66.5 293.5t-184.5 203.5t-267 73q-226 0 -370 -158 t-144 -407zM469 1538q0 70 41 127t100 57q51 0 134.5 -43.5t168.5 -119.5q48 149 95 149q51 0 51 -53q0 -71 -41 -128t-100 -57q-51 0 -135 44t-169 120q-48 -149 -94 -149q-51 0 -51 53z" />
<glyph unicode="&#xd6;" horiz-adv-x="1523" d="M102 659q0 200 84.5 358t235.5 245.5t342 87.5q192 0 342 -86.5t232.5 -242.5t82.5 -357t-83.5 -358t-234.5 -244t-343 -87q-292 0 -475 190t-183 494zM246 664q0 -249 146 -408.5t372 -159.5t370 157t144 406q0 163 -66.5 293.5t-184.5 203.5t-267 73q-226 0 -370 -158 t-144 -407zM489 1571q0 46 27.5 73t71.5 27q47 0 74.5 -26.5t27.5 -73.5t-26.5 -74t-71.5 -27q-46 0 -74.5 27t-28.5 74zM834 1571q0 47 26.5 73.5t71.5 26.5q46 0 74 -26.5t28 -73.5t-27 -74t-71 -27q-47 0 -74.5 27t-27.5 74z" />
<glyph unicode="&#xd7;" horiz-adv-x="1103" d="M211 193q-41 38 4 86l258 266l-258 262q-45 48 -4 86l8 8q40 43 88 -8l250 -262l250 262q48 51 88 8l8 -8q41 -38 -4 -86l-258 -262l258 -266q45 -48 4 -86l-8 -9q-39 -43 -88 9l-250 264l-250 -264q-49 -52 -88 -9z" />
<glyph unicode="&#xd8;" horiz-adv-x="1523" d="M94 -12q0 46 49 100l109 121q-150 186 -150 450q0 193 82.5 350t235 249t344.5 92q233 0 405 -136l166 185q4 4 20.5 -4t32 -28t15.5 -42q0 -46 -49 -100l-90 -101q157 -190 157 -460q0 -193 -82.5 -349.5t-234.5 -248t-344 -91.5q-244 0 -416 142l-182 -203 q-4 -4 -20.5 4t-32 28t-15.5 42zM242 664q0 -211 108 -359l729 811q-132 107 -319 107q-154 0 -273 -73t-182 -200t-63 -286zM434 215q135 -113 330 -113q154 0 273 72.5t182 199t63 285.5q0 218 -117 369z" />
<glyph unicode="&#xd9;" horiz-adv-x="1308" d="M137 522v748q0 63 60 63h18q61 0 61 -63v-738q0 -432 379 -432q192 0 285.5 108t93.5 327v735q0 63 58 63h24q55 0 55 -63v-742q0 -268 -134 -410.5t-386 -142.5q-251 0 -382.5 140t-131.5 407zM365 1700q0 24 18 39.5t43 15.5q45 0 94 -37q60 -42 124 -104.5t100 -107.5 t32 -50q-5 -7 -58.5 9t-134 52.5t-143.5 79.5q-75 51 -75 103z" />
<glyph unicode="&#xda;" horiz-adv-x="1308" d="M137 522v748q0 63 60 63h18q61 0 61 -63v-738q0 -432 379 -432q192 0 285.5 108t93.5 327v735q0 63 58 63h24q55 0 55 -63v-742q0 -268 -134 -410.5t-386 -142.5q-251 0 -382.5 140t-131.5 407zM547 1456q-4 5 32 50t100 107.5t124 104.5q49 37 94 37q25 0 43 -15.5 t18 -39.5q0 -52 -75 -103q-63 -43 -143.5 -79.5t-134 -52.5t-58.5 -9z" />
<glyph unicode="&#xdb;" horiz-adv-x="1308" d="M137 522v748q0 63 60 63h18q61 0 61 -63v-738q0 -432 379 -432q192 0 285.5 108t93.5 327v735q0 63 58 63h24q55 0 55 -63v-742q0 -268 -134 -410.5t-386 -142.5q-251 0 -382.5 140t-131.5 407zM383 1520q0 34 40.5 88t90 100t91 79.5t46.5 33.5q8 0 72 -53.5 t129.5 -128.5t65.5 -119q0 -22 -12 -34.5t-32 -12.5q-33 0 -82 50.5t-143 178.5q-96 -131 -142 -180t-79 -49q-20 0 -32.5 12.5t-12.5 34.5z" />
<glyph unicode="&#xdc;" horiz-adv-x="1308" d="M137 522v748q0 63 60 63h18q61 0 61 -63v-738q0 -432 379 -432q192 0 285.5 108t93.5 327v735q0 63 58 63h24q55 0 55 -63v-742q0 -268 -134 -410.5t-386 -142.5q-251 0 -382.5 140t-131.5 407zM379 1571q0 46 27 73t71 27q48 0 75.5 -26.5t27.5 -73.5t-27 -74t-72 -27 q-46 0 -74 27t-28 74zM723 1571q0 47 26.5 73.5t71.5 26.5q46 0 74.5 -26.5t28.5 -73.5q0 -46 -27.5 -73.5t-71.5 -27.5q-47 0 -74.5 27t-27.5 74z" />
<glyph unicode="&#xdd;" horiz-adv-x="1124" d="M20 1274q-3 6 5 19.5t29.5 26.5t49.5 13q44 0 70 -47l389 -608l387 606q32 49 70 49q26 0 47 -13t29.5 -26.5t5.5 -19.5l-471 -723v-496q0 -63 -62 -63h-18q-59 0 -59 63v496zM436 1456q-4 5 32 50t100 107.5t124 104.5q49 37 94 37q26 0 44 -15.5t18 -39.5 q0 -51 -76 -103q-63 -43 -143.5 -79.5t-134 -52.5t-58.5 -9z" />
<glyph unicode="&#xde;" horiz-adv-x="1142" d="M158 55v1215q0 63 59 63h19q61 0 61 -63v-197h278q232 0 360 -104.5t128 -296.5q0 -199 -128 -303.5t-376 -104.5h-262v-209q0 -63 -61 -63h-19q-59 0 -59 63zM297 387h274q172 0 260.5 72t88.5 209q0 138 -86.5 209t-255.5 71h-281v-561z" />
<glyph unicode="&#xdf;" horiz-adv-x="1077" d="M137 55v903q0 219 111.5 336t306.5 117q172 0 278.5 -89.5t106.5 -234.5q0 -141 -84.5 -219t-247.5 -90q219 -54 312.5 -164t93.5 -272q0 -152 -110 -259.5t-265 -107.5q-126 0 -199 44t-73 96q0 22 12.5 42t25 29.5t15.5 6.5q43 -49 93 -75t130 -26q97 0 167.5 74 t70.5 176q0 56 -13.5 101t-47 89.5t-97 79.5t-156.5 58q-64 17 -89 42t-25 68q0 18 8.5 39t16.5 33l9 12q162 -2 242 48.5t80 164.5q0 98 -69 155.5t-187 57.5q-283 0 -283 -334v-901q0 -63 -59 -63h-14q-60 0 -60 63z" />
<glyph unicode="&#xe0;" horiz-adv-x="1034" d="M76 250q0 167 166.5 245.5t511.5 86.5h20v34q0 126 -71 193.5t-203 67.5q-112 0 -190.5 -50t-121.5 -131q0 -3 -19 2t-37.5 24.5t-18.5 51.5q0 28 26.5 65t74 73.5t126.5 61.5t170 25q186 0 290.5 -100.5t104.5 -277.5v-615q0 -2 -6 -5.5t-16.5 -6t-22.5 -2.5 q-39 0 -61.5 28t-22.5 101v37q-59 -86 -153.5 -133.5t-210.5 -47.5q-158 0 -247 74t-89 199zM211 250q0 -74 57 -118t156 -44q151 0 250.5 84.5t99.5 214.5v90l-57 -2q-259 -9 -382.5 -61t-123.5 -164zM250 1372q0 24 18 39.5t43 15.5q47 0 95 -36q60 -42 124 -105t100 -108 t32 -50q-5 -6 -58.5 9.5t-134 52.5t-143.5 80q-76 52 -76 102z" />
<glyph unicode="&#xe1;" horiz-adv-x="1034" d="M76 250q0 167 166.5 245.5t511.5 86.5h20v34q0 126 -71 193.5t-203 67.5q-112 0 -190.5 -50t-121.5 -131q0 -3 -19 2t-37.5 24.5t-18.5 51.5q0 28 26.5 65t74 73.5t126.5 61.5t170 25q186 0 290.5 -100.5t104.5 -277.5v-615q0 -2 -6 -5.5t-16.5 -6t-22.5 -2.5 q-39 0 -61.5 28t-22.5 101v37q-59 -86 -153.5 -133.5t-210.5 -47.5q-158 0 -247 74t-89 199zM211 250q0 -74 57 -118t156 -44q151 0 250.5 84.5t99.5 214.5v90l-57 -2q-259 -9 -382.5 -61t-123.5 -164zM371 1128q-4 5 32 50t100 108t124 105q48 36 94 36q25 0 43 -15.5 t18 -39.5q0 -51 -75 -102q-63 -43 -143.5 -80t-134 -52.5t-58.5 -9.5z" />
<glyph unicode="&#xe2;" horiz-adv-x="1034" d="M76 250q0 167 166.5 245.5t511.5 86.5h20v34q0 126 -71 193.5t-203 67.5q-112 0 -190.5 -50t-121.5 -131q0 -3 -19 2t-37.5 24.5t-18.5 51.5q0 28 26.5 65t74 73.5t126.5 61.5t170 25q186 0 290.5 -100.5t104.5 -277.5v-615q0 -2 -6 -5.5t-16.5 -6t-22.5 -2.5 q-39 0 -61.5 28t-22.5 101v37q-59 -86 -153.5 -133.5t-210.5 -47.5q-158 0 -247 74t-89 199zM211 250q0 -74 57 -118t156 -44q151 0 250.5 84.5t99.5 214.5v90l-57 -2q-259 -9 -382.5 -61t-123.5 -164zM248 1171q0 44 65.5 119t130 129t72.5 54t72 -54t129 -129t65 -119 q0 -22 -11.5 -34.5t-31.5 -12.5q-33 0 -81.5 50.5t-143.5 179.5q-97 -132 -143 -181t-78 -49q-20 0 -32.5 12.5t-12.5 34.5z" />
<glyph unicode="&#xe3;" horiz-adv-x="1034" d="M76 250q0 167 166.5 245.5t511.5 86.5h20v34q0 126 -71 193.5t-203 67.5q-112 0 -190.5 -50t-121.5 -131q0 -3 -19 2t-37.5 24.5t-18.5 51.5q0 28 26.5 65t74 73.5t126.5 61.5t170 25q186 0 290.5 -100.5t104.5 -277.5v-615q0 -2 -6 -5.5t-16.5 -6t-22.5 -2.5 q-39 0 -61.5 28t-22.5 101v37q-59 -86 -153.5 -133.5t-210.5 -47.5q-158 0 -247 74t-89 199zM211 250q0 -74 57 -118t156 -44q151 0 250.5 84.5t99.5 214.5v90l-57 -2q-259 -9 -382.5 -61t-123.5 -164zM223 1190q0 70 41.5 127t100.5 57q51 0 134.5 -44t168.5 -120 q48 150 94 150q51 0 51 -53q0 -71 -41 -128t-100 -57q-51 0 -134.5 44t-168.5 120q-48 -149 -95 -149q-51 0 -51 53z" />
<glyph unicode="&#xe4;" horiz-adv-x="1034" d="M76 250q0 167 166.5 245.5t511.5 86.5h20v34q0 126 -71 193.5t-203 67.5q-112 0 -190.5 -50t-121.5 -131q0 -3 -19 2t-37.5 24.5t-18.5 51.5q0 28 26.5 65t74 73.5t126.5 61.5t170 25q186 0 290.5 -100.5t104.5 -277.5v-615q0 -2 -6 -5.5t-16.5 -6t-22.5 -2.5 q-39 0 -61.5 28t-22.5 101v37q-59 -86 -153.5 -133.5t-210.5 -47.5q-158 0 -247 74t-89 199zM211 250q0 -74 57 -118t156 -44q151 0 250.5 84.5t99.5 214.5v90l-57 -2q-259 -9 -382.5 -61t-123.5 -164zM244 1223q0 46 27 73t71 27q47 0 74.5 -26.5t27.5 -73.5t-26.5 -74 t-71.5 -27q-46 0 -74 27t-28 74zM588 1223q0 47 26.5 73.5t71.5 26.5q46 0 74 -26.5t28 -73.5t-27 -74t-71 -27q-47 0 -74.5 27t-27.5 74z" />
<glyph unicode="&#xe5;" horiz-adv-x="1034" d="M76 250q0 167 166.5 245.5t511.5 86.5h20v34q0 126 -71 193.5t-203 67.5q-112 0 -190.5 -50t-121.5 -131q0 -3 -19 2t-37.5 24.5t-18.5 51.5q0 28 26.5 65t74 73.5t126.5 61.5t170 25q186 0 290.5 -100.5t104.5 -277.5v-615q0 -2 -6 -5.5t-16.5 -6t-22.5 -2.5 q-39 0 -61.5 28t-22.5 101v37q-59 -86 -153.5 -133.5t-210.5 -47.5q-158 0 -247 74t-89 199zM211 250q0 -74 57 -118t156 -44q151 0 250.5 84.5t99.5 214.5v90l-57 -2q-259 -9 -382.5 -61t-123.5 -164zM276 1362q0 102 69 171t171 69q104 0 171 -68.5t67 -171.5 q0 -102 -69 -171t-171 -69q-104 0 -171 68.5t-67 171.5zM377 1362q0 -62 40 -105t97 -43q60 0 98.5 42.5t38.5 105.5t-39.5 106t-97.5 43t-97.5 -43t-39.5 -106z" />
<glyph unicode="&#xe6;" horiz-adv-x="1771" d="M76 250q0 167 166.5 245.5t511.5 86.5h20v34q0 126 -71 193.5t-203 67.5q-112 0 -190.5 -50t-121.5 -131q0 -3 -19 2t-37.5 24.5t-18.5 51.5q0 28 26.5 65t74 73.5t126.5 61.5t170 25q131 0 219 -56.5t129 -160.5q61 103 161 160t228 57q206 0 330.5 -134t124.5 -357v-12 q0 -27 -11.5 -36t-43.5 -9h-758q8 -157 111.5 -257t259.5 -100q130 0 206.5 46.5t126.5 127.5q3 4 21.5 -2t37.5 -25.5t19 -47.5q0 -27 -28 -63t-77.5 -71.5t-131 -59.5t-174.5 -24q-154 0 -264 65t-165 185q-54 -112 -165.5 -180t-253.5 -68q-158 0 -247 74t-89 199z M211 250q0 -74 57 -118t156 -44q151 0 250.5 84.5t99.5 214.5v90l-57 -2q-259 -9 -382.5 -61t-123.5 -164zM893 565h668q-9 145 -93.5 231.5t-220.5 86.5q-137 0 -234.5 -88.5t-119.5 -229.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="1060" d="M72 489q0 221 142.5 365.5t358.5 144.5q101 0 185 -29.5t133 -73t75.5 -88t26.5 -79.5q0 -27 -22 -44.5t-41.5 -19t-24.5 0.5q-91 215 -332 215q-157 0 -261.5 -111t-104.5 -281t104 -282.5t262 -112.5q241 0 332 215q5 2 24.5 0.5t41.5 -19t22 -44.5q0 -35 -26.5 -79.5 t-75.5 -88.5t-133 -73.5t-185 -29.5q-216 0 -358.5 146t-142.5 368zM377 -428l123 256q10 22 14.5 30t14.5 20t22 16t31 4h10q29 0 50.5 -16.5t21.5 -43.5v-8q0 -4 -0.5 -7.5t-2.5 -8t-3.5 -7.5t-5.5 -8.5t-6 -8.5t-7.5 -10t-8.5 -10t-10 -11.5t-10 -12.5l-166 -207 q-13 -16 -46 -5.5t-21 38.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="1087" d="M72 483q0 223 139.5 369.5t351.5 146.5q206 0 330.5 -134t124.5 -357v-12q0 -27 -11.5 -36t-43.5 -9h-758q8 -157 111 -257t259 -100q131 0 207.5 46.5t126.5 127.5q3 4 21.5 -2t37.5 -25.5t19 -47.5q0 -27 -28 -63t-78 -71t-131.5 -59.5t-174.5 -24.5q-223 0 -363 142 t-140 366zM209 565h668q-9 145 -93.5 231.5t-220.5 86.5q-137 0 -234.5 -88.5t-119.5 -229.5zM266 1372q0 24 18 39.5t44 15.5q46 0 94 -36q60 -42 124 -105t100 -108t32 -50q-5 -6 -58.5 9.5t-134 52.5t-143.5 80q-76 52 -76 102z" />
<glyph unicode="&#xe9;" horiz-adv-x="1087" d="M72 483q0 223 139.5 369.5t351.5 146.5q206 0 330.5 -134t124.5 -357v-12q0 -27 -11.5 -36t-43.5 -9h-758q8 -157 111 -257t259 -100q131 0 207.5 46.5t126.5 127.5q3 4 21.5 -2t37.5 -25.5t19 -47.5q0 -27 -28 -63t-78 -71t-131.5 -59.5t-174.5 -24.5q-223 0 -363 142 t-140 366zM209 565h668q-9 145 -93.5 231.5t-220.5 86.5q-137 0 -234.5 -88.5t-119.5 -229.5zM418 1128q-4 5 32 50t100 108t124 105q48 36 94 36q25 0 43 -15.5t18 -39.5q0 -51 -75 -102q-63 -43 -143.5 -80t-134 -52.5t-58.5 -9.5z" />
<glyph unicode="&#xea;" horiz-adv-x="1087" d="M72 483q0 223 139.5 369.5t351.5 146.5q206 0 330.5 -134t124.5 -357v-12q0 -27 -11.5 -36t-43.5 -9h-758q8 -157 111 -257t259 -100q131 0 207.5 46.5t126.5 127.5q3 4 21.5 -2t37.5 -25.5t19 -47.5q0 -27 -28 -63t-78 -71t-131.5 -59.5t-174.5 -24.5q-223 0 -363 142 t-140 366zM209 565h668q-9 145 -93.5 231.5t-220.5 86.5q-137 0 -234.5 -88.5t-119.5 -229.5zM295 1171q0 44 65.5 119t130 129t72.5 54t72 -54t129 -129t65 -119q0 -22 -11.5 -34.5t-31.5 -12.5q-33 0 -81.5 50.5t-143.5 179.5q-97 -132 -143 -181t-78 -49 q-20 0 -32.5 12.5t-12.5 34.5z" />
<glyph unicode="&#xeb;" horiz-adv-x="1087" d="M72 483q0 223 139.5 369.5t351.5 146.5q206 0 330.5 -134t124.5 -357v-12q0 -27 -11.5 -36t-43.5 -9h-758q8 -157 111 -257t259 -100q131 0 207.5 46.5t126.5 127.5q3 4 21.5 -2t37.5 -25.5t19 -47.5q0 -27 -28 -63t-78 -71t-131.5 -59.5t-174.5 -24.5q-223 0 -363 142 t-140 366zM209 565h668q-9 145 -93.5 231.5t-220.5 86.5q-137 0 -234.5 -88.5t-119.5 -229.5zM291 1223q0 46 27 73t71 27q48 0 75.5 -26.5t27.5 -73.5t-27 -74t-72 -27q-46 0 -74 27t-28 74zM635 1223q0 47 26.5 73.5t71.5 26.5q46 0 74.5 -26.5t28.5 -73.5 q0 -46 -27.5 -73.5t-71.5 -27.5q-47 0 -74.5 27t-27.5 74z" />
<glyph unicode="&#xec;" d="M-84 1372q0 24 18 39.5t43 15.5q47 0 95 -36q60 -42 124 -105t100 -108t32 -50q-5 -6 -58.5 9.5t-134 52.5t-143.5 80q-76 52 -76 102zM147 55v865q0 63 60 63h14q60 0 60 -63v-865q0 -63 -60 -63h-14q-60 0 -60 63z" />
<glyph unicode="&#xed;" d="M119 1128q-4 5 32 50t100 108t124 105q48 36 94 36q25 0 43 -15.5t18 -39.5q0 -51 -75 -102q-63 -43 -143.5 -80t-134 -52.5t-58.5 -9.5zM147 55v865q0 63 60 63h14q60 0 60 -63v-865q0 -63 -60 -63h-14q-60 0 -60 63z" />
<glyph unicode="&#xee;" d="M-45 1171q0 44 65.5 119t130 129t72.5 54t72 -54t129 -129t65 -119q0 -22 -11.5 -34.5t-31.5 -12.5q-33 0 -81.5 50.5t-143.5 179.5q-97 -132 -143 -181t-78 -49q-20 0 -32.5 12.5t-12.5 34.5zM147 55v865q0 63 60 63h14q60 0 60 -63v-865q0 -63 -60 -63h-14 q-60 0 -60 63z" />
<glyph unicode="&#xef;" d="M-49 1223q0 46 27 73t71 27q48 0 75.5 -26.5t27.5 -73.5t-27 -74t-72 -27q-46 0 -74 27t-28 74zM147 55v865q0 63 60 63h14q60 0 60 -63v-865q0 -63 -60 -63h-14q-60 0 -60 63zM295 1223q0 47 26.5 73.5t71.5 26.5q46 0 74.5 -26.5t28.5 -73.5q0 -46 -27.5 -73.5 t-71.5 -27.5q-47 0 -74.5 27t-27.5 74z" />
<glyph unicode="&#xf0;" horiz-adv-x="1157" d="M88 436q0 203 133.5 327t349.5 124q111 0 208.5 -48t148.5 -116q-42 274 -252 430l-256 -147q-54 -33 -80 14l-4 8q-28 45 26 78l207 113q-167 81 -358 81q-3 4 -4.5 29t27 54.5t77.5 29.5q87 0 190.5 -32t200.5 -95l218 125q57 33 81 -14l5 -6q15 -23 8.5 -41.5 t-37.5 -36.5l-180 -99q123 -108 195.5 -271t72.5 -382q0 -275 -137 -430.5t-365 -155.5q-216 0 -345.5 124t-129.5 337zM223 438q0 -165 92 -255.5t250 -90.5q170 0 266.5 122t96.5 314v58q-40 71 -138.5 127.5t-207.5 56.5q-174 0 -266.5 -86t-92.5 -246z" />
<glyph unicode="&#xf1;" horiz-adv-x="1116" d="M137 55v914q0 2 6 5.5t16.5 6t22.5 2.5q86 0 86 -129v-33q54 82 138.5 130t193.5 48q189 0 288 -115.5t99 -314.5v-514q0 -63 -59 -63h-15q-59 0 -59 63v506q0 147 -69 230t-193 83q-111 0 -194.5 -55.5t-127.5 -150.5v-613q0 -63 -59 -63h-14q-60 0 -60 63zM252 1190 q0 70 41 127t100 57q51 0 134.5 -44t168.5 -120q48 150 95 150q51 0 51 -53q0 -70 -41.5 -127.5t-100.5 -57.5q-51 0 -134.5 44t-168.5 120q-48 -149 -94 -149q-51 0 -51 53z" />
<glyph unicode="&#xf2;" horiz-adv-x="1150" d="M72 489q0 222 142 366t361 144q218 0 360 -145.5t142 -368.5q0 -221 -142 -365.5t-360 -144.5q-219 0 -361 145.5t-142 368.5zM207 489q0 -170 105 -283.5t263 -113.5q157 0 262 111.5t105 281.5q0 171 -104.5 284.5t-262.5 113.5t-263 -112t-105 -282zM274 1372 q0 24 18 39.5t44 15.5q46 0 94 -36q60 -42 124 -105t100 -108t32 -50q-5 -6 -58.5 9.5t-134 52.5t-143.5 80q-76 52 -76 102z" />
<glyph unicode="&#xf3;" horiz-adv-x="1150" d="M72 489q0 222 142 366t361 144q218 0 360 -145.5t142 -368.5q0 -221 -142 -365.5t-360 -144.5q-219 0 -361 145.5t-142 368.5zM207 489q0 -170 105 -283.5t263 -113.5q157 0 262 111.5t105 281.5q0 171 -104.5 284.5t-262.5 113.5t-263 -112t-105 -282zM477 1128 q-4 5 32 50t100 108t124 105q48 36 94 36q25 0 43.5 -15.5t18.5 -39.5q0 -50 -76 -102q-63 -43 -143.5 -80t-134 -52.5t-58.5 -9.5z" />
<glyph unicode="&#xf4;" horiz-adv-x="1150" d="M72 489q0 222 142 366t361 144q218 0 360 -145.5t142 -368.5q0 -221 -142 -365.5t-360 -144.5q-219 0 -361 145.5t-142 368.5zM207 489q0 -170 105 -283.5t263 -113.5q157 0 262 111.5t105 281.5q0 171 -104.5 284.5t-262.5 113.5t-263 -112t-105 -282zM313 1171 q0 44 66 119t130.5 129t72.5 54t72 -54t129 -129t65 -119q0 -22 -11.5 -34.5t-31.5 -12.5q-33 0 -81.5 50.5t-143.5 179.5q-96 -131 -142.5 -180.5t-79.5 -49.5q-20 0 -32.5 12.5t-12.5 34.5z" />
<glyph unicode="&#xf5;" horiz-adv-x="1150" d="M72 489q0 222 142 366t361 144q218 0 360 -145.5t142 -368.5q0 -221 -142 -365.5t-360 -144.5q-219 0 -361 145.5t-142 368.5zM207 489q0 -170 105 -283.5t263 -113.5q157 0 262 111.5t105 281.5q0 171 -104.5 284.5t-262.5 113.5t-263 -112t-105 -282zM289 1190 q0 70 41 127t100 57q51 0 134.5 -44t168.5 -120q48 150 94 150q52 0 52 -53q0 -70 -41.5 -127.5t-100.5 -57.5q-51 0 -134.5 44t-168.5 120q-48 -149 -94 -149q-51 0 -51 53z" />
<glyph unicode="&#xf6;" horiz-adv-x="1150" d="M72 489q0 222 142 366t361 144q218 0 360 -145.5t142 -368.5q0 -221 -142 -365.5t-360 -144.5q-219 0 -361 145.5t-142 368.5zM207 489q0 -170 105 -283.5t263 -113.5q157 0 262 111.5t105 281.5q0 171 -104.5 284.5t-262.5 113.5t-263 -112t-105 -282zM309 1223 q0 46 27.5 73t71.5 27q47 0 74.5 -26.5t27.5 -73.5t-26.5 -74t-71.5 -27q-46 0 -74.5 27t-28.5 74zM653 1223q0 47 27 73.5t72 26.5q46 0 74 -26.5t28 -73.5t-27 -74t-71 -27q-47 0 -75 27t-28 74z" />
<glyph unicode="&#xf7;" horiz-adv-x="1228" d="M178 543v14q0 28 14.5 41.5t49.5 13.5h743q35 0 49.5 -13.5t14.5 -41.5v-14q0 -28 -14.5 -42t-49.5 -14h-743q-35 0 -49.5 14t-14.5 42zM512 211v20q0 43 24 68t66 25h21q44 0 68 -25t24 -68v-20q0 -43 -24 -66.5t-68 -23.5h-21q-43 0 -66.5 23.5t-23.5 66.5zM512 866v21 q0 43 23.5 67.5t66.5 24.5h21q44 0 68 -24.5t24 -67.5v-21q0 -43 -24 -66.5t-68 -23.5h-21q-43 0 -66.5 23.5t-23.5 66.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1150" d="M84 -12q0 40 45 90l72 78q-113 142 -113 329q0 141 66.5 259.5t180.5 186.5t249 68q165 0 297 -98l131 146q3 3 18 -4t29 -23t14 -35q0 -40 -45 -90l-65 -72q114 -142 114 -334q0 -106 -39 -201t-105.5 -163.5t-158 -109t-192.5 -40.5q-166 0 -301 101l-136 -150 q-3 -3 -18 4t-29 23t-14 35zM223 487q0 -131 72 -237l502 553q-95 76 -215 76q-155 0 -257 -112t-102 -280zM365 174q97 -78 219 -78q155 0 256.5 111.5t101.5 279.5q0 136 -74 240z" />
<glyph unicode="&#xf9;" horiz-adv-x="1116" d="M129 406v514q0 63 59 63h15q59 0 59 -63v-506q0 -148 71.5 -231t201.5 -83q103 0 185 56t126 151v613q0 63 59 63h15q59 0 59 -63v-914q0 -2 -6 -5.5t-16.5 -6t-22.5 -2.5q-86 0 -86 129v31q-55 -82 -139.5 -129.5t-192.5 -47.5q-189 0 -288 116t-99 315zM250 1372 q0 24 18 39.5t43 15.5q47 0 95 -36q60 -42 124 -105t100 -108t32 -50q-5 -6 -58.5 9.5t-134 52.5t-143.5 80q-76 52 -76 102z" />
<glyph unicode="&#xfa;" horiz-adv-x="1116" d="M129 406v514q0 63 59 63h15q59 0 59 -63v-506q0 -148 71.5 -231t201.5 -83q103 0 185 56t126 151v613q0 63 59 63h15q59 0 59 -63v-914q0 -2 -6 -5.5t-16.5 -6t-22.5 -2.5q-86 0 -86 129v31q-55 -82 -139.5 -129.5t-192.5 -47.5q-189 0 -288 116t-99 315zM473 1128 q-4 5 32 50t100 108t124 105q48 36 94 36q25 0 43.5 -15.5t18.5 -39.5q0 -50 -76 -102q-63 -43 -143.5 -80t-134 -52.5t-58.5 -9.5z" />
<glyph unicode="&#xfb;" horiz-adv-x="1116" d="M129 406v514q0 63 59 63h15q59 0 59 -63v-506q0 -148 71.5 -231t201.5 -83q103 0 185 56t126 151v613q0 63 59 63h15q59 0 59 -63v-914q0 -2 -6 -5.5t-16.5 -6t-22.5 -2.5q-86 0 -86 129v31q-55 -82 -139.5 -129.5t-192.5 -47.5q-189 0 -288 116t-99 315zM293 1171 q0 44 65.5 119t130 129t72.5 54t72 -54t129 -129t65 -119q0 -22 -11.5 -34.5t-31.5 -12.5q-33 0 -81.5 50.5t-143.5 179.5q-97 -132 -143 -181t-78 -49q-20 0 -32.5 12.5t-12.5 34.5z" />
<glyph unicode="&#xfc;" horiz-adv-x="1116" d="M129 406v514q0 63 59 63h15q59 0 59 -63v-506q0 -148 71.5 -231t201.5 -83q103 0 185 56t126 151v613q0 63 59 63h15q59 0 59 -63v-914q0 -2 -6 -5.5t-16.5 -6t-22.5 -2.5q-86 0 -86 129v31q-55 -82 -139.5 -129.5t-192.5 -47.5q-189 0 -288 116t-99 315zM291 1223 q0 46 27 73t71 27q48 0 75.5 -26.5t27.5 -73.5t-27 -74t-72 -27q-46 0 -74 27t-28 74zM635 1223q0 47 26.5 73.5t71.5 26.5q46 0 74.5 -26.5t28.5 -73.5q0 -46 -27.5 -73.5t-71.5 -27.5q-47 0 -74.5 27t-27.5 74z" />
<glyph unicode="&#xfd;" horiz-adv-x="966" d="M31 944q-1 5 5 13.5t22.5 17t37.5 8.5q36 0 58 -19.5t43 -72.5l309 -764l268 760q18 52 41.5 74t56.5 22q17 0 34 -7.5t26 -16t8 -11.5l-471 -1263q-33 -88 -94 -88q-17 0 -34.5 7.5t-27 15.5t-8.5 11l164 418q-34 0 -51.5 10.5t-30.5 40.5zM379 1108q-4 5 32 50 t100 107.5t124 104.5q49 37 94 37q25 0 43.5 -15.5t18.5 -39.5q0 -51 -76 -103q-63 -43 -143.5 -79.5t-134 -52.5t-58.5 -9z" />
<glyph unicode="&#xfe;" horiz-adv-x="1185" d="M137 -340v1720q0 2 6 5.5t16.5 6.5t22.5 3q88 0 88 -129v-451q138 184 363 184q197 0 329 -141t132 -369t-132.5 -371t-336.5 -143q-122 0 -215.5 54.5t-139.5 132.5v-502q0 -63 -59 -63h-14q-60 0 -60 63zM270 387q13 -126 112.5 -210.5t231.5 -84.5q155 0 249.5 109 t94.5 286q0 172 -96 284t-244 112q-124 0 -212.5 -60t-135.5 -168v-268z" />
<glyph unicode="&#xff;" horiz-adv-x="966" d="M31 944q-1 5 5 13.5t22.5 17t37.5 8.5q36 0 58 -19.5t43 -72.5l309 -764l268 760q18 52 41.5 74t56.5 22q17 0 34 -7.5t26 -16t8 -11.5l-471 -1263q-33 -88 -94 -88q-17 0 -34.5 7.5t-27 15.5t-8.5 11l164 418q-34 0 -51.5 10.5t-30.5 40.5zM219 1223q0 46 27 73t71 27 q48 0 75.5 -26.5t27.5 -73.5t-26.5 -74t-71.5 -27q-46 0 -74.5 27t-28.5 74zM563 1223q0 47 27 73.5t72 26.5q46 0 74 -26.5t28 -73.5t-27 -74t-71 -27q-47 0 -75 27t-28 74z" />
<glyph unicode="&#x152;" horiz-adv-x="2111" d="M102 659q0 200 84.5 358t235.5 245.5t342 87.5q98 0 190 -25h1006q63 0 63 -57v-13q0 -57 -63 -57h-764q197 -167 221 -457h398q63 0 63 -57v-8q0 -58 -63 -58h-396q-15 -309 -225 -491h778q64 0 64 -55v-13q0 -59 -64 -59h-1018q-92 -25 -194 -25q-292 0 -475 190 t-183 494zM246 664q0 -249 146 -408.5t372 -159.5t370 157t144 406q0 163 -66.5 293.5t-184.5 203.5t-267 73q-226 0 -370 -158t-144 -407z" />
<glyph unicode="&#x153;" horiz-adv-x="1955" d="M72 489q0 222 142 366t361 144q145 0 258.5 -69.5t174.5 -190.5q59 122 170 191t254 69q206 0 330 -134t124 -357v-12q0 -27 -11.5 -36t-43.5 -9h-758q8 -157 111.5 -257t259.5 -100q131 0 207.5 46.5t126.5 127.5q3 4 21.5 -2t37 -25.5t18.5 -47.5q0 -27 -28 -63 t-77.5 -71.5t-131 -59.5t-174.5 -24q-155 0 -269.5 66t-173.5 188q-61 -118 -172 -186t-254 -68q-219 0 -361 145.5t-142 368.5zM207 489q0 -170 105 -283.5t263 -113.5q157 0 262 111.5t105 281.5q0 171 -104.5 284.5t-262.5 113.5t-263 -112t-105 -282zM1077 565h668 q-9 146 -93 232t-220 86q-137 0 -235 -89t-120 -229z" />
<glyph unicode="&#x178;" horiz-adv-x="1124" d="M20 1274q-3 6 5 19.5t29.5 26.5t49.5 13q44 0 70 -47l389 -608l387 606q32 49 70 49q26 0 47 -13t29.5 -26.5t5.5 -19.5l-471 -723v-496q0 -63 -62 -63h-18q-59 0 -59 63v496zM289 1571q0 46 27 73t71 27q47 0 74.5 -26.5t27.5 -73.5t-26.5 -74t-71.5 -27q-46 0 -74 27 t-28 74zM633 1571q0 47 26.5 73.5t71.5 26.5q46 0 74.5 -26.5t28.5 -73.5q0 -46 -27.5 -73.5t-71.5 -27.5q-47 0 -74.5 27t-27.5 74z" />
<glyph unicode="&#x2c6;" horiz-adv-x="1024" d="M244 1171q0 44 65.5 119t130 129t72.5 54t72 -54t129 -129t65 -119q0 -22 -11.5 -34.5t-31.5 -12.5q-33 0 -81.5 50.5t-143.5 179.5q-97 -132 -143 -181t-78 -49q-20 0 -32.5 12.5t-12.5 34.5z" />
<glyph unicode="&#x2dc;" horiz-adv-x="1024" d="M217 1190q0 70 41 127t100 57q51 0 135 -44t169 -120q48 150 94 150q51 0 51 -53q0 -71 -41 -128t-100 -57q-51 0 -135 44t-169 120q-48 -149 -94 -149q-51 0 -51 53z" />
<glyph unicode="&#x2000;" horiz-adv-x="910" />
<glyph unicode="&#x2001;" horiz-adv-x="1821" />
<glyph unicode="&#x2002;" horiz-adv-x="910" />
<glyph unicode="&#x2003;" horiz-adv-x="1821" />
<glyph unicode="&#x2004;" horiz-adv-x="607" />
<glyph unicode="&#x2005;" horiz-adv-x="455" />
<glyph unicode="&#x2006;" horiz-adv-x="303" />
<glyph unicode="&#x2007;" horiz-adv-x="303" />
<glyph unicode="&#x2008;" horiz-adv-x="227" />
<glyph unicode="&#x2009;" horiz-adv-x="364" />
<glyph unicode="&#x200a;" horiz-adv-x="101" />
<glyph unicode="&#x2010;" horiz-adv-x="749" d="M92 487v15q0 55 64 55h438q63 0 63 -55v-15q0 -53 -63 -53h-438q-64 0 -64 53z" />
<glyph unicode="&#x2011;" horiz-adv-x="749" d="M92 487v15q0 55 64 55h438q63 0 63 -55v-15q0 -53 -63 -53h-438q-64 0 -64 53z" />
<glyph unicode="&#x2012;" horiz-adv-x="749" d="M92 487v15q0 55 64 55h438q63 0 63 -55v-15q0 -53 -63 -53h-438q-64 0 -64 53z" />
<glyph unicode="&#x2013;" horiz-adv-x="1075" d="M92 489v13q0 27 14.5 40t49.5 13h764q35 0 49 -12.5t14 -40.5v-13q0 -28 -14 -40.5t-49 -12.5h-764q-35 0 -49.5 13t-14.5 40z" />
<glyph unicode="&#x2014;" horiz-adv-x="1419" d="M92 489v13q0 27 14.5 40t49.5 13h1108q35 0 49 -12.5t14 -40.5v-13q0 -28 -14 -40.5t-49 -12.5h-1108q-35 0 -49.5 13t-14.5 40z" />
<glyph unicode="&#x2018;" horiz-adv-x="413" d="M86 946v6q0 46 35 101l172 288q6 11 25.5 12.5t34 -10t7.5 -32.5l-120 -365q-16 -41 -31.5 -57.5t-50.5 -16.5h-8q-27 0 -45.5 20t-18.5 54z" />
<glyph unicode="&#x2019;" horiz-adv-x="413" d="M53 915l123 365q15 42 30 58t50 16h10q26 0 44 -20t18 -54v-6q0 -42 -33 -100l-174 -289q-6 -11 -26 -12t-34.5 10.5t-7.5 31.5z" />
<glyph unicode="&#x201a;" horiz-adv-x="413" d="M33 -233l123 364q15 42 30 58t50 16h10q26 0 43.5 -20t17.5 -54v-6q0 -42 -33 -100l-174 -289q-6 -11 -26 -12t-34 11t-7 32z" />
<glyph unicode="&#x201c;" horiz-adv-x="727" d="M86 946v6q0 43 33 101l174 288q7 11 26 12t33 -10.5t8 -31.5l-122 -365q-13 -42 -28.5 -58t-51.5 -16h-11q-26 0 -43.5 20t-17.5 54zM401 946v6q0 43 33 101l174 288q7 11 26.5 12t33.5 -10.5t8 -31.5l-123 -365q-13 -42 -28.5 -58t-51.5 -16h-10q-26 0 -44 20t-18 54z " />
<glyph unicode="&#x201d;" horiz-adv-x="727" d="M51 915l123 365q15 42 30 58t50 16h10q26 0 44 -20t18 -54v-6q0 -42 -33 -100l-174 -289q-6 -11 -26 -12t-34.5 10.5t-7.5 31.5zM367 915l122 365q15 42 30 58t50 16h11q26 0 43.5 -20t17.5 -54v-6q0 -42 -33 -100l-174 -289q-6 -11 -26 -12t-34 10.5t-7 31.5z" />
<glyph unicode="&#x201e;" horiz-adv-x="727" d="M31 -233l123 364q11 30 17.5 42.5t21.5 22t40 9.5h11q26 0 43.5 -20t17.5 -54v-6q0 -42 -33 -100l-174 -289q-6 -11 -26 -12t-34 11t-7 32zM346 -233l123 364q15 42 30 58t50 16h10q26 0 44 -20t18 -54v-6q0 -42 -33 -100l-174 -289q-6 -11 -26 -12t-34.5 11t-7.5 32z " />
<glyph unicode="&#x2022;" horiz-adv-x="663" d="M123 496q0 88 61 150.5t148 62.5t148 -62.5t61 -150.5t-60.5 -149.5t-148.5 -61.5t-148.5 61.5t-60.5 149.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="1284" d="M113 78v20q0 43 23.5 67.5t66.5 24.5h20q44 0 68 -24.5t24 -67.5v-20q0 -43 -24 -66.5t-68 -23.5h-20q-43 0 -66.5 23.5t-23.5 66.5zM541 78v20q0 43 23.5 67.5t66.5 24.5h20q44 0 68 -24.5t24 -67.5v-20q0 -43 -24 -66.5t-68 -23.5h-20q-43 0 -66.5 23.5t-23.5 66.5z M969 78v20q0 43 23.5 67.5t66.5 24.5h20q44 0 68 -24.5t24 -67.5v-20q0 -43 -24 -66.5t-68 -23.5h-20q-43 0 -66.5 23.5t-23.5 66.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="364" />
<glyph unicode="&#x2039;" horiz-adv-x="638" d="M92 498q0 11 45.5 64.5t106.5 114.5t130.5 110t110.5 49h2q21 0 35.5 -15t14.5 -33v-4q0 -39 -83.5 -109.5t-236.5 -178.5q320 -222 320 -285v-6q0 -18 -14.5 -31.5t-35.5 -13.5h-2q-53 0 -150.5 79t-170 161t-72.5 98z" />
<glyph unicode="&#x203a;" horiz-adv-x="638" d="M104 205v6q0 66 320 285q-151 105 -235.5 177t-84.5 111v4q0 18 14 33t34 15h2q41 0 110.5 -49t130.5 -110t106.5 -114.5t45.5 -64.5q0 -16 -72.5 -98t-170 -161t-150.5 -79h-2q-20 0 -34 13.5t-14 31.5z" />
<glyph unicode="&#x205f;" horiz-adv-x="455" />
<glyph unicode="&#x20ac;" horiz-adv-x="1282" d="M74 500v12q0 28 14 40.5t49 12.5h127v146q0 39 2 59h-129q-35 0 -49 12.5t-14 40.5v13q0 28 14 40.5t49 12.5h144q40 214 174 337.5t331 123.5q92 0 172 -26t130.5 -65.5t79 -82t28.5 -78.5q0 -28 -19.5 -48t-38.5 -26t-22 -2q-57 104 -137.5 157.5t-200.5 53.5 q-135 0 -230.5 -92t-131.5 -252h442q35 0 49.5 -13t14.5 -40v-13q0 -27 -14.5 -40t-49.5 -13h-459q-2 -18 -2 -57v-148h461q35 0 49.5 -13t14.5 -40v-12q0 -28 -14.5 -41t-49.5 -13h-444q36 -162 136 -257t241 -95q121 0 200 58.5t131 171.5q3 5 22.5 -0.5t39.5 -26 t20 -51.5q0 -38 -29.5 -84t-80 -88t-130 -70.5t-167.5 -28.5q-210 0 -345.5 124t-172.5 347h-142q-35 0 -49 13t-14 41z" />
<glyph unicode="&#x2122;" horiz-adv-x="1386" d="M43 1274v8q0 45 49 45h438q50 0 50 -45v-8q0 -45 -50 -45h-163v-479q0 -52 -50 -52h-14q-45 0 -45 52v479h-166q-49 0 -49 45zM690 750v524q0 59 53 59h52q26 0 38.5 -10.5t22.5 -34.5l129 -319l129 319q11 25 24.5 35t37.5 10h47q25 0 39 -15.5t14 -43.5v-524 q0 -52 -45 -52h-14q-43 0 -43 52v436l-134 -330q-4 -11 -7 -15.5t-15.5 -9t-34.5 -4.5t-34 5t-14.5 9t-6.5 15l-135 328v-434q0 -52 -41 -52h-19q-43 0 -43 52z" />
<glyph unicode="&#x25fc;" horiz-adv-x="983" d="M0 0v983h983v-983h-983z" />
<hkern u1="&#x26;" u2="&#x178;" k="61" />
<hkern u1="&#x26;" u2="&#xdd;" k="61" />
<hkern u1="&#x26;" u2="&#xc6;" k="-82" />
<hkern u1="&#x26;" u2="&#xc5;" k="-82" />
<hkern u1="&#x26;" u2="&#xc4;" k="-82" />
<hkern u1="&#x26;" u2="&#xc3;" k="-82" />
<hkern u1="&#x26;" u2="&#xc2;" k="-82" />
<hkern u1="&#x26;" u2="&#xc1;" k="-82" />
<hkern u1="&#x26;" u2="&#xc0;" k="-82" />
<hkern u1="&#x26;" u2="Y" k="61" />
<hkern u1="&#x26;" u2="X" k="-41" />
<hkern u1="&#x26;" u2="W" k="41" />
<hkern u1="&#x26;" u2="V" k="41" />
<hkern u1="&#x26;" u2="T" k="82" />
<hkern u1="&#x26;" u2="J" k="-41" />
<hkern u1="&#x26;" u2="A" k="-70" />
<hkern u1="&#x28;" u2="&#x178;" k="-102" />
<hkern u1="&#x28;" u2="&#xdd;" k="-102" />
<hkern u1="&#x28;" u2="j" k="-307" />
<hkern u1="&#x28;" u2="g" k="-41" />
<hkern u1="&#x28;" u2="Y" k="-102" />
<hkern u1="&#x28;" u2="X" k="-82" />
<hkern u1="&#x28;" u2="W" k="-20" />
<hkern u1="&#x28;" u2="V" k="-61" />
<hkern u1="&#x2a;" u2="&#xc6;" k="123" />
<hkern u1="&#x2a;" u2="&#xc5;" k="123" />
<hkern u1="&#x2a;" u2="&#xc4;" k="123" />
<hkern u1="&#x2a;" u2="&#xc3;" k="123" />
<hkern u1="&#x2a;" u2="&#xc2;" k="123" />
<hkern u1="&#x2a;" u2="&#xc1;" k="123" />
<hkern u1="&#x2a;" u2="&#xc0;" k="123" />
<hkern u1="&#x2a;" u2="A" k="123" />
<hkern u1="&#x2c;" u2="&#x178;" k="102" />
<hkern u1="&#x2c;" u2="&#x153;" k="41" />
<hkern u1="&#x2c;" u2="&#x152;" k="82" />
<hkern u1="&#x2c;" u2="&#xe7;" k="41" />
<hkern u1="&#x2c;" u2="&#xe6;" k="41" />
<hkern u1="&#x2c;" u2="&#xdd;" k="102" />
<hkern u1="&#x2c;" u2="&#xdc;" k="20" />
<hkern u1="&#x2c;" u2="&#xdb;" k="20" />
<hkern u1="&#x2c;" u2="&#xda;" k="20" />
<hkern u1="&#x2c;" u2="&#xd9;" k="20" />
<hkern u1="&#x2c;" u2="&#xd8;" k="82" />
<hkern u1="&#x2c;" u2="&#xd6;" k="82" />
<hkern u1="&#x2c;" u2="&#xd5;" k="82" />
<hkern u1="&#x2c;" u2="&#xd4;" k="82" />
<hkern u1="&#x2c;" u2="&#xd3;" k="82" />
<hkern u1="&#x2c;" u2="&#xd2;" k="82" />
<hkern u1="&#x2c;" u2="&#xc6;" k="-61" />
<hkern u1="&#x2c;" u2="&#xc5;" k="-61" />
<hkern u1="&#x2c;" u2="&#xc4;" k="-61" />
<hkern u1="&#x2c;" u2="&#xc3;" k="-61" />
<hkern u1="&#x2c;" u2="&#xc2;" k="-61" />
<hkern u1="&#x2c;" u2="&#xc1;" k="-61" />
<hkern u1="&#x2c;" u2="&#xc0;" k="-61" />
<hkern u1="&#x2c;" u2="y" k="61" />
<hkern u1="&#x2c;" u2="w" k="20" />
<hkern u1="&#x2c;" u2="v" k="61" />
<hkern u1="&#x2c;" u2="u" k="41" />
<hkern u1="&#x2c;" u2="t" k="82" />
<hkern u1="&#x2c;" u2="r" k="41" />
<hkern u1="&#x2c;" u2="q" k="41" />
<hkern u1="&#x2c;" u2="p" k="41" />
<hkern u1="&#x2c;" u2="o" k="41" />
<hkern u1="&#x2c;" u2="n" k="41" />
<hkern u1="&#x2c;" u2="m" k="41" />
<hkern u1="&#x2c;" u2="e" k="41" />
<hkern u1="&#x2c;" u2="d" k="41" />
<hkern u1="&#x2c;" u2="c" k="41" />
<hkern u1="&#x2c;" u2="a" k="41" />
<hkern u1="&#x2c;" u2="Y" k="102" />
<hkern u1="&#x2c;" u2="W" k="41" />
<hkern u1="&#x2c;" u2="V" k="123" />
<hkern u1="&#x2c;" u2="U" k="20" />
<hkern u1="&#x2c;" u2="T" k="123" />
<hkern u1="&#x2c;" u2="Q" k="82" />
<hkern u1="&#x2c;" u2="O" k="82" />
<hkern u1="&#x2c;" u2="G" k="82" />
<hkern u1="&#x2c;" u2="C" k="82" />
<hkern u1="&#x2c;" u2="A" k="-61" />
<hkern u1="&#x2c;" u2="&#x39;" k="20" />
<hkern u1="&#x2c;" u2="&#x38;" k="31" />
<hkern u1="&#x2c;" u2="&#x36;" k="61" />
<hkern u1="&#x2c;" u2="&#x34;" k="102" />
<hkern u1="&#x2c;" u2="&#x31;" k="123" />
<hkern u1="&#x2c;" u2="&#x30;" k="82" />
<hkern u1="&#x2d;" u2="&#x178;" k="102" />
<hkern u1="&#x2d;" u2="&#x153;" k="20" />
<hkern u1="&#x2d;" u2="&#xe7;" k="20" />
<hkern u1="&#x2d;" u2="&#xe6;" k="20" />
<hkern u1="&#x2d;" u2="&#xdd;" k="102" />
<hkern u1="&#x2d;" u2="&#xc6;" k="-20" />
<hkern u1="&#x2d;" u2="&#xc5;" k="-20" />
<hkern u1="&#x2d;" u2="&#xc4;" k="-20" />
<hkern u1="&#x2d;" u2="&#xc3;" k="-20" />
<hkern u1="&#x2d;" u2="&#xc2;" k="-20" />
<hkern u1="&#x2d;" u2="&#xc1;" k="-20" />
<hkern u1="&#x2d;" u2="&#xc0;" k="-20" />
<hkern u1="&#x2d;" u2="z" k="61" />
<hkern u1="&#x2d;" u2="y" k="20" />
<hkern u1="&#x2d;" u2="x" k="61" />
<hkern u1="&#x2d;" u2="v" k="20" />
<hkern u1="&#x2d;" u2="q" k="20" />
<hkern u1="&#x2d;" u2="o" k="20" />
<hkern u1="&#x2d;" u2="e" k="20" />
<hkern u1="&#x2d;" u2="d" k="20" />
<hkern u1="&#x2d;" u2="c" k="20" />
<hkern u1="&#x2d;" u2="a" k="20" />
<hkern u1="&#x2d;" u2="Z" k="20" />
<hkern u1="&#x2d;" u2="Y" k="102" />
<hkern u1="&#x2d;" u2="X" k="61" />
<hkern u1="&#x2d;" u2="W" k="82" />
<hkern u1="&#x2d;" u2="V" k="102" />
<hkern u1="&#x2d;" u2="T" k="143" />
<hkern u1="&#x2d;" u2="A" k="-20" />
<hkern u1="&#x2d;" u2="&#x37;" k="61" />
<hkern u1="&#x2d;" u2="&#x31;" k="41" />
<hkern u1="&#x2e;" u2="&#x178;" k="102" />
<hkern u1="&#x2e;" u2="&#x153;" k="41" />
<hkern u1="&#x2e;" u2="&#x152;" k="82" />
<hkern u1="&#x2e;" u2="&#xe7;" k="41" />
<hkern u1="&#x2e;" u2="&#xe6;" k="41" />
<hkern u1="&#x2e;" u2="&#xdd;" k="102" />
<hkern u1="&#x2e;" u2="&#xdc;" k="20" />
<hkern u1="&#x2e;" u2="&#xdb;" k="20" />
<hkern u1="&#x2e;" u2="&#xda;" k="20" />
<hkern u1="&#x2e;" u2="&#xd9;" k="20" />
<hkern u1="&#x2e;" u2="&#xd8;" k="82" />
<hkern u1="&#x2e;" u2="&#xd6;" k="82" />
<hkern u1="&#x2e;" u2="&#xd5;" k="82" />
<hkern u1="&#x2e;" u2="&#xd4;" k="82" />
<hkern u1="&#x2e;" u2="&#xd3;" k="82" />
<hkern u1="&#x2e;" u2="&#xd2;" k="82" />
<hkern u1="&#x2e;" u2="&#xc6;" k="-61" />
<hkern u1="&#x2e;" u2="&#xc5;" k="-61" />
<hkern u1="&#x2e;" u2="&#xc4;" k="-61" />
<hkern u1="&#x2e;" u2="&#xc3;" k="-61" />
<hkern u1="&#x2e;" u2="&#xc2;" k="-61" />
<hkern u1="&#x2e;" u2="&#xc1;" k="-61" />
<hkern u1="&#x2e;" u2="&#xc0;" k="-61" />
<hkern u1="&#x2e;" u2="y" k="61" />
<hkern u1="&#x2e;" u2="w" k="20" />
<hkern u1="&#x2e;" u2="v" k="61" />
<hkern u1="&#x2e;" u2="u" k="41" />
<hkern u1="&#x2e;" u2="t" k="82" />
<hkern u1="&#x2e;" u2="r" k="41" />
<hkern u1="&#x2e;" u2="q" k="41" />
<hkern u1="&#x2e;" u2="p" k="41" />
<hkern u1="&#x2e;" u2="o" k="41" />
<hkern u1="&#x2e;" u2="n" k="41" />
<hkern u1="&#x2e;" u2="m" k="41" />
<hkern u1="&#x2e;" u2="e" k="41" />
<hkern u1="&#x2e;" u2="d" k="41" />
<hkern u1="&#x2e;" u2="c" k="41" />
<hkern u1="&#x2e;" u2="a" k="41" />
<hkern u1="&#x2e;" u2="Y" k="102" />
<hkern u1="&#x2e;" u2="W" k="41" />
<hkern u1="&#x2e;" u2="V" k="123" />
<hkern u1="&#x2e;" u2="U" k="20" />
<hkern u1="&#x2e;" u2="T" k="123" />
<hkern u1="&#x2e;" u2="Q" k="82" />
<hkern u1="&#x2e;" u2="O" k="82" />
<hkern u1="&#x2e;" u2="G" k="82" />
<hkern u1="&#x2e;" u2="C" k="82" />
<hkern u1="&#x2e;" u2="A" k="-61" />
<hkern u1="&#x2e;" u2="&#x39;" k="20" />
<hkern u1="&#x2e;" u2="&#x38;" k="31" />
<hkern u1="&#x2e;" u2="&#x36;" k="61" />
<hkern u1="&#x2e;" u2="&#x34;" k="102" />
<hkern u1="&#x2e;" u2="&#x31;" k="123" />
<hkern u1="&#x2e;" u2="&#x30;" k="82" />
<hkern u1="&#x2f;" u2="&#x153;" k="20" />
<hkern u1="&#x2f;" u2="&#x152;" k="-20" />
<hkern u1="&#x2f;" u2="&#xe7;" k="20" />
<hkern u1="&#x2f;" u2="&#xe6;" k="20" />
<hkern u1="&#x2f;" u2="&#xd8;" k="-20" />
<hkern u1="&#x2f;" u2="&#xd6;" k="-20" />
<hkern u1="&#x2f;" u2="&#xd5;" k="-20" />
<hkern u1="&#x2f;" u2="&#xd4;" k="-20" />
<hkern u1="&#x2f;" u2="&#xd3;" k="-20" />
<hkern u1="&#x2f;" u2="&#xd2;" k="-20" />
<hkern u1="&#x2f;" u2="q" k="20" />
<hkern u1="&#x2f;" u2="o" k="20" />
<hkern u1="&#x2f;" u2="g" k="20" />
<hkern u1="&#x2f;" u2="e" k="20" />
<hkern u1="&#x2f;" u2="d" k="20" />
<hkern u1="&#x2f;" u2="c" k="20" />
<hkern u1="&#x2f;" u2="a" k="20" />
<hkern u1="&#x2f;" u2="Q" k="-20" />
<hkern u1="&#x2f;" u2="O" k="-20" />
<hkern u1="&#x2f;" u2="G" k="-20" />
<hkern u1="&#x2f;" u2="C" k="-20" />
<hkern u1="&#x2f;" u2="&#x34;" k="41" />
<hkern u1="&#x2f;" u2="&#x31;" k="-41" />
<hkern u1="&#x30;" u2="&#x2026;" k="82" />
<hkern u1="&#x30;" u2="&#x201e;" k="82" />
<hkern u1="&#x30;" u2="&#x201a;" k="82" />
<hkern u1="&#x30;" u2="&#x37;" k="41" />
<hkern u1="&#x30;" u2="&#x2e;" k="82" />
<hkern u1="&#x30;" u2="&#x2c;" k="82" />
<hkern u1="&#x32;" u2="&#x2014;" k="20" />
<hkern u1="&#x32;" u2="&#x2013;" k="20" />
<hkern u1="&#x32;" u2="&#x34;" k="61" />
<hkern u1="&#x32;" u2="&#x2d;" k="20" />
<hkern u1="&#x33;" u2="&#x2026;" k="41" />
<hkern u1="&#x33;" u2="&#x201e;" k="41" />
<hkern u1="&#x33;" u2="&#x201a;" k="41" />
<hkern u1="&#x33;" u2="&#x37;" k="41" />
<hkern u1="&#x33;" u2="&#x2e;" k="41" />
<hkern u1="&#x33;" u2="&#x2c;" k="41" />
<hkern u1="&#x34;" u2="&#x2122;" k="102" />
<hkern u1="&#x34;" u2="&#xb0;" k="82" />
<hkern u1="&#x34;" u2="&#x37;" k="20" />
<hkern u1="&#x35;" u2="&#x2026;" k="20" />
<hkern u1="&#x35;" u2="&#x201e;" k="20" />
<hkern u1="&#x35;" u2="&#x201a;" k="20" />
<hkern u1="&#x35;" u2="&#x37;" k="20" />
<hkern u1="&#x35;" u2="&#x2e;" k="20" />
<hkern u1="&#x35;" u2="&#x2c;" k="20" />
<hkern u1="&#x36;" u2="&#x2026;" k="20" />
<hkern u1="&#x36;" u2="&#x201e;" k="20" />
<hkern u1="&#x36;" u2="&#x201a;" k="20" />
<hkern u1="&#x36;" u2="&#x37;" k="20" />
<hkern u1="&#x36;" u2="&#x2e;" k="20" />
<hkern u1="&#x36;" u2="&#x2c;" k="20" />
<hkern u1="&#x37;" u2="&#x2026;" k="164" />
<hkern u1="&#x37;" u2="&#x201e;" k="164" />
<hkern u1="&#x37;" u2="&#x201a;" k="164" />
<hkern u1="&#x37;" u2="&#x2014;" k="82" />
<hkern u1="&#x37;" u2="&#x2013;" k="82" />
<hkern u1="&#x37;" u2="&#xa2;" k="82" />
<hkern u1="&#x37;" u2="&#x3b;" k="41" />
<hkern u1="&#x37;" u2="&#x3a;" k="41" />
<hkern u1="&#x37;" u2="&#x39;" k="-20" />
<hkern u1="&#x37;" u2="&#x35;" k="-31" />
<hkern u1="&#x37;" u2="&#x34;" k="123" />
<hkern u1="&#x37;" u2="&#x33;" k="-41" />
<hkern u1="&#x37;" u2="&#x31;" k="-41" />
<hkern u1="&#x37;" u2="&#x30;" k="20" />
<hkern u1="&#x37;" u2="&#x2e;" k="164" />
<hkern u1="&#x37;" u2="&#x2d;" k="82" />
<hkern u1="&#x37;" u2="&#x2c;" k="164" />
<hkern u1="&#x38;" u2="&#x2026;" k="31" />
<hkern u1="&#x38;" u2="&#x201e;" k="31" />
<hkern u1="&#x38;" u2="&#x201a;" k="31" />
<hkern u1="&#x38;" u2="&#x37;" k="31" />
<hkern u1="&#x38;" u2="&#x2e;" k="31" />
<hkern u1="&#x38;" u2="&#x2c;" k="31" />
<hkern u1="&#x39;" u2="&#x2026;" k="61" />
<hkern u1="&#x39;" u2="&#x201e;" k="61" />
<hkern u1="&#x39;" u2="&#x201a;" k="61" />
<hkern u1="&#x39;" u2="&#x37;" k="92" />
<hkern u1="&#x39;" u2="&#x2e;" k="61" />
<hkern u1="&#x39;" u2="&#x2c;" k="61" />
<hkern u1="&#x3a;" u2="&#x178;" k="61" />
<hkern u1="&#x3a;" u2="&#x153;" k="20" />
<hkern u1="&#x3a;" u2="&#xe7;" k="20" />
<hkern u1="&#x3a;" u2="&#xe6;" k="20" />
<hkern u1="&#x3a;" u2="&#xdd;" k="61" />
<hkern u1="&#x3a;" u2="q" k="20" />
<hkern u1="&#x3a;" u2="o" k="20" />
<hkern u1="&#x3a;" u2="e" k="20" />
<hkern u1="&#x3a;" u2="d" k="20" />
<hkern u1="&#x3a;" u2="c" k="20" />
<hkern u1="&#x3a;" u2="a" k="20" />
<hkern u1="&#x3a;" u2="Y" k="61" />
<hkern u1="&#x3a;" u2="W" k="41" />
<hkern u1="&#x3a;" u2="V" k="41" />
<hkern u1="&#x3a;" u2="T" k="102" />
<hkern u1="&#x3a;" u2="&#x37;" k="41" />
<hkern u1="&#x3a;" u2="&#x31;" k="61" />
<hkern u1="&#x3b;" u2="&#x178;" k="61" />
<hkern u1="&#x3b;" u2="&#x153;" k="20" />
<hkern u1="&#x3b;" u2="&#xe7;" k="20" />
<hkern u1="&#x3b;" u2="&#xe6;" k="20" />
<hkern u1="&#x3b;" u2="&#xdd;" k="61" />
<hkern u1="&#x3b;" u2="q" k="20" />
<hkern u1="&#x3b;" u2="o" k="20" />
<hkern u1="&#x3b;" u2="e" k="20" />
<hkern u1="&#x3b;" u2="d" k="20" />
<hkern u1="&#x3b;" u2="c" k="20" />
<hkern u1="&#x3b;" u2="a" k="20" />
<hkern u1="&#x3b;" u2="Y" k="61" />
<hkern u1="&#x3b;" u2="W" k="41" />
<hkern u1="&#x3b;" u2="V" k="41" />
<hkern u1="&#x3b;" u2="T" k="102" />
<hkern u1="&#x3b;" u2="&#x37;" k="41" />
<hkern u1="&#x3b;" u2="&#x31;" k="61" />
<hkern u1="&#x3e;" u2="&#x37;" k="123" />
<hkern u1="&#x40;" u2="&#x178;" k="61" />
<hkern u1="&#x40;" u2="&#xdd;" k="61" />
<hkern u1="&#x40;" u2="&#xc6;" k="20" />
<hkern u1="&#x40;" u2="&#xc5;" k="20" />
<hkern u1="&#x40;" u2="&#xc4;" k="20" />
<hkern u1="&#x40;" u2="&#xc3;" k="20" />
<hkern u1="&#x40;" u2="&#xc2;" k="20" />
<hkern u1="&#x40;" u2="&#xc1;" k="20" />
<hkern u1="&#x40;" u2="&#xc0;" k="20" />
<hkern u1="&#x40;" u2="Y" k="61" />
<hkern u1="&#x40;" u2="W" k="41" />
<hkern u1="&#x40;" u2="T" k="61" />
<hkern u1="&#x40;" u2="A" k="20" />
<hkern u1="B" u2="W" k="41" />
<hkern u1="B" u2="V" k="10" />
<hkern u1="C" u2="&#x178;" k="-41" />
<hkern u1="C" u2="&#x153;" k="-10" />
<hkern u1="C" u2="&#xe7;" k="-10" />
<hkern u1="C" u2="&#xe6;" k="-10" />
<hkern u1="C" u2="&#xdd;" k="-41" />
<hkern u1="C" u2="z" k="20" />
<hkern u1="C" u2="y" k="10" />
<hkern u1="C" u2="w" k="10" />
<hkern u1="C" u2="v" k="10" />
<hkern u1="C" u2="t" k="10" />
<hkern u1="C" u2="q" k="-10" />
<hkern u1="C" u2="o" k="-10" />
<hkern u1="C" u2="e" k="-10" />
<hkern u1="C" u2="d" k="-10" />
<hkern u1="C" u2="c" k="-10" />
<hkern u1="C" u2="a" k="-10" />
<hkern u1="C" u2="Y" k="-41" />
<hkern u1="C" u2="X" k="-20" />
<hkern u1="C" u2="V" k="-41" />
<hkern u1="D" u2="&#x2026;" k="82" />
<hkern u1="D" u2="&#x201e;" k="82" />
<hkern u1="D" u2="&#x201c;" k="41" />
<hkern u1="D" u2="&#x201a;" k="82" />
<hkern u1="D" u2="&#x2018;" k="41" />
<hkern u1="D" u2="&#x178;" k="51" />
<hkern u1="D" u2="&#x153;" k="10" />
<hkern u1="D" u2="&#xe7;" k="10" />
<hkern u1="D" u2="&#xe6;" k="10" />
<hkern u1="D" u2="&#xdd;" k="51" />
<hkern u1="D" u2="&#xc6;" k="41" />
<hkern u1="D" u2="&#xc5;" k="41" />
<hkern u1="D" u2="&#xc4;" k="41" />
<hkern u1="D" u2="&#xc3;" k="41" />
<hkern u1="D" u2="&#xc2;" k="41" />
<hkern u1="D" u2="&#xc1;" k="41" />
<hkern u1="D" u2="&#xc0;" k="41" />
<hkern u1="D" u2="z" k="20" />
<hkern u1="D" u2="x" k="20" />
<hkern u1="D" u2="u" k="10" />
<hkern u1="D" u2="r" k="10" />
<hkern u1="D" u2="q" k="10" />
<hkern u1="D" u2="p" k="10" />
<hkern u1="D" u2="o" k="10" />
<hkern u1="D" u2="n" k="10" />
<hkern u1="D" u2="m" k="10" />
<hkern u1="D" u2="l" k="20" />
<hkern u1="D" u2="k" k="20" />
<hkern u1="D" u2="h" k="20" />
<hkern u1="D" u2="e" k="10" />
<hkern u1="D" u2="d" k="10" />
<hkern u1="D" u2="c" k="10" />
<hkern u1="D" u2="b" k="20" />
<hkern u1="D" u2="a" k="10" />
<hkern u1="D" u2="Z" k="92" />
<hkern u1="D" u2="Y" k="51" />
<hkern u1="D" u2="X" k="51" />
<hkern u1="D" u2="W" k="82" />
<hkern u1="D" u2="V" k="41" />
<hkern u1="D" u2="T" k="61" />
<hkern u1="D" u2="J" k="92" />
<hkern u1="D" u2="A" k="41" />
<hkern u1="D" u2="&#x3f;" k="41" />
<hkern u1="D" u2="&#x2f;" k="102" />
<hkern u1="D" u2="&#x2e;" k="82" />
<hkern u1="D" u2="&#x2c;" k="82" />
<hkern u1="E" u2="&#x153;" k="51" />
<hkern u1="E" u2="&#xe7;" k="51" />
<hkern u1="E" u2="&#xe6;" k="51" />
<hkern u1="E" u2="&#xae;" k="41" />
<hkern u1="E" u2="&#xa9;" k="41" />
<hkern u1="E" u2="y" k="41" />
<hkern u1="E" u2="v" k="41" />
<hkern u1="E" u2="q" k="51" />
<hkern u1="E" u2="o" k="51" />
<hkern u1="E" u2="g" k="10" />
<hkern u1="E" u2="f" k="20" />
<hkern u1="E" u2="e" k="51" />
<hkern u1="E" u2="d" k="51" />
<hkern u1="E" u2="c" k="51" />
<hkern u1="E" u2="a" k="51" />
<hkern u1="E" u2="T" k="-31" />
<hkern u1="E" u2="&#x40;" k="41" />
<hkern u1="F" u2="&#x2026;" k="184" />
<hkern u1="F" u2="&#x201e;" k="184" />
<hkern u1="F" u2="&#x201a;" k="184" />
<hkern u1="F" u2="&#x2014;" k="102" />
<hkern u1="F" u2="&#x2013;" k="102" />
<hkern u1="F" u2="&#x178;" k="-51" />
<hkern u1="F" u2="&#x153;" k="102" />
<hkern u1="F" u2="&#xe7;" k="102" />
<hkern u1="F" u2="&#xe6;" k="102" />
<hkern u1="F" u2="&#xdd;" k="-51" />
<hkern u1="F" u2="&#xc6;" k="102" />
<hkern u1="F" u2="&#xc5;" k="102" />
<hkern u1="F" u2="&#xc4;" k="102" />
<hkern u1="F" u2="&#xc3;" k="102" />
<hkern u1="F" u2="&#xc2;" k="102" />
<hkern u1="F" u2="&#xc1;" k="102" />
<hkern u1="F" u2="&#xc0;" k="102" />
<hkern u1="F" u2="&#xae;" k="41" />
<hkern u1="F" u2="&#xa9;" k="41" />
<hkern u1="F" u2="z" k="82" />
<hkern u1="F" u2="y" k="41" />
<hkern u1="F" u2="x" k="82" />
<hkern u1="F" u2="w" k="41" />
<hkern u1="F" u2="v" k="41" />
<hkern u1="F" u2="u" k="82" />
<hkern u1="F" u2="t" k="41" />
<hkern u1="F" u2="s" k="82" />
<hkern u1="F" u2="r" k="82" />
<hkern u1="F" u2="q" k="102" />
<hkern u1="F" u2="p" k="82" />
<hkern u1="F" u2="o" k="102" />
<hkern u1="F" u2="n" k="82" />
<hkern u1="F" u2="m" k="82" />
<hkern u1="F" u2="l" k="20" />
<hkern u1="F" u2="k" k="20" />
<hkern u1="F" u2="j" k="20" />
<hkern u1="F" u2="i" k="20" />
<hkern u1="F" u2="h" k="20" />
<hkern u1="F" u2="g" k="61" />
<hkern u1="F" u2="f" k="41" />
<hkern u1="F" u2="e" k="102" />
<hkern u1="F" u2="d" k="102" />
<hkern u1="F" u2="c" k="102" />
<hkern u1="F" u2="b" k="20" />
<hkern u1="F" u2="a" k="102" />
<hkern u1="F" u2="Y" k="-51" />
<hkern u1="F" u2="X" k="-20" />
<hkern u1="F" u2="W" k="-10" />
<hkern u1="F" u2="V" k="-41" />
<hkern u1="F" u2="T" k="-61" />
<hkern u1="F" u2="J" k="184" />
<hkern u1="F" u2="A" k="102" />
<hkern u1="F" u2="&#x40;" k="41" />
<hkern u1="F" u2="&#x3b;" k="61" />
<hkern u1="F" u2="&#x3a;" k="61" />
<hkern u1="F" u2="&#x2f;" k="164" />
<hkern u1="F" u2="&#x2e;" k="184" />
<hkern u1="F" u2="&#x2d;" k="102" />
<hkern u1="F" u2="&#x2c;" k="184" />
<hkern u1="F" u2="&#x26;" k="82" />
<hkern u1="G" u2="&#x2122;" k="61" />
<hkern u1="G" u2="&#x2026;" k="-41" />
<hkern u1="G" u2="&#x201e;" k="-41" />
<hkern u1="G" u2="&#x201a;" k="-41" />
<hkern u1="G" u2="&#x178;" k="31" />
<hkern u1="G" u2="&#xdd;" k="31" />
<hkern u1="G" u2="y" k="20" />
<hkern u1="G" u2="v" k="20" />
<hkern u1="G" u2="t" k="20" />
<hkern u1="G" u2="Y" k="31" />
<hkern u1="G" u2="W" k="31" />
<hkern u1="G" u2="V" k="20" />
<hkern u1="G" u2="T" k="51" />
<hkern u1="G" u2="&#x2e;" k="-41" />
<hkern u1="G" u2="&#x2c;" k="-41" />
<hkern u1="H" u2="y" k="20" />
<hkern u1="H" u2="v" k="20" />
<hkern u1="H" u2="&#x2f;" k="41" />
<hkern u1="I" u2="y" k="20" />
<hkern u1="I" u2="v" k="20" />
<hkern u1="I" u2="&#x2f;" k="41" />
<hkern u1="J" u2="&#x2026;" k="20" />
<hkern u1="J" u2="&#x201e;" k="20" />
<hkern u1="J" u2="&#x201a;" k="20" />
<hkern u1="J" u2="J" k="20" />
<hkern u1="J" u2="A" k="10" />
<hkern u1="J" u2="&#x2e;" k="20" />
<hkern u1="J" u2="&#x2c;" k="20" />
<hkern u1="K" u2="&#x2014;" k="82" />
<hkern u1="K" u2="&#x2013;" k="82" />
<hkern u1="K" u2="&#x178;" k="-31" />
<hkern u1="K" u2="&#x153;" k="41" />
<hkern u1="K" u2="&#x152;" k="61" />
<hkern u1="K" u2="&#xf0;" k="41" />
<hkern u1="K" u2="&#xe7;" k="41" />
<hkern u1="K" u2="&#xe6;" k="41" />
<hkern u1="K" u2="&#xdd;" k="-31" />
<hkern u1="K" u2="&#xdc;" k="20" />
<hkern u1="K" u2="&#xdb;" k="20" />
<hkern u1="K" u2="&#xda;" k="20" />
<hkern u1="K" u2="&#xd9;" k="20" />
<hkern u1="K" u2="&#xd8;" k="61" />
<hkern u1="K" u2="&#xd6;" k="61" />
<hkern u1="K" u2="&#xd5;" k="61" />
<hkern u1="K" u2="&#xd4;" k="61" />
<hkern u1="K" u2="&#xd3;" k="61" />
<hkern u1="K" u2="&#xd2;" k="61" />
<hkern u1="K" u2="&#xae;" k="61" />
<hkern u1="K" u2="&#xa9;" k="61" />
<hkern u1="K" u2="y" k="41" />
<hkern u1="K" u2="v" k="41" />
<hkern u1="K" u2="t" k="20" />
<hkern u1="K" u2="q" k="41" />
<hkern u1="K" u2="o" k="41" />
<hkern u1="K" u2="g" k="20" />
<hkern u1="K" u2="f" k="10" />
<hkern u1="K" u2="e" k="41" />
<hkern u1="K" u2="d" k="41" />
<hkern u1="K" u2="c" k="41" />
<hkern u1="K" u2="a" k="31" />
<hkern u1="K" u2="Z" k="10" />
<hkern u1="K" u2="Y" k="-31" />
<hkern u1="K" u2="X" k="-20" />
<hkern u1="K" u2="W" k="20" />
<hkern u1="K" u2="V" k="-31" />
<hkern u1="K" u2="U" k="20" />
<hkern u1="K" u2="T" k="-10" />
<hkern u1="K" u2="S" k="10" />
<hkern u1="K" u2="Q" k="61" />
<hkern u1="K" u2="O" k="61" />
<hkern u1="K" u2="G" k="61" />
<hkern u1="K" u2="C" k="61" />
<hkern u1="K" u2="&#x40;" k="61" />
<hkern u1="K" u2="&#x2d;" k="82" />
<hkern u1="K" u2="&#x26;" k="41" />
<hkern u1="L" u2="&#x2122;" k="143" />
<hkern u1="L" u2="&#x201d;" k="123" />
<hkern u1="L" u2="&#x201c;" k="205" />
<hkern u1="L" u2="&#x2019;" k="123" />
<hkern u1="L" u2="&#x2018;" k="205" />
<hkern u1="L" u2="&#x178;" k="164" />
<hkern u1="L" u2="&#x153;" k="41" />
<hkern u1="L" u2="&#x152;" k="102" />
<hkern u1="L" u2="&#xe7;" k="41" />
<hkern u1="L" u2="&#xe6;" k="41" />
<hkern u1="L" u2="&#xdd;" k="164" />
<hkern u1="L" u2="&#xdc;" k="31" />
<hkern u1="L" u2="&#xdb;" k="31" />
<hkern u1="L" u2="&#xda;" k="31" />
<hkern u1="L" u2="&#xd9;" k="31" />
<hkern u1="L" u2="&#xd8;" k="102" />
<hkern u1="L" u2="&#xd6;" k="102" />
<hkern u1="L" u2="&#xd5;" k="102" />
<hkern u1="L" u2="&#xd4;" k="102" />
<hkern u1="L" u2="&#xd3;" k="102" />
<hkern u1="L" u2="&#xd2;" k="102" />
<hkern u1="L" u2="&#xc6;" k="-41" />
<hkern u1="L" u2="&#xc5;" k="-41" />
<hkern u1="L" u2="&#xc4;" k="-41" />
<hkern u1="L" u2="&#xc3;" k="-41" />
<hkern u1="L" u2="&#xc2;" k="-41" />
<hkern u1="L" u2="&#xc1;" k="-41" />
<hkern u1="L" u2="&#xc0;" k="-41" />
<hkern u1="L" u2="&#xae;" k="102" />
<hkern u1="L" u2="&#xa9;" k="102" />
<hkern u1="L" u2="y" k="82" />
<hkern u1="L" u2="w" k="20" />
<hkern u1="L" u2="v" k="82" />
<hkern u1="L" u2="q" k="41" />
<hkern u1="L" u2="o" k="41" />
<hkern u1="L" u2="e" k="41" />
<hkern u1="L" u2="d" k="41" />
<hkern u1="L" u2="c" k="41" />
<hkern u1="L" u2="a" k="41" />
<hkern u1="L" u2="\" k="102" />
<hkern u1="L" u2="Y" k="164" />
<hkern u1="L" u2="W" k="143" />
<hkern u1="L" u2="V" k="184" />
<hkern u1="L" u2="U" k="31" />
<hkern u1="L" u2="T" k="205" />
<hkern u1="L" u2="Q" k="102" />
<hkern u1="L" u2="O" k="102" />
<hkern u1="L" u2="G" k="102" />
<hkern u1="L" u2="C" k="102" />
<hkern u1="L" u2="A" k="-41" />
<hkern u1="L" u2="&#x40;" k="102" />
<hkern u1="M" u2="y" k="20" />
<hkern u1="M" u2="v" k="20" />
<hkern u1="M" u2="&#x2f;" k="41" />
<hkern u1="N" u2="y" k="20" />
<hkern u1="N" u2="v" k="20" />
<hkern u1="N" u2="&#x2f;" k="41" />
<hkern u1="O" u2="&#x2026;" k="82" />
<hkern u1="O" u2="&#x201e;" k="82" />
<hkern u1="O" u2="&#x201c;" k="41" />
<hkern u1="O" u2="&#x201a;" k="82" />
<hkern u1="O" u2="&#x2018;" k="41" />
<hkern u1="O" u2="&#x178;" k="51" />
<hkern u1="O" u2="&#x153;" k="10" />
<hkern u1="O" u2="&#xe7;" k="10" />
<hkern u1="O" u2="&#xe6;" k="10" />
<hkern u1="O" u2="&#xdd;" k="51" />
<hkern u1="O" u2="&#xc6;" k="41" />
<hkern u1="O" u2="&#xc5;" k="41" />
<hkern u1="O" u2="&#xc4;" k="41" />
<hkern u1="O" u2="&#xc3;" k="41" />
<hkern u1="O" u2="&#xc2;" k="41" />
<hkern u1="O" u2="&#xc1;" k="41" />
<hkern u1="O" u2="&#xc0;" k="41" />
<hkern u1="O" u2="z" k="20" />
<hkern u1="O" u2="x" k="20" />
<hkern u1="O" u2="u" k="10" />
<hkern u1="O" u2="r" k="10" />
<hkern u1="O" u2="q" k="10" />
<hkern u1="O" u2="p" k="10" />
<hkern u1="O" u2="o" k="10" />
<hkern u1="O" u2="n" k="10" />
<hkern u1="O" u2="m" k="10" />
<hkern u1="O" u2="l" k="20" />
<hkern u1="O" u2="k" k="20" />
<hkern u1="O" u2="h" k="20" />
<hkern u1="O" u2="e" k="10" />
<hkern u1="O" u2="d" k="10" />
<hkern u1="O" u2="c" k="10" />
<hkern u1="O" u2="b" k="20" />
<hkern u1="O" u2="a" k="10" />
<hkern u1="O" u2="Z" k="92" />
<hkern u1="O" u2="Y" k="51" />
<hkern u1="O" u2="X" k="51" />
<hkern u1="O" u2="W" k="82" />
<hkern u1="O" u2="V" k="41" />
<hkern u1="O" u2="T" k="61" />
<hkern u1="O" u2="J" k="92" />
<hkern u1="O" u2="A" k="41" />
<hkern u1="O" u2="&#x3f;" k="41" />
<hkern u1="O" u2="&#x2f;" k="102" />
<hkern u1="O" u2="&#x2e;" k="82" />
<hkern u1="O" u2="&#x2c;" k="82" />
<hkern u1="P" u2="&#x2026;" k="123" />
<hkern u1="P" u2="&#x201e;" k="123" />
<hkern u1="P" u2="&#x201a;" k="123" />
<hkern u1="P" u2="&#x178;" k="-31" />
<hkern u1="P" u2="&#x153;" k="20" />
<hkern u1="P" u2="&#xe7;" k="20" />
<hkern u1="P" u2="&#xe6;" k="20" />
<hkern u1="P" u2="&#xdd;" k="-31" />
<hkern u1="P" u2="&#xc6;" k="72" />
<hkern u1="P" u2="&#xc5;" k="72" />
<hkern u1="P" u2="&#xc4;" k="72" />
<hkern u1="P" u2="&#xc3;" k="72" />
<hkern u1="P" u2="&#xc2;" k="72" />
<hkern u1="P" u2="&#xc1;" k="72" />
<hkern u1="P" u2="&#xc0;" k="72" />
<hkern u1="P" u2="y" k="-31" />
<hkern u1="P" u2="x" k="-10" />
<hkern u1="P" u2="w" k="-20" />
<hkern u1="P" u2="v" k="-31" />
<hkern u1="P" u2="t" k="-20" />
<hkern u1="P" u2="q" k="20" />
<hkern u1="P" u2="o" k="20" />
<hkern u1="P" u2="f" k="-20" />
<hkern u1="P" u2="e" k="20" />
<hkern u1="P" u2="d" k="20" />
<hkern u1="P" u2="c" k="20" />
<hkern u1="P" u2="a" k="20" />
<hkern u1="P" u2="Z" k="51" />
<hkern u1="P" u2="Y" k="-31" />
<hkern u1="P" u2="X" k="-10" />
<hkern u1="P" u2="W" k="-10" />
<hkern u1="P" u2="V" k="-51" />
<hkern u1="P" u2="J" k="174" />
<hkern u1="P" u2="A" k="74" />
<hkern u1="P" u2="&#x2e;" k="123" />
<hkern u1="P" u2="&#x2c;" k="123" />
<hkern u1="P" u2="&#x26;" k="41" />
<hkern u1="Q" u2="&#x2026;" k="82" />
<hkern u1="Q" u2="&#x201e;" k="82" />
<hkern u1="Q" u2="&#x201c;" k="41" />
<hkern u1="Q" u2="&#x201a;" k="82" />
<hkern u1="Q" u2="&#x2018;" k="41" />
<hkern u1="Q" u2="&#x178;" k="51" />
<hkern u1="Q" u2="&#x153;" k="10" />
<hkern u1="Q" u2="&#xe7;" k="10" />
<hkern u1="Q" u2="&#xe6;" k="10" />
<hkern u1="Q" u2="&#xdd;" k="51" />
<hkern u1="Q" u2="&#xc6;" k="41" />
<hkern u1="Q" u2="&#xc5;" k="41" />
<hkern u1="Q" u2="&#xc4;" k="41" />
<hkern u1="Q" u2="&#xc3;" k="41" />
<hkern u1="Q" u2="&#xc2;" k="41" />
<hkern u1="Q" u2="&#xc1;" k="41" />
<hkern u1="Q" u2="&#xc0;" k="41" />
<hkern u1="Q" u2="z" k="20" />
<hkern u1="Q" u2="x" k="20" />
<hkern u1="Q" u2="u" k="10" />
<hkern u1="Q" u2="r" k="10" />
<hkern u1="Q" u2="q" k="10" />
<hkern u1="Q" u2="p" k="10" />
<hkern u1="Q" u2="o" k="10" />
<hkern u1="Q" u2="n" k="10" />
<hkern u1="Q" u2="m" k="10" />
<hkern u1="Q" u2="l" k="20" />
<hkern u1="Q" u2="k" k="20" />
<hkern u1="Q" u2="h" k="20" />
<hkern u1="Q" u2="e" k="10" />
<hkern u1="Q" u2="d" k="10" />
<hkern u1="Q" u2="c" k="10" />
<hkern u1="Q" u2="b" k="20" />
<hkern u1="Q" u2="a" k="10" />
<hkern u1="Q" u2="Z" k="92" />
<hkern u1="Q" u2="Y" k="51" />
<hkern u1="Q" u2="X" k="51" />
<hkern u1="Q" u2="W" k="82" />
<hkern u1="Q" u2="V" k="41" />
<hkern u1="Q" u2="T" k="61" />
<hkern u1="Q" u2="J" k="92" />
<hkern u1="Q" u2="A" k="41" />
<hkern u1="Q" u2="&#x3f;" k="41" />
<hkern u1="Q" u2="&#x2f;" k="61" />
<hkern u1="Q" u2="&#x2c;" k="82" />
<hkern u1="R" u2="&#x178;" k="-31" />
<hkern u1="R" u2="&#xdd;" k="-31" />
<hkern u1="R" u2="Y" k="-31" />
<hkern u1="R" u2="X" k="-20" />
<hkern u1="R" u2="V" k="-41" />
<hkern u1="R" u2="T" k="20" />
<hkern u1="R" u2="J" k="20" />
<hkern u1="R" u2="&#x26;" k="-20" />
<hkern u1="S" u2="&#x178;" k="-20" />
<hkern u1="S" u2="&#xdd;" k="-20" />
<hkern u1="S" u2="&#xc6;" k="-10" />
<hkern u1="S" u2="&#xc5;" k="-10" />
<hkern u1="S" u2="&#xc4;" k="-10" />
<hkern u1="S" u2="&#xc3;" k="-10" />
<hkern u1="S" u2="&#xc2;" k="-10" />
<hkern u1="S" u2="&#xc1;" k="-10" />
<hkern u1="S" u2="&#xc0;" k="-10" />
<hkern u1="S" u2="Y" k="-20" />
<hkern u1="S" u2="X" k="-10" />
<hkern u1="S" u2="V" k="-20" />
<hkern u1="S" u2="A" k="-10" />
<hkern u1="T" u2="&#x203a;" k="82" />
<hkern u1="T" u2="&#x2039;" k="225" />
<hkern u1="T" u2="&#x2026;" k="123" />
<hkern u1="T" u2="&#x201e;" k="123" />
<hkern u1="T" u2="&#x201a;" k="123" />
<hkern u1="T" u2="&#x2014;" k="143" />
<hkern u1="T" u2="&#x2013;" k="143" />
<hkern u1="T" u2="&#x178;" k="-41" />
<hkern u1="T" u2="&#x153;" k="195" />
<hkern u1="T" u2="&#x152;" k="61" />
<hkern u1="T" u2="&#xe7;" k="195" />
<hkern u1="T" u2="&#xe6;" k="195" />
<hkern u1="T" u2="&#xdd;" k="-41" />
<hkern u1="T" u2="&#xd8;" k="61" />
<hkern u1="T" u2="&#xd6;" k="61" />
<hkern u1="T" u2="&#xd5;" k="61" />
<hkern u1="T" u2="&#xd4;" k="61" />
<hkern u1="T" u2="&#xd3;" k="61" />
<hkern u1="T" u2="&#xd2;" k="61" />
<hkern u1="T" u2="&#xc6;" k="143" />
<hkern u1="T" u2="&#xc5;" k="143" />
<hkern u1="T" u2="&#xc4;" k="143" />
<hkern u1="T" u2="&#xc3;" k="143" />
<hkern u1="T" u2="&#xc2;" k="143" />
<hkern u1="T" u2="&#xc1;" k="143" />
<hkern u1="T" u2="&#xc0;" k="143" />
<hkern u1="T" u2="&#xbf;" k="102" />
<hkern u1="T" u2="&#xbb;" k="82" />
<hkern u1="T" u2="&#xae;" k="61" />
<hkern u1="T" u2="&#xab;" k="225" />
<hkern u1="T" u2="&#xa9;" k="61" />
<hkern u1="T" u2="z" k="164" />
<hkern u1="T" u2="y" k="143" />
<hkern u1="T" u2="x" k="164" />
<hkern u1="T" u2="w" k="123" />
<hkern u1="T" u2="v" k="143" />
<hkern u1="T" u2="u" k="164" />
<hkern u1="T" u2="t" k="41" />
<hkern u1="T" u2="s" k="205" />
<hkern u1="T" u2="r" k="164" />
<hkern u1="T" u2="q" k="195" />
<hkern u1="T" u2="p" k="164" />
<hkern u1="T" u2="o" k="195" />
<hkern u1="T" u2="n" k="164" />
<hkern u1="T" u2="m" k="164" />
<hkern u1="T" u2="g" k="195" />
<hkern u1="T" u2="f" k="20" />
<hkern u1="T" u2="e" k="195" />
<hkern u1="T" u2="d" k="195" />
<hkern u1="T" u2="c" k="195" />
<hkern u1="T" u2="a" k="195" />
<hkern u1="T" u2="\" k="-20" />
<hkern u1="T" u2="Z" k="20" />
<hkern u1="T" u2="Y" k="-41" />
<hkern u1="T" u2="X" k="-20" />
<hkern u1="T" u2="V" k="-41" />
<hkern u1="T" u2="T" k="-20" />
<hkern u1="T" u2="Q" k="61" />
<hkern u1="T" u2="O" k="61" />
<hkern u1="T" u2="J" k="174" />
<hkern u1="T" u2="G" k="61" />
<hkern u1="T" u2="C" k="61" />
<hkern u1="T" u2="A" k="135" />
<hkern u1="T" u2="&#x40;" k="61" />
<hkern u1="T" u2="&#x3b;" k="102" />
<hkern u1="T" u2="&#x3a;" k="102" />
<hkern u1="T" u2="&#x2e;" k="123" />
<hkern u1="T" u2="&#x2d;" k="143" />
<hkern u1="T" u2="&#x2c;" k="123" />
<hkern u1="T" u2="&#x26;" k="82" />
<hkern u1="U" u2="&#x2026;" k="20" />
<hkern u1="U" u2="&#x201e;" k="20" />
<hkern u1="U" u2="&#x201a;" k="20" />
<hkern u1="U" u2="J" k="20" />
<hkern u1="U" u2="A" k="10" />
<hkern u1="U" u2="&#x2e;" k="20" />
<hkern u1="U" u2="&#x2c;" k="20" />
<hkern u1="V" u2="&#x203a;" k="41" />
<hkern u1="V" u2="&#x2039;" k="102" />
<hkern u1="V" u2="&#x2026;" k="123" />
<hkern u1="V" u2="&#x201e;" k="123" />
<hkern u1="V" u2="&#x201a;" k="123" />
<hkern u1="V" u2="&#x2014;" k="123" />
<hkern u1="V" u2="&#x2013;" k="123" />
<hkern u1="V" u2="&#x178;" k="-61" />
<hkern u1="V" u2="&#x153;" k="82" />
<hkern u1="V" u2="&#x152;" k="41" />
<hkern u1="V" u2="&#xe7;" k="82" />
<hkern u1="V" u2="&#xe6;" k="82" />
<hkern u1="V" u2="&#xdd;" k="-61" />
<hkern u1="V" u2="&#xd8;" k="41" />
<hkern u1="V" u2="&#xd6;" k="41" />
<hkern u1="V" u2="&#xd5;" k="41" />
<hkern u1="V" u2="&#xd4;" k="41" />
<hkern u1="V" u2="&#xd3;" k="41" />
<hkern u1="V" u2="&#xd2;" k="41" />
<hkern u1="V" u2="&#xc6;" k="102" />
<hkern u1="V" u2="&#xc5;" k="102" />
<hkern u1="V" u2="&#xc4;" k="102" />
<hkern u1="V" u2="&#xc3;" k="102" />
<hkern u1="V" u2="&#xc2;" k="102" />
<hkern u1="V" u2="&#xc1;" k="102" />
<hkern u1="V" u2="&#xc0;" k="102" />
<hkern u1="V" u2="&#xbb;" k="41" />
<hkern u1="V" u2="&#xab;" k="102" />
<hkern u1="V" u2="&#x7d;" k="-61" />
<hkern u1="V" u2="y" k="20" />
<hkern u1="V" u2="x" k="51" />
<hkern u1="V" u2="w" k="20" />
<hkern u1="V" u2="v" k="20" />
<hkern u1="V" u2="u" k="61" />
<hkern u1="V" u2="t" k="20" />
<hkern u1="V" u2="s" k="82" />
<hkern u1="V" u2="r" k="61" />
<hkern u1="V" u2="q" k="82" />
<hkern u1="V" u2="p" k="61" />
<hkern u1="V" u2="o" k="82" />
<hkern u1="V" u2="n" k="61" />
<hkern u1="V" u2="m" k="61" />
<hkern u1="V" u2="l" k="20" />
<hkern u1="V" u2="k" k="20" />
<hkern u1="V" u2="h" k="20" />
<hkern u1="V" u2="g" k="72" />
<hkern u1="V" u2="f" k="20" />
<hkern u1="V" u2="e" k="82" />
<hkern u1="V" u2="d" k="82" />
<hkern u1="V" u2="c" k="82" />
<hkern u1="V" u2="b" k="20" />
<hkern u1="V" u2="a" k="82" />
<hkern u1="V" u2="]" k="-61" />
<hkern u1="V" u2="Y" k="-61" />
<hkern u1="V" u2="X" k="-41" />
<hkern u1="V" u2="W" k="10" />
<hkern u1="V" u2="V" k="-41" />
<hkern u1="V" u2="T" k="-41" />
<hkern u1="V" u2="S" k="-20" />
<hkern u1="V" u2="Q" k="41" />
<hkern u1="V" u2="O" k="41" />
<hkern u1="V" u2="J" k="102" />
<hkern u1="V" u2="G" k="41" />
<hkern u1="V" u2="C" k="41" />
<hkern u1="V" u2="A" k="117" />
<hkern u1="V" u2="&#x3b;" k="41" />
<hkern u1="V" u2="&#x3a;" k="41" />
<hkern u1="V" u2="&#x2e;" k="123" />
<hkern u1="V" u2="&#x2d;" k="123" />
<hkern u1="V" u2="&#x2c;" k="123" />
<hkern u1="V" u2="&#x29;" k="-61" />
<hkern u1="V" u2="&#x26;" k="82" />
<hkern u1="W" u2="&#x203a;" k="41" />
<hkern u1="W" u2="&#x2039;" k="82" />
<hkern u1="W" u2="&#x2026;" k="41" />
<hkern u1="W" u2="&#x201e;" k="41" />
<hkern u1="W" u2="&#x201a;" k="41" />
<hkern u1="W" u2="&#x2014;" k="82" />
<hkern u1="W" u2="&#x2013;" k="82" />
<hkern u1="W" u2="&#x153;" k="92" />
<hkern u1="W" u2="&#x152;" k="82" />
<hkern u1="W" u2="&#xe7;" k="92" />
<hkern u1="W" u2="&#xe6;" k="92" />
<hkern u1="W" u2="&#xd8;" k="82" />
<hkern u1="W" u2="&#xd6;" k="82" />
<hkern u1="W" u2="&#xd5;" k="82" />
<hkern u1="W" u2="&#xd4;" k="82" />
<hkern u1="W" u2="&#xd3;" k="82" />
<hkern u1="W" u2="&#xd2;" k="82" />
<hkern u1="W" u2="&#xc6;" k="102" />
<hkern u1="W" u2="&#xc5;" k="102" />
<hkern u1="W" u2="&#xc4;" k="102" />
<hkern u1="W" u2="&#xc3;" k="102" />
<hkern u1="W" u2="&#xc2;" k="102" />
<hkern u1="W" u2="&#xc1;" k="102" />
<hkern u1="W" u2="&#xc0;" k="102" />
<hkern u1="W" u2="&#xbb;" k="41" />
<hkern u1="W" u2="&#xae;" k="41" />
<hkern u1="W" u2="&#xab;" k="82" />
<hkern u1="W" u2="&#xa9;" k="41" />
<hkern u1="W" u2="&#x7d;" k="-20" />
<hkern u1="W" u2="z" k="61" />
<hkern u1="W" u2="y" k="61" />
<hkern u1="W" u2="x" k="61" />
<hkern u1="W" u2="w" k="61" />
<hkern u1="W" u2="v" k="61" />
<hkern u1="W" u2="u" k="72" />
<hkern u1="W" u2="t" k="61" />
<hkern u1="W" u2="s" k="102" />
<hkern u1="W" u2="r" k="72" />
<hkern u1="W" u2="q" k="92" />
<hkern u1="W" u2="p" k="72" />
<hkern u1="W" u2="o" k="92" />
<hkern u1="W" u2="n" k="72" />
<hkern u1="W" u2="m" k="72" />
<hkern u1="W" u2="l" k="20" />
<hkern u1="W" u2="k" k="20" />
<hkern u1="W" u2="j" k="41" />
<hkern u1="W" u2="i" k="41" />
<hkern u1="W" u2="h" k="20" />
<hkern u1="W" u2="g" k="102" />
<hkern u1="W" u2="f" k="31" />
<hkern u1="W" u2="e" k="92" />
<hkern u1="W" u2="d" k="92" />
<hkern u1="W" u2="c" k="92" />
<hkern u1="W" u2="b" k="20" />
<hkern u1="W" u2="a" k="92" />
<hkern u1="W" u2="]" k="-20" />
<hkern u1="W" u2="X" k="20" />
<hkern u1="W" u2="V" k="10" />
<hkern u1="W" u2="Q" k="82" />
<hkern u1="W" u2="O" k="82" />
<hkern u1="W" u2="J" k="82" />
<hkern u1="W" u2="G" k="82" />
<hkern u1="W" u2="C" k="82" />
<hkern u1="W" u2="A" k="111" />
<hkern u1="W" u2="&#x40;" k="41" />
<hkern u1="W" u2="&#x3b;" k="41" />
<hkern u1="W" u2="&#x3a;" k="41" />
<hkern u1="W" u2="&#x2e;" k="41" />
<hkern u1="W" u2="&#x2d;" k="82" />
<hkern u1="W" u2="&#x2c;" k="41" />
<hkern u1="W" u2="&#x29;" k="-20" />
<hkern u1="W" u2="&#x26;" k="61" />
<hkern u1="X" u2="&#x2014;" k="61" />
<hkern u1="X" u2="&#x2013;" k="61" />
<hkern u1="X" u2="&#x178;" k="-41" />
<hkern u1="X" u2="&#x153;" k="20" />
<hkern u1="X" u2="&#x152;" k="51" />
<hkern u1="X" u2="&#xe7;" k="20" />
<hkern u1="X" u2="&#xe6;" k="20" />
<hkern u1="X" u2="&#xdd;" k="-41" />
<hkern u1="X" u2="&#xd8;" k="51" />
<hkern u1="X" u2="&#xd6;" k="51" />
<hkern u1="X" u2="&#xd5;" k="51" />
<hkern u1="X" u2="&#xd4;" k="51" />
<hkern u1="X" u2="&#xd3;" k="51" />
<hkern u1="X" u2="&#xd2;" k="51" />
<hkern u1="X" u2="&#xc6;" k="-20" />
<hkern u1="X" u2="&#xc5;" k="-20" />
<hkern u1="X" u2="&#xc4;" k="-20" />
<hkern u1="X" u2="&#xc3;" k="-20" />
<hkern u1="X" u2="&#xc2;" k="-20" />
<hkern u1="X" u2="&#xc1;" k="-20" />
<hkern u1="X" u2="&#xc0;" k="-20" />
<hkern u1="X" u2="&#x7d;" k="-82" />
<hkern u1="X" u2="y" k="41" />
<hkern u1="X" u2="w" k="20" />
<hkern u1="X" u2="v" k="41" />
<hkern u1="X" u2="u" k="20" />
<hkern u1="X" u2="t" k="20" />
<hkern u1="X" u2="r" k="20" />
<hkern u1="X" u2="q" k="20" />
<hkern u1="X" u2="p" k="20" />
<hkern u1="X" u2="o" k="20" />
<hkern u1="X" u2="n" k="20" />
<hkern u1="X" u2="m" k="20" />
<hkern u1="X" u2="f" k="20" />
<hkern u1="X" u2="e" k="20" />
<hkern u1="X" u2="d" k="20" />
<hkern u1="X" u2="c" k="20" />
<hkern u1="X" u2="a" k="20" />
<hkern u1="X" u2="]" k="-82" />
<hkern u1="X" u2="Y" k="-41" />
<hkern u1="X" u2="X" k="-20" />
<hkern u1="X" u2="W" k="20" />
<hkern u1="X" u2="V" k="-41" />
<hkern u1="X" u2="T" k="-20" />
<hkern u1="X" u2="S" k="-10" />
<hkern u1="X" u2="Q" k="51" />
<hkern u1="X" u2="O" k="51" />
<hkern u1="X" u2="G" k="51" />
<hkern u1="X" u2="C" k="51" />
<hkern u1="X" u2="A" k="-20" />
<hkern u1="X" u2="&#x2d;" k="61" />
<hkern u1="X" u2="&#x29;" k="-82" />
<hkern u1="X" u2="&#x26;" k="41" />
<hkern u1="Y" u2="&#x203a;" k="82" />
<hkern u1="Y" u2="&#x2039;" k="123" />
<hkern u1="Y" u2="&#x2026;" k="102" />
<hkern u1="Y" u2="&#x201e;" k="102" />
<hkern u1="Y" u2="&#x201a;" k="102" />
<hkern u1="Y" u2="&#x2014;" k="102" />
<hkern u1="Y" u2="&#x2013;" k="102" />
<hkern u1="Y" u2="&#x153;" k="113" />
<hkern u1="Y" u2="&#x152;" k="51" />
<hkern u1="Y" u2="&#xe7;" k="113" />
<hkern u1="Y" u2="&#xe6;" k="113" />
<hkern u1="Y" u2="&#xd8;" k="51" />
<hkern u1="Y" u2="&#xd6;" k="51" />
<hkern u1="Y" u2="&#xd5;" k="51" />
<hkern u1="Y" u2="&#xd4;" k="51" />
<hkern u1="Y" u2="&#xd3;" k="51" />
<hkern u1="Y" u2="&#xd2;" k="51" />
<hkern u1="Y" u2="&#xc6;" k="102" />
<hkern u1="Y" u2="&#xc5;" k="102" />
<hkern u1="Y" u2="&#xc4;" k="102" />
<hkern u1="Y" u2="&#xc3;" k="102" />
<hkern u1="Y" u2="&#xc2;" k="102" />
<hkern u1="Y" u2="&#xc1;" k="102" />
<hkern u1="Y" u2="&#xc0;" k="102" />
<hkern u1="Y" u2="&#xbb;" k="82" />
<hkern u1="Y" u2="&#xae;" k="61" />
<hkern u1="Y" u2="&#xab;" k="123" />
<hkern u1="Y" u2="&#xa9;" k="61" />
<hkern u1="Y" u2="&#x7d;" k="-102" />
<hkern u1="Y" u2="z" k="61" />
<hkern u1="Y" u2="y" k="41" />
<hkern u1="Y" u2="x" k="41" />
<hkern u1="Y" u2="w" k="20" />
<hkern u1="Y" u2="v" k="41" />
<hkern u1="Y" u2="u" k="82" />
<hkern u1="Y" u2="t" k="41" />
<hkern u1="Y" u2="s" k="102" />
<hkern u1="Y" u2="r" k="82" />
<hkern u1="Y" u2="q" k="113" />
<hkern u1="Y" u2="p" k="82" />
<hkern u1="Y" u2="o" k="113" />
<hkern u1="Y" u2="n" k="82" />
<hkern u1="Y" u2="m" k="82" />
<hkern u1="Y" u2="g" k="82" />
<hkern u1="Y" u2="f" k="20" />
<hkern u1="Y" u2="e" k="113" />
<hkern u1="Y" u2="d" k="113" />
<hkern u1="Y" u2="c" k="113" />
<hkern u1="Y" u2="a" k="113" />
<hkern u1="Y" u2="]" k="-102" />
<hkern u1="Y" u2="X" k="-41" />
<hkern u1="Y" u2="V" k="-61" />
<hkern u1="Y" u2="T" k="-41" />
<hkern u1="Y" u2="S" k="-20" />
<hkern u1="Y" u2="Q" k="51" />
<hkern u1="Y" u2="O" k="51" />
<hkern u1="Y" u2="J" k="143" />
<hkern u1="Y" u2="G" k="51" />
<hkern u1="Y" u2="C" k="51" />
<hkern u1="Y" u2="A" k="104" />
<hkern u1="Y" u2="&#x40;" k="61" />
<hkern u1="Y" u2="&#x3b;" k="61" />
<hkern u1="Y" u2="&#x3a;" k="61" />
<hkern u1="Y" u2="&#x2e;" k="102" />
<hkern u1="Y" u2="&#x2d;" k="102" />
<hkern u1="Y" u2="&#x2c;" k="102" />
<hkern u1="Y" u2="&#x29;" k="-102" />
<hkern u1="Y" u2="&#x26;" k="61" />
<hkern u1="Z" u2="&#x2014;" k="102" />
<hkern u1="Z" u2="&#x2013;" k="102" />
<hkern u1="Z" u2="&#x153;" k="82" />
<hkern u1="Z" u2="&#x152;" k="92" />
<hkern u1="Z" u2="&#xf0;" k="61" />
<hkern u1="Z" u2="&#xe7;" k="82" />
<hkern u1="Z" u2="&#xe6;" k="61" />
<hkern u1="Z" u2="&#xd8;" k="92" />
<hkern u1="Z" u2="&#xd6;" k="92" />
<hkern u1="Z" u2="&#xd5;" k="92" />
<hkern u1="Z" u2="&#xd4;" k="92" />
<hkern u1="Z" u2="&#xd3;" k="92" />
<hkern u1="Z" u2="&#xd2;" k="92" />
<hkern u1="Z" u2="&#xc6;" k="20" />
<hkern u1="Z" u2="&#xc5;" k="20" />
<hkern u1="Z" u2="&#xc4;" k="20" />
<hkern u1="Z" u2="&#xc3;" k="20" />
<hkern u1="Z" u2="&#xc2;" k="20" />
<hkern u1="Z" u2="&#xc1;" k="20" />
<hkern u1="Z" u2="&#xc0;" k="20" />
<hkern u1="Z" u2="y" k="61" />
<hkern u1="Z" u2="w" k="20" />
<hkern u1="Z" u2="v" k="61" />
<hkern u1="Z" u2="u" k="41" />
<hkern u1="Z" u2="r" k="41" />
<hkern u1="Z" u2="q" k="82" />
<hkern u1="Z" u2="p" k="41" />
<hkern u1="Z" u2="o" k="82" />
<hkern u1="Z" u2="n" k="41" />
<hkern u1="Z" u2="m" k="41" />
<hkern u1="Z" u2="l" k="20" />
<hkern u1="Z" u2="k" k="20" />
<hkern u1="Z" u2="j" k="31" />
<hkern u1="Z" u2="i" k="41" />
<hkern u1="Z" u2="h" k="20" />
<hkern u1="Z" u2="g" k="41" />
<hkern u1="Z" u2="f" k="41" />
<hkern u1="Z" u2="e" k="82" />
<hkern u1="Z" u2="d" k="82" />
<hkern u1="Z" u2="c" k="82" />
<hkern u1="Z" u2="b" k="20" />
<hkern u1="Z" u2="a" k="61" />
<hkern u1="Z" u2="T" k="20" />
<hkern u1="Z" u2="Q" k="92" />
<hkern u1="Z" u2="O" k="92" />
<hkern u1="Z" u2="J" k="20" />
<hkern u1="Z" u2="G" k="92" />
<hkern u1="Z" u2="C" k="92" />
<hkern u1="Z" u2="A" k="20" />
<hkern u1="Z" u2="&#x2d;" k="102" />
<hkern u1="[" u2="&#x178;" k="-102" />
<hkern u1="[" u2="&#xdd;" k="-102" />
<hkern u1="[" u2="j" k="-307" />
<hkern u1="[" u2="g" k="-41" />
<hkern u1="[" u2="Y" k="-102" />
<hkern u1="[" u2="X" k="-82" />
<hkern u1="[" u2="W" k="-20" />
<hkern u1="[" u2="V" k="-61" />
<hkern u1="\" u2="&#x178;" k="123" />
<hkern u1="\" u2="&#x153;" k="20" />
<hkern u1="\" u2="&#xe7;" k="20" />
<hkern u1="\" u2="&#xe6;" k="20" />
<hkern u1="\" u2="&#xdd;" k="123" />
<hkern u1="\" u2="q" k="20" />
<hkern u1="\" u2="o" k="20" />
<hkern u1="\" u2="j" k="-266" />
<hkern u1="\" u2="e" k="20" />
<hkern u1="\" u2="d" k="20" />
<hkern u1="\" u2="c" k="20" />
<hkern u1="\" u2="a" k="20" />
<hkern u1="\" u2="Y" k="123" />
<hkern u1="\" u2="W" k="102" />
<hkern u1="\" u2="V" k="143" />
<hkern u1="\" u2="T" k="184" />
<hkern u1="a" u2="&#x2122;" k="41" />
<hkern u1="a" u2="&#x201c;" k="61" />
<hkern u1="a" u2="&#x2018;" k="61" />
<hkern u1="a" u2="y" k="10" />
<hkern u1="a" u2="v" k="10" />
<hkern u1="a" u2="t" k="20" />
<hkern u1="a" u2="\" k="20" />
<hkern u1="a" u2="&#x3f;" k="20" />
<hkern u1="b" u2="&#x2122;" k="61" />
<hkern u1="b" u2="&#x2026;" k="41" />
<hkern u1="b" u2="&#x201e;" k="41" />
<hkern u1="b" u2="&#x201c;" k="82" />
<hkern u1="b" u2="&#x201a;" k="41" />
<hkern u1="b" u2="&#x2018;" k="82" />
<hkern u1="b" u2="z" k="31" />
<hkern u1="b" u2="y" k="20" />
<hkern u1="b" u2="x" k="31" />
<hkern u1="b" u2="v" k="20" />
<hkern u1="b" u2="\" k="20" />
<hkern u1="b" u2="&#x3f;" k="41" />
<hkern u1="b" u2="&#x3b;" k="20" />
<hkern u1="b" u2="&#x3a;" k="20" />
<hkern u1="b" u2="&#x2f;" k="20" />
<hkern u1="b" u2="&#x2e;" k="41" />
<hkern u1="b" u2="&#x2c;" k="41" />
<hkern u1="e" u2="y" k="20" />
<hkern u1="e" u2="x" k="31" />
<hkern u1="e" u2="v" k="20" />
<hkern u1="f" u2="&#x2122;" k="-82" />
<hkern u1="f" u2="&#x203a;" k="41" />
<hkern u1="f" u2="&#x2039;" k="102" />
<hkern u1="f" u2="&#x2026;" k="164" />
<hkern u1="f" u2="&#x201e;" k="164" />
<hkern u1="f" u2="&#x201d;" k="-102" />
<hkern u1="f" u2="&#x201c;" k="-61" />
<hkern u1="f" u2="&#x201a;" k="164" />
<hkern u1="f" u2="&#x2019;" k="-102" />
<hkern u1="f" u2="&#x2018;" k="-61" />
<hkern u1="f" u2="&#x2014;" k="61" />
<hkern u1="f" u2="&#x2013;" k="61" />
<hkern u1="f" u2="&#x153;" k="41" />
<hkern u1="f" u2="&#xf0;" k="41" />
<hkern u1="f" u2="&#xe7;" k="41" />
<hkern u1="f" u2="&#xe6;" k="41" />
<hkern u1="f" u2="&#xbb;" k="41" />
<hkern u1="f" u2="&#xab;" k="102" />
<hkern u1="f" u2="&#x7d;" k="-143" />
<hkern u1="f" u2="u" k="10" />
<hkern u1="f" u2="r" k="10" />
<hkern u1="f" u2="q" k="41" />
<hkern u1="f" u2="p" k="10" />
<hkern u1="f" u2="o" k="41" />
<hkern u1="f" u2="n" k="10" />
<hkern u1="f" u2="m" k="10" />
<hkern u1="f" u2="e" k="41" />
<hkern u1="f" u2="d" k="41" />
<hkern u1="f" u2="c" k="41" />
<hkern u1="f" u2="a" k="41" />
<hkern u1="f" u2="]" k="-143" />
<hkern u1="f" u2="\" k="-82" />
<hkern u1="f" u2="&#x3f;" k="-82" />
<hkern u1="f" u2="&#x3b;" k="20" />
<hkern u1="f" u2="&#x3a;" k="20" />
<hkern u1="f" u2="&#x2f;" k="102" />
<hkern u1="f" u2="&#x2e;" k="164" />
<hkern u1="f" u2="&#x2d;" k="61" />
<hkern u1="f" u2="&#x2c;" k="164" />
<hkern u1="f" u2="&#x2a;" k="-41" />
<hkern u1="f" u2="&#x29;" k="-143" />
<hkern u1="f" u2="&#x26;" k="61" />
<hkern u1="g" u2="&#x201d;" k="-82" />
<hkern u1="g" u2="&#x201c;" k="-41" />
<hkern u1="g" u2="&#x2019;" k="-82" />
<hkern u1="g" u2="&#x2018;" k="-41" />
<hkern u1="g" u2="&#x7d;" k="-41" />
<hkern u1="g" u2="t" k="-20" />
<hkern u1="g" u2="j" k="-102" />
<hkern u1="g" u2="g" k="-20" />
<hkern u1="g" u2="]" k="-41" />
<hkern u1="g" u2="&#x3b;" k="-41" />
<hkern u1="g" u2="&#x2f;" k="-82" />
<hkern u1="g" u2="&#x2c;" k="-61" />
<hkern u1="g" u2="&#x29;" k="-41" />
<hkern u1="h" u2="&#x2122;" k="41" />
<hkern u1="h" u2="&#x201c;" k="61" />
<hkern u1="h" u2="&#x2018;" k="61" />
<hkern u1="h" u2="y" k="10" />
<hkern u1="h" u2="v" k="10" />
<hkern u1="h" u2="t" k="20" />
<hkern u1="h" u2="\" k="20" />
<hkern u1="h" u2="&#x3f;" k="20" />
<hkern u1="k" u2="&#x153;" k="20" />
<hkern u1="k" u2="&#xe7;" k="20" />
<hkern u1="k" u2="&#xe6;" k="20" />
<hkern u1="k" u2="q" k="20" />
<hkern u1="k" u2="o" k="20" />
<hkern u1="k" u2="g" k="41" />
<hkern u1="k" u2="e" k="20" />
<hkern u1="k" u2="d" k="20" />
<hkern u1="k" u2="c" k="20" />
<hkern u1="k" u2="a" k="20" />
<hkern u1="m" u2="&#x2122;" k="41" />
<hkern u1="m" u2="&#x201c;" k="61" />
<hkern u1="m" u2="&#x2018;" k="61" />
<hkern u1="m" u2="y" k="10" />
<hkern u1="m" u2="v" k="10" />
<hkern u1="m" u2="t" k="20" />
<hkern u1="m" u2="\" k="20" />
<hkern u1="m" u2="&#x3f;" k="20" />
<hkern u1="n" u2="&#x2122;" k="41" />
<hkern u1="n" u2="&#x201c;" k="61" />
<hkern u1="n" u2="&#x2018;" k="61" />
<hkern u1="n" u2="y" k="10" />
<hkern u1="n" u2="v" k="10" />
<hkern u1="n" u2="t" k="20" />
<hkern u1="n" u2="\" k="20" />
<hkern u1="n" u2="&#x3f;" k="20" />
<hkern u1="o" u2="&#x2122;" k="61" />
<hkern u1="o" u2="&#x2026;" k="41" />
<hkern u1="o" u2="&#x201e;" k="41" />
<hkern u1="o" u2="&#x201c;" k="82" />
<hkern u1="o" u2="&#x201a;" k="41" />
<hkern u1="o" u2="&#x2018;" k="82" />
<hkern u1="o" u2="z" k="31" />
<hkern u1="o" u2="y" k="20" />
<hkern u1="o" u2="x" k="31" />
<hkern u1="o" u2="v" k="20" />
<hkern u1="o" u2="\" k="20" />
<hkern u1="o" u2="&#x3f;" k="41" />
<hkern u1="o" u2="&#x3b;" k="20" />
<hkern u1="o" u2="&#x3a;" k="20" />
<hkern u1="o" u2="&#x2f;" k="20" />
<hkern u1="o" u2="&#x2e;" k="41" />
<hkern u1="o" u2="&#x2c;" k="41" />
<hkern u1="p" u2="&#x2122;" k="61" />
<hkern u1="p" u2="&#x2026;" k="41" />
<hkern u1="p" u2="&#x201e;" k="41" />
<hkern u1="p" u2="&#x201c;" k="82" />
<hkern u1="p" u2="&#x201a;" k="41" />
<hkern u1="p" u2="&#x2018;" k="82" />
<hkern u1="p" u2="z" k="31" />
<hkern u1="p" u2="y" k="20" />
<hkern u1="p" u2="x" k="31" />
<hkern u1="p" u2="v" k="20" />
<hkern u1="p" u2="\" k="20" />
<hkern u1="p" u2="&#x3f;" k="41" />
<hkern u1="p" u2="&#x3b;" k="20" />
<hkern u1="p" u2="&#x3a;" k="20" />
<hkern u1="p" u2="&#x2f;" k="20" />
<hkern u1="p" u2="&#x2e;" k="41" />
<hkern u1="p" u2="&#x2c;" k="41" />
<hkern u1="r" u2="&#x2026;" k="102" />
<hkern u1="r" u2="&#x201e;" k="102" />
<hkern u1="r" u2="&#x201d;" k="-82" />
<hkern u1="r" u2="&#x201c;" k="-41" />
<hkern u1="r" u2="&#x201a;" k="102" />
<hkern u1="r" u2="&#x2019;" k="-82" />
<hkern u1="r" u2="&#x2018;" k="-41" />
<hkern u1="r" u2="&#x2014;" k="41" />
<hkern u1="r" u2="&#x2013;" k="41" />
<hkern u1="r" u2="&#x153;" k="10" />
<hkern u1="r" u2="&#xe7;" k="10" />
<hkern u1="r" u2="&#xe6;" k="10" />
<hkern u1="r" u2="t" k="-41" />
<hkern u1="r" u2="q" k="10" />
<hkern u1="r" u2="o" k="10" />
<hkern u1="r" u2="g" k="20" />
<hkern u1="r" u2="f" k="-31" />
<hkern u1="r" u2="e" k="10" />
<hkern u1="r" u2="d" k="10" />
<hkern u1="r" u2="c" k="10" />
<hkern u1="r" u2="a" k="10" />
<hkern u1="r" u2="&#x3f;" k="-41" />
<hkern u1="r" u2="&#x2f;" k="123" />
<hkern u1="r" u2="&#x2e;" k="102" />
<hkern u1="r" u2="&#x2d;" k="41" />
<hkern u1="r" u2="&#x2c;" k="102" />
<hkern u1="t" u2="&#x2026;" k="-20" />
<hkern u1="t" u2="&#x201e;" k="-20" />
<hkern u1="t" u2="&#x201c;" k="-20" />
<hkern u1="t" u2="&#x201a;" k="-20" />
<hkern u1="t" u2="&#x2018;" k="-20" />
<hkern u1="t" u2="&#x153;" k="10" />
<hkern u1="t" u2="&#xe7;" k="10" />
<hkern u1="t" u2="&#xe6;" k="10" />
<hkern u1="t" u2="q" k="10" />
<hkern u1="t" u2="o" k="10" />
<hkern u1="t" u2="e" k="10" />
<hkern u1="t" u2="d" k="10" />
<hkern u1="t" u2="c" k="10" />
<hkern u1="t" u2="a" k="10" />
<hkern u1="t" u2="&#x2e;" k="-20" />
<hkern u1="t" u2="&#x2c;" k="-20" />
<hkern u1="v" u2="&#x2026;" k="102" />
<hkern u1="v" u2="&#x201e;" k="102" />
<hkern u1="v" u2="&#x201d;" k="-61" />
<hkern u1="v" u2="&#x201c;" k="-61" />
<hkern u1="v" u2="&#x201a;" k="102" />
<hkern u1="v" u2="&#x2019;" k="-61" />
<hkern u1="v" u2="&#x2018;" k="-61" />
<hkern u1="v" u2="&#x2014;" k="20" />
<hkern u1="v" u2="&#x2013;" k="20" />
<hkern u1="v" u2="&#x153;" k="20" />
<hkern u1="v" u2="&#xe7;" k="20" />
<hkern u1="v" u2="&#xe6;" k="20" />
<hkern u1="v" u2="q" k="20" />
<hkern u1="v" u2="o" k="20" />
<hkern u1="v" u2="e" k="20" />
<hkern u1="v" u2="d" k="20" />
<hkern u1="v" u2="c" k="20" />
<hkern u1="v" u2="a" k="20" />
<hkern u1="v" u2="&#x2e;" k="102" />
<hkern u1="v" u2="&#x2d;" k="20" />
<hkern u1="v" u2="&#x2c;" k="102" />
<hkern u1="w" u2="&#x2026;" k="61" />
<hkern u1="w" u2="&#x201e;" k="61" />
<hkern u1="w" u2="&#x201d;" k="-41" />
<hkern u1="w" u2="&#x201c;" k="-41" />
<hkern u1="w" u2="&#x201a;" k="61" />
<hkern u1="w" u2="&#x2019;" k="-41" />
<hkern u1="w" u2="&#x2018;" k="-41" />
<hkern u1="w" u2="&#x2e;" k="61" />
<hkern u1="w" u2="&#x2c;" k="61" />
<hkern u1="x" u2="&#x2039;" k="102" />
<hkern u1="x" u2="&#x201d;" k="-41" />
<hkern u1="x" u2="&#x201c;" k="-20" />
<hkern u1="x" u2="&#x2019;" k="-41" />
<hkern u1="x" u2="&#x2018;" k="-20" />
<hkern u1="x" u2="&#x2014;" k="61" />
<hkern u1="x" u2="&#x2013;" k="61" />
<hkern u1="x" u2="&#x153;" k="31" />
<hkern u1="x" u2="&#xe7;" k="31" />
<hkern u1="x" u2="&#xe6;" k="31" />
<hkern u1="x" u2="&#xab;" k="102" />
<hkern u1="x" u2="q" k="31" />
<hkern u1="x" u2="o" k="31" />
<hkern u1="x" u2="e" k="31" />
<hkern u1="x" u2="d" k="31" />
<hkern u1="x" u2="c" k="31" />
<hkern u1="x" u2="a" k="31" />
<hkern u1="x" u2="&#x2d;" k="61" />
<hkern u1="y" u2="&#x2026;" k="102" />
<hkern u1="y" u2="&#x201e;" k="102" />
<hkern u1="y" u2="&#x201d;" k="-61" />
<hkern u1="y" u2="&#x201c;" k="-61" />
<hkern u1="y" u2="&#x201a;" k="102" />
<hkern u1="y" u2="&#x2019;" k="-61" />
<hkern u1="y" u2="&#x2018;" k="-61" />
<hkern u1="y" u2="&#x2014;" k="20" />
<hkern u1="y" u2="&#x2013;" k="20" />
<hkern u1="y" u2="&#x153;" k="20" />
<hkern u1="y" u2="&#xe7;" k="20" />
<hkern u1="y" u2="&#xe6;" k="20" />
<hkern u1="y" u2="q" k="20" />
<hkern u1="y" u2="o" k="20" />
<hkern u1="y" u2="e" k="20" />
<hkern u1="y" u2="d" k="20" />
<hkern u1="y" u2="c" k="20" />
<hkern u1="y" u2="a" k="20" />
<hkern u1="y" u2="&#x2e;" k="102" />
<hkern u1="y" u2="&#x2d;" k="20" />
<hkern u1="y" u2="&#x2c;" k="102" />
<hkern u1="z" u2="&#x2039;" k="123" />
<hkern u1="z" u2="&#x2014;" k="61" />
<hkern u1="z" u2="&#x2013;" k="61" />
<hkern u1="z" u2="&#x153;" k="41" />
<hkern u1="z" u2="&#xe7;" k="41" />
<hkern u1="z" u2="&#xe6;" k="41" />
<hkern u1="z" u2="&#xab;" k="123" />
<hkern u1="z" u2="q" k="41" />
<hkern u1="z" u2="o" k="41" />
<hkern u1="z" u2="g" k="20" />
<hkern u1="z" u2="e" k="41" />
<hkern u1="z" u2="d" k="41" />
<hkern u1="z" u2="c" k="41" />
<hkern u1="z" u2="a" k="41" />
<hkern u1="z" u2="&#x2d;" k="61" />
<hkern u1="&#x7b;" u2="&#x178;" k="-102" />
<hkern u1="&#x7b;" u2="&#xdd;" k="-102" />
<hkern u1="&#x7b;" u2="j" k="-307" />
<hkern u1="&#x7b;" u2="g" k="-41" />
<hkern u1="&#x7b;" u2="Y" k="-102" />
<hkern u1="&#x7b;" u2="X" k="-82" />
<hkern u1="&#x7b;" u2="W" k="-20" />
<hkern u1="&#x7b;" u2="V" k="-61" />
<hkern u1="&#xa3;" u2="&#x34;" k="41" />
<hkern u1="&#xa3;" u2="&#x31;" k="-20" />
<hkern u1="&#xa4;" u2="&#x34;" k="41" />
<hkern u1="&#xa9;" u2="&#x178;" k="61" />
<hkern u1="&#xa9;" u2="&#xdd;" k="61" />
<hkern u1="&#xa9;" u2="&#xc6;" k="20" />
<hkern u1="&#xa9;" u2="&#xc5;" k="20" />
<hkern u1="&#xa9;" u2="&#xc4;" k="20" />
<hkern u1="&#xa9;" u2="&#xc3;" k="20" />
<hkern u1="&#xa9;" u2="&#xc2;" k="20" />
<hkern u1="&#xa9;" u2="&#xc1;" k="20" />
<hkern u1="&#xa9;" u2="&#xc0;" k="20" />
<hkern u1="&#xa9;" u2="Y" k="61" />
<hkern u1="&#xa9;" u2="W" k="41" />
<hkern u1="&#xa9;" u2="T" k="61" />
<hkern u1="&#xa9;" u2="A" k="20" />
<hkern u1="&#xab;" u2="&#x178;" k="82" />
<hkern u1="&#xab;" u2="&#xdd;" k="82" />
<hkern u1="&#xab;" u2="Y" k="82" />
<hkern u1="&#xab;" u2="W" k="41" />
<hkern u1="&#xab;" u2="V" k="41" />
<hkern u1="&#xab;" u2="T" k="82" />
<hkern u1="&#xae;" u2="&#x178;" k="61" />
<hkern u1="&#xae;" u2="&#xdd;" k="61" />
<hkern u1="&#xae;" u2="&#xc6;" k="20" />
<hkern u1="&#xae;" u2="&#xc5;" k="20" />
<hkern u1="&#xae;" u2="&#xc4;" k="20" />
<hkern u1="&#xae;" u2="&#xc3;" k="20" />
<hkern u1="&#xae;" u2="&#xc2;" k="20" />
<hkern u1="&#xae;" u2="&#xc1;" k="20" />
<hkern u1="&#xae;" u2="&#xc0;" k="20" />
<hkern u1="&#xae;" u2="Y" k="61" />
<hkern u1="&#xae;" u2="W" k="41" />
<hkern u1="&#xae;" u2="T" k="61" />
<hkern u1="&#xae;" u2="A" k="20" />
<hkern u1="&#xb0;" u2="&#x34;" k="133" />
<hkern u1="&#xbb;" u2="&#x178;" k="102" />
<hkern u1="&#xbb;" u2="&#xdd;" k="102" />
<hkern u1="&#xbb;" u2="z" k="102" />
<hkern u1="&#xbb;" u2="x" k="102" />
<hkern u1="&#xbb;" u2="Y" k="102" />
<hkern u1="&#xbb;" u2="W" k="82" />
<hkern u1="&#xbb;" u2="V" k="102" />
<hkern u1="&#xbb;" u2="T" k="225" />
<hkern u1="&#xbf;" u2="&#x178;" k="82" />
<hkern u1="&#xbf;" u2="&#x152;" k="41" />
<hkern u1="&#xbf;" u2="&#xdd;" k="82" />
<hkern u1="&#xbf;" u2="&#xd8;" k="41" />
<hkern u1="&#xbf;" u2="&#xd6;" k="41" />
<hkern u1="&#xbf;" u2="&#xd5;" k="41" />
<hkern u1="&#xbf;" u2="&#xd4;" k="41" />
<hkern u1="&#xbf;" u2="&#xd3;" k="41" />
<hkern u1="&#xbf;" u2="&#xd2;" k="41" />
<hkern u1="&#xbf;" u2="y" k="61" />
<hkern u1="&#xbf;" u2="x" k="20" />
<hkern u1="&#xbf;" u2="w" k="41" />
<hkern u1="&#xbf;" u2="v" k="61" />
<hkern u1="&#xbf;" u2="Y" k="82" />
<hkern u1="&#xbf;" u2="W" k="61" />
<hkern u1="&#xbf;" u2="V" k="102" />
<hkern u1="&#xbf;" u2="T" k="123" />
<hkern u1="&#xbf;" u2="Q" k="41" />
<hkern u1="&#xbf;" u2="O" k="41" />
<hkern u1="&#xbf;" u2="G" k="41" />
<hkern u1="&#xbf;" u2="C" k="41" />
<hkern u1="&#xbf;" u2="&#x37;" k="61" />
<hkern u1="&#xbf;" u2="&#x33;" k="-20" />
<hkern u1="&#xbf;" u2="&#x31;" k="82" />
<hkern u1="&#xc0;" u2="&#x2122;" k="143" />
<hkern u1="&#xc0;" u2="&#x2026;" k="-61" />
<hkern u1="&#xc0;" u2="&#x201e;" k="-61" />
<hkern u1="&#xc0;" u2="&#x201d;" k="61" />
<hkern u1="&#xc0;" u2="&#x201c;" k="123" />
<hkern u1="&#xc0;" u2="&#x201a;" k="-61" />
<hkern u1="&#xc0;" u2="&#x2019;" k="61" />
<hkern u1="&#xc0;" u2="&#x2018;" k="123" />
<hkern u1="&#xc0;" u2="&#x2014;" k="-20" />
<hkern u1="&#xc0;" u2="&#x2013;" k="-20" />
<hkern u1="&#xc0;" u2="&#x178;" k="102" />
<hkern u1="&#xc0;" u2="&#x153;" k="10" />
<hkern u1="&#xc0;" u2="&#x152;" k="41" />
<hkern u1="&#xc0;" u2="&#xe7;" k="10" />
<hkern u1="&#xc0;" u2="&#xe6;" k="10" />
<hkern u1="&#xc0;" u2="&#xdd;" k="102" />
<hkern u1="&#xc0;" u2="&#xd8;" k="41" />
<hkern u1="&#xc0;" u2="&#xd6;" k="41" />
<hkern u1="&#xc0;" u2="&#xd5;" k="41" />
<hkern u1="&#xc0;" u2="&#xd4;" k="41" />
<hkern u1="&#xc0;" u2="&#xd3;" k="41" />
<hkern u1="&#xc0;" u2="&#xd2;" k="41" />
<hkern u1="&#xc0;" u2="&#xc6;" k="-20" />
<hkern u1="&#xc0;" u2="&#xc5;" k="-20" />
<hkern u1="&#xc0;" u2="&#xc4;" k="-20" />
<hkern u1="&#xc0;" u2="&#xc3;" k="-20" />
<hkern u1="&#xc0;" u2="&#xc2;" k="-20" />
<hkern u1="&#xc0;" u2="&#xc1;" k="-20" />
<hkern u1="&#xc0;" u2="&#xc0;" k="-20" />
<hkern u1="&#xc0;" u2="&#xae;" k="20" />
<hkern u1="&#xc0;" u2="&#xa9;" k="20" />
<hkern u1="&#xc0;" u2="z" k="-20" />
<hkern u1="&#xc0;" u2="y" k="61" />
<hkern u1="&#xc0;" u2="x" k="-41" />
<hkern u1="&#xc0;" u2="w" k="20" />
<hkern u1="&#xc0;" u2="v" k="61" />
<hkern u1="&#xc0;" u2="u" k="10" />
<hkern u1="&#xc0;" u2="t" k="10" />
<hkern u1="&#xc0;" u2="r" k="10" />
<hkern u1="&#xc0;" u2="q" k="10" />
<hkern u1="&#xc0;" u2="p" k="10" />
<hkern u1="&#xc0;" u2="o" k="10" />
<hkern u1="&#xc0;" u2="n" k="10" />
<hkern u1="&#xc0;" u2="m" k="10" />
<hkern u1="&#xc0;" u2="e" k="10" />
<hkern u1="&#xc0;" u2="d" k="10" />
<hkern u1="&#xc0;" u2="c" k="10" />
<hkern u1="&#xc0;" u2="a" k="10" />
<hkern u1="&#xc0;" u2="Y" k="102" />
<hkern u1="&#xc0;" u2="X" k="-20" />
<hkern u1="&#xc0;" u2="W" k="102" />
<hkern u1="&#xc0;" u2="V" k="102" />
<hkern u1="&#xc0;" u2="T" k="143" />
<hkern u1="&#xc0;" u2="S" k="-10" />
<hkern u1="&#xc0;" u2="Q" k="41" />
<hkern u1="&#xc0;" u2="O" k="41" />
<hkern u1="&#xc0;" u2="J" k="-20" />
<hkern u1="&#xc0;" u2="G" k="41" />
<hkern u1="&#xc0;" u2="C" k="41" />
<hkern u1="&#xc0;" u2="A" k="-20" />
<hkern u1="&#xc0;" u2="&#x40;" k="20" />
<hkern u1="&#xc0;" u2="&#x2e;" k="-61" />
<hkern u1="&#xc0;" u2="&#x2d;" k="-20" />
<hkern u1="&#xc0;" u2="&#x2c;" k="-61" />
<hkern u1="&#xc0;" u2="&#x2a;" k="123" />
<hkern u1="&#xc1;" u2="&#x2122;" k="143" />
<hkern u1="&#xc1;" u2="&#x2026;" k="-61" />
<hkern u1="&#xc1;" u2="&#x201e;" k="-61" />
<hkern u1="&#xc1;" u2="&#x201d;" k="61" />
<hkern u1="&#xc1;" u2="&#x201c;" k="123" />
<hkern u1="&#xc1;" u2="&#x201a;" k="-61" />
<hkern u1="&#xc1;" u2="&#x2019;" k="61" />
<hkern u1="&#xc1;" u2="&#x2018;" k="123" />
<hkern u1="&#xc1;" u2="&#x2014;" k="-20" />
<hkern u1="&#xc1;" u2="&#x2013;" k="-20" />
<hkern u1="&#xc1;" u2="&#x178;" k="102" />
<hkern u1="&#xc1;" u2="&#x153;" k="10" />
<hkern u1="&#xc1;" u2="&#x152;" k="41" />
<hkern u1="&#xc1;" u2="&#xe7;" k="10" />
<hkern u1="&#xc1;" u2="&#xe6;" k="10" />
<hkern u1="&#xc1;" u2="&#xdd;" k="102" />
<hkern u1="&#xc1;" u2="&#xd8;" k="41" />
<hkern u1="&#xc1;" u2="&#xd6;" k="41" />
<hkern u1="&#xc1;" u2="&#xd5;" k="41" />
<hkern u1="&#xc1;" u2="&#xd4;" k="41" />
<hkern u1="&#xc1;" u2="&#xd3;" k="41" />
<hkern u1="&#xc1;" u2="&#xd2;" k="41" />
<hkern u1="&#xc1;" u2="&#xc6;" k="-20" />
<hkern u1="&#xc1;" u2="&#xc5;" k="-20" />
<hkern u1="&#xc1;" u2="&#xc4;" k="-20" />
<hkern u1="&#xc1;" u2="&#xc3;" k="-20" />
<hkern u1="&#xc1;" u2="&#xc2;" k="-20" />
<hkern u1="&#xc1;" u2="&#xc1;" k="-20" />
<hkern u1="&#xc1;" u2="&#xc0;" k="-20" />
<hkern u1="&#xc1;" u2="&#xae;" k="20" />
<hkern u1="&#xc1;" u2="&#xa9;" k="20" />
<hkern u1="&#xc1;" u2="z" k="-20" />
<hkern u1="&#xc1;" u2="y" k="61" />
<hkern u1="&#xc1;" u2="x" k="-41" />
<hkern u1="&#xc1;" u2="w" k="20" />
<hkern u1="&#xc1;" u2="v" k="61" />
<hkern u1="&#xc1;" u2="u" k="10" />
<hkern u1="&#xc1;" u2="t" k="10" />
<hkern u1="&#xc1;" u2="r" k="10" />
<hkern u1="&#xc1;" u2="q" k="10" />
<hkern u1="&#xc1;" u2="p" k="10" />
<hkern u1="&#xc1;" u2="o" k="10" />
<hkern u1="&#xc1;" u2="n" k="10" />
<hkern u1="&#xc1;" u2="m" k="10" />
<hkern u1="&#xc1;" u2="e" k="10" />
<hkern u1="&#xc1;" u2="d" k="10" />
<hkern u1="&#xc1;" u2="c" k="10" />
<hkern u1="&#xc1;" u2="a" k="10" />
<hkern u1="&#xc1;" u2="Y" k="102" />
<hkern u1="&#xc1;" u2="X" k="-20" />
<hkern u1="&#xc1;" u2="W" k="102" />
<hkern u1="&#xc1;" u2="V" k="102" />
<hkern u1="&#xc1;" u2="T" k="143" />
<hkern u1="&#xc1;" u2="S" k="-10" />
<hkern u1="&#xc1;" u2="Q" k="41" />
<hkern u1="&#xc1;" u2="O" k="41" />
<hkern u1="&#xc1;" u2="J" k="-20" />
<hkern u1="&#xc1;" u2="G" k="41" />
<hkern u1="&#xc1;" u2="C" k="41" />
<hkern u1="&#xc1;" u2="A" k="-20" />
<hkern u1="&#xc1;" u2="&#x40;" k="20" />
<hkern u1="&#xc1;" u2="&#x2e;" k="-61" />
<hkern u1="&#xc1;" u2="&#x2d;" k="-20" />
<hkern u1="&#xc1;" u2="&#x2c;" k="-61" />
<hkern u1="&#xc1;" u2="&#x2a;" k="123" />
<hkern u1="&#xc2;" u2="&#x2122;" k="143" />
<hkern u1="&#xc2;" u2="&#x2026;" k="-61" />
<hkern u1="&#xc2;" u2="&#x201e;" k="-61" />
<hkern u1="&#xc2;" u2="&#x201d;" k="61" />
<hkern u1="&#xc2;" u2="&#x201c;" k="123" />
<hkern u1="&#xc2;" u2="&#x201a;" k="-61" />
<hkern u1="&#xc2;" u2="&#x2019;" k="61" />
<hkern u1="&#xc2;" u2="&#x2018;" k="123" />
<hkern u1="&#xc2;" u2="&#x2014;" k="-20" />
<hkern u1="&#xc2;" u2="&#x2013;" k="-20" />
<hkern u1="&#xc2;" u2="&#x178;" k="102" />
<hkern u1="&#xc2;" u2="&#x153;" k="10" />
<hkern u1="&#xc2;" u2="&#x152;" k="41" />
<hkern u1="&#xc2;" u2="&#xe7;" k="10" />
<hkern u1="&#xc2;" u2="&#xe6;" k="10" />
<hkern u1="&#xc2;" u2="&#xdd;" k="102" />
<hkern u1="&#xc2;" u2="&#xd8;" k="41" />
<hkern u1="&#xc2;" u2="&#xd6;" k="41" />
<hkern u1="&#xc2;" u2="&#xd5;" k="41" />
<hkern u1="&#xc2;" u2="&#xd4;" k="41" />
<hkern u1="&#xc2;" u2="&#xd3;" k="41" />
<hkern u1="&#xc2;" u2="&#xd2;" k="41" />
<hkern u1="&#xc2;" u2="&#xc6;" k="-20" />
<hkern u1="&#xc2;" u2="&#xc5;" k="-20" />
<hkern u1="&#xc2;" u2="&#xc4;" k="-20" />
<hkern u1="&#xc2;" u2="&#xc3;" k="-20" />
<hkern u1="&#xc2;" u2="&#xc2;" k="-20" />
<hkern u1="&#xc2;" u2="&#xc1;" k="-20" />
<hkern u1="&#xc2;" u2="&#xc0;" k="-20" />
<hkern u1="&#xc2;" u2="&#xae;" k="20" />
<hkern u1="&#xc2;" u2="&#xa9;" k="20" />
<hkern u1="&#xc2;" u2="z" k="-20" />
<hkern u1="&#xc2;" u2="y" k="61" />
<hkern u1="&#xc2;" u2="x" k="-41" />
<hkern u1="&#xc2;" u2="w" k="20" />
<hkern u1="&#xc2;" u2="v" k="61" />
<hkern u1="&#xc2;" u2="u" k="10" />
<hkern u1="&#xc2;" u2="t" k="10" />
<hkern u1="&#xc2;" u2="r" k="10" />
<hkern u1="&#xc2;" u2="q" k="10" />
<hkern u1="&#xc2;" u2="p" k="10" />
<hkern u1="&#xc2;" u2="o" k="10" />
<hkern u1="&#xc2;" u2="n" k="10" />
<hkern u1="&#xc2;" u2="m" k="10" />
<hkern u1="&#xc2;" u2="e" k="10" />
<hkern u1="&#xc2;" u2="d" k="10" />
<hkern u1="&#xc2;" u2="c" k="10" />
<hkern u1="&#xc2;" u2="a" k="10" />
<hkern u1="&#xc2;" u2="Y" k="102" />
<hkern u1="&#xc2;" u2="X" k="-20" />
<hkern u1="&#xc2;" u2="W" k="102" />
<hkern u1="&#xc2;" u2="V" k="102" />
<hkern u1="&#xc2;" u2="T" k="143" />
<hkern u1="&#xc2;" u2="S" k="-10" />
<hkern u1="&#xc2;" u2="Q" k="41" />
<hkern u1="&#xc2;" u2="O" k="41" />
<hkern u1="&#xc2;" u2="J" k="-20" />
<hkern u1="&#xc2;" u2="G" k="41" />
<hkern u1="&#xc2;" u2="C" k="41" />
<hkern u1="&#xc2;" u2="A" k="-20" />
<hkern u1="&#xc2;" u2="&#x40;" k="20" />
<hkern u1="&#xc2;" u2="&#x2e;" k="-61" />
<hkern u1="&#xc2;" u2="&#x2d;" k="-20" />
<hkern u1="&#xc2;" u2="&#x2c;" k="-61" />
<hkern u1="&#xc2;" u2="&#x2a;" k="123" />
<hkern u1="&#xc3;" u2="&#x2122;" k="143" />
<hkern u1="&#xc3;" u2="&#x2026;" k="-61" />
<hkern u1="&#xc3;" u2="&#x201e;" k="-61" />
<hkern u1="&#xc3;" u2="&#x201d;" k="61" />
<hkern u1="&#xc3;" u2="&#x201c;" k="123" />
<hkern u1="&#xc3;" u2="&#x201a;" k="-61" />
<hkern u1="&#xc3;" u2="&#x2019;" k="61" />
<hkern u1="&#xc3;" u2="&#x2018;" k="123" />
<hkern u1="&#xc3;" u2="&#x2014;" k="-20" />
<hkern u1="&#xc3;" u2="&#x2013;" k="-20" />
<hkern u1="&#xc3;" u2="&#x178;" k="102" />
<hkern u1="&#xc3;" u2="&#x153;" k="10" />
<hkern u1="&#xc3;" u2="&#x152;" k="41" />
<hkern u1="&#xc3;" u2="&#xe7;" k="10" />
<hkern u1="&#xc3;" u2="&#xe6;" k="10" />
<hkern u1="&#xc3;" u2="&#xdd;" k="102" />
<hkern u1="&#xc3;" u2="&#xd8;" k="41" />
<hkern u1="&#xc3;" u2="&#xd6;" k="41" />
<hkern u1="&#xc3;" u2="&#xd5;" k="41" />
<hkern u1="&#xc3;" u2="&#xd4;" k="41" />
<hkern u1="&#xc3;" u2="&#xd3;" k="41" />
<hkern u1="&#xc3;" u2="&#xd2;" k="41" />
<hkern u1="&#xc3;" u2="&#xc6;" k="-20" />
<hkern u1="&#xc3;" u2="&#xc5;" k="-20" />
<hkern u1="&#xc3;" u2="&#xc4;" k="-20" />
<hkern u1="&#xc3;" u2="&#xc3;" k="-20" />
<hkern u1="&#xc3;" u2="&#xc2;" k="-20" />
<hkern u1="&#xc3;" u2="&#xc1;" k="-20" />
<hkern u1="&#xc3;" u2="&#xc0;" k="-20" />
<hkern u1="&#xc3;" u2="&#xae;" k="20" />
<hkern u1="&#xc3;" u2="&#xa9;" k="20" />
<hkern u1="&#xc3;" u2="z" k="-20" />
<hkern u1="&#xc3;" u2="y" k="61" />
<hkern u1="&#xc3;" u2="x" k="-41" />
<hkern u1="&#xc3;" u2="w" k="20" />
<hkern u1="&#xc3;" u2="v" k="61" />
<hkern u1="&#xc3;" u2="u" k="10" />
<hkern u1="&#xc3;" u2="t" k="10" />
<hkern u1="&#xc3;" u2="r" k="10" />
<hkern u1="&#xc3;" u2="q" k="10" />
<hkern u1="&#xc3;" u2="p" k="10" />
<hkern u1="&#xc3;" u2="o" k="10" />
<hkern u1="&#xc3;" u2="n" k="10" />
<hkern u1="&#xc3;" u2="m" k="10" />
<hkern u1="&#xc3;" u2="e" k="10" />
<hkern u1="&#xc3;" u2="d" k="10" />
<hkern u1="&#xc3;" u2="c" k="10" />
<hkern u1="&#xc3;" u2="a" k="10" />
<hkern u1="&#xc3;" u2="Y" k="102" />
<hkern u1="&#xc3;" u2="X" k="-20" />
<hkern u1="&#xc3;" u2="W" k="102" />
<hkern u1="&#xc3;" u2="V" k="102" />
<hkern u1="&#xc3;" u2="T" k="143" />
<hkern u1="&#xc3;" u2="S" k="-10" />
<hkern u1="&#xc3;" u2="Q" k="41" />
<hkern u1="&#xc3;" u2="O" k="41" />
<hkern u1="&#xc3;" u2="J" k="-20" />
<hkern u1="&#xc3;" u2="G" k="41" />
<hkern u1="&#xc3;" u2="C" k="41" />
<hkern u1="&#xc3;" u2="A" k="-20" />
<hkern u1="&#xc3;" u2="&#x40;" k="20" />
<hkern u1="&#xc3;" u2="&#x2e;" k="-61" />
<hkern u1="&#xc3;" u2="&#x2d;" k="-20" />
<hkern u1="&#xc3;" u2="&#x2c;" k="-61" />
<hkern u1="&#xc3;" u2="&#x2a;" k="123" />
<hkern u1="&#xc4;" u2="&#x2122;" k="143" />
<hkern u1="&#xc4;" u2="&#x2026;" k="-61" />
<hkern u1="&#xc4;" u2="&#x201e;" k="-61" />
<hkern u1="&#xc4;" u2="&#x201d;" k="61" />
<hkern u1="&#xc4;" u2="&#x201c;" k="123" />
<hkern u1="&#xc4;" u2="&#x201a;" k="-61" />
<hkern u1="&#xc4;" u2="&#x2019;" k="61" />
<hkern u1="&#xc4;" u2="&#x2018;" k="123" />
<hkern u1="&#xc4;" u2="&#x2014;" k="-20" />
<hkern u1="&#xc4;" u2="&#x2013;" k="-20" />
<hkern u1="&#xc4;" u2="&#x178;" k="102" />
<hkern u1="&#xc4;" u2="&#x153;" k="10" />
<hkern u1="&#xc4;" u2="&#x152;" k="41" />
<hkern u1="&#xc4;" u2="&#xe7;" k="10" />
<hkern u1="&#xc4;" u2="&#xe6;" k="10" />
<hkern u1="&#xc4;" u2="&#xdd;" k="102" />
<hkern u1="&#xc4;" u2="&#xd8;" k="41" />
<hkern u1="&#xc4;" u2="&#xd6;" k="41" />
<hkern u1="&#xc4;" u2="&#xd5;" k="41" />
<hkern u1="&#xc4;" u2="&#xd4;" k="41" />
<hkern u1="&#xc4;" u2="&#xd3;" k="41" />
<hkern u1="&#xc4;" u2="&#xd2;" k="41" />
<hkern u1="&#xc4;" u2="&#xc6;" k="-20" />
<hkern u1="&#xc4;" u2="&#xc5;" k="-20" />
<hkern u1="&#xc4;" u2="&#xc4;" k="-20" />
<hkern u1="&#xc4;" u2="&#xc3;" k="-20" />
<hkern u1="&#xc4;" u2="&#xc2;" k="-20" />
<hkern u1="&#xc4;" u2="&#xc1;" k="-20" />
<hkern u1="&#xc4;" u2="&#xc0;" k="-20" />
<hkern u1="&#xc4;" u2="&#xae;" k="20" />
<hkern u1="&#xc4;" u2="&#xa9;" k="20" />
<hkern u1="&#xc4;" u2="z" k="-20" />
<hkern u1="&#xc4;" u2="y" k="61" />
<hkern u1="&#xc4;" u2="x" k="-41" />
<hkern u1="&#xc4;" u2="w" k="20" />
<hkern u1="&#xc4;" u2="v" k="61" />
<hkern u1="&#xc4;" u2="u" k="10" />
<hkern u1="&#xc4;" u2="t" k="10" />
<hkern u1="&#xc4;" u2="r" k="10" />
<hkern u1="&#xc4;" u2="q" k="10" />
<hkern u1="&#xc4;" u2="p" k="10" />
<hkern u1="&#xc4;" u2="o" k="10" />
<hkern u1="&#xc4;" u2="n" k="10" />
<hkern u1="&#xc4;" u2="m" k="10" />
<hkern u1="&#xc4;" u2="e" k="10" />
<hkern u1="&#xc4;" u2="d" k="10" />
<hkern u1="&#xc4;" u2="c" k="10" />
<hkern u1="&#xc4;" u2="a" k="10" />
<hkern u1="&#xc4;" u2="Y" k="102" />
<hkern u1="&#xc4;" u2="X" k="-20" />
<hkern u1="&#xc4;" u2="W" k="102" />
<hkern u1="&#xc4;" u2="V" k="102" />
<hkern u1="&#xc4;" u2="T" k="143" />
<hkern u1="&#xc4;" u2="S" k="-10" />
<hkern u1="&#xc4;" u2="Q" k="41" />
<hkern u1="&#xc4;" u2="O" k="41" />
<hkern u1="&#xc4;" u2="J" k="-20" />
<hkern u1="&#xc4;" u2="G" k="41" />
<hkern u1="&#xc4;" u2="C" k="41" />
<hkern u1="&#xc4;" u2="A" k="-20" />
<hkern u1="&#xc4;" u2="&#x40;" k="20" />
<hkern u1="&#xc4;" u2="&#x2e;" k="-61" />
<hkern u1="&#xc4;" u2="&#x2d;" k="-20" />
<hkern u1="&#xc4;" u2="&#x2c;" k="-61" />
<hkern u1="&#xc4;" u2="&#x2a;" k="123" />
<hkern u1="&#xc5;" u2="&#x2122;" k="143" />
<hkern u1="&#xc5;" u2="&#x2026;" k="-61" />
<hkern u1="&#xc5;" u2="&#x201e;" k="-61" />
<hkern u1="&#xc5;" u2="&#x201d;" k="61" />
<hkern u1="&#xc5;" u2="&#x201c;" k="123" />
<hkern u1="&#xc5;" u2="&#x201a;" k="-61" />
<hkern u1="&#xc5;" u2="&#x2019;" k="61" />
<hkern u1="&#xc5;" u2="&#x2018;" k="123" />
<hkern u1="&#xc5;" u2="&#x2014;" k="-20" />
<hkern u1="&#xc5;" u2="&#x2013;" k="-20" />
<hkern u1="&#xc5;" u2="&#x178;" k="102" />
<hkern u1="&#xc5;" u2="&#x153;" k="10" />
<hkern u1="&#xc5;" u2="&#x152;" k="41" />
<hkern u1="&#xc5;" u2="&#xe7;" k="10" />
<hkern u1="&#xc5;" u2="&#xe6;" k="10" />
<hkern u1="&#xc5;" u2="&#xdd;" k="102" />
<hkern u1="&#xc5;" u2="&#xd8;" k="41" />
<hkern u1="&#xc5;" u2="&#xd6;" k="41" />
<hkern u1="&#xc5;" u2="&#xd5;" k="41" />
<hkern u1="&#xc5;" u2="&#xd4;" k="41" />
<hkern u1="&#xc5;" u2="&#xd3;" k="41" />
<hkern u1="&#xc5;" u2="&#xd2;" k="41" />
<hkern u1="&#xc5;" u2="&#xc6;" k="-20" />
<hkern u1="&#xc5;" u2="&#xc5;" k="-20" />
<hkern u1="&#xc5;" u2="&#xc4;" k="-20" />
<hkern u1="&#xc5;" u2="&#xc3;" k="-20" />
<hkern u1="&#xc5;" u2="&#xc2;" k="-20" />
<hkern u1="&#xc5;" u2="&#xc1;" k="-20" />
<hkern u1="&#xc5;" u2="&#xc0;" k="-20" />
<hkern u1="&#xc5;" u2="&#xae;" k="20" />
<hkern u1="&#xc5;" u2="&#xa9;" k="20" />
<hkern u1="&#xc5;" u2="z" k="-20" />
<hkern u1="&#xc5;" u2="y" k="61" />
<hkern u1="&#xc5;" u2="x" k="-41" />
<hkern u1="&#xc5;" u2="w" k="20" />
<hkern u1="&#xc5;" u2="v" k="61" />
<hkern u1="&#xc5;" u2="u" k="10" />
<hkern u1="&#xc5;" u2="t" k="10" />
<hkern u1="&#xc5;" u2="r" k="10" />
<hkern u1="&#xc5;" u2="q" k="10" />
<hkern u1="&#xc5;" u2="p" k="10" />
<hkern u1="&#xc5;" u2="o" k="10" />
<hkern u1="&#xc5;" u2="n" k="10" />
<hkern u1="&#xc5;" u2="m" k="10" />
<hkern u1="&#xc5;" u2="e" k="10" />
<hkern u1="&#xc5;" u2="d" k="10" />
<hkern u1="&#xc5;" u2="c" k="10" />
<hkern u1="&#xc5;" u2="a" k="10" />
<hkern u1="&#xc5;" u2="Y" k="102" />
<hkern u1="&#xc5;" u2="X" k="-20" />
<hkern u1="&#xc5;" u2="W" k="102" />
<hkern u1="&#xc5;" u2="V" k="102" />
<hkern u1="&#xc5;" u2="T" k="143" />
<hkern u1="&#xc5;" u2="S" k="-10" />
<hkern u1="&#xc5;" u2="Q" k="41" />
<hkern u1="&#xc5;" u2="O" k="41" />
<hkern u1="&#xc5;" u2="J" k="-20" />
<hkern u1="&#xc5;" u2="G" k="41" />
<hkern u1="&#xc5;" u2="C" k="41" />
<hkern u1="&#xc5;" u2="A" k="-20" />
<hkern u1="&#xc5;" u2="&#x40;" k="20" />
<hkern u1="&#xc5;" u2="&#x2e;" k="-61" />
<hkern u1="&#xc5;" u2="&#x2d;" k="-20" />
<hkern u1="&#xc5;" u2="&#x2c;" k="-61" />
<hkern u1="&#xc5;" u2="&#x2a;" k="123" />
<hkern u1="&#xc6;" u2="&#x153;" k="51" />
<hkern u1="&#xc6;" u2="&#xe7;" k="51" />
<hkern u1="&#xc6;" u2="&#xe6;" k="51" />
<hkern u1="&#xc6;" u2="&#xae;" k="41" />
<hkern u1="&#xc6;" u2="&#xa9;" k="41" />
<hkern u1="&#xc6;" u2="y" k="41" />
<hkern u1="&#xc6;" u2="v" k="41" />
<hkern u1="&#xc6;" u2="q" k="51" />
<hkern u1="&#xc6;" u2="o" k="51" />
<hkern u1="&#xc6;" u2="g" k="10" />
<hkern u1="&#xc6;" u2="f" k="20" />
<hkern u1="&#xc6;" u2="e" k="51" />
<hkern u1="&#xc6;" u2="d" k="51" />
<hkern u1="&#xc6;" u2="c" k="51" />
<hkern u1="&#xc6;" u2="a" k="51" />
<hkern u1="&#xc6;" u2="T" k="-31" />
<hkern u1="&#xc6;" u2="&#x40;" k="41" />
<hkern u1="&#xc8;" u2="&#x153;" k="51" />
<hkern u1="&#xc8;" u2="&#xe7;" k="51" />
<hkern u1="&#xc8;" u2="&#xe6;" k="51" />
<hkern u1="&#xc8;" u2="&#xae;" k="41" />
<hkern u1="&#xc8;" u2="&#xa9;" k="41" />
<hkern u1="&#xc8;" u2="y" k="41" />
<hkern u1="&#xc8;" u2="v" k="41" />
<hkern u1="&#xc8;" u2="q" k="51" />
<hkern u1="&#xc8;" u2="o" k="51" />
<hkern u1="&#xc8;" u2="g" k="10" />
<hkern u1="&#xc8;" u2="f" k="20" />
<hkern u1="&#xc8;" u2="e" k="51" />
<hkern u1="&#xc8;" u2="d" k="51" />
<hkern u1="&#xc8;" u2="c" k="51" />
<hkern u1="&#xc8;" u2="a" k="51" />
<hkern u1="&#xc8;" u2="T" k="-31" />
<hkern u1="&#xc8;" u2="&#x40;" k="41" />
<hkern u1="&#xc9;" u2="&#x153;" k="51" />
<hkern u1="&#xc9;" u2="&#xe7;" k="51" />
<hkern u1="&#xc9;" u2="&#xe6;" k="51" />
<hkern u1="&#xc9;" u2="&#xae;" k="41" />
<hkern u1="&#xc9;" u2="&#xa9;" k="41" />
<hkern u1="&#xc9;" u2="y" k="41" />
<hkern u1="&#xc9;" u2="v" k="41" />
<hkern u1="&#xc9;" u2="q" k="51" />
<hkern u1="&#xc9;" u2="o" k="51" />
<hkern u1="&#xc9;" u2="g" k="10" />
<hkern u1="&#xc9;" u2="f" k="20" />
<hkern u1="&#xc9;" u2="e" k="51" />
<hkern u1="&#xc9;" u2="d" k="51" />
<hkern u1="&#xc9;" u2="c" k="51" />
<hkern u1="&#xc9;" u2="a" k="51" />
<hkern u1="&#xc9;" u2="T" k="-31" />
<hkern u1="&#xc9;" u2="&#x40;" k="41" />
<hkern u1="&#xca;" u2="&#x153;" k="51" />
<hkern u1="&#xca;" u2="&#xe7;" k="51" />
<hkern u1="&#xca;" u2="&#xe6;" k="51" />
<hkern u1="&#xca;" u2="&#xae;" k="41" />
<hkern u1="&#xca;" u2="&#xa9;" k="41" />
<hkern u1="&#xca;" u2="y" k="41" />
<hkern u1="&#xca;" u2="v" k="41" />
<hkern u1="&#xca;" u2="q" k="51" />
<hkern u1="&#xca;" u2="o" k="51" />
<hkern u1="&#xca;" u2="g" k="10" />
<hkern u1="&#xca;" u2="f" k="20" />
<hkern u1="&#xca;" u2="e" k="51" />
<hkern u1="&#xca;" u2="d" k="51" />
<hkern u1="&#xca;" u2="c" k="51" />
<hkern u1="&#xca;" u2="a" k="51" />
<hkern u1="&#xca;" u2="T" k="-31" />
<hkern u1="&#xca;" u2="&#x40;" k="41" />
<hkern u1="&#xcb;" u2="&#x153;" k="51" />
<hkern u1="&#xcb;" u2="&#xe7;" k="51" />
<hkern u1="&#xcb;" u2="&#xe6;" k="51" />
<hkern u1="&#xcb;" u2="&#xae;" k="41" />
<hkern u1="&#xcb;" u2="&#xa9;" k="41" />
<hkern u1="&#xcb;" u2="y" k="41" />
<hkern u1="&#xcb;" u2="v" k="41" />
<hkern u1="&#xcb;" u2="q" k="51" />
<hkern u1="&#xcb;" u2="o" k="51" />
<hkern u1="&#xcb;" u2="g" k="10" />
<hkern u1="&#xcb;" u2="f" k="20" />
<hkern u1="&#xcb;" u2="e" k="51" />
<hkern u1="&#xcb;" u2="d" k="51" />
<hkern u1="&#xcb;" u2="c" k="51" />
<hkern u1="&#xcb;" u2="a" k="51" />
<hkern u1="&#xcb;" u2="T" k="-31" />
<hkern u1="&#xcb;" u2="&#x40;" k="41" />
<hkern u1="&#xcc;" u2="y" k="20" />
<hkern u1="&#xcc;" u2="v" k="20" />
<hkern u1="&#xcc;" u2="&#x2f;" k="41" />
<hkern u1="&#xcd;" u2="y" k="20" />
<hkern u1="&#xcd;" u2="v" k="20" />
<hkern u1="&#xcd;" u2="&#x2f;" k="41" />
<hkern u1="&#xce;" u2="y" k="20" />
<hkern u1="&#xce;" u2="v" k="20" />
<hkern u1="&#xce;" u2="&#x2f;" k="41" />
<hkern u1="&#xcf;" u2="y" k="20" />
<hkern u1="&#xcf;" u2="v" k="20" />
<hkern u1="&#xcf;" u2="&#x2f;" k="41" />
<hkern u1="&#xd0;" u2="&#x2026;" k="82" />
<hkern u1="&#xd0;" u2="&#x201e;" k="82" />
<hkern u1="&#xd0;" u2="&#x201c;" k="41" />
<hkern u1="&#xd0;" u2="&#x201a;" k="82" />
<hkern u1="&#xd0;" u2="&#x2018;" k="41" />
<hkern u1="&#xd0;" u2="&#x178;" k="51" />
<hkern u1="&#xd0;" u2="&#x153;" k="10" />
<hkern u1="&#xd0;" u2="&#xe7;" k="10" />
<hkern u1="&#xd0;" u2="&#xe6;" k="10" />
<hkern u1="&#xd0;" u2="&#xdd;" k="51" />
<hkern u1="&#xd0;" u2="&#xc6;" k="41" />
<hkern u1="&#xd0;" u2="&#xc5;" k="41" />
<hkern u1="&#xd0;" u2="&#xc4;" k="41" />
<hkern u1="&#xd0;" u2="&#xc3;" k="41" />
<hkern u1="&#xd0;" u2="&#xc2;" k="41" />
<hkern u1="&#xd0;" u2="&#xc1;" k="41" />
<hkern u1="&#xd0;" u2="&#xc0;" k="41" />
<hkern u1="&#xd0;" u2="z" k="20" />
<hkern u1="&#xd0;" u2="x" k="20" />
<hkern u1="&#xd0;" u2="u" k="10" />
<hkern u1="&#xd0;" u2="r" k="10" />
<hkern u1="&#xd0;" u2="q" k="10" />
<hkern u1="&#xd0;" u2="p" k="10" />
<hkern u1="&#xd0;" u2="o" k="10" />
<hkern u1="&#xd0;" u2="n" k="10" />
<hkern u1="&#xd0;" u2="m" k="10" />
<hkern u1="&#xd0;" u2="l" k="20" />
<hkern u1="&#xd0;" u2="k" k="20" />
<hkern u1="&#xd0;" u2="h" k="20" />
<hkern u1="&#xd0;" u2="e" k="10" />
<hkern u1="&#xd0;" u2="d" k="10" />
<hkern u1="&#xd0;" u2="c" k="10" />
<hkern u1="&#xd0;" u2="b" k="20" />
<hkern u1="&#xd0;" u2="a" k="10" />
<hkern u1="&#xd0;" u2="Z" k="92" />
<hkern u1="&#xd0;" u2="Y" k="51" />
<hkern u1="&#xd0;" u2="X" k="51" />
<hkern u1="&#xd0;" u2="W" k="82" />
<hkern u1="&#xd0;" u2="V" k="41" />
<hkern u1="&#xd0;" u2="T" k="61" />
<hkern u1="&#xd0;" u2="J" k="92" />
<hkern u1="&#xd0;" u2="A" k="41" />
<hkern u1="&#xd0;" u2="&#x3f;" k="41" />
<hkern u1="&#xd0;" u2="&#x2f;" k="102" />
<hkern u1="&#xd0;" u2="&#x2e;" k="82" />
<hkern u1="&#xd0;" u2="&#x2c;" k="82" />
<hkern u1="&#xd1;" u2="y" k="20" />
<hkern u1="&#xd1;" u2="v" k="20" />
<hkern u1="&#xd1;" u2="&#x2f;" k="41" />
<hkern u1="&#xd2;" u2="&#x2026;" k="82" />
<hkern u1="&#xd2;" u2="&#x201e;" k="82" />
<hkern u1="&#xd2;" u2="&#x201c;" k="41" />
<hkern u1="&#xd2;" u2="&#x201a;" k="82" />
<hkern u1="&#xd2;" u2="&#x2018;" k="41" />
<hkern u1="&#xd2;" u2="&#x178;" k="51" />
<hkern u1="&#xd2;" u2="&#x153;" k="10" />
<hkern u1="&#xd2;" u2="&#xe7;" k="10" />
<hkern u1="&#xd2;" u2="&#xe6;" k="10" />
<hkern u1="&#xd2;" u2="&#xdd;" k="51" />
<hkern u1="&#xd2;" u2="&#xc6;" k="41" />
<hkern u1="&#xd2;" u2="&#xc5;" k="41" />
<hkern u1="&#xd2;" u2="&#xc4;" k="41" />
<hkern u1="&#xd2;" u2="&#xc3;" k="41" />
<hkern u1="&#xd2;" u2="&#xc2;" k="41" />
<hkern u1="&#xd2;" u2="&#xc1;" k="41" />
<hkern u1="&#xd2;" u2="&#xc0;" k="41" />
<hkern u1="&#xd2;" u2="z" k="20" />
<hkern u1="&#xd2;" u2="x" k="20" />
<hkern u1="&#xd2;" u2="u" k="10" />
<hkern u1="&#xd2;" u2="r" k="10" />
<hkern u1="&#xd2;" u2="q" k="10" />
<hkern u1="&#xd2;" u2="p" k="10" />
<hkern u1="&#xd2;" u2="o" k="10" />
<hkern u1="&#xd2;" u2="n" k="10" />
<hkern u1="&#xd2;" u2="m" k="10" />
<hkern u1="&#xd2;" u2="l" k="20" />
<hkern u1="&#xd2;" u2="k" k="20" />
<hkern u1="&#xd2;" u2="h" k="20" />
<hkern u1="&#xd2;" u2="e" k="10" />
<hkern u1="&#xd2;" u2="d" k="10" />
<hkern u1="&#xd2;" u2="c" k="10" />
<hkern u1="&#xd2;" u2="b" k="20" />
<hkern u1="&#xd2;" u2="a" k="10" />
<hkern u1="&#xd2;" u2="Z" k="92" />
<hkern u1="&#xd2;" u2="Y" k="51" />
<hkern u1="&#xd2;" u2="X" k="51" />
<hkern u1="&#xd2;" u2="W" k="82" />
<hkern u1="&#xd2;" u2="V" k="41" />
<hkern u1="&#xd2;" u2="T" k="61" />
<hkern u1="&#xd2;" u2="J" k="92" />
<hkern u1="&#xd2;" u2="A" k="41" />
<hkern u1="&#xd2;" u2="&#x3f;" k="41" />
<hkern u1="&#xd2;" u2="&#x2f;" k="102" />
<hkern u1="&#xd2;" u2="&#x2e;" k="82" />
<hkern u1="&#xd2;" u2="&#x2c;" k="82" />
<hkern u1="&#xd3;" u2="&#x2026;" k="82" />
<hkern u1="&#xd3;" u2="&#x201e;" k="82" />
<hkern u1="&#xd3;" u2="&#x201c;" k="41" />
<hkern u1="&#xd3;" u2="&#x201a;" k="82" />
<hkern u1="&#xd3;" u2="&#x2018;" k="41" />
<hkern u1="&#xd3;" u2="&#x178;" k="51" />
<hkern u1="&#xd3;" u2="&#x153;" k="10" />
<hkern u1="&#xd3;" u2="&#xe7;" k="10" />
<hkern u1="&#xd3;" u2="&#xe6;" k="10" />
<hkern u1="&#xd3;" u2="&#xdd;" k="51" />
<hkern u1="&#xd3;" u2="&#xc6;" k="41" />
<hkern u1="&#xd3;" u2="&#xc5;" k="41" />
<hkern u1="&#xd3;" u2="&#xc4;" k="41" />
<hkern u1="&#xd3;" u2="&#xc3;" k="41" />
<hkern u1="&#xd3;" u2="&#xc2;" k="41" />
<hkern u1="&#xd3;" u2="&#xc1;" k="41" />
<hkern u1="&#xd3;" u2="&#xc0;" k="41" />
<hkern u1="&#xd3;" u2="z" k="20" />
<hkern u1="&#xd3;" u2="x" k="20" />
<hkern u1="&#xd3;" u2="u" k="10" />
<hkern u1="&#xd3;" u2="r" k="10" />
<hkern u1="&#xd3;" u2="q" k="10" />
<hkern u1="&#xd3;" u2="p" k="10" />
<hkern u1="&#xd3;" u2="o" k="10" />
<hkern u1="&#xd3;" u2="n" k="10" />
<hkern u1="&#xd3;" u2="m" k="10" />
<hkern u1="&#xd3;" u2="l" k="20" />
<hkern u1="&#xd3;" u2="k" k="20" />
<hkern u1="&#xd3;" u2="h" k="20" />
<hkern u1="&#xd3;" u2="e" k="10" />
<hkern u1="&#xd3;" u2="d" k="10" />
<hkern u1="&#xd3;" u2="c" k="10" />
<hkern u1="&#xd3;" u2="b" k="20" />
<hkern u1="&#xd3;" u2="a" k="10" />
<hkern u1="&#xd3;" u2="Z" k="92" />
<hkern u1="&#xd3;" u2="Y" k="51" />
<hkern u1="&#xd3;" u2="X" k="51" />
<hkern u1="&#xd3;" u2="W" k="82" />
<hkern u1="&#xd3;" u2="V" k="41" />
<hkern u1="&#xd3;" u2="T" k="61" />
<hkern u1="&#xd3;" u2="J" k="92" />
<hkern u1="&#xd3;" u2="A" k="41" />
<hkern u1="&#xd3;" u2="&#x3f;" k="41" />
<hkern u1="&#xd3;" u2="&#x2f;" k="102" />
<hkern u1="&#xd3;" u2="&#x2e;" k="82" />
<hkern u1="&#xd3;" u2="&#x2c;" k="82" />
<hkern u1="&#xd4;" u2="&#x2026;" k="82" />
<hkern u1="&#xd4;" u2="&#x201e;" k="82" />
<hkern u1="&#xd4;" u2="&#x201c;" k="41" />
<hkern u1="&#xd4;" u2="&#x201a;" k="82" />
<hkern u1="&#xd4;" u2="&#x2018;" k="41" />
<hkern u1="&#xd4;" u2="&#x178;" k="51" />
<hkern u1="&#xd4;" u2="&#x153;" k="10" />
<hkern u1="&#xd4;" u2="&#xe7;" k="10" />
<hkern u1="&#xd4;" u2="&#xe6;" k="10" />
<hkern u1="&#xd4;" u2="&#xdd;" k="51" />
<hkern u1="&#xd4;" u2="&#xc6;" k="41" />
<hkern u1="&#xd4;" u2="&#xc5;" k="41" />
<hkern u1="&#xd4;" u2="&#xc4;" k="41" />
<hkern u1="&#xd4;" u2="&#xc3;" k="41" />
<hkern u1="&#xd4;" u2="&#xc2;" k="41" />
<hkern u1="&#xd4;" u2="&#xc1;" k="41" />
<hkern u1="&#xd4;" u2="&#xc0;" k="41" />
<hkern u1="&#xd4;" u2="z" k="20" />
<hkern u1="&#xd4;" u2="x" k="20" />
<hkern u1="&#xd4;" u2="u" k="10" />
<hkern u1="&#xd4;" u2="r" k="10" />
<hkern u1="&#xd4;" u2="q" k="10" />
<hkern u1="&#xd4;" u2="p" k="10" />
<hkern u1="&#xd4;" u2="o" k="10" />
<hkern u1="&#xd4;" u2="n" k="10" />
<hkern u1="&#xd4;" u2="m" k="10" />
<hkern u1="&#xd4;" u2="l" k="20" />
<hkern u1="&#xd4;" u2="k" k="20" />
<hkern u1="&#xd4;" u2="h" k="20" />
<hkern u1="&#xd4;" u2="e" k="10" />
<hkern u1="&#xd4;" u2="d" k="10" />
<hkern u1="&#xd4;" u2="c" k="10" />
<hkern u1="&#xd4;" u2="b" k="20" />
<hkern u1="&#xd4;" u2="a" k="10" />
<hkern u1="&#xd4;" u2="Z" k="92" />
<hkern u1="&#xd4;" u2="Y" k="51" />
<hkern u1="&#xd4;" u2="X" k="51" />
<hkern u1="&#xd4;" u2="W" k="82" />
<hkern u1="&#xd4;" u2="V" k="41" />
<hkern u1="&#xd4;" u2="T" k="61" />
<hkern u1="&#xd4;" u2="J" k="92" />
<hkern u1="&#xd4;" u2="A" k="41" />
<hkern u1="&#xd4;" u2="&#x3f;" k="41" />
<hkern u1="&#xd4;" u2="&#x2f;" k="102" />
<hkern u1="&#xd4;" u2="&#x2e;" k="82" />
<hkern u1="&#xd4;" u2="&#x2c;" k="82" />
<hkern u1="&#xd5;" u2="&#x2026;" k="82" />
<hkern u1="&#xd5;" u2="&#x201e;" k="82" />
<hkern u1="&#xd5;" u2="&#x201c;" k="41" />
<hkern u1="&#xd5;" u2="&#x201a;" k="82" />
<hkern u1="&#xd5;" u2="&#x2018;" k="41" />
<hkern u1="&#xd5;" u2="&#x178;" k="51" />
<hkern u1="&#xd5;" u2="&#x153;" k="10" />
<hkern u1="&#xd5;" u2="&#xe7;" k="10" />
<hkern u1="&#xd5;" u2="&#xe6;" k="10" />
<hkern u1="&#xd5;" u2="&#xdd;" k="51" />
<hkern u1="&#xd5;" u2="&#xc6;" k="41" />
<hkern u1="&#xd5;" u2="&#xc5;" k="41" />
<hkern u1="&#xd5;" u2="&#xc4;" k="41" />
<hkern u1="&#xd5;" u2="&#xc3;" k="41" />
<hkern u1="&#xd5;" u2="&#xc2;" k="41" />
<hkern u1="&#xd5;" u2="&#xc1;" k="41" />
<hkern u1="&#xd5;" u2="&#xc0;" k="41" />
<hkern u1="&#xd5;" u2="z" k="20" />
<hkern u1="&#xd5;" u2="x" k="20" />
<hkern u1="&#xd5;" u2="u" k="10" />
<hkern u1="&#xd5;" u2="r" k="10" />
<hkern u1="&#xd5;" u2="q" k="10" />
<hkern u1="&#xd5;" u2="p" k="10" />
<hkern u1="&#xd5;" u2="o" k="10" />
<hkern u1="&#xd5;" u2="n" k="10" />
<hkern u1="&#xd5;" u2="m" k="10" />
<hkern u1="&#xd5;" u2="l" k="20" />
<hkern u1="&#xd5;" u2="k" k="20" />
<hkern u1="&#xd5;" u2="h" k="20" />
<hkern u1="&#xd5;" u2="e" k="10" />
<hkern u1="&#xd5;" u2="d" k="10" />
<hkern u1="&#xd5;" u2="c" k="10" />
<hkern u1="&#xd5;" u2="b" k="20" />
<hkern u1="&#xd5;" u2="a" k="10" />
<hkern u1="&#xd5;" u2="Z" k="92" />
<hkern u1="&#xd5;" u2="Y" k="51" />
<hkern u1="&#xd5;" u2="X" k="51" />
<hkern u1="&#xd5;" u2="W" k="82" />
<hkern u1="&#xd5;" u2="V" k="41" />
<hkern u1="&#xd5;" u2="T" k="61" />
<hkern u1="&#xd5;" u2="J" k="92" />
<hkern u1="&#xd5;" u2="A" k="41" />
<hkern u1="&#xd5;" u2="&#x3f;" k="41" />
<hkern u1="&#xd5;" u2="&#x2f;" k="102" />
<hkern u1="&#xd5;" u2="&#x2e;" k="82" />
<hkern u1="&#xd5;" u2="&#x2c;" k="82" />
<hkern u1="&#xd6;" u2="&#x2026;" k="82" />
<hkern u1="&#xd6;" u2="&#x201e;" k="82" />
<hkern u1="&#xd6;" u2="&#x201c;" k="41" />
<hkern u1="&#xd6;" u2="&#x201a;" k="82" />
<hkern u1="&#xd6;" u2="&#x2018;" k="41" />
<hkern u1="&#xd6;" u2="&#x178;" k="51" />
<hkern u1="&#xd6;" u2="&#x153;" k="10" />
<hkern u1="&#xd6;" u2="&#xe7;" k="10" />
<hkern u1="&#xd6;" u2="&#xe6;" k="10" />
<hkern u1="&#xd6;" u2="&#xdd;" k="51" />
<hkern u1="&#xd6;" u2="&#xc6;" k="41" />
<hkern u1="&#xd6;" u2="&#xc5;" k="41" />
<hkern u1="&#xd6;" u2="&#xc4;" k="41" />
<hkern u1="&#xd6;" u2="&#xc3;" k="41" />
<hkern u1="&#xd6;" u2="&#xc2;" k="41" />
<hkern u1="&#xd6;" u2="&#xc1;" k="41" />
<hkern u1="&#xd6;" u2="&#xc0;" k="41" />
<hkern u1="&#xd6;" u2="z" k="20" />
<hkern u1="&#xd6;" u2="x" k="20" />
<hkern u1="&#xd6;" u2="u" k="10" />
<hkern u1="&#xd6;" u2="r" k="10" />
<hkern u1="&#xd6;" u2="q" k="10" />
<hkern u1="&#xd6;" u2="p" k="10" />
<hkern u1="&#xd6;" u2="o" k="10" />
<hkern u1="&#xd6;" u2="n" k="10" />
<hkern u1="&#xd6;" u2="m" k="10" />
<hkern u1="&#xd6;" u2="l" k="20" />
<hkern u1="&#xd6;" u2="k" k="20" />
<hkern u1="&#xd6;" u2="h" k="20" />
<hkern u1="&#xd6;" u2="e" k="10" />
<hkern u1="&#xd6;" u2="d" k="10" />
<hkern u1="&#xd6;" u2="c" k="10" />
<hkern u1="&#xd6;" u2="b" k="20" />
<hkern u1="&#xd6;" u2="a" k="10" />
<hkern u1="&#xd6;" u2="Z" k="92" />
<hkern u1="&#xd6;" u2="Y" k="51" />
<hkern u1="&#xd6;" u2="X" k="51" />
<hkern u1="&#xd6;" u2="W" k="82" />
<hkern u1="&#xd6;" u2="V" k="41" />
<hkern u1="&#xd6;" u2="T" k="61" />
<hkern u1="&#xd6;" u2="J" k="92" />
<hkern u1="&#xd6;" u2="A" k="41" />
<hkern u1="&#xd6;" u2="&#x3f;" k="41" />
<hkern u1="&#xd6;" u2="&#x2f;" k="102" />
<hkern u1="&#xd6;" u2="&#x2e;" k="82" />
<hkern u1="&#xd6;" u2="&#x2c;" k="82" />
<hkern u1="&#xd8;" u2="&#x2026;" k="82" />
<hkern u1="&#xd8;" u2="&#x201e;" k="82" />
<hkern u1="&#xd8;" u2="&#x201c;" k="41" />
<hkern u1="&#xd8;" u2="&#x201a;" k="82" />
<hkern u1="&#xd8;" u2="&#x2018;" k="41" />
<hkern u1="&#xd8;" u2="&#x178;" k="51" />
<hkern u1="&#xd8;" u2="&#x153;" k="10" />
<hkern u1="&#xd8;" u2="&#xe7;" k="10" />
<hkern u1="&#xd8;" u2="&#xe6;" k="10" />
<hkern u1="&#xd8;" u2="&#xdd;" k="51" />
<hkern u1="&#xd8;" u2="&#xc6;" k="41" />
<hkern u1="&#xd8;" u2="&#xc5;" k="41" />
<hkern u1="&#xd8;" u2="&#xc4;" k="41" />
<hkern u1="&#xd8;" u2="&#xc3;" k="41" />
<hkern u1="&#xd8;" u2="&#xc2;" k="41" />
<hkern u1="&#xd8;" u2="&#xc1;" k="41" />
<hkern u1="&#xd8;" u2="&#xc0;" k="41" />
<hkern u1="&#xd8;" u2="z" k="20" />
<hkern u1="&#xd8;" u2="x" k="20" />
<hkern u1="&#xd8;" u2="u" k="10" />
<hkern u1="&#xd8;" u2="r" k="10" />
<hkern u1="&#xd8;" u2="q" k="10" />
<hkern u1="&#xd8;" u2="p" k="10" />
<hkern u1="&#xd8;" u2="o" k="10" />
<hkern u1="&#xd8;" u2="n" k="10" />
<hkern u1="&#xd8;" u2="m" k="10" />
<hkern u1="&#xd8;" u2="l" k="20" />
<hkern u1="&#xd8;" u2="k" k="20" />
<hkern u1="&#xd8;" u2="h" k="20" />
<hkern u1="&#xd8;" u2="e" k="10" />
<hkern u1="&#xd8;" u2="d" k="10" />
<hkern u1="&#xd8;" u2="c" k="10" />
<hkern u1="&#xd8;" u2="b" k="20" />
<hkern u1="&#xd8;" u2="a" k="10" />
<hkern u1="&#xd8;" u2="Z" k="92" />
<hkern u1="&#xd8;" u2="Y" k="51" />
<hkern u1="&#xd8;" u2="X" k="51" />
<hkern u1="&#xd8;" u2="W" k="82" />
<hkern u1="&#xd8;" u2="V" k="41" />
<hkern u1="&#xd8;" u2="T" k="61" />
<hkern u1="&#xd8;" u2="J" k="92" />
<hkern u1="&#xd8;" u2="A" k="41" />
<hkern u1="&#xd8;" u2="&#x3f;" k="41" />
<hkern u1="&#xd8;" u2="&#x2f;" k="102" />
<hkern u1="&#xd8;" u2="&#x2e;" k="82" />
<hkern u1="&#xd8;" u2="&#x2c;" k="82" />
<hkern u1="&#xd9;" u2="&#x2026;" k="20" />
<hkern u1="&#xd9;" u2="&#x201e;" k="20" />
<hkern u1="&#xd9;" u2="&#x201a;" k="20" />
<hkern u1="&#xd9;" u2="J" k="20" />
<hkern u1="&#xd9;" u2="&#x2e;" k="20" />
<hkern u1="&#xd9;" u2="&#x2c;" k="20" />
<hkern u1="&#xda;" u2="&#x2026;" k="20" />
<hkern u1="&#xda;" u2="&#x201e;" k="20" />
<hkern u1="&#xda;" u2="&#x201a;" k="20" />
<hkern u1="&#xda;" u2="J" k="20" />
<hkern u1="&#xda;" u2="&#x2e;" k="20" />
<hkern u1="&#xda;" u2="&#x2c;" k="20" />
<hkern u1="&#xdb;" u2="&#x2026;" k="20" />
<hkern u1="&#xdb;" u2="&#x201e;" k="20" />
<hkern u1="&#xdb;" u2="&#x201a;" k="20" />
<hkern u1="&#xdb;" u2="J" k="20" />
<hkern u1="&#xdb;" u2="&#x2e;" k="20" />
<hkern u1="&#xdb;" u2="&#x2c;" k="20" />
<hkern u1="&#xdc;" u2="&#x2026;" k="20" />
<hkern u1="&#xdc;" u2="&#x201e;" k="20" />
<hkern u1="&#xdc;" u2="&#x201a;" k="20" />
<hkern u1="&#xdc;" u2="J" k="20" />
<hkern u1="&#xdc;" u2="&#x2e;" k="20" />
<hkern u1="&#xdc;" u2="&#x2c;" k="20" />
<hkern u1="&#xdd;" u2="&#x203a;" k="82" />
<hkern u1="&#xdd;" u2="&#x2039;" k="123" />
<hkern u1="&#xdd;" u2="&#x2026;" k="102" />
<hkern u1="&#xdd;" u2="&#x201e;" k="102" />
<hkern u1="&#xdd;" u2="&#x201a;" k="102" />
<hkern u1="&#xdd;" u2="&#x2014;" k="102" />
<hkern u1="&#xdd;" u2="&#x2013;" k="102" />
<hkern u1="&#xdd;" u2="&#x153;" k="113" />
<hkern u1="&#xdd;" u2="&#x152;" k="51" />
<hkern u1="&#xdd;" u2="&#xe7;" k="113" />
<hkern u1="&#xdd;" u2="&#xe6;" k="113" />
<hkern u1="&#xdd;" u2="&#xd8;" k="51" />
<hkern u1="&#xdd;" u2="&#xd6;" k="51" />
<hkern u1="&#xdd;" u2="&#xd5;" k="51" />
<hkern u1="&#xdd;" u2="&#xd4;" k="51" />
<hkern u1="&#xdd;" u2="&#xd3;" k="51" />
<hkern u1="&#xdd;" u2="&#xd2;" k="51" />
<hkern u1="&#xdd;" u2="&#xc6;" k="102" />
<hkern u1="&#xdd;" u2="&#xc5;" k="102" />
<hkern u1="&#xdd;" u2="&#xc4;" k="102" />
<hkern u1="&#xdd;" u2="&#xc3;" k="102" />
<hkern u1="&#xdd;" u2="&#xc2;" k="102" />
<hkern u1="&#xdd;" u2="&#xc1;" k="102" />
<hkern u1="&#xdd;" u2="&#xc0;" k="102" />
<hkern u1="&#xdd;" u2="&#xbb;" k="82" />
<hkern u1="&#xdd;" u2="&#xae;" k="61" />
<hkern u1="&#xdd;" u2="&#xab;" k="123" />
<hkern u1="&#xdd;" u2="&#xa9;" k="61" />
<hkern u1="&#xdd;" u2="&#x7d;" k="-102" />
<hkern u1="&#xdd;" u2="z" k="61" />
<hkern u1="&#xdd;" u2="y" k="41" />
<hkern u1="&#xdd;" u2="x" k="41" />
<hkern u1="&#xdd;" u2="w" k="20" />
<hkern u1="&#xdd;" u2="v" k="41" />
<hkern u1="&#xdd;" u2="u" k="82" />
<hkern u1="&#xdd;" u2="t" k="41" />
<hkern u1="&#xdd;" u2="s" k="102" />
<hkern u1="&#xdd;" u2="r" k="82" />
<hkern u1="&#xdd;" u2="q" k="113" />
<hkern u1="&#xdd;" u2="p" k="82" />
<hkern u1="&#xdd;" u2="o" k="113" />
<hkern u1="&#xdd;" u2="n" k="82" />
<hkern u1="&#xdd;" u2="m" k="82" />
<hkern u1="&#xdd;" u2="g" k="82" />
<hkern u1="&#xdd;" u2="f" k="20" />
<hkern u1="&#xdd;" u2="e" k="113" />
<hkern u1="&#xdd;" u2="d" k="113" />
<hkern u1="&#xdd;" u2="c" k="113" />
<hkern u1="&#xdd;" u2="a" k="113" />
<hkern u1="&#xdd;" u2="]" k="-102" />
<hkern u1="&#xdd;" u2="X" k="-41" />
<hkern u1="&#xdd;" u2="V" k="-61" />
<hkern u1="&#xdd;" u2="T" k="-41" />
<hkern u1="&#xdd;" u2="S" k="-20" />
<hkern u1="&#xdd;" u2="Q" k="51" />
<hkern u1="&#xdd;" u2="O" k="51" />
<hkern u1="&#xdd;" u2="J" k="143" />
<hkern u1="&#xdd;" u2="G" k="51" />
<hkern u1="&#xdd;" u2="C" k="51" />
<hkern u1="&#xdd;" u2="A" k="102" />
<hkern u1="&#xdd;" u2="&#x40;" k="61" />
<hkern u1="&#xdd;" u2="&#x3b;" k="61" />
<hkern u1="&#xdd;" u2="&#x3a;" k="61" />
<hkern u1="&#xdd;" u2="&#x2e;" k="102" />
<hkern u1="&#xdd;" u2="&#x2d;" k="102" />
<hkern u1="&#xdd;" u2="&#x2c;" k="102" />
<hkern u1="&#xdd;" u2="&#x29;" k="-102" />
<hkern u1="&#xdd;" u2="&#x26;" k="61" />
<hkern u1="&#xde;" u2="&#x2026;" k="82" />
<hkern u1="&#xde;" u2="&#x201e;" k="82" />
<hkern u1="&#xde;" u2="&#x201c;" k="41" />
<hkern u1="&#xde;" u2="&#x201a;" k="82" />
<hkern u1="&#xde;" u2="&#x2018;" k="41" />
<hkern u1="&#xde;" u2="&#x178;" k="51" />
<hkern u1="&#xde;" u2="&#x153;" k="10" />
<hkern u1="&#xde;" u2="&#xe7;" k="10" />
<hkern u1="&#xde;" u2="&#xe6;" k="10" />
<hkern u1="&#xde;" u2="&#xdd;" k="51" />
<hkern u1="&#xde;" u2="&#xc6;" k="41" />
<hkern u1="&#xde;" u2="&#xc5;" k="41" />
<hkern u1="&#xde;" u2="&#xc4;" k="41" />
<hkern u1="&#xde;" u2="&#xc3;" k="41" />
<hkern u1="&#xde;" u2="&#xc2;" k="41" />
<hkern u1="&#xde;" u2="&#xc1;" k="41" />
<hkern u1="&#xde;" u2="&#xc0;" k="41" />
<hkern u1="&#xde;" u2="z" k="20" />
<hkern u1="&#xde;" u2="x" k="20" />
<hkern u1="&#xde;" u2="u" k="10" />
<hkern u1="&#xde;" u2="r" k="10" />
<hkern u1="&#xde;" u2="q" k="10" />
<hkern u1="&#xde;" u2="p" k="10" />
<hkern u1="&#xde;" u2="o" k="10" />
<hkern u1="&#xde;" u2="n" k="10" />
<hkern u1="&#xde;" u2="m" k="10" />
<hkern u1="&#xde;" u2="l" k="20" />
<hkern u1="&#xde;" u2="k" k="20" />
<hkern u1="&#xde;" u2="h" k="20" />
<hkern u1="&#xde;" u2="e" k="10" />
<hkern u1="&#xde;" u2="d" k="10" />
<hkern u1="&#xde;" u2="c" k="10" />
<hkern u1="&#xde;" u2="b" k="20" />
<hkern u1="&#xde;" u2="a" k="10" />
<hkern u1="&#xde;" u2="Z" k="92" />
<hkern u1="&#xde;" u2="Y" k="51" />
<hkern u1="&#xde;" u2="X" k="51" />
<hkern u1="&#xde;" u2="W" k="82" />
<hkern u1="&#xde;" u2="V" k="41" />
<hkern u1="&#xde;" u2="T" k="61" />
<hkern u1="&#xde;" u2="J" k="92" />
<hkern u1="&#xde;" u2="A" k="41" />
<hkern u1="&#xde;" u2="&#x3f;" k="41" />
<hkern u1="&#xde;" u2="&#x2f;" k="102" />
<hkern u1="&#xde;" u2="&#x2e;" k="82" />
<hkern u1="&#xde;" u2="&#x2c;" k="82" />
<hkern u1="&#xdf;" u2="&#x2122;" k="61" />
<hkern u1="&#xdf;" u2="&#x2026;" k="41" />
<hkern u1="&#xdf;" u2="&#x201e;" k="41" />
<hkern u1="&#xdf;" u2="&#x201c;" k="82" />
<hkern u1="&#xdf;" u2="&#x201a;" k="41" />
<hkern u1="&#xdf;" u2="&#x2018;" k="82" />
<hkern u1="&#xdf;" u2="z" k="31" />
<hkern u1="&#xdf;" u2="y" k="20" />
<hkern u1="&#xdf;" u2="x" k="31" />
<hkern u1="&#xdf;" u2="v" k="20" />
<hkern u1="&#xdf;" u2="t" k="20" />
<hkern u1="&#xdf;" u2="g" k="20" />
<hkern u1="&#xdf;" u2="\" k="20" />
<hkern u1="&#xdf;" u2="&#x3f;" k="41" />
<hkern u1="&#xdf;" u2="&#x3b;" k="20" />
<hkern u1="&#xdf;" u2="&#x3a;" k="20" />
<hkern u1="&#xdf;" u2="&#x2f;" k="20" />
<hkern u1="&#xdf;" u2="&#x2e;" k="41" />
<hkern u1="&#xdf;" u2="&#x2c;" k="41" />
<hkern u1="&#xe0;" u2="&#x2122;" k="41" />
<hkern u1="&#xe0;" u2="&#x201c;" k="61" />
<hkern u1="&#xe0;" u2="&#x2018;" k="61" />
<hkern u1="&#xe0;" u2="y" k="10" />
<hkern u1="&#xe0;" u2="v" k="10" />
<hkern u1="&#xe0;" u2="t" k="20" />
<hkern u1="&#xe0;" u2="\" k="20" />
<hkern u1="&#xe0;" u2="&#x3f;" k="20" />
<hkern u1="&#xe1;" u2="&#x2122;" k="41" />
<hkern u1="&#xe1;" u2="&#x201c;" k="61" />
<hkern u1="&#xe1;" u2="&#x2018;" k="61" />
<hkern u1="&#xe1;" u2="y" k="10" />
<hkern u1="&#xe1;" u2="v" k="10" />
<hkern u1="&#xe1;" u2="t" k="20" />
<hkern u1="&#xe1;" u2="\" k="20" />
<hkern u1="&#xe1;" u2="&#x3f;" k="20" />
<hkern u1="&#xe2;" u2="&#x2122;" k="41" />
<hkern u1="&#xe2;" u2="&#x201c;" k="61" />
<hkern u1="&#xe2;" u2="&#x2018;" k="61" />
<hkern u1="&#xe2;" u2="y" k="10" />
<hkern u1="&#xe2;" u2="v" k="10" />
<hkern u1="&#xe2;" u2="t" k="20" />
<hkern u1="&#xe2;" u2="\" k="20" />
<hkern u1="&#xe2;" u2="&#x3f;" k="20" />
<hkern u1="&#xe4;" u2="&#x2122;" k="41" />
<hkern u1="&#xe4;" u2="&#x201c;" k="61" />
<hkern u1="&#xe4;" u2="&#x2018;" k="61" />
<hkern u1="&#xe4;" u2="y" k="10" />
<hkern u1="&#xe4;" u2="v" k="10" />
<hkern u1="&#xe4;" u2="t" k="20" />
<hkern u1="&#xe4;" u2="\" k="20" />
<hkern u1="&#xe4;" u2="&#x3f;" k="20" />
<hkern u1="&#xe6;" u2="y" k="20" />
<hkern u1="&#xe6;" u2="x" k="31" />
<hkern u1="&#xe6;" u2="v" k="20" />
<hkern u1="&#xe8;" u2="y" k="20" />
<hkern u1="&#xe8;" u2="x" k="31" />
<hkern u1="&#xe8;" u2="v" k="20" />
<hkern u1="&#xe9;" u2="y" k="20" />
<hkern u1="&#xe9;" u2="x" k="31" />
<hkern u1="&#xe9;" u2="v" k="20" />
<hkern u1="&#xea;" u2="y" k="20" />
<hkern u1="&#xea;" u2="x" k="31" />
<hkern u1="&#xea;" u2="v" k="20" />
<hkern u1="&#xeb;" u2="y" k="20" />
<hkern u1="&#xeb;" u2="x" k="31" />
<hkern u1="&#xeb;" u2="v" k="20" />
<hkern u1="&#xf1;" u2="&#x2122;" k="41" />
<hkern u1="&#xf1;" u2="&#x201c;" k="61" />
<hkern u1="&#xf1;" u2="&#x2018;" k="61" />
<hkern u1="&#xf1;" u2="y" k="10" />
<hkern u1="&#xf1;" u2="v" k="10" />
<hkern u1="&#xf1;" u2="t" k="20" />
<hkern u1="&#xf1;" u2="\" k="20" />
<hkern u1="&#xf1;" u2="&#x3f;" k="20" />
<hkern u1="&#xf3;" u2="&#x2122;" k="61" />
<hkern u1="&#xf3;" u2="&#x2026;" k="41" />
<hkern u1="&#xf3;" u2="&#x201e;" k="41" />
<hkern u1="&#xf3;" u2="&#x201c;" k="82" />
<hkern u1="&#xf3;" u2="&#x201a;" k="41" />
<hkern u1="&#xf3;" u2="&#x2018;" k="82" />
<hkern u1="&#xf3;" u2="z" k="31" />
<hkern u1="&#xf3;" u2="y" k="20" />
<hkern u1="&#xf3;" u2="x" k="31" />
<hkern u1="&#xf3;" u2="v" k="20" />
<hkern u1="&#xf3;" u2="\" k="20" />
<hkern u1="&#xf3;" u2="&#x3f;" k="41" />
<hkern u1="&#xf3;" u2="&#x3b;" k="20" />
<hkern u1="&#xf3;" u2="&#x3a;" k="20" />
<hkern u1="&#xf3;" u2="&#x2f;" k="20" />
<hkern u1="&#xf3;" u2="&#x2e;" k="41" />
<hkern u1="&#xf3;" u2="&#x2c;" k="41" />
<hkern u1="&#xf4;" u2="&#x2122;" k="61" />
<hkern u1="&#xf4;" u2="&#x2026;" k="41" />
<hkern u1="&#xf4;" u2="&#x201e;" k="41" />
<hkern u1="&#xf4;" u2="&#x201c;" k="82" />
<hkern u1="&#xf4;" u2="&#x201a;" k="41" />
<hkern u1="&#xf4;" u2="&#x2018;" k="82" />
<hkern u1="&#xf4;" u2="z" k="31" />
<hkern u1="&#xf4;" u2="y" k="20" />
<hkern u1="&#xf4;" u2="x" k="31" />
<hkern u1="&#xf4;" u2="v" k="20" />
<hkern u1="&#xf4;" u2="\" k="20" />
<hkern u1="&#xf4;" u2="&#x3f;" k="41" />
<hkern u1="&#xf4;" u2="&#x3b;" k="20" />
<hkern u1="&#xf4;" u2="&#x3a;" k="20" />
<hkern u1="&#xf4;" u2="&#x2f;" k="20" />
<hkern u1="&#xf4;" u2="&#x2e;" k="41" />
<hkern u1="&#xf4;" u2="&#x2c;" k="41" />
<hkern u1="&#xf5;" u2="&#x2122;" k="61" />
<hkern u1="&#xf5;" u2="&#x2026;" k="41" />
<hkern u1="&#xf5;" u2="&#x201e;" k="41" />
<hkern u1="&#xf5;" u2="&#x201c;" k="82" />
<hkern u1="&#xf5;" u2="&#x201a;" k="41" />
<hkern u1="&#xf5;" u2="&#x2018;" k="82" />
<hkern u1="&#xf5;" u2="z" k="31" />
<hkern u1="&#xf5;" u2="y" k="20" />
<hkern u1="&#xf5;" u2="x" k="31" />
<hkern u1="&#xf5;" u2="v" k="20" />
<hkern u1="&#xf5;" u2="\" k="20" />
<hkern u1="&#xf5;" u2="&#x3f;" k="41" />
<hkern u1="&#xf5;" u2="&#x3b;" k="20" />
<hkern u1="&#xf5;" u2="&#x3a;" k="20" />
<hkern u1="&#xf5;" u2="&#x2f;" k="20" />
<hkern u1="&#xf5;" u2="&#x2e;" k="41" />
<hkern u1="&#xf5;" u2="&#x2c;" k="41" />
<hkern u1="&#xf6;" u2="&#x2122;" k="61" />
<hkern u1="&#xf6;" u2="&#x2026;" k="41" />
<hkern u1="&#xf6;" u2="&#x201e;" k="41" />
<hkern u1="&#xf6;" u2="&#x201c;" k="82" />
<hkern u1="&#xf6;" u2="&#x201a;" k="41" />
<hkern u1="&#xf6;" u2="&#x2018;" k="82" />
<hkern u1="&#xf6;" u2="z" k="31" />
<hkern u1="&#xf6;" u2="y" k="20" />
<hkern u1="&#xf6;" u2="x" k="31" />
<hkern u1="&#xf6;" u2="v" k="20" />
<hkern u1="&#xf6;" u2="\" k="20" />
<hkern u1="&#xf6;" u2="&#x3f;" k="41" />
<hkern u1="&#xf6;" u2="&#x3b;" k="20" />
<hkern u1="&#xf6;" u2="&#x3a;" k="20" />
<hkern u1="&#xf6;" u2="&#x2f;" k="20" />
<hkern u1="&#xf6;" u2="&#x2e;" k="41" />
<hkern u1="&#xf6;" u2="&#x2c;" k="41" />
<hkern u1="&#xf8;" u2="&#x2122;" k="61" />
<hkern u1="&#xf8;" u2="&#x2026;" k="41" />
<hkern u1="&#xf8;" u2="&#x201e;" k="41" />
<hkern u1="&#xf8;" u2="&#x201c;" k="82" />
<hkern u1="&#xf8;" u2="&#x201a;" k="41" />
<hkern u1="&#xf8;" u2="&#x2018;" k="82" />
<hkern u1="&#xf8;" u2="z" k="31" />
<hkern u1="&#xf8;" u2="y" k="20" />
<hkern u1="&#xf8;" u2="x" k="31" />
<hkern u1="&#xf8;" u2="v" k="20" />
<hkern u1="&#xf8;" u2="\" k="20" />
<hkern u1="&#xf8;" u2="&#x3f;" k="41" />
<hkern u1="&#xf8;" u2="&#x3b;" k="20" />
<hkern u1="&#xf8;" u2="&#x3a;" k="20" />
<hkern u1="&#xf8;" u2="&#x2f;" k="20" />
<hkern u1="&#xf8;" u2="&#x2e;" k="41" />
<hkern u1="&#xf8;" u2="&#x2c;" k="41" />
<hkern u1="&#x152;" u2="&#x153;" k="51" />
<hkern u1="&#x152;" u2="&#xe7;" k="51" />
<hkern u1="&#x152;" u2="&#xe6;" k="51" />
<hkern u1="&#x152;" u2="&#xae;" k="41" />
<hkern u1="&#x152;" u2="&#xa9;" k="41" />
<hkern u1="&#x152;" u2="y" k="41" />
<hkern u1="&#x152;" u2="v" k="41" />
<hkern u1="&#x152;" u2="q" k="51" />
<hkern u1="&#x152;" u2="o" k="51" />
<hkern u1="&#x152;" u2="g" k="10" />
<hkern u1="&#x152;" u2="f" k="20" />
<hkern u1="&#x152;" u2="e" k="51" />
<hkern u1="&#x152;" u2="d" k="51" />
<hkern u1="&#x152;" u2="c" k="51" />
<hkern u1="&#x152;" u2="a" k="51" />
<hkern u1="&#x152;" u2="T" k="-31" />
<hkern u1="&#x152;" u2="&#x40;" k="41" />
<hkern u1="&#x153;" u2="y" k="20" />
<hkern u1="&#x153;" u2="x" k="31" />
<hkern u1="&#x153;" u2="v" k="20" />
<hkern u1="&#x178;" u2="&#x203a;" k="82" />
<hkern u1="&#x178;" u2="&#x2039;" k="123" />
<hkern u1="&#x178;" u2="&#x2026;" k="102" />
<hkern u1="&#x178;" u2="&#x201e;" k="102" />
<hkern u1="&#x178;" u2="&#x201a;" k="102" />
<hkern u1="&#x178;" u2="&#x2014;" k="102" />
<hkern u1="&#x178;" u2="&#x2013;" k="102" />
<hkern u1="&#x178;" u2="&#x153;" k="113" />
<hkern u1="&#x178;" u2="&#x152;" k="51" />
<hkern u1="&#x178;" u2="&#xe7;" k="113" />
<hkern u1="&#x178;" u2="&#xe6;" k="113" />
<hkern u1="&#x178;" u2="&#xd8;" k="51" />
<hkern u1="&#x178;" u2="&#xd6;" k="51" />
<hkern u1="&#x178;" u2="&#xd5;" k="51" />
<hkern u1="&#x178;" u2="&#xd4;" k="51" />
<hkern u1="&#x178;" u2="&#xd3;" k="51" />
<hkern u1="&#x178;" u2="&#xd2;" k="51" />
<hkern u1="&#x178;" u2="&#xc6;" k="102" />
<hkern u1="&#x178;" u2="&#xc5;" k="102" />
<hkern u1="&#x178;" u2="&#xc4;" k="102" />
<hkern u1="&#x178;" u2="&#xc3;" k="102" />
<hkern u1="&#x178;" u2="&#xc2;" k="102" />
<hkern u1="&#x178;" u2="&#xc1;" k="102" />
<hkern u1="&#x178;" u2="&#xc0;" k="102" />
<hkern u1="&#x178;" u2="&#xbb;" k="82" />
<hkern u1="&#x178;" u2="&#xae;" k="61" />
<hkern u1="&#x178;" u2="&#xab;" k="123" />
<hkern u1="&#x178;" u2="&#xa9;" k="61" />
<hkern u1="&#x178;" u2="&#x7d;" k="-102" />
<hkern u1="&#x178;" u2="z" k="61" />
<hkern u1="&#x178;" u2="y" k="41" />
<hkern u1="&#x178;" u2="x" k="41" />
<hkern u1="&#x178;" u2="w" k="20" />
<hkern u1="&#x178;" u2="v" k="41" />
<hkern u1="&#x178;" u2="u" k="82" />
<hkern u1="&#x178;" u2="t" k="41" />
<hkern u1="&#x178;" u2="s" k="102" />
<hkern u1="&#x178;" u2="r" k="82" />
<hkern u1="&#x178;" u2="q" k="113" />
<hkern u1="&#x178;" u2="p" k="82" />
<hkern u1="&#x178;" u2="o" k="113" />
<hkern u1="&#x178;" u2="n" k="82" />
<hkern u1="&#x178;" u2="m" k="82" />
<hkern u1="&#x178;" u2="g" k="82" />
<hkern u1="&#x178;" u2="f" k="20" />
<hkern u1="&#x178;" u2="e" k="113" />
<hkern u1="&#x178;" u2="d" k="113" />
<hkern u1="&#x178;" u2="c" k="113" />
<hkern u1="&#x178;" u2="a" k="113" />
<hkern u1="&#x178;" u2="]" k="-102" />
<hkern u1="&#x178;" u2="X" k="-41" />
<hkern u1="&#x178;" u2="V" k="-61" />
<hkern u1="&#x178;" u2="T" k="-41" />
<hkern u1="&#x178;" u2="S" k="-20" />
<hkern u1="&#x178;" u2="Q" k="51" />
<hkern u1="&#x178;" u2="O" k="51" />
<hkern u1="&#x178;" u2="J" k="143" />
<hkern u1="&#x178;" u2="G" k="51" />
<hkern u1="&#x178;" u2="C" k="51" />
<hkern u1="&#x178;" u2="A" k="102" />
<hkern u1="&#x178;" u2="&#x40;" k="61" />
<hkern u1="&#x178;" u2="&#x3b;" k="61" />
<hkern u1="&#x178;" u2="&#x3a;" k="61" />
<hkern u1="&#x178;" u2="&#x2e;" k="102" />
<hkern u1="&#x178;" u2="&#x2d;" k="102" />
<hkern u1="&#x178;" u2="&#x2c;" k="102" />
<hkern u1="&#x178;" u2="&#x29;" k="-102" />
<hkern u1="&#x178;" u2="&#x26;" k="61" />
<hkern u1="&#x2013;" u2="&#x178;" k="102" />
<hkern u1="&#x2013;" u2="&#x153;" k="20" />
<hkern u1="&#x2013;" u2="&#xe7;" k="20" />
<hkern u1="&#x2013;" u2="&#xe6;" k="20" />
<hkern u1="&#x2013;" u2="&#xdd;" k="102" />
<hkern u1="&#x2013;" u2="&#xc6;" k="-20" />
<hkern u1="&#x2013;" u2="&#xc5;" k="-20" />
<hkern u1="&#x2013;" u2="&#xc4;" k="-20" />
<hkern u1="&#x2013;" u2="&#xc3;" k="-20" />
<hkern u1="&#x2013;" u2="&#xc2;" k="-20" />
<hkern u1="&#x2013;" u2="&#xc1;" k="-20" />
<hkern u1="&#x2013;" u2="&#xc0;" k="-20" />
<hkern u1="&#x2013;" u2="z" k="61" />
<hkern u1="&#x2013;" u2="y" k="20" />
<hkern u1="&#x2013;" u2="x" k="61" />
<hkern u1="&#x2013;" u2="v" k="20" />
<hkern u1="&#x2013;" u2="q" k="20" />
<hkern u1="&#x2013;" u2="o" k="20" />
<hkern u1="&#x2013;" u2="e" k="20" />
<hkern u1="&#x2013;" u2="d" k="20" />
<hkern u1="&#x2013;" u2="c" k="20" />
<hkern u1="&#x2013;" u2="a" k="20" />
<hkern u1="&#x2013;" u2="Z" k="20" />
<hkern u1="&#x2013;" u2="Y" k="102" />
<hkern u1="&#x2013;" u2="X" k="61" />
<hkern u1="&#x2013;" u2="W" k="82" />
<hkern u1="&#x2013;" u2="V" k="102" />
<hkern u1="&#x2013;" u2="T" k="143" />
<hkern u1="&#x2013;" u2="A" k="-20" />
<hkern u1="&#x2013;" u2="&#x37;" k="61" />
<hkern u1="&#x2013;" u2="&#x31;" k="41" />
<hkern u1="&#x2014;" u2="&#x178;" k="102" />
<hkern u1="&#x2014;" u2="&#x153;" k="20" />
<hkern u1="&#x2014;" u2="&#xe7;" k="20" />
<hkern u1="&#x2014;" u2="&#xe6;" k="20" />
<hkern u1="&#x2014;" u2="&#xdd;" k="102" />
<hkern u1="&#x2014;" u2="&#xc6;" k="-20" />
<hkern u1="&#x2014;" u2="&#xc5;" k="-20" />
<hkern u1="&#x2014;" u2="&#xc4;" k="-20" />
<hkern u1="&#x2014;" u2="&#xc3;" k="-20" />
<hkern u1="&#x2014;" u2="&#xc2;" k="-20" />
<hkern u1="&#x2014;" u2="&#xc1;" k="-20" />
<hkern u1="&#x2014;" u2="&#xc0;" k="-20" />
<hkern u1="&#x2014;" u2="z" k="61" />
<hkern u1="&#x2014;" u2="y" k="20" />
<hkern u1="&#x2014;" u2="x" k="61" />
<hkern u1="&#x2014;" u2="v" k="20" />
<hkern u1="&#x2014;" u2="q" k="20" />
<hkern u1="&#x2014;" u2="o" k="20" />
<hkern u1="&#x2014;" u2="e" k="20" />
<hkern u1="&#x2014;" u2="d" k="20" />
<hkern u1="&#x2014;" u2="c" k="20" />
<hkern u1="&#x2014;" u2="a" k="20" />
<hkern u1="&#x2014;" u2="Z" k="20" />
<hkern u1="&#x2014;" u2="Y" k="102" />
<hkern u1="&#x2014;" u2="X" k="61" />
<hkern u1="&#x2014;" u2="W" k="82" />
<hkern u1="&#x2014;" u2="V" k="102" />
<hkern u1="&#x2014;" u2="T" k="143" />
<hkern u1="&#x2014;" u2="A" k="-20" />
<hkern u1="&#x2014;" u2="&#x37;" k="61" />
<hkern u1="&#x2014;" u2="&#x31;" k="41" />
<hkern u1="&#x2018;" u2="&#x178;" k="-61" />
<hkern u1="&#x2018;" u2="&#x153;" k="61" />
<hkern u1="&#x2018;" u2="&#xe7;" k="61" />
<hkern u1="&#x2018;" u2="&#xe6;" k="61" />
<hkern u1="&#x2018;" u2="&#xdd;" k="-61" />
<hkern u1="&#x2018;" u2="&#xc6;" k="82" />
<hkern u1="&#x2018;" u2="&#xc5;" k="82" />
<hkern u1="&#x2018;" u2="&#xc4;" k="82" />
<hkern u1="&#x2018;" u2="&#xc3;" k="82" />
<hkern u1="&#x2018;" u2="&#xc2;" k="82" />
<hkern u1="&#x2018;" u2="&#xc1;" k="82" />
<hkern u1="&#x2018;" u2="&#xc0;" k="82" />
<hkern u1="&#x2018;" u2="u" k="20" />
<hkern u1="&#x2018;" u2="s" k="61" />
<hkern u1="&#x2018;" u2="r" k="20" />
<hkern u1="&#x2018;" u2="q" k="61" />
<hkern u1="&#x2018;" u2="p" k="20" />
<hkern u1="&#x2018;" u2="o" k="61" />
<hkern u1="&#x2018;" u2="n" k="20" />
<hkern u1="&#x2018;" u2="m" k="20" />
<hkern u1="&#x2018;" u2="g" k="82" />
<hkern u1="&#x2018;" u2="e" k="61" />
<hkern u1="&#x2018;" u2="d" k="61" />
<hkern u1="&#x2018;" u2="c" k="61" />
<hkern u1="&#x2018;" u2="a" k="61" />
<hkern u1="&#x2018;" u2="Y" k="-61" />
<hkern u1="&#x2018;" u2="X" k="-41" />
<hkern u1="&#x2018;" u2="W" k="-41" />
<hkern u1="&#x2018;" u2="V" k="-41" />
<hkern u1="&#x2018;" u2="T" k="-20" />
<hkern u1="&#x2018;" u2="J" k="225" />
<hkern u1="&#x2018;" u2="A" k="82" />
<hkern u1="&#x2019;" u2="&#x153;" k="102" />
<hkern u1="&#x2019;" u2="&#x152;" k="82" />
<hkern u1="&#x2019;" u2="&#xe7;" k="102" />
<hkern u1="&#x2019;" u2="&#xe6;" k="102" />
<hkern u1="&#x2019;" u2="&#xd8;" k="82" />
<hkern u1="&#x2019;" u2="&#xd6;" k="82" />
<hkern u1="&#x2019;" u2="&#xd5;" k="82" />
<hkern u1="&#x2019;" u2="&#xd4;" k="82" />
<hkern u1="&#x2019;" u2="&#xd3;" k="82" />
<hkern u1="&#x2019;" u2="&#xd2;" k="82" />
<hkern u1="&#x2019;" u2="q" k="102" />
<hkern u1="&#x2019;" u2="o" k="102" />
<hkern u1="&#x2019;" u2="e" k="102" />
<hkern u1="&#x2019;" u2="d" k="102" />
<hkern u1="&#x2019;" u2="c" k="102" />
<hkern u1="&#x2019;" u2="a" k="102" />
<hkern u1="&#x2019;" u2="S" k="20" />
<hkern u1="&#x2019;" u2="Q" k="82" />
<hkern u1="&#x2019;" u2="O" k="82" />
<hkern u1="&#x2019;" u2="J" k="246" />
<hkern u1="&#x2019;" u2="G" k="82" />
<hkern u1="&#x2019;" u2="C" k="82" />
<hkern u1="&#x201a;" u2="&#x178;" k="102" />
<hkern u1="&#x201a;" u2="&#x153;" k="41" />
<hkern u1="&#x201a;" u2="&#x152;" k="82" />
<hkern u1="&#x201a;" u2="&#xe7;" k="41" />
<hkern u1="&#x201a;" u2="&#xe6;" k="41" />
<hkern u1="&#x201a;" u2="&#xdd;" k="102" />
<hkern u1="&#x201a;" u2="&#xdc;" k="20" />
<hkern u1="&#x201a;" u2="&#xdb;" k="20" />
<hkern u1="&#x201a;" u2="&#xda;" k="20" />
<hkern u1="&#x201a;" u2="&#xd9;" k="20" />
<hkern u1="&#x201a;" u2="&#xd8;" k="82" />
<hkern u1="&#x201a;" u2="&#xd6;" k="82" />
<hkern u1="&#x201a;" u2="&#xd5;" k="82" />
<hkern u1="&#x201a;" u2="&#xd4;" k="82" />
<hkern u1="&#x201a;" u2="&#xd3;" k="82" />
<hkern u1="&#x201a;" u2="&#xd2;" k="82" />
<hkern u1="&#x201a;" u2="&#xc6;" k="-61" />
<hkern u1="&#x201a;" u2="&#xc5;" k="-61" />
<hkern u1="&#x201a;" u2="&#xc4;" k="-61" />
<hkern u1="&#x201a;" u2="&#xc3;" k="-61" />
<hkern u1="&#x201a;" u2="&#xc2;" k="-61" />
<hkern u1="&#x201a;" u2="&#xc1;" k="-61" />
<hkern u1="&#x201a;" u2="&#xc0;" k="-61" />
<hkern u1="&#x201a;" u2="y" k="61" />
<hkern u1="&#x201a;" u2="w" k="20" />
<hkern u1="&#x201a;" u2="v" k="61" />
<hkern u1="&#x201a;" u2="u" k="41" />
<hkern u1="&#x201a;" u2="t" k="82" />
<hkern u1="&#x201a;" u2="r" k="41" />
<hkern u1="&#x201a;" u2="q" k="41" />
<hkern u1="&#x201a;" u2="p" k="41" />
<hkern u1="&#x201a;" u2="o" k="41" />
<hkern u1="&#x201a;" u2="n" k="41" />
<hkern u1="&#x201a;" u2="m" k="41" />
<hkern u1="&#x201a;" u2="j" k="-61" />
<hkern u1="&#x201a;" u2="e" k="41" />
<hkern u1="&#x201a;" u2="d" k="41" />
<hkern u1="&#x201a;" u2="c" k="41" />
<hkern u1="&#x201a;" u2="a" k="41" />
<hkern u1="&#x201a;" u2="Y" k="102" />
<hkern u1="&#x201a;" u2="W" k="41" />
<hkern u1="&#x201a;" u2="V" k="123" />
<hkern u1="&#x201a;" u2="U" k="20" />
<hkern u1="&#x201a;" u2="T" k="123" />
<hkern u1="&#x201a;" u2="Q" k="82" />
<hkern u1="&#x201a;" u2="O" k="82" />
<hkern u1="&#x201a;" u2="G" k="82" />
<hkern u1="&#x201a;" u2="C" k="82" />
<hkern u1="&#x201a;" u2="A" k="-61" />
<hkern u1="&#x201a;" u2="&#x39;" k="20" />
<hkern u1="&#x201a;" u2="&#x38;" k="31" />
<hkern u1="&#x201a;" u2="&#x36;" k="61" />
<hkern u1="&#x201a;" u2="&#x34;" k="102" />
<hkern u1="&#x201a;" u2="&#x31;" k="123" />
<hkern u1="&#x201a;" u2="&#x30;" k="82" />
<hkern u1="&#x201c;" u2="&#x178;" k="-61" />
<hkern u1="&#x201c;" u2="&#x153;" k="61" />
<hkern u1="&#x201c;" u2="&#xe7;" k="61" />
<hkern u1="&#x201c;" u2="&#xe6;" k="61" />
<hkern u1="&#x201c;" u2="&#xdd;" k="-61" />
<hkern u1="&#x201c;" u2="&#xc6;" k="82" />
<hkern u1="&#x201c;" u2="&#xc5;" k="82" />
<hkern u1="&#x201c;" u2="&#xc4;" k="82" />
<hkern u1="&#x201c;" u2="&#xc3;" k="82" />
<hkern u1="&#x201c;" u2="&#xc2;" k="82" />
<hkern u1="&#x201c;" u2="&#xc1;" k="82" />
<hkern u1="&#x201c;" u2="&#xc0;" k="82" />
<hkern u1="&#x201c;" u2="u" k="20" />
<hkern u1="&#x201c;" u2="s" k="61" />
<hkern u1="&#x201c;" u2="r" k="20" />
<hkern u1="&#x201c;" u2="q" k="61" />
<hkern u1="&#x201c;" u2="p" k="20" />
<hkern u1="&#x201c;" u2="o" k="61" />
<hkern u1="&#x201c;" u2="n" k="20" />
<hkern u1="&#x201c;" u2="m" k="20" />
<hkern u1="&#x201c;" u2="g" k="82" />
<hkern u1="&#x201c;" u2="e" k="61" />
<hkern u1="&#x201c;" u2="d" k="61" />
<hkern u1="&#x201c;" u2="c" k="61" />
<hkern u1="&#x201c;" u2="a" k="61" />
<hkern u1="&#x201c;" u2="Y" k="-61" />
<hkern u1="&#x201c;" u2="X" k="-41" />
<hkern u1="&#x201c;" u2="W" k="-41" />
<hkern u1="&#x201c;" u2="V" k="-41" />
<hkern u1="&#x201c;" u2="T" k="-20" />
<hkern u1="&#x201c;" u2="J" k="225" />
<hkern u1="&#x201c;" u2="A" k="82" />
<hkern u1="&#x201d;" u2="&#x153;" k="102" />
<hkern u1="&#x201d;" u2="&#x152;" k="82" />
<hkern u1="&#x201d;" u2="&#xe7;" k="102" />
<hkern u1="&#x201d;" u2="&#xe6;" k="102" />
<hkern u1="&#x201d;" u2="&#xd8;" k="82" />
<hkern u1="&#x201d;" u2="&#xd6;" k="82" />
<hkern u1="&#x201d;" u2="&#xd5;" k="82" />
<hkern u1="&#x201d;" u2="&#xd4;" k="82" />
<hkern u1="&#x201d;" u2="&#xd3;" k="82" />
<hkern u1="&#x201d;" u2="&#xd2;" k="82" />
<hkern u1="&#x201d;" u2="q" k="102" />
<hkern u1="&#x201d;" u2="o" k="102" />
<hkern u1="&#x201d;" u2="e" k="102" />
<hkern u1="&#x201d;" u2="d" k="102" />
<hkern u1="&#x201d;" u2="c" k="102" />
<hkern u1="&#x201d;" u2="a" k="102" />
<hkern u1="&#x201d;" u2="S" k="20" />
<hkern u1="&#x201d;" u2="Q" k="82" />
<hkern u1="&#x201d;" u2="O" k="82" />
<hkern u1="&#x201d;" u2="J" k="246" />
<hkern u1="&#x201d;" u2="G" k="82" />
<hkern u1="&#x201d;" u2="C" k="82" />
<hkern u1="&#x201e;" u2="&#x178;" k="102" />
<hkern u1="&#x201e;" u2="&#x153;" k="41" />
<hkern u1="&#x201e;" u2="&#x152;" k="82" />
<hkern u1="&#x201e;" u2="&#xe7;" k="41" />
<hkern u1="&#x201e;" u2="&#xe6;" k="41" />
<hkern u1="&#x201e;" u2="&#xdd;" k="102" />
<hkern u1="&#x201e;" u2="&#xdc;" k="20" />
<hkern u1="&#x201e;" u2="&#xdb;" k="20" />
<hkern u1="&#x201e;" u2="&#xda;" k="20" />
<hkern u1="&#x201e;" u2="&#xd9;" k="20" />
<hkern u1="&#x201e;" u2="&#xd8;" k="82" />
<hkern u1="&#x201e;" u2="&#xd6;" k="82" />
<hkern u1="&#x201e;" u2="&#xd5;" k="82" />
<hkern u1="&#x201e;" u2="&#xd4;" k="82" />
<hkern u1="&#x201e;" u2="&#xd3;" k="82" />
<hkern u1="&#x201e;" u2="&#xd2;" k="82" />
<hkern u1="&#x201e;" u2="&#xc6;" k="-61" />
<hkern u1="&#x201e;" u2="&#xc5;" k="-61" />
<hkern u1="&#x201e;" u2="&#xc4;" k="-61" />
<hkern u1="&#x201e;" u2="&#xc3;" k="-61" />
<hkern u1="&#x201e;" u2="&#xc2;" k="-61" />
<hkern u1="&#x201e;" u2="&#xc1;" k="-61" />
<hkern u1="&#x201e;" u2="&#xc0;" k="-61" />
<hkern u1="&#x201e;" u2="y" k="61" />
<hkern u1="&#x201e;" u2="w" k="20" />
<hkern u1="&#x201e;" u2="v" k="61" />
<hkern u1="&#x201e;" u2="u" k="41" />
<hkern u1="&#x201e;" u2="t" k="82" />
<hkern u1="&#x201e;" u2="r" k="41" />
<hkern u1="&#x201e;" u2="q" k="41" />
<hkern u1="&#x201e;" u2="p" k="41" />
<hkern u1="&#x201e;" u2="o" k="41" />
<hkern u1="&#x201e;" u2="n" k="41" />
<hkern u1="&#x201e;" u2="m" k="41" />
<hkern u1="&#x201e;" u2="j" k="-61" />
<hkern u1="&#x201e;" u2="e" k="41" />
<hkern u1="&#x201e;" u2="d" k="41" />
<hkern u1="&#x201e;" u2="c" k="41" />
<hkern u1="&#x201e;" u2="a" k="41" />
<hkern u1="&#x201e;" u2="Y" k="102" />
<hkern u1="&#x201e;" u2="W" k="41" />
<hkern u1="&#x201e;" u2="V" k="123" />
<hkern u1="&#x201e;" u2="U" k="20" />
<hkern u1="&#x201e;" u2="T" k="123" />
<hkern u1="&#x201e;" u2="Q" k="82" />
<hkern u1="&#x201e;" u2="O" k="82" />
<hkern u1="&#x201e;" u2="G" k="82" />
<hkern u1="&#x201e;" u2="C" k="82" />
<hkern u1="&#x201e;" u2="A" k="-61" />
<hkern u1="&#x201e;" u2="&#x39;" k="20" />
<hkern u1="&#x201e;" u2="&#x38;" k="31" />
<hkern u1="&#x201e;" u2="&#x36;" k="61" />
<hkern u1="&#x201e;" u2="&#x34;" k="102" />
<hkern u1="&#x201e;" u2="&#x31;" k="123" />
<hkern u1="&#x201e;" u2="&#x30;" k="82" />
<hkern u1="&#x2026;" u2="&#x178;" k="102" />
<hkern u1="&#x2026;" u2="&#x153;" k="41" />
<hkern u1="&#x2026;" u2="&#x152;" k="82" />
<hkern u1="&#x2026;" u2="&#xe7;" k="41" />
<hkern u1="&#x2026;" u2="&#xe6;" k="41" />
<hkern u1="&#x2026;" u2="&#xdd;" k="102" />
<hkern u1="&#x2026;" u2="&#xdc;" k="20" />
<hkern u1="&#x2026;" u2="&#xdb;" k="20" />
<hkern u1="&#x2026;" u2="&#xda;" k="20" />
<hkern u1="&#x2026;" u2="&#xd9;" k="20" />
<hkern u1="&#x2026;" u2="&#xd8;" k="82" />
<hkern u1="&#x2026;" u2="&#xd6;" k="82" />
<hkern u1="&#x2026;" u2="&#xd5;" k="82" />
<hkern u1="&#x2026;" u2="&#xd4;" k="82" />
<hkern u1="&#x2026;" u2="&#xd3;" k="82" />
<hkern u1="&#x2026;" u2="&#xd2;" k="82" />
<hkern u1="&#x2026;" u2="&#xc6;" k="-61" />
<hkern u1="&#x2026;" u2="&#xc5;" k="-61" />
<hkern u1="&#x2026;" u2="&#xc4;" k="-61" />
<hkern u1="&#x2026;" u2="&#xc3;" k="-61" />
<hkern u1="&#x2026;" u2="&#xc2;" k="-61" />
<hkern u1="&#x2026;" u2="&#xc1;" k="-61" />
<hkern u1="&#x2026;" u2="&#xc0;" k="-61" />
<hkern u1="&#x2026;" u2="y" k="61" />
<hkern u1="&#x2026;" u2="w" k="20" />
<hkern u1="&#x2026;" u2="v" k="61" />
<hkern u1="&#x2026;" u2="u" k="41" />
<hkern u1="&#x2026;" u2="t" k="82" />
<hkern u1="&#x2026;" u2="r" k="41" />
<hkern u1="&#x2026;" u2="q" k="41" />
<hkern u1="&#x2026;" u2="p" k="41" />
<hkern u1="&#x2026;" u2="o" k="41" />
<hkern u1="&#x2026;" u2="n" k="41" />
<hkern u1="&#x2026;" u2="m" k="41" />
<hkern u1="&#x2026;" u2="e" k="41" />
<hkern u1="&#x2026;" u2="d" k="41" />
<hkern u1="&#x2026;" u2="c" k="41" />
<hkern u1="&#x2026;" u2="a" k="41" />
<hkern u1="&#x2026;" u2="Y" k="102" />
<hkern u1="&#x2026;" u2="W" k="41" />
<hkern u1="&#x2026;" u2="V" k="123" />
<hkern u1="&#x2026;" u2="U" k="20" />
<hkern u1="&#x2026;" u2="T" k="123" />
<hkern u1="&#x2026;" u2="Q" k="82" />
<hkern u1="&#x2026;" u2="O" k="82" />
<hkern u1="&#x2026;" u2="G" k="82" />
<hkern u1="&#x2026;" u2="C" k="82" />
<hkern u1="&#x2026;" u2="A" k="-61" />
<hkern u1="&#x2026;" u2="&#x39;" k="20" />
<hkern u1="&#x2026;" u2="&#x38;" k="31" />
<hkern u1="&#x2026;" u2="&#x36;" k="61" />
<hkern u1="&#x2026;" u2="&#x34;" k="102" />
<hkern u1="&#x2026;" u2="&#x31;" k="123" />
<hkern u1="&#x2026;" u2="&#x30;" k="82" />
<hkern u1="&#x2039;" u2="&#x178;" k="82" />
<hkern u1="&#x2039;" u2="&#xdd;" k="82" />
<hkern u1="&#x2039;" u2="Y" k="82" />
<hkern u1="&#x2039;" u2="W" k="41" />
<hkern u1="&#x2039;" u2="V" k="41" />
<hkern u1="&#x2039;" u2="T" k="82" />
<hkern u1="&#x203a;" u2="&#x178;" k="102" />
<hkern u1="&#x203a;" u2="&#xdd;" k="102" />
<hkern u1="&#x203a;" u2="z" k="102" />
<hkern u1="&#x203a;" u2="x" k="102" />
<hkern u1="&#x203a;" u2="Y" k="102" />
<hkern u1="&#x203a;" u2="W" k="82" />
<hkern u1="&#x203a;" u2="V" k="102" />
<hkern u1="&#x203a;" u2="T" k="225" />
</font>
</defs></svg> 