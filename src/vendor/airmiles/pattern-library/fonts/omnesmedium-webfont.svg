<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="omnesmedium" horiz-adv-x="1517" >
<font-face units-per-em="2048" ascent="1434" descent="-614" />
<missing-glyph horiz-adv-x="348" />
<glyph unicode="&#xfb01;" horiz-adv-x="1191" d="M16 897v14q0 38 21 56t69 18h115v74q0 182 81.5 272t232.5 90q108 0 164.5 -38t56.5 -95q0 -16 -4.5 -31t-11.5 -25.5t-14 -18.5t-12.5 -11t-6.5 -2q-23 23 -64 40t-84 17q-73 0 -107 -48t-34 -152v-72h166q48 0 69 -19t21 -57v-14q0 -38 -21 -57t-69 -19h-162v-731 q0 -53 -22.5 -74.5t-65.5 -21.5h-21q-92 0 -92 96v731h-115q-48 0 -69 19.5t-21 58.5zM838 1309q0 55 31.5 84.5t88.5 29.5q59 0 90 -29.5t31 -84.5t-31.5 -84t-89.5 -29q-57 0 -88.5 29.5t-31.5 83.5zM856 88v809q0 98 90 98h23q43 0 66.5 -22t23.5 -76v-809q0 -96 -90 -96 h-21q-92 0 -92 96z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1191" d="M16 897v14q0 38 21 56t69 18h115v74q0 182 81.5 272t232.5 90q108 0 164.5 -38t56.5 -95q0 -16 -4.5 -31t-11.5 -25.5t-14 -18.5t-12.5 -11t-6.5 -2q-23 23 -64 40t-84 17q-73 0 -107 -48t-34 -152v-72h166q48 0 69 -19t21 -57v-14q0 -38 -21 -57t-69 -19h-162v-731 q0 -53 -22.5 -74.5t-65.5 -21.5h-21q-92 0 -92 96v731h-115q-48 0 -69 19.5t-21 58.5zM856 88v1296q0 3 10 8.5t28 10t36 4.5q129 0 129 -174v-1145q0 -96 -90 -96h-21q-92 0 -92 96z" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="348" />
<glyph unicode=" "  horiz-adv-x="348" />
<glyph unicode="&#x09;" horiz-adv-x="348" />
<glyph unicode="&#xa0;" horiz-adv-x="348" />
<glyph unicode="!" horiz-adv-x="532" d="M141 96v25q0 54 28.5 83t82.5 29h27q112 0 112 -112v-25q0 -110 -112 -110h-27q-111 0 -111 110zM143 1212q-7 62 19 93.5t78 31.5h51q54 0 80.5 -31t17.5 -94l-61 -757q-5 -36 -16 -53t-36 -17h-22q-22 0 -32 16.5t-17 53.5z" />
<glyph unicode="&#x22;" horiz-adv-x="796" d="M115 1204q-9 71 17.5 101t92.5 30q67 0 94.5 -30t18.5 -101l-49 -373q-5 -34 -15 -48.5t-34 -14.5h-27q-24 0 -34 14.5t-15 48.5zM457 1204q-7 71 19.5 101t92.5 30t93 -30t18 -101l-47 -373q-5 -34 -16 -48.5t-35 -14.5h-27q-24 0 -34 14.5t-15 48.5z" />
<glyph unicode="#" horiz-adv-x="1357" d="M18 403v21q0 72 88 72h207l103 333h-156q-88 0 -88 72v19q0 71 88 71h205l104 340q0 8 27 8q36 0 67 -27t31 -81q0 -25 -16 -78l-51 -162h305l104 340q4 8 29 8q35 0 65.5 -27t30.5 -81q0 -25 -16 -78l-49 -162h155q88 0 88 -69v-21q0 -72 -88 -72h-207l-102 -333h156 q88 0 88 -70v-20q0 -72 -88 -72h-205l-102 -340q-4 -8 -29 -8q-36 0 -66 27t-30 81q0 24 14 78l49 162h-303l-104 -340q0 -8 -27 -8q-36 0 -67 27t-31 81q0 28 16 78l49 162h-156q-88 0 -88 69zM471 492h311l105 342h-312z" />
<glyph unicode="$" horiz-adv-x="1126" d="M72 246q0 32 22 60.5t44 40.5t26 7q47 -71 129.5 -122t185.5 -68v432q-62 15 -110 32t-100 47t-86 67.5t-56.5 94.5t-22.5 128q0 141 103 242t268 126v101q0 51 20 73.5t62 22.5h14q43 0 62.5 -22t19.5 -74v-97q165 -16 267 -85.5t102 -143.5q0 -33 -23 -62t-45.5 -42 t-25.5 -9q-45 64 -120.5 109.5t-164.5 58.5v-415q46 -11 83.5 -22.5t81 -29t76.5 -38t65.5 -49.5t53.5 -63t33.5 -79t12.5 -98q0 -152 -109 -255.5t-291 -123.5v-103q0 -52 -20.5 -75t-63.5 -23h-12q-43 0 -62.5 22.5t-19.5 75.5v101q-177 19 -288 98t-111 160zM305 969 q0 -72 45 -113t135 -70v373q-82 -19 -131 -70t-49 -120zM637 162q94 15 149.5 69t55.5 129q0 77 -52.5 121t-152.5 76v-395z" />
<glyph unicode="%" horiz-adv-x="1505" d="M59 1014v18q0 132 92 223t228 91q137 0 228 -89t91 -221v-18q0 -132 -92.5 -222.5t-228.5 -90.5t-227 88.5t-91 220.5zM156 57q0 28 12.5 51t46.5 58l1030 1186q5 5 27.5 -1.5t44.5 -26.5t22 -48q0 -46 -57 -111l-1032 -1183q-5 -6 -27.5 1.5t-44.5 27.5t-22 46z M215 1010q0 -76 46.5 -126t117.5 -50t116.5 49t45.5 127v30q0 78 -46.5 128.5t-117.5 50.5t-116.5 -50t-45.5 -129v-30zM807 297v18q0 132 92.5 223t228.5 91t227 -89t91 -221v-18q0 -132 -92 -222.5t-228 -90.5q-138 0 -228.5 88.5t-90.5 220.5zM965 293q0 -76 46 -126 t117 -50q70 0 116 49.5t46 126.5v31q0 78 -46.5 128t-117.5 50t-116 -49.5t-45 -128.5v-31z" />
<glyph unicode="&#x26;" horiz-adv-x="1366" d="M43 373q0 279 332 381q-117 128 -117 268t104 235t267 95q150 0 247 -88.5t97 -223.5q0 -231 -309 -323l311 -279q53 94 100 269q18 62 46 88t71 26q28 0 50.5 -13t31.5 -26t8 -18q-56 -250 -184 -438l278 -250q2 -1 -0.5 -10t-10.5 -22t-20.5 -25t-34 -20.5t-46.5 -8.5 q-75 0 -134 61l-141 135q-194 -213 -467 -213q-219 0 -349 105.5t-130 294.5zM240 389q0 -119 82 -187.5t215 -68.5q189 0 333 164l-397 358q-233 -75 -233 -266zM449 1022q0 -62 26 -112.5t80 -102.5q127 35 185.5 88.5t58.5 134.5q0 72 -48.5 121t-123.5 49 q-84 0 -131 -50.5t-47 -127.5z" />
<glyph unicode="'" horiz-adv-x="454" d="M115 1204q-9 71 17.5 101t92.5 30q67 0 94.5 -30t18.5 -101l-49 -373q-5 -34 -15 -48.5t-34 -14.5h-27q-24 0 -34 14.5t-15 48.5z" />
<glyph unicode="(" horiz-adv-x="768" d="M88 465q0 148 35.5 285.5t94.5 241.5t133.5 182.5t155.5 119.5t157 41q94 0 94 -82q0 -22 -12 -44t-19 -27q-133 -30 -232.5 -135t-149.5 -255t-50 -325q0 -180 50 -331.5t149 -255t233 -134.5q6 -2 18.5 -25t12.5 -45q0 -82 -94 -82q-76 0 -157 39.5t-155.5 116.5 t-133.5 180.5t-94.5 243t-35.5 291.5z" />
<glyph unicode=")" horiz-adv-x="768" d="M10 -324q0 22 12.5 45t18.5 25q134 31 233 134.5t149 255t50 331.5q0 175 -50 325t-149.5 255t-232.5 135q-7 5 -19 27t-12 44q0 82 94 82q76 0 157 -41t155.5 -119.5t133.5 -182.5t94.5 -241.5t35.5 -285.5q0 -152 -35.5 -291.5t-94.5 -243t-133.5 -180.5t-155.5 -116.5 t-157 -39.5q-94 0 -94 82z" />
<glyph unicode="*" horiz-adv-x="802" d="M46 1047.5q-8 17.5 1 44.5l2 8q8 25 25 34t53 3l199 -50l16 199q4 38 17.5 51t41.5 13h9q27 0 41 -13t18 -51l12 -199l199 50q65 11 80 -37l2 -8q9 -27 1 -44.5t-42 -31.5l-189 -78l109 -174q33 -56 -12 -86l-6 -4q-17 -16 -38.5 -13.5t-45.5 29.5l-136 158l-133 -158 q-26 -27 -46 -29t-40 13l-6 4q-46 25 -12 86l106 174l-184 78q-34 14 -42 31.5z" />
<glyph unicode="+" horiz-adv-x="1101" d="M80 535v12q0 76 88 76h297v303q0 88 78 88h16q78 0 78 -88v-303h297q88 0 88 -76v-12q0 -76 -88 -76h-297v-322q0 -88 -78 -88h-16q-78 0 -78 88v322h-297q-88 0 -88 76z" />
<glyph unicode="," horiz-adv-x="454" d="M27 -203l110 348q15 53 35.5 75t69.5 22h18q39 0 62.5 -25.5t23.5 -71.5v-10q0 -62 -47 -123l-170 -262q-16 -15 -42 -17t-46.5 17t-13.5 47z" />
<glyph unicode="-" horiz-adv-x="804" d="M84 483v29q0 36 23 58t65 22h461q43 0 65.5 -22t22.5 -58v-29q0 -36 -22.5 -56.5t-65.5 -20.5h-461q-42 0 -65 20.5t-23 56.5z" />
<glyph unicode="." horiz-adv-x="454" d="M102 96v25q0 112 111 112h27q112 0 112 -112v-25q0 -110 -112 -110h-27q-111 0 -111 110z" />
<glyph unicode="/" horiz-adv-x="694" d="M8 -319q0 25 17 81l487 1569q0 1 5 3t15 3.5t21 1.5q113 0 113 -94q0 -23 -17 -82l-485 -1569q0 -8 -41 -8q-115 0 -115 95z" />
<glyph unicode="0" horiz-adv-x="1300" d="M98 594v135q0 288 148 455.5t403 167.5q257 0 405 -166.5t148 -454.5v-133q0 -288 -148.5 -456.5t-404.5 -168.5q-257 0 -404 166.5t-147 454.5zM311 590q0 -209 89 -323.5t249 -114.5q164 0 253 113.5t89 322.5v147q0 209 -90.5 326t-251.5 117t-249.5 -115t-88.5 -324 v-149z" />
<glyph unicode="1" horiz-adv-x="749" d="M41 1122q0 20 5.5 38t13.5 30.5t16.5 21.5t15 12.5t8.5 2.5q50 -47 121 -47q65 0 111.5 44t54.5 111h72q92 0 92 -96v-1153q0 -96 -92 -96h-27q-90 0 -90 96v950q-55 -55 -137 -55q-72 0 -118 39t-46 102z" />
<glyph unicode="2" horiz-adv-x="1095" d="M78 115v51q0 79 25 147t60.5 115t95 92.5t108 73.5t120.5 63q156 77 199 104q117 75 129 166q2 12 2 25q0 103 -71.5 161t-198.5 58q-136 0 -214.5 -70t-109.5 -183q0 -2 -14.5 -2.5t-34.5 4t-40.5 14.5t-35 32.5t-14.5 53.5q0 53 31.5 110t88.5 107t151 82.5t206 32.5 q217 0 340 -105.5t123 -290.5q0 -50 -12 -94t-29.5 -78t-49 -66.5t-60 -55t-74 -48.5t-78.5 -42t-86 -40q-58 -26 -96 -44.5t-83 -45t-74 -52t-55 -57.5t-39 -70.5t-14 -82.5h672q101 0 101 -82v-12q0 -86 -101 -86h-737q-70 0 -100.5 26.5t-30.5 88.5z" />
<glyph unicode="3" horiz-adv-x="1118" d="M33 270q0 31 12.5 54t30 33.5t36 18t30.5 6t14 -2.5q38 -113 134 -173.5t230 -60.5q141 0 224 63.5t83 170.5q0 115 -77 174t-226 59h-94q-100 0 -100 76v12q0 40 23.5 58t72.5 18h123q120 0 184 53t64 148t-74.5 150t-204.5 55q-121 0 -202.5 -55t-120.5 -146 q-2 -1 -14.5 -2.5t-31 6.5t-35.5 18.5t-29.5 32t-12.5 49.5q0 43 32.5 89.5t88.5 86.5t143 65.5t184 25.5q229 0 355 -94.5t126 -264.5q0 -215 -225 -282q125 -41 191.5 -129t66.5 -211q0 -187 -136 -292.5t-378 -105.5q-93 0 -174 19.5t-137 51t-97 70.5t-60 79t-19 77z " />
<glyph unicode="4" horiz-adv-x="1216" d="M68 453v45q0 32 13.5 59.5t45.5 71.5l473 612q41 58 74 76t106 18q86 0 122 -31.5t36 -105.5v-672h145q93 0 93 -82v-10q0 -80 -93 -80h-145v-268q0 -96 -90 -96h-23q-39 0 -62.5 27t-23.5 69v268h-542q-59 0 -94 27.5t-35 71.5zM258 522h490v641z" />
<glyph unicode="5" horiz-adv-x="1101" d="M53 236q0 32 24 59.5t48.5 39t28.5 5.5q57 -93 152 -145t220 -52q146 0 232 71.5t86 191.5t-79 188t-220 68q-185 0 -309 -91q-130 9 -130 131v504q0 54 29 86.5t80 32.5h666q98 0 98 -86v-10q0 -84 -98 -84h-588v-379q131 59 272 59q216 0 345.5 -113t129.5 -304 q0 -199 -138.5 -317t-375.5 -118q-203 0 -338 85t-135 178z" />
<glyph unicode="6" horiz-adv-x="1202" d="M98 588v135q0 292 150 460.5t407 168.5q194 0 308 -74t114 -162q0 -36 -26 -62t-51.5 -35t-28.5 -5q-106 164 -305 164q-172 0 -265.5 -115.5t-93.5 -331.5v-41q69 64 168 105t209 41q204 0 326.5 -107.5t122.5 -292.5v-10q0 -197 -138.5 -325t-353.5 -128 q-259 0 -401 167t-142 448zM309 526q9 -183 92.5 -284t231.5 -101q131 0 214 74.5t83 194.5v12q0 123 -75.5 189.5t-215.5 66.5q-92 0 -180.5 -42.5t-149.5 -109.5z" />
<glyph unicode="7" horiz-adv-x="1085" d="M45 1223v14q0 88 94 88h774q76 0 103.5 -26.5t27.5 -73.5v-17q0 -45 -61 -153l-534 -992q-40 -73 -109 -73q-32 0 -62.5 17t-45 36t-9.5 27l588 1067h-672q-94 0 -94 86z" />
<glyph unicode="8" horiz-adv-x="1193" d="M76 352q0 117 68.5 202.5t199.5 127.5q-229 75 -229 299q0 165 132.5 268t350.5 103q221 0 349 -99.5t128 -267.5q0 -107 -57 -184.5t-170 -116.5q133 -41 200.5 -126.5t67.5 -205.5q0 -167 -143.5 -272t-376.5 -105q-242 0 -381 102t-139 275zM283 369 q0 -103 84.5 -167.5t230.5 -64.5q151 0 231 61.5t80 170.5q0 107 -83.5 171t-229.5 64q-148 0 -230.5 -63.5t-82.5 -171.5zM317 969q0 -96 75.5 -154.5t205.5 -58.5q131 0 203.5 56.5t72.5 156.5t-73.5 160.5t-204.5 60.5q-136 0 -207.5 -59t-71.5 -162z" />
<glyph unicode="9" horiz-adv-x="1202" d="M72 874v9q0 198 139 332.5t352 134.5q253 0 397 -162t144 -449v-141q0 -308 -145 -466.5t-422 -158.5q-194 0 -307 71t-113 155q0 35 21.5 62t42.5 37.5t24 6.5q123 -160 323 -160q187 0 278 108.5t91 336.5l-2 28q-66 -63 -166 -103t-209 -40q-206 0 -327 106.5 t-121 292.5zM274 889q0 -123 75 -189.5t214 -66.5q92 0 179.5 42t148.5 109q-6 190 -89 295t-233 105q-128 0 -211.5 -82t-83.5 -203v-10z" />
<glyph unicode=":" horiz-adv-x="503" d="M127 96v25q0 112 111 112h26q113 0 113 -112v-25q0 -110 -113 -110h-26q-111 0 -111 110zM127 762v24q0 111 111 111h26q113 0 113 -111v-24q0 -111 -113 -111h-26q-54 0 -82.5 28.5t-28.5 82.5z" />
<glyph unicode=";" horiz-adv-x="503" d="M51 -203l111 348q15 53 35 75t69 22h19q39 0 62.5 -25.5t23.5 -71.5v-10q0 -62 -47 -123l-170 -262q-16 -15 -42 -17t-47 17t-14 47zM127 762v24q0 111 111 111h26q113 0 113 -111v-24q0 -111 -113 -111h-26q-54 0 -82.5 28.5t-28.5 82.5z" />
<glyph unicode="&#x3c;" horiz-adv-x="950" d="M90 541q0 47 15.5 71t46.5 41l649 351q5 1 17.5 -11t25 -36t14.5 -48q4 -75 -100 -127l-467 -241l467 -238q104 -54 100 -129q-2 -26 -14 -50t-24.5 -35t-18.5 -9l-649 348q-31 17 -46.5 41.5t-15.5 71.5z" />
<glyph unicode="=" horiz-adv-x="1101" d="M104 285v20q0 80 89 80h716q88 0 88 -80v-20q0 -80 -88 -80h-716q-89 0 -89 80zM104 764v18q0 80 89 80h716q88 0 88 -80v-18q0 -80 -88 -80h-716q-89 0 -89 80z" />
<glyph unicode="&#x3e;" horiz-adv-x="948" d="M90 174q-4 73 100 129l465 238l-465 241q-104 53 -100 127q2 24 15 47.5t26 36t19 11.5l647 -351q31 -17 46 -41t15 -71q0 -48 -15 -72t-46 -41l-647 -348q-7 -2 -20 9.5t-25.5 35t-14.5 49.5z" />
<glyph unicode="?" horiz-adv-x="980" d="M61 1026q0 49 27 103t78 104.5t138.5 83.5t195.5 33q196 0 312 -97t116 -252q0 -68 -22 -122t-57 -89.5t-77.5 -65.5t-85.5 -58t-78.5 -59.5t-58.5 -78t-25 -104.5q-6 -45 -84 -45q-40 0 -63.5 18t-22.5 52q2 77 24 137.5t55 98t72.5 67t78.5 53t71 47t52 57t20 76.5 q0 89 -61 141t-170 52q-70 0 -124.5 -22t-88 -60.5t-52.5 -82.5t-26 -95q-2 0 -16 -1.5t-35.5 5t-42 16.5t-35.5 33.5t-15 54.5zM319 96v25q0 54 28.5 83t82.5 29h29q112 0 112 -112v-25q0 -110 -112 -110h-29q-111 0 -111 110z" />
<glyph unicode="@" horiz-adv-x="1521" d="M90 662q0 149 50.5 277.5t139.5 219t215.5 142t274.5 51.5q309 0 489.5 -168.5t180.5 -446.5q0 -194 -80.5 -297.5t-231.5 -103.5q-84 0 -142 35t-77 96q-33 -61 -87.5 -95t-123.5 -34q-121 0 -197.5 87.5t-76.5 223.5q0 137 78 223t200 86q60 0 111 -28t82 -72 q16 96 98 96q16 0 30 -4.5t21 -9.5t7 -8v-322q0 -80 27.5 -119.5t90.5 -39.5q75 0 116.5 76.5t41.5 207.5q0 228 -149 368t-408 140q-250 0 -408.5 -163t-158.5 -418q0 -166 74 -298.5t207 -207t302 -74.5q97 0 186 27.5t150 72.5q39 27 70 27q20 0 37 -17t23.5 -35t2.5 -22 q-79 -71 -203 -116.5t-266 -45.5q-313 0 -504.5 190.5t-191.5 498.5zM586 649q0 -79 42.5 -127.5t110.5 -48.5q65 0 109.5 40.5t44.5 104.5v101q-46 104 -152 104q-70 0 -112.5 -48t-42.5 -126z" />
<glyph unicode="A" horiz-adv-x="1290" d="M27 53l471 1217q13 34 44.5 50.5t100.5 16.5q70 0 101 -16t44 -51l476 -1217q3 -8 -13 -23t-45.5 -27.5t-58.5 -12.5q-88 0 -125 98l-117 301h-530l-117 -303q-35 -96 -117 -96q-28 0 -57 12t-44.5 27t-12.5 24zM426 559h426l-213 561z" />
<glyph unicode="B" horiz-adv-x="1265" d="M141 119v1087q0 54 31 86.5t82 32.5h436q217 0 332 -88.5t115 -255.5q0 -107 -59.5 -179t-174.5 -106q143 -33 215 -118.5t72 -206.5q0 -187 -123 -279t-381 -92h-432q-51 0 -82 32.5t-31 86.5zM350 170h322q160 0 230.5 51t70.5 166q0 209 -295 209h-328v-426zM350 748 h314q117 0 185.5 56.5t68.5 151.5q0 199 -259 199h-309v-407z" />
<glyph unicode="C" horiz-adv-x="1345" d="M90 659q0 150 50 279t138 220t213.5 142.5t272.5 51.5q95 0 179 -20.5t141 -52t99 -70t61.5 -74t19.5 -64.5q0 -44 -30 -73.5t-60 -38.5t-33 -3q-56 97 -157.5 151t-217.5 54q-202 0 -329.5 -138t-127.5 -359q0 -219 130.5 -359.5t330.5 -140.5q134 0 238 62t145 169 q2 1 15 1.5t33 -8.5t38.5 -20.5t31.5 -34t13 -50.5q0 -31 -20.5 -70t-64 -81.5t-103.5 -77.5t-149 -58t-189 -23q-194 0 -346.5 87t-237 243.5t-84.5 355.5z" />
<glyph unicode="D" horiz-adv-x="1386" d="M141 117v1089q0 53 32.5 86t80.5 33h324q338 0 528 -174.5t190 -484.5q0 -311 -194 -488.5t-535 -177.5h-309q-49 0 -83 34.5t-34 82.5zM350 182h221q243 0 377.5 126.5t134.5 355.5q0 225 -135 352t-375 127h-223v-961z" />
<glyph unicode="E" horiz-adv-x="1202" d="M141 117v1089q0 54 31 86.5t82 32.5h768q98 0 98 -88v-12q0 -88 -98 -88h-670v-371h518q99 0 99 -84v-6q0 -88 -99 -88h-518v-402h682q96 0 96 -84v-10q0 -92 -96 -92h-780q-51 0 -82 31.5t-31 85.5z" />
<glyph unicode="F" horiz-adv-x="1112" d="M141 102v1104q0 54 31 86.5t82 32.5h715q98 0 98 -86v-12q0 -88 -98 -88h-619v-422h459q98 0 98 -84v-8q0 -86 -98 -86h-455v-451q0 -96 -92 -96h-33q-88 0 -88 110z" />
<glyph unicode="G" horiz-adv-x="1458" d="M90 659q0 201 86 359t241 246t353 88q114 0 213 -28.5t160 -71t95 -89t34 -86.5q0 -43 -28 -71t-55.5 -35.5t-31.5 -1.5q-60 95 -164.5 147.5t-218.5 52.5q-206 0 -336.5 -142.5t-130.5 -369.5t122 -369.5t329 -142.5q160 0 268.5 82t128.5 215v84h-311q-55 0 -81 19 t-26 61v10q0 44 26 63t81 19h385q56 0 84 -31.5t28 -88.5v-566q0 -3 -9 -7.5t-25.5 -8.5t-34.5 -4q-53 0 -84 37t-31 133v24q-63 -100 -176 -155.5t-264 -55.5q-188 0 -331.5 88.5t-219.5 243.5t-76 352z" />
<glyph unicode="H" horiz-adv-x="1402" d="M141 88v1149q0 96 88 96h31q92 0 92 -96v-481h699v481q0 96 88 96h30q93 0 93 -96v-1149q0 -96 -93 -96h-30q-88 0 -88 96v483h-699v-483q0 -96 -92 -96h-31q-88 0 -88 96z" />
<glyph unicode="I" horiz-adv-x="530" d="M160 88v1149q0 96 90 96h29q92 0 92 -96v-1149q0 -96 -92 -96h-29q-90 0 -90 96z" />
<glyph unicode="J" horiz-adv-x="950" d="M47 186q0 36 23.5 66t46.5 42t26 7q37 -67 99 -106t141 -39q122 0 174.5 67.5t52.5 225.5v788q0 96 90 96h31q90 0 90 -96v-791q0 -241 -104 -357t-318 -116q-152 0 -252 66.5t-100 146.5z" />
<glyph unicode="K" horiz-adv-x="1191" d="M141 88v1149q0 96 88 96h31q92 0 92 -96v-532l531 561q55 67 116 67q25 0 46.5 -9t34 -22t21 -26.5t11.5 -23.5t2 -13l-528 -561l542 -594q2 -2 2 -5q0 -8 -8 -23q-11 -22 -41.5 -43t-72.5 -21q-67 0 -115 59l-541 590v-553q0 -96 -92 -96h-31q-88 0 -88 96z" />
<glyph unicode="L" horiz-adv-x="1081" d="M141 117v1120q0 96 88 96h31q92 0 92 -96v-1047h576q98 0 98 -90v-8q0 -44 -25.5 -68t-72.5 -24h-674q-51 0 -82 31.5t-31 85.5z" />
<glyph unicode="M" horiz-adv-x="1533" d="M141 88v1129q0 54 32.5 85t88.5 31h55q58 0 84.5 -22.5t53.5 -83.5l313 -707l315 707q26 61 53 83.5t83 22.5h51q57 0 91 -31t34 -85v-1129q0 -96 -88 -96h-29q-84 0 -84 96v973l-293 -660q-20 -47 -46.5 -68.5t-88.5 -21.5q-44 0 -71 13t-37.5 28.5t-26.5 48.5l-289 654 v-967q0 -96 -86 -96h-33q-82 0 -82 96z" />
<glyph unicode="N" horiz-adv-x="1396" d="M141 88v1126q0 55 26 87t75 32h34q44 0 69 -17.5t58 -62.5l656 -929v913q0 96 88 96h24q86 0 86 -96v-1126q0 -56 -26 -87.5t-72 -31.5h-16q-44 0 -65 13t-48 50l-690 977v-944q0 -96 -90 -96h-23q-86 0 -86 96z" />
<glyph unicode="O" d="M88 659q0 200 86 358.5t239.5 246.5t346.5 88q296 0 483 -192t187 -496q0 -200 -85.5 -358t-239 -245.5t-347.5 -87.5q-195 0 -348 87t-237.5 243.5t-84.5 355.5zM301 664q0 -221 129 -362.5t330 -141.5t329 139t128 360t-129 363.5t-330 142.5q-202 0 -329.5 -139.5 t-127.5 -361.5z" />
<glyph unicode="P" horiz-adv-x="1165" d="M141 88v1118q0 54 31 86.5t82 32.5h342q247 0 380.5 -109t133.5 -313q0 -209 -133 -317.5t-389 -108.5h-236v-389q0 -44 -23.5 -70t-68.5 -26h-31q-88 0 -88 96zM350 647h242q152 0 229.5 64.5t77.5 187.5q0 125 -77 188.5t-228 63.5h-244v-504z" />
<glyph unicode="Q" d="M88 659q0 200 86 358.5t240 246.5t348 88q295 0 481.5 -192t186.5 -496q0 -253 -152 -445l158 -162q1 -1 1 -3q0 -3 -3 -9q-4 -9 -14.5 -21t-24.5 -23.5t-35.5 -19.5t-44.5 -8q-61 0 -123 68l-51 51q-166 -119 -383 -119q-194 0 -347 86.5t-238 243t-85 356.5zM301 664 q0 -221 130 -362.5t331 -141.5q143 0 252 67l-211 217q-29 31 -33 68q-1 5 -1 11q0 30 22 52l14 13q24 20 50 20q7 0 15 -1q33 -7 62 -40l207 -213q82 124 82 305q0 220 -129.5 363t-331.5 143t-330.5 -140.5t-128.5 -360.5z" />
<glyph unicode="R" horiz-adv-x="1206" d="M141 100v1106q0 54 31 86.5t82 32.5h377q243 0 369 -100.5t126 -296.5q0 -170 -102 -272t-293 -117l387 -461q3 -4 -0.5 -13.5t-13.5 -22t-24.5 -24t-37 -19t-47.5 -7.5q-41 0 -72 18.5t-65 63.5l-362 444h-146v-430q0 -44 -23.5 -70t-68.5 -26h-29q-88 0 -88 108z M348 684h279q143 0 214.5 62t71.5 172q0 233 -292 233h-273v-467z" />
<glyph unicode="S" horiz-adv-x="1134" d="M66 238q0 32 23 61t46 42.5t27 8.5q61 -89 165 -146t234 -57q138 0 213.5 54.5t75.5 152.5q0 25 -6.5 46.5t-14.5 38t-26.5 32t-32.5 26t-42 22t-45 18t-53 16t-53.5 14t-58.5 13.5q-72 17 -123.5 33t-111 46.5t-96.5 69t-62 100t-25 138.5q0 169 133 277t345 108 q105 0 194.5 -23t144 -59t85 -76.5t30.5 -77.5q0 -34 -24 -64t-47.5 -43.5t-26.5 -9.5q-55 79 -149 129t-205 50q-125 0 -200 -55.5t-75 -147.5q0 -40 16 -71t34 -49t65.5 -36t74.5 -25.5t98 -23.5q17 -4 25 -6q75 -17 133.5 -36t119 -51.5t100 -72.5t65 -100.5t25.5 -134.5 q0 -183 -133.5 -289.5t-364.5 -106.5q-114 0 -211.5 26t-158 66t-94 85.5t-33.5 87.5z" />
<glyph unicode="T" horiz-adv-x="1214" d="M41 1227v10q0 43 25.5 65.5t72.5 22.5h938q97 0 97 -88v-10q0 -43 -25 -66.5t-72 -23.5h-362v-1049q0 -96 -94 -96h-29q-90 0 -90 96v1049h-363q-47 0 -72.5 23.5t-25.5 66.5z" />
<glyph unicode="U" horiz-adv-x="1331" d="M125 539v698q0 47 23.5 71.5t66.5 24.5h29q92 0 92 -96v-692q0 -189 83 -286t247 -97q167 0 248 95.5t81 289.5v690q0 96 90 96h33q88 0 88 -96v-694q0 -277 -139 -422.5t-403 -145.5t-401.5 143.5t-137.5 420.5z" />
<glyph unicode="V" horiz-adv-x="1269" d="M31 1266q-1 2 -1 4q0 8 14 21q17 16 48.5 29t63.5 13q91 0 123 -96l360 -1010l360 1010q33 96 117 96q31 0 62 -13t47.5 -29t13.5 -25l-473 -1198q-18 -43 -46.5 -61.5t-84.5 -18.5q-55 0 -82.5 18t-46.5 62z" />
<glyph unicode="W" horiz-adv-x="1894" d="M98 1274q-1 6 12.5 19.5t44.5 26.5t68 13q96 0 123 -123l215 -974l252 833q24 96 139 96q60 0 92 -23.5t48 -72.5l247 -829l220 972q25 121 116 121q35 0 65 -13t43 -26.5t11 -19.5l-305 -1204q-11 -46 -43.5 -65t-99.5 -19q-61 0 -92.5 20t-45.5 66l-256 856l-268 -856 q-12 -46 -42 -66t-91 -20q-68 0 -100.5 18.5t-42.5 65.5z" />
<glyph unicode="X" horiz-adv-x="1241" d="M47 78l436 588l-424 579q-5 8 9.5 28.5t47 40t68.5 19.5q73 0 117 -69l324 -471l336 473q46 71 108 71q33 0 64 -19t45 -40q11 -16 11 -24q0 -3 -1 -5l-432 -585l432 -582q1 -2 1 -4q-1 -4 -3 -10q-4 -10 -15 -22.5t-26 -24.5t-36.5 -19.5t-43.5 -7.5q-73 0 -117 69 l-334 478l-340 -480q-45 -71 -108 -71q-33 0 -64 19q-32 19 -46 40q-11 16 -10 24q0 3 1 5z" />
<glyph unicode="Y" horiz-adv-x="1173" d="M12 1247q-1 2 -1 4q0 4 3 11q3 9 13.5 21.5t25.5 23.5t39.5 18.5t52.5 7.5q63 0 103 -73l340 -541l340 541q45 73 104 73q26 0 49 -7.5t38 -19t25 -24t14 -22.5q2 -5 2 -9q0 -2 -1 -4l-467 -704v-455q0 -96 -92 -96h-29q-90 0 -90 96v455z" />
<glyph unicode="Z" horiz-adv-x="1247" d="M98 100v19q0 44 17 77t71 99l695 846h-682q-95 0 -95 86v12q0 86 95 86h800q69 0 102.5 -27.5t33.5 -70.5v-19q0 -42 -20.5 -79t-65.5 -91l-701 -854h723q94 0 94 -86v-12q0 -86 -94 -86h-835q-69 0 -103.5 28.5t-34.5 71.5z" />
<glyph unicode="[" horiz-adv-x="706" d="M143 -287v1499q0 54 31 86.5t82 32.5h315q52 0 74.5 -19t22.5 -59v-14q0 -42 -22.5 -61t-74.5 -19h-237v-1392h237q53 0 76 -19t23 -59v-13q0 -42 -23 -62t-76 -20h-315q-51 0 -82 32.5t-31 86.5z" />
<glyph unicode="\" horiz-adv-x="694" d="M27 1245q0 94 114 94q9 0 19 -1.5t16 -3.5t6 -3l486 -1569q16 -53 16 -81q0 -54 -34.5 -74.5t-78.5 -20.5q-41 0 -41 8l-487 1569q-16 56 -16 82z" />
<glyph unicode="]" horiz-adv-x="706" d="M37 -311q0 41 22.5 59.5t75.5 18.5h238v1392h-236q-53 0 -75.5 19t-22.5 61v14q0 41 22.5 59.5t75.5 18.5h314q51 0 81.5 -32.5t30.5 -86.5v-1499q0 -54 -30.5 -86.5t-81.5 -32.5h-316q-53 0 -75.5 20t-22.5 62v13z" />
<glyph unicode="^" horiz-adv-x="1024" d="M225 975q0 50 64 124t131.5 125t91.5 56q24 -5 91 -56t130.5 -125t63.5 -124q0 -29 -17.5 -47.5t-46.5 -18.5q-42 0 -94 51t-129 168q-81 -120 -129.5 -169.5t-89.5 -49.5q-29 0 -47.5 18.5t-18.5 47.5z" />
<glyph unicode="_" horiz-adv-x="980" d="M27 -80q0 37 23 58.5t65 21.5h751q42 0 65 -21.5t23 -58.5v-10q0 -37 -23 -57.5t-65 -20.5h-751q-42 0 -65 20.5t-23 57.5v10z" />
<glyph unicode="`" horiz-adv-x="1024" d="M225 1372q0 35 27 57.5t65 22.5q53 0 99 -35q61 -40 123.5 -106t96 -115.5t28.5 -58.5q-7 -4 -27 -4.5t-59.5 8t-81 21t-91.5 35.5t-92 50q-88 54 -88 125z" />
<glyph unicode="a" horiz-adv-x="1071" d="M68 258q0 168 162 248t499 86h27v20q0 113 -63 172.5t-181 59.5q-105 0 -175.5 -46.5t-107.5 -121.5q0 -1 -13 0t-31.5 6t-36.5 14.5t-31 30t-13 47.5q0 37 27.5 77t78 76t134 59.5t182.5 23.5q208 0 319 -102.5t111 -291.5v-604q0 -8 -23 -15t-50 -7q-122 0 -125 157 q-57 -80 -150 -125t-205 -45q-156 0 -245.5 76.5t-89.5 204.5zM266 266q0 -66 50 -104.5t137 -38.5q132 0 217.5 74.5t85.5 191.5v76l-56 -2q-221 -8 -327.5 -54t-106.5 -143z" />
<glyph unicode="b" horiz-adv-x="1198" d="M125 88v1296q0 3 9.5 8.5t27.5 10t37 4.5q55 0 92 -39t37 -135v-402q126 179 344 179q201 0 331 -142t130 -374q0 -231 -131 -376t-336 -145q-118 0 -208 49.5t-132 124.5v-59q0 -96 -88 -96h-23q-90 0 -90 96zM326 418q4 -119 92.5 -198t210.5 -79q138 0 222.5 97 t84.5 254q0 153 -85.5 251.5t-217.5 98.5q-111 0 -189.5 -54.5t-117.5 -150.5v-219z" />
<glyph unicode="c" horiz-adv-x="1056" d="M66 494q0 224 145 370t367 146q103 0 188 -29.5t133.5 -73t74 -88.5t25.5 -82q0 -24 -12 -43t-28.5 -28t-34.5 -16t-30 -6t-15 3q-80 193 -297 193q-139 0 -231.5 -98t-92.5 -248q0 -152 92 -251.5t232 -99.5q217 0 297 193q3 2 15 3t30 -5.5t34.5 -16t28.5 -29t12 -44.5 q0 -28 -15.5 -62.5t-49.5 -71.5t-81.5 -67.5t-119.5 -50t-155 -19.5q-223 0 -367.5 147t-144.5 374z" />
<glyph unicode="d" horiz-adv-x="1198" d="M66 492q0 232 130.5 375t335.5 143q114 0 203 -46t135 -116v536q0 3 10 8.5t28 10t36 4.5q129 0 129 -174v-1145q0 -96 -90 -96h-20q-91 0 -91 96v76q-125 -191 -346 -191q-200 0 -330 144t-130 375zM262 492q0 -154 85.5 -252.5t217.5 -98.5q126 0 216.5 82.5 t90.5 206.5v209q-83 203 -303 203q-138 0 -222.5 -96t-84.5 -254z" />
<glyph unicode="e" horiz-adv-x="1105" d="M66 487q0 148 64.5 267.5t180 187.5t258.5 68q213 0 343 -137t130 -359v-16q0 -33 -13 -46.5t-48 -13.5h-727q10 -136 103 -220.5t233 -84.5q214 0 309 164q4 4 29 -3.5t51.5 -33t26.5 -63.5q0 -75 -118 -149.5t-302 -74.5q-233 0 -376.5 142t-143.5 372zM256 575h590 q-8 129 -82.5 205t-196.5 76q-121 0 -206.5 -78.5t-104.5 -202.5z" />
<glyph unicode="f" horiz-adv-x="722" d="M16 897v14q0 38 21 56t69 18h115v74q0 182 81.5 272t232.5 90q108 0 164.5 -38t56.5 -95q0 -16 -4.5 -31t-11.5 -25.5t-14 -18.5t-12.5 -11t-6.5 -2q-23 23 -64 40t-84 17q-73 0 -107 -48t-34 -152v-72h166q48 0 69 -19t21 -57v-14q0 -38 -21 -57t-69 -19h-162v-731 q0 -53 -22.5 -74.5t-65.5 -21.5h-21q-92 0 -92 96v731h-115q-48 0 -69 19.5t-21 58.5z" />
<glyph unicode="g" horiz-adv-x="1001" d="M-4 -160q0 104 87 175.5t226 95.5q-39 31 -39 92q0 57 56 106q-119 37 -188.5 125t-69.5 203q0 157 120 265t301 108q126 0 236 -60l53 94q25 41 52.5 59.5t64.5 18.5q34 0 61 -21t37 -41q7 -15 7 -21q0 -2 -1 -3l-168 -174q87 -100 87 -219q0 -156 -119.5 -257 t-306.5 -101h-37q-11 -17 -11 -35q0 -13 5 -23.5t18.5 -19t27 -14.5t40 -13.5t48 -12.5t60.5 -14t68 -16q286 -76 286 -268q0 -140 -136.5 -220.5t-379.5 -80.5q-248 0 -366.5 67t-118.5 205zM190 -154q0 -66 75 -101.5t220 -35.5q150 0 233 40.5t83 113.5q0 21 -8.5 38 t-27 30.5t-37 23.5t-51 19.5t-55 15.5t-63 15.5t-61.5 14.5q-133 -8 -220.5 -53.5t-87.5 -120.5zM260 641q0 -99 65 -161t167 -62t165.5 62t63.5 161t-65 161t-169 62q-100 0 -163.5 -62t-63.5 -161z" />
<glyph unicode="h" horiz-adv-x="1148" d="M125 88v1296q0 3 9.5 8.5t27.5 10t37 4.5q55 0 92 -39t37 -135v-404q115 181 325 181q188 0 284.5 -116.5t96.5 -309.5v-496q0 -96 -90 -96h-22q-91 0 -91 96v473q0 127 -60.5 198.5t-166.5 71.5q-126 0 -201 -77.5t-75 -210.5v-455q0 -96 -90 -96h-23q-90 0 -90 96z" />
<glyph unicode="i" horiz-adv-x="471" d="M115 1309q0 55 32 84.5t91 29.5q57 0 87.5 -29.5t30.5 -84.5q0 -113 -120 -113q-57 0 -89 29.5t-32 83.5zM133 88v809q0 98 92 98h21q43 0 66.5 -22t23.5 -76v-809q0 -96 -90 -96h-21q-92 0 -92 96z" />
<glyph unicode="j" horiz-adv-x="471" d="M-168 -279q0 25 11 49t22.5 36t13.5 10q58 -58 131 -58q66 0 94.5 37t28.5 121v981q0 98 92 98h21q43 0 66.5 -22t23.5 -76v-993q0 -152 -75.5 -233t-219.5 -81q-96 0 -152.5 37t-56.5 94zM115 1309q0 55 32 84.5t91 29.5q57 0 87.5 -29.5t30.5 -84.5q0 -113 -120 -113 q-57 0 -89 29.5t-32 83.5z" />
<glyph unicode="k" horiz-adv-x="970" d="M125 88v1296q0 3 9.5 8.5t27.5 10t37 4.5q55 0 92 -39t37 -135v-690l342 383q55 69 121 69q33 0 60 -18t38 -37t6 -25l-358 -395l413 -448q1 -1 1 -3q0 -3 -3 -9q-4 -9 -13.5 -21t-22.5 -23t-32.5 -18.5t-39.5 -7.5q-65 0 -121 69l-391 422v-393q0 -96 -90 -96h-23 q-90 0 -90 96z" />
<glyph unicode="l" horiz-adv-x="471" d="M133 88v1296q0 3 10 8.5t28 10t36 4.5q129 0 129 -174v-1145q0 -96 -90 -96h-21q-92 0 -92 96z" />
<glyph unicode="m" horiz-adv-x="1779" d="M125 88v885q0 3 9.5 8t27.5 9.5t37 4.5q117 0 127 -151q118 166 305 166q119 0 200.5 -53.5t120.5 -153.5q122 207 342 207q184 0 277.5 -106t93.5 -300v-516q0 -96 -90 -96h-21q-44 0 -68 21.5t-24 74.5v492q0 251 -217 251q-77 0 -144 -44.5t-106 -118.5v-580 q0 -96 -90 -96h-20q-92 0 -92 96v483q0 260 -218 260q-76 0 -143 -44.5t-104 -118.5v-580q0 -96 -90 -96h-23q-90 0 -90 96z" />
<glyph unicode="n" horiz-adv-x="1148" d="M125 88v885q0 3 9.5 8t27.5 9.5t37 4.5q120 0 127 -159q118 174 327 174q188 0 284.5 -115.5t96.5 -306.5v-500q0 -96 -90 -96h-22q-91 0 -91 96v475q0 125 -60 196.5t-167 71.5q-94 0 -166 -46.5t-110 -125.5v-571q0 -96 -90 -96h-23q-90 0 -90 96z" />
<glyph unicode="o" horiz-adv-x="1163" d="M66 494q0 226 145 371t373 145q225 0 369.5 -146.5t144.5 -374.5q0 -226 -144.5 -371t-371.5 -145q-228 0 -372 146t-144 375zM252 494q0 -152 94.5 -253.5t235.5 -101.5t234 99.5t93 250.5q0 154 -92.5 254.5t-234.5 100.5t-236 -99t-94 -251z" />
<glyph unicode="p" horiz-adv-x="1198" d="M125 -305v1278q0 3 9.5 8t27.5 9.5t37 4.5q53 0 89.5 -38.5t37.5 -133.5q121 187 346 187q201 0 331 -142t130 -374q0 -231 -131 -376t-336 -145q-116 0 -204.5 52.5t-133.5 132.5v-463q0 -96 -90 -96h-23q-90 0 -90 96zM326 430q0 -123 89 -206t214 -83q138 0 222.5 97 t84.5 254q0 153 -85.5 251.5t-217.5 98.5q-111 0 -189.5 -54.5t-117.5 -150.5v-207z" />
<glyph unicode="q" horiz-adv-x="1198" d="M66 492q0 232 130.5 375t335.5 143q116 0 205 -47t135 -119q7 151 127 151q19 0 37 -4.5t27.5 -9.5t9.5 -8v-1278q0 -96 -90 -96h-22q-91 0 -91 96v465q-125 -187 -344 -187q-200 0 -330 144t-130 375zM262 492q0 -154 85.5 -252.5t217.5 -98.5q125 0 216 84t91 207v205 q-40 96 -116.5 150.5t-186.5 54.5q-138 0 -222.5 -96t-84.5 -254z" />
<glyph unicode="r" horiz-adv-x="761" d="M125 88v885q0 3 9.5 8t27.5 9.5t37 4.5q53 0 89.5 -38.5t37.5 -133.5q66 187 249 187q76 0 119 -34.5t43 -96.5q0 -35 -14.5 -61.5t-29 -36.5t-17.5 -7q-53 43 -125 43q-115 0 -169 -101.5t-54 -281.5v-346q0 -96 -90 -96h-23q-90 0 -90 96z" />
<glyph unicode="s" horiz-adv-x="950" d="M53 184q0 28 19 51.5t38 33.5t23 7q115 -147 332 -147q103 0 160 35.5t57 97.5q0 19 -3.5 34t-14 27.5t-19.5 21.5t-30 18.5t-34.5 14.5t-45 13t-49 12t-58.5 13q-42 9 -66 14.5t-65.5 18.5t-66.5 26t-56.5 35t-49 47.5t-30 62.5t-12.5 80q0 139 107 224.5t286 85.5 q177 0 278 -61t101 -138q0 -28 -20 -52.5t-39.5 -35t-22.5 -6.5q-47 64 -122.5 101.5t-166.5 37.5q-103 0 -158 -36t-55 -99q0 -27 11.5 -47t27.5 -33t52.5 -25.5t66.5 -20t88 -20.5q36 -8 53 -12t53.5 -13.5t56 -17t51 -21.5t49 -28t39.5 -35t33.5 -44.5t19.5 -53.5t8 -65 q0 -142 -110 -226t-300 -84q-191 0 -303.5 65t-112.5 144z" />
<glyph unicode="t" horiz-adv-x="759" d="M39 895v14q0 76 88 76h72v152q0 96 90 96h20q43 0 65.5 -21t22.5 -75v-152h224q46 0 66 -18t20 -56v-14q0 -78 -89 -78h-223v-497q0 -94 30 -138.5t101 -44.5q87 0 140 70q2 2 8 -0.5t14.5 -9.5t16 -17t13 -25.5t5.5 -33.5q0 -60 -62.5 -105t-166.5 -45q-295 0 -295 326 v520h-72q-88 0 -88 76z" />
<glyph unicode="u" horiz-adv-x="1148" d="M117 397v500q0 98 88 98h22q43 0 66.5 -22t23.5 -76v-475q0 -128 62 -199t174 -71q90 0 160.5 47t109.5 127v571q0 98 88 98h23q43 0 66.5 -22t23.5 -76v-883q0 -3 -9.5 -8t-27 -9.5t-35.5 -4.5q-124 0 -129 155q-120 -172 -327 -172q-188 0 -283.5 114.5t-95.5 307.5z " />
<glyph unicode="v" horiz-adv-x="1021" d="M27 936q-2 7 10.5 20.5t39 26t56.5 12.5q45 0 74.5 -26t54.5 -95l248 -694l256 694q25 68 54.5 94.5t70.5 26.5q29 0 55 -13t38.5 -26.5t10.5 -19.5l-358 -885q-25 -63 -125 -63q-54 0 -82 13t-41 46z" />
<glyph unicode="w" horiz-adv-x="1454" d="M41 936q-2 6 15.5 20t48 26.5t57.5 12.5q85 0 106 -108l154 -690l188 596q11 37 40 54t85 17q55 0 83.5 -17t39.5 -54l182 -592l152 686q10 58 39 83t65 25q27 0 56 -13t45.5 -26.5t15.5 -19.5l-244 -883q-9 -36 -37 -50.5t-85 -14.5q-62 0 -86.5 14.5t-38.5 56.5 l-191 596l-196 -596q-14 -41 -39 -56t-84 -15q-57 0 -86 14.5t-39 50.5z" />
<glyph unicode="x" horiz-adv-x="1001" d="M45 66l326 426l-322 430q-4 6 8.5 23t40 33.5t60.5 16.5q74 0 127 -84l219 -313l215 313q54 84 125 84q33 0 62 -17q30 -17 42 -34q9 -13 9 -19q0 -2 -1 -3l-325 -430l319 -426q2 -3 -1.5 -11.5t-13 -19.5t-22 -21t-32.5 -17t-41 -7q-73 0 -125 84l-219 311l-213 -309 q-55 -86 -127 -86q-33 0 -61.5 17.5t-41.5 35.5q-9 13 -9 19q0 3 1 4z" />
<glyph unicode="y" horiz-adv-x="1019" d="M27 936q-2 7 10.5 20.5t39 26t56.5 12.5q45 0 75 -25t54 -89l266 -680l236 676q38 118 127 118q25 0 52 -12t41.5 -25t12.5 -18l-465 -1227q-42 -112 -124 -112q-26 0 -53 12t-41.5 25t-12.5 18l164 399q-35 3 -54.5 15.5t-31.5 42.5z" />
<glyph unicode="z" horiz-adv-x="987" d="M72 78v16q0 30 11.5 51t45.5 60l516 618h-481q-46 0 -67 18t-21 56v14q0 74 88 74h657q84 0 84 -76v-12q0 -28 -11 -48t-44 -58l-526 -627h510q88 0 88 -76v-12q0 -76 -88 -76h-678q-43 0 -63.5 22t-20.5 56z" />
<glyph unicode="{" horiz-adv-x="886" d="M49 465q0 47 12 75t34.5 45.5t72.5 43.5l113 61q8 130 41.5 239.5t81.5 183.5t108.5 125.5t122.5 75t123 23.5q43 0 68.5 -23.5t25.5 -64.5q0 -23 -12 -45t-19 -24q-185 -34 -262.5 -174t-85.5 -410l-244 -131l242 -131q4 -107 16.5 -189t37.5 -151.5t64.5 -117.5 t96.5 -80t133 -46q8 -2 20.5 -24t12.5 -45q0 -41 -26.5 -65t-69.5 -24q-61 0 -123 23.5t-122.5 75.5t-108 125.5t-80.5 183.5t-41 240l-113 61q-74 39 -96.5 67.5t-22.5 96.5z" />
<glyph unicode="|" horiz-adv-x="524" d="M160 -307v1548q0 96 92 96h20q90 0 90 -96v-1548q0 -99 -90 -99h-20q-92 0 -92 99z" />
<glyph unicode="}" horiz-adv-x="886" d="M33 1249q0 41 25.5 64.5t68.5 23.5q61 0 123.5 -23.5t123 -75t108.5 -125.5t81 -183.5t41 -239.5l115 -61q71 -38 95 -67.5t24 -96.5t-23 -96t-96 -68l-113 -61q-8 -130 -41 -240t-81 -183.5t-108.5 -125.5t-123 -75.5t-123.5 -23.5q-43 0 -68.5 24t-25.5 65q0 23 12 45 t19 24q185 34 262.5 174t85.5 410l241 131l-241 131q-4 107 -16.5 189t-37.5 151.5t-64.5 117.5t-96.5 80t-133 46q-8 2 -20.5 24t-12.5 45z" />
<glyph unicode="~" horiz-adv-x="1021" d="M197 410q0 79 46 137.5t113 58.5q57 0 136 -40.5t159 -112.5q19 61 46.5 101t58.5 40q33 0 52 -20t19 -54q0 -77 -47 -135.5t-112 -58.5q-59 0 -138.5 40t-156.5 113q-49 -141 -105 -141q-33 0 -52 19.5t-19 52.5z" />
<glyph unicode="&#xa1;" horiz-adv-x="532" d="M141 1204v25q0 110 113 110h27q110 0 110 -110v-25q0 -110 -110 -110h-27q-113 0 -113 110zM143 113l62 759q7 36 17.5 52t33.5 16h23q23 0 32.5 -15.5t16.5 -52.5l61 -759q9 -61 -17 -93t-79 -32h-51q-54 0 -81 32t-18 93z" />
<glyph unicode="&#xa2;" horiz-adv-x="1095" d="M78 578q0 204 119 344.5t311 173.5v157q0 99 84 99h10q42 0 62 -23t20 -76v-151q160 -16 253 -89t93 -147q0 -37 -26 -64t-52 -35.5t-30 -2.5q-43 76 -101 118t-145 52v-713q181 21 258 189q4 5 31 -2.5t54.5 -32.5t27.5 -62q0 -82 -99 -163t-262 -97v-159q0 -51 -21 -74 t-63 -23h-10q-42 0 -62 22.5t-20 74.5v165q-203 30 -317.5 165.5t-114.5 353.5zM274 578q0 -136 66 -226.5t178 -120.5v697q-114 -27 -179 -118t-65 -232z" />
<glyph unicode="&#xa3;" horiz-adv-x="1185" d="M33 82v14q0 80 94 80h86l63 350h-126q-39 0 -60.5 18.5t-21.5 53.5v18q0 35 22.5 54.5t59.5 19.5h155l51 273q34 189 139 289t277 100q86 0 156.5 -24.5t113 -64t65.5 -86t23 -94.5q0 -32 -13 -54.5t-31 -33t-36.5 -15t-31.5 -4t-14 2.5q-43 201 -226 201 q-104 0 -157.5 -56.5t-75.5 -181.5l-47 -252h309q38 0 60 -19.5t22 -54.5v-18q0 -35 -21.5 -53.5t-60.5 -18.5h-338l-63 -350h610q94 0 94 -82v-12q0 -82 -94 -82h-889q-94 0 -94 82z" />
<glyph unicode="&#xa4;" horiz-adv-x="1286" d="M61 502v16q0 66 82 66h103v166h-103q-82 0 -82 65v16q0 64 82 64h119q43 212 183.5 333.5t345.5 121.5q95 0 177.5 -24.5t134.5 -63t81.5 -83.5t29.5 -87q0 -37 -26 -64t-52 -36.5t-30 -4.5q-57 95 -132 142t-188 47q-119 0 -203 -74.5t-118 -206.5h391q80 0 80 -64v-16 q0 -65 -80 -65h-412v-166h412q80 0 80 -66v-16q0 -66 -80 -66h-391q34 -133 121.5 -209.5t210.5 -76.5q114 0 187.5 51t125.5 153q4 6 31 -3t53.5 -37t26.5 -66q0 -41 -29 -88t-80 -89t-134 -70t-179 -28q-215 0 -356.5 122t-182.5 341h-117q-82 0 -82 66z" />
<glyph unicode="&#xa5;" horiz-adv-x="1206" d="M86 1245q-3 4 1 14.5t14 23.5t25 24.5t38.5 19.5t50.5 8q65 0 102 -73l285 -523l295 521q42 75 104 75q37 0 67.5 -20.5t42.5 -41.5t7 -28l-358 -571h248q84 0 84 -68v-16q0 -68 -84 -68h-310v-139h310q84 0 84 -68v-16q0 -68 -84 -68h-310v-145q0 -96 -86 -96h-22 q-82 0 -82 96v145h-311q-84 0 -84 68v16q0 68 84 68h311v139h-311q-84 0 -84 68v16q0 68 84 68h249z" />
<glyph unicode="&#xa7;" horiz-adv-x="1110" d="M66 -96q0 30 21 56t41.5 38t23.5 8q57 -90 155.5 -140t227.5 -50q127 0 197.5 40.5t70.5 114.5q0 80 -80 120q-51 25 -211 61l-1 1h-1h-1h-1l-1 1h-1h-1h-1h-1l-1 1h-1h-1h-1l-1 1h-2q-53 12 -85.5 20.5t-83 24.5t-81.5 32t-67.5 41.5t-57.5 54t-35 69t-14 87.5 q0 95 57 164t160 103q-174 80 -174 258q0 151 126.5 245.5t321.5 94.5q87 0 160.5 -16t121 -41t80.5 -56t47.5 -60.5t14.5 -56.5q0 -31 -21 -58t-42 -38.5t-25 -7.5q-127 172 -330 172q-109 0 -180.5 -42t-71.5 -116t74 -114t236 -76q41 -10 65.5 -16.5t64 -18t64 -21.5 t57.5 -26t53.5 -32t43.5 -38.5t35.5 -47.5t21 -57.5t8.5 -68.5q0 -95 -56.5 -163.5t-158.5 -102.5q85 -37 132 -98.5t47 -161.5q0 -151 -128 -243.5t-341 -92.5q-117 0 -210.5 24.5t-148 63.5t-82.5 81.5t-28 82.5zM254 514q0 -32 11 -57.5t36 -44.5t51 -33t69 -26.5 t77 -20.5t88 -19q7 -1 10 -2l51 -12q91 13 150 67.5t59 118.5q0 31 -11 56.5t-37 45t-50 32.5t-70.5 26.5t-77.5 20.5t-90 20q-21 3 -47 10q-97 -15 -158 -68t-61 -114z" />
<glyph unicode="&#xa8;" horiz-adv-x="1024" d="M203 1239q0 57 32.5 90t86.5 33q59 0 91.5 -32.5t32.5 -90.5q0 -57 -32 -89t-88 -32t-89.5 32.5t-33.5 88.5zM578 1239q0 58 32 90.5t88 32.5t89.5 -32.5t33.5 -90.5q0 -56 -33 -88.5t-86 -32.5q-58 0 -91 32t-33 89z" />
<glyph unicode="&#xa9;" horiz-adv-x="1540" d="M92 659q0 196 88 354.5t243.5 248.5t348.5 90q196 0 350.5 -88t240 -244.5t85.5 -353.5q0 -196 -87.5 -354.5t-242.5 -248.5t-348 -90q-196 0 -351 88t-241 244.5t-86 353.5zM213 664q0 -165 71.5 -297.5t199.5 -207.5t288 -75q247 0 401 160.5t154 417.5 q0 124 -42 231.5t-115.5 183.5t-177.5 120t-224 44q-247 0 -401 -160t-154 -417zM420 659q0 158 105 263.5t261 105.5q79 0 140.5 -25t95.5 -59q39 -39 39 -82q0 -25 -20 -45.5t-40 -29t-22 -3.5q-62 103 -191 103q-87 0 -148.5 -64.5t-61.5 -160.5q0 -99 62.5 -163.5 t154.5 -64.5q133 0 190 111q2 5 22.5 -3t41 -29t20.5 -48q0 -40 -47 -90q-33 -35 -94 -59.5t-140 -24.5q-162 0 -265 103t-103 265z" />
<glyph unicode="&#xaa;" horiz-adv-x="890" d="M63 743q0 50 14.5 88t51 71t98 55.5t156.5 38t225 22.5q0 190 -192 190q-140 0 -215 -131q-1 0 -11.5 -0.5t-27 4.5t-31.5 12.5t-26 24t-11 37.5q0 31 20.5 64t59.5 63.5t106 50t148 19.5q179 0 266.5 -86.5t87.5 -247.5v-467q0 -6 -19.5 -12.5t-43.5 -6.5 q-97 0 -105 119q-41 -60 -111.5 -94.5t-150.5 -34.5q-134 0 -211.5 59.5t-77.5 161.5zM233 756q0 -50 44 -78.5t120 -28.5q89 0 151 52.5t62 121.5v92q-151 -10 -236 -32.5t-113 -51.5t-28 -75zM317 86v20q0 101 101 101h22q103 0 103 -101v-20q0 -100 -103 -100h-22 q-101 0 -101 100z" />
<glyph unicode="&#xab;" horiz-adv-x="1062" d="M82 500q0 17 63 93t151 150t142 74h4q29 0 47.5 -19t18.5 -46v-4q0 -70 -268 -250q268 -176 268 -248v-2q0 -28 -18.5 -47t-47.5 -19h-4q-54 0 -142 74t-151 150.5t-63 93.5zM504 500q0 12 43.5 68t102.5 119.5t129.5 114t115.5 50.5h4q33 0 53.5 -21t20.5 -51v-6 q0 -44 -81.5 -113.5t-219.5 -162.5q301 -197 301 -273v-8q0 -29 -20.5 -49.5t-53.5 -20.5h-4q-46 0 -116 50.5t-129 114t-102.5 119.5t-43.5 69z" />
<glyph unicode="&#xad;" horiz-adv-x="804" d="M84 483v29q0 36 23 58t65 22h461q43 0 65.5 -22t22.5 -58v-29q0 -36 -22.5 -56.5t-65.5 -20.5h-461q-42 0 -65 20.5t-23 56.5z" />
<glyph unicode="&#xae;" horiz-adv-x="1540" d="M92 659q0 196 88 354.5t243.5 248.5t348.5 90q196 0 350.5 -88t240 -244.5t85.5 -353.5q0 -196 -87.5 -354.5t-242.5 -248.5t-348 -90q-196 0 -351 88t-241 244.5t-86 353.5zM213 664q0 -165 71.5 -297.5t199.5 -207.5t288 -75q247 0 401 160.5t154 417.5 q0 124 -42 231.5t-115.5 183.5t-177.5 120t-224 44q-247 0 -401 -160t-154 -417zM498 391v545q0 41 21.5 63.5t60.5 22.5h217q284 0 284 -225q0 -87 -50 -143.5t-140 -69.5l182 -222q3 -5 -7.5 -19t-33 -27t-45.5 -13q-60 0 -98 57l-156 205h-82v-188q0 -72 -65 -72h-23 q-32 0 -48.5 23t-16.5 63zM649 684h146q64 0 98.5 28.5t34.5 78.5q0 106 -140 106h-139v-213z" />
<glyph unicode="&#xaf;" horiz-adv-x="1024" d="M190 1231v12q0 36 27.5 56t75.5 20h438q50 0 76.5 -19.5t26.5 -56.5v-12q0 -80 -103 -80h-438q-103 0 -103 80z" />
<glyph unicode="&#xb0;" horiz-adv-x="817" d="M88 1018v18q0 137 92.5 227.5t231.5 90.5q138 0 227.5 -89t89.5 -227v-18q0 -138 -92 -228t-231 -90q-138 0 -228 89t-90 227zM231 1018q0 -80 53 -133.5t128 -53.5q74 0 124 52t50 133v22q0 81 -51.5 134t-126.5 53q-76 0 -126.5 -52t-50.5 -133v-22z" />
<glyph unicode="&#xb1;" horiz-adv-x="1228" d="M154 270v17q0 75 88 75h743q88 0 88 -75v-17q0 -75 -88 -75h-743q-88 0 -88 75zM174 885v12q0 76 88 76h266v262q0 88 78 88h15q77 0 77 -88v-262h267q88 0 88 -76v-12q0 -76 -88 -76h-267v-268q0 -88 -77 -88h-15q-78 0 -78 88v268h-266q-88 0 -88 76z" />
<glyph unicode="&#xb2;" horiz-adv-x="688" d="M70 668v34q0 54 16.5 95.5t51.5 73t69.5 51.5t89.5 45q10 5 37.5 18t37 18t30 16.5t28.5 19t19 19t14.5 24t3.5 26.5q0 46 -34 74t-93 28q-67 0 -105.5 -37.5t-52.5 -95.5q-1 -2 -12 -2.5t-27.5 2.5t-32.5 10t-27 23.5t-11 40.5q0 72 76.5 131t203.5 59q129 0 201 -61 t72 -168q0 -49 -16 -86.5t-50.5 -65t-64 -43.5t-80.5 -38q-8 -4 -33 -14t-34 -14t-29.5 -13.5t-29 -15t-24 -15.5t-23 -18.5t-16.5 -19.5t-13.5 -23.5t-6.5 -26.5h350q78 0 78 -64v-6q0 -34 -18.5 -49.5t-59.5 -15.5h-385q-57 0 -78.5 18.5t-21.5 65.5z" />
<glyph unicode="&#xb3;" horiz-adv-x="688" d="M41 737q0 24 9.5 41t23.5 25t28.5 12t25 3.5t11.5 -3.5q17 -57 65.5 -88t117.5 -31t112 30t43 79q0 50 -37 77t-104 27h-41q-76 0 -76 56v10q0 55 74 55h57q51 0 83 24t32 64q0 42 -41.5 69t-104.5 27q-124 0 -163 -108q-1 -1 -10.5 -2t-24.5 4t-28.5 12.5t-23.5 24.5 t-10 39q0 62 74.5 109.5t190.5 47.5q140 0 213 -52.5t73 -151.5q0 -118 -127 -156q144 -49 144 -186q0 -107 -79 -166.5t-224 -59.5q-127 0 -205 52t-78 116z" />
<glyph unicode="&#xb4;" horiz-adv-x="1024" d="M360 1137q-6 8 27.5 58t96 115.5t122.5 106.5q53 35 99 35q39 0 65.5 -22.5t26.5 -57.5q0 -72 -86 -125q-42 -27 -92 -50t-91.5 -35.5t-81 -21t-59.5 -8t-27 4.5z" />
<glyph unicode="&#xb6;" horiz-adv-x="1269" d="M57 952q0 185 119.5 282t353.5 97h232q40 0 65 -25t25 -65v-1178q0 -73 -68 -73h-22q-34 0 -52 20t-18 53v521h-153q-235 0 -358.5 93t-123.5 275zM969 63v1199q0 73 67 73h23q71 0 71 -73v-1199q0 -73 -71 -73h-23q-67 0 -67 73z" />
<glyph unicode="&#xb7;" horiz-adv-x="475" d="M111 485v25q0 54 28 83.5t82 29.5h29q55 0 83.5 -29.5t28.5 -83.5v-25q0 -108 -112 -108h-29q-110 0 -110 108z" />
<glyph unicode="&#xb8;" horiz-adv-x="1024" d="M313 -420l109 242q14 32 22.5 45t30 24t55.5 11h19q41 0 67.5 -19.5t26.5 -52.5v-10q0 -19 -15 -39t-50 -55l-162 -185q-26 -22 -71.5 -9t-31.5 48z" />
<glyph unicode="&#xb9;" horiz-adv-x="688" d="M80 1192q0 25 11 45.5t22.5 29t15.5 7.5q33 -33 78 -33q41 0 72.5 26t37.5 66h56q73 0 73 -78v-534h103q78 0 78 -70v-2q0 -65 -78 -65h-385q-76 0 -76 65v2q0 70 76 70h129v407q-40 -34 -96 -34q-46 0 -81.5 27.5t-35.5 70.5z" />
<glyph unicode="&#xba;" horiz-adv-x="978" d="M78 934q0 174 120 296t294 122q177 0 293 -119t116 -297q0 -175 -119 -295.5t-293 -120.5q-179 0 -295 118t-116 296zM242 936q0 -113 72 -190.5t178 -77.5t175.5 76t69.5 192q0 114 -70.5 191t-174.5 77q-107 0 -178.5 -77t-71.5 -191zM375 86v20q0 101 100 101h23 q102 0 102 -101v-20q0 -100 -102 -100h-23q-100 0 -100 100z" />
<glyph unicode="&#xbb;" horiz-adv-x="1062" d="M90 219v6q0 44 82 114t219 163q-301 197 -301 272v8q0 30 20 50t54 20h2q60 0 157 -82t165.5 -166.5t68.5 -103.5q0 -12 -43 -68t-102 -120t-129.5 -114.5t-116.5 -50.5h-2q-34 0 -54 21t-20 51zM555 248v4q0 69 266 250q-266 177 -266 248v2q0 28 18 46.5t48 18.5h2 q55 0 143.5 -74t151.5 -150t63 -93q0 -12 -39.5 -62t-93.5 -107.5t-118.5 -103t-106.5 -45.5h-2q-30 0 -48 19.5t-18 46.5z" />
<glyph unicode="&#xbc;" horiz-adv-x="1622" d="M80 1192q0 25 11 45.5t22.5 29t15.5 7.5q33 -33 78 -33q41 0 72.5 26t37.5 66h56q73 0 73 -78v-534h103q78 0 78 -70v-2q0 -65 -78 -65h-385q-76 0 -76 65v2q0 70 76 70h129v407q-40 -34 -96 -34q-46 0 -81.5 27.5t-35.5 70.5zM297 0q0 28 9 48t38 58q11 13 82.5 99 t170 204.5t171.5 208.5l541 680h20.5t34.5 -15.5t18 -41.5q0 -45 -51 -106q-12 -16 -81.5 -100.5t-170 -207t-170.5 -209.5l-538 -677q-6 -2 -23 -1t-34.5 17.5t-16.5 42.5zM958 254v29q2 29 37 88l226 315q23 40 49.5 52t89.5 12t89 -24t26 -83v-334h59q38 0 55 -14t17 -45 v-6q0 -32 -17 -46t-55 -14h-59v-114q0 -78 -70 -78h-8q-33 0 -50.5 21t-17.5 57v114h-274q-44 0 -70.5 19.5t-26.5 50.5zM1100 307h235l-2 328z" />
<glyph unicode="&#xbd;" horiz-adv-x="1622" d="M80 1192q0 25 11 45.5t22.5 29t15.5 7.5q33 -33 78 -33q41 0 72.5 26t37.5 66h56q73 0 73 -78v-534h103q78 0 78 -70v-2q0 -65 -78 -65h-385q-76 0 -76 65v2q0 70 76 70h129v407q-40 -34 -96 -34q-46 0 -81.5 27.5t-35.5 70.5zM240 0q0 28 9 48t38 58q11 13 82.5 99 t170 204.5t171.5 208.5l540 680h21t34.5 -15.5t18.5 -41.5q0 -45 -51 -106q-12 -16 -81.5 -100.5t-170 -207t-170.5 -209.5l-539 -677q-6 -2 -23 -1t-34 17.5t-16 42.5zM1004 84v35q0 54 16.5 95.5t51.5 72.5t69 51t90 45q10 5 37.5 18t37 18t30 16.5t28.5 19t19 19t14.5 24 t3.5 26.5q0 46 -34 74.5t-93 28.5q-67 0 -105.5 -37.5t-52.5 -95.5q-1 -2 -12 -2.5t-27.5 2.5t-32.5 9.5t-27 23t-11 40.5q0 72 76.5 131.5t203.5 59.5q129 0 201 -61t72 -169q0 -49 -16 -86.5t-50.5 -65t-64 -43.5t-80.5 -38q-8 -4 -33 -14t-34 -14t-29.5 -13.5t-29 -15 t-24 -15.5t-23 -18.5t-16.5 -19.5t-13.5 -23.5t-6.5 -26.5h350q78 0 78 -63v-6q0 -34 -18.5 -50t-59.5 -16h-385q-57 0 -78.5 18.5t-21.5 65.5z" />
<glyph unicode="&#xbe;" horiz-adv-x="1622" d="M41 737q0 24 9.5 41t23.5 25t28.5 12t25 3.5t11.5 -3.5q17 -57 65.5 -88t117.5 -31t112 30t43 79q0 50 -37 77t-104 27h-41q-76 0 -76 56v10q0 55 74 55h57q51 0 83 24t32 64q0 42 -41.5 69t-104.5 27q-124 0 -163 -108q-1 -1 -10.5 -2t-24.5 4t-28.5 12.5t-23.5 24.5 t-10 39q0 62 74.5 109.5t190.5 47.5q140 0 213 -52.5t73 -151.5q0 -118 -127 -156q144 -49 144 -186q0 -107 -79 -166.5t-224 -59.5q-127 0 -205 52t-78 116zM293 0q0 28 9 48t38 58q11 13 82.5 99t170 204.5t171.5 208.5l541 680h20.5t34.5 -15.5t18 -41.5q0 -45 -51 -106 q-12 -16 -81.5 -100.5t-170 -207t-170.5 -209.5l-538 -677q-6 -2 -23 -1t-34.5 17.5t-16.5 42.5zM958 254v29q2 29 37 88l226 315q23 40 49.5 52t89.5 12t89 -24t26 -83v-334h59q38 0 55 -14t17 -45v-6q0 -32 -17 -46t-55 -14h-59v-114q0 -78 -70 -78h-8q-33 0 -50.5 21 t-17.5 57v114h-274q-44 0 -70.5 19.5t-26.5 50.5zM1100 307h235l-2 328z" />
<glyph unicode="&#xbf;" horiz-adv-x="980" d="M53 326q0 68 22 121.5t57 88.5t77.5 65t85.5 58.5t78.5 60t58.5 78t25 105.5q6 43 84 43q40 0 63.5 -16.5t22.5 -50.5q-2 -77 -24 -137.5t-55 -98.5t-72.5 -68t-78.5 -53.5t-71 -47.5t-52 -57.5t-20 -76.5q0 -88 61 -139t170 -51q70 0 124.5 22t88 60t52.5 81.5t26 94.5 q2 1 16 2.5t35.5 -5.5t42 -17.5t35.5 -34t15 -54.5q0 -38 -16 -80.5t-52 -86t-85.5 -78.5t-125 -57t-162.5 -22q-195 0 -310.5 97.5t-115.5 253.5zM410 1204v25q0 110 112 110h29q111 0 111 -110v-25q0 -110 -111 -110h-29q-112 0 -112 110z" />
<glyph unicode="&#xc0;" horiz-adv-x="1290" d="M27 53l471 1217q13 34 44.5 50.5t100.5 16.5q70 0 101 -16t44 -51l476 -1217q3 -8 -13 -23t-45.5 -27.5t-58.5 -12.5q-88 0 -125 98l-117 301h-530l-117 -303q-35 -96 -117 -96q-28 0 -57 12t-44.5 27t-12.5 24zM307 1712q0 35 27 57.5t65 22.5q53 0 99 -35 q61 -40 123.5 -106t95.5 -115.5t28 -58.5q-7 -4 -27 -4.5t-59 8t-80.5 21t-91.5 35.5t-92 50q-88 54 -88 125zM426 559h426l-213 561z" />
<glyph unicode="&#xc1;" horiz-adv-x="1290" d="M27 53l471 1217q13 34 44.5 50.5t100.5 16.5q70 0 101 -16t44 -51l476 -1217q3 -8 -13 -23t-45.5 -27.5t-58.5 -12.5q-88 0 -125 98l-117 301h-530l-117 -303q-35 -96 -117 -96q-28 0 -57 12t-44.5 27t-12.5 24zM426 559h426l-213 561zM524 1477q-6 8 27.5 58t96 115.5 t122.5 106.5q53 35 98 35q39 0 66 -22.5t27 -57.5q0 -72 -87 -125q-42 -27 -92 -50t-91.5 -35.5t-80.5 -21t-59 -8t-27 4.5z" />
<glyph unicode="&#xc2;" horiz-adv-x="1290" d="M27 53l471 1217q13 34 44.5 50.5t100.5 16.5q70 0 101 -16t44 -51l476 -1217q3 -8 -13 -23t-45.5 -27.5t-58.5 -12.5q-88 0 -125 98l-117 301h-530l-117 -303q-35 -96 -117 -96q-28 0 -57 12t-44.5 27t-12.5 24zM356 1526q0 50 64 124t131.5 125t91.5 56q24 -5 91 -56 t130.5 -125t63.5 -124q0 -29 -17.5 -47.5t-46.5 -18.5q-42 0 -94 51t-129 168q-81 -120 -129.5 -169.5t-89.5 -49.5q-29 0 -47.5 18.5t-18.5 47.5zM426 559h426l-213 561z" />
<glyph unicode="&#xc3;" horiz-adv-x="1290" d="M27 53l471 1217q13 34 44.5 50.5t100.5 16.5q70 0 101 -16t44 -51l476 -1217q3 -8 -13 -23t-45.5 -27.5t-58.5 -12.5q-88 0 -125 98l-117 301h-530l-117 -303q-35 -96 -117 -96q-28 0 -57 12t-44.5 27t-12.5 24zM338 1542q0 79 46.5 138t113.5 59q56 0 135 -41t160 -113 q45 141 104 141q34 0 53 -19.5t19 -53.5q0 -77 -47.5 -136t-112.5 -59q-59 0 -138.5 40.5t-156.5 113.5q-49 -142 -104 -142q-33 0 -52.5 19.5t-19.5 52.5zM426 559h426l-213 561z" />
<glyph unicode="&#xc4;" horiz-adv-x="1290" d="M27 53l471 1217q13 34 44.5 50.5t100.5 16.5q70 0 101 -16t44 -51l476 -1217q3 -8 -13 -23t-45.5 -27.5t-58.5 -12.5q-88 0 -125 98l-117 301h-530l-117 -303q-35 -96 -117 -96q-28 0 -57 12t-44.5 27t-12.5 24zM334 1579q0 57 33 90t86 33q59 0 92 -32.5t33 -90.5 q0 -57 -32.5 -89t-88.5 -32t-89.5 32.5t-33.5 88.5zM426 559h426l-213 561zM709 1579q0 58 32 90.5t88 32.5t89.5 -32.5t33.5 -90.5q0 -56 -32.5 -88.5t-85.5 -32.5q-59 0 -92 32t-33 89z" />
<glyph unicode="&#xc5;" horiz-adv-x="1290" d="M27 53l471 1217q0 2 4 6q-50 29 -77.5 81t-27.5 118q0 105 70.5 175t177.5 70t176.5 -69.5t69.5 -175.5q0 -65 -29 -117.5t-80 -83.5l4 -4l478 -1217q3 -8 -13 -23t-45.5 -27.5t-58.5 -12.5q-88 0 -125 98l-117 301h-530l-117 -303q-35 -96 -117 -96q-28 0 -57 12 t-44.5 27t-12.5 24zM426 559h428l-215 565zM516 1475q0 -55 37 -94.5t90 -39.5q54 0 89.5 38.5t35.5 95.5q0 58 -36 97.5t-89 39.5q-54 0 -90.5 -39.5t-36.5 -97.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1757" d="M27 53l462 1192q27 80 136 80h952q98 0 98 -88v-12q0 -88 -98 -88h-746l144 -369h450q99 0 99 -84v-10q0 -86 -99 -86h-376l155 -402h385q97 0 97 -84v-10q0 -92 -97 -92h-418q-68 0 -102 22.5t-57 83.5l-107 283h-530l-117 -303q-38 -96 -117 -96q-28 0 -57 12t-44.5 27 t-12.5 24zM426 559h428l-215 561z" />
<glyph unicode="&#xc7;" horiz-adv-x="1345" d="M90 659q0 150 50 279t138 220t213.5 142.5t272.5 51.5q95 0 179 -20.5t141 -52t99 -70t61.5 -74t19.5 -64.5q0 -44 -30 -73.5t-60 -38.5t-33 -3q-56 97 -157.5 151t-217.5 54q-202 0 -329.5 -138t-127.5 -359q0 -219 130.5 -359.5t330.5 -140.5q134 0 238 62t145 169 q2 1 15 1.5t33 -8.5t38.5 -20.5t31.5 -34t13 -50.5q0 -31 -20.5 -70t-64 -81.5t-103.5 -77.5t-149 -58t-189 -23q-194 0 -346.5 87t-237 243.5t-84.5 355.5zM526 -420l109 242q14 32 22.5 45t30 24t55.5 11h19q41 0 67.5 -19.5t26.5 -52.5v-10q0 -19 -15 -39t-50 -55 l-162 -185q-26 -22 -71.5 -9t-31.5 48z" />
<glyph unicode="&#xc8;" horiz-adv-x="1202" d="M141 117v1089q0 54 31 86.5t82 32.5h768q98 0 98 -88v-12q0 -88 -98 -88h-670v-371h518q99 0 99 -84v-6q0 -88 -99 -88h-518v-402h682q96 0 96 -84v-10q0 -92 -96 -92h-780q-51 0 -82 31.5t-31 85.5zM354 1712q0 35 27 57.5t65 22.5q53 0 99 -35q61 -40 123.5 -106 t96 -115.5t28.5 -58.5q-7 -4 -27 -4.5t-59.5 8t-81 21t-91.5 35.5t-92 50q-88 54 -88 125z" />
<glyph unicode="&#xc9;" horiz-adv-x="1202" d="M141 117v1089q0 54 31 86.5t82 32.5h768q98 0 98 -88v-12q0 -88 -98 -88h-670v-371h518q99 0 99 -84v-6q0 -88 -99 -88h-518v-402h682q96 0 96 -84v-10q0 -92 -96 -92h-780q-51 0 -82 31.5t-31 85.5zM461 1477q-6 8 27.5 58t96 115.5t122.5 106.5q53 35 98 35 q39 0 65.5 -22.5t26.5 -57.5q0 -72 -86 -125q-42 -27 -92 -50t-91.5 -35.5t-80.5 -21t-59 -8t-27 4.5z" />
<glyph unicode="&#xca;" horiz-adv-x="1202" d="M141 117v1089q0 54 31 86.5t82 32.5h768q98 0 98 -88v-12q0 -88 -98 -88h-670v-371h518q99 0 99 -84v-6q0 -88 -99 -88h-518v-402h682q96 0 96 -84v-10q0 -92 -96 -92h-780q-51 0 -82 31.5t-31 85.5zM346 1526q0 50 64 124t131.5 125t91.5 56q24 -5 91 -56t130.5 -125 t63.5 -124q0 -29 -17.5 -47.5t-46.5 -18.5q-42 0 -94 51t-129 168q-81 -120 -129.5 -169.5t-89.5 -49.5q-29 0 -47.5 18.5t-18.5 47.5z" />
<glyph unicode="&#xcb;" horiz-adv-x="1202" d="M141 117v1089q0 54 31 86.5t82 32.5h768q98 0 98 -88v-12q0 -88 -98 -88h-670v-371h518q99 0 99 -84v-6q0 -88 -99 -88h-518v-402h682q96 0 96 -84v-10q0 -92 -96 -92h-780q-51 0 -82 31.5t-31 85.5zM324 1579q0 58 32.5 90.5t85.5 32.5q59 0 92 -32.5t33 -90.5 q0 -57 -32.5 -89t-88.5 -32t-89 32t-33 89zM698 1579q0 58 32.5 90.5t88.5 32.5t89.5 -32.5t33.5 -90.5q0 -56 -33 -88.5t-86 -32.5q-59 0 -92 32t-33 89z" />
<glyph unicode="&#xcc;" horiz-adv-x="530" d="M-47 1712q0 35 27 57.5t65 22.5q52 0 98 -35q61 -40 123.5 -106t96 -115.5t28.5 -58.5q-7 -4 -27 -4.5t-59 8t-80.5 21t-91.5 35.5t-92 50q-88 54 -88 125zM160 88v1149q0 96 90 96h29q92 0 92 -96v-1149q0 -96 -92 -96h-29q-90 0 -90 96z" />
<glyph unicode="&#xcd;" horiz-adv-x="530" d="M137 1477q-6 8 27.5 58t96 115.5t122.5 106.5q53 35 98 35q39 0 65.5 -22.5t26.5 -57.5q0 -72 -86 -125q-42 -27 -92 -50t-91.5 -35.5t-80.5 -21t-59 -8t-27 4.5zM160 88v1149q0 96 90 96h29q92 0 92 -96v-1149q0 -96 -92 -96h-29q-90 0 -90 96z" />
<glyph unicode="&#xce;" horiz-adv-x="530" d="M-23 1526q0 50 64 124t131.5 125t91.5 56q24 -5 91 -56t130.5 -125t63.5 -124q0 -29 -17.5 -47.5t-46.5 -18.5q-42 0 -94 51t-129 168q-81 -120 -129.5 -169.5t-89.5 -49.5q-29 0 -47.5 18.5t-18.5 47.5zM160 88v1149q0 96 90 96h29q92 0 92 -96v-1149q0 -96 -92 -96h-29 q-90 0 -90 96z" />
<glyph unicode="&#xcf;" horiz-adv-x="530" d="M-45 1579q0 57 32.5 90t86.5 33q59 0 92 -32.5t33 -90.5q0 -57 -32.5 -89t-88.5 -32t-89.5 32.5t-33.5 88.5zM160 88v1149q0 96 90 96h29q92 0 92 -96v-1149q0 -96 -92 -96h-29q-90 0 -90 96zM330 1579q0 58 32.5 90.5t88.5 32.5t89 -32.5t33 -90.5q0 -56 -32.5 -88.5 t-85.5 -32.5q-59 0 -92 32t-33 89z" />
<glyph unicode="&#xd0;" horiz-adv-x="1476" d="M27 655v19q0 71 79 71h109v461q0 53 32.5 86t80.5 33h331q337 0 532 -176t195 -483q0 -309 -198.5 -487.5t-538.5 -178.5h-317q-49 0 -83 34.5t-34 82.5v465h-109q-79 0 -79 73zM424 186h238q246 0 380 125t134 353q0 225 -134.5 350t-377.5 125h-240v-394h358 q82 0 82 -71v-19q0 -73 -82 -73h-358v-396z" />
<glyph unicode="&#xd1;" horiz-adv-x="1396" d="M141 88v1126q0 55 26 87t75 32h34q44 0 69 -17.5t58 -62.5l656 -929v913q0 96 88 96h24q86 0 86 -96v-1126q0 -56 -26 -87.5t-72 -31.5h-16q-44 0 -65 13t-48 50l-690 977v-944q0 -96 -90 -96h-23q-86 0 -86 96zM383 1542q0 79 46.5 138t113.5 59q56 0 135 -41t160 -113 q45 141 104 141q34 0 53 -19.5t19 -53.5q0 -77 -47.5 -136t-112.5 -59q-59 0 -138.5 40.5t-156.5 113.5q-49 -142 -104 -142q-33 0 -52.5 19.5t-19.5 52.5z" />
<glyph unicode="&#xd2;" d="M88 659q0 200 86 358.5t239.5 246.5t346.5 88q296 0 483 -192t187 -496q0 -200 -85.5 -358t-239 -245.5t-347.5 -87.5q-195 0 -348 87t-237.5 243.5t-84.5 355.5zM301 664q0 -221 129 -362.5t330 -141.5t329 139t128 360t-129 363.5t-330 142.5q-202 0 -329.5 -139.5 t-127.5 -361.5zM438 1712q0 35 27 57.5t65 22.5q53 0 99 -35q61 -40 123.5 -106t96 -115.5t28.5 -58.5q-7 -4 -27 -4.5t-59.5 8t-81 21t-91.5 35.5t-92 50q-88 54 -88 125z" />
<glyph unicode="&#xd3;" d="M88 659q0 200 86 358.5t239.5 246.5t346.5 88q296 0 483 -192t187 -496q0 -200 -85.5 -358t-239 -245.5t-347.5 -87.5q-195 0 -348 87t-237.5 243.5t-84.5 355.5zM301 664q0 -221 129 -362.5t330 -141.5t329 139t128 360t-129 363.5t-330 142.5q-202 0 -329.5 -139.5 t-127.5 -361.5zM631 1477q-6 8 27.5 58t96 115.5t122.5 106.5q53 35 98 35q39 0 65.5 -22.5t26.5 -57.5q0 -72 -86 -125q-42 -27 -92 -50t-91.5 -35.5t-80.5 -21t-59 -8t-27 4.5z" />
<glyph unicode="&#xd4;" d="M88 659q0 200 86 358.5t239.5 246.5t346.5 88q296 0 483 -192t187 -496q0 -200 -85.5 -358t-239 -245.5t-347.5 -87.5q-195 0 -348 87t-237.5 243.5t-84.5 355.5zM301 664q0 -221 129 -362.5t330 -141.5t329 139t128 360t-129 363.5t-330 142.5q-202 0 -329.5 -139.5 t-127.5 -361.5zM471 1526q0 50 64 124t131.5 125t91.5 56q23 -5 90.5 -56t130.5 -125t63 -124q0 -30 -17 -48t-46 -18q-42 0 -94 51t-129 168q-81 -120 -129.5 -169.5t-89.5 -49.5q-29 0 -47.5 18.5t-18.5 47.5z" />
<glyph unicode="&#xd5;" d="M88 659q0 200 86 358.5t239.5 246.5t346.5 88q296 0 483 -192t187 -496q0 -200 -85.5 -358t-239 -245.5t-347.5 -87.5q-195 0 -348 87t-237.5 243.5t-84.5 355.5zM301 664q0 -221 129 -362.5t330 -141.5t329 139t128 360t-129 363.5t-330 142.5q-202 0 -329.5 -139.5 t-127.5 -361.5zM444 1542q0 79 46.5 138t113.5 59q56 0 135 -41t160 -113q19 61 46.5 101t58.5 40q34 0 52.5 -19.5t18.5 -53.5q0 -77 -47.5 -136t-112.5 -59q-59 0 -137.5 40.5t-156.5 113.5q-49 -142 -105 -142q-33 0 -52.5 19.5t-19.5 52.5z" />
<glyph unicode="&#xd6;" d="M88 659q0 200 86 358.5t239.5 246.5t346.5 88q296 0 483 -192t187 -496q0 -200 -85.5 -358t-239 -245.5t-347.5 -87.5q-195 0 -348 87t-237.5 243.5t-84.5 355.5zM301 664q0 -221 129 -362.5t330 -141.5t329 139t128 360t-129 363.5t-330 142.5q-202 0 -329.5 -139.5 t-127.5 -361.5zM449 1579q0 58 32.5 90.5t85.5 32.5q59 0 92 -32.5t33 -90.5q0 -57 -32.5 -89t-88.5 -32t-89 32t-33 89zM823 1579q0 58 32.5 90.5t88.5 32.5t89.5 -32.5t33.5 -90.5q0 -56 -33 -88.5t-86 -32.5q-59 0 -92 32t-33 89z" />
<glyph unicode="&#xd7;" horiz-adv-x="1118" d="M195 170q-56 53 8 117l246 250l-246 245q-63 64 -8 119l12 12q56 59 119 -10l237 -250l236 250q66 69 121 10l10 -12q58 -55 -6 -119l-246 -245l246 -250q63 -63 6 -117l-12 -12q-20 -25 -53 -25t-66 35l-236 250l-237 -250q-62 -67 -119 -10z" />
<glyph unicode="&#xd8;" d="M86 -2q0 43 49 100l103 113q-148 187 -148 448q0 194 84.5 352t238.5 249.5t349 91.5q224 0 399 -125l158 174q5 6 26.5 -3.5t42.5 -32.5t21 -50q0 -46 -47 -101l-88 -96q156 -189 156 -454q0 -194 -84.5 -351.5t-238.5 -248.5t-349 -91q-237 0 -408 129l-172 -190 q-6 -6 -28 3.5t-43 32.5t-21 50zM291 664q0 -177 86 -308l653 725q-120 84 -272 84q-211 0 -339 -140.5t-128 -360.5zM481 246q119 -86 281 -86q210 0 338.5 140.5t128.5 358.5q0 182 -92 316z" />
<glyph unicode="&#xd9;" horiz-adv-x="1331" d="M125 539v698q0 47 23.5 71.5t66.5 24.5h29q92 0 92 -96v-692q0 -189 83 -286t247 -97q167 0 248 95.5t81 289.5v690q0 96 90 96h33q88 0 88 -96v-694q0 -277 -139 -422.5t-403 -145.5t-401.5 143.5t-137.5 420.5zM358 1696q0 34 27.5 57t65.5 23q52 0 98 -35 q61 -40 123.5 -106.5t96 -116t28.5 -58.5q-7 -4 -27 -4.5t-59.5 8.5t-81 21.5t-91.5 35.5t-92 50q-88 54 -88 125z" />
<glyph unicode="&#xda;" horiz-adv-x="1331" d="M125 539v698q0 47 23.5 71.5t66.5 24.5h29q92 0 92 -96v-692q0 -189 83 -286t247 -97q167 0 248 95.5t81 289.5v690q0 96 90 96h33q88 0 88 -96v-694q0 -277 -139 -422.5t-403 -145.5t-401.5 143.5t-137.5 420.5zM543 1460q-6 8 27 58.5t95.5 116.5t122.5 106 q53 35 99 35q39 0 65.5 -22.5t26.5 -57.5q0 -72 -86 -125q-42 -27 -92 -50t-91.5 -35.5t-80.5 -21.5t-59 -8.5t-27 4.5z" />
<glyph unicode="&#xdb;" horiz-adv-x="1331" d="M125 539v698q0 47 23.5 71.5t66.5 24.5h29q92 0 92 -96v-692q0 -189 83 -286t247 -97q167 0 248 95.5t81 289.5v690q0 96 90 96h33q88 0 88 -96v-694q0 -277 -139 -422.5t-403 -145.5t-401.5 143.5t-137.5 420.5zM375 1526q0 50 64 124t131.5 125t91.5 56q23 -5 90.5 -56 t130.5 -125t63 -124q0 -30 -17 -48t-46 -18q-42 0 -94.5 51.5t-129.5 167.5q-81 -120 -129.5 -169.5t-89.5 -49.5q-29 0 -47 18.5t-18 47.5z" />
<glyph unicode="&#xdc;" horiz-adv-x="1331" d="M125 539v698q0 47 23.5 71.5t66.5 24.5h29q92 0 92 -96v-692q0 -189 83 -286t247 -97q167 0 248 95.5t81 289.5v690q0 96 90 96h33q88 0 88 -96v-694q0 -277 -139 -422.5t-403 -145.5t-401.5 143.5t-137.5 420.5zM352 1579q0 57 33 90t86 33q59 0 92 -32.5t33 -90.5 q0 -57 -32.5 -89t-88.5 -32t-89.5 32.5t-33.5 88.5zM727 1579q0 58 32.5 90.5t88.5 32.5t89.5 -32.5t33.5 -90.5q0 -56 -33 -88.5t-86 -32.5q-59 0 -92 32t-33 89z" />
<glyph unicode="&#xdd;" horiz-adv-x="1173" d="M12 1247q-2 5 1.5 14.5t14 22t25.5 23.5t39.5 18.5t52.5 7.5q63 0 103 -73l340 -541l340 541q45 73 104 73q26 0 49 -7.5t38 -19t25 -24t14 -22t1 -13.5l-467 -704v-455q0 -96 -92 -96h-29q-90 0 -90 96v455zM451 1460q-6 8 27 58.5t95.5 116.5t122.5 106q53 35 99 35 q39 0 65.5 -22.5t26.5 -57.5q0 -72 -86 -125q-42 -27 -92 -50t-91.5 -35.5t-80.5 -21.5t-59 -8.5t-27 4.5z" />
<glyph unicode="&#xde;" horiz-adv-x="1177" d="M141 88v1149q0 96 88 96h31q92 0 92 -96v-150h242q248 0 381 -108t133 -309q0 -206 -132.5 -314t-389.5 -108h-234v-160q0 -96 -92 -96h-31q-88 0 -88 96zM352 418h240q152 0 229.5 63.5t77.5 184.5q0 247 -303 247h-244v-495z" />
<glyph unicode="&#xdf;" horiz-adv-x="1130" d="M125 86v860q0 237 117.5 356t339.5 119q191 0 305 -91.5t114 -242.5q0 -264 -292 -301q370 -99 370 -430q0 -161 -111 -272t-272 -111q-133 0 -207.5 46.5t-74.5 111.5q0 26 14 50.5t28.5 36t18.5 9.5q37 -42 82 -65t117 -23q85 0 147 64t62 153q0 102 -61.5 174.5 t-215.5 112.5q-65 19 -93 47.5t-28 81.5q0 24 10.5 49t20.5 39t12 14q139 -2 209 44t70 147q0 89 -61 139.5t-166 50.5q-252 0 -252 -299v-870q0 -96 -92 -96h-21q-90 0 -90 96z" />
<glyph unicode="&#xe0;" horiz-adv-x="1071" d="M68 258q0 168 162 248t499 86h27v20q0 113 -63 172.5t-181 59.5q-105 0 -175.5 -46.5t-107.5 -121.5q0 -1 -13 0t-31.5 6t-36.5 14.5t-31 30t-13 47.5q0 37 27.5 77t78 76t134 59.5t182.5 23.5q208 0 319 -102.5t111 -291.5v-604q0 -8 -23 -15t-50 -7q-122 0 -125 157 q-57 -80 -150 -125t-205 -45q-156 0 -245.5 76.5t-89.5 204.5zM252 1372q0 35 27 57.5t65 22.5q52 0 98 -35q61 -40 123.5 -106t96 -115.5t28.5 -58.5q-7 -4 -27 -4.5t-59 8t-80.5 21t-91.5 35.5t-92 50q-88 54 -88 125zM266 266q0 -66 50 -104.5t137 -38.5 q132 0 217.5 74.5t85.5 191.5v76l-56 -2q-221 -8 -327.5 -54t-106.5 -143z" />
<glyph unicode="&#xe1;" horiz-adv-x="1071" d="M68 258q0 168 162 248t499 86h27v20q0 113 -63 172.5t-181 59.5q-105 0 -175.5 -46.5t-107.5 -121.5q0 -1 -13 0t-31.5 6t-36.5 14.5t-31 30t-13 47.5q0 37 27.5 77t78 76t134 59.5t182.5 23.5q208 0 319 -102.5t111 -291.5v-604q0 -8 -23 -15t-50 -7q-122 0 -125 157 q-57 -80 -150 -125t-205 -45q-156 0 -245.5 76.5t-89.5 204.5zM266 266q0 -66 50 -104.5t137 -38.5q132 0 217.5 74.5t85.5 191.5v76l-56 -2q-221 -8 -327.5 -54t-106.5 -143zM379 1137q-6 8 27.5 58t96 115.5t122.5 106.5q53 35 98 35q39 0 65.5 -22.5t26.5 -57.5 q0 -72 -86 -125q-42 -27 -92 -50t-91.5 -35.5t-80.5 -21t-59 -8t-27 4.5z" />
<glyph unicode="&#xe2;" horiz-adv-x="1071" d="M68 258q0 168 162 248t499 86h27v20q0 113 -63 172.5t-181 59.5q-105 0 -175.5 -46.5t-107.5 -121.5q0 -1 -13 0t-31.5 6t-36.5 14.5t-31 30t-13 47.5q0 37 27.5 77t78 76t134 59.5t182.5 23.5q208 0 319 -102.5t111 -291.5v-604q0 -8 -23 -15t-50 -7q-122 0 -125 157 q-57 -80 -150 -125t-205 -45q-156 0 -245.5 76.5t-89.5 204.5zM248 1186q0 50 64 124t131.5 125t91.5 56q23 -5 90.5 -56t130.5 -125t63 -124q0 -30 -17 -48t-46 -18q-42 0 -94.5 51.5t-129.5 167.5q-81 -120 -129.5 -169.5t-89.5 -49.5q-29 0 -47 18.5t-18 47.5zM266 266 q0 -66 50 -104.5t137 -38.5q132 0 217.5 74.5t85.5 191.5v76l-56 -2q-221 -8 -327.5 -54t-106.5 -143z" />
<glyph unicode="&#xe3;" horiz-adv-x="1071" d="M68 258q0 168 162 248t499 86h27v20q0 113 -63 172.5t-181 59.5q-105 0 -175.5 -46.5t-107.5 -121.5q0 -1 -13 0t-31.5 6t-36.5 14.5t-31 30t-13 47.5q0 37 27.5 77t78 76t134 59.5t182.5 23.5q208 0 319 -102.5t111 -291.5v-604q0 -8 -23 -15t-50 -7q-122 0 -125 157 q-57 -80 -150 -125t-205 -45q-156 0 -245.5 76.5t-89.5 204.5zM217 1202q0 79 46.5 138t113.5 59q56 0 135 -41t160 -113q45 141 104 141q34 0 53 -19.5t19 -53.5q0 -77 -47.5 -136t-112.5 -59q-59 0 -138.5 40.5t-156.5 113.5q-49 -142 -104 -142q-33 0 -52.5 19.5 t-19.5 52.5zM266 266q0 -66 50 -104.5t137 -38.5q132 0 217.5 74.5t85.5 191.5v76l-56 -2q-221 -8 -327.5 -54t-106.5 -143z" />
<glyph unicode="&#xe4;" horiz-adv-x="1071" d="M68 258q0 168 162 248t499 86h27v20q0 113 -63 172.5t-181 59.5q-105 0 -175.5 -46.5t-107.5 -121.5q0 -1 -13 0t-31.5 6t-36.5 14.5t-31 30t-13 47.5q0 37 27.5 77t78 76t134 59.5t182.5 23.5q208 0 319 -102.5t111 -291.5v-604q0 -8 -23 -15t-50 -7q-122 0 -125 157 q-57 -80 -150 -125t-205 -45q-156 0 -245.5 76.5t-89.5 204.5zM221 1239q0 57 32.5 90t86.5 33q59 0 92 -32.5t33 -90.5q0 -57 -32.5 -89t-88.5 -32t-89.5 32.5t-33.5 88.5zM266 266q0 -66 50 -104.5t137 -38.5q132 0 217.5 74.5t85.5 191.5v76l-56 -2q-221 -8 -327.5 -54 t-106.5 -143zM596 1239q0 58 32.5 90.5t88.5 32.5t89.5 -32.5t33.5 -90.5q0 -56 -33 -88.5t-86 -32.5q-59 0 -92 32t-33 89z" />
<glyph unicode="&#xe5;" horiz-adv-x="1071" d="M68 258q0 168 162 248t499 86h27v20q0 113 -63 172.5t-181 59.5q-105 0 -175.5 -46.5t-107.5 -121.5q0 -1 -13 0t-31.5 6t-36.5 14.5t-31 30t-13 47.5q0 37 27.5 77t78 76t134 59.5t182.5 23.5q208 0 319 -102.5t111 -291.5v-604q0 -8 -23 -15t-50 -7q-122 0 -125 157 q-57 -80 -150 -125t-205 -45q-156 0 -245.5 76.5t-89.5 204.5zM266 266q0 -66 50 -104.5t137 -38.5q132 0 217.5 74.5t85.5 191.5v76l-56 -2q-221 -8 -327.5 -54t-106.5 -143zM283 1362q0 105 70 175.5t177 70.5q108 0 177 -70t69 -176q0 -103 -70.5 -173.5t-177.5 -70.5 q-108 0 -176.5 69.5t-68.5 174.5zM403 1362q0 -56 36.5 -94.5t88.5 -38.5q55 0 91 38t36 95q0 58 -36.5 97.5t-90.5 39.5t-89.5 -39t-35.5 -98z" />
<glyph unicode="&#xe6;" horiz-adv-x="1775" d="M68 258q0 168 162 248t499 86h27v20q0 113 -63 173.5t-181 60.5q-105 0 -175 -47.5t-108 -122.5q0 -1 -13 0t-31.5 6t-36.5 14.5t-31 30t-13 47.5q0 37 27 77t76.5 76t131 59.5t177.5 23.5q256 0 352 -181q63 86 158 133.5t213 47.5q213 0 344 -137t131 -359v-16 q-2 -33 -15 -46.5t-46 -13.5h-729q10 -136 103 -220.5t233 -84.5q212 0 311 164q2 4 27 -3.5t51 -33t26 -63.5q0 -33 -28.5 -71.5t-79.5 -73t-133.5 -57t-178.5 -22.5q-156 0 -267 62.5t-167 177.5q-54 -107 -164.5 -171.5t-250.5 -64.5q-157 0 -247.5 77t-90.5 204z M266 266q0 -66 50 -104.5t137 -38.5q132 0 217.5 74.5t85.5 191.5v76l-56 -2q-221 -8 -327.5 -54t-106.5 -143zM928 575h588q-8 128 -82 204.5t-195 76.5q-122 0 -207 -78.5t-104 -202.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="1056" d="M66 494q0 224 145 370t367 146q103 0 188 -29.5t133.5 -73t74 -88.5t25.5 -82q0 -24 -12 -43t-28.5 -28t-34.5 -16t-30 -6t-15 3q-80 193 -297 193q-139 0 -231.5 -98t-92.5 -248q0 -152 92 -251.5t232 -99.5q217 0 297 193q3 2 15 3t30 -5.5t34.5 -16t28.5 -29t12 -44.5 q0 -28 -15.5 -62.5t-49.5 -71.5t-81.5 -67.5t-119.5 -50t-155 -19.5q-223 0 -367.5 147t-144.5 374zM365 -420l108 242q11 24 17 35t18.5 23.5t30 17t43.5 4.5h18q41 0 67.5 -19.5t26.5 -52.5v-10q0 -19 -15 -39t-50 -55l-162 -185q-26 -22 -71 -9t-31 48z" />
<glyph unicode="&#xe8;" horiz-adv-x="1105" d="M66 487q0 148 64.5 267.5t180 187.5t258.5 68q213 0 343 -137t130 -359v-16q0 -33 -13 -46.5t-48 -13.5h-727q10 -136 103 -220.5t233 -84.5q214 0 309 164q4 4 29 -3.5t51.5 -33t26.5 -63.5q0 -75 -118 -149.5t-302 -74.5q-233 0 -376.5 142t-143.5 372zM256 575h590 q-8 129 -82.5 205t-196.5 76q-121 0 -206.5 -78.5t-104.5 -202.5zM256 1372q0 35 27 57.5t65 22.5q52 0 98 -35q61 -40 123.5 -106t96 -115.5t28.5 -58.5q-7 -4 -27 -4.5t-59 8t-80.5 21t-91.5 35.5t-92 50q-88 54 -88 125z" />
<glyph unicode="&#xe9;" horiz-adv-x="1105" d="M66 487q0 148 64.5 267.5t180 187.5t258.5 68q213 0 343 -137t130 -359v-16q0 -33 -13 -46.5t-48 -13.5h-727q10 -136 103 -220.5t233 -84.5q214 0 309 164q4 4 29 -3.5t51.5 -33t26.5 -63.5q0 -75 -118 -149.5t-302 -74.5q-233 0 -376.5 142t-143.5 372zM256 575h590 q-8 129 -82.5 205t-196.5 76q-121 0 -206.5 -78.5t-104.5 -202.5zM412 1137q-6 8 27 58t95.5 115.5t122.5 106.5q53 35 99 35q39 0 65.5 -22.5t26.5 -57.5q0 -72 -86 -125q-42 -27 -92 -50t-91.5 -35.5t-80.5 -21t-59 -8t-27 4.5z" />
<glyph unicode="&#xea;" horiz-adv-x="1105" d="M66 487q0 148 64.5 267.5t180 187.5t258.5 68q213 0 343 -137t130 -359v-16q0 -33 -13 -46.5t-48 -13.5h-727q10 -136 103 -220.5t233 -84.5q214 0 309 164q4 4 29 -3.5t51.5 -33t26.5 -63.5q0 -75 -118 -149.5t-302 -74.5q-233 0 -376.5 142t-143.5 372zM256 575h590 q-8 129 -82.5 205t-196.5 76q-121 0 -206.5 -78.5t-104.5 -202.5zM281 1186q0 50 63.5 124t131 125t91.5 56q24 -5 91 -56t130.5 -125t63.5 -124q0 -29 -17.5 -47.5t-46.5 -18.5q-42 0 -94 51t-129 168q-81 -120 -129.5 -169.5t-89.5 -49.5q-29 0 -47 18.5t-18 47.5z" />
<glyph unicode="&#xeb;" horiz-adv-x="1105" d="M66 487q0 148 64.5 267.5t180 187.5t258.5 68q213 0 343 -137t130 -359v-16q0 -33 -13 -46.5t-48 -13.5h-727q10 -136 103 -220.5t233 -84.5q214 0 309 164q4 4 29 -3.5t51.5 -33t26.5 -63.5q0 -75 -118 -149.5t-302 -74.5q-233 0 -376.5 142t-143.5 372zM256 575h590 q-8 129 -82.5 205t-196.5 76q-121 0 -206.5 -78.5t-104.5 -202.5zM258 1239q0 57 33 90t86 33q59 0 92 -32.5t33 -90.5q0 -57 -32.5 -89t-88.5 -32t-89.5 32.5t-33.5 88.5zM633 1239q0 58 32.5 90.5t88.5 32.5t89.5 -32.5t33.5 -90.5q0 -56 -33 -88.5t-86 -32.5 q-59 0 -92 32t-33 89z" />
<glyph unicode="&#xec;" horiz-adv-x="471" d="M-70 1372q0 34 27.5 57t65.5 23q52 0 98 -35q61 -40 123.5 -106t96 -115.5t28.5 -58.5q-7 -4 -27 -4.5t-59.5 8t-81 21t-91.5 35.5t-92 50q-88 54 -88 125zM133 88v809q0 98 92 98h21q43 0 66.5 -22t23.5 -76v-809q0 -96 -90 -96h-21q-92 0 -92 96z" />
<glyph unicode="&#xed;" horiz-adv-x="471" d="M115 1137q-6 8 27 58t95.5 115.5t122.5 106.5q53 35 99 35q39 0 65.5 -22.5t26.5 -57.5q0 -72 -86 -125q-42 -27 -92 -50t-91.5 -35.5t-80.5 -21t-59 -8t-27 4.5zM133 88v809q0 98 92 98h21q43 0 66.5 -22t23.5 -76v-809q0 -96 -90 -96h-21q-92 0 -92 96z" />
<glyph unicode="&#xee;" horiz-adv-x="471" d="M-45 1186q0 50 64 124t131.5 125t91.5 56q23 -5 90.5 -56t130.5 -125t63 -124q0 -30 -17 -48t-46 -18q-42 0 -94 51t-129 168q-81 -119 -129.5 -169t-90.5 -50q-29 0 -47 18.5t-18 47.5zM133 88v809q0 98 92 98h21q43 0 66.5 -22t23.5 -76v-809q0 -96 -90 -96h-21 q-92 0 -92 96z" />
<glyph unicode="&#xef;" horiz-adv-x="471" d="M-68 1239q0 57 33 90t86 33q59 0 92 -32.5t33 -90.5q0 -57 -32.5 -89t-88.5 -32t-89.5 32.5t-33.5 88.5zM133 88v809q0 98 92 98h21q43 0 66.5 -22t23.5 -76v-809q0 -96 -90 -96h-21q-92 0 -92 96zM307 1239q0 58 32.5 90.5t88.5 32.5t89.5 -32.5t33.5 -90.5 q0 -56 -33 -88.5t-86 -32.5q-59 0 -92 32t-33 89z" />
<glyph unicode="&#xf0;" horiz-adv-x="1187" d="M78 449q0 205 133 331.5t348 126.5q110 0 202.5 -48t141.5 -118q-46 253 -244 402l-231 -129q-76 -43 -106 22l-5 11q-31 60 41 102l152 82q-142 60 -303 72q-2 6 -1 36t42.5 64.5t107.5 34.5q176 0 353 -107l215 119q68 37 106 -25l6 -6q40 -58 -43 -102l-153 -82 q123 -113 195.5 -278t72.5 -377q0 -282 -145.5 -444.5t-384.5 -162.5q-227 0 -363.5 127.5t-136.5 348.5zM276 451q0 -146 83 -229t221 -83q150 0 235.5 105.5t85.5 271.5v68q-38 67 -124.5 117.5t-182.5 50.5q-153 0 -235.5 -79t-82.5 -222z" />
<glyph unicode="&#xf1;" horiz-adv-x="1148" d="M125 88v885q0 3 9.5 8t27.5 9.5t37 4.5q120 0 127 -159q118 174 327 174q188 0 284.5 -115.5t96.5 -306.5v-500q0 -96 -90 -96h-22q-91 0 -91 96v475q0 125 -60 196.5t-167 71.5q-94 0 -166 -46.5t-110 -125.5v-571q0 -96 -90 -96h-23q-90 0 -90 96zM254 1202 q0 79 46.5 138t113.5 59q56 0 135 -41t160 -113q45 141 104 141q34 0 53 -19.5t19 -53.5q0 -77 -47.5 -136t-112.5 -59q-59 0 -138.5 40.5t-156.5 113.5q-49 -142 -104 -142q-33 0 -52.5 19.5t-19.5 52.5z" />
<glyph unicode="&#xf2;" horiz-adv-x="1163" d="M66 494q0 226 145 371t373 145q225 0 369.5 -146.5t144.5 -374.5q0 -226 -144.5 -371t-371.5 -145q-228 0 -372 146t-144 375zM252 494q0 -152 94.5 -253.5t235.5 -101.5t234 99.5t93 250.5q0 154 -92.5 254.5t-234.5 100.5t-236 -99t-94 -251zM266 1372q0 35 27 57.5 t65 22.5q53 0 99 -35q61 -40 123.5 -106t96 -115.5t28.5 -58.5q-7 -4 -27 -4.5t-59.5 8t-81 21t-91.5 35.5t-92 50q-88 54 -88 125z" />
<glyph unicode="&#xf3;" horiz-adv-x="1163" d="M66 494q0 226 145 371t373 145q225 0 369.5 -146.5t144.5 -374.5q0 -226 -144.5 -371t-371.5 -145q-228 0 -372 146t-144 375zM252 494q0 -152 94.5 -253.5t235.5 -101.5t234 99.5t93 250.5q0 154 -92.5 254.5t-234.5 100.5t-236 -99t-94 -251zM467 1137q-6 8 27.5 58 t96 115.5t122.5 106.5q53 35 98 35q39 0 65.5 -22.5t26.5 -57.5q0 -72 -86 -125q-42 -27 -92 -50t-91.5 -35.5t-80.5 -21t-59 -8t-27 4.5z" />
<glyph unicode="&#xf4;" horiz-adv-x="1163" d="M66 494q0 226 145 371t373 145q225 0 369.5 -146.5t144.5 -374.5q0 -226 -144.5 -371t-371.5 -145q-228 0 -372 146t-144 375zM252 494q0 -152 94.5 -253.5t235.5 -101.5t234 99.5t93 250.5q0 154 -92.5 254.5t-234.5 100.5t-236 -99t-94 -251zM299 1186q0 50 64 124 t131.5 125t91.5 56q23 -5 90.5 -56t130.5 -125t63 -124q0 -30 -17 -48t-46 -18q-42 0 -94 51t-129 168q-81 -120 -129.5 -169.5t-89.5 -49.5q-29 0 -47.5 18.5t-18.5 47.5z" />
<glyph unicode="&#xf5;" horiz-adv-x="1163" d="M66 494q0 226 145 371t373 145q225 0 369.5 -146.5t144.5 -374.5q0 -226 -144.5 -371t-371.5 -145q-228 0 -372 146t-144 375zM252 494q0 -152 94.5 -253.5t235.5 -101.5t234 99.5t93 250.5q0 154 -92.5 254.5t-234.5 100.5t-236 -99t-94 -251zM272 1202q0 79 46.5 138 t113.5 59q56 0 135 -41t160 -113q45 141 104 141q34 0 53 -19.5t19 -53.5q0 -77 -47.5 -136t-112.5 -59q-59 0 -137.5 40.5t-156.5 113.5q-49 -142 -105 -142q-33 0 -52.5 19.5t-19.5 52.5z" />
<glyph unicode="&#xf6;" horiz-adv-x="1163" d="M66 494q0 226 145 371t373 145q225 0 369.5 -146.5t144.5 -374.5q0 -226 -144.5 -371t-371.5 -145q-228 0 -372 146t-144 375zM252 494q0 -152 94.5 -253.5t235.5 -101.5t234 99.5t93 250.5q0 154 -92.5 254.5t-234.5 100.5t-236 -99t-94 -251zM276 1239q0 57 33 90 t86 33q59 0 92 -32.5t33 -90.5q0 -57 -32.5 -89t-88.5 -32t-89.5 32.5t-33.5 88.5zM651 1239q0 58 32.5 90.5t88.5 32.5t89.5 -32.5t33.5 -90.5q0 -56 -33 -88.5t-86 -32.5q-59 0 -92 32t-33 89z" />
<glyph unicode="&#xf7;" horiz-adv-x="1228" d="M154 535v14q0 78 88 78h743q88 0 88 -78v-14q0 -78 -88 -78h-743q-88 0 -88 78zM496 182v23q0 104 104 104h25q106 0 106 -104v-23q0 -104 -106 -104h-25q-104 0 -104 104zM496 879v22q0 50 26.5 77.5t77.5 27.5h25q52 0 79 -27.5t27 -77.5v-22q0 -105 -106 -105h-25 q-104 0 -104 105z" />
<glyph unicode="&#xf8;" horiz-adv-x="1163" d="M63 -18q0 39 48 92l77 86q-110 138 -110 329q0 107 39.5 203.5t107.5 166t163.5 110.5t201.5 41q157 0 291 -90l141 157q5 5 26 -3.5t41.5 -28.5t20.5 -44q0 -40 -45 -90l-80 -86q113 -141 113 -331q0 -107 -39.5 -203.5t-107.5 -166t-163.5 -110.5t-201.5 -41 q-162 0 -291 90l-143 -157q-5 -5 -26.5 3.5t-42 28.5t-20.5 44zM262 492q0 -111 60 -203l448 493q-81 62 -182 62q-141 0 -233.5 -100t-92.5 -252zM406 203q84 -64 182 -64q140 0 232.5 101t92.5 252q0 118 -59 204z" />
<glyph unicode="&#xf9;" horiz-adv-x="1148" d="M117 397v500q0 98 88 98h22q43 0 66.5 -22t23.5 -76v-475q0 -128 62 -199t174 -71q90 0 160.5 47t109.5 127v571q0 98 88 98h23q43 0 66.5 -22t23.5 -76v-883q0 -3 -9.5 -8t-27 -9.5t-35.5 -4.5q-124 0 -129 155q-120 -172 -327 -172q-188 0 -283.5 114.5t-95.5 307.5z M254 1372q0 35 27 57.5t65 22.5q52 0 98 -35q61 -40 123.5 -106t96 -115.5t28.5 -58.5q-7 -4 -27 -4.5t-59 8t-80.5 21t-91.5 35.5t-92 50q-88 54 -88 125z" />
<glyph unicode="&#xfa;" horiz-adv-x="1148" d="M117 397v500q0 98 88 98h22q43 0 66.5 -22t23.5 -76v-475q0 -128 62 -199t174 -71q90 0 160.5 47t109.5 127v571q0 98 88 98h23q43 0 66.5 -22t23.5 -76v-883q0 -3 -9.5 -8t-27 -9.5t-35.5 -4.5q-124 0 -129 155q-120 -172 -327 -172q-188 0 -283.5 114.5t-95.5 307.5z M471 1137q-6 8 27.5 58t96 115.5t122.5 106.5q53 35 98 35q39 0 65.5 -22.5t26.5 -57.5q0 -72 -86 -125q-42 -27 -92 -50t-91.5 -35.5t-80.5 -21t-59 -8t-27 4.5z" />
<glyph unicode="&#xfb;" horiz-adv-x="1148" d="M117 397v500q0 98 88 98h22q43 0 66.5 -22t23.5 -76v-475q0 -128 62 -199t174 -71q90 0 160.5 47t109.5 127v571q0 98 88 98h23q43 0 66.5 -22t23.5 -76v-883q0 -3 -9.5 -8t-27 -9.5t-35.5 -4.5q-124 0 -129 155q-120 -172 -327 -172q-188 0 -283.5 114.5t-95.5 307.5z M289 1186q0 50 63.5 124t131 125t91.5 56q24 -5 91 -56t130.5 -125t63.5 -124q0 -30 -17 -48t-46 -18q-42 0 -94.5 51.5t-129.5 167.5q-81 -120 -129.5 -169.5t-89.5 -49.5q-29 0 -47 18.5t-18 47.5z" />
<glyph unicode="&#xfc;" horiz-adv-x="1148" d="M117 397v500q0 98 88 98h22q43 0 66.5 -22t23.5 -76v-475q0 -128 62 -199t174 -71q90 0 160.5 47t109.5 127v571q0 98 88 98h23q43 0 66.5 -22t23.5 -76v-883q0 -3 -9.5 -8t-27 -9.5t-35.5 -4.5q-124 0 -129 155q-120 -172 -327 -172q-188 0 -283.5 114.5t-95.5 307.5z M268 1239q0 57 33 90t86 33q59 0 92 -32.5t33 -90.5q0 -57 -32.5 -89t-88.5 -32t-89.5 32.5t-33.5 88.5zM643 1239q0 58 32.5 90.5t88.5 32.5t89.5 -32.5t33.5 -90.5q0 -56 -33 -88.5t-86 -32.5q-59 0 -92 32t-33 89z" />
<glyph unicode="&#xfd;" horiz-adv-x="1019" d="M27 936q-2 7 10.5 20.5t39 26t56.5 12.5q45 0 75 -25t54 -89l266 -680l236 676q38 118 127 118q25 0 52 -12t41.5 -25t12.5 -18l-465 -1227q-42 -112 -124 -112q-26 0 -53 12t-41.5 25t-12.5 18l164 399q-35 3 -54.5 15.5t-31.5 42.5zM391 1120q-6 8 27.5 58.5t96 116 t122.5 106.5q53 35 98 35q39 0 65.5 -22.5t26.5 -57.5q0 -72 -86 -125q-42 -27 -92 -50t-91.5 -35.5t-80.5 -21.5t-59 -8.5t-27 4.5z" />
<glyph unicode="&#xfe;" horiz-adv-x="1214" d="M125 -305v1689q0 3 9.5 8.5t27.5 10t37 4.5q55 0 92 -39t37 -135v-410q127 187 344 187q201 0 331 -142t130 -374q0 -233 -130 -377t-337 -144q-112 0 -201 49t-137 123v-450q0 -96 -90 -96h-23q-90 0 -90 96zM328 393q13 -109 99.5 -180.5t201.5 -71.5q138 0 222.5 97 t84.5 254q0 153 -85.5 251.5t-217.5 98.5q-110 0 -188 -53t-117 -146v-250z" />
<glyph unicode="&#xff;" horiz-adv-x="1019" d="M27 936q-2 7 10.5 20.5t39 26t56.5 12.5q45 0 75 -25t54 -89l266 -680l236 676q38 118 127 118q25 0 52 -12t41.5 -25t12.5 -18l-465 -1227q-42 -112 -124 -112q-26 0 -53 12t-41.5 25t-12.5 18l164 399q-35 3 -54.5 15.5t-31.5 42.5zM209 1239q0 57 32.5 90t86.5 33 q59 0 92 -32.5t33 -90.5q0 -57 -32.5 -89t-88.5 -32t-89.5 32.5t-33.5 88.5zM584 1239q0 58 32.5 90.5t88.5 32.5t89 -32.5t33 -90.5q0 -56 -32.5 -88.5t-85.5 -32.5q-59 0 -92 32t-33 89z" />
<glyph unicode="&#x152;" horiz-adv-x="2111" d="M88 659q0 200 86 358.5t239.5 246.5t346.5 88q95 0 198 -27h973q49 0 74 -23t25 -65v-12q0 -88 -99 -88h-690q160 -138 178 -371h361q98 0 98 -84v-6q0 -88 -98 -88h-359q-6 -125 -53 -228t-129 -174h705q96 0 96 -84v-10q0 -92 -96 -92h-986q-103 -27 -200 -27 q-195 0 -348 87t-237.5 243.5t-84.5 355.5zM301 664q0 -221 129 -362.5t330 -141.5t329 139t128 360t-129 363.5t-330 142.5q-202 0 -329.5 -139.5t-127.5 -361.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1949" d="M66 494q0 226 145 371t373 145q135 0 243 -60.5t172 -167.5q64 107 171.5 167.5t242.5 60.5q213 0 343 -137t130 -359v-16q0 -33 -13 -46.5t-48 -13.5h-727q10 -136 103 -220.5t233 -84.5q214 0 309 164q1 1 12.5 -0.5t27 -8.5t30.5 -17.5t25.5 -30t10.5 -43.5 q0 -75 -117.5 -149.5t-301.5 -74.5q-149 0 -261 57.5t-176 164.5q-65 -104 -171 -163t-240 -59q-228 0 -372 146t-144 375zM252 494q0 -152 94.5 -253.5t235.5 -101.5t234 99.5t93 250.5q0 154 -92.5 254.5t-234.5 100.5t-236 -99t-94 -251zM1100 575h590q-8 129 -82.5 205 t-196.5 76q-121 0 -206.5 -78.5t-104.5 -202.5z" />
<glyph unicode="&#x178;" horiz-adv-x="1173" d="M12 1247q-2 5 1.5 14.5t14 22t25.5 23.5t39.5 18.5t52.5 7.5q63 0 103 -73l340 -541l340 541q45 73 104 73q26 0 49 -7.5t38 -19t25 -24t14 -22t1 -13.5l-467 -704v-455q0 -96 -92 -96h-29q-90 0 -90 96v455zM276 1579q0 57 33 90t86 33q59 0 92 -32.5t33 -90.5 q0 -57 -32.5 -89t-88.5 -32t-89.5 32.5t-33.5 88.5zM651 1579q0 58 32.5 90.5t88.5 32.5t89.5 -32.5t33.5 -90.5q0 -56 -33 -88.5t-86 -32.5q-59 0 -92 32t-33 89z" />
<glyph unicode="&#x2c6;" horiz-adv-x="1024" d="M225 1186q0 50 64 124t131.5 125t91.5 56q24 -5 91 -56t130.5 -125t63.5 -124q0 -29 -17.5 -47.5t-46.5 -18.5q-42 0 -94 51t-129 168q-81 -120 -129.5 -169.5t-89.5 -49.5q-29 0 -47.5 18.5t-18.5 47.5z" />
<glyph unicode="&#x2dc;" horiz-adv-x="1024" d="M197 1202q0 79 46.5 138t112.5 59q56 0 135 -41t160 -113q19 61 46.5 101t58.5 40q34 0 52.5 -19.5t18.5 -53.5q0 -78 -47 -136.5t-112 -58.5q-59 0 -138.5 40.5t-156.5 113.5q-49 -142 -105 -142q-33 0 -52 19.5t-19 52.5z" />
<glyph unicode="&#x2000;" horiz-adv-x="915" />
<glyph unicode="&#x2001;" horiz-adv-x="1831" />
<glyph unicode="&#x2002;" horiz-adv-x="915" />
<glyph unicode="&#x2003;" horiz-adv-x="1831" />
<glyph unicode="&#x2004;" horiz-adv-x="610" />
<glyph unicode="&#x2005;" horiz-adv-x="457" />
<glyph unicode="&#x2006;" horiz-adv-x="305" />
<glyph unicode="&#x2007;" horiz-adv-x="305" />
<glyph unicode="&#x2008;" horiz-adv-x="228" />
<glyph unicode="&#x2009;" horiz-adv-x="366" />
<glyph unicode="&#x200a;" horiz-adv-x="101" />
<glyph unicode="&#x2010;" horiz-adv-x="804" d="M84 483v29q0 36 23 58t65 22h461q43 0 65.5 -22t22.5 -58v-29q0 -36 -22.5 -56.5t-65.5 -20.5h-461q-42 0 -65 20.5t-23 56.5z" />
<glyph unicode="&#x2011;" horiz-adv-x="804" d="M84 483v29q0 36 23 58t65 22h461q43 0 65.5 -22t22.5 -58v-29q0 -36 -22.5 -56.5t-65.5 -20.5h-461q-42 0 -65 20.5t-23 56.5z" />
<glyph unicode="&#x2012;" horiz-adv-x="804" d="M84 483v29q0 36 23 58t65 22h461q43 0 65.5 -22t22.5 -58v-29q0 -36 -22.5 -56.5t-65.5 -20.5h-461q-42 0 -65 20.5t-23 56.5z" />
<glyph unicode="&#x2013;" horiz-adv-x="1103" d="M84 489v19q0 78 88 78h756q88 0 88 -78v-19q0 -77 -88 -77h-756q-88 0 -88 77z" />
<glyph unicode="&#x2014;" horiz-adv-x="1443" d="M84 489v19q0 78 88 78h1100q88 0 88 -78v-19q0 -77 -88 -77h-1100q-88 0 -88 77z" />
<glyph unicode="&#x2018;" horiz-adv-x="442" d="M74 940v8q0 58 49 125l168 260q14 18 42.5 19t48 -17.5t11.5 -46.5l-106 -348q-17 -53 -37.5 -74.5t-69.5 -21.5h-18q-40 0 -64 25t-24 71z" />
<glyph unicode="&#x2019;" horiz-adv-x="442" d="M49 907l109 348q17 55 36.5 76t69.5 21h19q39 0 62.5 -25.5t23.5 -71.5v-8q0 -63 -47 -125l-170 -260q-17 -16 -43.5 -18t-47 16.5t-12.5 46.5z" />
<glyph unicode="&#x201a;" horiz-adv-x="442" d="M33 -201l108 348q17 55 37 76t70 21h18q39 0 62.5 -25.5t23.5 -71.5v-8q0 -63 -47 -125l-170 -260q-17 -16 -43.5 -18t-46.5 16.5t-12 46.5z" />
<glyph unicode="&#x201c;" horiz-adv-x="800" d="M74 940v8q0 57 49 125l168 260q18 16 44 18t46 -16.5t12 -46.5l-108 -348q-15 -54 -35.5 -75t-69.5 -21h-20q-38 0 -62 25t-24 71zM432 940v8q0 60 49 125l168 260q18 16 44 18t46.5 -16.5t12.5 -46.5l-109 -348q-15 -54 -35 -75t-71 -21h-19q-38 0 -62 25t-24 71z" />
<glyph unicode="&#x201d;" horiz-adv-x="800" d="M47 907l111 348q15 54 35 75.5t69 21.5h19q39 0 62.5 -25.5t23.5 -71.5v-8q0 -62 -48 -125l-169 -260q-16 -16 -42 -18t-47 16.5t-14 46.5zM406 907l110 348q17 56 36 76.5t69 20.5h20q38 0 61 -25.5t23 -71.5v-8q0 -60 -47 -125l-168 -260q-17 -16 -44 -18t-47.5 16.5 t-12.5 46.5z" />
<glyph unicode="&#x201e;" horiz-adv-x="800" d="M31 -201l110 348q15 54 35.5 75.5t69.5 21.5h18q39 0 62.5 -25.5t23.5 -71.5v-8q0 -63 -47 -125l-170 -260q-16 -16 -42 -18t-46.5 16.5t-13.5 46.5zM389 -201l111 348q17 56 35.5 76.5t68.5 20.5h21q38 0 61 -25.5t23 -71.5v-8q0 -60 -47 -125l-168 -260 q-17 -16 -44 -18t-48 16.5t-13 46.5z" />
<glyph unicode="&#x2022;" horiz-adv-x="684" d="M111 498q0 99 66.5 168t164.5 69t165.5 -69t67.5 -168q0 -97 -68 -165.5t-165 -68.5q-98 0 -164.5 67.5t-66.5 166.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="1366" d="M102 96v25q0 112 111 112h27q112 0 112 -112v-25q0 -110 -112 -110h-27q-111 0 -111 110zM557 96v25q0 54 28.5 83t82.5 29h26q113 0 113 -112v-25q0 -110 -113 -110h-26q-111 0 -111 110zM1012 96v25q0 54 28 83t82 29h27q115 0 115 -112v-25q0 -110 -115 -110h-27 q-110 0 -110 110z" />
<glyph unicode="&#x202f;" horiz-adv-x="366" />
<glyph unicode="&#x2039;" horiz-adv-x="645" d="M82 500q0 12 44 68t104 119.5t131 114t116 50.5h4q33 0 53.5 -21t20.5 -51v-6q0 -77 -301 -276q137 -91 219 -160t82 -113v-8q0 -29 -20.5 -49.5t-53.5 -20.5h-4q-46 0 -116.5 50.5t-130.5 114t-104 119.5t-44 69z" />
<glyph unicode="&#x203a;" horiz-adv-x="645" d="M92 217v8q0 45 83 115t218 158q-301 197 -301 276v6q0 30 19.5 51t52.5 21h2q46 0 117.5 -50.5t131.5 -114t104 -119.5t44 -68q0 -13 -44 -69t-104 -119.5t-131.5 -114t-117.5 -50.5h-2q-33 0 -52.5 20.5t-19.5 49.5z" />
<glyph unicode="&#x205f;" horiz-adv-x="457" />
<glyph unicode="&#x20ac;" horiz-adv-x="1286" d="M61 502v16q0 66 82 66h103v166h-103q-82 0 -82 65v16q0 64 82 64h119q43 212 183.5 333.5t345.5 121.5q95 0 177.5 -24.5t134.5 -63t81.5 -83.5t29.5 -87q0 -37 -26 -64t-52 -36.5t-30 -4.5q-57 95 -132 142t-188 47q-119 0 -203 -74.5t-118 -206.5h391q80 0 80 -64v-16 q0 -65 -80 -65h-412v-166h412q80 0 80 -66v-16q0 -66 -80 -66h-391q34 -133 121.5 -209.5t210.5 -76.5q114 0 187.5 51t125.5 153q4 6 31 -3t53.5 -37t26.5 -66q0 -41 -29 -88t-80 -89t-134 -70t-179 -28q-215 0 -356.5 122t-182.5 341h-117q-82 0 -82 66z" />
<glyph unicode="&#x2122;" horiz-adv-x="1398" d="M35 1264v6q0 57 63 57h432q62 0 62 -57v-6q0 -60 -62 -60h-147v-450q0 -66 -61 -66h-19q-27 0 -42 17.5t-15 48.5v450h-148q-63 0 -63 60zM688 754v510q0 69 62 69h55q31 0 45.5 -10.5t23.5 -36.5l123 -301l123 301q10 27 25.5 37t42.5 10h53q62 0 62 -69v-510 q0 -66 -56 -66h-18q-55 0 -55 66v387l-113 -289q-4 -12 -7.5 -17.5t-18 -10.5t-40.5 -5t-39.5 5.5t-15.5 9t-8 18.5l-115 287v-385q0 -66 -51 -66h-23q-55 0 -55 66z" />
<glyph unicode="&#x25fc;" horiz-adv-x="993" d="M0 0v993h993v-993h-993z" />
<hkern u1="&#x21;" u2="&#x153;" k="4" />
<hkern u1="&#x21;" u2="&#xe7;" k="4" />
<hkern u1="&#x21;" u2="&#xe6;" k="4" />
<hkern u1="&#x21;" u2="q" k="4" />
<hkern u1="&#x21;" u2="o" k="4" />
<hkern u1="&#x21;" u2="e" k="4" />
<hkern u1="&#x21;" u2="d" k="4" />
<hkern u1="&#x21;" u2="c" k="4" />
<hkern u1="&#x21;" u2="a" k="4" />
<hkern u1="&#x26;" u2="&#x178;" k="66" />
<hkern u1="&#x26;" u2="&#xdd;" k="66" />
<hkern u1="&#x26;" u2="&#xc6;" k="-72" />
<hkern u1="&#x26;" u2="&#xc5;" k="-72" />
<hkern u1="&#x26;" u2="&#xc4;" k="-72" />
<hkern u1="&#x26;" u2="&#xc3;" k="-72" />
<hkern u1="&#x26;" u2="&#xc2;" k="-72" />
<hkern u1="&#x26;" u2="&#xc1;" k="-72" />
<hkern u1="&#x26;" u2="&#xc0;" k="-72" />
<hkern u1="&#x26;" u2="Y" k="66" />
<hkern u1="&#x26;" u2="X" k="-41" />
<hkern u1="&#x26;" u2="W" k="45" />
<hkern u1="&#x26;" u2="V" k="53" />
<hkern u1="&#x26;" u2="T" k="82" />
<hkern u1="&#x26;" u2="J" k="-41" />
<hkern u1="&#x26;" u2="A" k="-61" />
<hkern u1="&#x28;" u2="&#x178;" k="-102" />
<hkern u1="&#x28;" u2="&#xdd;" k="-102" />
<hkern u1="&#x28;" u2="j" k="-307" />
<hkern u1="&#x28;" u2="g" k="-41" />
<hkern u1="&#x28;" u2="Y" k="-102" />
<hkern u1="&#x28;" u2="X" k="-82" />
<hkern u1="&#x28;" u2="W" k="-20" />
<hkern u1="&#x28;" u2="V" k="-61" />
<hkern u1="&#x28;" u2="J" k="8" />
<hkern u1="&#x28;" u2="&#x37;" k="-8" />
<hkern u1="&#x2a;" u2="&#xc6;" k="131" />
<hkern u1="&#x2a;" u2="&#xc5;" k="131" />
<hkern u1="&#x2a;" u2="&#xc4;" k="131" />
<hkern u1="&#x2a;" u2="&#xc3;" k="131" />
<hkern u1="&#x2a;" u2="&#xc2;" k="131" />
<hkern u1="&#x2a;" u2="&#xc1;" k="131" />
<hkern u1="&#x2a;" u2="&#xc0;" k="131" />
<hkern u1="&#x2a;" u2="T" k="-6" />
<hkern u1="&#x2a;" u2="J" k="29" />
<hkern u1="&#x2a;" u2="A" k="131" />
<hkern u1="&#x2c;" u2="&#x178;" k="102" />
<hkern u1="&#x2c;" u2="&#x153;" k="41" />
<hkern u1="&#x2c;" u2="&#x152;" k="78" />
<hkern u1="&#x2c;" u2="&#xe7;" k="41" />
<hkern u1="&#x2c;" u2="&#xe6;" k="41" />
<hkern u1="&#x2c;" u2="&#xdd;" k="102" />
<hkern u1="&#x2c;" u2="&#xdc;" k="20" />
<hkern u1="&#x2c;" u2="&#xdb;" k="20" />
<hkern u1="&#x2c;" u2="&#xda;" k="20" />
<hkern u1="&#x2c;" u2="&#xd9;" k="20" />
<hkern u1="&#x2c;" u2="&#xd8;" k="78" />
<hkern u1="&#x2c;" u2="&#xd6;" k="78" />
<hkern u1="&#x2c;" u2="&#xd5;" k="78" />
<hkern u1="&#x2c;" u2="&#xd4;" k="78" />
<hkern u1="&#x2c;" u2="&#xd3;" k="78" />
<hkern u1="&#x2c;" u2="&#xd2;" k="78" />
<hkern u1="&#x2c;" u2="&#xc6;" k="-61" />
<hkern u1="&#x2c;" u2="&#xc5;" k="-61" />
<hkern u1="&#x2c;" u2="&#xc4;" k="-61" />
<hkern u1="&#x2c;" u2="&#xc3;" k="-61" />
<hkern u1="&#x2c;" u2="&#xc2;" k="-61" />
<hkern u1="&#x2c;" u2="&#xc1;" k="-61" />
<hkern u1="&#x2c;" u2="&#xc0;" k="-61" />
<hkern u1="&#x2c;" u2="y" k="61" />
<hkern u1="&#x2c;" u2="w" k="20" />
<hkern u1="&#x2c;" u2="v" k="61" />
<hkern u1="&#x2c;" u2="u" k="41" />
<hkern u1="&#x2c;" u2="t" k="82" />
<hkern u1="&#x2c;" u2="r" k="41" />
<hkern u1="&#x2c;" u2="q" k="41" />
<hkern u1="&#x2c;" u2="p" k="41" />
<hkern u1="&#x2c;" u2="o" k="41" />
<hkern u1="&#x2c;" u2="n" k="41" />
<hkern u1="&#x2c;" u2="m" k="41" />
<hkern u1="&#x2c;" u2="f" k="16" />
<hkern u1="&#x2c;" u2="e" k="41" />
<hkern u1="&#x2c;" u2="d" k="41" />
<hkern u1="&#x2c;" u2="c" k="41" />
<hkern u1="&#x2c;" u2="a" k="41" />
<hkern u1="&#x2c;" u2="Y" k="102" />
<hkern u1="&#x2c;" u2="W" k="45" />
<hkern u1="&#x2c;" u2="V" k="127" />
<hkern u1="&#x2c;" u2="U" k="20" />
<hkern u1="&#x2c;" u2="T" k="135" />
<hkern u1="&#x2c;" u2="Q" k="78" />
<hkern u1="&#x2c;" u2="O" k="78" />
<hkern u1="&#x2c;" u2="G" k="78" />
<hkern u1="&#x2c;" u2="C" k="78" />
<hkern u1="&#x2c;" u2="A" k="-61" />
<hkern u1="&#x2c;" u2="&#x39;" k="16" />
<hkern u1="&#x2c;" u2="&#x38;" k="31" />
<hkern u1="&#x2c;" u2="&#x37;" k="4" />
<hkern u1="&#x2c;" u2="&#x36;" k="57" />
<hkern u1="&#x2c;" u2="&#x35;" k="-8" />
<hkern u1="&#x2c;" u2="&#x34;" k="82" />
<hkern u1="&#x2c;" u2="&#x33;" k="-4" />
<hkern u1="&#x2c;" u2="&#x32;" k="-4" />
<hkern u1="&#x2c;" u2="&#x31;" k="137" />
<hkern u1="&#x2c;" u2="&#x30;" k="70" />
<hkern u1="&#x2d;" u2="&#x178;" k="102" />
<hkern u1="&#x2d;" u2="&#x153;" k="20" />
<hkern u1="&#x2d;" u2="&#xe7;" k="20" />
<hkern u1="&#x2d;" u2="&#xe6;" k="20" />
<hkern u1="&#x2d;" u2="&#xdd;" k="102" />
<hkern u1="&#x2d;" u2="&#xc6;" k="-20" />
<hkern u1="&#x2d;" u2="&#xc5;" k="-20" />
<hkern u1="&#x2d;" u2="&#xc4;" k="-20" />
<hkern u1="&#x2d;" u2="&#xc3;" k="-20" />
<hkern u1="&#x2d;" u2="&#xc2;" k="-20" />
<hkern u1="&#x2d;" u2="&#xc1;" k="-20" />
<hkern u1="&#x2d;" u2="&#xc0;" k="-20" />
<hkern u1="&#x2d;" u2="z" k="53" />
<hkern u1="&#x2d;" u2="y" k="20" />
<hkern u1="&#x2d;" u2="x" k="61" />
<hkern u1="&#x2d;" u2="v" k="20" />
<hkern u1="&#x2d;" u2="q" k="20" />
<hkern u1="&#x2d;" u2="o" k="20" />
<hkern u1="&#x2d;" u2="e" k="20" />
<hkern u1="&#x2d;" u2="d" k="20" />
<hkern u1="&#x2d;" u2="c" k="20" />
<hkern u1="&#x2d;" u2="a" k="20" />
<hkern u1="&#x2d;" u2="Z" k="20" />
<hkern u1="&#x2d;" u2="Y" k="102" />
<hkern u1="&#x2d;" u2="X" k="61" />
<hkern u1="&#x2d;" u2="W" k="82" />
<hkern u1="&#x2d;" u2="V" k="102" />
<hkern u1="&#x2d;" u2="T" k="135" />
<hkern u1="&#x2d;" u2="A" k="-20" />
<hkern u1="&#x2d;" u2="&#x37;" k="61" />
<hkern u1="&#x2d;" u2="&#x31;" k="41" />
<hkern u1="&#x2e;" u2="&#x178;" k="102" />
<hkern u1="&#x2e;" u2="&#x153;" k="41" />
<hkern u1="&#x2e;" u2="&#x152;" k="78" />
<hkern u1="&#x2e;" u2="&#xe7;" k="41" />
<hkern u1="&#x2e;" u2="&#xe6;" k="41" />
<hkern u1="&#x2e;" u2="&#xdd;" k="102" />
<hkern u1="&#x2e;" u2="&#xdc;" k="20" />
<hkern u1="&#x2e;" u2="&#xdb;" k="20" />
<hkern u1="&#x2e;" u2="&#xda;" k="20" />
<hkern u1="&#x2e;" u2="&#xd9;" k="20" />
<hkern u1="&#x2e;" u2="&#xd8;" k="78" />
<hkern u1="&#x2e;" u2="&#xd6;" k="78" />
<hkern u1="&#x2e;" u2="&#xd5;" k="78" />
<hkern u1="&#x2e;" u2="&#xd4;" k="78" />
<hkern u1="&#x2e;" u2="&#xd3;" k="78" />
<hkern u1="&#x2e;" u2="&#xd2;" k="78" />
<hkern u1="&#x2e;" u2="&#xc6;" k="-61" />
<hkern u1="&#x2e;" u2="&#xc5;" k="-61" />
<hkern u1="&#x2e;" u2="&#xc4;" k="-61" />
<hkern u1="&#x2e;" u2="&#xc3;" k="-61" />
<hkern u1="&#x2e;" u2="&#xc2;" k="-61" />
<hkern u1="&#x2e;" u2="&#xc1;" k="-61" />
<hkern u1="&#x2e;" u2="&#xc0;" k="-61" />
<hkern u1="&#x2e;" u2="y" k="61" />
<hkern u1="&#x2e;" u2="w" k="20" />
<hkern u1="&#x2e;" u2="v" k="61" />
<hkern u1="&#x2e;" u2="u" k="41" />
<hkern u1="&#x2e;" u2="t" k="82" />
<hkern u1="&#x2e;" u2="r" k="41" />
<hkern u1="&#x2e;" u2="q" k="41" />
<hkern u1="&#x2e;" u2="p" k="41" />
<hkern u1="&#x2e;" u2="o" k="41" />
<hkern u1="&#x2e;" u2="n" k="41" />
<hkern u1="&#x2e;" u2="m" k="41" />
<hkern u1="&#x2e;" u2="f" k="16" />
<hkern u1="&#x2e;" u2="e" k="41" />
<hkern u1="&#x2e;" u2="d" k="41" />
<hkern u1="&#x2e;" u2="c" k="41" />
<hkern u1="&#x2e;" u2="a" k="41" />
<hkern u1="&#x2e;" u2="Y" k="102" />
<hkern u1="&#x2e;" u2="W" k="45" />
<hkern u1="&#x2e;" u2="V" k="127" />
<hkern u1="&#x2e;" u2="U" k="20" />
<hkern u1="&#x2e;" u2="T" k="135" />
<hkern u1="&#x2e;" u2="Q" k="78" />
<hkern u1="&#x2e;" u2="O" k="78" />
<hkern u1="&#x2e;" u2="G" k="78" />
<hkern u1="&#x2e;" u2="C" k="78" />
<hkern u1="&#x2e;" u2="A" k="-61" />
<hkern u1="&#x2e;" u2="&#x39;" k="16" />
<hkern u1="&#x2e;" u2="&#x38;" k="31" />
<hkern u1="&#x2e;" u2="&#x37;" k="4" />
<hkern u1="&#x2e;" u2="&#x36;" k="57" />
<hkern u1="&#x2e;" u2="&#x35;" k="-8" />
<hkern u1="&#x2e;" u2="&#x34;" k="82" />
<hkern u1="&#x2e;" u2="&#x33;" k="-4" />
<hkern u1="&#x2e;" u2="&#x32;" k="-4" />
<hkern u1="&#x2e;" u2="&#x31;" k="137" />
<hkern u1="&#x2e;" u2="&#x30;" k="70" />
<hkern u1="&#x2f;" u2="&#x153;" k="20" />
<hkern u1="&#x2f;" u2="&#x152;" k="-16" />
<hkern u1="&#x2f;" u2="&#xe7;" k="20" />
<hkern u1="&#x2f;" u2="&#xe6;" k="20" />
<hkern u1="&#x2f;" u2="&#xde;" k="-4" />
<hkern u1="&#x2f;" u2="&#xd8;" k="-16" />
<hkern u1="&#x2f;" u2="&#xd6;" k="-16" />
<hkern u1="&#x2f;" u2="&#xd5;" k="-16" />
<hkern u1="&#x2f;" u2="&#xd4;" k="-16" />
<hkern u1="&#x2f;" u2="&#xd3;" k="-16" />
<hkern u1="&#x2f;" u2="&#xd2;" k="-16" />
<hkern u1="&#x2f;" u2="&#xd1;" k="-4" />
<hkern u1="&#x2f;" u2="&#xd0;" k="-4" />
<hkern u1="&#x2f;" u2="&#xcf;" k="-4" />
<hkern u1="&#x2f;" u2="&#xce;" k="-4" />
<hkern u1="&#x2f;" u2="&#xcd;" k="-4" />
<hkern u1="&#x2f;" u2="&#xcc;" k="-4" />
<hkern u1="&#x2f;" u2="&#xcb;" k="-4" />
<hkern u1="&#x2f;" u2="&#xca;" k="-4" />
<hkern u1="&#x2f;" u2="&#xc9;" k="-4" />
<hkern u1="&#x2f;" u2="&#xc8;" k="-4" />
<hkern u1="&#x2f;" u2="q" k="20" />
<hkern u1="&#x2f;" u2="o" k="20" />
<hkern u1="&#x2f;" u2="g" k="25" />
<hkern u1="&#x2f;" u2="e" k="20" />
<hkern u1="&#x2f;" u2="d" k="20" />
<hkern u1="&#x2f;" u2="c" k="20" />
<hkern u1="&#x2f;" u2="a" k="20" />
<hkern u1="&#x2f;" u2="R" k="-4" />
<hkern u1="&#x2f;" u2="Q" k="-16" />
<hkern u1="&#x2f;" u2="P" k="-4" />
<hkern u1="&#x2f;" u2="O" k="-16" />
<hkern u1="&#x2f;" u2="N" k="-4" />
<hkern u1="&#x2f;" u2="M" k="-4" />
<hkern u1="&#x2f;" u2="L" k="-4" />
<hkern u1="&#x2f;" u2="K" k="-4" />
<hkern u1="&#x2f;" u2="J" k="20" />
<hkern u1="&#x2f;" u2="I" k="-4" />
<hkern u1="&#x2f;" u2="H" k="-4" />
<hkern u1="&#x2f;" u2="G" k="-16" />
<hkern u1="&#x2f;" u2="F" k="-4" />
<hkern u1="&#x2f;" u2="E" k="-4" />
<hkern u1="&#x2f;" u2="D" k="-4" />
<hkern u1="&#x2f;" u2="C" k="-16" />
<hkern u1="&#x2f;" u2="B" k="-4" />
<hkern u1="&#x2f;" u2="&#x37;" k="-16" />
<hkern u1="&#x2f;" u2="&#x35;" k="-8" />
<hkern u1="&#x2f;" u2="&#x34;" k="33" />
<hkern u1="&#x2f;" u2="&#x33;" k="-8" />
<hkern u1="&#x2f;" u2="&#x32;" k="-6" />
<hkern u1="&#x2f;" u2="&#x31;" k="-41" />
<hkern u1="&#x30;" u2="&#x2026;" k="70" />
<hkern u1="&#x30;" u2="&#x201e;" k="70" />
<hkern u1="&#x30;" u2="&#x201a;" k="70" />
<hkern u1="&#x30;" u2="&#x37;" k="41" />
<hkern u1="&#x30;" u2="&#x2e;" k="70" />
<hkern u1="&#x30;" u2="&#x2c;" k="70" />
<hkern u1="&#x31;" u2="&#x2026;" k="-4" />
<hkern u1="&#x31;" u2="&#x201e;" k="-4" />
<hkern u1="&#x31;" u2="&#x201a;" k="-4" />
<hkern u1="&#x31;" u2="&#x37;" k="-4" />
<hkern u1="&#x31;" u2="&#x2e;" k="-4" />
<hkern u1="&#x31;" u2="&#x2c;" k="-4" />
<hkern u1="&#x32;" u2="&#x2014;" k="20" />
<hkern u1="&#x32;" u2="&#x2013;" k="20" />
<hkern u1="&#x32;" u2="&#x34;" k="45" />
<hkern u1="&#x32;" u2="&#x2d;" k="20" />
<hkern u1="&#x33;" u2="&#x2026;" k="33" />
<hkern u1="&#x33;" u2="&#x201e;" k="33" />
<hkern u1="&#x33;" u2="&#x201a;" k="33" />
<hkern u1="&#x33;" u2="&#x37;" k="31" />
<hkern u1="&#x33;" u2="&#x2f;" k="8" />
<hkern u1="&#x33;" u2="&#x2e;" k="33" />
<hkern u1="&#x33;" u2="&#x2c;" k="33" />
<hkern u1="&#x34;" u2="&#x2122;" k="98" />
<hkern u1="&#x34;" u2="&#x2026;" k="-8" />
<hkern u1="&#x34;" u2="&#x201e;" k="-8" />
<hkern u1="&#x34;" u2="&#x201d;" k="12" />
<hkern u1="&#x34;" u2="&#x201c;" k="16" />
<hkern u1="&#x34;" u2="&#x201a;" k="-8" />
<hkern u1="&#x34;" u2="&#x2019;" k="12" />
<hkern u1="&#x34;" u2="&#x2018;" k="16" />
<hkern u1="&#x34;" u2="&#xb0;" k="66" />
<hkern u1="&#x34;" u2="&#x37;" k="25" />
<hkern u1="&#x34;" u2="&#x31;" k="12" />
<hkern u1="&#x34;" u2="&#x2f;" k="4" />
<hkern u1="&#x34;" u2="&#x2e;" k="-8" />
<hkern u1="&#x34;" u2="&#x2c;" k="-8" />
<hkern u1="&#x35;" u2="&#x2026;" k="20" />
<hkern u1="&#x35;" u2="&#x201e;" k="20" />
<hkern u1="&#x35;" u2="&#x201a;" k="20" />
<hkern u1="&#x35;" u2="&#x37;" k="12" />
<hkern u1="&#x35;" u2="&#x2f;" k="4" />
<hkern u1="&#x35;" u2="&#x2e;" k="20" />
<hkern u1="&#x35;" u2="&#x2c;" k="20" />
<hkern u1="&#x36;" u2="&#x2026;" k="16" />
<hkern u1="&#x36;" u2="&#x201e;" k="16" />
<hkern u1="&#x36;" u2="&#x201a;" k="16" />
<hkern u1="&#x36;" u2="&#x37;" k="20" />
<hkern u1="&#x36;" u2="&#x2e;" k="16" />
<hkern u1="&#x36;" u2="&#x2c;" k="16" />
<hkern u1="&#x37;" u2="&#x2026;" k="164" />
<hkern u1="&#x37;" u2="&#x201e;" k="164" />
<hkern u1="&#x37;" u2="&#x201a;" k="164" />
<hkern u1="&#x37;" u2="&#x2014;" k="82" />
<hkern u1="&#x37;" u2="&#x2013;" k="82" />
<hkern u1="&#x37;" u2="&#xa2;" k="82" />
<hkern u1="&#x37;" u2="&#x7d;" k="-4" />
<hkern u1="&#x37;" u2="]" k="-4" />
<hkern u1="&#x37;" u2="&#x3f;" k="-4" />
<hkern u1="&#x37;" u2="&#x3b;" k="41" />
<hkern u1="&#x37;" u2="&#x3a;" k="41" />
<hkern u1="&#x37;" u2="&#x39;" k="-20" />
<hkern u1="&#x37;" u2="&#x38;" k="2" />
<hkern u1="&#x37;" u2="&#x36;" k="2" />
<hkern u1="&#x37;" u2="&#x35;" k="-31" />
<hkern u1="&#x37;" u2="&#x34;" k="102" />
<hkern u1="&#x37;" u2="&#x33;" k="-39" />
<hkern u1="&#x37;" u2="&#x31;" k="-41" />
<hkern u1="&#x37;" u2="&#x30;" k="18" />
<hkern u1="&#x37;" u2="&#x2f;" k="37" />
<hkern u1="&#x37;" u2="&#x2e;" k="164" />
<hkern u1="&#x37;" u2="&#x2d;" k="82" />
<hkern u1="&#x37;" u2="&#x2c;" k="164" />
<hkern u1="&#x37;" u2="&#x29;" k="-4" />
<hkern u1="&#x38;" u2="&#x2026;" k="31" />
<hkern u1="&#x38;" u2="&#x201e;" k="31" />
<hkern u1="&#x38;" u2="&#x201a;" k="31" />
<hkern u1="&#x38;" u2="&#x37;" k="31" />
<hkern u1="&#x38;" u2="&#x2e;" k="31" />
<hkern u1="&#x38;" u2="&#x2c;" k="31" />
<hkern u1="&#x39;" u2="&#x2026;" k="57" />
<hkern u1="&#x39;" u2="&#x201e;" k="57" />
<hkern u1="&#x39;" u2="&#x201a;" k="57" />
<hkern u1="&#x39;" u2="&#x37;" k="78" />
<hkern u1="&#x39;" u2="&#x2e;" k="57" />
<hkern u1="&#x39;" u2="&#x2c;" k="57" />
<hkern u1="&#x3a;" u2="&#x178;" k="61" />
<hkern u1="&#x3a;" u2="&#x153;" k="20" />
<hkern u1="&#x3a;" u2="&#xe7;" k="20" />
<hkern u1="&#x3a;" u2="&#xe6;" k="20" />
<hkern u1="&#x3a;" u2="&#xdd;" k="61" />
<hkern u1="&#x3a;" u2="q" k="20" />
<hkern u1="&#x3a;" u2="o" k="20" />
<hkern u1="&#x3a;" u2="e" k="20" />
<hkern u1="&#x3a;" u2="d" k="20" />
<hkern u1="&#x3a;" u2="c" k="20" />
<hkern u1="&#x3a;" u2="a" k="20" />
<hkern u1="&#x3a;" u2="Y" k="61" />
<hkern u1="&#x3a;" u2="W" k="41" />
<hkern u1="&#x3a;" u2="V" k="41" />
<hkern u1="&#x3a;" u2="T" k="102" />
<hkern u1="&#x3a;" u2="&#x37;" k="41" />
<hkern u1="&#x3a;" u2="&#x31;" k="61" />
<hkern u1="&#x3b;" u2="&#x178;" k="61" />
<hkern u1="&#x3b;" u2="&#x153;" k="20" />
<hkern u1="&#x3b;" u2="&#xe7;" k="20" />
<hkern u1="&#x3b;" u2="&#xe6;" k="20" />
<hkern u1="&#x3b;" u2="&#xdd;" k="61" />
<hkern u1="&#x3b;" u2="q" k="20" />
<hkern u1="&#x3b;" u2="o" k="20" />
<hkern u1="&#x3b;" u2="e" k="20" />
<hkern u1="&#x3b;" u2="d" k="20" />
<hkern u1="&#x3b;" u2="c" k="20" />
<hkern u1="&#x3b;" u2="a" k="20" />
<hkern u1="&#x3b;" u2="Y" k="61" />
<hkern u1="&#x3b;" u2="W" k="41" />
<hkern u1="&#x3b;" u2="V" k="41" />
<hkern u1="&#x3b;" u2="T" k="102" />
<hkern u1="&#x3b;" u2="&#x37;" k="41" />
<hkern u1="&#x3b;" u2="&#x31;" k="61" />
<hkern u1="&#x3e;" u2="&#x37;" k="98" />
<hkern u1="&#x40;" u2="&#x178;" k="61" />
<hkern u1="&#x40;" u2="&#xdd;" k="61" />
<hkern u1="&#x40;" u2="&#xc6;" k="20" />
<hkern u1="&#x40;" u2="&#xc5;" k="20" />
<hkern u1="&#x40;" u2="&#xc4;" k="20" />
<hkern u1="&#x40;" u2="&#xc3;" k="20" />
<hkern u1="&#x40;" u2="&#xc2;" k="20" />
<hkern u1="&#x40;" u2="&#xc1;" k="20" />
<hkern u1="&#x40;" u2="&#xc0;" k="20" />
<hkern u1="&#x40;" u2="g" k="-4" />
<hkern u1="&#x40;" u2="Y" k="61" />
<hkern u1="&#x40;" u2="X" k="12" />
<hkern u1="&#x40;" u2="W" k="41" />
<hkern u1="&#x40;" u2="V" k="4" />
<hkern u1="&#x40;" u2="T" k="61" />
<hkern u1="&#x40;" u2="A" k="20" />
<hkern u1="&#x40;" u2="&#x33;" k="4" />
<hkern u1="A" u2="&#x2122;" k="29" />
<hkern u1="A" u2="&#x2026;" k="-12" />
<hkern u1="A" u2="&#x201e;" k="-12" />
<hkern u1="A" u2="&#x201d;" k="12" />
<hkern u1="A" u2="&#x201c;" k="25" />
<hkern u1="A" u2="&#x201a;" k="-12" />
<hkern u1="A" u2="&#x2019;" k="12" />
<hkern u1="A" u2="&#x2018;" k="25" />
<hkern u1="A" u2="&#x2014;" k="-4" />
<hkern u1="A" u2="&#x2013;" k="-4" />
<hkern u1="A" u2="&#x178;" k="23" />
<hkern u1="A" u2="&#x153;" k="2" />
<hkern u1="A" u2="&#x152;" k="8" />
<hkern u1="A" u2="&#xe7;" k="2" />
<hkern u1="A" u2="&#xe6;" k="2" />
<hkern u1="A" u2="&#xdd;" k="23" />
<hkern u1="A" u2="&#xd8;" k="8" />
<hkern u1="A" u2="&#xd6;" k="8" />
<hkern u1="A" u2="&#xd5;" k="8" />
<hkern u1="A" u2="&#xd4;" k="8" />
<hkern u1="A" u2="&#xd3;" k="8" />
<hkern u1="A" u2="&#xd2;" k="8" />
<hkern u1="A" u2="&#xc6;" k="-4" />
<hkern u1="A" u2="&#xc5;" k="-4" />
<hkern u1="A" u2="&#xc4;" k="-4" />
<hkern u1="A" u2="&#xc3;" k="-4" />
<hkern u1="A" u2="&#xc2;" k="-4" />
<hkern u1="A" u2="&#xc1;" k="-4" />
<hkern u1="A" u2="&#xc0;" k="-4" />
<hkern u1="A" u2="&#xae;" k="4" />
<hkern u1="A" u2="&#xa9;" k="4" />
<hkern u1="A" u2="z" k="-4" />
<hkern u1="A" u2="y" k="29" />
<hkern u1="A" u2="x" k="-4" />
<hkern u1="A" u2="w" k="20" />
<hkern u1="A" u2="v" k="29" />
<hkern u1="A" u2="u" k="2" />
<hkern u1="A" u2="t" k="2" />
<hkern u1="A" u2="s" k="-6" />
<hkern u1="A" u2="r" k="2" />
<hkern u1="A" u2="q" k="2" />
<hkern u1="A" u2="p" k="2" />
<hkern u1="A" u2="o" k="2" />
<hkern u1="A" u2="n" k="2" />
<hkern u1="A" u2="m" k="2" />
<hkern u1="A" u2="g" k="6" />
<hkern u1="A" u2="f" k="16" />
<hkern u1="A" u2="e" k="2" />
<hkern u1="A" u2="d" k="2" />
<hkern u1="A" u2="c" k="2" />
<hkern u1="A" u2="a" k="-4" />
<hkern u1="A" u2="Y" k="23" />
<hkern u1="A" u2="X" k="-4" />
<hkern u1="A" u2="W" k="27" />
<hkern u1="A" u2="V" k="33" />
<hkern u1="A" u2="T" k="23" />
<hkern u1="A" u2="S" k="-2" />
<hkern u1="A" u2="Q" k="8" />
<hkern u1="A" u2="O" k="8" />
<hkern u1="A" u2="J" k="-4" />
<hkern u1="A" u2="G" k="8" />
<hkern u1="A" u2="C" k="8" />
<hkern u1="A" u2="A" k="-4" />
<hkern u1="A" u2="&#x40;" k="4" />
<hkern u1="A" u2="&#x3f;" k="12" />
<hkern u1="A" u2="&#x2e;" k="-12" />
<hkern u1="A" u2="&#x2d;" k="-4" />
<hkern u1="A" u2="&#x2c;" k="-12" />
<hkern u1="A" u2="&#x2a;" k="33" />
<hkern u1="B" u2="&#x153;" k="-4" />
<hkern u1="B" u2="&#xe7;" k="-4" />
<hkern u1="B" u2="&#xe6;" k="-4" />
<hkern u1="B" u2="y" k="6" />
<hkern u1="B" u2="w" k="4" />
<hkern u1="B" u2="v" k="6" />
<hkern u1="B" u2="q" k="-4" />
<hkern u1="B" u2="o" k="-4" />
<hkern u1="B" u2="e" k="-4" />
<hkern u1="B" u2="d" k="-4" />
<hkern u1="B" u2="c" k="-4" />
<hkern u1="B" u2="a" k="-4" />
<hkern u1="B" u2="W" k="41" />
<hkern u1="B" u2="V" k="10" />
<hkern u1="C" u2="&#x178;" k="-41" />
<hkern u1="C" u2="&#x153;" k="-8" />
<hkern u1="C" u2="&#x152;" k="4" />
<hkern u1="C" u2="&#xe7;" k="-8" />
<hkern u1="C" u2="&#xe6;" k="-8" />
<hkern u1="C" u2="&#xdd;" k="-41" />
<hkern u1="C" u2="&#xd8;" k="4" />
<hkern u1="C" u2="&#xd6;" k="4" />
<hkern u1="C" u2="&#xd5;" k="4" />
<hkern u1="C" u2="&#xd4;" k="4" />
<hkern u1="C" u2="&#xd3;" k="4" />
<hkern u1="C" u2="&#xd2;" k="4" />
<hkern u1="C" u2="z" k="20" />
<hkern u1="C" u2="y" k="10" />
<hkern u1="C" u2="w" k="10" />
<hkern u1="C" u2="v" k="10" />
<hkern u1="C" u2="t" k="10" />
<hkern u1="C" u2="q" k="-8" />
<hkern u1="C" u2="o" k="-8" />
<hkern u1="C" u2="e" k="-8" />
<hkern u1="C" u2="d" k="-8" />
<hkern u1="C" u2="c" k="-8" />
<hkern u1="C" u2="a" k="-8" />
<hkern u1="C" u2="Y" k="-41" />
<hkern u1="C" u2="X" k="-20" />
<hkern u1="C" u2="V" k="-41" />
<hkern u1="C" u2="Q" k="4" />
<hkern u1="C" u2="O" k="4" />
<hkern u1="C" u2="G" k="4" />
<hkern u1="C" u2="C" k="4" />
<hkern u1="D" u2="&#x2026;" k="78" />
<hkern u1="D" u2="&#x201e;" k="78" />
<hkern u1="D" u2="&#x201c;" k="41" />
<hkern u1="D" u2="&#x201a;" k="78" />
<hkern u1="D" u2="&#x2018;" k="41" />
<hkern u1="D" u2="&#x178;" k="47" />
<hkern u1="D" u2="&#x153;" k="8" />
<hkern u1="D" u2="&#xe7;" k="8" />
<hkern u1="D" u2="&#xe6;" k="8" />
<hkern u1="D" u2="&#xdd;" k="47" />
<hkern u1="D" u2="&#xc6;" k="41" />
<hkern u1="D" u2="&#xc5;" k="41" />
<hkern u1="D" u2="&#xc4;" k="41" />
<hkern u1="D" u2="&#xc3;" k="41" />
<hkern u1="D" u2="&#xc2;" k="41" />
<hkern u1="D" u2="&#xc1;" k="41" />
<hkern u1="D" u2="&#xc0;" k="41" />
<hkern u1="D" u2="z" k="20" />
<hkern u1="D" u2="x" k="20" />
<hkern u1="D" u2="u" k="8" />
<hkern u1="D" u2="r" k="8" />
<hkern u1="D" u2="q" k="8" />
<hkern u1="D" u2="p" k="8" />
<hkern u1="D" u2="o" k="8" />
<hkern u1="D" u2="n" k="8" />
<hkern u1="D" u2="m" k="8" />
<hkern u1="D" u2="l" k="16" />
<hkern u1="D" u2="k" k="16" />
<hkern u1="D" u2="h" k="16" />
<hkern u1="D" u2="e" k="8" />
<hkern u1="D" u2="d" k="8" />
<hkern u1="D" u2="c" k="8" />
<hkern u1="D" u2="b" k="16" />
<hkern u1="D" u2="a" k="8" />
<hkern u1="D" u2="Z" k="78" />
<hkern u1="D" u2="Y" k="47" />
<hkern u1="D" u2="X" k="53" />
<hkern u1="D" u2="W" k="78" />
<hkern u1="D" u2="V" k="41" />
<hkern u1="D" u2="T" k="55" />
<hkern u1="D" u2="J" k="80" />
<hkern u1="D" u2="A" k="41" />
<hkern u1="D" u2="&#x3f;" k="41" />
<hkern u1="D" u2="&#x2f;" k="102" />
<hkern u1="D" u2="&#x2e;" k="78" />
<hkern u1="D" u2="&#x2c;" k="78" />
<hkern u1="E" u2="&#x2039;" k="8" />
<hkern u1="E" u2="&#x153;" k="49" />
<hkern u1="E" u2="&#x152;" k="10" />
<hkern u1="E" u2="&#xfe;" k="2" />
<hkern u1="E" u2="&#xe7;" k="49" />
<hkern u1="E" u2="&#xe6;" k="49" />
<hkern u1="E" u2="&#xd8;" k="10" />
<hkern u1="E" u2="&#xd6;" k="10" />
<hkern u1="E" u2="&#xd5;" k="10" />
<hkern u1="E" u2="&#xd4;" k="10" />
<hkern u1="E" u2="&#xd3;" k="10" />
<hkern u1="E" u2="&#xd2;" k="10" />
<hkern u1="E" u2="&#xae;" k="41" />
<hkern u1="E" u2="&#xab;" k="8" />
<hkern u1="E" u2="&#xa9;" k="41" />
<hkern u1="E" u2="y" k="41" />
<hkern u1="E" u2="v" k="41" />
<hkern u1="E" u2="u" k="4" />
<hkern u1="E" u2="r" k="4" />
<hkern u1="E" u2="q" k="49" />
<hkern u1="E" u2="p" k="4" />
<hkern u1="E" u2="o" k="49" />
<hkern u1="E" u2="n" k="4" />
<hkern u1="E" u2="m" k="4" />
<hkern u1="E" u2="l" k="2" />
<hkern u1="E" u2="k" k="2" />
<hkern u1="E" u2="h" k="2" />
<hkern u1="E" u2="g" k="16" />
<hkern u1="E" u2="f" k="20" />
<hkern u1="E" u2="e" k="49" />
<hkern u1="E" u2="d" k="49" />
<hkern u1="E" u2="c" k="49" />
<hkern u1="E" u2="b" k="2" />
<hkern u1="E" u2="a" k="41" />
<hkern u1="E" u2="W" k="2" />
<hkern u1="E" u2="V" k="-4" />
<hkern u1="E" u2="T" k="-27" />
<hkern u1="E" u2="Q" k="10" />
<hkern u1="E" u2="O" k="10" />
<hkern u1="E" u2="J" k="-2" />
<hkern u1="E" u2="G" k="10" />
<hkern u1="E" u2="C" k="10" />
<hkern u1="E" u2="&#x40;" k="41" />
<hkern u1="F" u2="&#x2122;" k="-8" />
<hkern u1="F" u2="&#x2039;" k="14" />
<hkern u1="F" u2="&#x2026;" k="184" />
<hkern u1="F" u2="&#x201e;" k="184" />
<hkern u1="F" u2="&#x201a;" k="184" />
<hkern u1="F" u2="&#x2014;" k="94" />
<hkern u1="F" u2="&#x2013;" k="94" />
<hkern u1="F" u2="&#x178;" k="-51" />
<hkern u1="F" u2="&#x153;" k="102" />
<hkern u1="F" u2="&#x152;" k="8" />
<hkern u1="F" u2="&#xfe;" k="4" />
<hkern u1="F" u2="&#xe7;" k="102" />
<hkern u1="F" u2="&#xe6;" k="102" />
<hkern u1="F" u2="&#xdd;" k="-51" />
<hkern u1="F" u2="&#xd8;" k="8" />
<hkern u1="F" u2="&#xd6;" k="8" />
<hkern u1="F" u2="&#xd5;" k="8" />
<hkern u1="F" u2="&#xd4;" k="8" />
<hkern u1="F" u2="&#xd3;" k="8" />
<hkern u1="F" u2="&#xd2;" k="8" />
<hkern u1="F" u2="&#xc6;" k="102" />
<hkern u1="F" u2="&#xc5;" k="102" />
<hkern u1="F" u2="&#xc4;" k="102" />
<hkern u1="F" u2="&#xc3;" k="102" />
<hkern u1="F" u2="&#xc2;" k="102" />
<hkern u1="F" u2="&#xc1;" k="102" />
<hkern u1="F" u2="&#xc0;" k="102" />
<hkern u1="F" u2="&#xae;" k="41" />
<hkern u1="F" u2="&#xab;" k="14" />
<hkern u1="F" u2="&#xa9;" k="41" />
<hkern u1="F" u2="z" k="82" />
<hkern u1="F" u2="y" k="41" />
<hkern u1="F" u2="x" k="82" />
<hkern u1="F" u2="w" k="41" />
<hkern u1="F" u2="v" k="41" />
<hkern u1="F" u2="u" k="82" />
<hkern u1="F" u2="t" k="41" />
<hkern u1="F" u2="s" k="82" />
<hkern u1="F" u2="r" k="82" />
<hkern u1="F" u2="q" k="102" />
<hkern u1="F" u2="p" k="82" />
<hkern u1="F" u2="o" k="102" />
<hkern u1="F" u2="n" k="82" />
<hkern u1="F" u2="m" k="82" />
<hkern u1="F" u2="l" k="20" />
<hkern u1="F" u2="k" k="20" />
<hkern u1="F" u2="j" k="20" />
<hkern u1="F" u2="i" k="20" />
<hkern u1="F" u2="h" k="20" />
<hkern u1="F" u2="g" k="61" />
<hkern u1="F" u2="f" k="41" />
<hkern u1="F" u2="e" k="102" />
<hkern u1="F" u2="d" k="102" />
<hkern u1="F" u2="c" k="102" />
<hkern u1="F" u2="b" k="20" />
<hkern u1="F" u2="a" k="102" />
<hkern u1="F" u2="Y" k="-51" />
<hkern u1="F" u2="X" k="-20" />
<hkern u1="F" u2="W" k="-10" />
<hkern u1="F" u2="V" k="-41" />
<hkern u1="F" u2="T" k="-53" />
<hkern u1="F" u2="Q" k="8" />
<hkern u1="F" u2="O" k="8" />
<hkern u1="F" u2="J" k="184" />
<hkern u1="F" u2="G" k="8" />
<hkern u1="F" u2="C" k="8" />
<hkern u1="F" u2="A" k="102" />
<hkern u1="F" u2="&#x40;" k="41" />
<hkern u1="F" u2="&#x3b;" k="61" />
<hkern u1="F" u2="&#x3a;" k="61" />
<hkern u1="F" u2="&#x2f;" k="164" />
<hkern u1="F" u2="&#x2e;" k="184" />
<hkern u1="F" u2="&#x2d;" k="94" />
<hkern u1="F" u2="&#x2c;" k="184" />
<hkern u1="F" u2="&#x26;" k="78" />
<hkern u1="G" u2="&#x2122;" k="61" />
<hkern u1="G" u2="&#x2026;" k="-41" />
<hkern u1="G" u2="&#x201e;" k="-41" />
<hkern u1="G" u2="&#x201a;" k="-41" />
<hkern u1="G" u2="&#x178;" k="35" />
<hkern u1="G" u2="&#xdd;" k="35" />
<hkern u1="G" u2="y" k="25" />
<hkern u1="G" u2="w" k="6" />
<hkern u1="G" u2="v" k="25" />
<hkern u1="G" u2="t" k="20" />
<hkern u1="G" u2="Y" k="35" />
<hkern u1="G" u2="W" k="25" />
<hkern u1="G" u2="V" k="29" />
<hkern u1="G" u2="T" k="51" />
<hkern u1="G" u2="&#x2e;" k="-41" />
<hkern u1="G" u2="&#x2c;" k="-41" />
<hkern u1="H" u2="y" k="20" />
<hkern u1="H" u2="v" k="20" />
<hkern u1="H" u2="&#x2f;" k="41" />
<hkern u1="I" u2="y" k="20" />
<hkern u1="I" u2="v" k="20" />
<hkern u1="I" u2="&#x2f;" k="41" />
<hkern u1="J" u2="&#x2026;" k="20" />
<hkern u1="J" u2="&#x201e;" k="20" />
<hkern u1="J" u2="&#x201a;" k="20" />
<hkern u1="J" u2="&#xc6;" k="8" />
<hkern u1="J" u2="&#xc5;" k="8" />
<hkern u1="J" u2="&#xc4;" k="8" />
<hkern u1="J" u2="&#xc3;" k="8" />
<hkern u1="J" u2="&#xc2;" k="8" />
<hkern u1="J" u2="&#xc1;" k="8" />
<hkern u1="J" u2="&#xc0;" k="8" />
<hkern u1="J" u2="J" k="20" />
<hkern u1="J" u2="A" k="16" />
<hkern u1="J" u2="&#x2e;" k="20" />
<hkern u1="J" u2="&#x2c;" k="20" />
<hkern u1="K" u2="&#x2014;" k="78" />
<hkern u1="K" u2="&#x2013;" k="78" />
<hkern u1="K" u2="&#x178;" k="-25" />
<hkern u1="K" u2="&#x153;" k="41" />
<hkern u1="K" u2="&#x152;" k="49" />
<hkern u1="K" u2="&#xf0;" k="41" />
<hkern u1="K" u2="&#xe7;" k="41" />
<hkern u1="K" u2="&#xe6;" k="41" />
<hkern u1="K" u2="&#xdd;" k="-25" />
<hkern u1="K" u2="&#xdc;" k="20" />
<hkern u1="K" u2="&#xdb;" k="20" />
<hkern u1="K" u2="&#xda;" k="20" />
<hkern u1="K" u2="&#xd9;" k="20" />
<hkern u1="K" u2="&#xd8;" k="49" />
<hkern u1="K" u2="&#xd6;" k="49" />
<hkern u1="K" u2="&#xd5;" k="49" />
<hkern u1="K" u2="&#xd4;" k="49" />
<hkern u1="K" u2="&#xd3;" k="49" />
<hkern u1="K" u2="&#xd2;" k="49" />
<hkern u1="K" u2="&#xae;" k="61" />
<hkern u1="K" u2="&#xa9;" k="61" />
<hkern u1="K" u2="y" k="47" />
<hkern u1="K" u2="x" k="4" />
<hkern u1="K" u2="w" k="10" />
<hkern u1="K" u2="v" k="47" />
<hkern u1="K" u2="t" k="20" />
<hkern u1="K" u2="q" k="41" />
<hkern u1="K" u2="o" k="41" />
<hkern u1="K" u2="g" k="20" />
<hkern u1="K" u2="f" k="10" />
<hkern u1="K" u2="e" k="41" />
<hkern u1="K" u2="d" k="41" />
<hkern u1="K" u2="c" k="41" />
<hkern u1="K" u2="a" k="31" />
<hkern u1="K" u2="Z" k="10" />
<hkern u1="K" u2="Y" k="-25" />
<hkern u1="K" u2="X" k="-18" />
<hkern u1="K" u2="W" k="27" />
<hkern u1="K" u2="V" k="-20" />
<hkern u1="K" u2="U" k="20" />
<hkern u1="K" u2="T" k="-10" />
<hkern u1="K" u2="S" k="10" />
<hkern u1="K" u2="Q" k="49" />
<hkern u1="K" u2="O" k="49" />
<hkern u1="K" u2="G" k="49" />
<hkern u1="K" u2="C" k="49" />
<hkern u1="K" u2="&#x40;" k="61" />
<hkern u1="K" u2="&#x2d;" k="78" />
<hkern u1="K" u2="&#x26;" k="41" />
<hkern u1="L" u2="&#x2122;" k="143" />
<hkern u1="L" u2="&#x2039;" k="8" />
<hkern u1="L" u2="&#x201d;" k="127" />
<hkern u1="L" u2="&#x201c;" k="205" />
<hkern u1="L" u2="&#x2019;" k="127" />
<hkern u1="L" u2="&#x2018;" k="205" />
<hkern u1="L" u2="&#x178;" k="170" />
<hkern u1="L" u2="&#x153;" k="37" />
<hkern u1="L" u2="&#x152;" k="92" />
<hkern u1="L" u2="&#xe7;" k="37" />
<hkern u1="L" u2="&#xe6;" k="31" />
<hkern u1="L" u2="&#xdd;" k="170" />
<hkern u1="L" u2="&#xdc;" k="31" />
<hkern u1="L" u2="&#xdb;" k="31" />
<hkern u1="L" u2="&#xda;" k="31" />
<hkern u1="L" u2="&#xd9;" k="31" />
<hkern u1="L" u2="&#xd8;" k="92" />
<hkern u1="L" u2="&#xd6;" k="92" />
<hkern u1="L" u2="&#xd5;" k="92" />
<hkern u1="L" u2="&#xd4;" k="92" />
<hkern u1="L" u2="&#xd3;" k="92" />
<hkern u1="L" u2="&#xd2;" k="92" />
<hkern u1="L" u2="&#xc6;" k="-41" />
<hkern u1="L" u2="&#xc5;" k="-41" />
<hkern u1="L" u2="&#xc4;" k="-41" />
<hkern u1="L" u2="&#xc3;" k="-41" />
<hkern u1="L" u2="&#xc2;" k="-41" />
<hkern u1="L" u2="&#xc1;" k="-41" />
<hkern u1="L" u2="&#xc0;" k="-41" />
<hkern u1="L" u2="&#xae;" k="102" />
<hkern u1="L" u2="&#xab;" k="8" />
<hkern u1="L" u2="&#xa9;" k="102" />
<hkern u1="L" u2="y" k="90" />
<hkern u1="L" u2="w" k="29" />
<hkern u1="L" u2="v" k="90" />
<hkern u1="L" u2="q" k="37" />
<hkern u1="L" u2="o" k="37" />
<hkern u1="L" u2="e" k="37" />
<hkern u1="L" u2="d" k="37" />
<hkern u1="L" u2="c" k="37" />
<hkern u1="L" u2="a" k="31" />
<hkern u1="L" u2="\" k="102" />
<hkern u1="L" u2="Z" k="-2" />
<hkern u1="L" u2="Y" k="170" />
<hkern u1="L" u2="W" k="143" />
<hkern u1="L" u2="V" k="184" />
<hkern u1="L" u2="U" k="31" />
<hkern u1="L" u2="T" k="203" />
<hkern u1="L" u2="Q" k="92" />
<hkern u1="L" u2="O" k="92" />
<hkern u1="L" u2="J" k="-6" />
<hkern u1="L" u2="G" k="92" />
<hkern u1="L" u2="C" k="92" />
<hkern u1="L" u2="A" k="-41" />
<hkern u1="L" u2="&#x40;" k="102" />
<hkern u1="L" u2="&#x3f;" k="29" />
<hkern u1="L" u2="&#x2a;" k="25" />
<hkern u1="M" u2="y" k="20" />
<hkern u1="M" u2="v" k="20" />
<hkern u1="M" u2="&#x2f;" k="41" />
<hkern u1="N" u2="y" k="20" />
<hkern u1="N" u2="v" k="20" />
<hkern u1="N" u2="&#x2f;" k="41" />
<hkern u1="O" u2="&#x2026;" k="78" />
<hkern u1="O" u2="&#x201e;" k="78" />
<hkern u1="O" u2="&#x201c;" k="41" />
<hkern u1="O" u2="&#x201a;" k="78" />
<hkern u1="O" u2="&#x2018;" k="41" />
<hkern u1="O" u2="&#x178;" k="47" />
<hkern u1="O" u2="&#x153;" k="8" />
<hkern u1="O" u2="&#xe7;" k="8" />
<hkern u1="O" u2="&#xe6;" k="8" />
<hkern u1="O" u2="&#xdd;" k="47" />
<hkern u1="O" u2="&#xc6;" k="41" />
<hkern u1="O" u2="&#xc5;" k="41" />
<hkern u1="O" u2="&#xc4;" k="41" />
<hkern u1="O" u2="&#xc3;" k="41" />
<hkern u1="O" u2="&#xc2;" k="41" />
<hkern u1="O" u2="&#xc1;" k="41" />
<hkern u1="O" u2="&#xc0;" k="41" />
<hkern u1="O" u2="z" k="20" />
<hkern u1="O" u2="x" k="20" />
<hkern u1="O" u2="u" k="8" />
<hkern u1="O" u2="r" k="8" />
<hkern u1="O" u2="q" k="8" />
<hkern u1="O" u2="p" k="8" />
<hkern u1="O" u2="o" k="8" />
<hkern u1="O" u2="n" k="8" />
<hkern u1="O" u2="m" k="8" />
<hkern u1="O" u2="l" k="16" />
<hkern u1="O" u2="k" k="16" />
<hkern u1="O" u2="h" k="16" />
<hkern u1="O" u2="e" k="8" />
<hkern u1="O" u2="d" k="8" />
<hkern u1="O" u2="c" k="8" />
<hkern u1="O" u2="b" k="16" />
<hkern u1="O" u2="a" k="8" />
<hkern u1="O" u2="Z" k="78" />
<hkern u1="O" u2="Y" k="47" />
<hkern u1="O" u2="X" k="53" />
<hkern u1="O" u2="W" k="78" />
<hkern u1="O" u2="V" k="41" />
<hkern u1="O" u2="T" k="55" />
<hkern u1="O" u2="J" k="80" />
<hkern u1="O" u2="A" k="41" />
<hkern u1="O" u2="&#x3f;" k="41" />
<hkern u1="O" u2="&#x2f;" k="102" />
<hkern u1="O" u2="&#x2e;" k="78" />
<hkern u1="O" u2="&#x2c;" k="78" />
<hkern u1="P" u2="&#x2026;" k="123" />
<hkern u1="P" u2="&#x201e;" k="123" />
<hkern u1="P" u2="&#x201a;" k="123" />
<hkern u1="P" u2="&#x178;" k="-27" />
<hkern u1="P" u2="&#x153;" k="20" />
<hkern u1="P" u2="&#xe7;" k="20" />
<hkern u1="P" u2="&#xe6;" k="20" />
<hkern u1="P" u2="&#xdd;" k="-27" />
<hkern u1="P" u2="&#xc6;" k="74" />
<hkern u1="P" u2="&#xc5;" k="74" />
<hkern u1="P" u2="&#xc4;" k="74" />
<hkern u1="P" u2="&#xc3;" k="74" />
<hkern u1="P" u2="&#xc2;" k="74" />
<hkern u1="P" u2="&#xc1;" k="74" />
<hkern u1="P" u2="&#xc0;" k="74" />
<hkern u1="P" u2="y" k="-31" />
<hkern u1="P" u2="x" k="-10" />
<hkern u1="P" u2="w" k="-20" />
<hkern u1="P" u2="v" k="-31" />
<hkern u1="P" u2="t" k="-16" />
<hkern u1="P" u2="q" k="20" />
<hkern u1="P" u2="o" k="20" />
<hkern u1="P" u2="f" k="-16" />
<hkern u1="P" u2="e" k="20" />
<hkern u1="P" u2="d" k="20" />
<hkern u1="P" u2="c" k="20" />
<hkern u1="P" u2="a" k="20" />
<hkern u1="P" u2="Z" k="51" />
<hkern u1="P" u2="Y" k="-27" />
<hkern u1="P" u2="X" k="-4" />
<hkern u1="P" u2="W" k="-4" />
<hkern u1="P" u2="V" k="-39" />
<hkern u1="P" u2="T" k="-4" />
<hkern u1="P" u2="J" k="160" />
<hkern u1="P" u2="A" k="76" />
<hkern u1="P" u2="&#x2e;" k="123" />
<hkern u1="P" u2="&#x2c;" k="123" />
<hkern u1="P" u2="&#x2a;" k="-4" />
<hkern u1="P" u2="&#x26;" k="37" />
<hkern u1="Q" u2="&#x2026;" k="78" />
<hkern u1="Q" u2="&#x201e;" k="53" />
<hkern u1="Q" u2="&#x201c;" k="41" />
<hkern u1="Q" u2="&#x201a;" k="53" />
<hkern u1="Q" u2="&#x2018;" k="41" />
<hkern u1="Q" u2="&#x178;" k="47" />
<hkern u1="Q" u2="&#x153;" k="8" />
<hkern u1="Q" u2="&#xe7;" k="8" />
<hkern u1="Q" u2="&#xe6;" k="8" />
<hkern u1="Q" u2="&#xdd;" k="47" />
<hkern u1="Q" u2="&#xc6;" k="41" />
<hkern u1="Q" u2="&#xc5;" k="41" />
<hkern u1="Q" u2="&#xc4;" k="41" />
<hkern u1="Q" u2="&#xc3;" k="41" />
<hkern u1="Q" u2="&#xc2;" k="41" />
<hkern u1="Q" u2="&#xc1;" k="41" />
<hkern u1="Q" u2="&#xc0;" k="41" />
<hkern u1="Q" u2="z" k="20" />
<hkern u1="Q" u2="x" k="20" />
<hkern u1="Q" u2="u" k="8" />
<hkern u1="Q" u2="r" k="8" />
<hkern u1="Q" u2="q" k="8" />
<hkern u1="Q" u2="p" k="8" />
<hkern u1="Q" u2="o" k="8" />
<hkern u1="Q" u2="n" k="8" />
<hkern u1="Q" u2="m" k="8" />
<hkern u1="Q" u2="l" k="16" />
<hkern u1="Q" u2="k" k="16" />
<hkern u1="Q" u2="h" k="16" />
<hkern u1="Q" u2="e" k="8" />
<hkern u1="Q" u2="d" k="8" />
<hkern u1="Q" u2="c" k="8" />
<hkern u1="Q" u2="b" k="16" />
<hkern u1="Q" u2="a" k="8" />
<hkern u1="Q" u2="Z" k="78" />
<hkern u1="Q" u2="Y" k="47" />
<hkern u1="Q" u2="X" k="33" />
<hkern u1="Q" u2="W" k="78" />
<hkern u1="Q" u2="V" k="41" />
<hkern u1="Q" u2="T" k="55" />
<hkern u1="Q" u2="J" k="70" />
<hkern u1="Q" u2="A" k="41" />
<hkern u1="Q" u2="&#x3f;" k="41" />
<hkern u1="Q" u2="&#x2f;" k="61" />
<hkern u1="Q" u2="&#x2e;" k="-8" />
<hkern u1="Q" u2="&#x2c;" k="53" />
<hkern u1="R" u2="&#x178;" k="-27" />
<hkern u1="R" u2="&#x152;" k="2" />
<hkern u1="R" u2="&#xdd;" k="-27" />
<hkern u1="R" u2="&#xd8;" k="2" />
<hkern u1="R" u2="&#xd6;" k="2" />
<hkern u1="R" u2="&#xd5;" k="2" />
<hkern u1="R" u2="&#xd4;" k="2" />
<hkern u1="R" u2="&#xd3;" k="2" />
<hkern u1="R" u2="&#xd2;" k="2" />
<hkern u1="R" u2="Y" k="-27" />
<hkern u1="R" u2="X" k="-20" />
<hkern u1="R" u2="W" k="6" />
<hkern u1="R" u2="V" k="-31" />
<hkern u1="R" u2="T" k="16" />
<hkern u1="R" u2="Q" k="2" />
<hkern u1="R" u2="O" k="2" />
<hkern u1="R" u2="J" k="18" />
<hkern u1="R" u2="G" k="2" />
<hkern u1="R" u2="C" k="2" />
<hkern u1="R" u2="&#x26;" k="-20" />
<hkern u1="S" u2="&#x178;" k="-20" />
<hkern u1="S" u2="&#xdd;" k="-20" />
<hkern u1="S" u2="&#xc6;" k="-10" />
<hkern u1="S" u2="&#xc5;" k="-10" />
<hkern u1="S" u2="&#xc4;" k="-10" />
<hkern u1="S" u2="&#xc3;" k="-10" />
<hkern u1="S" u2="&#xc2;" k="-10" />
<hkern u1="S" u2="&#xc1;" k="-10" />
<hkern u1="S" u2="&#xc0;" k="-10" />
<hkern u1="S" u2="Y" k="-20" />
<hkern u1="S" u2="X" k="-10" />
<hkern u1="S" u2="W" k="4" />
<hkern u1="S" u2="V" k="-18" />
<hkern u1="S" u2="A" k="-10" />
<hkern u1="T" u2="&#x203a;" k="82" />
<hkern u1="T" u2="&#x2039;" k="225" />
<hkern u1="T" u2="&#x2026;" k="135" />
<hkern u1="T" u2="&#x201e;" k="135" />
<hkern u1="T" u2="&#x201a;" k="135" />
<hkern u1="T" u2="&#x2014;" k="135" />
<hkern u1="T" u2="&#x2013;" k="135" />
<hkern u1="T" u2="&#x178;" k="-45" />
<hkern u1="T" u2="&#x153;" k="184" />
<hkern u1="T" u2="&#x152;" k="55" />
<hkern u1="T" u2="&#xe7;" k="184" />
<hkern u1="T" u2="&#xe6;" k="174" />
<hkern u1="T" u2="&#xdd;" k="-45" />
<hkern u1="T" u2="&#xd8;" k="55" />
<hkern u1="T" u2="&#xd6;" k="55" />
<hkern u1="T" u2="&#xd5;" k="55" />
<hkern u1="T" u2="&#xd4;" k="55" />
<hkern u1="T" u2="&#xd3;" k="55" />
<hkern u1="T" u2="&#xd2;" k="55" />
<hkern u1="T" u2="&#xc6;" k="137" />
<hkern u1="T" u2="&#xc5;" k="137" />
<hkern u1="T" u2="&#xc4;" k="137" />
<hkern u1="T" u2="&#xc3;" k="137" />
<hkern u1="T" u2="&#xc2;" k="137" />
<hkern u1="T" u2="&#xc1;" k="137" />
<hkern u1="T" u2="&#xc0;" k="137" />
<hkern u1="T" u2="&#xbf;" k="102" />
<hkern u1="T" u2="&#xbb;" k="82" />
<hkern u1="T" u2="&#xae;" k="61" />
<hkern u1="T" u2="&#xab;" k="225" />
<hkern u1="T" u2="&#xa9;" k="61" />
<hkern u1="T" u2="z" k="139" />
<hkern u1="T" u2="y" k="119" />
<hkern u1="T" u2="x" k="139" />
<hkern u1="T" u2="w" k="102" />
<hkern u1="T" u2="v" k="119" />
<hkern u1="T" u2="u" k="139" />
<hkern u1="T" u2="t" k="41" />
<hkern u1="T" u2="s" k="184" />
<hkern u1="T" u2="r" k="139" />
<hkern u1="T" u2="q" k="184" />
<hkern u1="T" u2="p" k="139" />
<hkern u1="T" u2="o" k="184" />
<hkern u1="T" u2="n" k="139" />
<hkern u1="T" u2="m" k="139" />
<hkern u1="T" u2="g" k="195" />
<hkern u1="T" u2="f" k="20" />
<hkern u1="T" u2="e" k="184" />
<hkern u1="T" u2="d" k="184" />
<hkern u1="T" u2="c" k="184" />
<hkern u1="T" u2="a" k="174" />
<hkern u1="T" u2="\" k="-20" />
<hkern u1="T" u2="Z" k="18" />
<hkern u1="T" u2="Y" k="-45" />
<hkern u1="T" u2="X" k="-20" />
<hkern u1="T" u2="V" k="-41" />
<hkern u1="T" u2="T" k="-20" />
<hkern u1="T" u2="Q" k="55" />
<hkern u1="T" u2="O" k="55" />
<hkern u1="T" u2="J" k="180" />
<hkern u1="T" u2="G" k="55" />
<hkern u1="T" u2="C" k="55" />
<hkern u1="T" u2="A" k="131" />
<hkern u1="T" u2="&#x40;" k="61" />
<hkern u1="T" u2="&#x3b;" k="102" />
<hkern u1="T" u2="&#x3a;" k="102" />
<hkern u1="T" u2="&#x2f;" k="20" />
<hkern u1="T" u2="&#x2e;" k="135" />
<hkern u1="T" u2="&#x2d;" k="135" />
<hkern u1="T" u2="&#x2c;" k="135" />
<hkern u1="T" u2="&#x2a;" k="-6" />
<hkern u1="T" u2="&#x26;" k="82" />
<hkern u1="U" u2="&#x2026;" k="20" />
<hkern u1="U" u2="&#x201e;" k="20" />
<hkern u1="U" u2="&#x201a;" k="20" />
<hkern u1="U" u2="&#xc6;" k="8" />
<hkern u1="U" u2="&#xc5;" k="8" />
<hkern u1="U" u2="&#xc4;" k="8" />
<hkern u1="U" u2="&#xc3;" k="8" />
<hkern u1="U" u2="&#xc2;" k="8" />
<hkern u1="U" u2="&#xc1;" k="8" />
<hkern u1="U" u2="&#xc0;" k="8" />
<hkern u1="U" u2="J" k="20" />
<hkern u1="U" u2="A" k="16" />
<hkern u1="U" u2="&#x2e;" k="20" />
<hkern u1="U" u2="&#x2c;" k="20" />
<hkern u1="V" u2="&#x203a;" k="41" />
<hkern u1="V" u2="&#x2039;" k="102" />
<hkern u1="V" u2="&#x2026;" k="127" />
<hkern u1="V" u2="&#x201e;" k="127" />
<hkern u1="V" u2="&#x201a;" k="127" />
<hkern u1="V" u2="&#x2014;" k="123" />
<hkern u1="V" u2="&#x2013;" k="123" />
<hkern u1="V" u2="&#x178;" k="-57" />
<hkern u1="V" u2="&#x153;" k="90" />
<hkern u1="V" u2="&#x152;" k="41" />
<hkern u1="V" u2="&#xfe;" k="4" />
<hkern u1="V" u2="&#xe7;" k="90" />
<hkern u1="V" u2="&#xe6;" k="86" />
<hkern u1="V" u2="&#xdd;" k="-57" />
<hkern u1="V" u2="&#xd8;" k="41" />
<hkern u1="V" u2="&#xd6;" k="41" />
<hkern u1="V" u2="&#xd5;" k="41" />
<hkern u1="V" u2="&#xd4;" k="41" />
<hkern u1="V" u2="&#xd3;" k="41" />
<hkern u1="V" u2="&#xd2;" k="41" />
<hkern u1="V" u2="&#xc6;" k="115" />
<hkern u1="V" u2="&#xc5;" k="115" />
<hkern u1="V" u2="&#xc4;" k="115" />
<hkern u1="V" u2="&#xc3;" k="115" />
<hkern u1="V" u2="&#xc2;" k="115" />
<hkern u1="V" u2="&#xc1;" k="115" />
<hkern u1="V" u2="&#xc0;" k="115" />
<hkern u1="V" u2="&#xbb;" k="41" />
<hkern u1="V" u2="&#xae;" k="4" />
<hkern u1="V" u2="&#xab;" k="102" />
<hkern u1="V" u2="&#xa9;" k="4" />
<hkern u1="V" u2="&#x7d;" k="-61" />
<hkern u1="V" u2="y" k="20" />
<hkern u1="V" u2="x" k="51" />
<hkern u1="V" u2="w" k="20" />
<hkern u1="V" u2="v" k="20" />
<hkern u1="V" u2="u" k="61" />
<hkern u1="V" u2="t" k="20" />
<hkern u1="V" u2="s" k="82" />
<hkern u1="V" u2="r" k="61" />
<hkern u1="V" u2="q" k="90" />
<hkern u1="V" u2="p" k="61" />
<hkern u1="V" u2="o" k="90" />
<hkern u1="V" u2="n" k="61" />
<hkern u1="V" u2="m" k="61" />
<hkern u1="V" u2="l" k="20" />
<hkern u1="V" u2="k" k="20" />
<hkern u1="V" u2="h" k="20" />
<hkern u1="V" u2="g" k="72" />
<hkern u1="V" u2="f" k="20" />
<hkern u1="V" u2="e" k="90" />
<hkern u1="V" u2="d" k="90" />
<hkern u1="V" u2="c" k="90" />
<hkern u1="V" u2="b" k="20" />
<hkern u1="V" u2="a" k="86" />
<hkern u1="V" u2="]" k="-61" />
<hkern u1="V" u2="Y" k="-57" />
<hkern u1="V" u2="X" k="-33" />
<hkern u1="V" u2="W" k="12" />
<hkern u1="V" u2="V" k="-35" />
<hkern u1="V" u2="T" k="-41" />
<hkern u1="V" u2="S" k="-16" />
<hkern u1="V" u2="Q" k="41" />
<hkern u1="V" u2="O" k="41" />
<hkern u1="V" u2="J" k="119" />
<hkern u1="V" u2="G" k="41" />
<hkern u1="V" u2="C" k="41" />
<hkern u1="V" u2="A" k="127" />
<hkern u1="V" u2="&#x40;" k="4" />
<hkern u1="V" u2="&#x3b;" k="41" />
<hkern u1="V" u2="&#x3a;" k="41" />
<hkern u1="V" u2="&#x2e;" k="127" />
<hkern u1="V" u2="&#x2d;" k="123" />
<hkern u1="V" u2="&#x2c;" k="127" />
<hkern u1="V" u2="&#x29;" k="-61" />
<hkern u1="V" u2="&#x26;" k="78" />
<hkern u1="W" u2="&#x203a;" k="41" />
<hkern u1="W" u2="&#x2039;" k="82" />
<hkern u1="W" u2="&#x2026;" k="45" />
<hkern u1="W" u2="&#x201e;" k="45" />
<hkern u1="W" u2="&#x201a;" k="45" />
<hkern u1="W" u2="&#x2014;" k="82" />
<hkern u1="W" u2="&#x2013;" k="82" />
<hkern u1="W" u2="&#x153;" k="100" />
<hkern u1="W" u2="&#x152;" k="78" />
<hkern u1="W" u2="&#xfe;" k="4" />
<hkern u1="W" u2="&#xe7;" k="100" />
<hkern u1="W" u2="&#xe6;" k="96" />
<hkern u1="W" u2="&#xd8;" k="78" />
<hkern u1="W" u2="&#xd6;" k="78" />
<hkern u1="W" u2="&#xd5;" k="78" />
<hkern u1="W" u2="&#xd4;" k="78" />
<hkern u1="W" u2="&#xd3;" k="78" />
<hkern u1="W" u2="&#xd2;" k="78" />
<hkern u1="W" u2="&#xc6;" k="109" />
<hkern u1="W" u2="&#xc5;" k="109" />
<hkern u1="W" u2="&#xc4;" k="109" />
<hkern u1="W" u2="&#xc3;" k="109" />
<hkern u1="W" u2="&#xc2;" k="109" />
<hkern u1="W" u2="&#xc1;" k="109" />
<hkern u1="W" u2="&#xc0;" k="109" />
<hkern u1="W" u2="&#xbb;" k="41" />
<hkern u1="W" u2="&#xae;" k="41" />
<hkern u1="W" u2="&#xab;" k="82" />
<hkern u1="W" u2="&#xa9;" k="41" />
<hkern u1="W" u2="&#x7d;" k="-20" />
<hkern u1="W" u2="z" k="61" />
<hkern u1="W" u2="y" k="61" />
<hkern u1="W" u2="x" k="61" />
<hkern u1="W" u2="w" k="61" />
<hkern u1="W" u2="v" k="61" />
<hkern u1="W" u2="u" k="72" />
<hkern u1="W" u2="t" k="61" />
<hkern u1="W" u2="s" k="102" />
<hkern u1="W" u2="r" k="72" />
<hkern u1="W" u2="q" k="100" />
<hkern u1="W" u2="p" k="72" />
<hkern u1="W" u2="o" k="100" />
<hkern u1="W" u2="n" k="72" />
<hkern u1="W" u2="m" k="72" />
<hkern u1="W" u2="l" k="20" />
<hkern u1="W" u2="k" k="20" />
<hkern u1="W" u2="j" k="41" />
<hkern u1="W" u2="i" k="41" />
<hkern u1="W" u2="h" k="20" />
<hkern u1="W" u2="g" k="102" />
<hkern u1="W" u2="f" k="31" />
<hkern u1="W" u2="e" k="100" />
<hkern u1="W" u2="d" k="100" />
<hkern u1="W" u2="c" k="100" />
<hkern u1="W" u2="b" k="20" />
<hkern u1="W" u2="a" k="96" />
<hkern u1="W" u2="]" k="-20" />
<hkern u1="W" u2="X" k="25" />
<hkern u1="W" u2="W" k="12" />
<hkern u1="W" u2="V" k="12" />
<hkern u1="W" u2="S" k="6" />
<hkern u1="W" u2="Q" k="78" />
<hkern u1="W" u2="O" k="78" />
<hkern u1="W" u2="J" k="100" />
<hkern u1="W" u2="G" k="78" />
<hkern u1="W" u2="C" k="78" />
<hkern u1="W" u2="A" k="115" />
<hkern u1="W" u2="&#x40;" k="41" />
<hkern u1="W" u2="&#x3b;" k="41" />
<hkern u1="W" u2="&#x3a;" k="41" />
<hkern u1="W" u2="&#x2e;" k="45" />
<hkern u1="W" u2="&#x2d;" k="82" />
<hkern u1="W" u2="&#x2c;" k="45" />
<hkern u1="W" u2="&#x29;" k="-20" />
<hkern u1="W" u2="&#x26;" k="68" />
<hkern u1="X" u2="&#x2014;" k="61" />
<hkern u1="X" u2="&#x2013;" k="61" />
<hkern u1="X" u2="&#x178;" k="-37" />
<hkern u1="X" u2="&#x153;" k="20" />
<hkern u1="X" u2="&#x152;" k="53" />
<hkern u1="X" u2="&#xe7;" k="20" />
<hkern u1="X" u2="&#xe6;" k="20" />
<hkern u1="X" u2="&#xdd;" k="-37" />
<hkern u1="X" u2="&#xd8;" k="53" />
<hkern u1="X" u2="&#xd6;" k="53" />
<hkern u1="X" u2="&#xd5;" k="53" />
<hkern u1="X" u2="&#xd4;" k="53" />
<hkern u1="X" u2="&#xd3;" k="53" />
<hkern u1="X" u2="&#xd2;" k="53" />
<hkern u1="X" u2="&#xc6;" k="-20" />
<hkern u1="X" u2="&#xc5;" k="-20" />
<hkern u1="X" u2="&#xc4;" k="-20" />
<hkern u1="X" u2="&#xc3;" k="-20" />
<hkern u1="X" u2="&#xc2;" k="-20" />
<hkern u1="X" u2="&#xc1;" k="-20" />
<hkern u1="X" u2="&#xc0;" k="-20" />
<hkern u1="X" u2="&#xae;" k="12" />
<hkern u1="X" u2="&#xa9;" k="12" />
<hkern u1="X" u2="&#x7d;" k="-82" />
<hkern u1="X" u2="y" k="41" />
<hkern u1="X" u2="w" k="20" />
<hkern u1="X" u2="v" k="41" />
<hkern u1="X" u2="u" k="20" />
<hkern u1="X" u2="t" k="20" />
<hkern u1="X" u2="r" k="20" />
<hkern u1="X" u2="q" k="20" />
<hkern u1="X" u2="p" k="20" />
<hkern u1="X" u2="o" k="20" />
<hkern u1="X" u2="n" k="20" />
<hkern u1="X" u2="m" k="20" />
<hkern u1="X" u2="f" k="20" />
<hkern u1="X" u2="e" k="20" />
<hkern u1="X" u2="d" k="20" />
<hkern u1="X" u2="c" k="20" />
<hkern u1="X" u2="a" k="20" />
<hkern u1="X" u2="]" k="-82" />
<hkern u1="X" u2="Y" k="-37" />
<hkern u1="X" u2="X" k="-16" />
<hkern u1="X" u2="W" k="25" />
<hkern u1="X" u2="V" k="-33" />
<hkern u1="X" u2="T" k="-20" />
<hkern u1="X" u2="S" k="-12" />
<hkern u1="X" u2="Q" k="53" />
<hkern u1="X" u2="O" k="53" />
<hkern u1="X" u2="G" k="53" />
<hkern u1="X" u2="C" k="53" />
<hkern u1="X" u2="A" k="-20" />
<hkern u1="X" u2="&#x40;" k="12" />
<hkern u1="X" u2="&#x2d;" k="61" />
<hkern u1="X" u2="&#x29;" k="-82" />
<hkern u1="X" u2="&#x26;" k="33" />
<hkern u1="Y" u2="&#x203a;" k="82" />
<hkern u1="Y" u2="&#x2039;" k="123" />
<hkern u1="Y" u2="&#x2026;" k="102" />
<hkern u1="Y" u2="&#x201e;" k="102" />
<hkern u1="Y" u2="&#x201a;" k="102" />
<hkern u1="Y" u2="&#x2014;" k="102" />
<hkern u1="Y" u2="&#x2013;" k="102" />
<hkern u1="Y" u2="&#x153;" k="121" />
<hkern u1="Y" u2="&#x152;" k="47" />
<hkern u1="Y" u2="&#xe7;" k="121" />
<hkern u1="Y" u2="&#xe6;" k="117" />
<hkern u1="Y" u2="&#xd8;" k="47" />
<hkern u1="Y" u2="&#xd6;" k="47" />
<hkern u1="Y" u2="&#xd5;" k="47" />
<hkern u1="Y" u2="&#xd4;" k="47" />
<hkern u1="Y" u2="&#xd3;" k="47" />
<hkern u1="Y" u2="&#xd2;" k="47" />
<hkern u1="Y" u2="&#xc6;" k="104" />
<hkern u1="Y" u2="&#xc5;" k="104" />
<hkern u1="Y" u2="&#xc4;" k="104" />
<hkern u1="Y" u2="&#xc3;" k="104" />
<hkern u1="Y" u2="&#xc2;" k="104" />
<hkern u1="Y" u2="&#xc1;" k="104" />
<hkern u1="Y" u2="&#xc0;" k="104" />
<hkern u1="Y" u2="&#xbb;" k="82" />
<hkern u1="Y" u2="&#xae;" k="61" />
<hkern u1="Y" u2="&#xab;" k="123" />
<hkern u1="Y" u2="&#xa9;" k="61" />
<hkern u1="Y" u2="&#x7d;" k="-102" />
<hkern u1="Y" u2="z" k="61" />
<hkern u1="Y" u2="y" k="41" />
<hkern u1="Y" u2="x" k="41" />
<hkern u1="Y" u2="w" k="20" />
<hkern u1="Y" u2="v" k="41" />
<hkern u1="Y" u2="u" k="82" />
<hkern u1="Y" u2="t" k="41" />
<hkern u1="Y" u2="s" k="104" />
<hkern u1="Y" u2="r" k="82" />
<hkern u1="Y" u2="q" k="121" />
<hkern u1="Y" u2="p" k="82" />
<hkern u1="Y" u2="o" k="121" />
<hkern u1="Y" u2="n" k="82" />
<hkern u1="Y" u2="m" k="82" />
<hkern u1="Y" u2="g" k="82" />
<hkern u1="Y" u2="f" k="20" />
<hkern u1="Y" u2="e" k="121" />
<hkern u1="Y" u2="d" k="121" />
<hkern u1="Y" u2="c" k="121" />
<hkern u1="Y" u2="a" k="117" />
<hkern u1="Y" u2="]" k="-102" />
<hkern u1="Y" u2="X" k="-37" />
<hkern u1="Y" u2="V" k="-57" />
<hkern u1="Y" u2="T" k="-45" />
<hkern u1="Y" u2="S" k="-20" />
<hkern u1="Y" u2="Q" k="47" />
<hkern u1="Y" u2="O" k="47" />
<hkern u1="Y" u2="J" k="152" />
<hkern u1="Y" u2="G" k="47" />
<hkern u1="Y" u2="C" k="47" />
<hkern u1="Y" u2="A" k="106" />
<hkern u1="Y" u2="&#x40;" k="61" />
<hkern u1="Y" u2="&#x3b;" k="61" />
<hkern u1="Y" u2="&#x3a;" k="61" />
<hkern u1="Y" u2="&#x2f;" k="25" />
<hkern u1="Y" u2="&#x2e;" k="102" />
<hkern u1="Y" u2="&#x2d;" k="102" />
<hkern u1="Y" u2="&#x2c;" k="102" />
<hkern u1="Y" u2="&#x29;" k="-102" />
<hkern u1="Y" u2="&#x26;" k="61" />
<hkern u1="Z" u2="&#x2014;" k="86" />
<hkern u1="Z" u2="&#x2013;" k="86" />
<hkern u1="Z" u2="&#x153;" k="70" />
<hkern u1="Z" u2="&#x152;" k="76" />
<hkern u1="Z" u2="&#xfe;" k="4" />
<hkern u1="Z" u2="&#xf0;" k="61" />
<hkern u1="Z" u2="&#xe7;" k="70" />
<hkern u1="Z" u2="&#xe6;" k="49" />
<hkern u1="Z" u2="&#xd8;" k="76" />
<hkern u1="Z" u2="&#xd6;" k="76" />
<hkern u1="Z" u2="&#xd5;" k="76" />
<hkern u1="Z" u2="&#xd4;" k="76" />
<hkern u1="Z" u2="&#xd3;" k="76" />
<hkern u1="Z" u2="&#xd2;" k="76" />
<hkern u1="Z" u2="&#xc6;" k="20" />
<hkern u1="Z" u2="&#xc5;" k="20" />
<hkern u1="Z" u2="&#xc4;" k="20" />
<hkern u1="Z" u2="&#xc3;" k="20" />
<hkern u1="Z" u2="&#xc2;" k="20" />
<hkern u1="Z" u2="&#xc1;" k="20" />
<hkern u1="Z" u2="&#xc0;" k="20" />
<hkern u1="Z" u2="y" k="61" />
<hkern u1="Z" u2="w" k="20" />
<hkern u1="Z" u2="v" k="61" />
<hkern u1="Z" u2="u" k="41" />
<hkern u1="Z" u2="r" k="41" />
<hkern u1="Z" u2="q" k="70" />
<hkern u1="Z" u2="p" k="41" />
<hkern u1="Z" u2="o" k="70" />
<hkern u1="Z" u2="n" k="41" />
<hkern u1="Z" u2="m" k="41" />
<hkern u1="Z" u2="l" k="20" />
<hkern u1="Z" u2="k" k="20" />
<hkern u1="Z" u2="j" k="31" />
<hkern u1="Z" u2="i" k="41" />
<hkern u1="Z" u2="h" k="20" />
<hkern u1="Z" u2="g" k="41" />
<hkern u1="Z" u2="f" k="41" />
<hkern u1="Z" u2="e" k="70" />
<hkern u1="Z" u2="d" k="70" />
<hkern u1="Z" u2="c" k="70" />
<hkern u1="Z" u2="b" k="20" />
<hkern u1="Z" u2="a" k="49" />
<hkern u1="Z" u2="T" k="14" />
<hkern u1="Z" u2="Q" k="76" />
<hkern u1="Z" u2="O" k="76" />
<hkern u1="Z" u2="J" k="12" />
<hkern u1="Z" u2="G" k="76" />
<hkern u1="Z" u2="C" k="76" />
<hkern u1="Z" u2="A" k="20" />
<hkern u1="Z" u2="&#x2d;" k="86" />
<hkern u1="[" u2="&#x178;" k="-102" />
<hkern u1="[" u2="&#xdd;" k="-102" />
<hkern u1="[" u2="j" k="-307" />
<hkern u1="[" u2="g" k="-41" />
<hkern u1="[" u2="Y" k="-102" />
<hkern u1="[" u2="X" k="-82" />
<hkern u1="[" u2="W" k="-20" />
<hkern u1="[" u2="V" k="-61" />
<hkern u1="[" u2="J" k="8" />
<hkern u1="[" u2="&#x37;" k="-8" />
<hkern u1="\" u2="&#x178;" k="123" />
<hkern u1="\" u2="&#x153;" k="20" />
<hkern u1="\" u2="&#xe7;" k="20" />
<hkern u1="\" u2="&#xe6;" k="20" />
<hkern u1="\" u2="&#xdd;" k="123" />
<hkern u1="\" u2="q" k="20" />
<hkern u1="\" u2="o" k="20" />
<hkern u1="\" u2="j" k="-266" />
<hkern u1="\" u2="e" k="20" />
<hkern u1="\" u2="d" k="20" />
<hkern u1="\" u2="c" k="20" />
<hkern u1="\" u2="a" k="20" />
<hkern u1="\" u2="Y" k="123" />
<hkern u1="\" u2="W" k="102" />
<hkern u1="\" u2="V" k="143" />
<hkern u1="\" u2="T" k="184" />
<hkern u1="a" u2="&#x2122;" k="41" />
<hkern u1="a" u2="&#x201c;" k="61" />
<hkern u1="a" u2="&#x2018;" k="61" />
<hkern u1="a" u2="y" k="12" />
<hkern u1="a" u2="v" k="12" />
<hkern u1="a" u2="t" k="20" />
<hkern u1="a" u2="f" k="2" />
<hkern u1="a" u2="\" k="20" />
<hkern u1="a" u2="&#x3f;" k="25" />
<hkern u1="b" u2="&#x2122;" k="61" />
<hkern u1="b" u2="&#x2026;" k="41" />
<hkern u1="b" u2="&#x201e;" k="41" />
<hkern u1="b" u2="&#x201c;" k="74" />
<hkern u1="b" u2="&#x201a;" k="41" />
<hkern u1="b" u2="&#x2018;" k="74" />
<hkern u1="b" u2="z" k="33" />
<hkern u1="b" u2="y" k="27" />
<hkern u1="b" u2="x" k="35" />
<hkern u1="b" u2="w" k="6" />
<hkern u1="b" u2="v" k="27" />
<hkern u1="b" u2="t" k="6" />
<hkern u1="b" u2="f" k="4" />
<hkern u1="b" u2="\" k="20" />
<hkern u1="b" u2="&#x3f;" k="47" />
<hkern u1="b" u2="&#x3b;" k="20" />
<hkern u1="b" u2="&#x3a;" k="20" />
<hkern u1="b" u2="&#x2f;" k="20" />
<hkern u1="b" u2="&#x2e;" k="41" />
<hkern u1="b" u2="&#x2c;" k="41" />
<hkern u1="b" u2="&#x21;" k="8" />
<hkern u1="c" u2="&#x2039;" k="8" />
<hkern u1="c" u2="&#xab;" k="8" />
<hkern u1="e" u2="y" k="25" />
<hkern u1="e" u2="x" k="33" />
<hkern u1="e" u2="w" k="4" />
<hkern u1="e" u2="v" k="25" />
<hkern u1="e" u2="t" k="2" />
<hkern u1="e" u2="f" k="4" />
<hkern u1="f" u2="&#x2122;" k="-86" />
<hkern u1="f" u2="&#x203a;" k="29" />
<hkern u1="f" u2="&#x2039;" k="82" />
<hkern u1="f" u2="&#x2026;" k="152" />
<hkern u1="f" u2="&#x201e;" k="152" />
<hkern u1="f" u2="&#x201d;" k="-102" />
<hkern u1="f" u2="&#x201c;" k="-61" />
<hkern u1="f" u2="&#x201a;" k="152" />
<hkern u1="f" u2="&#x2019;" k="-102" />
<hkern u1="f" u2="&#x2018;" k="-61" />
<hkern u1="f" u2="&#x2014;" k="53" />
<hkern u1="f" u2="&#x2013;" k="53" />
<hkern u1="f" u2="&#x153;" k="39" />
<hkern u1="f" u2="&#xfe;" k="-6" />
<hkern u1="f" u2="&#xf0;" k="41" />
<hkern u1="f" u2="&#xe7;" k="39" />
<hkern u1="f" u2="&#xe6;" k="39" />
<hkern u1="f" u2="&#xbb;" k="29" />
<hkern u1="f" u2="&#xae;" k="-8" />
<hkern u1="f" u2="&#xab;" k="82" />
<hkern u1="f" u2="&#xa9;" k="-8" />
<hkern u1="f" u2="&#x7d;" k="-143" />
<hkern u1="f" u2="u" k="8" />
<hkern u1="f" u2="t" k="-4" />
<hkern u1="f" u2="r" k="8" />
<hkern u1="f" u2="q" k="39" />
<hkern u1="f" u2="p" k="8" />
<hkern u1="f" u2="o" k="39" />
<hkern u1="f" u2="n" k="8" />
<hkern u1="f" u2="m" k="8" />
<hkern u1="f" u2="l" k="-6" />
<hkern u1="f" u2="k" k="-6" />
<hkern u1="f" u2="h" k="-6" />
<hkern u1="f" u2="g" k="4" />
<hkern u1="f" u2="f" k="-4" />
<hkern u1="f" u2="e" k="39" />
<hkern u1="f" u2="d" k="39" />
<hkern u1="f" u2="c" k="39" />
<hkern u1="f" u2="b" k="-6" />
<hkern u1="f" u2="a" k="39" />
<hkern u1="f" u2="]" k="-143" />
<hkern u1="f" u2="\" k="-82" />
<hkern u1="f" u2="&#x40;" k="-8" />
<hkern u1="f" u2="&#x3f;" k="-82" />
<hkern u1="f" u2="&#x3b;" k="12" />
<hkern u1="f" u2="&#x3a;" k="12" />
<hkern u1="f" u2="&#x2f;" k="111" />
<hkern u1="f" u2="&#x2e;" k="152" />
<hkern u1="f" u2="&#x2d;" k="53" />
<hkern u1="f" u2="&#x2c;" k="152" />
<hkern u1="f" u2="&#x2a;" k="-45" />
<hkern u1="f" u2="&#x29;" k="-143" />
<hkern u1="f" u2="&#x26;" k="53" />
<hkern u1="f" u2="&#x21;" k="-16" />
<hkern u1="g" u2="&#x201d;" k="-82" />
<hkern u1="g" u2="&#x201c;" k="-41" />
<hkern u1="g" u2="&#x2019;" k="-82" />
<hkern u1="g" u2="&#x2018;" k="-41" />
<hkern u1="g" u2="&#xae;" k="-8" />
<hkern u1="g" u2="&#xa9;" k="-8" />
<hkern u1="g" u2="&#x7d;" k="-41" />
<hkern u1="g" u2="t" k="-20" />
<hkern u1="g" u2="j" k="-102" />
<hkern u1="g" u2="g" k="-20" />
<hkern u1="g" u2="]" k="-41" />
<hkern u1="g" u2="&#x40;" k="-8" />
<hkern u1="g" u2="&#x3f;" k="-12" />
<hkern u1="g" u2="&#x3b;" k="-41" />
<hkern u1="g" u2="&#x2f;" k="-86" />
<hkern u1="g" u2="&#x2c;" k="-57" />
<hkern u1="g" u2="&#x2a;" k="-16" />
<hkern u1="g" u2="&#x29;" k="-41" />
<hkern u1="h" u2="&#x2122;" k="41" />
<hkern u1="h" u2="&#x201c;" k="61" />
<hkern u1="h" u2="&#x2018;" k="61" />
<hkern u1="h" u2="y" k="12" />
<hkern u1="h" u2="v" k="12" />
<hkern u1="h" u2="t" k="20" />
<hkern u1="h" u2="f" k="2" />
<hkern u1="h" u2="\" k="20" />
<hkern u1="h" u2="&#x3f;" k="25" />
<hkern u1="k" u2="&#x2039;" k="16" />
<hkern u1="k" u2="&#x153;" k="23" />
<hkern u1="k" u2="&#xe7;" k="23" />
<hkern u1="k" u2="&#xe6;" k="23" />
<hkern u1="k" u2="&#xab;" k="16" />
<hkern u1="k" u2="y" k="2" />
<hkern u1="k" u2="v" k="2" />
<hkern u1="k" u2="q" k="23" />
<hkern u1="k" u2="o" k="23" />
<hkern u1="k" u2="g" k="41" />
<hkern u1="k" u2="e" k="23" />
<hkern u1="k" u2="d" k="23" />
<hkern u1="k" u2="c" k="23" />
<hkern u1="k" u2="a" k="23" />
<hkern u1="m" u2="&#x2122;" k="41" />
<hkern u1="m" u2="&#x201c;" k="61" />
<hkern u1="m" u2="&#x2018;" k="61" />
<hkern u1="m" u2="y" k="12" />
<hkern u1="m" u2="v" k="12" />
<hkern u1="m" u2="t" k="20" />
<hkern u1="m" u2="f" k="2" />
<hkern u1="m" u2="\" k="20" />
<hkern u1="m" u2="&#x3f;" k="25" />
<hkern u1="n" u2="&#x2122;" k="41" />
<hkern u1="n" u2="&#x201c;" k="61" />
<hkern u1="n" u2="&#x2018;" k="61" />
<hkern u1="n" u2="y" k="12" />
<hkern u1="n" u2="v" k="12" />
<hkern u1="n" u2="t" k="20" />
<hkern u1="n" u2="f" k="2" />
<hkern u1="n" u2="\" k="20" />
<hkern u1="n" u2="&#x3f;" k="25" />
<hkern u1="o" u2="&#x2122;" k="61" />
<hkern u1="o" u2="&#x2026;" k="41" />
<hkern u1="o" u2="&#x201e;" k="41" />
<hkern u1="o" u2="&#x201c;" k="74" />
<hkern u1="o" u2="&#x201a;" k="41" />
<hkern u1="o" u2="&#x2018;" k="74" />
<hkern u1="o" u2="z" k="33" />
<hkern u1="o" u2="y" k="27" />
<hkern u1="o" u2="x" k="35" />
<hkern u1="o" u2="w" k="6" />
<hkern u1="o" u2="v" k="27" />
<hkern u1="o" u2="t" k="6" />
<hkern u1="o" u2="f" k="4" />
<hkern u1="o" u2="\" k="20" />
<hkern u1="o" u2="&#x3f;" k="47" />
<hkern u1="o" u2="&#x3b;" k="20" />
<hkern u1="o" u2="&#x3a;" k="20" />
<hkern u1="o" u2="&#x2f;" k="20" />
<hkern u1="o" u2="&#x2e;" k="41" />
<hkern u1="o" u2="&#x2c;" k="41" />
<hkern u1="o" u2="&#x21;" k="8" />
<hkern u1="p" u2="&#x2122;" k="61" />
<hkern u1="p" u2="&#x2026;" k="41" />
<hkern u1="p" u2="&#x201e;" k="41" />
<hkern u1="p" u2="&#x201c;" k="74" />
<hkern u1="p" u2="&#x201a;" k="41" />
<hkern u1="p" u2="&#x2018;" k="74" />
<hkern u1="p" u2="z" k="33" />
<hkern u1="p" u2="y" k="27" />
<hkern u1="p" u2="x" k="35" />
<hkern u1="p" u2="w" k="6" />
<hkern u1="p" u2="v" k="27" />
<hkern u1="p" u2="t" k="6" />
<hkern u1="p" u2="f" k="4" />
<hkern u1="p" u2="\" k="20" />
<hkern u1="p" u2="&#x3f;" k="47" />
<hkern u1="p" u2="&#x3b;" k="20" />
<hkern u1="p" u2="&#x3a;" k="20" />
<hkern u1="p" u2="&#x2f;" k="20" />
<hkern u1="p" u2="&#x2e;" k="41" />
<hkern u1="p" u2="&#x2c;" k="41" />
<hkern u1="p" u2="&#x21;" k="8" />
<hkern u1="r" u2="&#x2026;" k="119" />
<hkern u1="r" u2="&#x201e;" k="119" />
<hkern u1="r" u2="&#x201d;" k="-82" />
<hkern u1="r" u2="&#x201c;" k="-41" />
<hkern u1="r" u2="&#x201a;" k="119" />
<hkern u1="r" u2="&#x2019;" k="-82" />
<hkern u1="r" u2="&#x2018;" k="-41" />
<hkern u1="r" u2="&#x2014;" k="37" />
<hkern u1="r" u2="&#x2013;" k="37" />
<hkern u1="r" u2="&#x153;" k="12" />
<hkern u1="r" u2="&#xe7;" k="12" />
<hkern u1="r" u2="&#xe6;" k="12" />
<hkern u1="r" u2="x" k="2" />
<hkern u1="r" u2="t" k="-33" />
<hkern u1="r" u2="q" k="12" />
<hkern u1="r" u2="o" k="12" />
<hkern u1="r" u2="g" k="20" />
<hkern u1="r" u2="f" k="-25" />
<hkern u1="r" u2="e" k="12" />
<hkern u1="r" u2="d" k="12" />
<hkern u1="r" u2="c" k="12" />
<hkern u1="r" u2="a" k="12" />
<hkern u1="r" u2="&#x3f;" k="-33" />
<hkern u1="r" u2="&#x2f;" k="123" />
<hkern u1="r" u2="&#x2e;" k="119" />
<hkern u1="r" u2="&#x2d;" k="37" />
<hkern u1="r" u2="&#x2c;" k="119" />
<hkern u1="r" u2="&#x2a;" k="-4" />
<hkern u1="r" u2="&#x26;" k="12" />
<hkern u1="s" u2="s" k="-2" />
<hkern u1="s" u2="g" k="4" />
<hkern u1="s" u2="&#x3f;" k="8" />
<hkern u1="t" u2="&#x2026;" k="-20" />
<hkern u1="t" u2="&#x201e;" k="-20" />
<hkern u1="t" u2="&#x201c;" k="-20" />
<hkern u1="t" u2="&#x201a;" k="-20" />
<hkern u1="t" u2="&#x2018;" k="-20" />
<hkern u1="t" u2="&#x153;" k="14" />
<hkern u1="t" u2="&#xe7;" k="14" />
<hkern u1="t" u2="&#xe6;" k="14" />
<hkern u1="t" u2="y" k="6" />
<hkern u1="t" u2="v" k="6" />
<hkern u1="t" u2="t" k="2" />
<hkern u1="t" u2="q" k="14" />
<hkern u1="t" u2="o" k="14" />
<hkern u1="t" u2="e" k="14" />
<hkern u1="t" u2="d" k="14" />
<hkern u1="t" u2="c" k="14" />
<hkern u1="t" u2="a" k="14" />
<hkern u1="t" u2="&#x2e;" k="-20" />
<hkern u1="t" u2="&#x2c;" k="-20" />
<hkern u1="u" u2="&#x3f;" k="4" />
<hkern u1="v" u2="&#x2039;" k="12" />
<hkern u1="v" u2="&#x2026;" k="102" />
<hkern u1="v" u2="&#x201e;" k="102" />
<hkern u1="v" u2="&#x201d;" k="-61" />
<hkern u1="v" u2="&#x201c;" k="-61" />
<hkern u1="v" u2="&#x201a;" k="102" />
<hkern u1="v" u2="&#x2019;" k="-61" />
<hkern u1="v" u2="&#x2018;" k="-61" />
<hkern u1="v" u2="&#x2014;" k="20" />
<hkern u1="v" u2="&#x2013;" k="20" />
<hkern u1="v" u2="&#x153;" k="27" />
<hkern u1="v" u2="&#xe7;" k="27" />
<hkern u1="v" u2="&#xe6;" k="27" />
<hkern u1="v" u2="&#xab;" k="12" />
<hkern u1="v" u2="y" k="6" />
<hkern u1="v" u2="x" k="8" />
<hkern u1="v" u2="w" k="6" />
<hkern u1="v" u2="v" k="6" />
<hkern u1="v" u2="q" k="27" />
<hkern u1="v" u2="o" k="27" />
<hkern u1="v" u2="e" k="27" />
<hkern u1="v" u2="d" k="27" />
<hkern u1="v" u2="c" k="27" />
<hkern u1="v" u2="a" k="27" />
<hkern u1="v" u2="&#x2e;" k="102" />
<hkern u1="v" u2="&#x2d;" k="20" />
<hkern u1="v" u2="&#x2c;" k="102" />
<hkern u1="w" u2="&#x2039;" k="8" />
<hkern u1="w" u2="&#x2026;" k="61" />
<hkern u1="w" u2="&#x201e;" k="61" />
<hkern u1="w" u2="&#x201d;" k="-41" />
<hkern u1="w" u2="&#x201c;" k="-41" />
<hkern u1="w" u2="&#x201a;" k="61" />
<hkern u1="w" u2="&#x2019;" k="-41" />
<hkern u1="w" u2="&#x2018;" k="-41" />
<hkern u1="w" u2="&#x153;" k="6" />
<hkern u1="w" u2="&#xe7;" k="6" />
<hkern u1="w" u2="&#xe6;" k="6" />
<hkern u1="w" u2="&#xab;" k="8" />
<hkern u1="w" u2="y" k="6" />
<hkern u1="w" u2="x" k="6" />
<hkern u1="w" u2="w" k="6" />
<hkern u1="w" u2="v" k="6" />
<hkern u1="w" u2="q" k="6" />
<hkern u1="w" u2="o" k="6" />
<hkern u1="w" u2="e" k="6" />
<hkern u1="w" u2="d" k="6" />
<hkern u1="w" u2="c" k="6" />
<hkern u1="w" u2="a" k="6" />
<hkern u1="w" u2="&#x2e;" k="61" />
<hkern u1="w" u2="&#x2c;" k="61" />
<hkern u1="x" u2="&#x2039;" k="102" />
<hkern u1="x" u2="&#x201d;" k="-41" />
<hkern u1="x" u2="&#x201c;" k="-20" />
<hkern u1="x" u2="&#x2019;" k="-41" />
<hkern u1="x" u2="&#x2018;" k="-20" />
<hkern u1="x" u2="&#x2014;" k="61" />
<hkern u1="x" u2="&#x2013;" k="61" />
<hkern u1="x" u2="&#x153;" k="35" />
<hkern u1="x" u2="&#xe7;" k="35" />
<hkern u1="x" u2="&#xe6;" k="35" />
<hkern u1="x" u2="&#xab;" k="102" />
<hkern u1="x" u2="y" k="8" />
<hkern u1="x" u2="w" k="6" />
<hkern u1="x" u2="v" k="8" />
<hkern u1="x" u2="q" k="35" />
<hkern u1="x" u2="o" k="35" />
<hkern u1="x" u2="e" k="35" />
<hkern u1="x" u2="d" k="35" />
<hkern u1="x" u2="c" k="35" />
<hkern u1="x" u2="a" k="35" />
<hkern u1="x" u2="&#x2d;" k="61" />
<hkern u1="y" u2="&#x2039;" k="12" />
<hkern u1="y" u2="&#x2026;" k="102" />
<hkern u1="y" u2="&#x201e;" k="102" />
<hkern u1="y" u2="&#x201d;" k="-61" />
<hkern u1="y" u2="&#x201c;" k="-61" />
<hkern u1="y" u2="&#x201a;" k="102" />
<hkern u1="y" u2="&#x2019;" k="-61" />
<hkern u1="y" u2="&#x2018;" k="-61" />
<hkern u1="y" u2="&#x2014;" k="20" />
<hkern u1="y" u2="&#x2013;" k="20" />
<hkern u1="y" u2="&#x153;" k="27" />
<hkern u1="y" u2="&#xe7;" k="27" />
<hkern u1="y" u2="&#xe6;" k="27" />
<hkern u1="y" u2="&#xab;" k="12" />
<hkern u1="y" u2="y" k="6" />
<hkern u1="y" u2="x" k="8" />
<hkern u1="y" u2="w" k="6" />
<hkern u1="y" u2="v" k="6" />
<hkern u1="y" u2="q" k="27" />
<hkern u1="y" u2="o" k="27" />
<hkern u1="y" u2="e" k="27" />
<hkern u1="y" u2="d" k="27" />
<hkern u1="y" u2="c" k="27" />
<hkern u1="y" u2="a" k="27" />
<hkern u1="y" u2="&#x2e;" k="102" />
<hkern u1="y" u2="&#x2d;" k="20" />
<hkern u1="y" u2="&#x2c;" k="102" />
<hkern u1="z" u2="&#x2039;" k="115" />
<hkern u1="z" u2="&#x2014;" k="53" />
<hkern u1="z" u2="&#x2013;" k="53" />
<hkern u1="z" u2="&#x153;" k="41" />
<hkern u1="z" u2="&#xe7;" k="41" />
<hkern u1="z" u2="&#xe6;" k="41" />
<hkern u1="z" u2="&#xab;" k="115" />
<hkern u1="z" u2="q" k="41" />
<hkern u1="z" u2="o" k="41" />
<hkern u1="z" u2="g" k="16" />
<hkern u1="z" u2="e" k="41" />
<hkern u1="z" u2="d" k="41" />
<hkern u1="z" u2="c" k="41" />
<hkern u1="z" u2="a" k="41" />
<hkern u1="z" u2="&#x2d;" k="53" />
<hkern u1="&#x7b;" u2="&#x178;" k="-102" />
<hkern u1="&#x7b;" u2="&#xdd;" k="-102" />
<hkern u1="&#x7b;" u2="j" k="-307" />
<hkern u1="&#x7b;" u2="g" k="-41" />
<hkern u1="&#x7b;" u2="Y" k="-102" />
<hkern u1="&#x7b;" u2="X" k="-82" />
<hkern u1="&#x7b;" u2="W" k="-20" />
<hkern u1="&#x7b;" u2="V" k="-61" />
<hkern u1="&#x7b;" u2="J" k="8" />
<hkern u1="&#x7b;" u2="&#x37;" k="-8" />
<hkern u1="&#xa1;" u2="&#x178;" k="8" />
<hkern u1="&#xa1;" u2="&#xdd;" k="8" />
<hkern u1="&#xa1;" u2="y" k="16" />
<hkern u1="&#xa1;" u2="w" k="10" />
<hkern u1="&#xa1;" u2="v" k="16" />
<hkern u1="&#xa1;" u2="f" k="8" />
<hkern u1="&#xa1;" u2="Y" k="8" />
<hkern u1="&#xa1;" u2="W" k="8" />
<hkern u1="&#xa1;" u2="V" k="8" />
<hkern u1="&#xa3;" u2="&#x34;" k="41" />
<hkern u1="&#xa3;" u2="&#x31;" k="-20" />
<hkern u1="&#xa4;" u2="&#x34;" k="41" />
<hkern u1="&#xa9;" u2="&#x178;" k="61" />
<hkern u1="&#xa9;" u2="&#xdd;" k="61" />
<hkern u1="&#xa9;" u2="&#xc6;" k="20" />
<hkern u1="&#xa9;" u2="&#xc5;" k="20" />
<hkern u1="&#xa9;" u2="&#xc4;" k="20" />
<hkern u1="&#xa9;" u2="&#xc3;" k="20" />
<hkern u1="&#xa9;" u2="&#xc2;" k="20" />
<hkern u1="&#xa9;" u2="&#xc1;" k="20" />
<hkern u1="&#xa9;" u2="&#xc0;" k="20" />
<hkern u1="&#xa9;" u2="g" k="-4" />
<hkern u1="&#xa9;" u2="Y" k="61" />
<hkern u1="&#xa9;" u2="X" k="12" />
<hkern u1="&#xa9;" u2="W" k="41" />
<hkern u1="&#xa9;" u2="V" k="4" />
<hkern u1="&#xa9;" u2="T" k="61" />
<hkern u1="&#xa9;" u2="A" k="20" />
<hkern u1="&#xa9;" u2="&#x33;" k="4" />
<hkern u1="&#xab;" u2="&#x178;" k="82" />
<hkern u1="&#xab;" u2="&#xdd;" k="82" />
<hkern u1="&#xab;" u2="f" k="-8" />
<hkern u1="&#xab;" u2="Y" k="82" />
<hkern u1="&#xab;" u2="W" k="41" />
<hkern u1="&#xab;" u2="V" k="41" />
<hkern u1="&#xab;" u2="T" k="82" />
<hkern u1="&#xae;" u2="&#x178;" k="61" />
<hkern u1="&#xae;" u2="&#xdd;" k="61" />
<hkern u1="&#xae;" u2="&#xc6;" k="20" />
<hkern u1="&#xae;" u2="&#xc5;" k="20" />
<hkern u1="&#xae;" u2="&#xc4;" k="20" />
<hkern u1="&#xae;" u2="&#xc3;" k="20" />
<hkern u1="&#xae;" u2="&#xc2;" k="20" />
<hkern u1="&#xae;" u2="&#xc1;" k="20" />
<hkern u1="&#xae;" u2="&#xc0;" k="20" />
<hkern u1="&#xae;" u2="g" k="-4" />
<hkern u1="&#xae;" u2="Y" k="61" />
<hkern u1="&#xae;" u2="X" k="12" />
<hkern u1="&#xae;" u2="W" k="41" />
<hkern u1="&#xae;" u2="V" k="4" />
<hkern u1="&#xae;" u2="T" k="61" />
<hkern u1="&#xae;" u2="A" k="20" />
<hkern u1="&#xae;" u2="&#x33;" k="4" />
<hkern u1="&#xb0;" u2="&#x34;" k="106" />
<hkern u1="&#xbb;" u2="&#x178;" k="102" />
<hkern u1="&#xbb;" u2="&#xdd;" k="102" />
<hkern u1="&#xbb;" u2="z" k="94" />
<hkern u1="&#xbb;" u2="y" k="12" />
<hkern u1="&#xbb;" u2="x" k="102" />
<hkern u1="&#xbb;" u2="w" k="8" />
<hkern u1="&#xbb;" u2="v" k="12" />
<hkern u1="&#xbb;" u2="f" k="4" />
<hkern u1="&#xbb;" u2="Y" k="102" />
<hkern u1="&#xbb;" u2="W" k="82" />
<hkern u1="&#xbb;" u2="V" k="102" />
<hkern u1="&#xbb;" u2="T" k="225" />
<hkern u1="&#xbf;" u2="&#x178;" k="90" />
<hkern u1="&#xbf;" u2="&#x153;" k="-4" />
<hkern u1="&#xbf;" u2="&#x152;" k="41" />
<hkern u1="&#xbf;" u2="&#xe7;" k="-4" />
<hkern u1="&#xbf;" u2="&#xe6;" k="-4" />
<hkern u1="&#xbf;" u2="&#xdd;" k="90" />
<hkern u1="&#xbf;" u2="&#xd8;" k="41" />
<hkern u1="&#xbf;" u2="&#xd6;" k="41" />
<hkern u1="&#xbf;" u2="&#xd5;" k="41" />
<hkern u1="&#xbf;" u2="&#xd4;" k="41" />
<hkern u1="&#xbf;" u2="&#xd3;" k="41" />
<hkern u1="&#xbf;" u2="&#xd2;" k="41" />
<hkern u1="&#xbf;" u2="y" k="70" />
<hkern u1="&#xbf;" u2="x" k="20" />
<hkern u1="&#xbf;" u2="w" k="49" />
<hkern u1="&#xbf;" u2="v" k="70" />
<hkern u1="&#xbf;" u2="t" k="16" />
<hkern u1="&#xbf;" u2="s" k="-4" />
<hkern u1="&#xbf;" u2="q" k="-4" />
<hkern u1="&#xbf;" u2="o" k="-4" />
<hkern u1="&#xbf;" u2="e" k="-4" />
<hkern u1="&#xbf;" u2="d" k="-4" />
<hkern u1="&#xbf;" u2="c" k="-4" />
<hkern u1="&#xbf;" u2="a" k="-4" />
<hkern u1="&#xbf;" u2="Y" k="90" />
<hkern u1="&#xbf;" u2="W" k="70" />
<hkern u1="&#xbf;" u2="V" k="111" />
<hkern u1="&#xbf;" u2="T" k="127" />
<hkern u1="&#xbf;" u2="Q" k="41" />
<hkern u1="&#xbf;" u2="O" k="41" />
<hkern u1="&#xbf;" u2="G" k="41" />
<hkern u1="&#xbf;" u2="C" k="41" />
<hkern u1="&#xbf;" u2="&#x37;" k="66" />
<hkern u1="&#xbf;" u2="&#x34;" k="-4" />
<hkern u1="&#xbf;" u2="&#x33;" k="-16" />
<hkern u1="&#xbf;" u2="&#x32;" k="-8" />
<hkern u1="&#xbf;" u2="&#x31;" k="90" />
<hkern u1="&#xc0;" u2="&#x2122;" k="143" />
<hkern u1="&#xc0;" u2="&#x2026;" k="-61" />
<hkern u1="&#xc0;" u2="&#x201e;" k="-61" />
<hkern u1="&#xc0;" u2="&#x201d;" k="61" />
<hkern u1="&#xc0;" u2="&#x201c;" k="123" />
<hkern u1="&#xc0;" u2="&#x201a;" k="-61" />
<hkern u1="&#xc0;" u2="&#x2019;" k="61" />
<hkern u1="&#xc0;" u2="&#x2018;" k="123" />
<hkern u1="&#xc0;" u2="&#x2014;" k="-20" />
<hkern u1="&#xc0;" u2="&#x2013;" k="-20" />
<hkern u1="&#xc0;" u2="&#x178;" k="104" />
<hkern u1="&#xc0;" u2="&#x153;" k="10" />
<hkern u1="&#xc0;" u2="&#x152;" k="41" />
<hkern u1="&#xc0;" u2="&#xe7;" k="10" />
<hkern u1="&#xc0;" u2="&#xe6;" k="10" />
<hkern u1="&#xc0;" u2="&#xdd;" k="104" />
<hkern u1="&#xc0;" u2="&#xd8;" k="41" />
<hkern u1="&#xc0;" u2="&#xd6;" k="41" />
<hkern u1="&#xc0;" u2="&#xd5;" k="41" />
<hkern u1="&#xc0;" u2="&#xd4;" k="41" />
<hkern u1="&#xc0;" u2="&#xd3;" k="41" />
<hkern u1="&#xc0;" u2="&#xd2;" k="41" />
<hkern u1="&#xc0;" u2="&#xc6;" k="-20" />
<hkern u1="&#xc0;" u2="&#xc5;" k="-20" />
<hkern u1="&#xc0;" u2="&#xc4;" k="-20" />
<hkern u1="&#xc0;" u2="&#xc3;" k="-20" />
<hkern u1="&#xc0;" u2="&#xc2;" k="-20" />
<hkern u1="&#xc0;" u2="&#xc1;" k="-20" />
<hkern u1="&#xc0;" u2="&#xc0;" k="-20" />
<hkern u1="&#xc0;" u2="&#xae;" k="20" />
<hkern u1="&#xc0;" u2="&#xa9;" k="20" />
<hkern u1="&#xc0;" u2="z" k="-20" />
<hkern u1="&#xc0;" u2="y" k="78" />
<hkern u1="&#xc0;" u2="x" k="-37" />
<hkern u1="&#xc0;" u2="w" k="37" />
<hkern u1="&#xc0;" u2="v" k="78" />
<hkern u1="&#xc0;" u2="u" k="10" />
<hkern u1="&#xc0;" u2="t" k="10" />
<hkern u1="&#xc0;" u2="s" k="-6" />
<hkern u1="&#xc0;" u2="r" k="10" />
<hkern u1="&#xc0;" u2="q" k="10" />
<hkern u1="&#xc0;" u2="p" k="10" />
<hkern u1="&#xc0;" u2="o" k="10" />
<hkern u1="&#xc0;" u2="n" k="10" />
<hkern u1="&#xc0;" u2="m" k="10" />
<hkern u1="&#xc0;" u2="g" k="6" />
<hkern u1="&#xc0;" u2="f" k="16" />
<hkern u1="&#xc0;" u2="e" k="10" />
<hkern u1="&#xc0;" u2="d" k="10" />
<hkern u1="&#xc0;" u2="c" k="10" />
<hkern u1="&#xc0;" u2="a" k="10" />
<hkern u1="&#xc0;" u2="Y" k="104" />
<hkern u1="&#xc0;" u2="X" k="-20" />
<hkern u1="&#xc0;" u2="W" k="109" />
<hkern u1="&#xc0;" u2="V" k="115" />
<hkern u1="&#xc0;" u2="T" k="137" />
<hkern u1="&#xc0;" u2="S" k="-10" />
<hkern u1="&#xc0;" u2="Q" k="41" />
<hkern u1="&#xc0;" u2="O" k="41" />
<hkern u1="&#xc0;" u2="J" k="-20" />
<hkern u1="&#xc0;" u2="G" k="41" />
<hkern u1="&#xc0;" u2="C" k="41" />
<hkern u1="&#xc0;" u2="A" k="-20" />
<hkern u1="&#xc0;" u2="&#x40;" k="20" />
<hkern u1="&#xc0;" u2="&#x3f;" k="12" />
<hkern u1="&#xc0;" u2="&#x2e;" k="-61" />
<hkern u1="&#xc0;" u2="&#x2d;" k="-20" />
<hkern u1="&#xc0;" u2="&#x2c;" k="-61" />
<hkern u1="&#xc0;" u2="&#x2a;" k="131" />
<hkern u1="&#xc1;" u2="&#x2122;" k="143" />
<hkern u1="&#xc1;" u2="&#x2026;" k="-61" />
<hkern u1="&#xc1;" u2="&#x201e;" k="-61" />
<hkern u1="&#xc1;" u2="&#x201d;" k="61" />
<hkern u1="&#xc1;" u2="&#x201c;" k="123" />
<hkern u1="&#xc1;" u2="&#x201a;" k="-61" />
<hkern u1="&#xc1;" u2="&#x2019;" k="61" />
<hkern u1="&#xc1;" u2="&#x2018;" k="123" />
<hkern u1="&#xc1;" u2="&#x2014;" k="-20" />
<hkern u1="&#xc1;" u2="&#x2013;" k="-20" />
<hkern u1="&#xc1;" u2="&#x178;" k="104" />
<hkern u1="&#xc1;" u2="&#x153;" k="10" />
<hkern u1="&#xc1;" u2="&#x152;" k="41" />
<hkern u1="&#xc1;" u2="&#xe7;" k="10" />
<hkern u1="&#xc1;" u2="&#xe6;" k="10" />
<hkern u1="&#xc1;" u2="&#xdd;" k="104" />
<hkern u1="&#xc1;" u2="&#xd8;" k="41" />
<hkern u1="&#xc1;" u2="&#xd6;" k="41" />
<hkern u1="&#xc1;" u2="&#xd5;" k="41" />
<hkern u1="&#xc1;" u2="&#xd4;" k="41" />
<hkern u1="&#xc1;" u2="&#xd3;" k="41" />
<hkern u1="&#xc1;" u2="&#xd2;" k="41" />
<hkern u1="&#xc1;" u2="&#xc6;" k="-20" />
<hkern u1="&#xc1;" u2="&#xc5;" k="-20" />
<hkern u1="&#xc1;" u2="&#xc4;" k="-20" />
<hkern u1="&#xc1;" u2="&#xc3;" k="-20" />
<hkern u1="&#xc1;" u2="&#xc2;" k="-20" />
<hkern u1="&#xc1;" u2="&#xc1;" k="-20" />
<hkern u1="&#xc1;" u2="&#xc0;" k="-20" />
<hkern u1="&#xc1;" u2="&#xae;" k="20" />
<hkern u1="&#xc1;" u2="&#xa9;" k="20" />
<hkern u1="&#xc1;" u2="z" k="-20" />
<hkern u1="&#xc1;" u2="y" k="78" />
<hkern u1="&#xc1;" u2="x" k="-37" />
<hkern u1="&#xc1;" u2="w" k="37" />
<hkern u1="&#xc1;" u2="v" k="78" />
<hkern u1="&#xc1;" u2="u" k="10" />
<hkern u1="&#xc1;" u2="t" k="10" />
<hkern u1="&#xc1;" u2="s" k="-6" />
<hkern u1="&#xc1;" u2="r" k="10" />
<hkern u1="&#xc1;" u2="q" k="10" />
<hkern u1="&#xc1;" u2="p" k="10" />
<hkern u1="&#xc1;" u2="o" k="10" />
<hkern u1="&#xc1;" u2="n" k="10" />
<hkern u1="&#xc1;" u2="m" k="10" />
<hkern u1="&#xc1;" u2="g" k="6" />
<hkern u1="&#xc1;" u2="f" k="16" />
<hkern u1="&#xc1;" u2="e" k="10" />
<hkern u1="&#xc1;" u2="d" k="10" />
<hkern u1="&#xc1;" u2="c" k="10" />
<hkern u1="&#xc1;" u2="a" k="10" />
<hkern u1="&#xc1;" u2="Y" k="104" />
<hkern u1="&#xc1;" u2="X" k="-20" />
<hkern u1="&#xc1;" u2="W" k="109" />
<hkern u1="&#xc1;" u2="V" k="115" />
<hkern u1="&#xc1;" u2="T" k="137" />
<hkern u1="&#xc1;" u2="S" k="-10" />
<hkern u1="&#xc1;" u2="Q" k="41" />
<hkern u1="&#xc1;" u2="O" k="41" />
<hkern u1="&#xc1;" u2="J" k="-20" />
<hkern u1="&#xc1;" u2="G" k="41" />
<hkern u1="&#xc1;" u2="C" k="41" />
<hkern u1="&#xc1;" u2="A" k="-20" />
<hkern u1="&#xc1;" u2="&#x40;" k="20" />
<hkern u1="&#xc1;" u2="&#x3f;" k="12" />
<hkern u1="&#xc1;" u2="&#x2e;" k="-61" />
<hkern u1="&#xc1;" u2="&#x2d;" k="-20" />
<hkern u1="&#xc1;" u2="&#x2c;" k="-61" />
<hkern u1="&#xc1;" u2="&#x2a;" k="131" />
<hkern u1="&#xc2;" u2="&#x2122;" k="143" />
<hkern u1="&#xc2;" u2="&#x2026;" k="-61" />
<hkern u1="&#xc2;" u2="&#x201e;" k="-61" />
<hkern u1="&#xc2;" u2="&#x201d;" k="61" />
<hkern u1="&#xc2;" u2="&#x201c;" k="123" />
<hkern u1="&#xc2;" u2="&#x201a;" k="-61" />
<hkern u1="&#xc2;" u2="&#x2019;" k="61" />
<hkern u1="&#xc2;" u2="&#x2018;" k="123" />
<hkern u1="&#xc2;" u2="&#x2014;" k="-20" />
<hkern u1="&#xc2;" u2="&#x2013;" k="-20" />
<hkern u1="&#xc2;" u2="&#x178;" k="104" />
<hkern u1="&#xc2;" u2="&#x153;" k="10" />
<hkern u1="&#xc2;" u2="&#x152;" k="41" />
<hkern u1="&#xc2;" u2="&#xe7;" k="10" />
<hkern u1="&#xc2;" u2="&#xe6;" k="10" />
<hkern u1="&#xc2;" u2="&#xdd;" k="104" />
<hkern u1="&#xc2;" u2="&#xd8;" k="41" />
<hkern u1="&#xc2;" u2="&#xd6;" k="41" />
<hkern u1="&#xc2;" u2="&#xd5;" k="41" />
<hkern u1="&#xc2;" u2="&#xd4;" k="41" />
<hkern u1="&#xc2;" u2="&#xd3;" k="41" />
<hkern u1="&#xc2;" u2="&#xd2;" k="41" />
<hkern u1="&#xc2;" u2="&#xc6;" k="-20" />
<hkern u1="&#xc2;" u2="&#xc5;" k="-20" />
<hkern u1="&#xc2;" u2="&#xc4;" k="-20" />
<hkern u1="&#xc2;" u2="&#xc3;" k="-20" />
<hkern u1="&#xc2;" u2="&#xc2;" k="-20" />
<hkern u1="&#xc2;" u2="&#xc1;" k="-20" />
<hkern u1="&#xc2;" u2="&#xc0;" k="-20" />
<hkern u1="&#xc2;" u2="&#xae;" k="20" />
<hkern u1="&#xc2;" u2="&#xa9;" k="20" />
<hkern u1="&#xc2;" u2="z" k="-20" />
<hkern u1="&#xc2;" u2="y" k="78" />
<hkern u1="&#xc2;" u2="x" k="-37" />
<hkern u1="&#xc2;" u2="w" k="37" />
<hkern u1="&#xc2;" u2="v" k="78" />
<hkern u1="&#xc2;" u2="u" k="10" />
<hkern u1="&#xc2;" u2="t" k="10" />
<hkern u1="&#xc2;" u2="s" k="-6" />
<hkern u1="&#xc2;" u2="r" k="10" />
<hkern u1="&#xc2;" u2="q" k="10" />
<hkern u1="&#xc2;" u2="p" k="10" />
<hkern u1="&#xc2;" u2="o" k="10" />
<hkern u1="&#xc2;" u2="n" k="10" />
<hkern u1="&#xc2;" u2="m" k="10" />
<hkern u1="&#xc2;" u2="g" k="6" />
<hkern u1="&#xc2;" u2="f" k="16" />
<hkern u1="&#xc2;" u2="e" k="10" />
<hkern u1="&#xc2;" u2="d" k="10" />
<hkern u1="&#xc2;" u2="c" k="10" />
<hkern u1="&#xc2;" u2="a" k="10" />
<hkern u1="&#xc2;" u2="Y" k="104" />
<hkern u1="&#xc2;" u2="X" k="-20" />
<hkern u1="&#xc2;" u2="W" k="109" />
<hkern u1="&#xc2;" u2="V" k="115" />
<hkern u1="&#xc2;" u2="T" k="137" />
<hkern u1="&#xc2;" u2="S" k="-10" />
<hkern u1="&#xc2;" u2="Q" k="41" />
<hkern u1="&#xc2;" u2="O" k="41" />
<hkern u1="&#xc2;" u2="J" k="-20" />
<hkern u1="&#xc2;" u2="G" k="41" />
<hkern u1="&#xc2;" u2="C" k="41" />
<hkern u1="&#xc2;" u2="A" k="-20" />
<hkern u1="&#xc2;" u2="&#x40;" k="20" />
<hkern u1="&#xc2;" u2="&#x3f;" k="12" />
<hkern u1="&#xc2;" u2="&#x2e;" k="-61" />
<hkern u1="&#xc2;" u2="&#x2d;" k="-20" />
<hkern u1="&#xc2;" u2="&#x2c;" k="-61" />
<hkern u1="&#xc2;" u2="&#x2a;" k="131" />
<hkern u1="&#xc3;" u2="&#x2122;" k="143" />
<hkern u1="&#xc3;" u2="&#x2026;" k="-61" />
<hkern u1="&#xc3;" u2="&#x201e;" k="-61" />
<hkern u1="&#xc3;" u2="&#x201d;" k="61" />
<hkern u1="&#xc3;" u2="&#x201c;" k="123" />
<hkern u1="&#xc3;" u2="&#x201a;" k="-61" />
<hkern u1="&#xc3;" u2="&#x2019;" k="61" />
<hkern u1="&#xc3;" u2="&#x2018;" k="123" />
<hkern u1="&#xc3;" u2="&#x2014;" k="-20" />
<hkern u1="&#xc3;" u2="&#x2013;" k="-20" />
<hkern u1="&#xc3;" u2="&#x178;" k="104" />
<hkern u1="&#xc3;" u2="&#x153;" k="10" />
<hkern u1="&#xc3;" u2="&#x152;" k="41" />
<hkern u1="&#xc3;" u2="&#xe7;" k="10" />
<hkern u1="&#xc3;" u2="&#xe6;" k="10" />
<hkern u1="&#xc3;" u2="&#xdd;" k="104" />
<hkern u1="&#xc3;" u2="&#xd8;" k="41" />
<hkern u1="&#xc3;" u2="&#xd6;" k="41" />
<hkern u1="&#xc3;" u2="&#xd5;" k="41" />
<hkern u1="&#xc3;" u2="&#xd4;" k="41" />
<hkern u1="&#xc3;" u2="&#xd3;" k="41" />
<hkern u1="&#xc3;" u2="&#xd2;" k="41" />
<hkern u1="&#xc3;" u2="&#xc6;" k="-20" />
<hkern u1="&#xc3;" u2="&#xc5;" k="-20" />
<hkern u1="&#xc3;" u2="&#xc4;" k="-20" />
<hkern u1="&#xc3;" u2="&#xc3;" k="-20" />
<hkern u1="&#xc3;" u2="&#xc2;" k="-20" />
<hkern u1="&#xc3;" u2="&#xc1;" k="-20" />
<hkern u1="&#xc3;" u2="&#xc0;" k="-20" />
<hkern u1="&#xc3;" u2="&#xae;" k="20" />
<hkern u1="&#xc3;" u2="&#xa9;" k="20" />
<hkern u1="&#xc3;" u2="z" k="-20" />
<hkern u1="&#xc3;" u2="y" k="78" />
<hkern u1="&#xc3;" u2="x" k="-37" />
<hkern u1="&#xc3;" u2="w" k="37" />
<hkern u1="&#xc3;" u2="v" k="78" />
<hkern u1="&#xc3;" u2="u" k="10" />
<hkern u1="&#xc3;" u2="t" k="10" />
<hkern u1="&#xc3;" u2="s" k="-6" />
<hkern u1="&#xc3;" u2="r" k="10" />
<hkern u1="&#xc3;" u2="q" k="10" />
<hkern u1="&#xc3;" u2="p" k="10" />
<hkern u1="&#xc3;" u2="o" k="10" />
<hkern u1="&#xc3;" u2="n" k="10" />
<hkern u1="&#xc3;" u2="m" k="10" />
<hkern u1="&#xc3;" u2="g" k="6" />
<hkern u1="&#xc3;" u2="f" k="16" />
<hkern u1="&#xc3;" u2="e" k="10" />
<hkern u1="&#xc3;" u2="d" k="10" />
<hkern u1="&#xc3;" u2="c" k="10" />
<hkern u1="&#xc3;" u2="a" k="10" />
<hkern u1="&#xc3;" u2="Y" k="104" />
<hkern u1="&#xc3;" u2="X" k="-20" />
<hkern u1="&#xc3;" u2="W" k="109" />
<hkern u1="&#xc3;" u2="V" k="115" />
<hkern u1="&#xc3;" u2="T" k="137" />
<hkern u1="&#xc3;" u2="S" k="-10" />
<hkern u1="&#xc3;" u2="Q" k="41" />
<hkern u1="&#xc3;" u2="O" k="41" />
<hkern u1="&#xc3;" u2="J" k="-20" />
<hkern u1="&#xc3;" u2="G" k="41" />
<hkern u1="&#xc3;" u2="C" k="41" />
<hkern u1="&#xc3;" u2="A" k="-20" />
<hkern u1="&#xc3;" u2="&#x40;" k="20" />
<hkern u1="&#xc3;" u2="&#x3f;" k="12" />
<hkern u1="&#xc3;" u2="&#x2e;" k="-61" />
<hkern u1="&#xc3;" u2="&#x2d;" k="-20" />
<hkern u1="&#xc3;" u2="&#x2c;" k="-61" />
<hkern u1="&#xc3;" u2="&#x2a;" k="131" />
<hkern u1="&#xc4;" u2="&#x2122;" k="143" />
<hkern u1="&#xc4;" u2="&#x2026;" k="-61" />
<hkern u1="&#xc4;" u2="&#x201e;" k="-61" />
<hkern u1="&#xc4;" u2="&#x201d;" k="61" />
<hkern u1="&#xc4;" u2="&#x201c;" k="123" />
<hkern u1="&#xc4;" u2="&#x201a;" k="-61" />
<hkern u1="&#xc4;" u2="&#x2019;" k="61" />
<hkern u1="&#xc4;" u2="&#x2018;" k="123" />
<hkern u1="&#xc4;" u2="&#x2014;" k="-20" />
<hkern u1="&#xc4;" u2="&#x2013;" k="-20" />
<hkern u1="&#xc4;" u2="&#x178;" k="104" />
<hkern u1="&#xc4;" u2="&#x153;" k="10" />
<hkern u1="&#xc4;" u2="&#x152;" k="41" />
<hkern u1="&#xc4;" u2="&#xe7;" k="10" />
<hkern u1="&#xc4;" u2="&#xe6;" k="10" />
<hkern u1="&#xc4;" u2="&#xdd;" k="104" />
<hkern u1="&#xc4;" u2="&#xd8;" k="41" />
<hkern u1="&#xc4;" u2="&#xd6;" k="41" />
<hkern u1="&#xc4;" u2="&#xd5;" k="41" />
<hkern u1="&#xc4;" u2="&#xd4;" k="41" />
<hkern u1="&#xc4;" u2="&#xd3;" k="41" />
<hkern u1="&#xc4;" u2="&#xd2;" k="41" />
<hkern u1="&#xc4;" u2="&#xc6;" k="-20" />
<hkern u1="&#xc4;" u2="&#xc5;" k="-20" />
<hkern u1="&#xc4;" u2="&#xc4;" k="-20" />
<hkern u1="&#xc4;" u2="&#xc3;" k="-20" />
<hkern u1="&#xc4;" u2="&#xc2;" k="-20" />
<hkern u1="&#xc4;" u2="&#xc1;" k="-20" />
<hkern u1="&#xc4;" u2="&#xc0;" k="-20" />
<hkern u1="&#xc4;" u2="&#xae;" k="20" />
<hkern u1="&#xc4;" u2="&#xa9;" k="20" />
<hkern u1="&#xc4;" u2="z" k="-20" />
<hkern u1="&#xc4;" u2="y" k="78" />
<hkern u1="&#xc4;" u2="x" k="-37" />
<hkern u1="&#xc4;" u2="w" k="37" />
<hkern u1="&#xc4;" u2="v" k="78" />
<hkern u1="&#xc4;" u2="u" k="10" />
<hkern u1="&#xc4;" u2="t" k="10" />
<hkern u1="&#xc4;" u2="s" k="-6" />
<hkern u1="&#xc4;" u2="r" k="10" />
<hkern u1="&#xc4;" u2="q" k="10" />
<hkern u1="&#xc4;" u2="p" k="10" />
<hkern u1="&#xc4;" u2="o" k="10" />
<hkern u1="&#xc4;" u2="n" k="10" />
<hkern u1="&#xc4;" u2="m" k="10" />
<hkern u1="&#xc4;" u2="g" k="6" />
<hkern u1="&#xc4;" u2="f" k="16" />
<hkern u1="&#xc4;" u2="e" k="10" />
<hkern u1="&#xc4;" u2="d" k="10" />
<hkern u1="&#xc4;" u2="c" k="10" />
<hkern u1="&#xc4;" u2="a" k="10" />
<hkern u1="&#xc4;" u2="Y" k="104" />
<hkern u1="&#xc4;" u2="X" k="-20" />
<hkern u1="&#xc4;" u2="W" k="109" />
<hkern u1="&#xc4;" u2="V" k="115" />
<hkern u1="&#xc4;" u2="T" k="137" />
<hkern u1="&#xc4;" u2="S" k="-10" />
<hkern u1="&#xc4;" u2="Q" k="41" />
<hkern u1="&#xc4;" u2="O" k="41" />
<hkern u1="&#xc4;" u2="J" k="-20" />
<hkern u1="&#xc4;" u2="G" k="41" />
<hkern u1="&#xc4;" u2="C" k="41" />
<hkern u1="&#xc4;" u2="A" k="-20" />
<hkern u1="&#xc4;" u2="&#x40;" k="20" />
<hkern u1="&#xc4;" u2="&#x3f;" k="12" />
<hkern u1="&#xc4;" u2="&#x2e;" k="-61" />
<hkern u1="&#xc4;" u2="&#x2d;" k="-20" />
<hkern u1="&#xc4;" u2="&#x2c;" k="-61" />
<hkern u1="&#xc4;" u2="&#x2a;" k="131" />
<hkern u1="&#xc5;" u2="&#x2122;" k="143" />
<hkern u1="&#xc5;" u2="&#x2026;" k="-61" />
<hkern u1="&#xc5;" u2="&#x201e;" k="-61" />
<hkern u1="&#xc5;" u2="&#x201d;" k="61" />
<hkern u1="&#xc5;" u2="&#x201c;" k="123" />
<hkern u1="&#xc5;" u2="&#x201a;" k="-61" />
<hkern u1="&#xc5;" u2="&#x2019;" k="61" />
<hkern u1="&#xc5;" u2="&#x2018;" k="123" />
<hkern u1="&#xc5;" u2="&#x2014;" k="-20" />
<hkern u1="&#xc5;" u2="&#x2013;" k="-20" />
<hkern u1="&#xc5;" u2="&#x178;" k="104" />
<hkern u1="&#xc5;" u2="&#x153;" k="10" />
<hkern u1="&#xc5;" u2="&#x152;" k="41" />
<hkern u1="&#xc5;" u2="&#xe7;" k="10" />
<hkern u1="&#xc5;" u2="&#xe6;" k="10" />
<hkern u1="&#xc5;" u2="&#xdd;" k="104" />
<hkern u1="&#xc5;" u2="&#xd8;" k="41" />
<hkern u1="&#xc5;" u2="&#xd6;" k="41" />
<hkern u1="&#xc5;" u2="&#xd5;" k="41" />
<hkern u1="&#xc5;" u2="&#xd4;" k="41" />
<hkern u1="&#xc5;" u2="&#xd3;" k="41" />
<hkern u1="&#xc5;" u2="&#xd2;" k="41" />
<hkern u1="&#xc5;" u2="&#xc6;" k="-20" />
<hkern u1="&#xc5;" u2="&#xc5;" k="-20" />
<hkern u1="&#xc5;" u2="&#xc4;" k="-20" />
<hkern u1="&#xc5;" u2="&#xc3;" k="-20" />
<hkern u1="&#xc5;" u2="&#xc2;" k="-20" />
<hkern u1="&#xc5;" u2="&#xc1;" k="-20" />
<hkern u1="&#xc5;" u2="&#xc0;" k="-20" />
<hkern u1="&#xc5;" u2="&#xae;" k="20" />
<hkern u1="&#xc5;" u2="&#xa9;" k="20" />
<hkern u1="&#xc5;" u2="z" k="-20" />
<hkern u1="&#xc5;" u2="y" k="78" />
<hkern u1="&#xc5;" u2="x" k="-37" />
<hkern u1="&#xc5;" u2="w" k="37" />
<hkern u1="&#xc5;" u2="v" k="78" />
<hkern u1="&#xc5;" u2="u" k="10" />
<hkern u1="&#xc5;" u2="t" k="10" />
<hkern u1="&#xc5;" u2="s" k="-6" />
<hkern u1="&#xc5;" u2="r" k="10" />
<hkern u1="&#xc5;" u2="q" k="10" />
<hkern u1="&#xc5;" u2="p" k="10" />
<hkern u1="&#xc5;" u2="o" k="10" />
<hkern u1="&#xc5;" u2="n" k="10" />
<hkern u1="&#xc5;" u2="m" k="10" />
<hkern u1="&#xc5;" u2="g" k="6" />
<hkern u1="&#xc5;" u2="f" k="16" />
<hkern u1="&#xc5;" u2="e" k="10" />
<hkern u1="&#xc5;" u2="d" k="10" />
<hkern u1="&#xc5;" u2="c" k="10" />
<hkern u1="&#xc5;" u2="a" k="10" />
<hkern u1="&#xc5;" u2="Y" k="104" />
<hkern u1="&#xc5;" u2="X" k="-20" />
<hkern u1="&#xc5;" u2="W" k="109" />
<hkern u1="&#xc5;" u2="V" k="115" />
<hkern u1="&#xc5;" u2="T" k="137" />
<hkern u1="&#xc5;" u2="S" k="-10" />
<hkern u1="&#xc5;" u2="Q" k="41" />
<hkern u1="&#xc5;" u2="O" k="41" />
<hkern u1="&#xc5;" u2="J" k="-20" />
<hkern u1="&#xc5;" u2="G" k="41" />
<hkern u1="&#xc5;" u2="C" k="41" />
<hkern u1="&#xc5;" u2="A" k="-20" />
<hkern u1="&#xc5;" u2="&#x40;" k="20" />
<hkern u1="&#xc5;" u2="&#x3f;" k="12" />
<hkern u1="&#xc5;" u2="&#x2e;" k="-61" />
<hkern u1="&#xc5;" u2="&#x2d;" k="-20" />
<hkern u1="&#xc5;" u2="&#x2c;" k="-61" />
<hkern u1="&#xc5;" u2="&#x2a;" k="131" />
<hkern u1="&#xc6;" u2="&#x2039;" k="8" />
<hkern u1="&#xc6;" u2="&#x153;" k="49" />
<hkern u1="&#xc6;" u2="&#x152;" k="10" />
<hkern u1="&#xc6;" u2="&#xfe;" k="2" />
<hkern u1="&#xc6;" u2="&#xe7;" k="49" />
<hkern u1="&#xc6;" u2="&#xe6;" k="49" />
<hkern u1="&#xc6;" u2="&#xd8;" k="10" />
<hkern u1="&#xc6;" u2="&#xd6;" k="10" />
<hkern u1="&#xc6;" u2="&#xd5;" k="10" />
<hkern u1="&#xc6;" u2="&#xd4;" k="10" />
<hkern u1="&#xc6;" u2="&#xd3;" k="10" />
<hkern u1="&#xc6;" u2="&#xd2;" k="10" />
<hkern u1="&#xc6;" u2="&#xae;" k="41" />
<hkern u1="&#xc6;" u2="&#xab;" k="8" />
<hkern u1="&#xc6;" u2="&#xa9;" k="41" />
<hkern u1="&#xc6;" u2="y" k="41" />
<hkern u1="&#xc6;" u2="v" k="41" />
<hkern u1="&#xc6;" u2="u" k="4" />
<hkern u1="&#xc6;" u2="r" k="4" />
<hkern u1="&#xc6;" u2="q" k="49" />
<hkern u1="&#xc6;" u2="p" k="4" />
<hkern u1="&#xc6;" u2="o" k="49" />
<hkern u1="&#xc6;" u2="n" k="4" />
<hkern u1="&#xc6;" u2="m" k="4" />
<hkern u1="&#xc6;" u2="l" k="2" />
<hkern u1="&#xc6;" u2="k" k="2" />
<hkern u1="&#xc6;" u2="h" k="2" />
<hkern u1="&#xc6;" u2="g" k="16" />
<hkern u1="&#xc6;" u2="f" k="20" />
<hkern u1="&#xc6;" u2="e" k="49" />
<hkern u1="&#xc6;" u2="d" k="49" />
<hkern u1="&#xc6;" u2="c" k="49" />
<hkern u1="&#xc6;" u2="b" k="2" />
<hkern u1="&#xc6;" u2="a" k="49" />
<hkern u1="&#xc6;" u2="W" k="2" />
<hkern u1="&#xc6;" u2="V" k="-4" />
<hkern u1="&#xc6;" u2="T" k="-27" />
<hkern u1="&#xc6;" u2="Q" k="10" />
<hkern u1="&#xc6;" u2="O" k="10" />
<hkern u1="&#xc6;" u2="J" k="-2" />
<hkern u1="&#xc6;" u2="G" k="10" />
<hkern u1="&#xc6;" u2="C" k="10" />
<hkern u1="&#xc6;" u2="&#x40;" k="41" />
<hkern u1="&#xc8;" u2="&#x2039;" k="8" />
<hkern u1="&#xc8;" u2="&#x153;" k="49" />
<hkern u1="&#xc8;" u2="&#x152;" k="10" />
<hkern u1="&#xc8;" u2="&#xfe;" k="2" />
<hkern u1="&#xc8;" u2="&#xe7;" k="49" />
<hkern u1="&#xc8;" u2="&#xe6;" k="49" />
<hkern u1="&#xc8;" u2="&#xd8;" k="10" />
<hkern u1="&#xc8;" u2="&#xd6;" k="10" />
<hkern u1="&#xc8;" u2="&#xd5;" k="10" />
<hkern u1="&#xc8;" u2="&#xd4;" k="10" />
<hkern u1="&#xc8;" u2="&#xd3;" k="10" />
<hkern u1="&#xc8;" u2="&#xd2;" k="10" />
<hkern u1="&#xc8;" u2="&#xae;" k="41" />
<hkern u1="&#xc8;" u2="&#xab;" k="8" />
<hkern u1="&#xc8;" u2="&#xa9;" k="41" />
<hkern u1="&#xc8;" u2="y" k="41" />
<hkern u1="&#xc8;" u2="v" k="41" />
<hkern u1="&#xc8;" u2="u" k="4" />
<hkern u1="&#xc8;" u2="r" k="4" />
<hkern u1="&#xc8;" u2="q" k="49" />
<hkern u1="&#xc8;" u2="p" k="4" />
<hkern u1="&#xc8;" u2="o" k="49" />
<hkern u1="&#xc8;" u2="n" k="4" />
<hkern u1="&#xc8;" u2="m" k="4" />
<hkern u1="&#xc8;" u2="l" k="2" />
<hkern u1="&#xc8;" u2="k" k="2" />
<hkern u1="&#xc8;" u2="h" k="2" />
<hkern u1="&#xc8;" u2="g" k="16" />
<hkern u1="&#xc8;" u2="f" k="20" />
<hkern u1="&#xc8;" u2="e" k="49" />
<hkern u1="&#xc8;" u2="d" k="49" />
<hkern u1="&#xc8;" u2="c" k="49" />
<hkern u1="&#xc8;" u2="b" k="2" />
<hkern u1="&#xc8;" u2="a" k="49" />
<hkern u1="&#xc8;" u2="W" k="2" />
<hkern u1="&#xc8;" u2="V" k="-4" />
<hkern u1="&#xc8;" u2="T" k="-27" />
<hkern u1="&#xc8;" u2="Q" k="10" />
<hkern u1="&#xc8;" u2="O" k="10" />
<hkern u1="&#xc8;" u2="J" k="-2" />
<hkern u1="&#xc8;" u2="G" k="10" />
<hkern u1="&#xc8;" u2="C" k="10" />
<hkern u1="&#xc8;" u2="&#x40;" k="41" />
<hkern u1="&#xc9;" u2="&#x2039;" k="8" />
<hkern u1="&#xc9;" u2="&#x153;" k="49" />
<hkern u1="&#xc9;" u2="&#x152;" k="10" />
<hkern u1="&#xc9;" u2="&#xfe;" k="2" />
<hkern u1="&#xc9;" u2="&#xe7;" k="49" />
<hkern u1="&#xc9;" u2="&#xe6;" k="49" />
<hkern u1="&#xc9;" u2="&#xd8;" k="10" />
<hkern u1="&#xc9;" u2="&#xd6;" k="10" />
<hkern u1="&#xc9;" u2="&#xd5;" k="10" />
<hkern u1="&#xc9;" u2="&#xd4;" k="10" />
<hkern u1="&#xc9;" u2="&#xd3;" k="10" />
<hkern u1="&#xc9;" u2="&#xd2;" k="10" />
<hkern u1="&#xc9;" u2="&#xae;" k="41" />
<hkern u1="&#xc9;" u2="&#xab;" k="8" />
<hkern u1="&#xc9;" u2="&#xa9;" k="41" />
<hkern u1="&#xc9;" u2="y" k="41" />
<hkern u1="&#xc9;" u2="v" k="41" />
<hkern u1="&#xc9;" u2="u" k="4" />
<hkern u1="&#xc9;" u2="r" k="4" />
<hkern u1="&#xc9;" u2="q" k="49" />
<hkern u1="&#xc9;" u2="p" k="4" />
<hkern u1="&#xc9;" u2="o" k="49" />
<hkern u1="&#xc9;" u2="n" k="4" />
<hkern u1="&#xc9;" u2="m" k="4" />
<hkern u1="&#xc9;" u2="l" k="2" />
<hkern u1="&#xc9;" u2="k" k="2" />
<hkern u1="&#xc9;" u2="h" k="2" />
<hkern u1="&#xc9;" u2="g" k="16" />
<hkern u1="&#xc9;" u2="f" k="20" />
<hkern u1="&#xc9;" u2="e" k="49" />
<hkern u1="&#xc9;" u2="d" k="49" />
<hkern u1="&#xc9;" u2="c" k="49" />
<hkern u1="&#xc9;" u2="b" k="2" />
<hkern u1="&#xc9;" u2="a" k="49" />
<hkern u1="&#xc9;" u2="W" k="2" />
<hkern u1="&#xc9;" u2="V" k="-4" />
<hkern u1="&#xc9;" u2="T" k="-27" />
<hkern u1="&#xc9;" u2="Q" k="10" />
<hkern u1="&#xc9;" u2="O" k="10" />
<hkern u1="&#xc9;" u2="J" k="-2" />
<hkern u1="&#xc9;" u2="G" k="10" />
<hkern u1="&#xc9;" u2="C" k="10" />
<hkern u1="&#xc9;" u2="&#x40;" k="41" />
<hkern u1="&#xca;" u2="&#x2039;" k="8" />
<hkern u1="&#xca;" u2="&#x153;" k="49" />
<hkern u1="&#xca;" u2="&#x152;" k="10" />
<hkern u1="&#xca;" u2="&#xfe;" k="2" />
<hkern u1="&#xca;" u2="&#xe7;" k="49" />
<hkern u1="&#xca;" u2="&#xe6;" k="49" />
<hkern u1="&#xca;" u2="&#xd8;" k="10" />
<hkern u1="&#xca;" u2="&#xd6;" k="10" />
<hkern u1="&#xca;" u2="&#xd5;" k="10" />
<hkern u1="&#xca;" u2="&#xd4;" k="10" />
<hkern u1="&#xca;" u2="&#xd3;" k="10" />
<hkern u1="&#xca;" u2="&#xd2;" k="10" />
<hkern u1="&#xca;" u2="&#xae;" k="41" />
<hkern u1="&#xca;" u2="&#xab;" k="8" />
<hkern u1="&#xca;" u2="&#xa9;" k="41" />
<hkern u1="&#xca;" u2="y" k="41" />
<hkern u1="&#xca;" u2="v" k="41" />
<hkern u1="&#xca;" u2="u" k="4" />
<hkern u1="&#xca;" u2="r" k="4" />
<hkern u1="&#xca;" u2="q" k="49" />
<hkern u1="&#xca;" u2="p" k="4" />
<hkern u1="&#xca;" u2="o" k="49" />
<hkern u1="&#xca;" u2="n" k="4" />
<hkern u1="&#xca;" u2="m" k="4" />
<hkern u1="&#xca;" u2="l" k="2" />
<hkern u1="&#xca;" u2="k" k="2" />
<hkern u1="&#xca;" u2="h" k="2" />
<hkern u1="&#xca;" u2="g" k="16" />
<hkern u1="&#xca;" u2="f" k="20" />
<hkern u1="&#xca;" u2="e" k="49" />
<hkern u1="&#xca;" u2="d" k="49" />
<hkern u1="&#xca;" u2="c" k="49" />
<hkern u1="&#xca;" u2="b" k="2" />
<hkern u1="&#xca;" u2="a" k="49" />
<hkern u1="&#xca;" u2="W" k="2" />
<hkern u1="&#xca;" u2="V" k="-4" />
<hkern u1="&#xca;" u2="T" k="-27" />
<hkern u1="&#xca;" u2="Q" k="10" />
<hkern u1="&#xca;" u2="O" k="10" />
<hkern u1="&#xca;" u2="J" k="-2" />
<hkern u1="&#xca;" u2="G" k="10" />
<hkern u1="&#xca;" u2="C" k="10" />
<hkern u1="&#xca;" u2="&#x40;" k="41" />
<hkern u1="&#xcb;" u2="&#x2039;" k="8" />
<hkern u1="&#xcb;" u2="&#x153;" k="49" />
<hkern u1="&#xcb;" u2="&#x152;" k="10" />
<hkern u1="&#xcb;" u2="&#xfe;" k="2" />
<hkern u1="&#xcb;" u2="&#xe7;" k="49" />
<hkern u1="&#xcb;" u2="&#xe6;" k="49" />
<hkern u1="&#xcb;" u2="&#xd8;" k="10" />
<hkern u1="&#xcb;" u2="&#xd6;" k="10" />
<hkern u1="&#xcb;" u2="&#xd5;" k="10" />
<hkern u1="&#xcb;" u2="&#xd4;" k="10" />
<hkern u1="&#xcb;" u2="&#xd3;" k="10" />
<hkern u1="&#xcb;" u2="&#xd2;" k="10" />
<hkern u1="&#xcb;" u2="&#xae;" k="41" />
<hkern u1="&#xcb;" u2="&#xab;" k="8" />
<hkern u1="&#xcb;" u2="&#xa9;" k="41" />
<hkern u1="&#xcb;" u2="y" k="41" />
<hkern u1="&#xcb;" u2="v" k="41" />
<hkern u1="&#xcb;" u2="u" k="4" />
<hkern u1="&#xcb;" u2="r" k="4" />
<hkern u1="&#xcb;" u2="q" k="49" />
<hkern u1="&#xcb;" u2="p" k="4" />
<hkern u1="&#xcb;" u2="o" k="49" />
<hkern u1="&#xcb;" u2="n" k="4" />
<hkern u1="&#xcb;" u2="m" k="4" />
<hkern u1="&#xcb;" u2="l" k="2" />
<hkern u1="&#xcb;" u2="k" k="2" />
<hkern u1="&#xcb;" u2="h" k="2" />
<hkern u1="&#xcb;" u2="g" k="16" />
<hkern u1="&#xcb;" u2="f" k="20" />
<hkern u1="&#xcb;" u2="e" k="49" />
<hkern u1="&#xcb;" u2="d" k="49" />
<hkern u1="&#xcb;" u2="c" k="49" />
<hkern u1="&#xcb;" u2="b" k="2" />
<hkern u1="&#xcb;" u2="a" k="49" />
<hkern u1="&#xcb;" u2="W" k="2" />
<hkern u1="&#xcb;" u2="V" k="-4" />
<hkern u1="&#xcb;" u2="T" k="-27" />
<hkern u1="&#xcb;" u2="Q" k="10" />
<hkern u1="&#xcb;" u2="O" k="10" />
<hkern u1="&#xcb;" u2="J" k="-2" />
<hkern u1="&#xcb;" u2="G" k="10" />
<hkern u1="&#xcb;" u2="C" k="10" />
<hkern u1="&#xcb;" u2="&#x40;" k="41" />
<hkern u1="&#xcc;" u2="y" k="20" />
<hkern u1="&#xcc;" u2="v" k="20" />
<hkern u1="&#xcc;" u2="&#x2f;" k="41" />
<hkern u1="&#xcd;" u2="y" k="20" />
<hkern u1="&#xcd;" u2="v" k="20" />
<hkern u1="&#xcd;" u2="&#x2f;" k="41" />
<hkern u1="&#xce;" u2="y" k="20" />
<hkern u1="&#xce;" u2="v" k="20" />
<hkern u1="&#xce;" u2="&#x2f;" k="41" />
<hkern u1="&#xcf;" u2="y" k="20" />
<hkern u1="&#xcf;" u2="v" k="20" />
<hkern u1="&#xcf;" u2="&#x2f;" k="41" />
<hkern u1="&#xd0;" u2="&#x2026;" k="78" />
<hkern u1="&#xd0;" u2="&#x201e;" k="78" />
<hkern u1="&#xd0;" u2="&#x201c;" k="41" />
<hkern u1="&#xd0;" u2="&#x201a;" k="78" />
<hkern u1="&#xd0;" u2="&#x2018;" k="41" />
<hkern u1="&#xd0;" u2="&#x178;" k="47" />
<hkern u1="&#xd0;" u2="&#x153;" k="8" />
<hkern u1="&#xd0;" u2="&#xe7;" k="8" />
<hkern u1="&#xd0;" u2="&#xe6;" k="8" />
<hkern u1="&#xd0;" u2="&#xdd;" k="47" />
<hkern u1="&#xd0;" u2="&#xc6;" k="41" />
<hkern u1="&#xd0;" u2="&#xc5;" k="41" />
<hkern u1="&#xd0;" u2="&#xc4;" k="41" />
<hkern u1="&#xd0;" u2="&#xc3;" k="41" />
<hkern u1="&#xd0;" u2="&#xc2;" k="41" />
<hkern u1="&#xd0;" u2="&#xc1;" k="41" />
<hkern u1="&#xd0;" u2="&#xc0;" k="41" />
<hkern u1="&#xd0;" u2="z" k="20" />
<hkern u1="&#xd0;" u2="x" k="20" />
<hkern u1="&#xd0;" u2="u" k="8" />
<hkern u1="&#xd0;" u2="r" k="8" />
<hkern u1="&#xd0;" u2="q" k="8" />
<hkern u1="&#xd0;" u2="p" k="8" />
<hkern u1="&#xd0;" u2="o" k="8" />
<hkern u1="&#xd0;" u2="n" k="8" />
<hkern u1="&#xd0;" u2="m" k="8" />
<hkern u1="&#xd0;" u2="l" k="16" />
<hkern u1="&#xd0;" u2="k" k="16" />
<hkern u1="&#xd0;" u2="h" k="16" />
<hkern u1="&#xd0;" u2="e" k="8" />
<hkern u1="&#xd0;" u2="d" k="8" />
<hkern u1="&#xd0;" u2="c" k="8" />
<hkern u1="&#xd0;" u2="b" k="16" />
<hkern u1="&#xd0;" u2="a" k="8" />
<hkern u1="&#xd0;" u2="Z" k="78" />
<hkern u1="&#xd0;" u2="Y" k="47" />
<hkern u1="&#xd0;" u2="X" k="53" />
<hkern u1="&#xd0;" u2="W" k="78" />
<hkern u1="&#xd0;" u2="V" k="41" />
<hkern u1="&#xd0;" u2="T" k="55" />
<hkern u1="&#xd0;" u2="J" k="80" />
<hkern u1="&#xd0;" u2="A" k="41" />
<hkern u1="&#xd0;" u2="&#x3f;" k="41" />
<hkern u1="&#xd0;" u2="&#x2f;" k="102" />
<hkern u1="&#xd0;" u2="&#x2e;" k="78" />
<hkern u1="&#xd0;" u2="&#x2c;" k="78" />
<hkern u1="&#xd1;" u2="y" k="20" />
<hkern u1="&#xd1;" u2="v" k="20" />
<hkern u1="&#xd1;" u2="&#x2f;" k="41" />
<hkern u1="&#xd2;" u2="&#x2026;" k="78" />
<hkern u1="&#xd2;" u2="&#x201e;" k="78" />
<hkern u1="&#xd2;" u2="&#x201c;" k="41" />
<hkern u1="&#xd2;" u2="&#x201a;" k="78" />
<hkern u1="&#xd2;" u2="&#x2018;" k="41" />
<hkern u1="&#xd2;" u2="&#x178;" k="47" />
<hkern u1="&#xd2;" u2="&#x153;" k="8" />
<hkern u1="&#xd2;" u2="&#xe7;" k="8" />
<hkern u1="&#xd2;" u2="&#xe6;" k="8" />
<hkern u1="&#xd2;" u2="&#xdd;" k="47" />
<hkern u1="&#xd2;" u2="&#xc6;" k="41" />
<hkern u1="&#xd2;" u2="&#xc5;" k="41" />
<hkern u1="&#xd2;" u2="&#xc4;" k="41" />
<hkern u1="&#xd2;" u2="&#xc3;" k="41" />
<hkern u1="&#xd2;" u2="&#xc2;" k="41" />
<hkern u1="&#xd2;" u2="&#xc1;" k="41" />
<hkern u1="&#xd2;" u2="&#xc0;" k="41" />
<hkern u1="&#xd2;" u2="z" k="20" />
<hkern u1="&#xd2;" u2="x" k="20" />
<hkern u1="&#xd2;" u2="u" k="8" />
<hkern u1="&#xd2;" u2="r" k="8" />
<hkern u1="&#xd2;" u2="q" k="8" />
<hkern u1="&#xd2;" u2="p" k="8" />
<hkern u1="&#xd2;" u2="o" k="8" />
<hkern u1="&#xd2;" u2="n" k="8" />
<hkern u1="&#xd2;" u2="m" k="8" />
<hkern u1="&#xd2;" u2="l" k="16" />
<hkern u1="&#xd2;" u2="k" k="16" />
<hkern u1="&#xd2;" u2="h" k="16" />
<hkern u1="&#xd2;" u2="e" k="8" />
<hkern u1="&#xd2;" u2="d" k="8" />
<hkern u1="&#xd2;" u2="c" k="8" />
<hkern u1="&#xd2;" u2="b" k="16" />
<hkern u1="&#xd2;" u2="a" k="8" />
<hkern u1="&#xd2;" u2="Z" k="78" />
<hkern u1="&#xd2;" u2="Y" k="47" />
<hkern u1="&#xd2;" u2="X" k="53" />
<hkern u1="&#xd2;" u2="W" k="78" />
<hkern u1="&#xd2;" u2="V" k="41" />
<hkern u1="&#xd2;" u2="T" k="55" />
<hkern u1="&#xd2;" u2="J" k="80" />
<hkern u1="&#xd2;" u2="A" k="41" />
<hkern u1="&#xd2;" u2="&#x3f;" k="41" />
<hkern u1="&#xd2;" u2="&#x2f;" k="102" />
<hkern u1="&#xd2;" u2="&#x2e;" k="78" />
<hkern u1="&#xd2;" u2="&#x2c;" k="78" />
<hkern u1="&#xd3;" u2="&#x2026;" k="78" />
<hkern u1="&#xd3;" u2="&#x201e;" k="78" />
<hkern u1="&#xd3;" u2="&#x201c;" k="41" />
<hkern u1="&#xd3;" u2="&#x201a;" k="78" />
<hkern u1="&#xd3;" u2="&#x2018;" k="41" />
<hkern u1="&#xd3;" u2="&#x178;" k="47" />
<hkern u1="&#xd3;" u2="&#x153;" k="8" />
<hkern u1="&#xd3;" u2="&#xe7;" k="8" />
<hkern u1="&#xd3;" u2="&#xe6;" k="8" />
<hkern u1="&#xd3;" u2="&#xdd;" k="47" />
<hkern u1="&#xd3;" u2="&#xc6;" k="41" />
<hkern u1="&#xd3;" u2="&#xc5;" k="41" />
<hkern u1="&#xd3;" u2="&#xc4;" k="41" />
<hkern u1="&#xd3;" u2="&#xc3;" k="41" />
<hkern u1="&#xd3;" u2="&#xc2;" k="41" />
<hkern u1="&#xd3;" u2="&#xc1;" k="41" />
<hkern u1="&#xd3;" u2="&#xc0;" k="41" />
<hkern u1="&#xd3;" u2="z" k="20" />
<hkern u1="&#xd3;" u2="x" k="20" />
<hkern u1="&#xd3;" u2="u" k="8" />
<hkern u1="&#xd3;" u2="r" k="8" />
<hkern u1="&#xd3;" u2="q" k="8" />
<hkern u1="&#xd3;" u2="p" k="8" />
<hkern u1="&#xd3;" u2="o" k="8" />
<hkern u1="&#xd3;" u2="n" k="8" />
<hkern u1="&#xd3;" u2="m" k="8" />
<hkern u1="&#xd3;" u2="l" k="16" />
<hkern u1="&#xd3;" u2="k" k="16" />
<hkern u1="&#xd3;" u2="h" k="16" />
<hkern u1="&#xd3;" u2="e" k="8" />
<hkern u1="&#xd3;" u2="d" k="8" />
<hkern u1="&#xd3;" u2="c" k="8" />
<hkern u1="&#xd3;" u2="b" k="16" />
<hkern u1="&#xd3;" u2="a" k="8" />
<hkern u1="&#xd3;" u2="Z" k="78" />
<hkern u1="&#xd3;" u2="Y" k="47" />
<hkern u1="&#xd3;" u2="X" k="53" />
<hkern u1="&#xd3;" u2="W" k="78" />
<hkern u1="&#xd3;" u2="V" k="41" />
<hkern u1="&#xd3;" u2="T" k="55" />
<hkern u1="&#xd3;" u2="J" k="80" />
<hkern u1="&#xd3;" u2="A" k="41" />
<hkern u1="&#xd3;" u2="&#x3f;" k="41" />
<hkern u1="&#xd3;" u2="&#x2f;" k="102" />
<hkern u1="&#xd3;" u2="&#x2e;" k="78" />
<hkern u1="&#xd3;" u2="&#x2c;" k="78" />
<hkern u1="&#xd4;" u2="&#x2026;" k="78" />
<hkern u1="&#xd4;" u2="&#x201e;" k="78" />
<hkern u1="&#xd4;" u2="&#x201c;" k="41" />
<hkern u1="&#xd4;" u2="&#x201a;" k="78" />
<hkern u1="&#xd4;" u2="&#x2018;" k="41" />
<hkern u1="&#xd4;" u2="&#x178;" k="47" />
<hkern u1="&#xd4;" u2="&#x153;" k="8" />
<hkern u1="&#xd4;" u2="&#xe7;" k="8" />
<hkern u1="&#xd4;" u2="&#xe6;" k="8" />
<hkern u1="&#xd4;" u2="&#xdd;" k="47" />
<hkern u1="&#xd4;" u2="&#xc6;" k="41" />
<hkern u1="&#xd4;" u2="&#xc5;" k="41" />
<hkern u1="&#xd4;" u2="&#xc4;" k="41" />
<hkern u1="&#xd4;" u2="&#xc3;" k="41" />
<hkern u1="&#xd4;" u2="&#xc2;" k="41" />
<hkern u1="&#xd4;" u2="&#xc1;" k="41" />
<hkern u1="&#xd4;" u2="&#xc0;" k="41" />
<hkern u1="&#xd4;" u2="z" k="20" />
<hkern u1="&#xd4;" u2="x" k="20" />
<hkern u1="&#xd4;" u2="u" k="8" />
<hkern u1="&#xd4;" u2="r" k="8" />
<hkern u1="&#xd4;" u2="q" k="8" />
<hkern u1="&#xd4;" u2="p" k="8" />
<hkern u1="&#xd4;" u2="o" k="8" />
<hkern u1="&#xd4;" u2="n" k="8" />
<hkern u1="&#xd4;" u2="m" k="8" />
<hkern u1="&#xd4;" u2="l" k="16" />
<hkern u1="&#xd4;" u2="k" k="16" />
<hkern u1="&#xd4;" u2="h" k="16" />
<hkern u1="&#xd4;" u2="e" k="8" />
<hkern u1="&#xd4;" u2="d" k="8" />
<hkern u1="&#xd4;" u2="c" k="8" />
<hkern u1="&#xd4;" u2="b" k="16" />
<hkern u1="&#xd4;" u2="a" k="8" />
<hkern u1="&#xd4;" u2="Z" k="78" />
<hkern u1="&#xd4;" u2="Y" k="47" />
<hkern u1="&#xd4;" u2="X" k="53" />
<hkern u1="&#xd4;" u2="W" k="78" />
<hkern u1="&#xd4;" u2="V" k="41" />
<hkern u1="&#xd4;" u2="T" k="55" />
<hkern u1="&#xd4;" u2="J" k="80" />
<hkern u1="&#xd4;" u2="A" k="41" />
<hkern u1="&#xd4;" u2="&#x3f;" k="41" />
<hkern u1="&#xd4;" u2="&#x2f;" k="102" />
<hkern u1="&#xd4;" u2="&#x2e;" k="78" />
<hkern u1="&#xd4;" u2="&#x2c;" k="78" />
<hkern u1="&#xd5;" u2="&#x2026;" k="78" />
<hkern u1="&#xd5;" u2="&#x201e;" k="78" />
<hkern u1="&#xd5;" u2="&#x201c;" k="41" />
<hkern u1="&#xd5;" u2="&#x201a;" k="78" />
<hkern u1="&#xd5;" u2="&#x2018;" k="41" />
<hkern u1="&#xd5;" u2="&#x178;" k="47" />
<hkern u1="&#xd5;" u2="&#x153;" k="8" />
<hkern u1="&#xd5;" u2="&#xe7;" k="8" />
<hkern u1="&#xd5;" u2="&#xe6;" k="8" />
<hkern u1="&#xd5;" u2="&#xdd;" k="47" />
<hkern u1="&#xd5;" u2="&#xc6;" k="41" />
<hkern u1="&#xd5;" u2="&#xc5;" k="41" />
<hkern u1="&#xd5;" u2="&#xc4;" k="41" />
<hkern u1="&#xd5;" u2="&#xc3;" k="41" />
<hkern u1="&#xd5;" u2="&#xc2;" k="41" />
<hkern u1="&#xd5;" u2="&#xc1;" k="41" />
<hkern u1="&#xd5;" u2="&#xc0;" k="41" />
<hkern u1="&#xd5;" u2="z" k="20" />
<hkern u1="&#xd5;" u2="x" k="20" />
<hkern u1="&#xd5;" u2="u" k="8" />
<hkern u1="&#xd5;" u2="r" k="8" />
<hkern u1="&#xd5;" u2="q" k="8" />
<hkern u1="&#xd5;" u2="p" k="8" />
<hkern u1="&#xd5;" u2="o" k="8" />
<hkern u1="&#xd5;" u2="n" k="8" />
<hkern u1="&#xd5;" u2="m" k="8" />
<hkern u1="&#xd5;" u2="l" k="16" />
<hkern u1="&#xd5;" u2="k" k="16" />
<hkern u1="&#xd5;" u2="h" k="16" />
<hkern u1="&#xd5;" u2="e" k="8" />
<hkern u1="&#xd5;" u2="d" k="8" />
<hkern u1="&#xd5;" u2="c" k="8" />
<hkern u1="&#xd5;" u2="b" k="16" />
<hkern u1="&#xd5;" u2="a" k="8" />
<hkern u1="&#xd5;" u2="Z" k="78" />
<hkern u1="&#xd5;" u2="Y" k="47" />
<hkern u1="&#xd5;" u2="X" k="53" />
<hkern u1="&#xd5;" u2="W" k="78" />
<hkern u1="&#xd5;" u2="V" k="41" />
<hkern u1="&#xd5;" u2="T" k="55" />
<hkern u1="&#xd5;" u2="J" k="80" />
<hkern u1="&#xd5;" u2="A" k="41" />
<hkern u1="&#xd5;" u2="&#x3f;" k="41" />
<hkern u1="&#xd5;" u2="&#x2f;" k="102" />
<hkern u1="&#xd5;" u2="&#x2e;" k="78" />
<hkern u1="&#xd5;" u2="&#x2c;" k="78" />
<hkern u1="&#xd6;" u2="&#x2026;" k="78" />
<hkern u1="&#xd6;" u2="&#x201e;" k="78" />
<hkern u1="&#xd6;" u2="&#x201c;" k="41" />
<hkern u1="&#xd6;" u2="&#x201a;" k="78" />
<hkern u1="&#xd6;" u2="&#x2018;" k="41" />
<hkern u1="&#xd6;" u2="&#x178;" k="47" />
<hkern u1="&#xd6;" u2="&#x153;" k="8" />
<hkern u1="&#xd6;" u2="&#xe7;" k="8" />
<hkern u1="&#xd6;" u2="&#xe6;" k="8" />
<hkern u1="&#xd6;" u2="&#xdd;" k="47" />
<hkern u1="&#xd6;" u2="&#xc6;" k="41" />
<hkern u1="&#xd6;" u2="&#xc5;" k="41" />
<hkern u1="&#xd6;" u2="&#xc4;" k="41" />
<hkern u1="&#xd6;" u2="&#xc3;" k="41" />
<hkern u1="&#xd6;" u2="&#xc2;" k="41" />
<hkern u1="&#xd6;" u2="&#xc1;" k="41" />
<hkern u1="&#xd6;" u2="&#xc0;" k="41" />
<hkern u1="&#xd6;" u2="z" k="20" />
<hkern u1="&#xd6;" u2="x" k="20" />
<hkern u1="&#xd6;" u2="u" k="8" />
<hkern u1="&#xd6;" u2="r" k="8" />
<hkern u1="&#xd6;" u2="q" k="8" />
<hkern u1="&#xd6;" u2="p" k="8" />
<hkern u1="&#xd6;" u2="o" k="8" />
<hkern u1="&#xd6;" u2="n" k="8" />
<hkern u1="&#xd6;" u2="m" k="8" />
<hkern u1="&#xd6;" u2="l" k="16" />
<hkern u1="&#xd6;" u2="k" k="16" />
<hkern u1="&#xd6;" u2="h" k="16" />
<hkern u1="&#xd6;" u2="e" k="8" />
<hkern u1="&#xd6;" u2="d" k="8" />
<hkern u1="&#xd6;" u2="c" k="8" />
<hkern u1="&#xd6;" u2="b" k="16" />
<hkern u1="&#xd6;" u2="a" k="8" />
<hkern u1="&#xd6;" u2="Z" k="78" />
<hkern u1="&#xd6;" u2="Y" k="47" />
<hkern u1="&#xd6;" u2="X" k="53" />
<hkern u1="&#xd6;" u2="W" k="78" />
<hkern u1="&#xd6;" u2="V" k="41" />
<hkern u1="&#xd6;" u2="T" k="55" />
<hkern u1="&#xd6;" u2="J" k="80" />
<hkern u1="&#xd6;" u2="A" k="41" />
<hkern u1="&#xd6;" u2="&#x3f;" k="41" />
<hkern u1="&#xd6;" u2="&#x2f;" k="102" />
<hkern u1="&#xd6;" u2="&#x2e;" k="78" />
<hkern u1="&#xd6;" u2="&#x2c;" k="78" />
<hkern u1="&#xd8;" u2="&#x2026;" k="78" />
<hkern u1="&#xd8;" u2="&#x201e;" k="78" />
<hkern u1="&#xd8;" u2="&#x201c;" k="41" />
<hkern u1="&#xd8;" u2="&#x201a;" k="78" />
<hkern u1="&#xd8;" u2="&#x2018;" k="41" />
<hkern u1="&#xd8;" u2="&#x178;" k="47" />
<hkern u1="&#xd8;" u2="&#x153;" k="8" />
<hkern u1="&#xd8;" u2="&#xe7;" k="8" />
<hkern u1="&#xd8;" u2="&#xe6;" k="8" />
<hkern u1="&#xd8;" u2="&#xdd;" k="47" />
<hkern u1="&#xd8;" u2="&#xc6;" k="41" />
<hkern u1="&#xd8;" u2="&#xc5;" k="41" />
<hkern u1="&#xd8;" u2="&#xc4;" k="41" />
<hkern u1="&#xd8;" u2="&#xc3;" k="41" />
<hkern u1="&#xd8;" u2="&#xc2;" k="41" />
<hkern u1="&#xd8;" u2="&#xc1;" k="41" />
<hkern u1="&#xd8;" u2="&#xc0;" k="41" />
<hkern u1="&#xd8;" u2="z" k="20" />
<hkern u1="&#xd8;" u2="x" k="20" />
<hkern u1="&#xd8;" u2="u" k="8" />
<hkern u1="&#xd8;" u2="r" k="8" />
<hkern u1="&#xd8;" u2="q" k="8" />
<hkern u1="&#xd8;" u2="p" k="8" />
<hkern u1="&#xd8;" u2="o" k="8" />
<hkern u1="&#xd8;" u2="n" k="8" />
<hkern u1="&#xd8;" u2="m" k="8" />
<hkern u1="&#xd8;" u2="l" k="16" />
<hkern u1="&#xd8;" u2="k" k="16" />
<hkern u1="&#xd8;" u2="h" k="16" />
<hkern u1="&#xd8;" u2="e" k="8" />
<hkern u1="&#xd8;" u2="d" k="8" />
<hkern u1="&#xd8;" u2="c" k="8" />
<hkern u1="&#xd8;" u2="b" k="16" />
<hkern u1="&#xd8;" u2="a" k="8" />
<hkern u1="&#xd8;" u2="Z" k="78" />
<hkern u1="&#xd8;" u2="Y" k="47" />
<hkern u1="&#xd8;" u2="X" k="53" />
<hkern u1="&#xd8;" u2="W" k="78" />
<hkern u1="&#xd8;" u2="V" k="41" />
<hkern u1="&#xd8;" u2="T" k="55" />
<hkern u1="&#xd8;" u2="J" k="80" />
<hkern u1="&#xd8;" u2="A" k="41" />
<hkern u1="&#xd8;" u2="&#x3f;" k="41" />
<hkern u1="&#xd8;" u2="&#x2f;" k="102" />
<hkern u1="&#xd8;" u2="&#x2e;" k="78" />
<hkern u1="&#xd8;" u2="&#x2c;" k="78" />
<hkern u1="&#xd9;" u2="&#x2026;" k="20" />
<hkern u1="&#xd9;" u2="&#x201e;" k="20" />
<hkern u1="&#xd9;" u2="&#x201a;" k="20" />
<hkern u1="&#xd9;" u2="&#xc6;" k="8" />
<hkern u1="&#xd9;" u2="&#xc5;" k="8" />
<hkern u1="&#xd9;" u2="&#xc4;" k="8" />
<hkern u1="&#xd9;" u2="&#xc3;" k="8" />
<hkern u1="&#xd9;" u2="&#xc2;" k="8" />
<hkern u1="&#xd9;" u2="&#xc1;" k="8" />
<hkern u1="&#xd9;" u2="&#xc0;" k="8" />
<hkern u1="&#xd9;" u2="J" k="20" />
<hkern u1="&#xd9;" u2="A" k="8" />
<hkern u1="&#xd9;" u2="&#x2e;" k="20" />
<hkern u1="&#xd9;" u2="&#x2c;" k="20" />
<hkern u1="&#xda;" u2="&#x2026;" k="20" />
<hkern u1="&#xda;" u2="&#x201e;" k="20" />
<hkern u1="&#xda;" u2="&#x201a;" k="20" />
<hkern u1="&#xda;" u2="&#xc6;" k="8" />
<hkern u1="&#xda;" u2="&#xc5;" k="8" />
<hkern u1="&#xda;" u2="&#xc4;" k="8" />
<hkern u1="&#xda;" u2="&#xc3;" k="8" />
<hkern u1="&#xda;" u2="&#xc2;" k="8" />
<hkern u1="&#xda;" u2="&#xc1;" k="8" />
<hkern u1="&#xda;" u2="&#xc0;" k="8" />
<hkern u1="&#xda;" u2="J" k="20" />
<hkern u1="&#xda;" u2="A" k="8" />
<hkern u1="&#xda;" u2="&#x2e;" k="20" />
<hkern u1="&#xda;" u2="&#x2c;" k="20" />
<hkern u1="&#xdb;" u2="&#x2026;" k="20" />
<hkern u1="&#xdb;" u2="&#x201e;" k="20" />
<hkern u1="&#xdb;" u2="&#x201a;" k="20" />
<hkern u1="&#xdb;" u2="&#xc6;" k="8" />
<hkern u1="&#xdb;" u2="&#xc5;" k="8" />
<hkern u1="&#xdb;" u2="&#xc4;" k="8" />
<hkern u1="&#xdb;" u2="&#xc3;" k="8" />
<hkern u1="&#xdb;" u2="&#xc2;" k="8" />
<hkern u1="&#xdb;" u2="&#xc1;" k="8" />
<hkern u1="&#xdb;" u2="&#xc0;" k="8" />
<hkern u1="&#xdb;" u2="J" k="20" />
<hkern u1="&#xdb;" u2="A" k="8" />
<hkern u1="&#xdb;" u2="&#x2e;" k="20" />
<hkern u1="&#xdb;" u2="&#x2c;" k="20" />
<hkern u1="&#xdc;" u2="&#x2026;" k="20" />
<hkern u1="&#xdc;" u2="&#x201e;" k="20" />
<hkern u1="&#xdc;" u2="&#x201a;" k="20" />
<hkern u1="&#xdc;" u2="&#xc6;" k="8" />
<hkern u1="&#xdc;" u2="&#xc5;" k="8" />
<hkern u1="&#xdc;" u2="&#xc4;" k="8" />
<hkern u1="&#xdc;" u2="&#xc3;" k="8" />
<hkern u1="&#xdc;" u2="&#xc2;" k="8" />
<hkern u1="&#xdc;" u2="&#xc1;" k="8" />
<hkern u1="&#xdc;" u2="&#xc0;" k="8" />
<hkern u1="&#xdc;" u2="J" k="20" />
<hkern u1="&#xdc;" u2="A" k="8" />
<hkern u1="&#xdc;" u2="&#x2e;" k="20" />
<hkern u1="&#xdc;" u2="&#x2c;" k="20" />
<hkern u1="&#xdd;" u2="&#x203a;" k="82" />
<hkern u1="&#xdd;" u2="&#x2039;" k="123" />
<hkern u1="&#xdd;" u2="&#x2026;" k="102" />
<hkern u1="&#xdd;" u2="&#x201e;" k="102" />
<hkern u1="&#xdd;" u2="&#x201a;" k="102" />
<hkern u1="&#xdd;" u2="&#x2014;" k="102" />
<hkern u1="&#xdd;" u2="&#x2013;" k="102" />
<hkern u1="&#xdd;" u2="&#x153;" k="121" />
<hkern u1="&#xdd;" u2="&#x152;" k="47" />
<hkern u1="&#xdd;" u2="&#xe7;" k="121" />
<hkern u1="&#xdd;" u2="&#xe6;" k="121" />
<hkern u1="&#xdd;" u2="&#xd8;" k="47" />
<hkern u1="&#xdd;" u2="&#xd6;" k="47" />
<hkern u1="&#xdd;" u2="&#xd5;" k="47" />
<hkern u1="&#xdd;" u2="&#xd4;" k="47" />
<hkern u1="&#xdd;" u2="&#xd3;" k="47" />
<hkern u1="&#xdd;" u2="&#xd2;" k="47" />
<hkern u1="&#xdd;" u2="&#xc6;" k="104" />
<hkern u1="&#xdd;" u2="&#xc5;" k="104" />
<hkern u1="&#xdd;" u2="&#xc4;" k="104" />
<hkern u1="&#xdd;" u2="&#xc3;" k="104" />
<hkern u1="&#xdd;" u2="&#xc2;" k="104" />
<hkern u1="&#xdd;" u2="&#xc1;" k="104" />
<hkern u1="&#xdd;" u2="&#xc0;" k="104" />
<hkern u1="&#xdd;" u2="&#xbb;" k="82" />
<hkern u1="&#xdd;" u2="&#xae;" k="61" />
<hkern u1="&#xdd;" u2="&#xab;" k="123" />
<hkern u1="&#xdd;" u2="&#xa9;" k="61" />
<hkern u1="&#xdd;" u2="&#x7d;" k="-102" />
<hkern u1="&#xdd;" u2="z" k="61" />
<hkern u1="&#xdd;" u2="y" k="41" />
<hkern u1="&#xdd;" u2="x" k="41" />
<hkern u1="&#xdd;" u2="w" k="20" />
<hkern u1="&#xdd;" u2="v" k="41" />
<hkern u1="&#xdd;" u2="u" k="82" />
<hkern u1="&#xdd;" u2="t" k="41" />
<hkern u1="&#xdd;" u2="s" k="104" />
<hkern u1="&#xdd;" u2="r" k="82" />
<hkern u1="&#xdd;" u2="q" k="121" />
<hkern u1="&#xdd;" u2="p" k="82" />
<hkern u1="&#xdd;" u2="o" k="121" />
<hkern u1="&#xdd;" u2="n" k="82" />
<hkern u1="&#xdd;" u2="m" k="82" />
<hkern u1="&#xdd;" u2="g" k="82" />
<hkern u1="&#xdd;" u2="f" k="20" />
<hkern u1="&#xdd;" u2="e" k="121" />
<hkern u1="&#xdd;" u2="d" k="121" />
<hkern u1="&#xdd;" u2="c" k="121" />
<hkern u1="&#xdd;" u2="a" k="121" />
<hkern u1="&#xdd;" u2="]" k="-102" />
<hkern u1="&#xdd;" u2="X" k="-37" />
<hkern u1="&#xdd;" u2="V" k="-57" />
<hkern u1="&#xdd;" u2="T" k="-45" />
<hkern u1="&#xdd;" u2="S" k="-20" />
<hkern u1="&#xdd;" u2="Q" k="47" />
<hkern u1="&#xdd;" u2="O" k="47" />
<hkern u1="&#xdd;" u2="J" k="152" />
<hkern u1="&#xdd;" u2="G" k="47" />
<hkern u1="&#xdd;" u2="C" k="47" />
<hkern u1="&#xdd;" u2="A" k="104" />
<hkern u1="&#xdd;" u2="&#x40;" k="61" />
<hkern u1="&#xdd;" u2="&#x3b;" k="61" />
<hkern u1="&#xdd;" u2="&#x3a;" k="61" />
<hkern u1="&#xdd;" u2="&#x2f;" k="25" />
<hkern u1="&#xdd;" u2="&#x2e;" k="102" />
<hkern u1="&#xdd;" u2="&#x2d;" k="102" />
<hkern u1="&#xdd;" u2="&#x2c;" k="102" />
<hkern u1="&#xdd;" u2="&#x29;" k="-102" />
<hkern u1="&#xdd;" u2="&#x26;" k="61" />
<hkern u1="&#xde;" u2="&#x2026;" k="78" />
<hkern u1="&#xde;" u2="&#x201e;" k="78" />
<hkern u1="&#xde;" u2="&#x201c;" k="41" />
<hkern u1="&#xde;" u2="&#x201a;" k="78" />
<hkern u1="&#xde;" u2="&#x2018;" k="41" />
<hkern u1="&#xde;" u2="&#x178;" k="47" />
<hkern u1="&#xde;" u2="&#x153;" k="8" />
<hkern u1="&#xde;" u2="&#xe7;" k="8" />
<hkern u1="&#xde;" u2="&#xe6;" k="8" />
<hkern u1="&#xde;" u2="&#xdd;" k="47" />
<hkern u1="&#xde;" u2="&#xc6;" k="41" />
<hkern u1="&#xde;" u2="&#xc5;" k="41" />
<hkern u1="&#xde;" u2="&#xc4;" k="41" />
<hkern u1="&#xde;" u2="&#xc3;" k="41" />
<hkern u1="&#xde;" u2="&#xc2;" k="41" />
<hkern u1="&#xde;" u2="&#xc1;" k="41" />
<hkern u1="&#xde;" u2="&#xc0;" k="41" />
<hkern u1="&#xde;" u2="z" k="20" />
<hkern u1="&#xde;" u2="x" k="20" />
<hkern u1="&#xde;" u2="u" k="8" />
<hkern u1="&#xde;" u2="r" k="8" />
<hkern u1="&#xde;" u2="q" k="8" />
<hkern u1="&#xde;" u2="p" k="8" />
<hkern u1="&#xde;" u2="o" k="8" />
<hkern u1="&#xde;" u2="n" k="8" />
<hkern u1="&#xde;" u2="m" k="8" />
<hkern u1="&#xde;" u2="l" k="16" />
<hkern u1="&#xde;" u2="k" k="16" />
<hkern u1="&#xde;" u2="h" k="16" />
<hkern u1="&#xde;" u2="e" k="8" />
<hkern u1="&#xde;" u2="d" k="8" />
<hkern u1="&#xde;" u2="c" k="8" />
<hkern u1="&#xde;" u2="b" k="16" />
<hkern u1="&#xde;" u2="a" k="8" />
<hkern u1="&#xde;" u2="Z" k="78" />
<hkern u1="&#xde;" u2="Y" k="47" />
<hkern u1="&#xde;" u2="X" k="53" />
<hkern u1="&#xde;" u2="W" k="78" />
<hkern u1="&#xde;" u2="V" k="41" />
<hkern u1="&#xde;" u2="T" k="55" />
<hkern u1="&#xde;" u2="J" k="80" />
<hkern u1="&#xde;" u2="A" k="41" />
<hkern u1="&#xde;" u2="&#x3f;" k="41" />
<hkern u1="&#xde;" u2="&#x2f;" k="102" />
<hkern u1="&#xde;" u2="&#x2e;" k="78" />
<hkern u1="&#xde;" u2="&#x2c;" k="78" />
<hkern u1="&#xdf;" u2="&#x2122;" k="61" />
<hkern u1="&#xdf;" u2="&#x2026;" k="41" />
<hkern u1="&#xdf;" u2="&#x201e;" k="41" />
<hkern u1="&#xdf;" u2="&#x201c;" k="74" />
<hkern u1="&#xdf;" u2="&#x201a;" k="41" />
<hkern u1="&#xdf;" u2="&#x2018;" k="74" />
<hkern u1="&#xdf;" u2="z" k="33" />
<hkern u1="&#xdf;" u2="y" k="27" />
<hkern u1="&#xdf;" u2="x" k="35" />
<hkern u1="&#xdf;" u2="w" k="6" />
<hkern u1="&#xdf;" u2="v" k="27" />
<hkern u1="&#xdf;" u2="t" k="20" />
<hkern u1="&#xdf;" u2="g" k="20" />
<hkern u1="&#xdf;" u2="f" k="4" />
<hkern u1="&#xdf;" u2="\" k="20" />
<hkern u1="&#xdf;" u2="&#x3f;" k="47" />
<hkern u1="&#xdf;" u2="&#x3b;" k="20" />
<hkern u1="&#xdf;" u2="&#x3a;" k="20" />
<hkern u1="&#xdf;" u2="&#x2f;" k="20" />
<hkern u1="&#xdf;" u2="&#x2e;" k="41" />
<hkern u1="&#xdf;" u2="&#x2c;" k="41" />
<hkern u1="&#xdf;" u2="&#x21;" k="8" />
<hkern u1="&#xe0;" u2="&#x2122;" k="41" />
<hkern u1="&#xe0;" u2="&#x201c;" k="61" />
<hkern u1="&#xe0;" u2="&#x2018;" k="61" />
<hkern u1="&#xe0;" u2="y" k="12" />
<hkern u1="&#xe0;" u2="v" k="12" />
<hkern u1="&#xe0;" u2="t" k="20" />
<hkern u1="&#xe0;" u2="f" k="2" />
<hkern u1="&#xe0;" u2="\" k="20" />
<hkern u1="&#xe0;" u2="&#x3f;" k="25" />
<hkern u1="&#xe1;" u2="&#x2122;" k="41" />
<hkern u1="&#xe1;" u2="&#x201c;" k="61" />
<hkern u1="&#xe1;" u2="&#x2018;" k="61" />
<hkern u1="&#xe1;" u2="y" k="12" />
<hkern u1="&#xe1;" u2="v" k="12" />
<hkern u1="&#xe1;" u2="t" k="20" />
<hkern u1="&#xe1;" u2="f" k="2" />
<hkern u1="&#xe1;" u2="\" k="20" />
<hkern u1="&#xe1;" u2="&#x3f;" k="25" />
<hkern u1="&#xe2;" u2="&#x2122;" k="41" />
<hkern u1="&#xe2;" u2="&#x201c;" k="61" />
<hkern u1="&#xe2;" u2="&#x2018;" k="61" />
<hkern u1="&#xe2;" u2="y" k="12" />
<hkern u1="&#xe2;" u2="v" k="12" />
<hkern u1="&#xe2;" u2="t" k="20" />
<hkern u1="&#xe2;" u2="f" k="2" />
<hkern u1="&#xe2;" u2="\" k="20" />
<hkern u1="&#xe2;" u2="&#x3f;" k="25" />
<hkern u1="&#xe4;" u2="&#x2122;" k="41" />
<hkern u1="&#xe4;" u2="&#x201c;" k="61" />
<hkern u1="&#xe4;" u2="&#x2018;" k="61" />
<hkern u1="&#xe4;" u2="y" k="12" />
<hkern u1="&#xe4;" u2="v" k="12" />
<hkern u1="&#xe4;" u2="t" k="20" />
<hkern u1="&#xe4;" u2="f" k="2" />
<hkern u1="&#xe4;" u2="\" k="20" />
<hkern u1="&#xe4;" u2="&#x3f;" k="25" />
<hkern u1="&#xe6;" u2="y" k="25" />
<hkern u1="&#xe6;" u2="x" k="33" />
<hkern u1="&#xe6;" u2="w" k="4" />
<hkern u1="&#xe6;" u2="v" k="25" />
<hkern u1="&#xe6;" u2="t" k="2" />
<hkern u1="&#xe6;" u2="f" k="4" />
<hkern u1="&#xe7;" u2="&#x2039;" k="8" />
<hkern u1="&#xe7;" u2="&#xab;" k="8" />
<hkern u1="&#xe8;" u2="y" k="25" />
<hkern u1="&#xe8;" u2="x" k="33" />
<hkern u1="&#xe8;" u2="w" k="4" />
<hkern u1="&#xe8;" u2="v" k="25" />
<hkern u1="&#xe8;" u2="t" k="2" />
<hkern u1="&#xe8;" u2="f" k="4" />
<hkern u1="&#xe9;" u2="y" k="25" />
<hkern u1="&#xe9;" u2="x" k="33" />
<hkern u1="&#xe9;" u2="w" k="4" />
<hkern u1="&#xe9;" u2="v" k="25" />
<hkern u1="&#xe9;" u2="t" k="2" />
<hkern u1="&#xe9;" u2="f" k="4" />
<hkern u1="&#xea;" u2="y" k="25" />
<hkern u1="&#xea;" u2="x" k="33" />
<hkern u1="&#xea;" u2="w" k="4" />
<hkern u1="&#xea;" u2="v" k="25" />
<hkern u1="&#xea;" u2="t" k="2" />
<hkern u1="&#xea;" u2="f" k="4" />
<hkern u1="&#xeb;" u2="y" k="25" />
<hkern u1="&#xeb;" u2="x" k="33" />
<hkern u1="&#xeb;" u2="w" k="4" />
<hkern u1="&#xeb;" u2="v" k="25" />
<hkern u1="&#xeb;" u2="t" k="2" />
<hkern u1="&#xeb;" u2="f" k="4" />
<hkern u1="&#xf1;" u2="&#x2122;" k="41" />
<hkern u1="&#xf1;" u2="&#x201c;" k="61" />
<hkern u1="&#xf1;" u2="&#x2018;" k="61" />
<hkern u1="&#xf1;" u2="y" k="12" />
<hkern u1="&#xf1;" u2="v" k="12" />
<hkern u1="&#xf1;" u2="t" k="20" />
<hkern u1="&#xf1;" u2="f" k="2" />
<hkern u1="&#xf1;" u2="\" k="20" />
<hkern u1="&#xf1;" u2="&#x3f;" k="25" />
<hkern u1="&#xf3;" u2="&#x2122;" k="61" />
<hkern u1="&#xf3;" u2="&#x2026;" k="41" />
<hkern u1="&#xf3;" u2="&#x201e;" k="41" />
<hkern u1="&#xf3;" u2="&#x201c;" k="74" />
<hkern u1="&#xf3;" u2="&#x201a;" k="41" />
<hkern u1="&#xf3;" u2="&#x2018;" k="74" />
<hkern u1="&#xf3;" u2="z" k="33" />
<hkern u1="&#xf3;" u2="y" k="27" />
<hkern u1="&#xf3;" u2="x" k="35" />
<hkern u1="&#xf3;" u2="w" k="6" />
<hkern u1="&#xf3;" u2="v" k="27" />
<hkern u1="&#xf3;" u2="t" k="6" />
<hkern u1="&#xf3;" u2="f" k="4" />
<hkern u1="&#xf3;" u2="\" k="20" />
<hkern u1="&#xf3;" u2="&#x3f;" k="47" />
<hkern u1="&#xf3;" u2="&#x3b;" k="20" />
<hkern u1="&#xf3;" u2="&#x3a;" k="20" />
<hkern u1="&#xf3;" u2="&#x2f;" k="20" />
<hkern u1="&#xf3;" u2="&#x2e;" k="41" />
<hkern u1="&#xf3;" u2="&#x2c;" k="41" />
<hkern u1="&#xf3;" u2="&#x21;" k="8" />
<hkern u1="&#xf4;" u2="&#x2122;" k="61" />
<hkern u1="&#xf4;" u2="&#x2026;" k="41" />
<hkern u1="&#xf4;" u2="&#x201e;" k="41" />
<hkern u1="&#xf4;" u2="&#x201c;" k="74" />
<hkern u1="&#xf4;" u2="&#x201a;" k="41" />
<hkern u1="&#xf4;" u2="&#x2018;" k="74" />
<hkern u1="&#xf4;" u2="z" k="33" />
<hkern u1="&#xf4;" u2="y" k="27" />
<hkern u1="&#xf4;" u2="x" k="35" />
<hkern u1="&#xf4;" u2="w" k="6" />
<hkern u1="&#xf4;" u2="v" k="27" />
<hkern u1="&#xf4;" u2="t" k="6" />
<hkern u1="&#xf4;" u2="f" k="4" />
<hkern u1="&#xf4;" u2="\" k="20" />
<hkern u1="&#xf4;" u2="&#x3f;" k="47" />
<hkern u1="&#xf4;" u2="&#x3b;" k="20" />
<hkern u1="&#xf4;" u2="&#x3a;" k="20" />
<hkern u1="&#xf4;" u2="&#x2f;" k="20" />
<hkern u1="&#xf4;" u2="&#x2e;" k="41" />
<hkern u1="&#xf4;" u2="&#x2c;" k="41" />
<hkern u1="&#xf4;" u2="&#x21;" k="8" />
<hkern u1="&#xf5;" u2="&#x2122;" k="61" />
<hkern u1="&#xf5;" u2="&#x2026;" k="41" />
<hkern u1="&#xf5;" u2="&#x201e;" k="41" />
<hkern u1="&#xf5;" u2="&#x201c;" k="74" />
<hkern u1="&#xf5;" u2="&#x201a;" k="41" />
<hkern u1="&#xf5;" u2="&#x2018;" k="74" />
<hkern u1="&#xf5;" u2="z" k="33" />
<hkern u1="&#xf5;" u2="y" k="27" />
<hkern u1="&#xf5;" u2="x" k="35" />
<hkern u1="&#xf5;" u2="w" k="6" />
<hkern u1="&#xf5;" u2="v" k="27" />
<hkern u1="&#xf5;" u2="t" k="6" />
<hkern u1="&#xf5;" u2="f" k="4" />
<hkern u1="&#xf5;" u2="\" k="20" />
<hkern u1="&#xf5;" u2="&#x3f;" k="47" />
<hkern u1="&#xf5;" u2="&#x3b;" k="20" />
<hkern u1="&#xf5;" u2="&#x3a;" k="20" />
<hkern u1="&#xf5;" u2="&#x2f;" k="20" />
<hkern u1="&#xf5;" u2="&#x2e;" k="41" />
<hkern u1="&#xf5;" u2="&#x2c;" k="41" />
<hkern u1="&#xf5;" u2="&#x21;" k="8" />
<hkern u1="&#xf6;" u2="&#x2122;" k="61" />
<hkern u1="&#xf6;" u2="&#x2026;" k="41" />
<hkern u1="&#xf6;" u2="&#x201e;" k="41" />
<hkern u1="&#xf6;" u2="&#x201c;" k="74" />
<hkern u1="&#xf6;" u2="&#x201a;" k="41" />
<hkern u1="&#xf6;" u2="&#x2018;" k="74" />
<hkern u1="&#xf6;" u2="z" k="33" />
<hkern u1="&#xf6;" u2="y" k="27" />
<hkern u1="&#xf6;" u2="x" k="35" />
<hkern u1="&#xf6;" u2="w" k="6" />
<hkern u1="&#xf6;" u2="v" k="27" />
<hkern u1="&#xf6;" u2="t" k="6" />
<hkern u1="&#xf6;" u2="f" k="4" />
<hkern u1="&#xf6;" u2="\" k="20" />
<hkern u1="&#xf6;" u2="&#x3f;" k="47" />
<hkern u1="&#xf6;" u2="&#x3b;" k="20" />
<hkern u1="&#xf6;" u2="&#x3a;" k="20" />
<hkern u1="&#xf6;" u2="&#x2f;" k="20" />
<hkern u1="&#xf6;" u2="&#x2e;" k="41" />
<hkern u1="&#xf6;" u2="&#x2c;" k="41" />
<hkern u1="&#xf6;" u2="&#x21;" k="8" />
<hkern u1="&#xf8;" u2="&#x2122;" k="61" />
<hkern u1="&#xf8;" u2="&#x2026;" k="41" />
<hkern u1="&#xf8;" u2="&#x201e;" k="41" />
<hkern u1="&#xf8;" u2="&#x201c;" k="74" />
<hkern u1="&#xf8;" u2="&#x201a;" k="41" />
<hkern u1="&#xf8;" u2="&#x2018;" k="74" />
<hkern u1="&#xf8;" u2="z" k="33" />
<hkern u1="&#xf8;" u2="y" k="27" />
<hkern u1="&#xf8;" u2="x" k="35" />
<hkern u1="&#xf8;" u2="w" k="6" />
<hkern u1="&#xf8;" u2="v" k="27" />
<hkern u1="&#xf8;" u2="t" k="6" />
<hkern u1="&#xf8;" u2="f" k="4" />
<hkern u1="&#xf8;" u2="\" k="20" />
<hkern u1="&#xf8;" u2="&#x3f;" k="47" />
<hkern u1="&#xf8;" u2="&#x3b;" k="20" />
<hkern u1="&#xf8;" u2="&#x3a;" k="20" />
<hkern u1="&#xf8;" u2="&#x2f;" k="20" />
<hkern u1="&#xf8;" u2="&#x2e;" k="41" />
<hkern u1="&#xf8;" u2="&#x2c;" k="41" />
<hkern u1="&#xf8;" u2="&#x21;" k="8" />
<hkern u1="&#x152;" u2="&#x2039;" k="8" />
<hkern u1="&#x152;" u2="&#x153;" k="49" />
<hkern u1="&#x152;" u2="&#x152;" k="10" />
<hkern u1="&#x152;" u2="&#xfe;" k="2" />
<hkern u1="&#x152;" u2="&#xe7;" k="49" />
<hkern u1="&#x152;" u2="&#xe6;" k="49" />
<hkern u1="&#x152;" u2="&#xd8;" k="10" />
<hkern u1="&#x152;" u2="&#xd6;" k="10" />
<hkern u1="&#x152;" u2="&#xd5;" k="10" />
<hkern u1="&#x152;" u2="&#xd4;" k="10" />
<hkern u1="&#x152;" u2="&#xd3;" k="10" />
<hkern u1="&#x152;" u2="&#xd2;" k="10" />
<hkern u1="&#x152;" u2="&#xae;" k="41" />
<hkern u1="&#x152;" u2="&#xab;" k="8" />
<hkern u1="&#x152;" u2="&#xa9;" k="41" />
<hkern u1="&#x152;" u2="y" k="41" />
<hkern u1="&#x152;" u2="v" k="41" />
<hkern u1="&#x152;" u2="u" k="4" />
<hkern u1="&#x152;" u2="r" k="4" />
<hkern u1="&#x152;" u2="q" k="49" />
<hkern u1="&#x152;" u2="p" k="4" />
<hkern u1="&#x152;" u2="o" k="49" />
<hkern u1="&#x152;" u2="n" k="4" />
<hkern u1="&#x152;" u2="m" k="4" />
<hkern u1="&#x152;" u2="l" k="2" />
<hkern u1="&#x152;" u2="k" k="2" />
<hkern u1="&#x152;" u2="h" k="2" />
<hkern u1="&#x152;" u2="g" k="16" />
<hkern u1="&#x152;" u2="f" k="20" />
<hkern u1="&#x152;" u2="e" k="49" />
<hkern u1="&#x152;" u2="d" k="49" />
<hkern u1="&#x152;" u2="c" k="49" />
<hkern u1="&#x152;" u2="b" k="2" />
<hkern u1="&#x152;" u2="a" k="49" />
<hkern u1="&#x152;" u2="W" k="2" />
<hkern u1="&#x152;" u2="V" k="-4" />
<hkern u1="&#x152;" u2="T" k="-27" />
<hkern u1="&#x152;" u2="Q" k="10" />
<hkern u1="&#x152;" u2="O" k="10" />
<hkern u1="&#x152;" u2="J" k="-2" />
<hkern u1="&#x152;" u2="G" k="10" />
<hkern u1="&#x152;" u2="C" k="10" />
<hkern u1="&#x152;" u2="&#x40;" k="41" />
<hkern u1="&#x153;" u2="y" k="25" />
<hkern u1="&#x153;" u2="x" k="33" />
<hkern u1="&#x153;" u2="w" k="4" />
<hkern u1="&#x153;" u2="v" k="25" />
<hkern u1="&#x153;" u2="t" k="2" />
<hkern u1="&#x153;" u2="f" k="4" />
<hkern u1="&#x178;" u2="&#x203a;" k="82" />
<hkern u1="&#x178;" u2="&#x2039;" k="123" />
<hkern u1="&#x178;" u2="&#x2026;" k="102" />
<hkern u1="&#x178;" u2="&#x201e;" k="102" />
<hkern u1="&#x178;" u2="&#x201a;" k="102" />
<hkern u1="&#x178;" u2="&#x2014;" k="102" />
<hkern u1="&#x178;" u2="&#x2013;" k="102" />
<hkern u1="&#x178;" u2="&#x153;" k="121" />
<hkern u1="&#x178;" u2="&#x152;" k="47" />
<hkern u1="&#x178;" u2="&#xe7;" k="121" />
<hkern u1="&#x178;" u2="&#xe6;" k="121" />
<hkern u1="&#x178;" u2="&#xd8;" k="47" />
<hkern u1="&#x178;" u2="&#xd6;" k="47" />
<hkern u1="&#x178;" u2="&#xd5;" k="47" />
<hkern u1="&#x178;" u2="&#xd4;" k="47" />
<hkern u1="&#x178;" u2="&#xd3;" k="47" />
<hkern u1="&#x178;" u2="&#xd2;" k="47" />
<hkern u1="&#x178;" u2="&#xc6;" k="104" />
<hkern u1="&#x178;" u2="&#xc5;" k="104" />
<hkern u1="&#x178;" u2="&#xc4;" k="104" />
<hkern u1="&#x178;" u2="&#xc3;" k="104" />
<hkern u1="&#x178;" u2="&#xc2;" k="104" />
<hkern u1="&#x178;" u2="&#xc1;" k="104" />
<hkern u1="&#x178;" u2="&#xc0;" k="104" />
<hkern u1="&#x178;" u2="&#xbb;" k="82" />
<hkern u1="&#x178;" u2="&#xae;" k="61" />
<hkern u1="&#x178;" u2="&#xab;" k="123" />
<hkern u1="&#x178;" u2="&#xa9;" k="61" />
<hkern u1="&#x178;" u2="&#x7d;" k="-102" />
<hkern u1="&#x178;" u2="z" k="61" />
<hkern u1="&#x178;" u2="y" k="41" />
<hkern u1="&#x178;" u2="x" k="41" />
<hkern u1="&#x178;" u2="w" k="20" />
<hkern u1="&#x178;" u2="v" k="41" />
<hkern u1="&#x178;" u2="u" k="82" />
<hkern u1="&#x178;" u2="t" k="41" />
<hkern u1="&#x178;" u2="s" k="104" />
<hkern u1="&#x178;" u2="r" k="82" />
<hkern u1="&#x178;" u2="q" k="121" />
<hkern u1="&#x178;" u2="p" k="82" />
<hkern u1="&#x178;" u2="o" k="121" />
<hkern u1="&#x178;" u2="n" k="82" />
<hkern u1="&#x178;" u2="m" k="82" />
<hkern u1="&#x178;" u2="g" k="82" />
<hkern u1="&#x178;" u2="f" k="20" />
<hkern u1="&#x178;" u2="e" k="121" />
<hkern u1="&#x178;" u2="d" k="121" />
<hkern u1="&#x178;" u2="c" k="121" />
<hkern u1="&#x178;" u2="a" k="121" />
<hkern u1="&#x178;" u2="]" k="-102" />
<hkern u1="&#x178;" u2="X" k="-37" />
<hkern u1="&#x178;" u2="V" k="-57" />
<hkern u1="&#x178;" u2="T" k="-45" />
<hkern u1="&#x178;" u2="S" k="-20" />
<hkern u1="&#x178;" u2="Q" k="47" />
<hkern u1="&#x178;" u2="O" k="47" />
<hkern u1="&#x178;" u2="J" k="152" />
<hkern u1="&#x178;" u2="G" k="47" />
<hkern u1="&#x178;" u2="C" k="47" />
<hkern u1="&#x178;" u2="A" k="104" />
<hkern u1="&#x178;" u2="&#x40;" k="61" />
<hkern u1="&#x178;" u2="&#x3b;" k="61" />
<hkern u1="&#x178;" u2="&#x3a;" k="61" />
<hkern u1="&#x178;" u2="&#x2f;" k="25" />
<hkern u1="&#x178;" u2="&#x2e;" k="102" />
<hkern u1="&#x178;" u2="&#x2d;" k="102" />
<hkern u1="&#x178;" u2="&#x2c;" k="102" />
<hkern u1="&#x178;" u2="&#x29;" k="-102" />
<hkern u1="&#x178;" u2="&#x26;" k="61" />
<hkern u1="&#x2013;" u2="&#x178;" k="102" />
<hkern u1="&#x2013;" u2="&#x153;" k="20" />
<hkern u1="&#x2013;" u2="&#xe7;" k="20" />
<hkern u1="&#x2013;" u2="&#xe6;" k="20" />
<hkern u1="&#x2013;" u2="&#xdd;" k="102" />
<hkern u1="&#x2013;" u2="&#xc6;" k="-20" />
<hkern u1="&#x2013;" u2="&#xc5;" k="-20" />
<hkern u1="&#x2013;" u2="&#xc4;" k="-20" />
<hkern u1="&#x2013;" u2="&#xc3;" k="-20" />
<hkern u1="&#x2013;" u2="&#xc2;" k="-20" />
<hkern u1="&#x2013;" u2="&#xc1;" k="-20" />
<hkern u1="&#x2013;" u2="&#xc0;" k="-20" />
<hkern u1="&#x2013;" u2="z" k="53" />
<hkern u1="&#x2013;" u2="y" k="20" />
<hkern u1="&#x2013;" u2="x" k="61" />
<hkern u1="&#x2013;" u2="v" k="20" />
<hkern u1="&#x2013;" u2="q" k="20" />
<hkern u1="&#x2013;" u2="o" k="20" />
<hkern u1="&#x2013;" u2="e" k="20" />
<hkern u1="&#x2013;" u2="d" k="20" />
<hkern u1="&#x2013;" u2="c" k="20" />
<hkern u1="&#x2013;" u2="a" k="20" />
<hkern u1="&#x2013;" u2="Z" k="20" />
<hkern u1="&#x2013;" u2="Y" k="102" />
<hkern u1="&#x2013;" u2="X" k="61" />
<hkern u1="&#x2013;" u2="W" k="82" />
<hkern u1="&#x2013;" u2="V" k="102" />
<hkern u1="&#x2013;" u2="T" k="135" />
<hkern u1="&#x2013;" u2="A" k="-20" />
<hkern u1="&#x2013;" u2="&#x37;" k="61" />
<hkern u1="&#x2013;" u2="&#x31;" k="41" />
<hkern u1="&#x2014;" u2="&#x178;" k="102" />
<hkern u1="&#x2014;" u2="&#x153;" k="20" />
<hkern u1="&#x2014;" u2="&#xe7;" k="20" />
<hkern u1="&#x2014;" u2="&#xe6;" k="20" />
<hkern u1="&#x2014;" u2="&#xdd;" k="102" />
<hkern u1="&#x2014;" u2="&#xc6;" k="-20" />
<hkern u1="&#x2014;" u2="&#xc5;" k="-20" />
<hkern u1="&#x2014;" u2="&#xc4;" k="-20" />
<hkern u1="&#x2014;" u2="&#xc3;" k="-20" />
<hkern u1="&#x2014;" u2="&#xc2;" k="-20" />
<hkern u1="&#x2014;" u2="&#xc1;" k="-20" />
<hkern u1="&#x2014;" u2="&#xc0;" k="-20" />
<hkern u1="&#x2014;" u2="z" k="53" />
<hkern u1="&#x2014;" u2="y" k="20" />
<hkern u1="&#x2014;" u2="x" k="61" />
<hkern u1="&#x2014;" u2="v" k="20" />
<hkern u1="&#x2014;" u2="q" k="20" />
<hkern u1="&#x2014;" u2="o" k="20" />
<hkern u1="&#x2014;" u2="e" k="20" />
<hkern u1="&#x2014;" u2="d" k="20" />
<hkern u1="&#x2014;" u2="c" k="20" />
<hkern u1="&#x2014;" u2="a" k="20" />
<hkern u1="&#x2014;" u2="Z" k="20" />
<hkern u1="&#x2014;" u2="Y" k="102" />
<hkern u1="&#x2014;" u2="X" k="61" />
<hkern u1="&#x2014;" u2="W" k="82" />
<hkern u1="&#x2014;" u2="V" k="102" />
<hkern u1="&#x2014;" u2="T" k="135" />
<hkern u1="&#x2014;" u2="A" k="-20" />
<hkern u1="&#x2014;" u2="&#x37;" k="61" />
<hkern u1="&#x2014;" u2="&#x31;" k="41" />
<hkern u1="&#x2018;" u2="&#x178;" k="-61" />
<hkern u1="&#x2018;" u2="&#x153;" k="61" />
<hkern u1="&#x2018;" u2="&#xe7;" k="61" />
<hkern u1="&#x2018;" u2="&#xe6;" k="61" />
<hkern u1="&#x2018;" u2="&#xdd;" k="-61" />
<hkern u1="&#x2018;" u2="&#xc6;" k="82" />
<hkern u1="&#x2018;" u2="&#xc5;" k="82" />
<hkern u1="&#x2018;" u2="&#xc4;" k="82" />
<hkern u1="&#x2018;" u2="&#xc3;" k="82" />
<hkern u1="&#x2018;" u2="&#xc2;" k="82" />
<hkern u1="&#x2018;" u2="&#xc1;" k="82" />
<hkern u1="&#x2018;" u2="&#xc0;" k="82" />
<hkern u1="&#x2018;" u2="u" k="20" />
<hkern u1="&#x2018;" u2="s" k="61" />
<hkern u1="&#x2018;" u2="r" k="20" />
<hkern u1="&#x2018;" u2="q" k="61" />
<hkern u1="&#x2018;" u2="p" k="20" />
<hkern u1="&#x2018;" u2="o" k="61" />
<hkern u1="&#x2018;" u2="n" k="20" />
<hkern u1="&#x2018;" u2="m" k="20" />
<hkern u1="&#x2018;" u2="g" k="82" />
<hkern u1="&#x2018;" u2="e" k="61" />
<hkern u1="&#x2018;" u2="d" k="61" />
<hkern u1="&#x2018;" u2="c" k="61" />
<hkern u1="&#x2018;" u2="a" k="61" />
<hkern u1="&#x2018;" u2="Y" k="-61" />
<hkern u1="&#x2018;" u2="X" k="-41" />
<hkern u1="&#x2018;" u2="W" k="-41" />
<hkern u1="&#x2018;" u2="V" k="-41" />
<hkern u1="&#x2018;" u2="T" k="-20" />
<hkern u1="&#x2018;" u2="J" k="229" />
<hkern u1="&#x2018;" u2="A" k="82" />
<hkern u1="&#x2019;" u2="&#x153;" k="102" />
<hkern u1="&#x2019;" u2="&#x152;" k="82" />
<hkern u1="&#x2019;" u2="&#xe7;" k="102" />
<hkern u1="&#x2019;" u2="&#xe6;" k="102" />
<hkern u1="&#x2019;" u2="&#xd8;" k="82" />
<hkern u1="&#x2019;" u2="&#xd6;" k="82" />
<hkern u1="&#x2019;" u2="&#xd5;" k="82" />
<hkern u1="&#x2019;" u2="&#xd4;" k="82" />
<hkern u1="&#x2019;" u2="&#xd3;" k="82" />
<hkern u1="&#x2019;" u2="&#xd2;" k="82" />
<hkern u1="&#x2019;" u2="&#xc6;" k="20" />
<hkern u1="&#x2019;" u2="&#xc5;" k="20" />
<hkern u1="&#x2019;" u2="&#xc4;" k="20" />
<hkern u1="&#x2019;" u2="&#xc3;" k="20" />
<hkern u1="&#x2019;" u2="&#xc2;" k="20" />
<hkern u1="&#x2019;" u2="&#xc1;" k="20" />
<hkern u1="&#x2019;" u2="&#xc0;" k="20" />
<hkern u1="&#x2019;" u2="q" k="102" />
<hkern u1="&#x2019;" u2="o" k="102" />
<hkern u1="&#x2019;" u2="e" k="102" />
<hkern u1="&#x2019;" u2="d" k="102" />
<hkern u1="&#x2019;" u2="c" k="102" />
<hkern u1="&#x2019;" u2="a" k="102" />
<hkern u1="&#x2019;" u2="S" k="20" />
<hkern u1="&#x2019;" u2="Q" k="82" />
<hkern u1="&#x2019;" u2="O" k="82" />
<hkern u1="&#x2019;" u2="J" k="246" />
<hkern u1="&#x2019;" u2="G" k="82" />
<hkern u1="&#x2019;" u2="C" k="82" />
<hkern u1="&#x2019;" u2="A" k="20" />
<hkern u1="&#x201a;" u2="&#x178;" k="102" />
<hkern u1="&#x201a;" u2="&#x153;" k="41" />
<hkern u1="&#x201a;" u2="&#x152;" k="78" />
<hkern u1="&#x201a;" u2="&#xe7;" k="41" />
<hkern u1="&#x201a;" u2="&#xe6;" k="41" />
<hkern u1="&#x201a;" u2="&#xdd;" k="102" />
<hkern u1="&#x201a;" u2="&#xdc;" k="20" />
<hkern u1="&#x201a;" u2="&#xdb;" k="20" />
<hkern u1="&#x201a;" u2="&#xda;" k="20" />
<hkern u1="&#x201a;" u2="&#xd9;" k="20" />
<hkern u1="&#x201a;" u2="&#xd8;" k="78" />
<hkern u1="&#x201a;" u2="&#xd6;" k="78" />
<hkern u1="&#x201a;" u2="&#xd5;" k="78" />
<hkern u1="&#x201a;" u2="&#xd4;" k="78" />
<hkern u1="&#x201a;" u2="&#xd3;" k="78" />
<hkern u1="&#x201a;" u2="&#xd2;" k="78" />
<hkern u1="&#x201a;" u2="&#xc6;" k="-61" />
<hkern u1="&#x201a;" u2="&#xc5;" k="-61" />
<hkern u1="&#x201a;" u2="&#xc4;" k="-61" />
<hkern u1="&#x201a;" u2="&#xc3;" k="-61" />
<hkern u1="&#x201a;" u2="&#xc2;" k="-61" />
<hkern u1="&#x201a;" u2="&#xc1;" k="-61" />
<hkern u1="&#x201a;" u2="&#xc0;" k="-61" />
<hkern u1="&#x201a;" u2="y" k="61" />
<hkern u1="&#x201a;" u2="w" k="20" />
<hkern u1="&#x201a;" u2="v" k="61" />
<hkern u1="&#x201a;" u2="u" k="41" />
<hkern u1="&#x201a;" u2="t" k="82" />
<hkern u1="&#x201a;" u2="r" k="41" />
<hkern u1="&#x201a;" u2="q" k="41" />
<hkern u1="&#x201a;" u2="p" k="41" />
<hkern u1="&#x201a;" u2="o" k="41" />
<hkern u1="&#x201a;" u2="n" k="41" />
<hkern u1="&#x201a;" u2="m" k="41" />
<hkern u1="&#x201a;" u2="j" k="-61" />
<hkern u1="&#x201a;" u2="f" k="16" />
<hkern u1="&#x201a;" u2="e" k="41" />
<hkern u1="&#x201a;" u2="d" k="41" />
<hkern u1="&#x201a;" u2="c" k="41" />
<hkern u1="&#x201a;" u2="a" k="41" />
<hkern u1="&#x201a;" u2="Y" k="102" />
<hkern u1="&#x201a;" u2="W" k="45" />
<hkern u1="&#x201a;" u2="V" k="127" />
<hkern u1="&#x201a;" u2="U" k="20" />
<hkern u1="&#x201a;" u2="T" k="135" />
<hkern u1="&#x201a;" u2="Q" k="78" />
<hkern u1="&#x201a;" u2="O" k="78" />
<hkern u1="&#x201a;" u2="G" k="78" />
<hkern u1="&#x201a;" u2="C" k="78" />
<hkern u1="&#x201a;" u2="A" k="-61" />
<hkern u1="&#x201a;" u2="&#x39;" k="16" />
<hkern u1="&#x201a;" u2="&#x38;" k="31" />
<hkern u1="&#x201a;" u2="&#x37;" k="4" />
<hkern u1="&#x201a;" u2="&#x36;" k="57" />
<hkern u1="&#x201a;" u2="&#x35;" k="-8" />
<hkern u1="&#x201a;" u2="&#x34;" k="82" />
<hkern u1="&#x201a;" u2="&#x33;" k="-4" />
<hkern u1="&#x201a;" u2="&#x32;" k="-4" />
<hkern u1="&#x201a;" u2="&#x31;" k="137" />
<hkern u1="&#x201a;" u2="&#x30;" k="70" />
<hkern u1="&#x201c;" u2="&#x178;" k="-61" />
<hkern u1="&#x201c;" u2="&#x153;" k="61" />
<hkern u1="&#x201c;" u2="&#xe7;" k="61" />
<hkern u1="&#x201c;" u2="&#xe6;" k="61" />
<hkern u1="&#x201c;" u2="&#xdd;" k="-61" />
<hkern u1="&#x201c;" u2="&#xc6;" k="82" />
<hkern u1="&#x201c;" u2="&#xc5;" k="82" />
<hkern u1="&#x201c;" u2="&#xc4;" k="82" />
<hkern u1="&#x201c;" u2="&#xc3;" k="82" />
<hkern u1="&#x201c;" u2="&#xc2;" k="82" />
<hkern u1="&#x201c;" u2="&#xc1;" k="82" />
<hkern u1="&#x201c;" u2="&#xc0;" k="82" />
<hkern u1="&#x201c;" u2="u" k="20" />
<hkern u1="&#x201c;" u2="s" k="61" />
<hkern u1="&#x201c;" u2="r" k="20" />
<hkern u1="&#x201c;" u2="q" k="61" />
<hkern u1="&#x201c;" u2="p" k="20" />
<hkern u1="&#x201c;" u2="o" k="61" />
<hkern u1="&#x201c;" u2="n" k="20" />
<hkern u1="&#x201c;" u2="m" k="20" />
<hkern u1="&#x201c;" u2="g" k="82" />
<hkern u1="&#x201c;" u2="e" k="61" />
<hkern u1="&#x201c;" u2="d" k="61" />
<hkern u1="&#x201c;" u2="c" k="61" />
<hkern u1="&#x201c;" u2="a" k="61" />
<hkern u1="&#x201c;" u2="Y" k="-61" />
<hkern u1="&#x201c;" u2="X" k="-41" />
<hkern u1="&#x201c;" u2="W" k="-41" />
<hkern u1="&#x201c;" u2="V" k="-41" />
<hkern u1="&#x201c;" u2="T" k="-20" />
<hkern u1="&#x201c;" u2="J" k="229" />
<hkern u1="&#x201c;" u2="A" k="82" />
<hkern u1="&#x201d;" u2="&#x153;" k="102" />
<hkern u1="&#x201d;" u2="&#x152;" k="82" />
<hkern u1="&#x201d;" u2="&#xe7;" k="102" />
<hkern u1="&#x201d;" u2="&#xe6;" k="102" />
<hkern u1="&#x201d;" u2="&#xd8;" k="82" />
<hkern u1="&#x201d;" u2="&#xd6;" k="82" />
<hkern u1="&#x201d;" u2="&#xd5;" k="82" />
<hkern u1="&#x201d;" u2="&#xd4;" k="82" />
<hkern u1="&#x201d;" u2="&#xd3;" k="82" />
<hkern u1="&#x201d;" u2="&#xd2;" k="82" />
<hkern u1="&#x201d;" u2="&#xc6;" k="20" />
<hkern u1="&#x201d;" u2="&#xc5;" k="20" />
<hkern u1="&#x201d;" u2="&#xc4;" k="20" />
<hkern u1="&#x201d;" u2="&#xc3;" k="20" />
<hkern u1="&#x201d;" u2="&#xc2;" k="20" />
<hkern u1="&#x201d;" u2="&#xc1;" k="20" />
<hkern u1="&#x201d;" u2="&#xc0;" k="20" />
<hkern u1="&#x201d;" u2="q" k="102" />
<hkern u1="&#x201d;" u2="o" k="102" />
<hkern u1="&#x201d;" u2="e" k="102" />
<hkern u1="&#x201d;" u2="d" k="102" />
<hkern u1="&#x201d;" u2="c" k="102" />
<hkern u1="&#x201d;" u2="a" k="102" />
<hkern u1="&#x201d;" u2="S" k="20" />
<hkern u1="&#x201d;" u2="Q" k="82" />
<hkern u1="&#x201d;" u2="O" k="82" />
<hkern u1="&#x201d;" u2="J" k="246" />
<hkern u1="&#x201d;" u2="G" k="82" />
<hkern u1="&#x201d;" u2="C" k="82" />
<hkern u1="&#x201d;" u2="A" k="20" />
<hkern u1="&#x201e;" u2="&#x178;" k="102" />
<hkern u1="&#x201e;" u2="&#x153;" k="41" />
<hkern u1="&#x201e;" u2="&#x152;" k="78" />
<hkern u1="&#x201e;" u2="&#xe7;" k="41" />
<hkern u1="&#x201e;" u2="&#xe6;" k="41" />
<hkern u1="&#x201e;" u2="&#xdd;" k="102" />
<hkern u1="&#x201e;" u2="&#xdc;" k="20" />
<hkern u1="&#x201e;" u2="&#xdb;" k="20" />
<hkern u1="&#x201e;" u2="&#xda;" k="20" />
<hkern u1="&#x201e;" u2="&#xd9;" k="20" />
<hkern u1="&#x201e;" u2="&#xd8;" k="78" />
<hkern u1="&#x201e;" u2="&#xd6;" k="78" />
<hkern u1="&#x201e;" u2="&#xd5;" k="78" />
<hkern u1="&#x201e;" u2="&#xd4;" k="78" />
<hkern u1="&#x201e;" u2="&#xd3;" k="78" />
<hkern u1="&#x201e;" u2="&#xd2;" k="78" />
<hkern u1="&#x201e;" u2="&#xc6;" k="-61" />
<hkern u1="&#x201e;" u2="&#xc5;" k="-61" />
<hkern u1="&#x201e;" u2="&#xc4;" k="-61" />
<hkern u1="&#x201e;" u2="&#xc3;" k="-61" />
<hkern u1="&#x201e;" u2="&#xc2;" k="-61" />
<hkern u1="&#x201e;" u2="&#xc1;" k="-61" />
<hkern u1="&#x201e;" u2="&#xc0;" k="-61" />
<hkern u1="&#x201e;" u2="y" k="61" />
<hkern u1="&#x201e;" u2="w" k="20" />
<hkern u1="&#x201e;" u2="v" k="61" />
<hkern u1="&#x201e;" u2="u" k="41" />
<hkern u1="&#x201e;" u2="t" k="82" />
<hkern u1="&#x201e;" u2="r" k="41" />
<hkern u1="&#x201e;" u2="q" k="41" />
<hkern u1="&#x201e;" u2="p" k="41" />
<hkern u1="&#x201e;" u2="o" k="41" />
<hkern u1="&#x201e;" u2="n" k="41" />
<hkern u1="&#x201e;" u2="m" k="41" />
<hkern u1="&#x201e;" u2="j" k="-61" />
<hkern u1="&#x201e;" u2="f" k="16" />
<hkern u1="&#x201e;" u2="e" k="41" />
<hkern u1="&#x201e;" u2="d" k="41" />
<hkern u1="&#x201e;" u2="c" k="41" />
<hkern u1="&#x201e;" u2="a" k="41" />
<hkern u1="&#x201e;" u2="Y" k="102" />
<hkern u1="&#x201e;" u2="W" k="45" />
<hkern u1="&#x201e;" u2="V" k="127" />
<hkern u1="&#x201e;" u2="U" k="20" />
<hkern u1="&#x201e;" u2="T" k="135" />
<hkern u1="&#x201e;" u2="Q" k="78" />
<hkern u1="&#x201e;" u2="O" k="78" />
<hkern u1="&#x201e;" u2="G" k="78" />
<hkern u1="&#x201e;" u2="C" k="78" />
<hkern u1="&#x201e;" u2="A" k="-61" />
<hkern u1="&#x201e;" u2="&#x39;" k="16" />
<hkern u1="&#x201e;" u2="&#x38;" k="31" />
<hkern u1="&#x201e;" u2="&#x37;" k="4" />
<hkern u1="&#x201e;" u2="&#x36;" k="57" />
<hkern u1="&#x201e;" u2="&#x35;" k="-8" />
<hkern u1="&#x201e;" u2="&#x34;" k="82" />
<hkern u1="&#x201e;" u2="&#x33;" k="-4" />
<hkern u1="&#x201e;" u2="&#x32;" k="-4" />
<hkern u1="&#x201e;" u2="&#x31;" k="137" />
<hkern u1="&#x201e;" u2="&#x30;" k="70" />
<hkern u1="&#x2026;" u2="&#x178;" k="102" />
<hkern u1="&#x2026;" u2="&#x153;" k="41" />
<hkern u1="&#x2026;" u2="&#x152;" k="78" />
<hkern u1="&#x2026;" u2="&#xe7;" k="41" />
<hkern u1="&#x2026;" u2="&#xe6;" k="41" />
<hkern u1="&#x2026;" u2="&#xdd;" k="102" />
<hkern u1="&#x2026;" u2="&#xdc;" k="20" />
<hkern u1="&#x2026;" u2="&#xdb;" k="20" />
<hkern u1="&#x2026;" u2="&#xda;" k="20" />
<hkern u1="&#x2026;" u2="&#xd9;" k="20" />
<hkern u1="&#x2026;" u2="&#xd8;" k="78" />
<hkern u1="&#x2026;" u2="&#xd6;" k="78" />
<hkern u1="&#x2026;" u2="&#xd5;" k="78" />
<hkern u1="&#x2026;" u2="&#xd4;" k="78" />
<hkern u1="&#x2026;" u2="&#xd3;" k="78" />
<hkern u1="&#x2026;" u2="&#xd2;" k="78" />
<hkern u1="&#x2026;" u2="&#xc6;" k="-61" />
<hkern u1="&#x2026;" u2="&#xc5;" k="-61" />
<hkern u1="&#x2026;" u2="&#xc4;" k="-61" />
<hkern u1="&#x2026;" u2="&#xc3;" k="-61" />
<hkern u1="&#x2026;" u2="&#xc2;" k="-61" />
<hkern u1="&#x2026;" u2="&#xc1;" k="-61" />
<hkern u1="&#x2026;" u2="&#xc0;" k="-61" />
<hkern u1="&#x2026;" u2="y" k="61" />
<hkern u1="&#x2026;" u2="w" k="20" />
<hkern u1="&#x2026;" u2="v" k="61" />
<hkern u1="&#x2026;" u2="u" k="41" />
<hkern u1="&#x2026;" u2="t" k="82" />
<hkern u1="&#x2026;" u2="r" k="41" />
<hkern u1="&#x2026;" u2="q" k="41" />
<hkern u1="&#x2026;" u2="p" k="41" />
<hkern u1="&#x2026;" u2="o" k="41" />
<hkern u1="&#x2026;" u2="n" k="41" />
<hkern u1="&#x2026;" u2="m" k="41" />
<hkern u1="&#x2026;" u2="f" k="16" />
<hkern u1="&#x2026;" u2="e" k="41" />
<hkern u1="&#x2026;" u2="d" k="41" />
<hkern u1="&#x2026;" u2="c" k="41" />
<hkern u1="&#x2026;" u2="a" k="41" />
<hkern u1="&#x2026;" u2="Y" k="102" />
<hkern u1="&#x2026;" u2="W" k="45" />
<hkern u1="&#x2026;" u2="V" k="127" />
<hkern u1="&#x2026;" u2="U" k="20" />
<hkern u1="&#x2026;" u2="T" k="135" />
<hkern u1="&#x2026;" u2="Q" k="78" />
<hkern u1="&#x2026;" u2="O" k="78" />
<hkern u1="&#x2026;" u2="G" k="78" />
<hkern u1="&#x2026;" u2="C" k="78" />
<hkern u1="&#x2026;" u2="A" k="-61" />
<hkern u1="&#x2026;" u2="&#x39;" k="16" />
<hkern u1="&#x2026;" u2="&#x38;" k="31" />
<hkern u1="&#x2026;" u2="&#x37;" k="4" />
<hkern u1="&#x2026;" u2="&#x36;" k="57" />
<hkern u1="&#x2026;" u2="&#x35;" k="-8" />
<hkern u1="&#x2026;" u2="&#x34;" k="82" />
<hkern u1="&#x2026;" u2="&#x33;" k="-4" />
<hkern u1="&#x2026;" u2="&#x32;" k="-4" />
<hkern u1="&#x2026;" u2="&#x31;" k="137" />
<hkern u1="&#x2026;" u2="&#x30;" k="70" />
<hkern u1="&#x2039;" u2="&#x178;" k="82" />
<hkern u1="&#x2039;" u2="&#xdd;" k="82" />
<hkern u1="&#x2039;" u2="f" k="-8" />
<hkern u1="&#x2039;" u2="Y" k="82" />
<hkern u1="&#x2039;" u2="W" k="41" />
<hkern u1="&#x2039;" u2="V" k="41" />
<hkern u1="&#x2039;" u2="T" k="82" />
<hkern u1="&#x203a;" u2="&#x178;" k="102" />
<hkern u1="&#x203a;" u2="&#xdd;" k="102" />
<hkern u1="&#x203a;" u2="z" k="94" />
<hkern u1="&#x203a;" u2="y" k="12" />
<hkern u1="&#x203a;" u2="x" k="102" />
<hkern u1="&#x203a;" u2="w" k="8" />
<hkern u1="&#x203a;" u2="v" k="12" />
<hkern u1="&#x203a;" u2="f" k="4" />
<hkern u1="&#x203a;" u2="Y" k="102" />
<hkern u1="&#x203a;" u2="W" k="82" />
<hkern u1="&#x203a;" u2="V" k="102" />
<hkern u1="&#x203a;" u2="T" k="225" />
</font>
</defs></svg> 