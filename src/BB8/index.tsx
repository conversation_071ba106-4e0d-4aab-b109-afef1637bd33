export * from "./components/BB8Alert";
// export * from "./components/BB8AuthPanel";               // This will cause a circular import error
export * from "./components/BB8Button";
export * from "./components/BB8CustomAlert";

// BB8CustomTable and BB8Table both has IColProps, ITemplateCellProps export with different
// attributes, so it will be ignored from the export in both of them and the import will
// be directly from the desired file
import BB8CustomTable from "./components/BB8CustomTable";
export { BB8CustomTable };

export * from "./components/BB8CustomTopNav";
export * from "./components/BB8Form";

// importing formFieldStyles from this file will cause webpack issues, so it will be ignored
// in this export
import { BB8FormField, BB8InnerFormField } from "./components/BB8FormField";
export { BB8FormField, BB8InnerFormField };

// export * from "./components/BB8FormLookupField";         // Not being used outside BB8

export * from "./components/BB8Lookup";
export * from "./components/BB8MainPage";
export * from "./components/BB8ModalTypes";
export * from "./components/BB8NavBar";
export * from "./components/BB8PageSpinner";

// BB8CustomTable and BB8Table both has IColProps, ITemplateCellProps export with different
// attributes, so it will be ignored from the export in both of them and the import will
// be directly from the desired file
import BB8Table from "./components/BB8Table";
export { BB8Table };

export * from "./components/BB8Shared";
export * from "./containers/NotFound";

/*
TODO: - Change IColProps name in both BB8Table and BB8CustomTable or
            do something to fix code duplication (fix IColProps imports accordingly)
      - Change ITemplateCellProps name in both BB8Table and BB8CustomTable or
            do something to fix code duplication (fix ITemplateCellProps imports accordingly)
      - Fix code duplication in Custom components and original BB8 components
*/