.no-data-row {
  border-left: solid 4px #44a648;
  height: 64px; }
  .no-data-row .no-data-row-message {
    margin-left: 18px; }
  .no-data-row .no-data-row-icon {
    margin-left: 28px;
    height: 36px;
    width: 36px;
    border: 2px solid #44a648;
    border-radius: 50%;
    background-image: url(static/media/bb8-success.608b4967.svg);
    background-position: center;
    background-size: 24px;
    background-repeat: no-repeat; }

.user-profile {
  display: flex;
  flex-flow: row nowrap;
  align-items: center; }

.user {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  padding-right: 15px;
  background: right 0%/1px 100% no-repeat linear-gradient(#979797, #979797); }

.user > .avatar {
  height: 20px;
  width: 20px;
  border: 1px solid #979797;
  border-radius: 50%;
  margin-right: 10px; }

.actions {
  display: flex;
  flex-flow: row nowrap;
  align-items: center; }

.bb8-badge {
  border-radius: 100px;
  color: #231F20;
  font-size: 14px;
  font-weight: 500;
  line-height: 2;
  opacity: 0.9;
  width: 108px;
  margin-top: 10px;
  margin-bottom: 10px;
  text-align: center; }

.bb8-blue {
  background-color: #D1E9F5; }

.bb8-orange {
  background-color: #F2C7A8; }

.bb8-pink {
  background-color: #F6CFE8; }

.bb8-green {
  background-color: #E5EFAC; }

.bb8-basic-flex {
  display: flex;
  flex-direction: row;
  align-items: center; }

.bb8-breadcrumbs-wrapper {
  margin: 0 auto;
  padding: 0;
  height: 44px;
  display: flex;
  flex-flow: row nowrap;
  flex: 0 0 100%;
  align-items: center;
  list-style-type: none; }
  .bb8-breadcrumbs-wrapper > li a {
    color: #0a6fb3;
    text-decoration: none;
    font-weight: 500; }
  .bb8-breadcrumbs-wrapper > li:not(:first-of-type) {
    position: relative;
    margin-left: 24px; }
    .bb8-breadcrumbs-wrapper > li:not(:first-of-type):before {
      content: "";
      display: block;
      position: absolute;
      left: -24px;
      width: 24px;
      height: 24px;
      background-image: url(static/media/bb8-expand_more-24px.78fbcb86.svg);
      background-position: center;
      background-size: 24px;
      background-repeat: no-repeat;
      -webkit-transform: rotate(-90deg);
              transform: rotate(-90deg);
      -webkit-transform-origin: center;
              transform-origin: center; }

.main-page {
  width: 100%;
  margin: 0 auto; }
  .main-page.padded {
    width: 1366px; }
    @media screen and (max-width: 1365px) {
      .main-page.padded {
        width: 96vw; } }

@font-face {
  font-family: 'Omnes';
  font-weight: normal;
  src: local("Omnes"), url(static/media/omnes-regular-webfont.bf6eaba9.woff2) format("woff2"), url(static/media/omnes-regular-webfont.99d79548.ttf) format("truetype"); }

@font-face {
  font-family: 'Omnes';
  font-style: normal;
  font-weight: 300;
  src: local("Omnes"), url(static/media/omneslight-webfont.5fbca3f8.woff2) format("woff2"), url(static/media/omneslight-webfont.e186e9dc.ttf) format("truetype"); }

@font-face {
  font-family: 'Omnes';
  font-style: normal;
  font-weight: 500;
  src: local("Omnes"), url(static/media/omnesmedium-webfont.84de2660.woff2) format("woff2"), url(static/media/omnesmedium-webfont.dc13a34f.ttf) format("truetype"); }

html,
body {
  font-family: 'Omnes', sans-serif;
  font-size: 16px; }

.BB8Carousel {
  width: 100%;
  margin-left: 0;
  padding-right: 1em;
  padding-left: 1em; }
  .BB8Carousel > button::before {
    color: gray; }
  .BB8Carousel .slick-prev {
    left: 0; }
    .BB8Carousel .slick-prev img {
      -webkit-transform: rotateY(180deg);
              transform: rotateY(180deg); }
  .BB8Carousel .slick-next {
    right: -8px; }
  .BB8Carousel .slick-prev,
  .BB8Carousel .slick-next {
    display: "block";
    height: "48px";
    width: "14px"; }
    .BB8Carousel .slick-prev:before,
    .BB8Carousel .slick-next:before {
      content: none; }

.carousel-container h3 {
  margin-bottom: 0 !important; }

.BB8Carousel-item .card-inner-wrapper {
  background: white;
  border: 2px solid #2684fb;
  border-radius: 8px !important; }

.BB8Carousel * {
  cursor: pointer; }
  .BB8Carousel * :focus {
    outline: none; }

.BB8Carousel .BB8Carousel-item {
  position: relative; }
  .BB8Carousel .BB8Carousel-item .card-inner-wrapper {
    border-color: transparent;
    border-radius: 4px;
    background-color: #ffffff;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
    padding: 0.5em;
    margin: 0.5rem;
    margin-top: 1rem;
    margin-bottom: 1rem;
    min-height: 12em;
    display: flex;
    flex-direction: column; }
    .BB8Carousel .BB8Carousel-item .card-inner-wrapper > h5 {
      margin: 0;
      color: black;
      font-size: 24px;
      margin-top: 15px; }
    .BB8Carousel .BB8Carousel-item .card-inner-wrapper > p {
      margin-bottom: 15px; }
    .BB8Carousel .BB8Carousel-item .card-inner-wrapper .description {
      flex-grow: 1; }
      .BB8Carousel .BB8Carousel-item .card-inner-wrapper .description p {
        font-size: 19px;
        line-height: 26px;
        margin: 0; }
  .BB8Carousel .BB8Carousel-item.selected-carousel-item .card-inner-wrapper {
    background-color: #ebf3f8;
    border: 4px solid #61c1ee; }
  .BB8Carousel .BB8Carousel-item.selected-carousel-item:before {
    content: "";
    background-color: inherit;
    background-color: #ebf3f8;
    border-right: 4px solid #61c1ee;
    border-bottom: 4px solid #61c1ee;
    height: 19px;
    width: 19px;
    position: absolute;
    bottom: -8px;
    left: calc(50% - 9.5px);
    -webkit-transform-origin: center;
            transform-origin: center;
    -webkit-transform: rotate(45deg);
            transform: rotate(45deg); }
  .BB8Carousel .BB8Carousel-item.subItems-wrapper {
    display: flex; }
    .BB8Carousel .BB8Carousel-item.subItems-wrapper .subItem {
      margin-right: 0.5em;
      border: 1px solid #999;
      padding: 3px;
      border-radius: 7px; }

.BB8Carousel .edit-icon {
  position: absolute;
  right: 20px;
  cursor: pointer;
  z-index: 100;
  height: 26px;
  min-height: 26px;
  padding: 0;
  border-radius: 4px; }

.date-range-picker-container {
  display: inline-block;
  position: relative; }
  .date-range-picker-container .date-range-picker-wrapper {
    position: absolute;
    right: 0;
    margin-top: .5em;
    z-index: 1000;
    background: #fff;
    border: 1px solid #979797;
    box-shadow: 1px 4px 2px 0 #979797;
    display: flex;
    flex-flow: row wrap;
    width: 622px; }
    .date-range-picker-container .date-range-picker-wrapper .date-range-picker-inputs {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 1em; }
      .date-range-picker-container .date-range-picker-wrapper .date-range-picker-inputs span {
        color: #d5dbdf;
        flex-grow: 0;
        height: 2.5em;
        border-radius: 3px;
        border: 1px solid #d5dbdf;
        margin: 0 .5em;
        width: 6.5em;
        padding: 0.5em; }
    .date-range-picker-container .date-range-picker-wrapper .date-range-picker-content {
      min-height: 330px; }
      .date-range-picker-container .date-range-picker-wrapper .date-range-picker-content .DayPicker__hidden {
        visibility: visible; }
      .date-range-picker-container .date-range-picker-wrapper .date-range-picker-content .CalendarDay__selected_span {
        background: #d1e9f5;
        color: black;
        border: 1px solid transparent; }
      .date-range-picker-container .date-range-picker-wrapper .date-range-picker-content .CalendarDay__selected {
        background: #0a6fb3;
        color: #fff; }
      .date-range-picker-container .date-range-picker-wrapper .date-range-picker-content .CalendarDay__selected:hover {
        background: #61c1ee;
        color: #fff; }
      .date-range-picker-container .date-range-picker-wrapper .date-range-picker-content .CalendarDay__hovered_span:hover,
      .date-range-picker-container .date-range-picker-wrapper .date-range-picker-content .CalendarDay__hovered_span {
        background: #1790cc; }
    .date-range-picker-container .date-range-picker-wrapper .date-range-picker-footer {
      padding: 1em;
      width: 100%;
      display: flex;
      justify-content: flex-end; }
      .date-range-picker-container .date-range-picker-wrapper .date-range-picker-footer button {
        margin-left: 1em; }

.bb8-basic-flex {
  display: flex;
  flex-direction: row;
  align-items: center; }

.bb8-search-wrapper {
  position: relative;
  min-height: 3em;
  width: auto;
  display: flex;
  flex-flow: row nowrap;
  flex: 0 0 auto; }
  .bb8-search-wrapper .bb8-search {
    background: #f1f2f2;
    color: rgba(94, 94, 94, 0.5);
    flex: 1 1 100%;
    font-family: "Omnes";
    font-size: 16px;
    border-radius: 3px;
    border: 0;
    padding-left: 1em;
    padding-right: 1em;
    height: 100%;
    width: 100%; }
    .bb8-search-wrapper .bb8-search:focus {
      outline: none;
      box-shadow: 0 0 1pt 1pt #fff; }
    .bb8-search-wrapper .bb8-search.dirty.invalid {
      background: #faa; }
    .bb8-search-wrapper .bb8-search.valid {
      background: #f1f2f2; }
  .bb8-search-wrapper .bb8-search-icon {
    position: absolute;
    right: 0rem;
    visibility: hidden;
    pointer-events: none; }
    .bb8-search-wrapper .bb8-search-icon.show {
      visibility: visible;
      pointer-events: all; }
  .bb8-search-wrapper .bb8-offer-modal-style {
    background: none !important;
    font: inherit !important;
    color: rgba(0, 0, 0, 0.54) !important;
    width: 220px !important;
    border: 1px solid gray !important;
    margin-top: 21px !important;
    padding: 27px 12px !important; }
  .bb8-search-wrapper .bb8-customer-modal-style {
    background: none !important;
    font: inherit !important;
    color: rgba(0, 0, 0, 0.54) !important;
    width: 450px !important;
    border: 1px solid gray !important;
    margin-top: 21px !important;
    padding: 27px 12px !important; }

.margin-20-top {
  margin-top: 20px !important; }

.rem-1-font {
  font-size: 1rem !important; }

.bb8-link {
  margin-bottom: -11px;
  width: 30px; }

.modal-content {
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%); }
  .modal-content.default {
    width: 750px; }
  .modal-content.small {
    width: 400px; }
  .modal-content.medium {
    width: 550px; }
  .modal-content .btn-close-modal {
    float: right; }

.bb8-basic-flex, .bb8-nav-bar, .bb8-nav-bar .bb8-title-wapper {
  display: flex;
  flex-direction: row;
  align-items: center; }

.bb8-nav-bar-wrapper {
  margin: 0 auto;
  background-image: url(static/media/bb8-nav-bar-bg.acfe3ed7.jpg);
  background-size: contain;
  height: 78px;
  box-shadow: 0px 3px 2px 0px #dddddd;
  margin-bottom: 4px;
  width: 100%;
  display: flex;
  justify-content: center; }
  .bb8-nav-bar-wrapper h1 {
    color: #ffffff;
    font-size: 40px; }

.bb8-nav-bar {
  font-family: "Omnes", Calibri, "Trebuchet MS";
  width: 1366px; }
  @media screen and (max-width: 1365px) {
    .bb8-nav-bar {
      width: 100%; } }
  .bb8-nav-bar * {
    flex-grow: 1; }
  .bb8-nav-bar .bb8-title-img-wrapper {
    flex-grow: 0; }
    .bb8-nav-bar .bb8-title-img-wrapper img {
      height: auto;
      width: 48px; }
  .bb8-nav-bar .bb8-title-wapper * {
    margin-right: 1em; }
  .bb8-nav-bar .bb8-title-wapper h1 {
    font-weight: lighter; }

#spinner-root.shown {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.2);
  z-index: 9000; }

.bb8-step {
  border-radius: 20px;
  margin-top: -5px;
  padding: 4px;
  padding-bottom: 0px; }
  .bb8-step.rejected {
    border: 2px solid red; }
  .bb8-step.hold {
    border: 2px solid orange; }
  .bb8-step.processing {
    border: 2px solid #2e6fae; }
  .bb8-step.completed {
    border: 2px solid green; }
  .bb8-step.idle {
    border: 2px solid gray;
    padding: 14px; }

.bb8-stepper {
  font-family: Omnes; }
  .bb8-stepper.title {
    line-height: 16px;
    font-weight: bold; }
  .bb8-stepper.last-update {
    color: #9B9B9B;
    font-family: Arial;
    font-size: 10px;
    font-style: italic;
    line-height: 11px; }

.bb8-basic-flex {
  display: flex;
  flex-direction: row;
  align-items: center; }

.bb8-search-wrapper {
  position: relative;
  height: 3em;
  width: auto;
  display: flex;
  flex-flow: row nowrap;
  flex: 0 0 auto; }
  .bb8-search-wrapper .bb8-search {
    background: #f1f2f2;
    color: rgba(94, 94, 94, 0.5);
    flex: 1 1 100%;
    font-family: "Omnes";
    font-size: 16px;
    border-radius: 3px;
    border: 0;
    padding-left: 1em;
    padding-right: 1em;
    height: 100%;
    width: 100%; }
    .bb8-search-wrapper .bb8-search:focus {
      outline: none;
      box-shadow: 0 0 1pt 1pt #fff; }
    .bb8-search-wrapper .bb8-search.dirty.invalid {
      background: #faa; }
    .bb8-search-wrapper .bb8-search.valid {
      background: #f1f2f2; }
  .bb8-search-wrapper .bb8-search-icon {
    position: absolute;
    right: 0rem; }

.bb8-basic-flex {
  display: flex;
  flex-direction: row;
  align-items: center; }

.navbar-links-wrapper {
  width: 100%;
  height: 45px;
  background-color: #ffffff; }
  .navbar-links-wrapper .navbar-links {
    width: 1366px;
    height: 100%;
    position: relative;
    display: flex;
    flex-flow: row nowrap;
    flex: 1 1;
    align-items: center;
    margin: 0 auto; }
    @media screen and (max-width: 1365px) {
      .navbar-links-wrapper .navbar-links {
        width: 100%; } }
    .navbar-links-wrapper .navbar-links > nav {
      display: inherit;
      height: 100%; }
      .navbar-links-wrapper .navbar-links > nav > a {
        display: block;
        height: 100%;
        line-height: 45px;
        color: #7c7e80;
        font-family: Omnes;
        font-size: 14px;
        font-weight: 500;
        letter-spacing: 0.25px;
        text-decoration: none;
        text-transform: uppercase;
        padding-left: 10px;
        padding-right: 10px; }
        .navbar-links-wrapper .navbar-links > nav > a:first-child {
          margin-right: 44px; }
        .navbar-links-wrapper .navbar-links > nav > a.active {
          color: #0a6fb3;
          background: top left/100% 4px no-repeat linear-gradient(#0a6fb3, #0a6fb3); }
    .navbar-links-wrapper .navbar-links > .user-profile {
      position: absolute;
      right: 0; }
  .navbar-links-wrapper .navbar-sub-links {
    width: 1366px;
    height: 100%;
    position: relative;
    display: flex;
    flex-flow: row nowrap;
    flex: 1 1;
    align-items: center;
    margin: 0 auto;
    background-color: #3a84b2; }
    @media screen and (max-width: 1365px) {
      .navbar-links-wrapper .navbar-sub-links {
        width: 100%; } }
    .navbar-links-wrapper .navbar-sub-links > nav {
      display: inherit;
      height: 100%; }
      .navbar-links-wrapper .navbar-sub-links > nav > a {
        display: block;
        height: 100%;
        line-height: 45px;
        font-family: Omnes;
        font-size: 14px;
        font-weight: 500;
        letter-spacing: 0.25px;
        text-decoration: none;
        text-transform: uppercase;
        color: white;
        padding-left: 10px;
        padding-right: 10px; }
        .navbar-links-wrapper .navbar-sub-links > nav > a:first-child {
          margin-right: 44px; }
        .navbar-links-wrapper .navbar-sub-links > nav > a.active {
          background-color: #317099; }
  .navbar-links-wrapper .btn-login {
    margin-left: auto; }

.sub-header {
  background-color: #3a84b2; }

.bb8-basic-flex {
  display: flex;
  flex-direction: row;
  align-items: center; }

main.page {
  align-items: center;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  display: flex;
  flex-flow: column wrap;
  height: calc(100% - 180px);
  justify-content: center;
  width: 100%; }
  main.page > * {
    color: #fff; }
  main.page h1 {
    font-size: 150px;
    margin-bottom: 2rem; }
  main.page h2 {
    font-size: 64px; }
  main.page .link {
    background-color: #0a6fb3;
    padding: 1rem 3rem;
    text-decoration: none; }


/*# sourceMappingURL=iframe.css.map*/