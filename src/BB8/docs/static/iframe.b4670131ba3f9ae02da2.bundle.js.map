{"version": 3, "file": "static/iframe.b4670131ba3f9ae02da2.bundle.js", "sources": ["webpack:///./stories/index.js"], "sourcesContent": ["import React from 'react'\n\nimport { storiesOf } from '@storybook/react'\nimport { action } from '@storybook/addon-actions'\nimport { linkTo } from '@storybook/addon-links'\n\nimport { Welcome } from '@storybook/react/demo'\nimport { BB8Button, BB8Alert, BB8AlertTable, mainTheme } from '../lib/index'\nimport BB8Lookup from '../lib'\nimport { withKnobs, text, boolean, number, select } from '@storybook/addon-knobs'\nimport { MuiThemeProvider, MenuItem } from '@material-ui/core'\n\nstoriesOf('Welcome', module).add('to Storybook', () => <Welcome showApp={linkTo('Button')} />)\nconst buttonVariantsLabel = 'Variants'\nconst buttonVariantOptions = ['contained', 'text', 'flat', 'outlined', 'raised', 'fab']\nconst buttonVariantDefaultValue = 'contained'\nconst buttonColorLabel = 'Colors'\nconst buttonColorOptions = ['default', 'primary', 'secondary']\nconst buttonColorDefaultValue = 'contained'\nconst groupId = 'BB8ButtonGroup'\n\nstoriesOf('Components/BB8Button', module)\n  .addDecorator(withKnobs)\n  .add('Variants', () => (\n    <BB8Button\n      onClick={action('clicked')}\n      color={select(buttonColorLabel, buttonColorOptions, buttonColorDefaultValue, groupId)}\n      variant={select(\n        buttonVariantsLabel,\n        buttonVariantOptions,\n        buttonVariantDefaultValue,\n        groupId\n      )}\n    >\n      {text('Label', 'Hello BB8Button', groupId)}\n    </BB8Button>\n  ))\n\nstoriesOf('Components/BB8Alert', module)\n  .addDecorator(withKnobs)\n  .add('Variants', () => (\n    <MuiThemeProvider theme={mainTheme}>\n      <BB8Alert show={true} message={text('Label', 'Hello BB8Button', groupId)} />\n    </MuiThemeProvider>\n  ))\n\nstoriesOf('Components/BB8AlertTable', module)\n  .addDecorator(withKnobs)\n  .add('Variants', () => (\n    <MuiThemeProvider theme={mainTheme}>\n      <BB8AlertTable cols={[{ id: 'title', title: 'Error' }]} data={[{ title: 'cookie' }]} />\n    </MuiThemeProvider>\n  ))\n\nfunction renderCustomerLookupOption({ classes, highlightedIndex, index, itemProps, option }) {\n  const isHighlighted = highlightedIndex === index\n  debugger\n  return (\n    <MenuItem\n      {...itemProps}\n      key={`${option.customerId}`}\n      className={classes.menuItem}\n      selected={isHighlighted}\n    >\n      {option.customerName}\n    </MenuItem>\n  )\n}\n\nstoriesOf('Components/BB8Lookup', module)\n  .addDecorator(withKnobs)\n  .add('Variants', () => (\n    <MuiThemeProvider theme={mainTheme}>\n      <BB8Lookup\n        id=\"search-customers-issuers\"\n        placeholder=\"Search customers or issuers\"\n        onInputChange={action('onInputChange')}\n        onSelect={action('onSelect')}\n        options={[\n          { customerId: 1, customerName: 'LCBO' },\n          { customerId: 2, customerName: 'Wine Rack' }\n        ]}\n        renderOption={renderCustomerLookupOption}\n        loading={false}\n        itemToString={''}\n      />\n    </MuiThemeProvider>\n  ))\n"], "mappings": "AAYA", "sourceRoot": ""}