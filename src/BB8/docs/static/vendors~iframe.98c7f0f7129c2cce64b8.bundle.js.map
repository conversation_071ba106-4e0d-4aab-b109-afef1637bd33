{"version": 3, "file": "static/vendors~iframe.98c7f0f7129c2cce64b8.bundle.js", "sources": ["webpack:///./node_modules/react/index.js", "webpack:///./node_modules/classnames/index.js", "webpack:///./node_modules/keycode/index.js", "webpack:///./node_modules/object-assign/index.js", "webpack:///./node_modules/es5-shim/es5-shim.js", "webpack:///./node_modules/es5-shim/es5-sham.js", "webpack:///./node_modules/es6-shim/es6-shim.js", "webpack:///./node_modules/react/cjs/react.production.min.js", "webpack:///./node_modules/react-dom/cjs/react-dom.production.min.js", "webpack:///./node_modules/scheduler/cjs/scheduler.production.min.js", "webpack:///./node_modules/tslib/tslib.es6.js", "webpack:///./node_modules/is-plain-object/index.js", "webpack:///./node_modules/isobject/index.js", "webpack:///./node_modules/css-vendor/lib/index.js", "webpack:///./node_modules/@material-ui/core/node_modules/hoist-non-react-statics/node_modules/react-is/cjs/react-is.production.min.js", "webpack:///./node_modules/popper.js/dist/esm/popper.js", "webpack:///./node_modules/react-is/cjs/react-is.production.min.js", "webpack:///./node_modules/escape-html/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react.production.min.js');\n} else {\n  module.exports = require('./cjs/react.development.js');\n}\n", "/*!\n  Copyright (c) 2017 <PERSON>.\n  Licensed under the MIT License (MIT), see\n  http://jedwatson.github.io/classnames\n*/\n/* global define */\n\n(function () {\n\t'use strict';\n\n\tvar hasOwn = {}.hasOwnProperty;\n\n\tfunction classNames () {\n\t\tvar classes = [];\n\n\t\tfor (var i = 0; i < arguments.length; i++) {\n\t\t\tvar arg = arguments[i];\n\t\t\tif (!arg) continue;\n\n\t\t\tvar argType = typeof arg;\n\n\t\t\tif (argType === 'string' || argType === 'number') {\n\t\t\t\tclasses.push(arg);\n\t\t\t} else if (Array.isArray(arg) && arg.length) {\n\t\t\t\tvar inner = classNames.apply(null, arg);\n\t\t\t\tif (inner) {\n\t\t\t\t\tclasses.push(inner);\n\t\t\t\t}\n\t\t\t} else if (argType === 'object') {\n\t\t\t\tfor (var key in arg) {\n\t\t\t\t\tif (hasOwn.call(arg, key) && arg[key]) {\n\t\t\t\t\t\tclasses.push(key);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn classes.join(' ');\n\t}\n\n\tif (typeof module !== 'undefined' && module.exports) {\n\t\tclassNames.default = classNames;\n\t\tmodule.exports = classNames;\n\t} else if (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\n\t\t// register as 'classnames', consistent with npm package name\n\t\tdefine('classnames', [], function () {\n\t\t\treturn classNames;\n\t\t});\n\t} else {\n\t\twindow.classNames = classNames;\n\t}\n}());\n", "// Source: http://jsfiddle.net/vWx8V/\n// http://stackoverflow.com/questions/5603195/full-list-of-javascript-keycodes\n\n/**\n * Conenience method returns corresponding value for given keyName or keyCode.\n *\n * @param {Mixed} keyCode {Number} or keyName {String}\n * @return {Mixed}\n * @api public\n */\n\nfunction keyCode(searchInput) {\n  // Keyboard Events\n  if (searchInput && 'object' === typeof searchInput) {\n    var hasKeyCode = searchInput.which || searchInput.keyCode || searchInput.charCode\n    if (hasKeyCode) searchInput = hasKeyCode\n  }\n\n  // Numbers\n  if ('number' === typeof searchInput) return names[searchInput]\n\n  // Everything else (cast to string)\n  var search = String(searchInput)\n\n  // check codes\n  var foundNamedKey = codes[search.toLowerCase()]\n  if (foundNamedKey) return foundNamedKey\n\n  // check aliases\n  var foundNamedKey = aliases[search.toLowerCase()]\n  if (foundNamed<PERSON><PERSON>) return foundNamed<PERSON>ey\n\n  // weird character?\n  if (search.length === 1) return search.charCodeAt(0)\n\n  return undefined\n}\n\n/**\n * Compares a keyboard event with a given keyCode or keyName.\n *\n * @param {Event} event Keyboard event that should be tested\n * @param {Mixed} keyCode {Number} or keyName {String}\n * @return {Boolean}\n * @api public\n */\nkeyCode.isEventKey = function isEventKey(event, nameOrCode) {\n  if (event && 'object' === typeof event) {\n    var keyCode = event.which || event.keyCode || event.charCode\n    if (keyCode === null || keyCode === undefined) { return false; }\n    if (typeof nameOrCode === 'string') {\n      // check codes\n      var foundNamedKey = codes[nameOrCode.toLowerCase()]\n      if (foundNamedKey) { return foundNamedKey === keyCode; }\n    \n      // check aliases\n      var foundNamedKey = aliases[nameOrCode.toLowerCase()]\n      if (foundNamedKey) { return foundNamedKey === keyCode; }\n    } else if (typeof nameOrCode === 'number') {\n      return nameOrCode === keyCode;\n    }\n    return false;\n  }\n}\n\nexports = module.exports = keyCode;\n\n/**\n * Get by name\n *\n *   exports.code['enter'] // => 13\n */\n\nvar codes = exports.code = exports.codes = {\n  'backspace': 8,\n  'tab': 9,\n  'enter': 13,\n  'shift': 16,\n  'ctrl': 17,\n  'alt': 18,\n  'pause/break': 19,\n  'caps lock': 20,\n  'esc': 27,\n  'space': 32,\n  'page up': 33,\n  'page down': 34,\n  'end': 35,\n  'home': 36,\n  'left': 37,\n  'up': 38,\n  'right': 39,\n  'down': 40,\n  'insert': 45,\n  'delete': 46,\n  'command': 91,\n  'left command': 91,\n  'right command': 93,\n  'numpad *': 106,\n  'numpad +': 107,\n  'numpad -': 109,\n  'numpad .': 110,\n  'numpad /': 111,\n  'num lock': 144,\n  'scroll lock': 145,\n  'my computer': 182,\n  'my calculator': 183,\n  ';': 186,\n  '=': 187,\n  ',': 188,\n  '-': 189,\n  '.': 190,\n  '/': 191,\n  '`': 192,\n  '[': 219,\n  '\\\\': 220,\n  ']': 221,\n  \"'\": 222\n}\n\n// Helper aliases\n\nvar aliases = exports.aliases = {\n  'windows': 91,\n  '⇧': 16,\n  '⌥': 18,\n  '⌃': 17,\n  '⌘': 91,\n  'ctl': 17,\n  'control': 17,\n  'option': 18,\n  'pause': 19,\n  'break': 19,\n  'caps': 20,\n  'return': 13,\n  'escape': 27,\n  'spc': 32,\n  'spacebar': 32,\n  'pgup': 33,\n  'pgdn': 34,\n  'ins': 45,\n  'del': 46,\n  'cmd': 91\n}\n\n/*!\n * Programatically add the following\n */\n\n// lower case chars\nfor (i = 97; i < 123; i++) codes[String.fromCharCode(i)] = i - 32\n\n// numbers\nfor (var i = 48; i < 58; i++) codes[i - 48] = i\n\n// function keys\nfor (i = 1; i < 13; i++) codes['f'+i] = i + 111\n\n// numpad keys\nfor (i = 0; i < 10; i++) codes['numpad '+i] = i + 96\n\n/**\n * Get by code\n *\n *   exports.name[13] // => 'Enter'\n */\n\nvar names = exports.names = exports.title = {} // title for backward compat\n\n// Create reverse mapping\nfor (i in codes) names[codes[i]] = i\n\n// Add aliases\nfor (var alias in aliases) {\n  codes[alias] = aliases[alias]\n}\n", "/*\nobject-assign\n(c) <PERSON><PERSON> Sorhus\n@license MIT\n*/\n\n'use strict';\n/* eslint-disable no-unused-vars */\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n\nfunction toObject(val) {\n\tif (val === null || val === undefined) {\n\t\tthrow new TypeError('Object.assign cannot be called with null or undefined');\n\t}\n\n\treturn Object(val);\n}\n\nfunction shouldUseNative() {\n\ttry {\n\t\tif (!Object.assign) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Detect buggy property enumeration order in older V8 versions.\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=4118\n\t\tvar test1 = new String('abc');  // eslint-disable-line no-new-wrappers\n\t\ttest1[5] = 'de';\n\t\tif (Object.getOwnPropertyNames(test1)[0] === '5') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test2 = {};\n\t\tfor (var i = 0; i < 10; i++) {\n\t\t\ttest2['_' + String.fromCharCode(i)] = i;\n\t\t}\n\t\tvar order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n\t\t\treturn test2[n];\n\t\t});\n\t\tif (order2.join('') !== '0123456789') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test3 = {};\n\t\t'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n\t\t\ttest3[letter] = letter;\n\t\t});\n\t\tif (Object.keys(Object.assign({}, test3)).join('') !==\n\t\t\t\t'abcdefghijklmnopqrst') {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t} catch (err) {\n\t\t// We don't expect any of the above to throw, but better to be safe.\n\t\treturn false;\n\t}\n}\n\nmodule.exports = shouldUseNative() ? Object.assign : function (target, source) {\n\tvar from;\n\tvar to = toObject(target);\n\tvar symbols;\n\n\tfor (var s = 1; s < arguments.length; s++) {\n\t\tfrom = Object(arguments[s]);\n\n\t\tfor (var key in from) {\n\t\t\tif (hasOwnProperty.call(from, key)) {\n\t\t\t\tto[key] = from[key];\n\t\t\t}\n\t\t}\n\n\t\tif (getOwnPropertySymbols) {\n\t\t\tsymbols = getOwnPropertySymbols(from);\n\t\t\tfor (var i = 0; i < symbols.length; i++) {\n\t\t\t\tif (propIsEnumerable.call(from, symbols[i])) {\n\t\t\t\t\tto[symbols[i]] = from[symbols[i]];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn to;\n};\n", "/*!\n * https://github.com/es-shims/es5-shim\n * @license es5-shim Copyright 2009-2015 by contributors, MIT License\n * see https://github.com/es-shims/es5-shim/blob/master/LICENSE\n */\n\n// vim: ts=4 sts=4 sw=4 expandtab\n\n// Add semicolon to prevent IIFE from being passed as argument to concatenated code.\n;\n\n// UMD (Universal Module Definition)\n// see https://github.com/umdjs/umd/blob/master/templates/returnExports.js\n(function (root, factory) {\n    'use strict';\n\n    /* global define, exports, module */\n    if (typeof define === 'function' && define.amd) {\n        // AMD. Register as an anonymous module.\n        define(factory);\n    } else if (typeof exports === 'object') {\n        // Node. Does not work with strict CommonJS, but\n        // only CommonJS-like enviroments that support module.exports,\n        // like Node.\n        module.exports = factory();\n    } else {\n        // Browser globals (root is window)\n        root.returnExports = factory();\n    }\n}(this, function () {\n    /**\n     * Brings an environment as close to ECMAScript 5 compliance\n     * as is possible with the facilities of erstwhile engines.\n     *\n     * Annotated ES5: http://es5.github.com/ (specific links below)\n     * ES5 Spec: http://www.ecma-international.org/publications/files/ECMA-ST/Ecma-262.pdf\n     * Required reading: http://javascriptweblog.wordpress.com/2011/12/05/extending-javascript-natives/\n     */\n\n    // Shortcut to an often accessed properties, in order to avoid multiple\n    // dereference that costs universally. This also holds a reference to known-good\n    // functions.\n    var $Array = Array;\n    var ArrayPrototype = $Array.prototype;\n    var $Object = Object;\n    var ObjectPrototype = $Object.prototype;\n    var $Function = Function;\n    var FunctionPrototype = $Function.prototype;\n    var $String = String;\n    var StringPrototype = $String.prototype;\n    var $Number = Number;\n    var NumberPrototype = $Number.prototype;\n    var array_slice = ArrayPrototype.slice;\n    var array_splice = ArrayPrototype.splice;\n    var array_push = ArrayPrototype.push;\n    var array_unshift = ArrayPrototype.unshift;\n    var array_concat = ArrayPrototype.concat;\n    var array_join = ArrayPrototype.join;\n    var call = FunctionPrototype.call;\n    var apply = FunctionPrototype.apply;\n    var max = Math.max;\n    var min = Math.min;\n\n    // Having a toString local variable name breaks in Opera so use to_string.\n    var to_string = ObjectPrototype.toString;\n\n    /* global Symbol */\n    /* eslint-disable one-var-declaration-per-line, no-redeclare, max-statements-per-line */\n    var hasToStringTag = typeof Symbol === 'function' && typeof Symbol.toStringTag === 'symbol';\n    var isCallable; /* inlined from https://npmjs.com/is-callable */ var fnToStr = Function.prototype.toString, constructorRegex = /^\\s*class /, isES6ClassFn = function isES6ClassFn(value) { try { var fnStr = fnToStr.call(value); var singleStripped = fnStr.replace(/\\/\\/.*\\n/g, ''); var multiStripped = singleStripped.replace(/\\/\\*[.\\s\\S]*\\*\\//g, ''); var spaceStripped = multiStripped.replace(/\\n/mg, ' ').replace(/ {2}/g, ' '); return constructorRegex.test(spaceStripped); } catch (e) { return false; /* not a function */ } }, tryFunctionObject = function tryFunctionObject(value) { try { if (isES6ClassFn(value)) { return false; } fnToStr.call(value); return true; } catch (e) { return false; } }, fnClass = '[object Function]', genClass = '[object GeneratorFunction]', isCallable = function isCallable(value) { if (!value) { return false; } if (typeof value !== 'function' && typeof value !== 'object') { return false; } if (hasToStringTag) { return tryFunctionObject(value); } if (isES6ClassFn(value)) { return false; } var strClass = to_string.call(value); return strClass === fnClass || strClass === genClass; };\n\n    var isRegex; /* inlined from https://npmjs.com/is-regex */ var regexExec = RegExp.prototype.exec, tryRegexExec = function tryRegexExec(value) { try { regexExec.call(value); return true; } catch (e) { return false; } }, regexClass = '[object RegExp]'; isRegex = function isRegex(value) { if (typeof value !== 'object') { return false; } return hasToStringTag ? tryRegexExec(value) : to_string.call(value) === regexClass; };\n    var isString; /* inlined from https://npmjs.com/is-string */ var strValue = String.prototype.valueOf, tryStringObject = function tryStringObject(value) { try { strValue.call(value); return true; } catch (e) { return false; } }, stringClass = '[object String]'; isString = function isString(value) { if (typeof value === 'string') { return true; } if (typeof value !== 'object') { return false; } return hasToStringTag ? tryStringObject(value) : to_string.call(value) === stringClass; };\n    /* eslint-enable one-var-declaration-per-line, no-redeclare, max-statements-per-line */\n\n    /* inlined from http://npmjs.com/define-properties */\n    var supportsDescriptors = $Object.defineProperty && (function () {\n        try {\n            var obj = {};\n            $Object.defineProperty(obj, 'x', { enumerable: false, value: obj });\n            for (var _ in obj) { // jscs:ignore disallowUnusedVariables\n                return false;\n            }\n            return obj.x === obj;\n        } catch (e) { /* this is ES3 */\n            return false;\n        }\n    }());\n    var defineProperties = (function (has) {\n        // Define configurable, writable, and non-enumerable props\n        // if they don't exist.\n        var defineProperty;\n        if (supportsDescriptors) {\n            defineProperty = function (object, name, method, forceAssign) {\n                if (!forceAssign && (name in object)) {\n                    return;\n                }\n                $Object.defineProperty(object, name, {\n                    configurable: true,\n                    enumerable: false,\n                    writable: true,\n                    value: method\n                });\n            };\n        } else {\n            defineProperty = function (object, name, method, forceAssign) {\n                if (!forceAssign && (name in object)) {\n                    return;\n                }\n                object[name] = method;\n            };\n        }\n        return function defineProperties(object, map, forceAssign) {\n            for (var name in map) {\n                if (has.call(map, name)) {\n                    defineProperty(object, name, map[name], forceAssign);\n                }\n            }\n        };\n    }(ObjectPrototype.hasOwnProperty));\n\n    //\n    // Util\n    // ======\n    //\n\n    /* replaceable with https://npmjs.com/package/es-abstract /helpers/isPrimitive */\n    var isPrimitive = function isPrimitive(input) {\n        var type = typeof input;\n        return input === null || (type !== 'object' && type !== 'function');\n    };\n\n    var isActualNaN = $Number.isNaN || function isActualNaN(x) {\n        return x !== x;\n    };\n\n    var ES = {\n        // ES5 9.4\n        // http://es5.github.com/#x9.4\n        // http://jsperf.com/to-integer\n        /* replaceable with https://npmjs.com/package/es-abstract ES5.ToInteger */\n        ToInteger: function ToInteger(num) {\n            var n = +num;\n            if (isActualNaN(n)) {\n                n = 0;\n            } else if (n !== 0 && n !== (1 / 0) && n !== -(1 / 0)) {\n                n = (n > 0 || -1) * Math.floor(Math.abs(n));\n            }\n            return n;\n        },\n\n        /* replaceable with https://npmjs.com/package/es-abstract ES5.ToPrimitive */\n        ToPrimitive: function ToPrimitive(input) {\n            var val, valueOf, toStr;\n            if (isPrimitive(input)) {\n                return input;\n            }\n            valueOf = input.valueOf;\n            if (isCallable(valueOf)) {\n                val = valueOf.call(input);\n                if (isPrimitive(val)) {\n                    return val;\n                }\n            }\n            toStr = input.toString;\n            if (isCallable(toStr)) {\n                val = toStr.call(input);\n                if (isPrimitive(val)) {\n                    return val;\n                }\n            }\n            throw new TypeError();\n        },\n\n        // ES5 9.9\n        // http://es5.github.com/#x9.9\n        /* replaceable with https://npmjs.com/package/es-abstract ES5.ToObject */\n        ToObject: function (o) {\n            if (o == null) { // this matches both null and undefined\n                throw new TypeError(\"can't convert \" + o + ' to object');\n            }\n            return $Object(o);\n        },\n\n        /* replaceable with https://npmjs.com/package/es-abstract ES5.ToUint32 */\n        ToUint32: function ToUint32(x) {\n            return x >>> 0;\n        }\n    };\n\n    //\n    // Function\n    // ========\n    //\n\n    // ES-5 ********\n    // http://es5.github.com/#x********\n\n    var Empty = function Empty() {};\n\n    defineProperties(FunctionPrototype, {\n        bind: function bind(that) { // .length is 1\n            // 1. Let Target be the this value.\n            var target = this;\n            // 2. If IsCallable(Target) is false, throw a TypeError exception.\n            if (!isCallable(target)) {\n                throw new TypeError('Function.prototype.bind called on incompatible ' + target);\n            }\n            // 3. Let A be a new (possibly empty) internal list of all of the\n            //   argument values provided after thisArg (arg1, arg2 etc), in order.\n            // XXX slicedArgs will stand in for \"A\" if used\n            var args = array_slice.call(arguments, 1); // for normal call\n            // 4. Let F be a new native ECMAScript object.\n            // 11. Set the [[Prototype]] internal property of F to the standard\n            //   built-in Function prototype object as specified in ********.\n            // 12. Set the [[Call]] internal property of F as described in\n            //   ********.1.\n            // 13. Set the [[Construct]] internal property of F as described in\n            //   ********.2.\n            // 14. Set the [[HasInstance]] internal property of F as described in\n            //   ********.3.\n            var bound;\n            var binder = function () {\n\n                if (this instanceof bound) {\n                    // ********.2 [[Construct]]\n                    // When the [[Construct]] internal method of a function object,\n                    // F that was created using the bind function is called with a\n                    // list of arguments ExtraArgs, the following steps are taken:\n                    // 1. Let target be the value of F's [[TargetFunction]]\n                    //   internal property.\n                    // 2. If target has no [[Construct]] internal method, a\n                    //   TypeError exception is thrown.\n                    // 3. Let boundArgs be the value of F's [[BoundArgs]] internal\n                    //   property.\n                    // 4. Let args be a new list containing the same values as the\n                    //   list boundArgs in the same order followed by the same\n                    //   values as the list ExtraArgs in the same order.\n                    // 5. Return the result of calling the [[Construct]] internal\n                    //   method of target providing args as the arguments.\n\n                    var result = apply.call(\n                        target,\n                        this,\n                        array_concat.call(args, array_slice.call(arguments))\n                    );\n                    if ($Object(result) === result) {\n                        return result;\n                    }\n                    return this;\n\n                } else {\n                    // ********.1 [[Call]]\n                    // When the [[Call]] internal method of a function object, F,\n                    // which was created using the bind function is called with a\n                    // this value and a list of arguments ExtraArgs, the following\n                    // steps are taken:\n                    // 1. Let boundArgs be the value of F's [[BoundArgs]] internal\n                    //   property.\n                    // 2. Let boundThis be the value of F's [[BoundThis]] internal\n                    //   property.\n                    // 3. Let target be the value of F's [[TargetFunction]] internal\n                    //   property.\n                    // 4. Let args be a new list containing the same values as the\n                    //   list boundArgs in the same order followed by the same\n                    //   values as the list ExtraArgs in the same order.\n                    // 5. Return the result of calling the [[Call]] internal method\n                    //   of target providing boundThis as the this value and\n                    //   providing args as the arguments.\n\n                    // equiv: target.call(this, ...boundArgs, ...args)\n                    return apply.call(\n                        target,\n                        that,\n                        array_concat.call(args, array_slice.call(arguments))\n                    );\n\n                }\n\n            };\n\n            // 15. If the [[Class]] internal property of Target is \"Function\", then\n            //     a. Let L be the length property of Target minus the length of A.\n            //     b. Set the length own property of F to either 0 or L, whichever is\n            //       larger.\n            // 16. Else set the length own property of F to 0.\n\n            var boundLength = max(0, target.length - args.length);\n\n            // 17. Set the attributes of the length own property of F to the values\n            //   specified in 15.3.5.1.\n            var boundArgs = [];\n            for (var i = 0; i < boundLength; i++) {\n                array_push.call(boundArgs, '$' + i);\n            }\n\n            // XXX Build a dynamic function with desired amount of arguments is the only\n            // way to set the length property of a function.\n            // In environments where Content Security Policies enabled (Chrome extensions,\n            // for ex.) all use of eval or Function costructor throws an exception.\n            // However in all of these environments Function.prototype.bind exists\n            // and so this code will never be executed.\n            bound = $Function('binder', 'return function (' + array_join.call(boundArgs, ',') + '){ return binder.apply(this, arguments); }')(binder);\n\n            if (target.prototype) {\n                Empty.prototype = target.prototype;\n                bound.prototype = new Empty();\n                // Clean up dangling references.\n                Empty.prototype = null;\n            }\n\n            // TODO\n            // 18. Set the [[Extensible]] internal property of F to true.\n\n            // TODO\n            // 19. Let thrower be the [[ThrowTypeError]] function Object (13.2.3).\n            // 20. Call the [[DefineOwnProperty]] internal method of F with\n            //   arguments \"caller\", PropertyDescriptor {[[Get]]: thrower, [[Set]]:\n            //   thrower, [[Enumerable]]: false, [[Configurable]]: false}, and\n            //   false.\n            // 21. Call the [[DefineOwnProperty]] internal method of F with\n            //   arguments \"arguments\", PropertyDescriptor {[[Get]]: thrower,\n            //   [[Set]]: thrower, [[Enumerable]]: false, [[Configurable]]: false},\n            //   and false.\n\n            // TODO\n            // NOTE Function objects created using Function.prototype.bind do not\n            // have a prototype property or the [[Code]], [[FormalParameters]], and\n            // [[Scope]] internal properties.\n            // XXX can't delete prototype in pure-js.\n\n            // 22. Return F.\n            return bound;\n        }\n    });\n\n    // _Please note: Shortcuts are defined after `Function.prototype.bind` as we\n    // use it in defining shortcuts.\n    var owns = call.bind(ObjectPrototype.hasOwnProperty);\n    var toStr = call.bind(ObjectPrototype.toString);\n    var arraySlice = call.bind(array_slice);\n    var arraySliceApply = apply.bind(array_slice);\n    /* globals document */\n    if (typeof document === 'object' && document && document.documentElement) {\n        try {\n            arraySlice(document.documentElement.childNodes);\n        } catch (e) {\n            var origArraySlice = arraySlice;\n            var origArraySliceApply = arraySliceApply;\n            arraySlice = function arraySliceIE(arr) {\n                var r = [];\n                var i = arr.length;\n                while (i-- > 0) {\n                    r[i] = arr[i];\n                }\n                return origArraySliceApply(r, origArraySlice(arguments, 1));\n            };\n            arraySliceApply = function arraySliceApplyIE(arr, args) {\n                return origArraySliceApply(arraySlice(arr), args);\n            };\n        }\n    }\n    var strSlice = call.bind(StringPrototype.slice);\n    var strSplit = call.bind(StringPrototype.split);\n    var strIndexOf = call.bind(StringPrototype.indexOf);\n    var pushCall = call.bind(array_push);\n    var isEnum = call.bind(ObjectPrototype.propertyIsEnumerable);\n    var arraySort = call.bind(ArrayPrototype.sort);\n\n    //\n    // Array\n    // =====\n    //\n\n    var isArray = $Array.isArray || function isArray(obj) {\n        return toStr(obj) === '[object Array]';\n    };\n\n    // ES5 *********\n    // http://es5.github.com/#x15.4.4.13\n    // Return len+argCount.\n    // [bugfix, ielt8]\n    // IE < 8 bug: [].unshift(0) === undefined but should be \"1\"\n    var hasUnshiftReturnValueBug = [].unshift(0) !== 1;\n    defineProperties(ArrayPrototype, {\n        unshift: function () {\n            array_unshift.apply(this, arguments);\n            return this.length;\n        }\n    }, hasUnshiftReturnValueBug);\n\n    // ES5 ********\n    // http://es5.github.com/#x********\n    // https://developer.mozilla.org/en/JavaScript/Reference/Global_Objects/Array/isArray\n    defineProperties($Array, { isArray: isArray });\n\n    // The IsCallable() check in the Array functions\n    // has been replaced with a strict check on the\n    // internal class of the object to trap cases where\n    // the provided function was actually a regular\n    // expression literal, which in V8 and\n    // JavaScriptCore is a typeof \"function\".  Only in\n    // V8 are regular expression literals permitted as\n    // reduce parameters, so it is desirable in the\n    // general case for the shim to match the more\n    // strict and common behavior of rejecting regular\n    // expressions.\n\n    // ES5 *********\n    // http://es5.github.com/#x*********\n    // https://developer.mozilla.org/en/JavaScript/Reference/Global_Objects/array/forEach\n\n    // Check failure of by-index access of string characters (IE < 9)\n    // and failure of `0 in boxedString` (Rhino)\n    var boxedString = $Object('a');\n    var splitString = boxedString[0] !== 'a' || !(0 in boxedString);\n\n    var properlyBoxesContext = function properlyBoxed(method) {\n        // Check node 0.6.21 bug where third parameter is not boxed\n        var properlyBoxesNonStrict = true;\n        var properlyBoxesStrict = true;\n        var threwException = false;\n        if (method) {\n            try {\n                method.call('foo', function (_, __, context) {\n                    if (typeof context !== 'object') {\n                        properlyBoxesNonStrict = false;\n                    }\n                });\n\n                method.call([1], function () {\n                    'use strict';\n\n                    properlyBoxesStrict = typeof this === 'string';\n                }, 'x');\n            } catch (e) {\n                threwException = true;\n            }\n        }\n        return !!method && !threwException && properlyBoxesNonStrict && properlyBoxesStrict;\n    };\n\n    defineProperties(ArrayPrototype, {\n        forEach: function forEach(callbackfn/*, thisArg*/) {\n            var object = ES.ToObject(this);\n            var self = splitString && isString(this) ? strSplit(this, '') : object;\n            var i = -1;\n            var length = ES.ToUint32(self.length);\n            var T;\n            if (arguments.length > 1) {\n                T = arguments[1];\n            }\n\n            // If no callback function or if callback is not a callable function\n            if (!isCallable(callbackfn)) {\n                throw new TypeError('Array.prototype.forEach callback must be a function');\n            }\n\n            while (++i < length) {\n                if (i in self) {\n                    // Invoke the callback function with call, passing arguments:\n                    // context, property value, property key, thisArg object\n                    if (typeof T === 'undefined') {\n                        callbackfn(self[i], i, object);\n                    } else {\n                        callbackfn.call(T, self[i], i, object);\n                    }\n                }\n            }\n        }\n    }, !properlyBoxesContext(ArrayPrototype.forEach));\n\n    // ES5 *********\n    // http://es5.github.com/#x*********\n    // https://developer.mozilla.org/en/Core_JavaScript_1.5_Reference/Objects/Array/map\n    defineProperties(ArrayPrototype, {\n        map: function map(callbackfn/*, thisArg*/) {\n            var object = ES.ToObject(this);\n            var self = splitString && isString(this) ? strSplit(this, '') : object;\n            var length = ES.ToUint32(self.length);\n            var result = $Array(length);\n            var T;\n            if (arguments.length > 1) {\n                T = arguments[1];\n            }\n\n            // If no callback function or if callback is not a callable function\n            if (!isCallable(callbackfn)) {\n                throw new TypeError('Array.prototype.map callback must be a function');\n            }\n\n            for (var i = 0; i < length; i++) {\n                if (i in self) {\n                    if (typeof T === 'undefined') {\n                        result[i] = callbackfn(self[i], i, object);\n                    } else {\n                        result[i] = callbackfn.call(T, self[i], i, object);\n                    }\n                }\n            }\n            return result;\n        }\n    }, !properlyBoxesContext(ArrayPrototype.map));\n\n    // ES5 *********\n    // http://es5.github.com/#x*********\n    // https://developer.mozilla.org/en/Core_JavaScript_1.5_Reference/Objects/Array/filter\n    defineProperties(ArrayPrototype, {\n        filter: function filter(callbackfn/*, thisArg*/) {\n            var object = ES.ToObject(this);\n            var self = splitString && isString(this) ? strSplit(this, '') : object;\n            var length = ES.ToUint32(self.length);\n            var result = [];\n            var value;\n            var T;\n            if (arguments.length > 1) {\n                T = arguments[1];\n            }\n\n            // If no callback function or if callback is not a callable function\n            if (!isCallable(callbackfn)) {\n                throw new TypeError('Array.prototype.filter callback must be a function');\n            }\n\n            for (var i = 0; i < length; i++) {\n                if (i in self) {\n                    value = self[i];\n                    if (typeof T === 'undefined' ? callbackfn(value, i, object) : callbackfn.call(T, value, i, object)) {\n                        pushCall(result, value);\n                    }\n                }\n            }\n            return result;\n        }\n    }, !properlyBoxesContext(ArrayPrototype.filter));\n\n    // ES5 *********\n    // http://es5.github.com/#x*********\n    // https://developer.mozilla.org/en/JavaScript/Reference/Global_Objects/Array/every\n    defineProperties(ArrayPrototype, {\n        every: function every(callbackfn/*, thisArg*/) {\n            var object = ES.ToObject(this);\n            var self = splitString && isString(this) ? strSplit(this, '') : object;\n            var length = ES.ToUint32(self.length);\n            var T;\n            if (arguments.length > 1) {\n                T = arguments[1];\n            }\n\n            // If no callback function or if callback is not a callable function\n            if (!isCallable(callbackfn)) {\n                throw new TypeError('Array.prototype.every callback must be a function');\n            }\n\n            for (var i = 0; i < length; i++) {\n                if (i in self && !(typeof T === 'undefined' ? callbackfn(self[i], i, object) : callbackfn.call(T, self[i], i, object))) {\n                    return false;\n                }\n            }\n            return true;\n        }\n    }, !properlyBoxesContext(ArrayPrototype.every));\n\n    // ES5 *********\n    // http://es5.github.com/#x*********\n    // https://developer.mozilla.org/en/JavaScript/Reference/Global_Objects/Array/some\n    defineProperties(ArrayPrototype, {\n        some: function some(callbackfn/*, thisArg */) {\n            var object = ES.ToObject(this);\n            var self = splitString && isString(this) ? strSplit(this, '') : object;\n            var length = ES.ToUint32(self.length);\n            var T;\n            if (arguments.length > 1) {\n                T = arguments[1];\n            }\n\n            // If no callback function or if callback is not a callable function\n            if (!isCallable(callbackfn)) {\n                throw new TypeError('Array.prototype.some callback must be a function');\n            }\n\n            for (var i = 0; i < length; i++) {\n                if (i in self && (typeof T === 'undefined' ? callbackfn(self[i], i, object) : callbackfn.call(T, self[i], i, object))) {\n                    return true;\n                }\n            }\n            return false;\n        }\n    }, !properlyBoxesContext(ArrayPrototype.some));\n\n    // ES5 *********\n    // http://es5.github.com/#x*********\n    // https://developer.mozilla.org/en/Core_JavaScript_1.5_Reference/Objects/Array/reduce\n    var reduceCoercesToObject = false;\n    if (ArrayPrototype.reduce) {\n        reduceCoercesToObject = typeof ArrayPrototype.reduce.call('es5', function (_, __, ___, list) {\n            return list;\n        }) === 'object';\n    }\n    defineProperties(ArrayPrototype, {\n        reduce: function reduce(callbackfn/*, initialValue*/) {\n            var object = ES.ToObject(this);\n            var self = splitString && isString(this) ? strSplit(this, '') : object;\n            var length = ES.ToUint32(self.length);\n\n            // If no callback function or if callback is not a callable function\n            if (!isCallable(callbackfn)) {\n                throw new TypeError('Array.prototype.reduce callback must be a function');\n            }\n\n            // no value to return if no initial value and an empty array\n            if (length === 0 && arguments.length === 1) {\n                throw new TypeError('reduce of empty array with no initial value');\n            }\n\n            var i = 0;\n            var result;\n            if (arguments.length >= 2) {\n                result = arguments[1];\n            } else {\n                do {\n                    if (i in self) {\n                        result = self[i++];\n                        break;\n                    }\n\n                    // if array contains no values, no initial value to return\n                    if (++i >= length) {\n                        throw new TypeError('reduce of empty array with no initial value');\n                    }\n                } while (true);\n            }\n\n            for (; i < length; i++) {\n                if (i in self) {\n                    result = callbackfn(result, self[i], i, object);\n                }\n            }\n\n            return result;\n        }\n    }, !reduceCoercesToObject);\n\n    // ES5 *********\n    // http://es5.github.com/#x*********\n    // https://developer.mozilla.org/en/Core_JavaScript_1.5_Reference/Objects/Array/reduceRight\n    var reduceRightCoercesToObject = false;\n    if (ArrayPrototype.reduceRight) {\n        reduceRightCoercesToObject = typeof ArrayPrototype.reduceRight.call('es5', function (_, __, ___, list) {\n            return list;\n        }) === 'object';\n    }\n    defineProperties(ArrayPrototype, {\n        reduceRight: function reduceRight(callbackfn/*, initial*/) {\n            var object = ES.ToObject(this);\n            var self = splitString && isString(this) ? strSplit(this, '') : object;\n            var length = ES.ToUint32(self.length);\n\n            // If no callback function or if callback is not a callable function\n            if (!isCallable(callbackfn)) {\n                throw new TypeError('Array.prototype.reduceRight callback must be a function');\n            }\n\n            // no value to return if no initial value, empty array\n            if (length === 0 && arguments.length === 1) {\n                throw new TypeError('reduceRight of empty array with no initial value');\n            }\n\n            var result;\n            var i = length - 1;\n            if (arguments.length >= 2) {\n                result = arguments[1];\n            } else {\n                do {\n                    if (i in self) {\n                        result = self[i--];\n                        break;\n                    }\n\n                    // if array contains no values, no initial value to return\n                    if (--i < 0) {\n                        throw new TypeError('reduceRight of empty array with no initial value');\n                    }\n                } while (true);\n            }\n\n            if (i < 0) {\n                return result;\n            }\n\n            do {\n                if (i in self) {\n                    result = callbackfn(result, self[i], i, object);\n                }\n            } while (i--);\n\n            return result;\n        }\n    }, !reduceRightCoercesToObject);\n\n    // ES5 *********\n    // http://es5.github.com/#x*********\n    // https://developer.mozilla.org/en/JavaScript/Reference/Global_Objects/Array/indexOf\n    var hasFirefox2IndexOfBug = ArrayPrototype.indexOf && [0, 1].indexOf(1, 2) !== -1;\n    defineProperties(ArrayPrototype, {\n        indexOf: function indexOf(searchElement/*, fromIndex */) {\n            var self = splitString && isString(this) ? strSplit(this, '') : ES.ToObject(this);\n            var length = ES.ToUint32(self.length);\n\n            if (length === 0) {\n                return -1;\n            }\n\n            var i = 0;\n            if (arguments.length > 1) {\n                i = ES.ToInteger(arguments[1]);\n            }\n\n            // handle negative indices\n            i = i >= 0 ? i : max(0, length + i);\n            for (; i < length; i++) {\n                if (i in self && self[i] === searchElement) {\n                    return i;\n                }\n            }\n            return -1;\n        }\n    }, hasFirefox2IndexOfBug);\n\n    // ES5 *********\n    // http://es5.github.com/#x*********\n    // https://developer.mozilla.org/en/JavaScript/Reference/Global_Objects/Array/lastIndexOf\n    var hasFirefox2LastIndexOfBug = ArrayPrototype.lastIndexOf && [0, 1].lastIndexOf(0, -3) !== -1;\n    defineProperties(ArrayPrototype, {\n        lastIndexOf: function lastIndexOf(searchElement/*, fromIndex */) {\n            var self = splitString && isString(this) ? strSplit(this, '') : ES.ToObject(this);\n            var length = ES.ToUint32(self.length);\n\n            if (length === 0) {\n                return -1;\n            }\n            var i = length - 1;\n            if (arguments.length > 1) {\n                i = min(i, ES.ToInteger(arguments[1]));\n            }\n            // handle negative indices\n            i = i >= 0 ? i : length - Math.abs(i);\n            for (; i >= 0; i--) {\n                if (i in self && searchElement === self[i]) {\n                    return i;\n                }\n            }\n            return -1;\n        }\n    }, hasFirefox2LastIndexOfBug);\n\n    // ES5 *********\n    // http://es5.github.com/#x*********\n    var spliceNoopReturnsEmptyArray = (function () {\n        var a = [1, 2];\n        var result = a.splice();\n        return a.length === 2 && isArray(result) && result.length === 0;\n    }());\n    defineProperties(ArrayPrototype, {\n        // Safari 5.0 bug where .splice() returns undefined\n        splice: function splice(start, deleteCount) {\n            if (arguments.length === 0) {\n                return [];\n            } else {\n                return array_splice.apply(this, arguments);\n            }\n        }\n    }, !spliceNoopReturnsEmptyArray);\n\n    var spliceWorksWithEmptyObject = (function () {\n        var obj = {};\n        ArrayPrototype.splice.call(obj, 0, 0, 1);\n        return obj.length === 1;\n    }());\n    defineProperties(ArrayPrototype, {\n        splice: function splice(start, deleteCount) {\n            if (arguments.length === 0) {\n                return [];\n            }\n            var args = arguments;\n            this.length = max(ES.ToInteger(this.length), 0);\n            if (arguments.length > 0 && typeof deleteCount !== 'number') {\n                args = arraySlice(arguments);\n                if (args.length < 2) {\n                    pushCall(args, this.length - start);\n                } else {\n                    args[1] = ES.ToInteger(deleteCount);\n                }\n            }\n            return array_splice.apply(this, args);\n        }\n    }, !spliceWorksWithEmptyObject);\n    var spliceWorksWithLargeSparseArrays = (function () {\n        // Per https://github.com/es-shims/es5-shim/issues/295\n        // Safari 7/8 breaks with sparse arrays of size 1e5 or greater\n        var arr = new $Array(1e5);\n        // note: the index MUST be 8 or larger or the test will false pass\n        arr[8] = 'x';\n        arr.splice(1, 1);\n        // note: this test must be defined *after* the indexOf shim\n        // per https://github.com/es-shims/es5-shim/issues/313\n        return arr.indexOf('x') === 7;\n    }());\n    var spliceWorksWithSmallSparseArrays = (function () {\n        // Per https://github.com/es-shims/es5-shim/issues/295\n        // Opera 12.15 breaks on this, no idea why.\n        var n = 256;\n        var arr = [];\n        arr[n] = 'a';\n        arr.splice(n + 1, 0, 'b');\n        return arr[n] === 'a';\n    }());\n    defineProperties(ArrayPrototype, {\n        splice: function splice(start, deleteCount) {\n            var O = ES.ToObject(this);\n            var A = [];\n            var len = ES.ToUint32(O.length);\n            var relativeStart = ES.ToInteger(start);\n            var actualStart = relativeStart < 0 ? max((len + relativeStart), 0) : min(relativeStart, len);\n            var actualDeleteCount = min(max(ES.ToInteger(deleteCount), 0), len - actualStart);\n\n            var k = 0;\n            var from;\n            while (k < actualDeleteCount) {\n                from = $String(actualStart + k);\n                if (owns(O, from)) {\n                    A[k] = O[from];\n                }\n                k += 1;\n            }\n\n            var items = arraySlice(arguments, 2);\n            var itemCount = items.length;\n            var to;\n            if (itemCount < actualDeleteCount) {\n                k = actualStart;\n                var maxK = len - actualDeleteCount;\n                while (k < maxK) {\n                    from = $String(k + actualDeleteCount);\n                    to = $String(k + itemCount);\n                    if (owns(O, from)) {\n                        O[to] = O[from];\n                    } else {\n                        delete O[to];\n                    }\n                    k += 1;\n                }\n                k = len;\n                var minK = len - actualDeleteCount + itemCount;\n                while (k > minK) {\n                    delete O[k - 1];\n                    k -= 1;\n                }\n            } else if (itemCount > actualDeleteCount) {\n                k = len - actualDeleteCount;\n                while (k > actualStart) {\n                    from = $String(k + actualDeleteCount - 1);\n                    to = $String(k + itemCount - 1);\n                    if (owns(O, from)) {\n                        O[to] = O[from];\n                    } else {\n                        delete O[to];\n                    }\n                    k -= 1;\n                }\n            }\n            k = actualStart;\n            for (var i = 0; i < items.length; ++i) {\n                O[k] = items[i];\n                k += 1;\n            }\n            O.length = len - actualDeleteCount + itemCount;\n\n            return A;\n        }\n    }, !spliceWorksWithLargeSparseArrays || !spliceWorksWithSmallSparseArrays);\n\n    var originalJoin = ArrayPrototype.join;\n    var hasStringJoinBug;\n    try {\n        hasStringJoinBug = Array.prototype.join.call('123', ',') !== '1,2,3';\n    } catch (e) {\n        hasStringJoinBug = true;\n    }\n    if (hasStringJoinBug) {\n        defineProperties(ArrayPrototype, {\n            join: function join(separator) {\n                var sep = typeof separator === 'undefined' ? ',' : separator;\n                return originalJoin.call(isString(this) ? strSplit(this, '') : this, sep);\n            }\n        }, hasStringJoinBug);\n    }\n\n    var hasJoinUndefinedBug = [1, 2].join(undefined) !== '1,2';\n    if (hasJoinUndefinedBug) {\n        defineProperties(ArrayPrototype, {\n            join: function join(separator) {\n                var sep = typeof separator === 'undefined' ? ',' : separator;\n                return originalJoin.call(this, sep);\n            }\n        }, hasJoinUndefinedBug);\n    }\n\n    var pushShim = function push(item) {\n        var O = ES.ToObject(this);\n        var n = ES.ToUint32(O.length);\n        var i = 0;\n        while (i < arguments.length) {\n            O[n + i] = arguments[i];\n            i += 1;\n        }\n        O.length = n + i;\n        return n + i;\n    };\n\n    var pushIsNotGeneric = (function () {\n        var obj = {};\n        var result = Array.prototype.push.call(obj, undefined);\n        return result !== 1 || obj.length !== 1 || typeof obj[0] !== 'undefined' || !owns(obj, 0);\n    }());\n    defineProperties(ArrayPrototype, {\n        push: function push(item) {\n            if (isArray(this)) {\n                return array_push.apply(this, arguments);\n            }\n            return pushShim.apply(this, arguments);\n        }\n    }, pushIsNotGeneric);\n\n    // This fixes a very weird bug in Opera 10.6 when pushing `undefined\n    var pushUndefinedIsWeird = (function () {\n        var arr = [];\n        var result = arr.push(undefined);\n        return result !== 1 || arr.length !== 1 || typeof arr[0] !== 'undefined' || !owns(arr, 0);\n    }());\n    defineProperties(ArrayPrototype, { push: pushShim }, pushUndefinedIsWeird);\n\n    // ES5 15.2.3.14\n    // http://es5.github.io/#x15.4.4.10\n    // Fix boxed string bug\n    defineProperties(ArrayPrototype, {\n        slice: function (start, end) {\n            var arr = isString(this) ? strSplit(this, '') : this;\n            return arraySliceApply(arr, arguments);\n        }\n    }, splitString);\n\n    var sortIgnoresNonFunctions = (function () {\n        try {\n            [1, 2].sort(null);\n        } catch (e) {\n            try {\n                [1, 2].sort({});\n            } catch (e2) {\n                return false;\n            }\n        }\n        return true;\n    }());\n    var sortThrowsOnRegex = (function () {\n        // this is a problem in Firefox 4, in which `typeof /a/ === 'function'`\n        try {\n            [1, 2].sort(/a/);\n            return false;\n        } catch (e) {}\n        return true;\n    }());\n    var sortIgnoresUndefined = (function () {\n        // applies in IE 8, for one.\n        try {\n            [1, 2].sort(undefined);\n            return true;\n        } catch (e) {}\n        return false;\n    }());\n    defineProperties(ArrayPrototype, {\n        sort: function sort(compareFn) {\n            if (typeof compareFn === 'undefined') {\n                return arraySort(this);\n            }\n            if (!isCallable(compareFn)) {\n                throw new TypeError('Array.prototype.sort callback must be a function');\n            }\n            return arraySort(this, compareFn);\n        }\n    }, sortIgnoresNonFunctions || !sortIgnoresUndefined || !sortThrowsOnRegex);\n\n    //\n    // Object\n    // ======\n    //\n\n    // ES5 15.2.3.14\n    // http://es5.github.com/#x15.2.3.14\n\n    // http://whattheheadsaid.com/2010/10/a-safer-object-keys-compatibility-implementation\n    var hasDontEnumBug = !isEnum({ 'toString': null }, 'toString'); // jscs:ignore disallowQuotedKeysInObjects\n    var hasProtoEnumBug = isEnum(function () {}, 'prototype');\n    var hasStringEnumBug = !owns('x', '0');\n    var equalsConstructorPrototype = function (o) {\n        var ctor = o.constructor;\n        return ctor && ctor.prototype === o;\n    };\n    var excludedKeys = {\n        $applicationCache: true,\n        $console: true,\n        $external: true,\n        $frame: true,\n        $frameElement: true,\n        $frames: true,\n        $innerHeight: true,\n        $innerWidth: true,\n        $outerHeight: true,\n        $outerWidth: true,\n        $pageXOffset: true,\n        $pageYOffset: true,\n        $parent: true,\n        $scrollLeft: true,\n        $scrollTop: true,\n        $scrollX: true,\n        $scrollY: true,\n        $self: true,\n        $webkitIndexedDB: true,\n        $webkitStorageInfo: true,\n        $window: true,\n\n        $width: true,\n        $height: true,\n        $top: true,\n        $localStorage: true\n    };\n    var hasAutomationEqualityBug = (function () {\n        /* globals window */\n        if (typeof window === 'undefined') {\n            return false;\n        }\n        for (var k in window) {\n            try {\n                if (!excludedKeys['$' + k] && owns(window, k) && window[k] !== null && typeof window[k] === 'object') {\n                    equalsConstructorPrototype(window[k]);\n                }\n            } catch (e) {\n                return true;\n            }\n        }\n        return false;\n    }());\n    var equalsConstructorPrototypeIfNotBuggy = function (object) {\n        if (typeof window === 'undefined' || !hasAutomationEqualityBug) {\n            return equalsConstructorPrototype(object);\n        }\n        try {\n            return equalsConstructorPrototype(object);\n        } catch (e) {\n            return false;\n        }\n    };\n    var dontEnums = [\n        'toString',\n        'toLocaleString',\n        'valueOf',\n        'hasOwnProperty',\n        'isPrototypeOf',\n        'propertyIsEnumerable',\n        'constructor'\n    ];\n    var dontEnumsLength = dontEnums.length;\n\n    // taken directly from https://github.com/ljharb/is-arguments/blob/master/index.js\n    // can be replaced with require('is-arguments') if we ever use a build process instead\n    var isStandardArguments = function isArguments(value) {\n        return toStr(value) === '[object Arguments]';\n    };\n    var isLegacyArguments = function isArguments(value) {\n        return value !== null\n            && typeof value === 'object'\n            && typeof value.length === 'number'\n            && value.length >= 0\n            && !isArray(value)\n            && isCallable(value.callee);\n    };\n    var isArguments = isStandardArguments(arguments) ? isStandardArguments : isLegacyArguments;\n\n    defineProperties($Object, {\n        keys: function keys(object) {\n            var isFn = isCallable(object);\n            var isArgs = isArguments(object);\n            var isObject = object !== null && typeof object === 'object';\n            var isStr = isObject && isString(object);\n\n            if (!isObject && !isFn && !isArgs) {\n                throw new TypeError('Object.keys called on a non-object');\n            }\n\n            var theKeys = [];\n            var skipProto = hasProtoEnumBug && isFn;\n            if ((isStr && hasStringEnumBug) || isArgs) {\n                for (var i = 0; i < object.length; ++i) {\n                    pushCall(theKeys, $String(i));\n                }\n            }\n\n            if (!isArgs) {\n                for (var name in object) {\n                    if (!(skipProto && name === 'prototype') && owns(object, name)) {\n                        pushCall(theKeys, $String(name));\n                    }\n                }\n            }\n\n            if (hasDontEnumBug) {\n                var skipConstructor = equalsConstructorPrototypeIfNotBuggy(object);\n                for (var j = 0; j < dontEnumsLength; j++) {\n                    var dontEnum = dontEnums[j];\n                    if (!(skipConstructor && dontEnum === 'constructor') && owns(object, dontEnum)) {\n                        pushCall(theKeys, dontEnum);\n                    }\n                }\n            }\n            return theKeys;\n        }\n    });\n\n    var keysWorksWithArguments = $Object.keys && (function () {\n        // Safari 5.0 bug\n        return $Object.keys(arguments).length === 2;\n    }(1, 2));\n    var keysHasArgumentsLengthBug = $Object.keys && (function () {\n        var argKeys = $Object.keys(arguments);\n        return arguments.length !== 1 || argKeys.length !== 1 || argKeys[0] !== 1;\n    }(1));\n    var originalKeys = $Object.keys;\n    defineProperties($Object, {\n        keys: function keys(object) {\n            if (isArguments(object)) {\n                return originalKeys(arraySlice(object));\n            } else {\n                return originalKeys(object);\n            }\n        }\n    }, !keysWorksWithArguments || keysHasArgumentsLengthBug);\n\n    //\n    // Date\n    // ====\n    //\n\n    var hasNegativeMonthYearBug = new Date(-3509827329600292).getUTCMonth() !== 0;\n    var aNegativeTestDate = new Date(-1509842289600292);\n    var aPositiveTestDate = new Date(1449662400000);\n    var hasToUTCStringFormatBug = aNegativeTestDate.toUTCString() !== 'Mon, 01 Jan -45875 11:59:59 GMT';\n    var hasToDateStringFormatBug;\n    var hasToStringFormatBug;\n    var timeZoneOffset = aNegativeTestDate.getTimezoneOffset();\n    if (timeZoneOffset < -720) {\n        hasToDateStringFormatBug = aNegativeTestDate.toDateString() !== 'Tue Jan 02 -45875';\n        hasToStringFormatBug = !(/^Thu Dec 10 2015 \\d\\d:\\d\\d:\\d\\d GMT[-+]\\d\\d\\d\\d(?: |$)/).test(String(aPositiveTestDate));\n    } else {\n        hasToDateStringFormatBug = aNegativeTestDate.toDateString() !== 'Mon Jan 01 -45875';\n        hasToStringFormatBug = !(/^Wed Dec 09 2015 \\d\\d:\\d\\d:\\d\\d GMT[-+]\\d\\d\\d\\d(?: |$)/).test(String(aPositiveTestDate));\n    }\n\n    var originalGetFullYear = call.bind(Date.prototype.getFullYear);\n    var originalGetMonth = call.bind(Date.prototype.getMonth);\n    var originalGetDate = call.bind(Date.prototype.getDate);\n    var originalGetUTCFullYear = call.bind(Date.prototype.getUTCFullYear);\n    var originalGetUTCMonth = call.bind(Date.prototype.getUTCMonth);\n    var originalGetUTCDate = call.bind(Date.prototype.getUTCDate);\n    var originalGetUTCDay = call.bind(Date.prototype.getUTCDay);\n    var originalGetUTCHours = call.bind(Date.prototype.getUTCHours);\n    var originalGetUTCMinutes = call.bind(Date.prototype.getUTCMinutes);\n    var originalGetUTCSeconds = call.bind(Date.prototype.getUTCSeconds);\n    var originalGetUTCMilliseconds = call.bind(Date.prototype.getUTCMilliseconds);\n    var dayName = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];\n    var monthName = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n    var daysInMonth = function daysInMonth(month, year) {\n        return originalGetDate(new Date(year, month, 0));\n    };\n\n    defineProperties(Date.prototype, {\n        getFullYear: function getFullYear() {\n            if (!this || !(this instanceof Date)) {\n                throw new TypeError('this is not a Date object.');\n            }\n            var year = originalGetFullYear(this);\n            if (year < 0 && originalGetMonth(this) > 11) {\n                return year + 1;\n            }\n            return year;\n        },\n        getMonth: function getMonth() {\n            if (!this || !(this instanceof Date)) {\n                throw new TypeError('this is not a Date object.');\n            }\n            var year = originalGetFullYear(this);\n            var month = originalGetMonth(this);\n            if (year < 0 && month > 11) {\n                return 0;\n            }\n            return month;\n        },\n        getDate: function getDate() {\n            if (!this || !(this instanceof Date)) {\n                throw new TypeError('this is not a Date object.');\n            }\n            var year = originalGetFullYear(this);\n            var month = originalGetMonth(this);\n            var date = originalGetDate(this);\n            if (year < 0 && month > 11) {\n                if (month === 12) {\n                    return date;\n                }\n                var days = daysInMonth(0, year + 1);\n                return (days - date) + 1;\n            }\n            return date;\n        },\n        getUTCFullYear: function getUTCFullYear() {\n            if (!this || !(this instanceof Date)) {\n                throw new TypeError('this is not a Date object.');\n            }\n            var year = originalGetUTCFullYear(this);\n            if (year < 0 && originalGetUTCMonth(this) > 11) {\n                return year + 1;\n            }\n            return year;\n        },\n        getUTCMonth: function getUTCMonth() {\n            if (!this || !(this instanceof Date)) {\n                throw new TypeError('this is not a Date object.');\n            }\n            var year = originalGetUTCFullYear(this);\n            var month = originalGetUTCMonth(this);\n            if (year < 0 && month > 11) {\n                return 0;\n            }\n            return month;\n        },\n        getUTCDate: function getUTCDate() {\n            if (!this || !(this instanceof Date)) {\n                throw new TypeError('this is not a Date object.');\n            }\n            var year = originalGetUTCFullYear(this);\n            var month = originalGetUTCMonth(this);\n            var date = originalGetUTCDate(this);\n            if (year < 0 && month > 11) {\n                if (month === 12) {\n                    return date;\n                }\n                var days = daysInMonth(0, year + 1);\n                return (days - date) + 1;\n            }\n            return date;\n        }\n    }, hasNegativeMonthYearBug);\n\n    defineProperties(Date.prototype, {\n        toUTCString: function toUTCString() {\n            if (!this || !(this instanceof Date)) {\n                throw new TypeError('this is not a Date object.');\n            }\n            var day = originalGetUTCDay(this);\n            var date = originalGetUTCDate(this);\n            var month = originalGetUTCMonth(this);\n            var year = originalGetUTCFullYear(this);\n            var hour = originalGetUTCHours(this);\n            var minute = originalGetUTCMinutes(this);\n            var second = originalGetUTCSeconds(this);\n            return dayName[day] + ', '\n                + (date < 10 ? '0' + date : date) + ' '\n                + monthName[month] + ' '\n                + year + ' '\n                + (hour < 10 ? '0' + hour : hour) + ':'\n                + (minute < 10 ? '0' + minute : minute) + ':'\n                + (second < 10 ? '0' + second : second) + ' GMT';\n        }\n    }, hasNegativeMonthYearBug || hasToUTCStringFormatBug);\n\n    // Opera 12 has `,`\n    defineProperties(Date.prototype, {\n        toDateString: function toDateString() {\n            if (!this || !(this instanceof Date)) {\n                throw new TypeError('this is not a Date object.');\n            }\n            var day = this.getDay();\n            var date = this.getDate();\n            var month = this.getMonth();\n            var year = this.getFullYear();\n            return dayName[day] + ' '\n                + monthName[month] + ' '\n                + (date < 10 ? '0' + date : date) + ' '\n                + year;\n        }\n    }, hasNegativeMonthYearBug || hasToDateStringFormatBug);\n\n    // can't use defineProperties here because of toString enumeration issue in IE <= 8\n    if (hasNegativeMonthYearBug || hasToStringFormatBug) {\n        Date.prototype.toString = function toString() {\n            if (!this || !(this instanceof Date)) {\n                throw new TypeError('this is not a Date object.');\n            }\n            var day = this.getDay();\n            var date = this.getDate();\n            var month = this.getMonth();\n            var year = this.getFullYear();\n            var hour = this.getHours();\n            var minute = this.getMinutes();\n            var second = this.getSeconds();\n            var timezoneOffset = this.getTimezoneOffset();\n            var hoursOffset = Math.floor(Math.abs(timezoneOffset) / 60);\n            var minutesOffset = Math.floor(Math.abs(timezoneOffset) % 60);\n            return dayName[day] + ' '\n                + monthName[month] + ' '\n                + (date < 10 ? '0' + date : date) + ' '\n                + year + ' '\n                + (hour < 10 ? '0' + hour : hour) + ':'\n                + (minute < 10 ? '0' + minute : minute) + ':'\n                + (second < 10 ? '0' + second : second) + ' GMT'\n                + (timezoneOffset > 0 ? '-' : '+')\n                + (hoursOffset < 10 ? '0' + hoursOffset : hoursOffset)\n                + (minutesOffset < 10 ? '0' + minutesOffset : minutesOffset);\n        };\n        if (supportsDescriptors) {\n            $Object.defineProperty(Date.prototype, 'toString', {\n                configurable: true,\n                enumerable: false,\n                writable: true\n            });\n        }\n    }\n\n    // ES5 *********\n    // http://es5.github.com/#x*********\n    // This function returns a String value represent the instance in time\n    // represented by this Date object. The format of the String is the Date Time\n    // string format defined in *********. All fields are present in the String.\n    // The time zone is always UTC, denoted by the suffix Z. If the time value of\n    // this object is not a finite Number a RangeError exception is thrown.\n    var negativeDate = -62198755200000;\n    var negativeYearString = '-000001';\n    var hasNegativeDateBug = Date.prototype.toISOString && new Date(negativeDate).toISOString().indexOf(negativeYearString) === -1; // eslint-disable-line max-len\n    var hasSafari51DateBug = Date.prototype.toISOString && new Date(-1).toISOString() !== '1969-12-31T23:59:59.999Z';\n\n    var getTime = call.bind(Date.prototype.getTime);\n\n    defineProperties(Date.prototype, {\n        toISOString: function toISOString() {\n            if (!isFinite(this) || !isFinite(getTime(this))) {\n                // Adope Photoshop requires the second check.\n                throw new RangeError('Date.prototype.toISOString called on non-finite value.');\n            }\n\n            var year = originalGetUTCFullYear(this);\n\n            var month = originalGetUTCMonth(this);\n            // see https://github.com/es-shims/es5-shim/issues/111\n            year += Math.floor(month / 12);\n            month = ((month % 12) + 12) % 12;\n\n            // the date time string format is specified in *********.\n            var result = [\n                month + 1,\n                originalGetUTCDate(this),\n                originalGetUTCHours(this),\n                originalGetUTCMinutes(this),\n                originalGetUTCSeconds(this)\n            ];\n            year = (\n                (year < 0 ? '-' : (year > 9999 ? '+' : ''))\n                + strSlice('00000' + Math.abs(year), (0 <= year && year <= 9999) ? -4 : -6)\n            );\n\n            for (var i = 0; i < result.length; ++i) {\n                // pad months, days, hours, minutes, and seconds to have two digits.\n                result[i] = strSlice('00' + result[i], -2);\n            }\n            // pad milliseconds to have three digits.\n            return (\n                year + '-' + arraySlice(result, 0, 2).join('-')\n                + 'T' + arraySlice(result, 2).join(':') + '.'\n                + strSlice('000' + originalGetUTCMilliseconds(this), -3) + 'Z'\n            );\n        }\n    }, hasNegativeDateBug || hasSafari51DateBug);\n\n    // ES5 15.9.5.44\n    // http://es5.github.com/#x15.9.5.44\n    // This function provides a String representation of a Date object for use by\n    // JSON.stringify (15.12.3).\n    var dateToJSONIsSupported = (function () {\n        try {\n            return Date.prototype.toJSON\n                && new Date(NaN).toJSON() === null\n                && new Date(negativeDate).toJSON().indexOf(negativeYearString) !== -1\n                && Date.prototype.toJSON.call({ // generic\n                    toISOString: function () { return true; }\n                });\n        } catch (e) {\n            return false;\n        }\n    }());\n    if (!dateToJSONIsSupported) {\n        Date.prototype.toJSON = function toJSON(key) {\n            // When the toJSON method is called with argument key, the following\n            // steps are taken:\n\n            // 1.  Let O be the result of calling ToObject, giving it the this\n            // value as its argument.\n            // 2. Let tv be ES.ToPrimitive(O, hint Number).\n            var O = $Object(this);\n            var tv = ES.ToPrimitive(O);\n            // 3. If tv is a Number and is not finite, return null.\n            if (typeof tv === 'number' && !isFinite(tv)) {\n                return null;\n            }\n            // 4. Let toISO be the result of calling the [[Get]] internal method of\n            // O with argument \"toISOString\".\n            var toISO = O.toISOString;\n            // 5. If IsCallable(toISO) is false, throw a TypeError exception.\n            if (!isCallable(toISO)) {\n                throw new TypeError('toISOString property is not callable');\n            }\n            // 6. Return the result of calling the [[Call]] internal method of\n            //  toISO with O as the this value and an empty argument list.\n            return toISO.call(O);\n\n            // NOTE 1 The argument is ignored.\n\n            // NOTE 2 The toJSON function is intentionally generic; it does not\n            // require that its this value be a Date object. Therefore, it can be\n            // transferred to other kinds of objects for use as a method. However,\n            // it does require that any such object have a toISOString method. An\n            // object is free to use the argument key to filter its\n            // stringification.\n        };\n    }\n\n    // ES5 15.9.4.2\n    // http://es5.github.com/#x15.9.4.2\n    // based on work shared by Daniel Friesen (dantman)\n    // http://gist.github.com/303249\n    var supportsExtendedYears = Date.parse('+033658-09-27T01:46:40.000Z') === 1e15;\n    var acceptsInvalidDates = !isNaN(Date.parse('2012-04-04T24:00:00.500Z')) || !isNaN(Date.parse('2012-11-31T23:59:59.000Z')) || !isNaN(Date.parse('2012-12-31T23:59:60.000Z'));\n    var doesNotParseY2KNewYear = isNaN(Date.parse('2000-01-01T00:00:00.000Z'));\n    if (doesNotParseY2KNewYear || acceptsInvalidDates || !supportsExtendedYears) {\n        // XXX global assignment won't work in embeddings that use\n        // an alternate object for the context.\n        /* global Date: true */\n        var maxSafeUnsigned32Bit = Math.pow(2, 31) - 1;\n        var hasSafariSignedIntBug = isActualNaN(new Date(1970, 0, 1, 0, 0, 0, maxSafeUnsigned32Bit + 1).getTime());\n        // eslint-disable-next-line no-implicit-globals, no-global-assign\n        Date = (function (NativeDate) {\n            // Date.length === 7\n            var DateShim = function Date(Y, M, D, h, m, s, ms) {\n                var length = arguments.length;\n                var date;\n                if (this instanceof NativeDate) {\n                    var seconds = s;\n                    var millis = ms;\n                    if (hasSafariSignedIntBug && length >= 7 && ms > maxSafeUnsigned32Bit) {\n                        // work around a Safari 8/9 bug where it treats the seconds as signed\n                        var msToShift = Math.floor(ms / maxSafeUnsigned32Bit) * maxSafeUnsigned32Bit;\n                        var sToShift = Math.floor(msToShift / 1e3);\n                        seconds += sToShift;\n                        millis -= sToShift * 1e3;\n                    }\n                    date = length === 1 && $String(Y) === Y // isString(Y)\n                        // We explicitly pass it through parse:\n                        ? new NativeDate(DateShim.parse(Y))\n                        // We have to manually make calls depending on argument\n                        // length here\n                        : length >= 7 ? new NativeDate(Y, M, D, h, m, seconds, millis)\n                            : length >= 6 ? new NativeDate(Y, M, D, h, m, seconds)\n                                : length >= 5 ? new NativeDate(Y, M, D, h, m)\n                                    : length >= 4 ? new NativeDate(Y, M, D, h)\n                                        : length >= 3 ? new NativeDate(Y, M, D)\n                                            : length >= 2 ? new NativeDate(Y, M)\n                                                : length >= 1 ? new NativeDate(Y instanceof NativeDate ? +Y : Y)\n                                                    : new NativeDate();\n                } else {\n                    date = NativeDate.apply(this, arguments);\n                }\n                if (!isPrimitive(date)) {\n                    // Prevent mixups with unfixed Date object\n                    defineProperties(date, { constructor: DateShim }, true);\n                }\n                return date;\n            };\n\n            // ********* Date Time String Format.\n            var isoDateExpression = new RegExp('^'\n                + '(\\\\d{4}|[+-]\\\\d{6})' // four-digit year capture or sign + 6-digit extended year\n                + '(?:-(\\\\d{2})' // optional month capture\n                + '(?:-(\\\\d{2})' // optional day capture\n                + '(?:' // capture hours:minutes:seconds.milliseconds\n                    + 'T(\\\\d{2})' // hours capture\n                    + ':(\\\\d{2})' // minutes capture\n                    + '(?:' // optional :seconds.milliseconds\n                        + ':(\\\\d{2})' // seconds capture\n                        + '(?:(\\\\.\\\\d{1,}))?' // milliseconds capture\n                    + ')?'\n                + '(' // capture UTC offset component\n                    + 'Z|' // UTC capture\n                    + '(?:' // offset specifier +/-hours:minutes\n                        + '([-+])' // sign capture\n                        + '(\\\\d{2})' // hours offset capture\n                        + ':(\\\\d{2})' // minutes offset capture\n                    + ')'\n                + ')?)?)?)?'\n            + '$');\n\n            var months = [0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334, 365];\n\n            var dayFromMonth = function dayFromMonth(year, month) {\n                var t = month > 1 ? 1 : 0;\n                return (\n                    months[month]\n                    + Math.floor((year - 1969 + t) / 4)\n                    - Math.floor((year - 1901 + t) / 100)\n                    + Math.floor((year - 1601 + t) / 400)\n                    + (365 * (year - 1970))\n                );\n            };\n\n            var toUTC = function toUTC(t) {\n                var s = 0;\n                var ms = t;\n                if (hasSafariSignedIntBug && ms > maxSafeUnsigned32Bit) {\n                    // work around a Safari 8/9 bug where it treats the seconds as signed\n                    var msToShift = Math.floor(ms / maxSafeUnsigned32Bit) * maxSafeUnsigned32Bit;\n                    var sToShift = Math.floor(msToShift / 1e3);\n                    s += sToShift;\n                    ms -= sToShift * 1e3;\n                }\n                return $Number(new NativeDate(1970, 0, 1, 0, 0, s, ms));\n            };\n\n            // Copy any custom methods a 3rd party library may have added\n            for (var key in NativeDate) {\n                if (owns(NativeDate, key)) {\n                    DateShim[key] = NativeDate[key];\n                }\n            }\n\n            // Copy \"native\" methods explicitly; they may be non-enumerable\n            defineProperties(DateShim, {\n                now: NativeDate.now,\n                UTC: NativeDate.UTC\n            }, true);\n            DateShim.prototype = NativeDate.prototype;\n            defineProperties(DateShim.prototype, { constructor: DateShim }, true);\n\n            // Upgrade Date.parse to handle simplified ISO 8601 strings\n            var parseShim = function parse(string) {\n                var match = isoDateExpression.exec(string);\n                if (match) {\n                    // parse months, days, hours, minutes, seconds, and milliseconds\n                    // provide default values if necessary\n                    // parse the UTC offset component\n                    var year = $Number(match[1]),\n                        month = $Number(match[2] || 1) - 1,\n                        day = $Number(match[3] || 1) - 1,\n                        hour = $Number(match[4] || 0),\n                        minute = $Number(match[5] || 0),\n                        second = $Number(match[6] || 0),\n                        millisecond = Math.floor($Number(match[7] || 0) * 1000),\n                        // When time zone is missed, local offset should be used\n                        // (ES 5.1 bug)\n                        // see https://bugs.ecmascript.org/show_bug.cgi?id=112\n                        isLocalTime = Boolean(match[4] && !match[8]),\n                        signOffset = match[9] === '-' ? 1 : -1,\n                        hourOffset = $Number(match[10] || 0),\n                        minuteOffset = $Number(match[11] || 0),\n                        result;\n                    var hasMinutesOrSecondsOrMilliseconds = minute > 0 || second > 0 || millisecond > 0;\n                    if (\n                        hour < (hasMinutesOrSecondsOrMilliseconds ? 24 : 25)\n                        && minute < 60 && second < 60 && millisecond < 1000\n                        && month > -1 && month < 12 && hourOffset < 24\n                        && minuteOffset < 60 // detect invalid offsets\n                        && day > -1\n                        && day < (dayFromMonth(year, month + 1) - dayFromMonth(year, month))\n                    ) {\n                        result = (\n                            ((dayFromMonth(year, month) + day) * 24)\n                            + hour\n                            + (hourOffset * signOffset)\n                        ) * 60;\n                        result = ((\n                            ((result + minute + (minuteOffset * signOffset)) * 60)\n                            + second\n                        ) * 1000) + millisecond;\n                        if (isLocalTime) {\n                            result = toUTC(result);\n                        }\n                        if (-8.64e15 <= result && result <= 8.64e15) {\n                            return result;\n                        }\n                    }\n                    return NaN;\n                }\n                return NativeDate.parse.apply(this, arguments);\n            };\n            defineProperties(DateShim, { parse: parseShim });\n\n            return DateShim;\n        }(Date));\n        /* global Date: false */\n    }\n\n    // ES5 15.9.4.4\n    // http://es5.github.com/#x15.9.4.4\n    if (!Date.now) {\n        Date.now = function now() {\n            return new Date().getTime();\n        };\n    }\n\n    //\n    // Number\n    // ======\n    //\n\n    // ES5.1 ********\n    // http://es5.github.com/#x********\n    var hasToFixedBugs = NumberPrototype.toFixed && (\n        (0.00008).toFixed(3) !== '0.000'\n        || (0.9).toFixed(0) !== '1'\n        || (1.255).toFixed(2) !== '1.25'\n        || (1000000000000000128).toFixed(0) !== '1000000000000000128'\n    );\n\n    var toFixedHelpers = {\n        base: 1e7,\n        size: 6,\n        data: [0, 0, 0, 0, 0, 0],\n        multiply: function multiply(n, c) {\n            var i = -1;\n            var c2 = c;\n            while (++i < toFixedHelpers.size) {\n                c2 += n * toFixedHelpers.data[i];\n                toFixedHelpers.data[i] = c2 % toFixedHelpers.base;\n                c2 = Math.floor(c2 / toFixedHelpers.base);\n            }\n        },\n        divide: function divide(n) {\n            var i = toFixedHelpers.size;\n            var c = 0;\n            while (--i >= 0) {\n                c += toFixedHelpers.data[i];\n                toFixedHelpers.data[i] = Math.floor(c / n);\n                c = (c % n) * toFixedHelpers.base;\n            }\n        },\n        numToString: function numToString() {\n            var i = toFixedHelpers.size;\n            var s = '';\n            while (--i >= 0) {\n                if (s !== '' || i === 0 || toFixedHelpers.data[i] !== 0) {\n                    var t = $String(toFixedHelpers.data[i]);\n                    if (s === '') {\n                        s = t;\n                    } else {\n                        s += strSlice('0000000', 0, 7 - t.length) + t;\n                    }\n                }\n            }\n            return s;\n        },\n        pow: function pow(x, n, acc) {\n            return (n === 0 ? acc : (n % 2 === 1 ? pow(x, n - 1, acc * x) : pow(x * x, n / 2, acc)));\n        },\n        log: function log(x) {\n            var n = 0;\n            var x2 = x;\n            while (x2 >= 4096) {\n                n += 12;\n                x2 /= 4096;\n            }\n            while (x2 >= 2) {\n                n += 1;\n                x2 /= 2;\n            }\n            return n;\n        }\n    };\n\n    var toFixedShim = function toFixed(fractionDigits) {\n        var f, x, s, m, e, z, j, k;\n\n        // Test for NaN and round fractionDigits down\n        f = $Number(fractionDigits);\n        f = isActualNaN(f) ? 0 : Math.floor(f);\n\n        if (f < 0 || f > 20) {\n            throw new RangeError('Number.toFixed called with invalid number of decimals');\n        }\n\n        x = $Number(this);\n\n        if (isActualNaN(x)) {\n            return 'NaN';\n        }\n\n        // If it is too big or small, return the string value of the number\n        if (x <= -1e21 || x >= 1e21) {\n            return $String(x);\n        }\n\n        s = '';\n\n        if (x < 0) {\n            s = '-';\n            x = -x;\n        }\n\n        m = '0';\n\n        if (x > 1e-21) {\n            // 1e-21 < x < 1e21\n            // -70 < log2(x) < 70\n            e = toFixedHelpers.log(x * toFixedHelpers.pow(2, 69, 1)) - 69;\n            z = (e < 0 ? x * toFixedHelpers.pow(2, -e, 1) : x / toFixedHelpers.pow(2, e, 1));\n            z *= 0x10000000000000; // Math.pow(2, 52);\n            e = 52 - e;\n\n            // -18 < e < 122\n            // x = z / 2 ^ e\n            if (e > 0) {\n                toFixedHelpers.multiply(0, z);\n                j = f;\n\n                while (j >= 7) {\n                    toFixedHelpers.multiply(1e7, 0);\n                    j -= 7;\n                }\n\n                toFixedHelpers.multiply(toFixedHelpers.pow(10, j, 1), 0);\n                j = e - 1;\n\n                while (j >= 23) {\n                    toFixedHelpers.divide(1 << 23);\n                    j -= 23;\n                }\n\n                toFixedHelpers.divide(1 << j);\n                toFixedHelpers.multiply(1, 1);\n                toFixedHelpers.divide(2);\n                m = toFixedHelpers.numToString();\n            } else {\n                toFixedHelpers.multiply(0, z);\n                toFixedHelpers.multiply(1 << (-e), 0);\n                m = toFixedHelpers.numToString() + strSlice('0.00000000000000000000', 2, 2 + f);\n            }\n        }\n\n        if (f > 0) {\n            k = m.length;\n\n            if (k <= f) {\n                m = s + strSlice('0.0000000000000000000', 0, f - k + 2) + m;\n            } else {\n                m = s + strSlice(m, 0, k - f) + '.' + strSlice(m, k - f);\n            }\n        } else {\n            m = s + m;\n        }\n\n        return m;\n    };\n    defineProperties(NumberPrototype, { toFixed: toFixedShim }, hasToFixedBugs);\n\n    var hasToPrecisionUndefinedBug = (function () {\n        try {\n            return 1.0.toPrecision(undefined) === '1';\n        } catch (e) {\n            return true;\n        }\n    }());\n    var originalToPrecision = NumberPrototype.toPrecision;\n    defineProperties(NumberPrototype, {\n        toPrecision: function toPrecision(precision) {\n            return typeof precision === 'undefined' ? originalToPrecision.call(this) : originalToPrecision.call(this, precision);\n        }\n    }, hasToPrecisionUndefinedBug);\n\n    //\n    // String\n    // ======\n    //\n\n    // ES5 15.5.4.14\n    // http://es5.github.com/#x15.5.4.14\n\n    // [bugfix, IE lt 9, firefox 4, Konqueror, Opera, obscure browsers]\n    // Many browsers do not split properly with regular expressions or they\n    // do not perform the split correctly under obscure conditions.\n    // See http://blog.stevenlevithan.com/archives/cross-browser-split\n    // I've tested in many browsers and this seems to cover the deviant ones:\n    //    'ab'.split(/(?:ab)*/) should be [\"\", \"\"], not [\"\"]\n    //    '.'.split(/(.?)(.?)/) should be [\"\", \".\", \"\", \"\"], not [\"\", \"\"]\n    //    'tesst'.split(/(s)*/) should be [\"t\", undefined, \"e\", \"s\", \"t\"], not\n    //       [undefined, \"t\", undefined, \"e\", ...]\n    //    ''.split(/.?/) should be [], not [\"\"]\n    //    '.'.split(/()()/) should be [\".\"], not [\"\", \"\", \".\"]\n\n    if (\n        'ab'.split(/(?:ab)*/).length !== 2\n        || '.'.split(/(.?)(.?)/).length !== 4\n        || 'tesst'.split(/(s)*/)[1] === 't'\n        || 'test'.split(/(?:)/, -1).length !== 4\n        || ''.split(/.?/).length\n        || '.'.split(/()()/).length > 1\n    ) {\n        (function () {\n            var compliantExecNpcg = typeof (/()??/).exec('')[1] === 'undefined'; // NPCG: nonparticipating capturing group\n            var maxSafe32BitInt = Math.pow(2, 32) - 1;\n\n            StringPrototype.split = function (separator, limit) {\n                var string = String(this);\n                if (typeof separator === 'undefined' && limit === 0) {\n                    return [];\n                }\n\n                // If `separator` is not a regex, use native split\n                if (!isRegex(separator)) {\n                    return strSplit(this, separator, limit);\n                }\n\n                var output = [];\n                var flags = (separator.ignoreCase ? 'i' : '')\n                            + (separator.multiline ? 'm' : '')\n                            + (separator.unicode ? 'u' : '') // in ES6\n                            + (separator.sticky ? 'y' : ''), // Firefox 3+ and ES6\n                    lastLastIndex = 0,\n                    // Make `global` and avoid `lastIndex` issues by working with a copy\n                    separator2, match, lastIndex, lastLength;\n                var separatorCopy = new RegExp(separator.source, flags + 'g');\n                if (!compliantExecNpcg) {\n                    // Doesn't need flags gy, but they don't hurt\n                    separator2 = new RegExp('^' + separatorCopy.source + '$(?!\\\\s)', flags);\n                }\n                /* Values for `limit`, per the spec:\n                 * If undefined: 4294967295 // maxSafe32BitInt\n                 * If 0, Infinity, or NaN: 0\n                 * If positive number: limit = Math.floor(limit); if (limit > 4294967295) limit -= 4294967296;\n                 * If negative number: 4294967296 - Math.floor(Math.abs(limit))\n                 * If other: Type-convert, then use the above rules\n                 */\n                var splitLimit = typeof limit === 'undefined' ? maxSafe32BitInt : ES.ToUint32(limit);\n                match = separatorCopy.exec(string);\n                while (match) {\n                    // `separatorCopy.lastIndex` is not reliable cross-browser\n                    lastIndex = match.index + match[0].length;\n                    if (lastIndex > lastLastIndex) {\n                        pushCall(output, strSlice(string, lastLastIndex, match.index));\n                        // Fix browsers whose `exec` methods don't consistently return `undefined` for\n                        // nonparticipating capturing groups\n                        if (!compliantExecNpcg && match.length > 1) {\n                            /* eslint-disable no-loop-func */\n                            match[0].replace(separator2, function () {\n                                for (var i = 1; i < arguments.length - 2; i++) {\n                                    if (typeof arguments[i] === 'undefined') {\n                                        match[i] = void 0;\n                                    }\n                                }\n                            });\n                            /* eslint-enable no-loop-func */\n                        }\n                        if (match.length > 1 && match.index < string.length) {\n                            array_push.apply(output, arraySlice(match, 1));\n                        }\n                        lastLength = match[0].length;\n                        lastLastIndex = lastIndex;\n                        if (output.length >= splitLimit) {\n                            break;\n                        }\n                    }\n                    if (separatorCopy.lastIndex === match.index) {\n                        separatorCopy.lastIndex++; // Avoid an infinite loop\n                    }\n                    match = separatorCopy.exec(string);\n                }\n                if (lastLastIndex === string.length) {\n                    if (lastLength || !separatorCopy.test('')) {\n                        pushCall(output, '');\n                    }\n                } else {\n                    pushCall(output, strSlice(string, lastLastIndex));\n                }\n                return output.length > splitLimit ? arraySlice(output, 0, splitLimit) : output;\n            };\n        }());\n\n    // [bugfix, chrome]\n    // If separator is undefined, then the result array contains just one String,\n    // which is the this value (converted to a String). If limit is not undefined,\n    // then the output array is truncated so that it contains no more than limit\n    // elements.\n    // \"0\".split(undefined, 0) -> []\n    } else if ('0'.split(void 0, 0).length) {\n        StringPrototype.split = function split(separator, limit) {\n            if (typeof separator === 'undefined' && limit === 0) {\n                return [];\n            }\n            return strSplit(this, separator, limit);\n        };\n    }\n\n    var str_replace = StringPrototype.replace;\n    var replaceReportsGroupsCorrectly = (function () {\n        var groups = [];\n        'x'.replace(/x(.)?/g, function (match, group) {\n            pushCall(groups, group);\n        });\n        return groups.length === 1 && typeof groups[0] === 'undefined';\n    }());\n\n    if (!replaceReportsGroupsCorrectly) {\n        StringPrototype.replace = function replace(searchValue, replaceValue) {\n            var isFn = isCallable(replaceValue);\n            var hasCapturingGroups = isRegex(searchValue) && (/\\)[*?]/).test(searchValue.source);\n            if (!isFn || !hasCapturingGroups) {\n                return str_replace.call(this, searchValue, replaceValue);\n            } else {\n                var wrappedReplaceValue = function (match) {\n                    var length = arguments.length;\n                    var originalLastIndex = searchValue.lastIndex;\n                    searchValue.lastIndex = 0;\n                    var args = searchValue.exec(match) || [];\n                    searchValue.lastIndex = originalLastIndex;\n                    pushCall(args, arguments[length - 2], arguments[length - 1]);\n                    return replaceValue.apply(this, args);\n                };\n                return str_replace.call(this, searchValue, wrappedReplaceValue);\n            }\n        };\n    }\n\n    // ECMA-262, 3rd B.2.3\n    // Not an ECMAScript standard, although ECMAScript 3rd Edition has a\n    // non-normative section suggesting uniform semantics and it should be\n    // normalized across all browsers\n    // [bugfix, IE lt 9] IE < 9 substr() with negative value not working in IE\n    var string_substr = StringPrototype.substr;\n    var hasNegativeSubstrBug = ''.substr && '0b'.substr(-1) !== 'b';\n    defineProperties(StringPrototype, {\n        substr: function substr(start, length) {\n            var normalizedStart = start;\n            if (start < 0) {\n                normalizedStart = max(this.length + start, 0);\n            }\n            return string_substr.call(this, normalizedStart, length);\n        }\n    }, hasNegativeSubstrBug);\n\n    // ES5 *********\n    // whitespace from: http://es5.github.io/#x*********\n    var ws = '\\x09\\x0A\\x0B\\x0C\\x0D\\x20\\xA0\\u1680\\u180E\\u2000\\u2001\\u2002\\u2003'\n        + '\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200A\\u202F\\u205F\\u3000\\u2028'\n        + '\\u2029\\uFEFF';\n    var zeroWidth = '\\u200b';\n    var wsRegexChars = '[' + ws + ']';\n    var trimBeginRegexp = new RegExp('^' + wsRegexChars + wsRegexChars + '*');\n    var trimEndRegexp = new RegExp(wsRegexChars + wsRegexChars + '*$');\n    var hasTrimWhitespaceBug = StringPrototype.trim && (ws.trim() || !zeroWidth.trim());\n    defineProperties(StringPrototype, {\n        // http://blog.stevenlevithan.com/archives/faster-trim-javascript\n        // http://perfectionkills.com/whitespace-deviations/\n        trim: function trim() {\n            if (typeof this === 'undefined' || this === null) {\n                throw new TypeError(\"can't convert \" + this + ' to object');\n            }\n            return $String(this).replace(trimBeginRegexp, '').replace(trimEndRegexp, '');\n        }\n    }, hasTrimWhitespaceBug);\n    var trim = call.bind(String.prototype.trim);\n\n    var hasLastIndexBug = StringPrototype.lastIndexOf && 'abcあい'.lastIndexOf('あい', 2) !== -1;\n    defineProperties(StringPrototype, {\n        lastIndexOf: function lastIndexOf(searchString) {\n            if (typeof this === 'undefined' || this === null) {\n                throw new TypeError(\"can't convert \" + this + ' to object');\n            }\n            var S = $String(this);\n            var searchStr = $String(searchString);\n            var numPos = arguments.length > 1 ? $Number(arguments[1]) : NaN;\n            var pos = isActualNaN(numPos) ? Infinity : ES.ToInteger(numPos);\n            var start = min(max(pos, 0), S.length);\n            var searchLen = searchStr.length;\n            var k = start + searchLen;\n            while (k > 0) {\n                k = max(0, k - searchLen);\n                var index = strIndexOf(strSlice(S, k, start + searchLen), searchStr);\n                if (index !== -1) {\n                    return k + index;\n                }\n            }\n            return -1;\n        }\n    }, hasLastIndexBug);\n\n    var originalLastIndexOf = StringPrototype.lastIndexOf;\n    defineProperties(StringPrototype, {\n        lastIndexOf: function lastIndexOf(searchString) {\n            return originalLastIndexOf.apply(this, arguments);\n        }\n    }, StringPrototype.lastIndexOf.length !== 1);\n\n    // ES-5 15.1.2.2\n    // eslint-disable-next-line radix\n    if (parseInt(ws + '08') !== 8 || parseInt(ws + '0x16') !== 22) {\n        /* global parseInt: true */\n        parseInt = (function (origParseInt) {\n            var hexRegex = /^[-+]?0[xX]/;\n            return function parseInt(str, radix) {\n                if (typeof str === 'symbol') {\n                    // handle Symbols in node 8.3/8.4\n                    // eslint-disable-next-line no-implicit-coercion, no-unused-expressions\n                    '' + str; // jscs:ignore disallowImplicitTypeConversion\n                }\n\n                var string = trim(String(str));\n                var defaultedRadix = $Number(radix) || (hexRegex.test(string) ? 16 : 10);\n                return origParseInt(string, defaultedRadix);\n            };\n        }(parseInt));\n    }\n\n    // https://es5.github.io/#x15.1.2.3\n    if (1 / parseFloat('-0') !== -Infinity) {\n        /* global parseFloat: true */\n        parseFloat = (function (origParseFloat) {\n            return function parseFloat(string) {\n                var inputString = trim(String(string));\n                var result = origParseFloat(inputString);\n                return result === 0 && strSlice(inputString, 0, 1) === '-' ? -0 : result;\n            };\n        }(parseFloat));\n    }\n\n    if (String(new RangeError('test')) !== 'RangeError: test') {\n        var errorToStringShim = function toString() {\n            if (typeof this === 'undefined' || this === null) {\n                throw new TypeError(\"can't convert \" + this + ' to object');\n            }\n            var name = this.name;\n            if (typeof name === 'undefined') {\n                name = 'Error';\n            } else if (typeof name !== 'string') {\n                name = $String(name);\n            }\n            var msg = this.message;\n            if (typeof msg === 'undefined') {\n                msg = '';\n            } else if (typeof msg !== 'string') {\n                msg = $String(msg);\n            }\n            if (!name) {\n                return msg;\n            }\n            if (!msg) {\n                return name;\n            }\n            return name + ': ' + msg;\n        };\n        // can't use defineProperties here because of toString enumeration issue in IE <= 8\n        Error.prototype.toString = errorToStringShim;\n    }\n\n    if (supportsDescriptors) {\n        var ensureNonEnumerable = function (obj, prop) {\n            if (isEnum(obj, prop)) {\n                var desc = Object.getOwnPropertyDescriptor(obj, prop);\n                if (desc.configurable) {\n                    desc.enumerable = false;\n                    Object.defineProperty(obj, prop, desc);\n                }\n            }\n        };\n        ensureNonEnumerable(Error.prototype, 'message');\n        if (Error.prototype.message !== '') {\n            Error.prototype.message = '';\n        }\n        ensureNonEnumerable(Error.prototype, 'name');\n    }\n\n    if (String(/a/mig) !== '/a/gim') {\n        var regexToString = function toString() {\n            var str = '/' + this.source + '/';\n            if (this.global) {\n                str += 'g';\n            }\n            if (this.ignoreCase) {\n                str += 'i';\n            }\n            if (this.multiline) {\n                str += 'm';\n            }\n            return str;\n        };\n        // can't use defineProperties here because of toString enumeration issue in IE <= 8\n        RegExp.prototype.toString = regexToString;\n    }\n}));\n", "/*!\n * https://github.com/es-shims/es5-shim\n * @license es5-shim Copyright 2009-2015 by contributors, MIT License\n * see https://github.com/es-shims/es5-shim/blob/master/LICENSE\n */\n\n// vim: ts=4 sts=4 sw=4 expandtab\n\n// Add semicolon to prevent IIFE from being passed as argument to concatenated code.\n;\n\n// UMD (Universal Module Definition)\n// see https://github.com/umdjs/umd/blob/master/templates/returnExports.js\n(function (root, factory) {\n    'use strict';\n\n    /* global define, exports, module */\n    if (typeof define === 'function' && define.amd) {\n        // AMD. Register as an anonymous module.\n        define(factory);\n    } else if (typeof exports === 'object') {\n        // Node. Does not work with strict CommonJS, but\n        // only CommonJS-like enviroments that support module.exports,\n        // like Node.\n        module.exports = factory();\n    } else {\n        // Browser globals (root is window)\n        root.returnExports = factory();\n    }\n}(this, function () {\n\n    var call = Function.call;\n    var prototypeOfObject = Object.prototype;\n    var owns = call.bind(prototypeOfObject.hasOwnProperty);\n    var isEnumerable = call.bind(prototypeOfObject.propertyIsEnumerable);\n    var toStr = call.bind(prototypeOfObject.toString);\n\n    // If JS engine supports accessors creating shortcuts.\n    var defineGetter;\n    var defineSetter;\n    var lookupGetter;\n    var lookupSetter;\n    var supportsAccessors = owns(prototypeOfObject, '__defineGetter__');\n    if (supportsAccessors) {\n        /* eslint-disable no-underscore-dangle, no-restricted-properties */\n        defineGetter = call.bind(prototypeOfObject.__defineGetter__);\n        defineSetter = call.bind(prototypeOfObject.__defineSetter__);\n        lookupGetter = call.bind(prototypeOfObject.__lookupGetter__);\n        lookupSetter = call.bind(prototypeOfObject.__lookupSetter__);\n        /* eslint-enable no-underscore-dangle, no-restricted-properties */\n    }\n\n    var isPrimitive = function isPrimitive(o) {\n        return o == null || (typeof o !== 'object' && typeof o !== 'function');\n    };\n\n    // ES5 15.2.3.2\n    // http://es5.github.com/#x15.2.3.2\n    if (!Object.getPrototypeOf) {\n        // https://github.com/es-shims/es5-shim/issues#issue/2\n        // http://ejohn.org/blog/objectgetprototypeof/\n        // recommended by fschaefer on github\n        //\n        // sure, and webreflection says ^_^\n        // ... this will nerever possibly return null\n        // ... Opera Mini breaks here with infinite loops\n        Object.getPrototypeOf = function getPrototypeOf(object) {\n            // eslint-disable-next-line no-proto\n            var proto = object.__proto__;\n            if (proto || proto === null) {\n                return proto;\n            } else if (toStr(object.constructor) === '[object Function]') {\n                return object.constructor.prototype;\n            } else if (object instanceof Object) {\n                return prototypeOfObject;\n            } else {\n                // Correctly return null for Objects created with `Object.create(null)`\n                // (shammed or native) or `{ __proto__: null}`.  Also returns null for\n                // cross-realm objects on browsers that lack `__proto__` support (like\n                // IE <11), but that's the best we can do.\n                return null;\n            }\n        };\n    }\n\n    // ES5 ********\n    // http://es5.github.com/#x********\n\n    var doesGetOwnPropertyDescriptorWork = function doesGetOwnPropertyDescriptorWork(object) {\n        try {\n            object.sentinel = 0;\n            return Object.getOwnPropertyDescriptor(object, 'sentinel').value === 0;\n        } catch (exception) {\n            return false;\n        }\n    };\n\n    // check whether getOwnPropertyDescriptor works if it's given. Otherwise, shim partially.\n    if (Object.defineProperty) {\n        var getOwnPropertyDescriptorWorksOnObject = doesGetOwnPropertyDescriptorWork({});\n        var getOwnPropertyDescriptorWorksOnDom = typeof document === 'undefined'\n            || doesGetOwnPropertyDescriptorWork(document.createElement('div'));\n        if (!getOwnPropertyDescriptorWorksOnDom || !getOwnPropertyDescriptorWorksOnObject) {\n            var getOwnPropertyDescriptorFallback = Object.getOwnPropertyDescriptor;\n        }\n    }\n\n    if (!Object.getOwnPropertyDescriptor || getOwnPropertyDescriptorFallback) {\n        var ERR_NON_OBJECT = 'Object.getOwnPropertyDescriptor called on a non-object: ';\n\n        /* eslint-disable no-proto */\n        Object.getOwnPropertyDescriptor = function getOwnPropertyDescriptor(object, property) {\n            if (isPrimitive(object)) {\n                throw new TypeError(ERR_NON_OBJECT + object);\n            }\n\n            // make a valiant attempt to use the real getOwnPropertyDescriptor\n            // for I8's DOM elements.\n            if (getOwnPropertyDescriptorFallback) {\n                try {\n                    return getOwnPropertyDescriptorFallback.call(Object, object, property);\n                } catch (exception) {\n                    // try the shim if the real one doesn't work\n                }\n            }\n\n            var descriptor;\n\n            // If object does not owns property return undefined immediately.\n            if (!owns(object, property)) {\n                return descriptor;\n            }\n\n            // If object has a property then it's for sure `configurable`, and\n            // probably `enumerable`. Detect enumerability though.\n            descriptor = {\n                enumerable: isEnumerable(object, property),\n                configurable: true\n            };\n\n            // If JS engine supports accessor properties then property may be a\n            // getter or setter.\n            if (supportsAccessors) {\n                // Unfortunately `__lookupGetter__` will return a getter even\n                // if object has own non getter property along with a same named\n                // inherited getter. To avoid misbehavior we temporary remove\n                // `__proto__` so that `__lookupGetter__` will return getter only\n                // if it's owned by an object.\n                var prototype = object.__proto__;\n                var notPrototypeOfObject = object !== prototypeOfObject;\n                // avoid recursion problem, breaking in Opera Mini when\n                // Object.getOwnPropertyDescriptor(Object.prototype, 'toString')\n                // or any other Object.prototype accessor\n                if (notPrototypeOfObject) {\n                    object.__proto__ = prototypeOfObject;\n                }\n\n                var getter = lookupGetter(object, property);\n                var setter = lookupSetter(object, property);\n\n                if (notPrototypeOfObject) {\n                    // Once we have getter and setter we can put values back.\n                    object.__proto__ = prototype;\n                }\n\n                if (getter || setter) {\n                    if (getter) {\n                        descriptor.get = getter;\n                    }\n                    if (setter) {\n                        descriptor.set = setter;\n                    }\n                    // If it was accessor property we're done and return here\n                    // in order to avoid adding `value` to the descriptor.\n                    return descriptor;\n                }\n            }\n\n            // If we got this far we know that object has an own property that is\n            // not an accessor so we set it as a value and return descriptor.\n            descriptor.value = object[property];\n            descriptor.writable = true;\n            return descriptor;\n        };\n        /* eslint-enable no-proto */\n    }\n\n    // ES5 ********\n    // http://es5.github.com/#x********\n    if (!Object.getOwnPropertyNames) {\n        Object.getOwnPropertyNames = function getOwnPropertyNames(object) {\n            return Object.keys(object);\n        };\n    }\n\n    // ES5 ********\n    // http://es5.github.com/#x********\n    if (!Object.create) {\n\n        // Contributed by Brandon Benvie, October, 2012\n        var createEmpty;\n        var supportsProto = !({ __proto__: null } instanceof Object);\n        // the following produces false positives\n        // in Opera Mini => not a reliable check\n        // Object.prototype.__proto__ === null\n\n        // Check for document.domain and active x support\n        // No need to use active x approach when document.domain is not set\n        // see https://github.com/es-shims/es5-shim/issues/150\n        // variation of https://github.com/kitcambridge/es5-shim/commit/4f738ac066346\n        /* global ActiveXObject */\n        var shouldUseActiveX = function shouldUseActiveX() {\n            // return early if document.domain not set\n            if (!document.domain) {\n                return false;\n            }\n\n            try {\n                return !!new ActiveXObject('htmlfile');\n            } catch (exception) {\n                return false;\n            }\n        };\n\n        // This supports IE8 when document.domain is used\n        // see https://github.com/es-shims/es5-shim/issues/150\n        // variation of https://github.com/kitcambridge/es5-shim/commit/4f738ac066346\n        var getEmptyViaActiveX = function getEmptyViaActiveX() {\n            var empty;\n            var xDoc;\n\n            xDoc = new ActiveXObject('htmlfile');\n\n            var script = 'script';\n            xDoc.write('<' + script + '></' + script + '>');\n            xDoc.close();\n\n            empty = xDoc.parentWindow.Object.prototype;\n            xDoc = null;\n\n            return empty;\n        };\n\n        // The original implementation using an iframe\n        // before the activex approach was added\n        // see https://github.com/es-shims/es5-shim/issues/150\n        var getEmptyViaIFrame = function getEmptyViaIFrame() {\n            var iframe = document.createElement('iframe');\n            var parent = document.body || document.documentElement;\n            var empty;\n\n            iframe.style.display = 'none';\n            parent.appendChild(iframe);\n            // eslint-disable-next-line no-script-url\n            iframe.src = 'javascript:';\n\n            empty = iframe.contentWindow.Object.prototype;\n            parent.removeChild(iframe);\n            iframe = null;\n\n            return empty;\n        };\n\n        /* global document */\n        if (supportsProto || typeof document === 'undefined') {\n            createEmpty = function () {\n                return { __proto__: null };\n            };\n        } else {\n            // In old IE __proto__ can't be used to manually set `null`, nor does\n            // any other method exist to make an object that inherits from nothing,\n            // aside from Object.prototype itself. Instead, create a new global\n            // object and *steal* its Object.prototype and strip it bare. This is\n            // used as the prototype to create nullary objects.\n            createEmpty = function () {\n                // Determine which approach to use\n                // see https://github.com/es-shims/es5-shim/issues/150\n                var empty = shouldUseActiveX() ? getEmptyViaActiveX() : getEmptyViaIFrame();\n\n                delete empty.constructor;\n                delete empty.hasOwnProperty;\n                delete empty.propertyIsEnumerable;\n                delete empty.isPrototypeOf;\n                delete empty.toLocaleString;\n                delete empty.toString;\n                delete empty.valueOf;\n\n                var Empty = function Empty() {};\n                Empty.prototype = empty;\n                // short-circuit future calls\n                createEmpty = function () {\n                    return new Empty();\n                };\n                return new Empty();\n            };\n        }\n\n        Object.create = function create(prototype, properties) {\n\n            var object;\n            var Type = function Type() {}; // An empty constructor.\n\n            if (prototype === null) {\n                object = createEmpty();\n            } else {\n                if (prototype !== null && isPrimitive(prototype)) {\n                    // In the native implementation `parent` can be `null`\n                    // OR *any* `instanceof Object`  (Object|Function|Array|RegExp|etc)\n                    // Use `typeof` tho, b/c in old IE, DOM elements are not `instanceof Object`\n                    // like they are in modern browsers. Using `Object.create` on DOM elements\n                    // is...err...probably inappropriate, but the native version allows for it.\n                    throw new TypeError('Object prototype may only be an Object or null'); // same msg as Chrome\n                }\n                Type.prototype = prototype;\n                object = new Type();\n                // IE has no built-in implementation of `Object.getPrototypeOf`\n                // neither `__proto__`, but this manually setting `__proto__` will\n                // guarantee that `Object.getPrototypeOf` will work as expected with\n                // objects created using `Object.create`\n                // eslint-disable-next-line no-proto\n                object.__proto__ = prototype;\n            }\n\n            if (properties !== void 0) {\n                Object.defineProperties(object, properties);\n            }\n\n            return object;\n        };\n    }\n\n    // ES5 15.2.3.6\n    // http://es5.github.com/#x15.2.3.6\n\n    // Patch for WebKit and IE8 standard mode\n    // Designed by hax <hax.github.com>\n    // related issue: https://github.com/es-shims/es5-shim/issues#issue/5\n    // IE8 Reference:\n    //     http://msdn.microsoft.com/en-us/library/dd282900.aspx\n    //     http://msdn.microsoft.com/en-us/library/dd229916.aspx\n    // WebKit Bugs:\n    //     https://bugs.webkit.org/show_bug.cgi?id=36423\n\n    var doesDefinePropertyWork = function doesDefinePropertyWork(object) {\n        try {\n            Object.defineProperty(object, 'sentinel', {});\n            return 'sentinel' in object;\n        } catch (exception) {\n            return false;\n        }\n    };\n\n    // check whether defineProperty works if it's given. Otherwise,\n    // shim partially.\n    if (Object.defineProperty) {\n        var definePropertyWorksOnObject = doesDefinePropertyWork({});\n        var definePropertyWorksOnDom = typeof document === 'undefined'\n            || doesDefinePropertyWork(document.createElement('div'));\n        if (!definePropertyWorksOnObject || !definePropertyWorksOnDom) {\n            var definePropertyFallback = Object.defineProperty,\n                definePropertiesFallback = Object.defineProperties;\n        }\n    }\n\n    if (!Object.defineProperty || definePropertyFallback) {\n        var ERR_NON_OBJECT_DESCRIPTOR = 'Property description must be an object: ';\n        var ERR_NON_OBJECT_TARGET = 'Object.defineProperty called on non-object: ';\n        var ERR_ACCESSORS_NOT_SUPPORTED = 'getters & setters can not be defined on this javascript engine';\n\n        Object.defineProperty = function defineProperty(object, property, descriptor) {\n            if (isPrimitive(object)) {\n                throw new TypeError(ERR_NON_OBJECT_TARGET + object);\n            }\n            if (isPrimitive(descriptor)) {\n                throw new TypeError(ERR_NON_OBJECT_DESCRIPTOR + descriptor);\n            }\n            // make a valiant attempt to use the real defineProperty\n            // for I8's DOM elements.\n            if (definePropertyFallback) {\n                try {\n                    return definePropertyFallback.call(Object, object, property, descriptor);\n                } catch (exception) {\n                    // try the shim if the real one doesn't work\n                }\n            }\n\n            // If it's a data property.\n            if ('value' in descriptor) {\n                // fail silently if 'writable', 'enumerable', or 'configurable'\n                // are requested but not supported\n                /*\n                // alternate approach:\n                if ( // can't implement these features; allow false but not true\n                    ('writable' in descriptor && !descriptor.writable) ||\n                    ('enumerable' in descriptor && !descriptor.enumerable) ||\n                    ('configurable' in descriptor && !descriptor.configurable)\n                ))\n                    throw new RangeError(\n                        'This implementation of Object.defineProperty does not support configurable, enumerable, or writable.'\n                    );\n                */\n\n                if (supportsAccessors && (lookupGetter(object, property) || lookupSetter(object, property))) {\n                    // As accessors are supported only on engines implementing\n                    // `__proto__` we can safely override `__proto__` while defining\n                    // a property to make sure that we don't hit an inherited\n                    // accessor.\n                    /* eslint-disable no-proto */\n                    var prototype = object.__proto__;\n                    object.__proto__ = prototypeOfObject;\n                    // Deleting a property anyway since getter / setter may be\n                    // defined on object itself.\n                    delete object[property];\n                    object[property] = descriptor.value;\n                    // Setting original `__proto__` back now.\n                    object.__proto__ = prototype;\n                    /* eslint-enable no-proto */\n                } else {\n                    object[property] = descriptor.value;\n                }\n            } else {\n                var hasGetter = 'get' in descriptor;\n                var hasSetter = 'set' in descriptor;\n                if (!supportsAccessors && (hasGetter || hasSetter)) {\n                    throw new TypeError(ERR_ACCESSORS_NOT_SUPPORTED);\n                }\n                // If we got that far then getters and setters can be defined !!\n                if (hasGetter) {\n                    defineGetter(object, property, descriptor.get);\n                }\n                if (hasSetter) {\n                    defineSetter(object, property, descriptor.set);\n                }\n            }\n            return object;\n        };\n    }\n\n    // ES5 ********\n    // http://es5.github.com/#x********\n    if (!Object.defineProperties || definePropertiesFallback) {\n        Object.defineProperties = function defineProperties(object, properties) {\n            // make a valiant attempt to use the real defineProperties\n            if (definePropertiesFallback) {\n                try {\n                    return definePropertiesFallback.call(Object, object, properties);\n                } catch (exception) {\n                    // try the shim if the real one doesn't work\n                }\n            }\n\n            Object.keys(properties).forEach(function (property) {\n                if (property !== '__proto__') {\n                    Object.defineProperty(object, property, properties[property]);\n                }\n            });\n            return object;\n        };\n    }\n\n    // ES5 ********\n    // http://es5.github.com/#x********\n    if (!Object.seal) {\n        Object.seal = function seal(object) {\n            if (Object(object) !== object) {\n                throw new TypeError('Object.seal can only be called on Objects.');\n            }\n            // this is misleading and breaks feature-detection, but\n            // allows \"securable\" code to \"gracefully\" degrade to working\n            // but insecure code.\n            return object;\n        };\n    }\n\n    // ES5 15.2.3.9\n    // http://es5.github.com/#x15.2.3.9\n    if (!Object.freeze) {\n        Object.freeze = function freeze(object) {\n            if (Object(object) !== object) {\n                throw new TypeError('Object.freeze can only be called on Objects.');\n            }\n            // this is misleading and breaks feature-detection, but\n            // allows \"securable\" code to \"gracefully\" degrade to working\n            // but insecure code.\n            return object;\n        };\n    }\n\n    // detect a Rhino bug and patch it\n    try {\n        Object.freeze(function () {});\n    } catch (exception) {\n        Object.freeze = (function (freezeObject) {\n            return function freeze(object) {\n                if (typeof object === 'function') {\n                    return object;\n                } else {\n                    return freezeObject(object);\n                }\n            };\n        }(Object.freeze));\n    }\n\n    // ES5 15.2.3.10\n    // http://es5.github.com/#x15.2.3.10\n    if (!Object.preventExtensions) {\n        Object.preventExtensions = function preventExtensions(object) {\n            if (Object(object) !== object) {\n                throw new TypeError('Object.preventExtensions can only be called on Objects.');\n            }\n            // this is misleading and breaks feature-detection, but\n            // allows \"securable\" code to \"gracefully\" degrade to working\n            // but insecure code.\n            return object;\n        };\n    }\n\n    // ES5 15.2.3.11\n    // http://es5.github.com/#x15.2.3.11\n    if (!Object.isSealed) {\n        Object.isSealed = function isSealed(object) {\n            if (Object(object) !== object) {\n                throw new TypeError('Object.isSealed can only be called on Objects.');\n            }\n            return false;\n        };\n    }\n\n    // ES5 15.2.3.12\n    // http://es5.github.com/#x15.2.3.12\n    if (!Object.isFrozen) {\n        Object.isFrozen = function isFrozen(object) {\n            if (Object(object) !== object) {\n                throw new TypeError('Object.isFrozen can only be called on Objects.');\n            }\n            return false;\n        };\n    }\n\n    // ES5 15.2.3.13\n    // http://es5.github.com/#x15.2.3.13\n    if (!Object.isExtensible) {\n        Object.isExtensible = function isExtensible(object) {\n            // 1. If Type(O) is not Object throw a TypeError exception.\n            if (Object(object) !== object) {\n                throw new TypeError('Object.isExtensible can only be called on Objects.');\n            }\n            // 2. Return the Boolean value of the [[Extensible]] internal property of O.\n            var name = '';\n            while (owns(object, name)) {\n                name += '?';\n            }\n            object[name] = true;\n            var returnValue = owns(object, name);\n            delete object[name];\n            return returnValue;\n        };\n    }\n\n}));\n", "/*!\n * https://github.com/paulmillr/es6-shim\n * @license es6-shim Copyright 2013-2016 by <PERSON> (http://paulmillr.com)\n *   and contributors,  MIT License\n * es6-shim: v0.35.4\n * see https://github.com/paulmillr/es6-shim/blob/0.35.3/LICENSE\n * Details and documentation:\n * https://github.com/paulmillr/es6-shim/\n */\n\n// UMD (Universal Module Definition)\n// see https://github.com/umdjs/umd/blob/master/returnExports.js\n(function (root, factory) {\n  /*global define, module, exports */\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(factory);\n  } else if (typeof exports === 'object') {\n    // Node. Does not work with strict CommonJS, but\n    // only CommonJS-like environments that support module.exports,\n    // like Node.\n    module.exports = factory();\n  } else {\n    // Browser globals (root is window)\n    root.returnExports = factory();\n  }\n}(this, function () {\n  'use strict';\n\n  var _apply = Function.call.bind(Function.apply);\n  var _call = Function.call.bind(Function.call);\n  var isArray = Array.isArray;\n  var keys = Object.keys;\n\n  var not = function notThunker(func) {\n    return function notThunk() {\n      return !_apply(func, this, arguments);\n    };\n  };\n  var throwsError = function (func) {\n    try {\n      func();\n      return false;\n    } catch (e) {\n      return true;\n    }\n  };\n  var valueOrFalseIfThrows = function valueOrFalseIfThrows(func) {\n    try {\n      return func();\n    } catch (e) {\n      return false;\n    }\n  };\n\n  var isCallableWithoutNew = not(throwsError);\n  var arePropertyDescriptorsSupported = function () {\n    // if Object.defineProperty exists but throws, it's IE 8\n    return !throwsError(function () {\n      return Object.defineProperty({}, 'x', { get: function () { } }); // eslint-disable-line getter-return\n    });\n  };\n  var supportsDescriptors = !!Object.defineProperty && arePropertyDescriptorsSupported();\n  var functionsHaveNames = (function foo() {}).name === 'foo'; // eslint-disable-line no-extra-parens\n\n  var _forEach = Function.call.bind(Array.prototype.forEach);\n  var _reduce = Function.call.bind(Array.prototype.reduce);\n  var _filter = Function.call.bind(Array.prototype.filter);\n  var _some = Function.call.bind(Array.prototype.some);\n\n  var defineProperty = function (object, name, value, force) {\n    if (!force && name in object) { return; }\n    if (supportsDescriptors) {\n      Object.defineProperty(object, name, {\n        configurable: true,\n        enumerable: false,\n        writable: true,\n        value: value\n      });\n    } else {\n      object[name] = value;\n    }\n  };\n\n  // Define configurable, writable and non-enumerable props\n  // if they don’t exist.\n  var defineProperties = function (object, map, forceOverride) {\n    _forEach(keys(map), function (name) {\n      var method = map[name];\n      defineProperty(object, name, method, !!forceOverride);\n    });\n  };\n\n  var _toString = Function.call.bind(Object.prototype.toString);\n  var isCallable = typeof /abc/ === 'function' ? function IsCallableSlow(x) {\n    // Some old browsers (IE, FF) say that typeof /abc/ === 'function'\n    return typeof x === 'function' && _toString(x) === '[object Function]';\n  } : function IsCallableFast(x) { return typeof x === 'function'; };\n\n  var Value = {\n    getter: function (object, name, getter) {\n      if (!supportsDescriptors) {\n        throw new TypeError('getters require true ES5 support');\n      }\n      Object.defineProperty(object, name, {\n        configurable: true,\n        enumerable: false,\n        get: getter\n      });\n    },\n    proxy: function (originalObject, key, targetObject) {\n      if (!supportsDescriptors) {\n        throw new TypeError('getters require true ES5 support');\n      }\n      var originalDescriptor = Object.getOwnPropertyDescriptor(originalObject, key);\n      Object.defineProperty(targetObject, key, {\n        configurable: originalDescriptor.configurable,\n        enumerable: originalDescriptor.enumerable,\n        get: function getKey() { return originalObject[key]; },\n        set: function setKey(value) { originalObject[key] = value; }\n      });\n    },\n    redefine: function (object, property, newValue) {\n      if (supportsDescriptors) {\n        var descriptor = Object.getOwnPropertyDescriptor(object, property);\n        descriptor.value = newValue;\n        Object.defineProperty(object, property, descriptor);\n      } else {\n        object[property] = newValue;\n      }\n    },\n    defineByDescriptor: function (object, property, descriptor) {\n      if (supportsDescriptors) {\n        Object.defineProperty(object, property, descriptor);\n      } else if ('value' in descriptor) {\n        object[property] = descriptor.value;\n      }\n    },\n    preserveToString: function (target, source) {\n      if (source && isCallable(source.toString)) {\n        defineProperty(target, 'toString', source.toString.bind(source), true);\n      }\n    }\n  };\n\n  // Simple shim for Object.create on ES3 browsers\n  // (unlike real shim, no attempt to support `prototype === null`)\n  var create = Object.create || function (prototype, properties) {\n    var Prototype = function Prototype() {};\n    Prototype.prototype = prototype;\n    var object = new Prototype();\n    if (typeof properties !== 'undefined') {\n      keys(properties).forEach(function (key) {\n        Value.defineByDescriptor(object, key, properties[key]);\n      });\n    }\n    return object;\n  };\n\n  var supportsSubclassing = function (C, f) {\n    if (!Object.setPrototypeOf) { return false; /* skip test on IE < 11 */ }\n    return valueOrFalseIfThrows(function () {\n      var Sub = function Subclass(arg) {\n        var o = new C(arg);\n        Object.setPrototypeOf(o, Subclass.prototype);\n        return o;\n      };\n      Object.setPrototypeOf(Sub, C);\n      Sub.prototype = create(C.prototype, {\n        constructor: { value: Sub }\n      });\n      return f(Sub);\n    });\n  };\n\n  var getGlobal = function () {\n    /* global self, window, global */\n    // the only reliable means to get the global object is\n    // `Function('return this')()`\n    // However, this causes CSP violations in Chrome apps.\n    if (typeof self !== 'undefined') { return self; }\n    if (typeof window !== 'undefined') { return window; }\n    if (typeof global !== 'undefined') { return global; }\n    throw new Error('unable to locate global object');\n  };\n\n  var globals = getGlobal();\n  var globalIsFinite = globals.isFinite;\n  var _indexOf = Function.call.bind(String.prototype.indexOf);\n  var _arrayIndexOfApply = Function.apply.bind(Array.prototype.indexOf);\n  var _concat = Function.call.bind(Array.prototype.concat);\n  // var _sort = Function.call.bind(Array.prototype.sort);\n  var _strSlice = Function.call.bind(String.prototype.slice);\n  var _push = Function.call.bind(Array.prototype.push);\n  var _pushApply = Function.apply.bind(Array.prototype.push);\n  var _shift = Function.call.bind(Array.prototype.shift);\n  var _max = Math.max;\n  var _min = Math.min;\n  var _floor = Math.floor;\n  var _abs = Math.abs;\n  var _exp = Math.exp;\n  var _log = Math.log;\n  var _sqrt = Math.sqrt;\n  var _hasOwnProperty = Function.call.bind(Object.prototype.hasOwnProperty);\n  var ArrayIterator; // make our implementation private\n  var noop = function () {};\n\n  var OrigMap = globals.Map;\n  var origMapDelete = OrigMap && OrigMap.prototype['delete'];\n  var origMapGet = OrigMap && OrigMap.prototype.get;\n  var origMapHas = OrigMap && OrigMap.prototype.has;\n  var origMapSet = OrigMap && OrigMap.prototype.set;\n\n  var Symbol = globals.Symbol || {};\n  var symbolSpecies = Symbol.species || '@@species';\n\n  var numberIsNaN = Number.isNaN || function isNaN(value) {\n    // NaN !== NaN, but they are identical.\n    // NaNs are the only non-reflexive value, i.e., if x !== x,\n    // then x is NaN.\n    // isNaN is broken: it converts its argument to number, so\n    // isNaN('foo') => true\n    return value !== value;\n  };\n  var numberIsFinite = Number.isFinite || function isFinite(value) {\n    return typeof value === 'number' && globalIsFinite(value);\n  };\n  var _sign = isCallable(Math.sign) ? Math.sign : function sign(value) {\n    var number = Number(value);\n    if (number === 0) { return number; }\n    if (numberIsNaN(number)) { return number; }\n    return number < 0 ? -1 : 1;\n  };\n  var _log1p = function log1p(value) {\n    var x = Number(value);\n    if (x < -1 || numberIsNaN(x)) { return NaN; }\n    if (x === 0 || x === Infinity) { return x; }\n    if (x === -1) { return -Infinity; }\n\n    return (1 + x) - 1 === 0 ? x : x * (_log(1 + x) / ((1 + x) - 1));\n  };\n\n  // taken directly from https://github.com/ljharb/is-arguments/blob/master/index.js\n  // can be replaced with require('is-arguments') if we ever use a build process instead\n  var isStandardArguments = function isArguments(value) {\n    return _toString(value) === '[object Arguments]';\n  };\n  var isLegacyArguments = function isArguments(value) {\n    return value !== null &&\n      typeof value === 'object' &&\n      typeof value.length === 'number' &&\n      value.length >= 0 &&\n      _toString(value) !== '[object Array]' &&\n      _toString(value.callee) === '[object Function]';\n  };\n  var isArguments = isStandardArguments(arguments) ? isStandardArguments : isLegacyArguments;\n\n  var Type = {\n    primitive: function (x) { return x === null || (typeof x !== 'function' && typeof x !== 'object'); },\n    string: function (x) { return _toString(x) === '[object String]'; },\n    regex: function (x) { return _toString(x) === '[object RegExp]'; },\n    symbol: function (x) {\n      return typeof globals.Symbol === 'function' && typeof x === 'symbol';\n    }\n  };\n\n  var overrideNative = function overrideNative(object, property, replacement) {\n    var original = object[property];\n    defineProperty(object, property, replacement, true);\n    Value.preserveToString(object[property], original);\n  };\n\n  // eslint-disable-next-line no-restricted-properties\n  var hasSymbols = typeof Symbol === 'function' && typeof Symbol['for'] === 'function' && Type.symbol(Symbol());\n\n  // This is a private name in the es6 spec, equal to '[Symbol.iterator]'\n  // we're going to use an arbitrary _-prefixed name to make our shims\n  // work properly with each other, even though we don't have full Iterator\n  // support.  That is, `Array.from(map.keys())` will work, but we don't\n  // pretend to export a \"real\" Iterator interface.\n  var $iterator$ = Type.symbol(Symbol.iterator) ? Symbol.iterator : '_es6-shim iterator_';\n  // Firefox ships a partial implementation using the name @@iterator.\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=907077#c14\n  // So use that name if we detect it.\n  if (globals.Set && typeof new globals.Set()['@@iterator'] === 'function') {\n    $iterator$ = '@@iterator';\n  }\n\n  // Reflect\n  if (!globals.Reflect) {\n    defineProperty(globals, 'Reflect', {}, true);\n  }\n  var Reflect = globals.Reflect;\n\n  var $String = String;\n\n  /* global document */\n  var domAll = (typeof document === 'undefined' || !document) ? null : document.all;\n  /* jshint eqnull:true */\n  var isNullOrUndefined = domAll == null ? function isNullOrUndefined(x) {\n    /* jshint eqnull:true */\n    return x == null;\n  } : function isNullOrUndefinedAndNotDocumentAll(x) {\n    /* jshint eqnull:true */\n    return x == null && x !== domAll;\n  };\n\n  var ES = {\n    // http://www.ecma-international.org/ecma-262/6.0/#sec-call\n    Call: function Call(F, V) {\n      var args = arguments.length > 2 ? arguments[2] : [];\n      if (!ES.IsCallable(F)) {\n        throw new TypeError(F + ' is not a function');\n      }\n      return _apply(F, V, args);\n    },\n\n    RequireObjectCoercible: function (x, optMessage) {\n      if (isNullOrUndefined(x)) {\n        throw new TypeError(optMessage || 'Cannot call method on ' + x);\n      }\n      return x;\n    },\n\n    // This might miss the \"(non-standard exotic and does not implement\n    // [[Call]])\" case from\n    // http://www.ecma-international.org/ecma-262/6.0/#sec-typeof-operator-runtime-semantics-evaluation\n    // but we can't find any evidence these objects exist in practice.\n    // If we find some in the future, you could test `Object(x) === x`,\n    // which is reliable according to\n    // http://www.ecma-international.org/ecma-262/6.0/#sec-toobject\n    // but is not well optimized by runtimes and creates an object\n    // whenever it returns false, and thus is very slow.\n    TypeIsObject: function (x) {\n      if (x === void 0 || x === null || x === true || x === false) {\n        return false;\n      }\n      return typeof x === 'function' || typeof x === 'object' || x === domAll;\n    },\n\n    ToObject: function (o, optMessage) {\n      return Object(ES.RequireObjectCoercible(o, optMessage));\n    },\n\n    IsCallable: isCallable,\n\n    IsConstructor: function (x) {\n      // We can't tell callables from constructors in ES5\n      return ES.IsCallable(x);\n    },\n\n    ToInt32: function (x) {\n      return ES.ToNumber(x) >> 0;\n    },\n\n    ToUint32: function (x) {\n      return ES.ToNumber(x) >>> 0;\n    },\n\n    ToNumber: function (value) {\n      if (_toString(value) === '[object Symbol]') {\n        throw new TypeError('Cannot convert a Symbol value to a number');\n      }\n      return +value;\n    },\n\n    ToInteger: function (value) {\n      var number = ES.ToNumber(value);\n      if (numberIsNaN(number)) { return 0; }\n      if (number === 0 || !numberIsFinite(number)) { return number; }\n      return (number > 0 ? 1 : -1) * _floor(_abs(number));\n    },\n\n    ToLength: function (value) {\n      var len = ES.ToInteger(value);\n      if (len <= 0) { return 0; } // includes converting -0 to +0\n      if (len > Number.MAX_SAFE_INTEGER) { return Number.MAX_SAFE_INTEGER; }\n      return len;\n    },\n\n    SameValue: function (a, b) {\n      if (a === b) {\n        // 0 === -0, but they are not identical.\n        if (a === 0) { return 1 / a === 1 / b; }\n        return true;\n      }\n      return numberIsNaN(a) && numberIsNaN(b);\n    },\n\n    SameValueZero: function (a, b) {\n      // same as SameValue except for SameValueZero(+0, -0) == true\n      return (a === b) || (numberIsNaN(a) && numberIsNaN(b));\n    },\n\n    IsIterable: function (o) {\n      return ES.TypeIsObject(o) && (typeof o[$iterator$] !== 'undefined' || isArguments(o));\n    },\n\n    GetIterator: function (o) {\n      if (isArguments(o)) {\n        // special case support for `arguments`\n        return new ArrayIterator(o, 'value');\n      }\n      var itFn = ES.GetMethod(o, $iterator$);\n      if (!ES.IsCallable(itFn)) {\n        // Better diagnostics if itFn is null or undefined\n        throw new TypeError('value is not an iterable');\n      }\n      var it = ES.Call(itFn, o);\n      if (!ES.TypeIsObject(it)) {\n        throw new TypeError('bad iterator');\n      }\n      return it;\n    },\n\n    GetMethod: function (o, p) {\n      var func = ES.ToObject(o)[p];\n      if (isNullOrUndefined(func)) {\n        return void 0;\n      }\n      if (!ES.IsCallable(func)) {\n        throw new TypeError('Method not callable: ' + p);\n      }\n      return func;\n    },\n\n    IteratorComplete: function (iterResult) {\n      return !!iterResult.done;\n    },\n\n    IteratorClose: function (iterator, completionIsThrow) {\n      var returnMethod = ES.GetMethod(iterator, 'return');\n      if (returnMethod === void 0) {\n        return;\n      }\n      var innerResult, innerException;\n      try {\n        innerResult = ES.Call(returnMethod, iterator);\n      } catch (e) {\n        innerException = e;\n      }\n      if (completionIsThrow) {\n        return;\n      }\n      if (innerException) {\n        throw innerException;\n      }\n      if (!ES.TypeIsObject(innerResult)) {\n        throw new TypeError(\"Iterator's return method returned a non-object.\");\n      }\n    },\n\n    IteratorNext: function (it) {\n      var result = arguments.length > 1 ? it.next(arguments[1]) : it.next();\n      if (!ES.TypeIsObject(result)) {\n        throw new TypeError('bad iterator');\n      }\n      return result;\n    },\n\n    IteratorStep: function (it) {\n      var result = ES.IteratorNext(it);\n      var done = ES.IteratorComplete(result);\n      return done ? false : result;\n    },\n\n    Construct: function (C, args, newTarget, isES6internal) {\n      var target = typeof newTarget === 'undefined' ? C : newTarget;\n\n      if (!isES6internal && Reflect.construct) {\n        // Try to use Reflect.construct if available\n        return Reflect.construct(C, args, target);\n      }\n      // OK, we have to fake it.  This will only work if the\n      // C.[[ConstructorKind]] == \"base\" -- but that's the only\n      // kind we can make in ES5 code anyway.\n\n      // OrdinaryCreateFromConstructor(target, \"%ObjectPrototype%\")\n      var proto = target.prototype;\n      if (!ES.TypeIsObject(proto)) {\n        proto = Object.prototype;\n      }\n      var obj = create(proto);\n      // Call the constructor.\n      var result = ES.Call(C, obj, args);\n      return ES.TypeIsObject(result) ? result : obj;\n    },\n\n    SpeciesConstructor: function (O, defaultConstructor) {\n      var C = O.constructor;\n      if (C === void 0) {\n        return defaultConstructor;\n      }\n      if (!ES.TypeIsObject(C)) {\n        throw new TypeError('Bad constructor');\n      }\n      var S = C[symbolSpecies];\n      if (isNullOrUndefined(S)) {\n        return defaultConstructor;\n      }\n      if (!ES.IsConstructor(S)) {\n        throw new TypeError('Bad @@species');\n      }\n      return S;\n    },\n\n    CreateHTML: function (string, tag, attribute, value) {\n      var S = ES.ToString(string);\n      var p1 = '<' + tag;\n      if (attribute !== '') {\n        var V = ES.ToString(value);\n        var escapedV = V.replace(/\"/g, '&quot;');\n        p1 += ' ' + attribute + '=\"' + escapedV + '\"';\n      }\n      var p2 = p1 + '>';\n      var p3 = p2 + S;\n      return p3 + '</' + tag + '>';\n    },\n\n    IsRegExp: function IsRegExp(argument) {\n      if (!ES.TypeIsObject(argument)) {\n        return false;\n      }\n      var isRegExp = argument[Symbol.match];\n      if (typeof isRegExp !== 'undefined') {\n        return !!isRegExp;\n      }\n      return Type.regex(argument);\n    },\n\n    ToString: function ToString(string) {\n      return $String(string);\n    }\n  };\n\n  // Well-known Symbol shims\n  if (supportsDescriptors && hasSymbols) {\n    var defineWellKnownSymbol = function defineWellKnownSymbol(name) {\n      if (Type.symbol(Symbol[name])) {\n        return Symbol[name];\n      }\n      // eslint-disable-next-line no-restricted-properties\n      var sym = Symbol['for']('Symbol.' + name);\n      Object.defineProperty(Symbol, name, {\n        configurable: false,\n        enumerable: false,\n        writable: false,\n        value: sym\n      });\n      return sym;\n    };\n    if (!Type.symbol(Symbol.search)) {\n      var symbolSearch = defineWellKnownSymbol('search');\n      var originalSearch = String.prototype.search;\n      defineProperty(RegExp.prototype, symbolSearch, function search(string) {\n        return ES.Call(originalSearch, string, [this]);\n      });\n      var searchShim = function search(regexp) {\n        var O = ES.RequireObjectCoercible(this);\n        if (!isNullOrUndefined(regexp)) {\n          var searcher = ES.GetMethod(regexp, symbolSearch);\n          if (typeof searcher !== 'undefined') {\n            return ES.Call(searcher, regexp, [O]);\n          }\n        }\n        return ES.Call(originalSearch, O, [ES.ToString(regexp)]);\n      };\n      overrideNative(String.prototype, 'search', searchShim);\n    }\n    if (!Type.symbol(Symbol.replace)) {\n      var symbolReplace = defineWellKnownSymbol('replace');\n      var originalReplace = String.prototype.replace;\n      defineProperty(RegExp.prototype, symbolReplace, function replace(string, replaceValue) {\n        return ES.Call(originalReplace, string, [this, replaceValue]);\n      });\n      var replaceShim = function replace(searchValue, replaceValue) {\n        var O = ES.RequireObjectCoercible(this);\n        if (!isNullOrUndefined(searchValue)) {\n          var replacer = ES.GetMethod(searchValue, symbolReplace);\n          if (typeof replacer !== 'undefined') {\n            return ES.Call(replacer, searchValue, [O, replaceValue]);\n          }\n        }\n        return ES.Call(originalReplace, O, [ES.ToString(searchValue), replaceValue]);\n      };\n      overrideNative(String.prototype, 'replace', replaceShim);\n    }\n    if (!Type.symbol(Symbol.split)) {\n      var symbolSplit = defineWellKnownSymbol('split');\n      var originalSplit = String.prototype.split;\n      defineProperty(RegExp.prototype, symbolSplit, function split(string, limit) {\n        return ES.Call(originalSplit, string, [this, limit]);\n      });\n      var splitShim = function split(separator, limit) {\n        var O = ES.RequireObjectCoercible(this);\n        if (!isNullOrUndefined(separator)) {\n          var splitter = ES.GetMethod(separator, symbolSplit);\n          if (typeof splitter !== 'undefined') {\n            return ES.Call(splitter, separator, [O, limit]);\n          }\n        }\n        return ES.Call(originalSplit, O, [ES.ToString(separator), limit]);\n      };\n      overrideNative(String.prototype, 'split', splitShim);\n    }\n    var symbolMatchExists = Type.symbol(Symbol.match);\n    var stringMatchIgnoresSymbolMatch = symbolMatchExists && (function () {\n      // Firefox 41, through Nightly 45 has Symbol.match, but String#match ignores it.\n      // Firefox 40 and below have Symbol.match but String#match works fine.\n      var o = {};\n      o[Symbol.match] = function () { return 42; };\n      return 'a'.match(o) !== 42;\n    }());\n    if (!symbolMatchExists || stringMatchIgnoresSymbolMatch) {\n      var symbolMatch = defineWellKnownSymbol('match');\n\n      var originalMatch = String.prototype.match;\n      defineProperty(RegExp.prototype, symbolMatch, function match(string) {\n        return ES.Call(originalMatch, string, [this]);\n      });\n\n      var matchShim = function match(regexp) {\n        var O = ES.RequireObjectCoercible(this);\n        if (!isNullOrUndefined(regexp)) {\n          var matcher = ES.GetMethod(regexp, symbolMatch);\n          if (typeof matcher !== 'undefined') {\n            return ES.Call(matcher, regexp, [O]);\n          }\n        }\n        return ES.Call(originalMatch, O, [ES.ToString(regexp)]);\n      };\n      overrideNative(String.prototype, 'match', matchShim);\n    }\n  }\n\n  var wrapConstructor = function wrapConstructor(original, replacement, keysToSkip) {\n    Value.preserveToString(replacement, original);\n    if (Object.setPrototypeOf) {\n      // sets up proper prototype chain where possible\n      Object.setPrototypeOf(original, replacement);\n    }\n    if (supportsDescriptors) {\n      _forEach(Object.getOwnPropertyNames(original), function (key) {\n        if (key in noop || keysToSkip[key]) { return; }\n        Value.proxy(original, key, replacement);\n      });\n    } else {\n      _forEach(Object.keys(original), function (key) {\n        if (key in noop || keysToSkip[key]) { return; }\n        replacement[key] = original[key];\n      });\n    }\n    replacement.prototype = original.prototype;\n    Value.redefine(original.prototype, 'constructor', replacement);\n  };\n\n  var defaultSpeciesGetter = function () { return this; };\n  var addDefaultSpecies = function (C) {\n    if (supportsDescriptors && !_hasOwnProperty(C, symbolSpecies)) {\n      Value.getter(C, symbolSpecies, defaultSpeciesGetter);\n    }\n  };\n\n  var addIterator = function (prototype, impl) {\n    var implementation = impl || function iterator() { return this; };\n    defineProperty(prototype, $iterator$, implementation);\n    if (!prototype[$iterator$] && Type.symbol($iterator$)) {\n      // implementations are buggy when $iterator$ is a Symbol\n      prototype[$iterator$] = implementation;\n    }\n  };\n\n  var createDataProperty = function createDataProperty(object, name, value) {\n    if (supportsDescriptors) {\n      Object.defineProperty(object, name, {\n        configurable: true,\n        enumerable: true,\n        writable: true,\n        value: value\n      });\n    } else {\n      object[name] = value;\n    }\n  };\n  var createDataPropertyOrThrow = function createDataPropertyOrThrow(object, name, value) {\n    createDataProperty(object, name, value);\n    if (!ES.SameValue(object[name], value)) {\n      throw new TypeError('property is nonconfigurable');\n    }\n  };\n\n  var emulateES6construct = function (o, defaultNewTarget, defaultProto, slots) {\n    // This is an es5 approximation to es6 construct semantics.  in es6,\n    // 'new Foo' invokes Foo.[[Construct]] which (for almost all objects)\n    // just sets the internal variable NewTarget (in es6 syntax `new.target`)\n    // to Foo and then returns Foo().\n\n    // Many ES6 object then have constructors of the form:\n    // 1. If NewTarget is undefined, throw a TypeError exception\n    // 2. Let xxx by OrdinaryCreateFromConstructor(NewTarget, yyy, zzz)\n\n    // So we're going to emulate those first two steps.\n    if (!ES.TypeIsObject(o)) {\n      throw new TypeError('Constructor requires `new`: ' + defaultNewTarget.name);\n    }\n    var proto = defaultNewTarget.prototype;\n    if (!ES.TypeIsObject(proto)) {\n      proto = defaultProto;\n    }\n    var obj = create(proto);\n    for (var name in slots) {\n      if (_hasOwnProperty(slots, name)) {\n        var value = slots[name];\n        defineProperty(obj, name, value, true);\n      }\n    }\n    return obj;\n  };\n\n  // Firefox 31 reports this function's length as 0\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=1062484\n  if (String.fromCodePoint && String.fromCodePoint.length !== 1) {\n    var originalFromCodePoint = String.fromCodePoint;\n    overrideNative(String, 'fromCodePoint', function fromCodePoint(codePoints) {\n      return ES.Call(originalFromCodePoint, this, arguments);\n    });\n  }\n\n  var StringShims = {\n    fromCodePoint: function fromCodePoint(codePoints) {\n      var result = [];\n      var next;\n      for (var i = 0, length = arguments.length; i < length; i++) {\n        next = Number(arguments[i]);\n        if (!ES.SameValue(next, ES.ToInteger(next)) || next < 0 || next > 0x10FFFF) {\n          throw new RangeError('Invalid code point ' + next);\n        }\n\n        if (next < 0x10000) {\n          _push(result, String.fromCharCode(next));\n        } else {\n          next -= 0x10000;\n          _push(result, String.fromCharCode((next >> 10) + 0xD800));\n          _push(result, String.fromCharCode((next % 0x400) + 0xDC00));\n        }\n      }\n      return result.join('');\n    },\n\n    raw: function raw(callSite) {\n      var cooked = ES.ToObject(callSite, 'bad callSite');\n      var rawString = ES.ToObject(cooked.raw, 'bad raw value');\n      var len = rawString.length;\n      var literalsegments = ES.ToLength(len);\n      if (literalsegments <= 0) {\n        return '';\n      }\n\n      var stringElements = [];\n      var nextIndex = 0;\n      var nextKey, next, nextSeg, nextSub;\n      while (nextIndex < literalsegments) {\n        nextKey = ES.ToString(nextIndex);\n        nextSeg = ES.ToString(rawString[nextKey]);\n        _push(stringElements, nextSeg);\n        if (nextIndex + 1 >= literalsegments) {\n          break;\n        }\n        next = nextIndex + 1 < arguments.length ? arguments[nextIndex + 1] : '';\n        nextSub = ES.ToString(next);\n        _push(stringElements, nextSub);\n        nextIndex += 1;\n      }\n      return stringElements.join('');\n    }\n  };\n  if (String.raw && String.raw({ raw: { 0: 'x', 1: 'y', length: 2 } }) !== 'xy') {\n    // IE 11 TP has a broken String.raw implementation\n    overrideNative(String, 'raw', StringShims.raw);\n  }\n  defineProperties(String, StringShims);\n\n  // Fast repeat, uses the `Exponentiation by squaring` algorithm.\n  // Perf: http://jsperf.com/string-repeat2/2\n  var stringRepeat = function repeat(s, times) {\n    if (times < 1) { return ''; }\n    if (times % 2) { return repeat(s, times - 1) + s; }\n    var half = repeat(s, times / 2);\n    return half + half;\n  };\n  var stringMaxLength = Infinity;\n\n  var StringPrototypeShims = {\n    repeat: function repeat(times) {\n      var thisStr = ES.ToString(ES.RequireObjectCoercible(this));\n      var numTimes = ES.ToInteger(times);\n      if (numTimes < 0 || numTimes >= stringMaxLength) {\n        throw new RangeError('repeat count must be less than infinity and not overflow maximum string size');\n      }\n      return stringRepeat(thisStr, numTimes);\n    },\n\n    startsWith: function startsWith(searchString) {\n      var S = ES.ToString(ES.RequireObjectCoercible(this));\n      if (ES.IsRegExp(searchString)) {\n        throw new TypeError('Cannot call method \"startsWith\" with a regex');\n      }\n      var searchStr = ES.ToString(searchString);\n      var position;\n      if (arguments.length > 1) {\n        position = arguments[1];\n      }\n      var start = _max(ES.ToInteger(position), 0);\n      return _strSlice(S, start, start + searchStr.length) === searchStr;\n    },\n\n    endsWith: function endsWith(searchString) {\n      var S = ES.ToString(ES.RequireObjectCoercible(this));\n      if (ES.IsRegExp(searchString)) {\n        throw new TypeError('Cannot call method \"endsWith\" with a regex');\n      }\n      var searchStr = ES.ToString(searchString);\n      var len = S.length;\n      var endPosition;\n      if (arguments.length > 1) {\n        endPosition = arguments[1];\n      }\n      var pos = typeof endPosition === 'undefined' ? len : ES.ToInteger(endPosition);\n      var end = _min(_max(pos, 0), len);\n      return _strSlice(S, end - searchStr.length, end) === searchStr;\n    },\n\n    includes: function includes(searchString) {\n      if (ES.IsRegExp(searchString)) {\n        throw new TypeError('\"includes\" does not accept a RegExp');\n      }\n      var searchStr = ES.ToString(searchString);\n      var position;\n      if (arguments.length > 1) {\n        position = arguments[1];\n      }\n      // Somehow this trick makes method 100% compat with the spec.\n      return _indexOf(this, searchStr, position) !== -1;\n    },\n\n    codePointAt: function codePointAt(pos) {\n      var thisStr = ES.ToString(ES.RequireObjectCoercible(this));\n      var position = ES.ToInteger(pos);\n      var length = thisStr.length;\n      if (position >= 0 && position < length) {\n        var first = thisStr.charCodeAt(position);\n        var isEnd = position + 1 === length;\n        if (first < 0xD800 || first > 0xDBFF || isEnd) { return first; }\n        var second = thisStr.charCodeAt(position + 1);\n        if (second < 0xDC00 || second > 0xDFFF) { return first; }\n        return ((first - 0xD800) * 1024) + (second - 0xDC00) + 0x10000;\n      }\n    }\n  };\n  if (String.prototype.includes && 'a'.includes('a', Infinity) !== false) {\n    overrideNative(String.prototype, 'includes', StringPrototypeShims.includes);\n  }\n\n  if (String.prototype.startsWith && String.prototype.endsWith) {\n    var startsWithRejectsRegex = throwsError(function () {\n      /* throws if spec-compliant */\n      return '/a/'.startsWith(/a/);\n    });\n    var startsWithHandlesInfinity = valueOrFalseIfThrows(function () {\n      return 'abc'.startsWith('a', Infinity) === false;\n    });\n    if (!startsWithRejectsRegex || !startsWithHandlesInfinity) {\n      // Firefox (< 37?) and IE 11 TP have a noncompliant startsWith implementation\n      overrideNative(String.prototype, 'startsWith', StringPrototypeShims.startsWith);\n      overrideNative(String.prototype, 'endsWith', StringPrototypeShims.endsWith);\n    }\n  }\n  if (hasSymbols) {\n    var startsWithSupportsSymbolMatch = valueOrFalseIfThrows(function () {\n      var re = /a/;\n      re[Symbol.match] = false;\n      return '/a/'.startsWith(re);\n    });\n    if (!startsWithSupportsSymbolMatch) {\n      overrideNative(String.prototype, 'startsWith', StringPrototypeShims.startsWith);\n    }\n    var endsWithSupportsSymbolMatch = valueOrFalseIfThrows(function () {\n      var re = /a/;\n      re[Symbol.match] = false;\n      return '/a/'.endsWith(re);\n    });\n    if (!endsWithSupportsSymbolMatch) {\n      overrideNative(String.prototype, 'endsWith', StringPrototypeShims.endsWith);\n    }\n    var includesSupportsSymbolMatch = valueOrFalseIfThrows(function () {\n      var re = /a/;\n      re[Symbol.match] = false;\n      return '/a/'.includes(re);\n    });\n    if (!includesSupportsSymbolMatch) {\n      overrideNative(String.prototype, 'includes', StringPrototypeShims.includes);\n    }\n  }\n\n  defineProperties(String.prototype, StringPrototypeShims);\n\n  // whitespace from: http://es5.github.io/#x*********\n  // implementation from https://github.com/es-shims/es5-shim/blob/v3.4.0/es5-shim.js#L1304-L1324\n  var ws = [\n    '\\x09\\x0A\\x0B\\x0C\\x0D\\x20\\xA0\\u1680\\u180E\\u2000\\u2001\\u2002\\u2003',\n    '\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200A\\u202F\\u205F\\u3000\\u2028',\n    '\\u2029\\uFEFF'\n  ].join('');\n  var trimRegexp = new RegExp('(^[' + ws + ']+)|([' + ws + ']+$)', 'g');\n  var trimShim = function trim() {\n    return ES.ToString(ES.RequireObjectCoercible(this)).replace(trimRegexp, '');\n  };\n  var nonWS = ['\\u0085', '\\u200b', '\\ufffe'].join('');\n  var nonWSregex = new RegExp('[' + nonWS + ']', 'g');\n  var isBadHexRegex = /^[-+]0x[0-9a-f]+$/i;\n  var hasStringTrimBug = nonWS.trim().length !== nonWS.length;\n  defineProperty(String.prototype, 'trim', trimShim, hasStringTrimBug);\n\n  // Given an argument x, it will return an IteratorResult object,\n  // with value set to x and done to false.\n  // Given no arguments, it will return an iterator completion object.\n  var iteratorResult = function (x) {\n    return { value: x, done: arguments.length === 0 };\n  };\n\n  // see http://www.ecma-international.org/ecma-262/6.0/#sec-string.prototype-@@iterator\n  var StringIterator = function (s) {\n    ES.RequireObjectCoercible(s);\n    this._s = ES.ToString(s);\n    this._i = 0;\n  };\n  StringIterator.prototype.next = function () {\n    var s = this._s;\n    var i = this._i;\n    if (typeof s === 'undefined' || i >= s.length) {\n      this._s = void 0;\n      return iteratorResult();\n    }\n    var first = s.charCodeAt(i);\n    var second, len;\n    if (first < 0xD800 || first > 0xDBFF || (i + 1) === s.length) {\n      len = 1;\n    } else {\n      second = s.charCodeAt(i + 1);\n      len = (second < 0xDC00 || second > 0xDFFF) ? 1 : 2;\n    }\n    this._i = i + len;\n    return iteratorResult(s.substr(i, len));\n  };\n  addIterator(StringIterator.prototype);\n  addIterator(String.prototype, function () {\n    return new StringIterator(this);\n  });\n\n  var ArrayShims = {\n    from: function from(items) {\n      var C = this;\n      var mapFn;\n      if (arguments.length > 1) {\n        mapFn = arguments[1];\n      }\n      var mapping, T;\n      if (typeof mapFn === 'undefined') {\n        mapping = false;\n      } else {\n        if (!ES.IsCallable(mapFn)) {\n          throw new TypeError('Array.from: when provided, the second argument must be a function');\n        }\n        if (arguments.length > 2) {\n          T = arguments[2];\n        }\n        mapping = true;\n      }\n\n      // Note that that Arrays will use ArrayIterator:\n      // https://bugs.ecmascript.org/show_bug.cgi?id=2416\n      var usingIterator = typeof (isArguments(items) || ES.GetMethod(items, $iterator$)) !== 'undefined';\n\n      var length, result, i;\n      if (usingIterator) {\n        result = ES.IsConstructor(C) ? Object(new C()) : [];\n        var iterator = ES.GetIterator(items);\n        var next, nextValue;\n\n        i = 0;\n        while (true) {\n          next = ES.IteratorStep(iterator);\n          if (next === false) {\n            break;\n          }\n          nextValue = next.value;\n          try {\n            if (mapping) {\n              nextValue = typeof T === 'undefined' ? mapFn(nextValue, i) : _call(mapFn, T, nextValue, i);\n            }\n            result[i] = nextValue;\n          } catch (e) {\n            ES.IteratorClose(iterator, true);\n            throw e;\n          }\n          i += 1;\n        }\n        length = i;\n      } else {\n        var arrayLike = ES.ToObject(items);\n        length = ES.ToLength(arrayLike.length);\n        result = ES.IsConstructor(C) ? Object(new C(length)) : new Array(length);\n        var value;\n        for (i = 0; i < length; ++i) {\n          value = arrayLike[i];\n          if (mapping) {\n            value = typeof T === 'undefined' ? mapFn(value, i) : _call(mapFn, T, value, i);\n          }\n          createDataPropertyOrThrow(result, i, value);\n        }\n      }\n\n      result.length = length;\n      return result;\n    },\n\n    of: function of() {\n      var len = arguments.length;\n      var C = this;\n      var A = isArray(C) || !ES.IsCallable(C) ? new Array(len) : ES.Construct(C, [len]);\n      for (var k = 0; k < len; ++k) {\n        createDataPropertyOrThrow(A, k, arguments[k]);\n      }\n      A.length = len;\n      return A;\n    }\n  };\n  defineProperties(Array, ArrayShims);\n  addDefaultSpecies(Array);\n\n  // Our ArrayIterator is private; see\n  // https://github.com/paulmillr/es6-shim/issues/252\n  ArrayIterator = function (array, kind) {\n    this.i = 0;\n    this.array = array;\n    this.kind = kind;\n  };\n\n  defineProperties(ArrayIterator.prototype, {\n    next: function () {\n      var i = this.i;\n      var array = this.array;\n      if (!(this instanceof ArrayIterator)) {\n        throw new TypeError('Not an ArrayIterator');\n      }\n      if (typeof array !== 'undefined') {\n        var len = ES.ToLength(array.length);\n        for (; i < len; i++) {\n          var kind = this.kind;\n          var retval;\n          if (kind === 'key') {\n            retval = i;\n          } else if (kind === 'value') {\n            retval = array[i];\n          } else if (kind === 'entry') {\n            retval = [i, array[i]];\n          }\n          this.i = i + 1;\n          return iteratorResult(retval);\n        }\n      }\n      this.array = void 0;\n      return iteratorResult();\n    }\n  });\n  addIterator(ArrayIterator.prototype);\n\n  /*\n  var orderKeys = function orderKeys(a, b) {\n    var aNumeric = String(ES.ToInteger(a)) === a;\n    var bNumeric = String(ES.ToInteger(b)) === b;\n    if (aNumeric && bNumeric) {\n      return b - a;\n    } else if (aNumeric && !bNumeric) {\n      return -1;\n    } else if (!aNumeric && bNumeric) {\n      return 1;\n    } else {\n      return a.localeCompare(b);\n    }\n  };\n\n  var getAllKeys = function getAllKeys(object) {\n    var ownKeys = [];\n    var keys = [];\n\n    for (var key in object) {\n      _push(_hasOwnProperty(object, key) ? ownKeys : keys, key);\n    }\n    _sort(ownKeys, orderKeys);\n    _sort(keys, orderKeys);\n\n    return _concat(ownKeys, keys);\n  };\n  */\n\n  // note: this is positioned here because it depends on ArrayIterator\n  var arrayOfSupportsSubclassing = Array.of === ArrayShims.of || (function () {\n    // Detects a bug in Webkit nightly r181886\n    var Foo = function Foo(len) { this.length = len; };\n    Foo.prototype = [];\n    var fooArr = Array.of.apply(Foo, [1, 2]);\n    return fooArr instanceof Foo && fooArr.length === 2;\n  }());\n  if (!arrayOfSupportsSubclassing) {\n    overrideNative(Array, 'of', ArrayShims.of);\n  }\n\n  var ArrayPrototypeShims = {\n    copyWithin: function copyWithin(target, start) {\n      var o = ES.ToObject(this);\n      var len = ES.ToLength(o.length);\n      var relativeTarget = ES.ToInteger(target);\n      var relativeStart = ES.ToInteger(start);\n      var to = relativeTarget < 0 ? _max(len + relativeTarget, 0) : _min(relativeTarget, len);\n      var from = relativeStart < 0 ? _max(len + relativeStart, 0) : _min(relativeStart, len);\n      var end;\n      if (arguments.length > 2) {\n        end = arguments[2];\n      }\n      var relativeEnd = typeof end === 'undefined' ? len : ES.ToInteger(end);\n      var finalItem = relativeEnd < 0 ? _max(len + relativeEnd, 0) : _min(relativeEnd, len);\n      var count = _min(finalItem - from, len - to);\n      var direction = 1;\n      if (from < to && to < (from + count)) {\n        direction = -1;\n        from += count - 1;\n        to += count - 1;\n      }\n      while (count > 0) {\n        if (from in o) {\n          o[to] = o[from];\n        } else {\n          delete o[to];\n        }\n        from += direction;\n        to += direction;\n        count -= 1;\n      }\n      return o;\n    },\n\n    fill: function fill(value) {\n      var start;\n      if (arguments.length > 1) {\n        start = arguments[1];\n      }\n      var end;\n      if (arguments.length > 2) {\n        end = arguments[2];\n      }\n      var O = ES.ToObject(this);\n      var len = ES.ToLength(O.length);\n      start = ES.ToInteger(typeof start === 'undefined' ? 0 : start);\n      end = ES.ToInteger(typeof end === 'undefined' ? len : end);\n\n      var relativeStart = start < 0 ? _max(len + start, 0) : _min(start, len);\n      var relativeEnd = end < 0 ? len + end : end;\n\n      for (var i = relativeStart; i < len && i < relativeEnd; ++i) {\n        O[i] = value;\n      }\n      return O;\n    },\n\n    find: function find(predicate) {\n      var list = ES.ToObject(this);\n      var length = ES.ToLength(list.length);\n      if (!ES.IsCallable(predicate)) {\n        throw new TypeError('Array#find: predicate must be a function');\n      }\n      var thisArg = arguments.length > 1 ? arguments[1] : null;\n      for (var i = 0, value; i < length; i++) {\n        value = list[i];\n        if (thisArg) {\n          if (_call(predicate, thisArg, value, i, list)) {\n            return value;\n          }\n        } else if (predicate(value, i, list)) {\n          return value;\n        }\n      }\n    },\n\n    findIndex: function findIndex(predicate) {\n      var list = ES.ToObject(this);\n      var length = ES.ToLength(list.length);\n      if (!ES.IsCallable(predicate)) {\n        throw new TypeError('Array#findIndex: predicate must be a function');\n      }\n      var thisArg = arguments.length > 1 ? arguments[1] : null;\n      for (var i = 0; i < length; i++) {\n        if (thisArg) {\n          if (_call(predicate, thisArg, list[i], i, list)) {\n            return i;\n          }\n        } else if (predicate(list[i], i, list)) {\n          return i;\n        }\n      }\n      return -1;\n    },\n\n    keys: function keys() {\n      return new ArrayIterator(this, 'key');\n    },\n\n    values: function values() {\n      return new ArrayIterator(this, 'value');\n    },\n\n    entries: function entries() {\n      return new ArrayIterator(this, 'entry');\n    }\n  };\n  // Safari 7.1 defines Array#keys and Array#entries natively,\n  // but the resulting ArrayIterator objects don't have a \"next\" method.\n  if (Array.prototype.keys && !ES.IsCallable([1].keys().next)) {\n    delete Array.prototype.keys;\n  }\n  if (Array.prototype.entries && !ES.IsCallable([1].entries().next)) {\n    delete Array.prototype.entries;\n  }\n\n  // Chrome 38 defines Array#keys and Array#entries, and Array#@@iterator, but not Array#values\n  if (Array.prototype.keys && Array.prototype.entries && !Array.prototype.values && Array.prototype[$iterator$]) {\n    defineProperties(Array.prototype, {\n      values: Array.prototype[$iterator$]\n    });\n    if (Type.symbol(Symbol.unscopables)) {\n      Array.prototype[Symbol.unscopables].values = true;\n    }\n  }\n  // Chrome 40 defines Array#values with the incorrect name, although Array#{keys,entries} have the correct name\n  if (functionsHaveNames && Array.prototype.values && Array.prototype.values.name !== 'values') {\n    var originalArrayPrototypeValues = Array.prototype.values;\n    overrideNative(Array.prototype, 'values', function values() { return ES.Call(originalArrayPrototypeValues, this, arguments); });\n    defineProperty(Array.prototype, $iterator$, Array.prototype.values, true);\n  }\n  defineProperties(Array.prototype, ArrayPrototypeShims);\n\n  if (1 / [true].indexOf(true, -0) < 0) {\n    // indexOf when given a position arg of -0 should return +0.\n    // https://github.com/tc39/ecma262/pull/316\n    defineProperty(Array.prototype, 'indexOf', function indexOf(searchElement) {\n      var value = _arrayIndexOfApply(this, arguments);\n      if (value === 0 && (1 / value) < 0) {\n        return 0;\n      }\n      return value;\n    }, true);\n  }\n\n  addIterator(Array.prototype, function () { return this.values(); });\n  // Chrome defines keys/values/entries on Array, but doesn't give us\n  // any way to identify its iterator.  So add our own shimmed field.\n  if (Object.getPrototypeOf) {\n    addIterator(Object.getPrototypeOf([].values()));\n  }\n\n  // note: this is positioned here because it relies on Array#entries\n  var arrayFromSwallowsNegativeLengths = (function () {\n    // Detects a Firefox bug in v32\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1063993\n    return valueOrFalseIfThrows(function () {\n      return Array.from({ length: -1 }).length === 0;\n    });\n  }());\n  var arrayFromHandlesIterables = (function () {\n    // Detects a bug in Webkit nightly r181886\n    var arr = Array.from([0].entries());\n    return arr.length === 1 && isArray(arr[0]) && arr[0][0] === 0 && arr[0][1] === 0;\n  }());\n  if (!arrayFromSwallowsNegativeLengths || !arrayFromHandlesIterables) {\n    overrideNative(Array, 'from', ArrayShims.from);\n  }\n  var arrayFromHandlesUndefinedMapFunction = (function () {\n    // Microsoft Edge v0.11 throws if the mapFn argument is *provided* but undefined,\n    // but the spec doesn't care if it's provided or not - undefined doesn't throw.\n    return valueOrFalseIfThrows(function () {\n      return Array.from([0], void 0);\n    });\n  }());\n  if (!arrayFromHandlesUndefinedMapFunction) {\n    var origArrayFrom = Array.from;\n    overrideNative(Array, 'from', function from(items) {\n      if (arguments.length > 1 && typeof arguments[1] !== 'undefined') {\n        return ES.Call(origArrayFrom, this, arguments);\n      } else {\n        return _call(origArrayFrom, this, items);\n      }\n    });\n  }\n\n  var int32sAsOne = -(Math.pow(2, 32) - 1);\n  var toLengthsCorrectly = function (method, reversed) {\n    var obj = { length: int32sAsOne };\n    obj[reversed ? (obj.length >>> 0) - 1 : 0] = true;\n    return valueOrFalseIfThrows(function () {\n      _call(method, obj, function () {\n        // note: in nonconforming browsers, this will be called\n        // -1 >>> 0 times, which is 4294967295, so the throw matters.\n        throw new RangeError('should not reach here');\n      }, []);\n      return true;\n    });\n  };\n  if (!toLengthsCorrectly(Array.prototype.forEach)) {\n    var originalForEach = Array.prototype.forEach;\n    overrideNative(Array.prototype, 'forEach', function forEach(callbackFn) {\n      return ES.Call(originalForEach, this.length >= 0 ? this : [], arguments);\n    }, true);\n  }\n  if (!toLengthsCorrectly(Array.prototype.map)) {\n    var originalMap = Array.prototype.map;\n    overrideNative(Array.prototype, 'map', function map(callbackFn) {\n      return ES.Call(originalMap, this.length >= 0 ? this : [], arguments);\n    }, true);\n  }\n  if (!toLengthsCorrectly(Array.prototype.filter)) {\n    var originalFilter = Array.prototype.filter;\n    overrideNative(Array.prototype, 'filter', function filter(callbackFn) {\n      return ES.Call(originalFilter, this.length >= 0 ? this : [], arguments);\n    }, true);\n  }\n  if (!toLengthsCorrectly(Array.prototype.some)) {\n    var originalSome = Array.prototype.some;\n    overrideNative(Array.prototype, 'some', function some(callbackFn) {\n      return ES.Call(originalSome, this.length >= 0 ? this : [], arguments);\n    }, true);\n  }\n  if (!toLengthsCorrectly(Array.prototype.every)) {\n    var originalEvery = Array.prototype.every;\n    overrideNative(Array.prototype, 'every', function every(callbackFn) {\n      return ES.Call(originalEvery, this.length >= 0 ? this : [], arguments);\n    }, true);\n  }\n  if (!toLengthsCorrectly(Array.prototype.reduce)) {\n    var originalReduce = Array.prototype.reduce;\n    overrideNative(Array.prototype, 'reduce', function reduce(callbackFn) {\n      return ES.Call(originalReduce, this.length >= 0 ? this : [], arguments);\n    }, true);\n  }\n  if (!toLengthsCorrectly(Array.prototype.reduceRight, true)) {\n    var originalReduceRight = Array.prototype.reduceRight;\n    overrideNative(Array.prototype, 'reduceRight', function reduceRight(callbackFn) {\n      return ES.Call(originalReduceRight, this.length >= 0 ? this : [], arguments);\n    }, true);\n  }\n\n  var lacksOctalSupport = Number('0o10') !== 8;\n  var lacksBinarySupport = Number('0b10') !== 2;\n  var trimsNonWhitespace = _some(nonWS, function (c) {\n    return Number(c + 0 + c) === 0;\n  });\n  if (lacksOctalSupport || lacksBinarySupport || trimsNonWhitespace) {\n    var OrigNumber = Number;\n    var binaryRegex = /^0b[01]+$/i;\n    var octalRegex = /^0o[0-7]+$/i;\n    // Note that in IE 8, RegExp.prototype.test doesn't seem to exist: ie, \"test\" is an own property of regexes. wtf.\n    var isBinary = binaryRegex.test.bind(binaryRegex);\n    var isOctal = octalRegex.test.bind(octalRegex);\n    var toPrimitive = function (O) { // need to replace this with `es-to-primitive/es6`\n      var result;\n      if (typeof O.valueOf === 'function') {\n        result = O.valueOf();\n        if (Type.primitive(result)) {\n          return result;\n        }\n      }\n      if (typeof O.toString === 'function') {\n        result = O.toString();\n        if (Type.primitive(result)) {\n          return result;\n        }\n      }\n      throw new TypeError('No default value');\n    };\n    var hasNonWS = nonWSregex.test.bind(nonWSregex);\n    var isBadHex = isBadHexRegex.test.bind(isBadHexRegex);\n    var NumberShim = (function () {\n      // this is wrapped in an IIFE because of IE 6-8's wacky scoping issues with named function expressions.\n      var NumberShim = function Number(value) {\n        var primValue;\n        if (arguments.length > 0) {\n          primValue = Type.primitive(value) ? value : toPrimitive(value, 'number');\n        } else {\n          primValue = 0;\n        }\n        if (typeof primValue === 'string') {\n          primValue = ES.Call(trimShim, primValue);\n          if (isBinary(primValue)) {\n            primValue = parseInt(_strSlice(primValue, 2), 2);\n          } else if (isOctal(primValue)) {\n            primValue = parseInt(_strSlice(primValue, 2), 8);\n          } else if (hasNonWS(primValue) || isBadHex(primValue)) {\n            primValue = NaN;\n          }\n        }\n        var receiver = this;\n        var valueOfSucceeds = valueOrFalseIfThrows(function () {\n          OrigNumber.prototype.valueOf.call(receiver);\n          return true;\n        });\n        if (receiver instanceof NumberShim && !valueOfSucceeds) {\n          return new OrigNumber(primValue);\n        }\n        /* jshint newcap: false */\n        return OrigNumber(primValue);\n        /* jshint newcap: true */\n      };\n      return NumberShim;\n    }());\n    wrapConstructor(OrigNumber, NumberShim, {});\n    // this is necessary for ES3 browsers, where these properties are non-enumerable.\n    defineProperties(NumberShim, {\n      NaN: OrigNumber.NaN,\n      MAX_VALUE: OrigNumber.MAX_VALUE,\n      MIN_VALUE: OrigNumber.MIN_VALUE,\n      NEGATIVE_INFINITY: OrigNumber.NEGATIVE_INFINITY,\n      POSITIVE_INFINITY: OrigNumber.POSITIVE_INFINITY\n    });\n    /* globals Number: true */\n    /* eslint-disable no-undef, no-global-assign */\n    /* jshint -W020 */\n    Number = NumberShim;\n    Value.redefine(globals, 'Number', NumberShim);\n    /* jshint +W020 */\n    /* eslint-enable no-undef, no-global-assign */\n    /* globals Number: false */\n  }\n\n  var maxSafeInteger = Math.pow(2, 53) - 1;\n  defineProperties(Number, {\n    MAX_SAFE_INTEGER: maxSafeInteger,\n    MIN_SAFE_INTEGER: -maxSafeInteger,\n    EPSILON: 2.220446049250313e-16,\n\n    parseInt: globals.parseInt,\n    parseFloat: globals.parseFloat,\n\n    isFinite: numberIsFinite,\n\n    isInteger: function isInteger(value) {\n      return numberIsFinite(value) && ES.ToInteger(value) === value;\n    },\n\n    isSafeInteger: function isSafeInteger(value) {\n      return Number.isInteger(value) && _abs(value) <= Number.MAX_SAFE_INTEGER;\n    },\n\n    isNaN: numberIsNaN\n  });\n  // Firefox 37 has a conforming Number.parseInt, but it's not === to the global parseInt (fixed in v40)\n  defineProperty(Number, 'parseInt', globals.parseInt, Number.parseInt !== globals.parseInt);\n\n  // Work around bugs in Array#find and Array#findIndex -- early\n  // implementations skipped holes in sparse arrays. (Note that the\n  // implementations of find/findIndex indirectly use shimmed\n  // methods of Number, so this test has to happen down here.)\n  /*jshint elision: true */\n  /* eslint-disable no-sparse-arrays */\n  if ([, 1].find(function () { return true; }) === 1) {\n    overrideNative(Array.prototype, 'find', ArrayPrototypeShims.find);\n  }\n  if ([, 1].findIndex(function () { return true; }) !== 0) {\n    overrideNative(Array.prototype, 'findIndex', ArrayPrototypeShims.findIndex);\n  }\n  /* eslint-enable no-sparse-arrays */\n  /*jshint elision: false */\n\n  var isEnumerableOn = Function.bind.call(Function.bind, Object.prototype.propertyIsEnumerable);\n  var ensureEnumerable = function ensureEnumerable(obj, prop) {\n    if (supportsDescriptors && isEnumerableOn(obj, prop)) {\n      Object.defineProperty(obj, prop, { enumerable: false });\n    }\n  };\n  var sliceArgs = function sliceArgs() {\n    // per https://github.com/petkaantonov/bluebird/wiki/Optimization-killers#32-leaking-arguments\n    // and https://gist.github.com/WebReflection/4327762cb87a8c634a29\n    var initial = Number(this);\n    var len = arguments.length;\n    var desiredArgCount = len - initial;\n    var args = new Array(desiredArgCount < 0 ? 0 : desiredArgCount);\n    for (var i = initial; i < len; ++i) {\n      args[i - initial] = arguments[i];\n    }\n    return args;\n  };\n  var assignTo = function assignTo(source) {\n    return function assignToSource(target, key) {\n      target[key] = source[key];\n      return target;\n    };\n  };\n  var assignReducer = function (target, source) {\n    var sourceKeys = keys(Object(source));\n    var symbols;\n    if (ES.IsCallable(Object.getOwnPropertySymbols)) {\n      symbols = _filter(Object.getOwnPropertySymbols(Object(source)), isEnumerableOn(source));\n    }\n    return _reduce(_concat(sourceKeys, symbols || []), assignTo(source), target);\n  };\n\n  var ObjectShims = {\n    // 19.1.3.1\n    assign: function (target, source) {\n      var to = ES.ToObject(target, 'Cannot convert undefined or null to object');\n      return _reduce(ES.Call(sliceArgs, 1, arguments), assignReducer, to);\n    },\n\n    // Added in WebKit in https://bugs.webkit.org/show_bug.cgi?id=143865\n    is: function is(a, b) {\n      return ES.SameValue(a, b);\n    }\n  };\n  var assignHasPendingExceptions = Object.assign && Object.preventExtensions && (function () {\n    // Firefox 37 still has \"pending exception\" logic in its Object.assign implementation,\n    // which is 72% slower than our shim, and Firefox 40's native implementation.\n    var thrower = Object.preventExtensions({ 1: 2 });\n    try {\n      Object.assign(thrower, 'xy');\n    } catch (e) {\n      return thrower[1] === 'y';\n    }\n  }());\n  if (assignHasPendingExceptions) {\n    overrideNative(Object, 'assign', ObjectShims.assign);\n  }\n  defineProperties(Object, ObjectShims);\n\n  if (supportsDescriptors) {\n    var ES5ObjectShims = {\n      // ********\n      // shim from https://gist.github.com/WebReflection/5593554\n      setPrototypeOf: (function (Object, magic) {\n        var set;\n\n        var checkArgs = function (O, proto) {\n          if (!ES.TypeIsObject(O)) {\n            throw new TypeError('cannot set prototype on a non-object');\n          }\n          if (!(proto === null || ES.TypeIsObject(proto))) {\n            throw new TypeError('can only set prototype to an object or null' + proto);\n          }\n        };\n\n        var setPrototypeOf = function (O, proto) {\n          checkArgs(O, proto);\n          _call(set, O, proto);\n          return O;\n        };\n\n        try {\n          // this works already in Firefox and Safari\n          set = Object.getOwnPropertyDescriptor(Object.prototype, magic).set;\n          _call(set, {}, null);\n        } catch (e) {\n          if (Object.prototype !== {}[magic]) {\n            // IE < 11 cannot be shimmed\n            return;\n          }\n          // probably Chrome or some old Mobile stock browser\n          set = function (proto) {\n            this[magic] = proto;\n          };\n          // please note that this will **not** work\n          // in those browsers that do not inherit\n          // __proto__ by mistake from Object.prototype\n          // in these cases we should probably throw an error\n          // or at least be informed about the issue\n          setPrototypeOf.polyfill = setPrototypeOf(\n            setPrototypeOf({}, null),\n            Object.prototype\n          ) instanceof Object;\n          // setPrototypeOf.polyfill === true means it works as meant\n          // setPrototypeOf.polyfill === false means it's not 100% reliable\n          // setPrototypeOf.polyfill === undefined\n          // or\n          // setPrototypeOf.polyfill ==  null means it's not a polyfill\n          // which means it works as expected\n          // we can even delete Object.prototype.__proto__;\n        }\n        return setPrototypeOf;\n      }(Object, '__proto__'))\n    };\n\n    defineProperties(Object, ES5ObjectShims);\n  }\n\n  // Workaround bug in Opera 12 where setPrototypeOf(x, null) doesn't work,\n  // but Object.create(null) does.\n  if (Object.setPrototypeOf && Object.getPrototypeOf &&\n      Object.getPrototypeOf(Object.setPrototypeOf({}, null)) !== null &&\n      Object.getPrototypeOf(Object.create(null)) === null) {\n    (function () {\n      var FAKENULL = Object.create(null);\n      var gpo = Object.getPrototypeOf;\n      var spo = Object.setPrototypeOf;\n      Object.getPrototypeOf = function (o) {\n        var result = gpo(o);\n        return result === FAKENULL ? null : result;\n      };\n      Object.setPrototypeOf = function (o, p) {\n        var proto = p === null ? FAKENULL : p;\n        return spo(o, proto);\n      };\n      Object.setPrototypeOf.polyfill = false;\n    }());\n  }\n\n  var objectKeysAcceptsPrimitives = !throwsError(function () { return Object.keys('foo'); });\n  if (!objectKeysAcceptsPrimitives) {\n    var originalObjectKeys = Object.keys;\n    overrideNative(Object, 'keys', function keys(value) {\n      return originalObjectKeys(ES.ToObject(value));\n    });\n    keys = Object.keys;\n  }\n  var objectKeysRejectsRegex = throwsError(function () { return Object.keys(/a/g); });\n  if (objectKeysRejectsRegex) {\n    var regexRejectingObjectKeys = Object.keys;\n    overrideNative(Object, 'keys', function keys(value) {\n      if (Type.regex(value)) {\n        var regexKeys = [];\n        for (var k in value) {\n          if (_hasOwnProperty(value, k)) {\n            _push(regexKeys, k);\n          }\n        }\n        return regexKeys;\n      }\n      return regexRejectingObjectKeys(value);\n    });\n    keys = Object.keys;\n  }\n\n  if (Object.getOwnPropertyNames) {\n    var objectGOPNAcceptsPrimitives = !throwsError(function () { return Object.getOwnPropertyNames('foo'); });\n    if (!objectGOPNAcceptsPrimitives) {\n      var cachedWindowNames = typeof window === 'object' ? Object.getOwnPropertyNames(window) : [];\n      var originalObjectGetOwnPropertyNames = Object.getOwnPropertyNames;\n      overrideNative(Object, 'getOwnPropertyNames', function getOwnPropertyNames(value) {\n        var val = ES.ToObject(value);\n        if (_toString(val) === '[object Window]') {\n          try {\n            return originalObjectGetOwnPropertyNames(val);\n          } catch (e) {\n            // IE bug where layout engine calls userland gOPN for cross-domain `window` objects\n            return _concat([], cachedWindowNames);\n          }\n        }\n        return originalObjectGetOwnPropertyNames(val);\n      });\n    }\n  }\n  if (Object.getOwnPropertyDescriptor) {\n    var objectGOPDAcceptsPrimitives = !throwsError(function () { return Object.getOwnPropertyDescriptor('foo', 'bar'); });\n    if (!objectGOPDAcceptsPrimitives) {\n      var originalObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n      overrideNative(Object, 'getOwnPropertyDescriptor', function getOwnPropertyDescriptor(value, property) {\n        return originalObjectGetOwnPropertyDescriptor(ES.ToObject(value), property);\n      });\n    }\n  }\n  if (Object.seal) {\n    var objectSealAcceptsPrimitives = !throwsError(function () { return Object.seal('foo'); });\n    if (!objectSealAcceptsPrimitives) {\n      var originalObjectSeal = Object.seal;\n      overrideNative(Object, 'seal', function seal(value) {\n        if (!ES.TypeIsObject(value)) { return value; }\n        return originalObjectSeal(value);\n      });\n    }\n  }\n  if (Object.isSealed) {\n    var objectIsSealedAcceptsPrimitives = !throwsError(function () { return Object.isSealed('foo'); });\n    if (!objectIsSealedAcceptsPrimitives) {\n      var originalObjectIsSealed = Object.isSealed;\n      overrideNative(Object, 'isSealed', function isSealed(value) {\n        if (!ES.TypeIsObject(value)) { return true; }\n        return originalObjectIsSealed(value);\n      });\n    }\n  }\n  if (Object.freeze) {\n    var objectFreezeAcceptsPrimitives = !throwsError(function () { return Object.freeze('foo'); });\n    if (!objectFreezeAcceptsPrimitives) {\n      var originalObjectFreeze = Object.freeze;\n      overrideNative(Object, 'freeze', function freeze(value) {\n        if (!ES.TypeIsObject(value)) { return value; }\n        return originalObjectFreeze(value);\n      });\n    }\n  }\n  if (Object.isFrozen) {\n    var objectIsFrozenAcceptsPrimitives = !throwsError(function () { return Object.isFrozen('foo'); });\n    if (!objectIsFrozenAcceptsPrimitives) {\n      var originalObjectIsFrozen = Object.isFrozen;\n      overrideNative(Object, 'isFrozen', function isFrozen(value) {\n        if (!ES.TypeIsObject(value)) { return true; }\n        return originalObjectIsFrozen(value);\n      });\n    }\n  }\n  if (Object.preventExtensions) {\n    var objectPreventExtensionsAcceptsPrimitives = !throwsError(function () { return Object.preventExtensions('foo'); });\n    if (!objectPreventExtensionsAcceptsPrimitives) {\n      var originalObjectPreventExtensions = Object.preventExtensions;\n      overrideNative(Object, 'preventExtensions', function preventExtensions(value) {\n        if (!ES.TypeIsObject(value)) { return value; }\n        return originalObjectPreventExtensions(value);\n      });\n    }\n  }\n  if (Object.isExtensible) {\n    var objectIsExtensibleAcceptsPrimitives = !throwsError(function () { return Object.isExtensible('foo'); });\n    if (!objectIsExtensibleAcceptsPrimitives) {\n      var originalObjectIsExtensible = Object.isExtensible;\n      overrideNative(Object, 'isExtensible', function isExtensible(value) {\n        if (!ES.TypeIsObject(value)) { return false; }\n        return originalObjectIsExtensible(value);\n      });\n    }\n  }\n  if (Object.getPrototypeOf) {\n    var objectGetProtoAcceptsPrimitives = !throwsError(function () { return Object.getPrototypeOf('foo'); });\n    if (!objectGetProtoAcceptsPrimitives) {\n      var originalGetProto = Object.getPrototypeOf;\n      overrideNative(Object, 'getPrototypeOf', function getPrototypeOf(value) {\n        return originalGetProto(ES.ToObject(value));\n      });\n    }\n  }\n\n  var hasFlags = supportsDescriptors && (function () {\n    var desc = Object.getOwnPropertyDescriptor(RegExp.prototype, 'flags');\n    return desc && ES.IsCallable(desc.get);\n  }());\n  if (supportsDescriptors && !hasFlags) {\n    var regExpFlagsGetter = function flags() {\n      if (!ES.TypeIsObject(this)) {\n        throw new TypeError('Method called on incompatible type: must be an object.');\n      }\n      var result = '';\n      if (this.global) {\n        result += 'g';\n      }\n      if (this.ignoreCase) {\n        result += 'i';\n      }\n      if (this.multiline) {\n        result += 'm';\n      }\n      if (this.unicode) {\n        result += 'u';\n      }\n      if (this.sticky) {\n        result += 'y';\n      }\n      return result;\n    };\n\n    Value.getter(RegExp.prototype, 'flags', regExpFlagsGetter);\n  }\n\n  var regExpSupportsFlagsWithRegex = supportsDescriptors && valueOrFalseIfThrows(function () {\n    return String(new RegExp(/a/g, 'i')) === '/a/i';\n  });\n  var regExpNeedsToSupportSymbolMatch = hasSymbols && supportsDescriptors && (function () {\n    // Edge 0.12 supports flags fully, but does not support Symbol.match\n    var regex = /./;\n    regex[Symbol.match] = false;\n    return RegExp(regex) === regex;\n  }());\n\n  var regexToStringIsGeneric = valueOrFalseIfThrows(function () {\n    return RegExp.prototype.toString.call({ source: 'abc' }) === '/abc/';\n  });\n  var regexToStringSupportsGenericFlags = regexToStringIsGeneric && valueOrFalseIfThrows(function () {\n    return RegExp.prototype.toString.call({ source: 'a', flags: 'b' }) === '/a/b';\n  });\n  if (!regexToStringIsGeneric || !regexToStringSupportsGenericFlags) {\n    var origRegExpToString = RegExp.prototype.toString;\n    defineProperty(RegExp.prototype, 'toString', function toString() {\n      var R = ES.RequireObjectCoercible(this);\n      if (Type.regex(R)) {\n        return _call(origRegExpToString, R);\n      }\n      var pattern = $String(R.source);\n      var flags = $String(R.flags);\n      return '/' + pattern + '/' + flags;\n    }, true);\n    Value.preserveToString(RegExp.prototype.toString, origRegExpToString);\n  }\n\n  if (supportsDescriptors && (!regExpSupportsFlagsWithRegex || regExpNeedsToSupportSymbolMatch)) {\n    var flagsGetter = Object.getOwnPropertyDescriptor(RegExp.prototype, 'flags').get;\n    var sourceDesc = Object.getOwnPropertyDescriptor(RegExp.prototype, 'source') || {};\n    var legacySourceGetter = function () {\n      // prior to it being a getter, it's own + nonconfigurable\n      return this.source;\n    };\n    var sourceGetter = ES.IsCallable(sourceDesc.get) ? sourceDesc.get : legacySourceGetter;\n\n    var OrigRegExp = RegExp;\n    var RegExpShim = (function () {\n      return function RegExp(pattern, flags) {\n        var patternIsRegExp = ES.IsRegExp(pattern);\n        var calledWithNew = this instanceof RegExp;\n        if (!calledWithNew && patternIsRegExp && typeof flags === 'undefined' && pattern.constructor === RegExp) {\n          return pattern;\n        }\n\n        var P = pattern;\n        var F = flags;\n        if (Type.regex(pattern)) {\n          P = ES.Call(sourceGetter, pattern);\n          F = typeof flags === 'undefined' ? ES.Call(flagsGetter, pattern) : flags;\n          return new RegExp(P, F);\n        } else if (patternIsRegExp) {\n          P = pattern.source;\n          F = typeof flags === 'undefined' ? pattern.flags : flags;\n        }\n        return new OrigRegExp(pattern, flags);\n      };\n    }());\n    wrapConstructor(OrigRegExp, RegExpShim, {\n      $input: true // Chrome < v39 & Opera < 26 have a nonstandard \"$input\" property\n    });\n    /* globals RegExp: true */\n    /* eslint-disable no-undef, no-global-assign */\n    /* jshint -W020 */\n    RegExp = RegExpShim;\n    Value.redefine(globals, 'RegExp', RegExpShim);\n    /* jshint +W020 */\n    /* eslint-enable no-undef, no-global-assign */\n    /* globals RegExp: false */\n  }\n\n  if (supportsDescriptors) {\n    var regexGlobals = {\n      input: '$_',\n      lastMatch: '$&',\n      lastParen: '$+',\n      leftContext: '$`',\n      rightContext: '$\\''\n    };\n    _forEach(keys(regexGlobals), function (prop) {\n      if (prop in RegExp && !(regexGlobals[prop] in RegExp)) {\n        Value.getter(RegExp, regexGlobals[prop], function get() {\n          return RegExp[prop];\n        });\n      }\n    });\n  }\n  addDefaultSpecies(RegExp);\n\n  var inverseEpsilon = 1 / Number.EPSILON;\n  var roundTiesToEven = function roundTiesToEven(n) {\n    // Even though this reduces down to `return n`, it takes advantage of built-in rounding.\n    return (n + inverseEpsilon) - inverseEpsilon;\n  };\n  var BINARY_32_EPSILON = Math.pow(2, -23);\n  var BINARY_32_MAX_VALUE = Math.pow(2, 127) * (2 - BINARY_32_EPSILON);\n  var BINARY_32_MIN_VALUE = Math.pow(2, -126);\n  var E = Math.E;\n  var LOG2E = Math.LOG2E;\n  var LOG10E = Math.LOG10E;\n  var numberCLZ = Number.prototype.clz;\n  delete Number.prototype.clz; // Safari 8 has Number#clz\n\n  var MathShims = {\n    acosh: function acosh(value) {\n      var x = Number(value);\n      if (numberIsNaN(x) || value < 1) { return NaN; }\n      if (x === 1) { return 0; }\n      if (x === Infinity) { return x; }\n\n      var xInvSquared = 1 / (x * x);\n      if (x < 2) {\n        return _log1p(x - 1 + (_sqrt(1 - xInvSquared) * x));\n      }\n      var halfX = x / 2;\n      return _log1p(halfX + (_sqrt(1 - xInvSquared) * halfX) - 1) + (1 / LOG2E);\n    },\n\n    asinh: function asinh(value) {\n      var x = Number(value);\n      if (x === 0 || !globalIsFinite(x)) {\n        return x;\n      }\n\n      var a = _abs(x);\n      var aSquared = a * a;\n      var s = _sign(x);\n      if (a < 1) {\n        return s * _log1p(a + (aSquared / (_sqrt(aSquared + 1) + 1)));\n      }\n      return s * (_log1p((a / 2) + (_sqrt(1 + (1 / aSquared)) * a / 2) - 1) + (1 / LOG2E));\n    },\n\n    atanh: function atanh(value) {\n      var x = Number(value);\n\n      if (x === 0) { return x; }\n      if (x === -1) { return -Infinity; }\n      if (x === 1) { return Infinity; }\n      if (numberIsNaN(x) || x < -1 || x > 1) {\n        return NaN;\n      }\n\n      var a = _abs(x);\n      return _sign(x) * _log1p(2 * a / (1 - a)) / 2;\n    },\n\n    cbrt: function cbrt(value) {\n      var x = Number(value);\n      if (x === 0) { return x; }\n      var negate = x < 0;\n      var result;\n      if (negate) { x = -x; }\n      if (x === Infinity) {\n        result = Infinity;\n      } else {\n        result = _exp(_log(x) / 3);\n        // from http://en.wikipedia.org/wiki/Cube_root#Numerical_methods\n        result = ((x / (result * result)) + (2 * result)) / 3;\n      }\n      return negate ? -result : result;\n    },\n\n    clz32: function clz32(value) {\n      // See https://bugs.ecmascript.org/show_bug.cgi?id=2465\n      var x = Number(value);\n      var number = ES.ToUint32(x);\n      if (number === 0) {\n        return 32;\n      }\n      return numberCLZ ? ES.Call(numberCLZ, number) : 31 - _floor(_log(number + 0.5) * LOG2E);\n    },\n\n    cosh: function cosh(value) {\n      var x = Number(value);\n      if (x === 0) { return 1; } // +0 or -0\n      if (numberIsNaN(x)) { return NaN; }\n      if (!globalIsFinite(x)) { return Infinity; }\n\n      var t = _exp(_abs(x) - 1);\n      return (t + (1 / (t * E * E))) * (E / 2);\n    },\n\n    expm1: function expm1(value) {\n      var x = Number(value);\n      if (x === -Infinity) { return -1; }\n      if (!globalIsFinite(x) || x === 0) { return x; }\n      if (_abs(x) > 0.5) {\n        return _exp(x) - 1;\n      }\n      // A more precise approximation using Taylor series expansion\n      // from https://github.com/paulmillr/es6-shim/issues/314#issuecomment-70293986\n      var t = x;\n      var sum = 0;\n      var n = 1;\n      while (sum + t !== sum) {\n        sum += t;\n        n += 1;\n        t *= x / n;\n      }\n      return sum;\n    },\n\n    hypot: function hypot(x, y) {\n      var result = 0;\n      var largest = 0;\n      for (var i = 0; i < arguments.length; ++i) {\n        var value = _abs(Number(arguments[i]));\n        if (largest < value) {\n          result *= (largest / value) * (largest / value);\n          result += 1;\n          largest = value;\n        } else {\n          result += value > 0 ? (value / largest) * (value / largest) : value;\n        }\n      }\n      return largest === Infinity ? Infinity : largest * _sqrt(result);\n    },\n\n    log2: function log2(value) {\n      return _log(value) * LOG2E;\n    },\n\n    log10: function log10(value) {\n      return _log(value) * LOG10E;\n    },\n\n    log1p: _log1p,\n\n    sign: _sign,\n\n    sinh: function sinh(value) {\n      var x = Number(value);\n      if (!globalIsFinite(x) || x === 0) { return x; }\n\n      var a = _abs(x);\n      if (a < 1) {\n        var u = Math.expm1(a);\n        return _sign(x) * u * (1 + (1 / (u + 1))) / 2;\n      }\n      var t = _exp(a - 1);\n      return _sign(x) * (t - (1 / (t * E * E))) * (E / 2);\n    },\n\n    tanh: function tanh(value) {\n      var x = Number(value);\n      if (numberIsNaN(x) || x === 0) { return x; }\n      // can exit early at +-20 as JS loses precision for true value at this integer\n      if (x >= 20) { return 1; }\n      if (x <= -20) { return -1; }\n\n      return (Math.expm1(x) - Math.expm1(-x)) / (_exp(x) + _exp(-x));\n    },\n\n    trunc: function trunc(value) {\n      var x = Number(value);\n      return x < 0 ? -_floor(-x) : _floor(x);\n    },\n\n    imul: function imul(x, y) {\n      // taken from https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/imul\n      var a = ES.ToUint32(x);\n      var b = ES.ToUint32(y);\n      var ah = (a >>> 16) & 0xffff;\n      var al = a & 0xffff;\n      var bh = (b >>> 16) & 0xffff;\n      var bl = b & 0xffff;\n      // the shift by 0 fixes the sign on the high part\n      // the final |0 converts the unsigned value into a signed value\n      return (al * bl) + ((((ah * bl) + (al * bh)) << 16) >>> 0) | 0;\n    },\n\n    fround: function fround(x) {\n      var v = Number(x);\n      if (v === 0 || v === Infinity || v === -Infinity || numberIsNaN(v)) {\n        return v;\n      }\n      var sign = _sign(v);\n      var abs = _abs(v);\n      if (abs < BINARY_32_MIN_VALUE) {\n        return sign * roundTiesToEven(abs / BINARY_32_MIN_VALUE / BINARY_32_EPSILON) * BINARY_32_MIN_VALUE * BINARY_32_EPSILON;\n      }\n      // Veltkamp's splitting (?)\n      var a = (1 + (BINARY_32_EPSILON / Number.EPSILON)) * abs;\n      var result = a - (a - abs);\n      if (result > BINARY_32_MAX_VALUE || numberIsNaN(result)) {\n        return sign * Infinity;\n      }\n      return sign * result;\n    }\n  };\n\n  var withinULPDistance = function withinULPDistance(result, expected, distance) {\n    return _abs(1 - (result / expected)) / Number.EPSILON < (distance || 8);\n  };\n\n  defineProperties(Math, MathShims);\n  // Chrome < 40 sinh returns ∞ for large numbers\n  defineProperty(Math, 'sinh', MathShims.sinh, Math.sinh(710) === Infinity);\n  // Chrome < 40 cosh returns ∞ for large numbers\n  defineProperty(Math, 'cosh', MathShims.cosh, Math.cosh(710) === Infinity);\n  // IE 11 TP has an imprecise log1p: reports Math.log1p(-1e-17) as 0\n  defineProperty(Math, 'log1p', MathShims.log1p, Math.log1p(-1e-17) !== -1e-17);\n  // IE 11 TP has an imprecise asinh: reports Math.asinh(-1e7) as not exactly equal to -Math.asinh(1e7)\n  defineProperty(Math, 'asinh', MathShims.asinh, Math.asinh(-1e7) !== -Math.asinh(1e7));\n  // Chrome < 54 asinh returns ∞ for large numbers and should not\n  defineProperty(Math, 'asinh', MathShims.asinh, Math.asinh(1e+300) === Infinity);\n  // Chrome < 54 atanh incorrectly returns 0 for large numbers\n  defineProperty(Math, 'atanh', MathShims.atanh, Math.atanh(1e-300) === 0);\n  // Chrome 40 has an imprecise Math.tanh with very small numbers\n  defineProperty(Math, 'tanh', MathShims.tanh, Math.tanh(-2e-17) !== -2e-17);\n  // Chrome 40 loses Math.acosh precision with high numbers\n  defineProperty(Math, 'acosh', MathShims.acosh, Math.acosh(Number.MAX_VALUE) === Infinity);\n  // Chrome < 54 has an inaccurate acosh for EPSILON deltas\n  defineProperty(Math, 'acosh', MathShims.acosh, !withinULPDistance(Math.acosh(1 + Number.EPSILON), Math.sqrt(2 * Number.EPSILON)));\n  // Firefox 38 on Windows\n  defineProperty(Math, 'cbrt', MathShims.cbrt, !withinULPDistance(Math.cbrt(1e-300), 1e-100));\n  // node 0.11 has an imprecise Math.sinh with very small numbers\n  defineProperty(Math, 'sinh', MathShims.sinh, Math.sinh(-2e-17) !== -2e-17);\n  // FF 35 on Linux reports 22025.465794806725 for Math.expm1(10)\n  var expm1OfTen = Math.expm1(10);\n  defineProperty(Math, 'expm1', MathShims.expm1, expm1OfTen > 22025.465794806719 || expm1OfTen < 22025.4657948067165168);\n\n  var origMathRound = Math.round;\n  // breaks in e.g. Safari 8, Internet Explorer 11, Opera 12\n  var roundHandlesBoundaryConditions = Math.round(0.5 - (Number.EPSILON / 4)) === 0 &&\n    Math.round(-0.5 + (Number.EPSILON / 3.99)) === 1;\n\n  // When engines use Math.floor(x + 0.5) internally, Math.round can be buggy for large integers.\n  // This behavior should be governed by \"round to nearest, ties to even mode\"\n  // see http://www.ecma-international.org/ecma-262/6.0/#sec-terms-and-definitions-number-type\n  // These are the boundary cases where it breaks.\n  var smallestPositiveNumberWhereRoundBreaks = inverseEpsilon + 1;\n  var largestPositiveNumberWhereRoundBreaks = (2 * inverseEpsilon) - 1;\n  var roundDoesNotIncreaseIntegers = [\n    smallestPositiveNumberWhereRoundBreaks,\n    largestPositiveNumberWhereRoundBreaks\n  ].every(function (num) {\n    return Math.round(num) === num;\n  });\n  defineProperty(Math, 'round', function round(x) {\n    var floor = _floor(x);\n    var ceil = floor === -1 ? -0 : floor + 1;\n    return x - floor < 0.5 ? floor : ceil;\n  }, !roundHandlesBoundaryConditions || !roundDoesNotIncreaseIntegers);\n  Value.preserveToString(Math.round, origMathRound);\n\n  var origImul = Math.imul;\n  if (Math.imul(0xffffffff, 5) !== -5) {\n    // Safari 6.1, at least, reports \"0\" for this value\n    Math.imul = MathShims.imul;\n    Value.preserveToString(Math.imul, origImul);\n  }\n  if (Math.imul.length !== 2) {\n    // Safari 8.0.4 has a length of 1\n    // fixed in https://bugs.webkit.org/show_bug.cgi?id=143658\n    overrideNative(Math, 'imul', function imul(x, y) {\n      return ES.Call(origImul, Math, arguments);\n    });\n  }\n\n  // Promises\n  // Simplest possible implementation; use a 3rd-party library if you\n  // want the best possible speed and/or long stack traces.\n  var PromiseShim = (function () {\n    var setTimeout = globals.setTimeout;\n    // some environments don't have setTimeout - no way to shim here.\n    if (typeof setTimeout !== 'function' && typeof setTimeout !== 'object') { return; }\n\n    ES.IsPromise = function (promise) {\n      if (!ES.TypeIsObject(promise)) {\n        return false;\n      }\n      if (typeof promise._promise === 'undefined') {\n        return false; // uninitialized, or missing our hidden field.\n      }\n      return true;\n    };\n\n    // \"PromiseCapability\" in the spec is what most promise implementations\n    // call a \"deferred\".\n    var PromiseCapability = function (C) {\n      if (!ES.IsConstructor(C)) {\n        throw new TypeError('Bad promise constructor');\n      }\n      var capability = this;\n      var resolver = function (resolve, reject) {\n        if (capability.resolve !== void 0 || capability.reject !== void 0) {\n          throw new TypeError('Bad Promise implementation!');\n        }\n        capability.resolve = resolve;\n        capability.reject = reject;\n      };\n      // Initialize fields to inform optimizers about the object shape.\n      capability.resolve = void 0;\n      capability.reject = void 0;\n      capability.promise = new C(resolver);\n      if (!(ES.IsCallable(capability.resolve) && ES.IsCallable(capability.reject))) {\n        throw new TypeError('Bad promise constructor');\n      }\n    };\n\n    // find an appropriate setImmediate-alike\n    var makeZeroTimeout;\n    /*global window */\n    if (typeof window !== 'undefined' && ES.IsCallable(window.postMessage)) {\n      makeZeroTimeout = function () {\n        // from http://dbaron.org/log/20100309-faster-timeouts\n        var timeouts = [];\n        var messageName = 'zero-timeout-message';\n        var setZeroTimeout = function (fn) {\n          _push(timeouts, fn);\n          window.postMessage(messageName, '*');\n        };\n        var handleMessage = function (event) {\n          if (event.source === window && event.data === messageName) {\n            event.stopPropagation();\n            if (timeouts.length === 0) { return; }\n            var fn = _shift(timeouts);\n            fn();\n          }\n        };\n        window.addEventListener('message', handleMessage, true);\n        return setZeroTimeout;\n      };\n    }\n    var makePromiseAsap = function () {\n      // An efficient task-scheduler based on a pre-existing Promise\n      // implementation, which we can use even if we override the\n      // global Promise below (in order to workaround bugs)\n      // https://github.com/Raynos/observ-hash/issues/2#issuecomment-35857671\n      var P = globals.Promise;\n      var pr = P && P.resolve && P.resolve();\n      return pr && function (task) {\n        return pr.then(task);\n      };\n    };\n    /*global process */\n    /* jscs:disable disallowMultiLineTernary */\n    var enqueue = ES.IsCallable(globals.setImmediate) ?\n      globals.setImmediate :\n      typeof process === 'object' && process.nextTick ? process.nextTick : makePromiseAsap() ||\n      (ES.IsCallable(makeZeroTimeout) ? makeZeroTimeout() : function (task) { setTimeout(task, 0); }); // fallback\n    /* jscs:enable disallowMultiLineTernary */\n\n    // Constants for Promise implementation\n    var PROMISE_IDENTITY = function (x) { return x; };\n    var PROMISE_THROWER = function (e) { throw e; };\n    var PROMISE_PENDING = 0;\n    var PROMISE_FULFILLED = 1;\n    var PROMISE_REJECTED = 2;\n    // We store fulfill/reject handlers and capabilities in a single array.\n    var PROMISE_FULFILL_OFFSET = 0;\n    var PROMISE_REJECT_OFFSET = 1;\n    var PROMISE_CAPABILITY_OFFSET = 2;\n    // This is used in an optimization for chaining promises via then.\n    var PROMISE_FAKE_CAPABILITY = {};\n\n    var enqueuePromiseReactionJob = function (handler, capability, argument) {\n      enqueue(function () {\n        promiseReactionJob(handler, capability, argument);\n      });\n    };\n\n    var promiseReactionJob = function (handler, promiseCapability, argument) {\n      var handlerResult, f;\n      if (promiseCapability === PROMISE_FAKE_CAPABILITY) {\n        // Fast case, when we don't actually need to chain through to a\n        // (real) promiseCapability.\n        return handler(argument);\n      }\n      try {\n        handlerResult = handler(argument);\n        f = promiseCapability.resolve;\n      } catch (e) {\n        handlerResult = e;\n        f = promiseCapability.reject;\n      }\n      f(handlerResult);\n    };\n\n    var fulfillPromise = function (promise, value) {\n      var _promise = promise._promise;\n      var length = _promise.reactionLength;\n      if (length > 0) {\n        enqueuePromiseReactionJob(\n          _promise.fulfillReactionHandler0,\n          _promise.reactionCapability0,\n          value\n        );\n        _promise.fulfillReactionHandler0 = void 0;\n        _promise.rejectReactions0 = void 0;\n        _promise.reactionCapability0 = void 0;\n        if (length > 1) {\n          for (var i = 1, idx = 0; i < length; i++, idx += 3) {\n            enqueuePromiseReactionJob(\n              _promise[idx + PROMISE_FULFILL_OFFSET],\n              _promise[idx + PROMISE_CAPABILITY_OFFSET],\n              value\n            );\n            promise[idx + PROMISE_FULFILL_OFFSET] = void 0;\n            promise[idx + PROMISE_REJECT_OFFSET] = void 0;\n            promise[idx + PROMISE_CAPABILITY_OFFSET] = void 0;\n          }\n        }\n      }\n      _promise.result = value;\n      _promise.state = PROMISE_FULFILLED;\n      _promise.reactionLength = 0;\n    };\n\n    var rejectPromise = function (promise, reason) {\n      var _promise = promise._promise;\n      var length = _promise.reactionLength;\n      if (length > 0) {\n        enqueuePromiseReactionJob(\n          _promise.rejectReactionHandler0,\n          _promise.reactionCapability0,\n          reason\n        );\n        _promise.fulfillReactionHandler0 = void 0;\n        _promise.rejectReactions0 = void 0;\n        _promise.reactionCapability0 = void 0;\n        if (length > 1) {\n          for (var i = 1, idx = 0; i < length; i++, idx += 3) {\n            enqueuePromiseReactionJob(\n              _promise[idx + PROMISE_REJECT_OFFSET],\n              _promise[idx + PROMISE_CAPABILITY_OFFSET],\n              reason\n            );\n            promise[idx + PROMISE_FULFILL_OFFSET] = void 0;\n            promise[idx + PROMISE_REJECT_OFFSET] = void 0;\n            promise[idx + PROMISE_CAPABILITY_OFFSET] = void 0;\n          }\n        }\n      }\n      _promise.result = reason;\n      _promise.state = PROMISE_REJECTED;\n      _promise.reactionLength = 0;\n    };\n\n    var createResolvingFunctions = function (promise) {\n      var alreadyResolved = false;\n      var resolve = function (resolution) {\n        var then;\n        if (alreadyResolved) { return; }\n        alreadyResolved = true;\n        if (resolution === promise) {\n          return rejectPromise(promise, new TypeError('Self resolution'));\n        }\n        if (!ES.TypeIsObject(resolution)) {\n          return fulfillPromise(promise, resolution);\n        }\n        try {\n          then = resolution.then;\n        } catch (e) {\n          return rejectPromise(promise, e);\n        }\n        if (!ES.IsCallable(then)) {\n          return fulfillPromise(promise, resolution);\n        }\n        enqueue(function () {\n          promiseResolveThenableJob(promise, resolution, then);\n        });\n      };\n      var reject = function (reason) {\n        if (alreadyResolved) { return; }\n        alreadyResolved = true;\n        return rejectPromise(promise, reason);\n      };\n      return { resolve: resolve, reject: reject };\n    };\n\n    var optimizedThen = function (then, thenable, resolve, reject) {\n      // Optimization: since we discard the result, we can pass our\n      // own then implementation a special hint to let it know it\n      // doesn't have to create it.  (The PROMISE_FAKE_CAPABILITY\n      // object is local to this implementation and unforgeable outside.)\n      if (then === Promise$prototype$then) {\n        _call(then, thenable, resolve, reject, PROMISE_FAKE_CAPABILITY);\n      } else {\n        _call(then, thenable, resolve, reject);\n      }\n    };\n    var promiseResolveThenableJob = function (promise, thenable, then) {\n      var resolvingFunctions = createResolvingFunctions(promise);\n      var resolve = resolvingFunctions.resolve;\n      var reject = resolvingFunctions.reject;\n      try {\n        optimizedThen(then, thenable, resolve, reject);\n      } catch (e) {\n        reject(e);\n      }\n    };\n\n    var Promise$prototype, Promise$prototype$then;\n    var Promise = (function () {\n      var PromiseShim = function Promise(resolver) {\n        if (!(this instanceof PromiseShim)) {\n          throw new TypeError('Constructor Promise requires \"new\"');\n        }\n        if (this && this._promise) {\n          throw new TypeError('Bad construction');\n        }\n        // see https://bugs.ecmascript.org/show_bug.cgi?id=2482\n        if (!ES.IsCallable(resolver)) {\n          throw new TypeError('not a valid resolver');\n        }\n        var promise = emulateES6construct(this, PromiseShim, Promise$prototype, {\n          _promise: {\n            result: void 0,\n            state: PROMISE_PENDING,\n            // The first member of the \"reactions\" array is inlined here,\n            // since most promises only have one reaction.\n            // We've also exploded the 'reaction' object to inline the\n            // \"handler\" and \"capability\" fields, since both fulfill and\n            // reject reactions share the same capability.\n            reactionLength: 0,\n            fulfillReactionHandler0: void 0,\n            rejectReactionHandler0: void 0,\n            reactionCapability0: void 0\n          }\n        });\n        var resolvingFunctions = createResolvingFunctions(promise);\n        var reject = resolvingFunctions.reject;\n        try {\n          resolver(resolvingFunctions.resolve, reject);\n        } catch (e) {\n          reject(e);\n        }\n        return promise;\n      };\n      return PromiseShim;\n    }());\n    Promise$prototype = Promise.prototype;\n\n    var _promiseAllResolver = function (index, values, capability, remaining) {\n      var alreadyCalled = false;\n      return function (x) {\n        if (alreadyCalled) { return; }\n        alreadyCalled = true;\n        values[index] = x;\n        if ((--remaining.count) === 0) {\n          var resolve = capability.resolve;\n          resolve(values); // call w/ this===undefined\n        }\n      };\n    };\n\n    var performPromiseAll = function (iteratorRecord, C, resultCapability) {\n      var it = iteratorRecord.iterator;\n      var values = [];\n      var remaining = { count: 1 };\n      var next, nextValue;\n      var index = 0;\n      while (true) {\n        try {\n          next = ES.IteratorStep(it);\n          if (next === false) {\n            iteratorRecord.done = true;\n            break;\n          }\n          nextValue = next.value;\n        } catch (e) {\n          iteratorRecord.done = true;\n          throw e;\n        }\n        values[index] = void 0;\n        var nextPromise = C.resolve(nextValue);\n        var resolveElement = _promiseAllResolver(\n          index,\n          values,\n          resultCapability,\n          remaining\n        );\n        remaining.count += 1;\n        optimizedThen(nextPromise.then, nextPromise, resolveElement, resultCapability.reject);\n        index += 1;\n      }\n      if ((--remaining.count) === 0) {\n        var resolve = resultCapability.resolve;\n        resolve(values); // call w/ this===undefined\n      }\n      return resultCapability.promise;\n    };\n\n    var performPromiseRace = function (iteratorRecord, C, resultCapability) {\n      var it = iteratorRecord.iterator;\n      var next, nextValue, nextPromise;\n      while (true) {\n        try {\n          next = ES.IteratorStep(it);\n          if (next === false) {\n            // NOTE: If iterable has no items, resulting promise will never\n            // resolve; see:\n            // https://github.com/domenic/promises-unwrapping/issues/75\n            // https://bugs.ecmascript.org/show_bug.cgi?id=2515\n            iteratorRecord.done = true;\n            break;\n          }\n          nextValue = next.value;\n        } catch (e) {\n          iteratorRecord.done = true;\n          throw e;\n        }\n        nextPromise = C.resolve(nextValue);\n        optimizedThen(nextPromise.then, nextPromise, resultCapability.resolve, resultCapability.reject);\n      }\n      return resultCapability.promise;\n    };\n\n    defineProperties(Promise, {\n      all: function all(iterable) {\n        var C = this;\n        if (!ES.TypeIsObject(C)) {\n          throw new TypeError('Promise is not object');\n        }\n        var capability = new PromiseCapability(C);\n        var iterator, iteratorRecord;\n        try {\n          iterator = ES.GetIterator(iterable);\n          iteratorRecord = { iterator: iterator, done: false };\n          return performPromiseAll(iteratorRecord, C, capability);\n        } catch (e) {\n          var exception = e;\n          if (iteratorRecord && !iteratorRecord.done) {\n            try {\n              ES.IteratorClose(iterator, true);\n            } catch (ee) {\n              exception = ee;\n            }\n          }\n          var reject = capability.reject;\n          reject(exception);\n          return capability.promise;\n        }\n      },\n\n      race: function race(iterable) {\n        var C = this;\n        if (!ES.TypeIsObject(C)) {\n          throw new TypeError('Promise is not object');\n        }\n        var capability = new PromiseCapability(C);\n        var iterator, iteratorRecord;\n        try {\n          iterator = ES.GetIterator(iterable);\n          iteratorRecord = { iterator: iterator, done: false };\n          return performPromiseRace(iteratorRecord, C, capability);\n        } catch (e) {\n          var exception = e;\n          if (iteratorRecord && !iteratorRecord.done) {\n            try {\n              ES.IteratorClose(iterator, true);\n            } catch (ee) {\n              exception = ee;\n            }\n          }\n          var reject = capability.reject;\n          reject(exception);\n          return capability.promise;\n        }\n      },\n\n      reject: function reject(reason) {\n        var C = this;\n        if (!ES.TypeIsObject(C)) {\n          throw new TypeError('Bad promise constructor');\n        }\n        var capability = new PromiseCapability(C);\n        var rejectFunc = capability.reject;\n        rejectFunc(reason); // call with this===undefined\n        return capability.promise;\n      },\n\n      resolve: function resolve(v) {\n        // See https://esdiscuss.org/topic/fixing-promise-resolve for spec\n        var C = this;\n        if (!ES.TypeIsObject(C)) {\n          throw new TypeError('Bad promise constructor');\n        }\n        if (ES.IsPromise(v)) {\n          var constructor = v.constructor;\n          if (constructor === C) {\n            return v;\n          }\n        }\n        var capability = new PromiseCapability(C);\n        var resolveFunc = capability.resolve;\n        resolveFunc(v); // call with this===undefined\n        return capability.promise;\n      }\n    });\n\n    defineProperties(Promise$prototype, {\n      'catch': function (onRejected) {\n        return this.then(null, onRejected);\n      },\n\n      then: function then(onFulfilled, onRejected) {\n        var promise = this;\n        if (!ES.IsPromise(promise)) { throw new TypeError('not a promise'); }\n        var C = ES.SpeciesConstructor(promise, Promise);\n        var resultCapability;\n        var returnValueIsIgnored = arguments.length > 2 && arguments[2] === PROMISE_FAKE_CAPABILITY;\n        if (returnValueIsIgnored && C === Promise) {\n          resultCapability = PROMISE_FAKE_CAPABILITY;\n        } else {\n          resultCapability = new PromiseCapability(C);\n        }\n        // PerformPromiseThen(promise, onFulfilled, onRejected, resultCapability)\n        // Note that we've split the 'reaction' object into its two\n        // components, \"capabilities\" and \"handler\"\n        // \"capabilities\" is always equal to `resultCapability`\n        var fulfillReactionHandler = ES.IsCallable(onFulfilled) ? onFulfilled : PROMISE_IDENTITY;\n        var rejectReactionHandler = ES.IsCallable(onRejected) ? onRejected : PROMISE_THROWER;\n        var _promise = promise._promise;\n        var value;\n        if (_promise.state === PROMISE_PENDING) {\n          if (_promise.reactionLength === 0) {\n            _promise.fulfillReactionHandler0 = fulfillReactionHandler;\n            _promise.rejectReactionHandler0 = rejectReactionHandler;\n            _promise.reactionCapability0 = resultCapability;\n          } else {\n            var idx = 3 * (_promise.reactionLength - 1);\n            _promise[idx + PROMISE_FULFILL_OFFSET] = fulfillReactionHandler;\n            _promise[idx + PROMISE_REJECT_OFFSET] = rejectReactionHandler;\n            _promise[idx + PROMISE_CAPABILITY_OFFSET] = resultCapability;\n          }\n          _promise.reactionLength += 1;\n        } else if (_promise.state === PROMISE_FULFILLED) {\n          value = _promise.result;\n          enqueuePromiseReactionJob(\n            fulfillReactionHandler,\n            resultCapability,\n            value\n          );\n        } else if (_promise.state === PROMISE_REJECTED) {\n          value = _promise.result;\n          enqueuePromiseReactionJob(\n            rejectReactionHandler,\n            resultCapability,\n            value\n          );\n        } else {\n          throw new TypeError('unexpected Promise state');\n        }\n        return resultCapability.promise;\n      }\n    });\n    // This helps the optimizer by ensuring that methods which take\n    // capabilities aren't polymorphic.\n    PROMISE_FAKE_CAPABILITY = new PromiseCapability(Promise);\n    Promise$prototype$then = Promise$prototype.then;\n\n    return Promise;\n  }());\n\n  // Chrome's native Promise has extra methods that it shouldn't have. Let's remove them.\n  if (globals.Promise) {\n    delete globals.Promise.accept;\n    delete globals.Promise.defer;\n    delete globals.Promise.prototype.chain;\n  }\n\n  if (typeof PromiseShim === 'function') {\n    // export the Promise constructor.\n    defineProperties(globals, { Promise: PromiseShim });\n    // In Chrome 33 (and thereabouts) Promise is defined, but the\n    // implementation is buggy in a number of ways.  Let's check subclassing\n    // support to see if we have a buggy implementation.\n    var promiseSupportsSubclassing = supportsSubclassing(globals.Promise, function (S) {\n      return S.resolve(42).then(function () {}) instanceof S;\n    });\n    var promiseIgnoresNonFunctionThenCallbacks = !throwsError(function () {\n      return globals.Promise.reject(42).then(null, 5).then(null, noop);\n    });\n    var promiseRequiresObjectContext = throwsError(function () { return globals.Promise.call(3, noop); });\n    // Promise.resolve() was errata'ed late in the ES6 process.\n    // See: https://bugzilla.mozilla.org/show_bug.cgi?id=1170742\n    //      https://code.google.com/p/v8/issues/detail?id=4161\n    // It serves as a proxy for a number of other bugs in early Promise\n    // implementations.\n    var promiseResolveBroken = (function (Promise) {\n      var p = Promise.resolve(5);\n      p.constructor = {};\n      var p2 = Promise.resolve(p);\n      try {\n        p2.then(null, noop).then(null, noop); // avoid \"uncaught rejection\" warnings in console\n      } catch (e) {\n        return true; // v8 native Promises break here https://code.google.com/p/chromium/issues/detail?id=575314\n      }\n      return p === p2; // This *should* be false!\n    }(globals.Promise));\n\n    // Chrome 46 (probably older too) does not retrieve a thenable's .then synchronously\n    var getsThenSynchronously = supportsDescriptors && (function () {\n      var count = 0;\n      // eslint-disable-next-line getter-return\n      var thenable = Object.defineProperty({}, 'then', { get: function () { count += 1; } });\n      Promise.resolve(thenable);\n      return count === 1;\n    }());\n\n    var BadResolverPromise = function BadResolverPromise(executor) {\n      var p = new Promise(executor);\n      executor(3, function () {});\n      this.then = p.then;\n      this.constructor = BadResolverPromise;\n    };\n    BadResolverPromise.prototype = Promise.prototype;\n    BadResolverPromise.all = Promise.all;\n    // Chrome Canary 49 (probably older too) has some implementation bugs\n    var hasBadResolverPromise = valueOrFalseIfThrows(function () {\n      return !!BadResolverPromise.all([1, 2]);\n    });\n\n    if (!promiseSupportsSubclassing || !promiseIgnoresNonFunctionThenCallbacks ||\n        !promiseRequiresObjectContext || promiseResolveBroken ||\n        !getsThenSynchronously || hasBadResolverPromise) {\n      /* globals Promise: true */\n      /* eslint-disable no-undef, no-global-assign */\n      /* jshint -W020 */\n      Promise = PromiseShim;\n      /* jshint +W020 */\n      /* eslint-enable no-undef, no-global-assign */\n      /* globals Promise: false */\n      overrideNative(globals, 'Promise', PromiseShim);\n    }\n    if (Promise.all.length !== 1) {\n      var origAll = Promise.all;\n      overrideNative(Promise, 'all', function all(iterable) {\n        return ES.Call(origAll, this, arguments);\n      });\n    }\n    if (Promise.race.length !== 1) {\n      var origRace = Promise.race;\n      overrideNative(Promise, 'race', function race(iterable) {\n        return ES.Call(origRace, this, arguments);\n      });\n    }\n    if (Promise.resolve.length !== 1) {\n      var origResolve = Promise.resolve;\n      overrideNative(Promise, 'resolve', function resolve(x) {\n        return ES.Call(origResolve, this, arguments);\n      });\n    }\n    if (Promise.reject.length !== 1) {\n      var origReject = Promise.reject;\n      overrideNative(Promise, 'reject', function reject(r) {\n        return ES.Call(origReject, this, arguments);\n      });\n    }\n    ensureEnumerable(Promise, 'all');\n    ensureEnumerable(Promise, 'race');\n    ensureEnumerable(Promise, 'resolve');\n    ensureEnumerable(Promise, 'reject');\n    addDefaultSpecies(Promise);\n  }\n\n  // Map and Set require a true ES5 environment\n  // Their fast path also requires that the environment preserve\n  // property insertion order, which is not guaranteed by the spec.\n  var testOrder = function (a) {\n    var b = keys(_reduce(a, function (o, k) {\n      o[k] = true;\n      return o;\n    }, {}));\n    return a.join(':') === b.join(':');\n  };\n  var preservesInsertionOrder = testOrder(['z', 'a', 'bb']);\n  // some engines (eg, Chrome) only preserve insertion order for string keys\n  var preservesNumericInsertionOrder = testOrder(['z', 1, 'a', '3', 2]);\n\n  if (supportsDescriptors) {\n\n    var fastkey = function fastkey(key, skipInsertionOrderCheck) {\n      if (!skipInsertionOrderCheck && !preservesInsertionOrder) {\n        return null;\n      }\n      if (isNullOrUndefined(key)) {\n        return '^' + ES.ToString(key);\n      } else if (typeof key === 'string') {\n        return '$' + key;\n      } else if (typeof key === 'number') {\n        // note that -0 will get coerced to \"0\" when used as a property key\n        if (!preservesNumericInsertionOrder) {\n          return 'n' + key;\n        }\n        return key;\n      } else if (typeof key === 'boolean') {\n        return 'b' + key;\n      }\n      return null;\n    };\n\n    var emptyObject = function emptyObject() {\n      // accomodate some older not-quite-ES5 browsers\n      return Object.create ? Object.create(null) : {};\n    };\n\n    var addIterableToMap = function addIterableToMap(MapConstructor, map, iterable) {\n      if (isArray(iterable) || Type.string(iterable)) {\n        _forEach(iterable, function (entry) {\n          if (!ES.TypeIsObject(entry)) {\n            throw new TypeError('Iterator value ' + entry + ' is not an entry object');\n          }\n          map.set(entry[0], entry[1]);\n        });\n      } else if (iterable instanceof MapConstructor) {\n        _call(MapConstructor.prototype.forEach, iterable, function (value, key) {\n          map.set(key, value);\n        });\n      } else {\n        var iter, adder;\n        if (!isNullOrUndefined(iterable)) {\n          adder = map.set;\n          if (!ES.IsCallable(adder)) { throw new TypeError('bad map'); }\n          iter = ES.GetIterator(iterable);\n        }\n        if (typeof iter !== 'undefined') {\n          while (true) {\n            var next = ES.IteratorStep(iter);\n            if (next === false) { break; }\n            var nextItem = next.value;\n            try {\n              if (!ES.TypeIsObject(nextItem)) {\n                throw new TypeError('Iterator value ' + nextItem + ' is not an entry object');\n              }\n              _call(adder, map, nextItem[0], nextItem[1]);\n            } catch (e) {\n              ES.IteratorClose(iter, true);\n              throw e;\n            }\n          }\n        }\n      }\n    };\n    var addIterableToSet = function addIterableToSet(SetConstructor, set, iterable) {\n      if (isArray(iterable) || Type.string(iterable)) {\n        _forEach(iterable, function (value) {\n          set.add(value);\n        });\n      } else if (iterable instanceof SetConstructor) {\n        _call(SetConstructor.prototype.forEach, iterable, function (value) {\n          set.add(value);\n        });\n      } else {\n        var iter, adder;\n        if (!isNullOrUndefined(iterable)) {\n          adder = set.add;\n          if (!ES.IsCallable(adder)) { throw new TypeError('bad set'); }\n          iter = ES.GetIterator(iterable);\n        }\n        if (typeof iter !== 'undefined') {\n          while (true) {\n            var next = ES.IteratorStep(iter);\n            if (next === false) { break; }\n            var nextValue = next.value;\n            try {\n              _call(adder, set, nextValue);\n            } catch (e) {\n              ES.IteratorClose(iter, true);\n              throw e;\n            }\n          }\n        }\n      }\n    };\n\n    var collectionShims = {\n      Map: (function () {\n\n        var empty = {};\n\n        var MapEntry = function MapEntry(key, value) {\n          this.key = key;\n          this.value = value;\n          this.next = null;\n          this.prev = null;\n        };\n\n        MapEntry.prototype.isRemoved = function isRemoved() {\n          return this.key === empty;\n        };\n\n        var isMap = function isMap(map) {\n          return !!map._es6map;\n        };\n\n        var requireMapSlot = function requireMapSlot(map, method) {\n          if (!ES.TypeIsObject(map) || !isMap(map)) {\n            throw new TypeError('Method Map.prototype.' + method + ' called on incompatible receiver ' + ES.ToString(map));\n          }\n        };\n\n        var MapIterator = function MapIterator(map, kind) {\n          requireMapSlot(map, '[[MapIterator]]');\n          this.head = map._head;\n          this.i = this.head;\n          this.kind = kind;\n        };\n\n        MapIterator.prototype = {\n          isMapIterator: true,\n          next: function next() {\n            if (!this.isMapIterator) {\n              throw new TypeError('Not a MapIterator');\n            }\n            var i = this.i;\n            var kind = this.kind;\n            var head = this.head;\n            if (typeof this.i === 'undefined') {\n              return iteratorResult();\n            }\n            while (i.isRemoved() && i !== head) {\n              // back up off of removed entries\n              i = i.prev;\n            }\n            // advance to next unreturned element.\n            var result;\n            while (i.next !== head) {\n              i = i.next;\n              if (!i.isRemoved()) {\n                if (kind === 'key') {\n                  result = i.key;\n                } else if (kind === 'value') {\n                  result = i.value;\n                } else {\n                  result = [i.key, i.value];\n                }\n                this.i = i;\n                return iteratorResult(result);\n              }\n            }\n            // once the iterator is done, it is done forever.\n            this.i = void 0;\n            return iteratorResult();\n          }\n        };\n        addIterator(MapIterator.prototype);\n\n        var Map$prototype;\n        var MapShim = function Map() {\n          if (!(this instanceof Map)) {\n            throw new TypeError('Constructor Map requires \"new\"');\n          }\n          if (this && this._es6map) {\n            throw new TypeError('Bad construction');\n          }\n          var map = emulateES6construct(this, Map, Map$prototype, {\n            _es6map: true,\n            _head: null,\n            _map: OrigMap ? new OrigMap() : null,\n            _size: 0,\n            _storage: emptyObject()\n          });\n\n          var head = new MapEntry(null, null);\n          // circular doubly-linked list.\n          /* eslint no-multi-assign: 1 */\n          head.next = head.prev = head;\n          map._head = head;\n\n          // Optionally initialize map from iterable\n          if (arguments.length > 0) {\n            addIterableToMap(Map, map, arguments[0]);\n          }\n          return map;\n        };\n        Map$prototype = MapShim.prototype;\n\n        Value.getter(Map$prototype, 'size', function () {\n          if (typeof this._size === 'undefined') {\n            throw new TypeError('size method called on incompatible Map');\n          }\n          return this._size;\n        });\n\n        defineProperties(Map$prototype, {\n          get: function get(key) {\n            requireMapSlot(this, 'get');\n            var entry;\n            var fkey = fastkey(key, true);\n            if (fkey !== null) {\n              // fast O(1) path\n              entry = this._storage[fkey];\n              if (entry) {\n                return entry.value;\n              } else {\n                return;\n              }\n            }\n            if (this._map) {\n              // fast object key path\n              entry = origMapGet.call(this._map, key);\n              if (entry) {\n                return entry.value;\n              } else {\n                return;\n              }\n            }\n            var head = this._head;\n            var i = head;\n            while ((i = i.next) !== head) {\n              if (ES.SameValueZero(i.key, key)) {\n                return i.value;\n              }\n            }\n          },\n\n          has: function has(key) {\n            requireMapSlot(this, 'has');\n            var fkey = fastkey(key, true);\n            if (fkey !== null) {\n              // fast O(1) path\n              return typeof this._storage[fkey] !== 'undefined';\n            }\n            if (this._map) {\n              // fast object key path\n              return origMapHas.call(this._map, key);\n            }\n            var head = this._head;\n            var i = head;\n            while ((i = i.next) !== head) {\n              if (ES.SameValueZero(i.key, key)) {\n                return true;\n              }\n            }\n            return false;\n          },\n\n          set: function set(key, value) {\n            requireMapSlot(this, 'set');\n            var head = this._head;\n            var i = head;\n            var entry;\n            var fkey = fastkey(key, true);\n            if (fkey !== null) {\n              // fast O(1) path\n              if (typeof this._storage[fkey] !== 'undefined') {\n                this._storage[fkey].value = value;\n                return this;\n              } else {\n                entry = this._storage[fkey] = new MapEntry(key, value); /* eslint no-multi-assign: 1 */\n                i = head.prev;\n                // fall through\n              }\n            } else if (this._map) {\n              // fast object key path\n              if (origMapHas.call(this._map, key)) {\n                origMapGet.call(this._map, key).value = value;\n              } else {\n                entry = new MapEntry(key, value);\n                origMapSet.call(this._map, key, entry);\n                i = head.prev;\n                // fall through\n              }\n            }\n            while ((i = i.next) !== head) {\n              if (ES.SameValueZero(i.key, key)) {\n                i.value = value;\n                return this;\n              }\n            }\n            entry = entry || new MapEntry(key, value);\n            if (ES.SameValue(-0, key)) {\n              entry.key = +0; // coerce -0 to +0 in entry\n            }\n            entry.next = this._head;\n            entry.prev = this._head.prev;\n            entry.prev.next = entry;\n            entry.next.prev = entry;\n            this._size += 1;\n            return this;\n          },\n\n          'delete': function (key) {\n            requireMapSlot(this, 'delete');\n            var head = this._head;\n            var i = head;\n            var fkey = fastkey(key, true);\n            if (fkey !== null) {\n              // fast O(1) path\n              if (typeof this._storage[fkey] === 'undefined') {\n                return false;\n              }\n              i = this._storage[fkey].prev;\n              delete this._storage[fkey];\n              // fall through\n            } else if (this._map) {\n              // fast object key path\n              if (!origMapHas.call(this._map, key)) {\n                return false;\n              }\n              i = origMapGet.call(this._map, key).prev;\n              origMapDelete.call(this._map, key);\n              // fall through\n            }\n            while ((i = i.next) !== head) {\n              if (ES.SameValueZero(i.key, key)) {\n                i.key = empty;\n                i.value = empty;\n                i.prev.next = i.next;\n                i.next.prev = i.prev;\n                this._size -= 1;\n                return true;\n              }\n            }\n            return false;\n          },\n\n          clear: function clear() {\n            /* eslint no-multi-assign: 1 */\n            requireMapSlot(this, 'clear');\n            this._map = OrigMap ? new OrigMap() : null;\n            this._size = 0;\n            this._storage = emptyObject();\n            var head = this._head;\n            var i = head;\n            var p = i.next;\n            while ((i = p) !== head) {\n              i.key = empty;\n              i.value = empty;\n              p = i.next;\n              i.next = i.prev = head;\n            }\n            head.next = head.prev = head;\n          },\n\n          keys: function keys() {\n            requireMapSlot(this, 'keys');\n            return new MapIterator(this, 'key');\n          },\n\n          values: function values() {\n            requireMapSlot(this, 'values');\n            return new MapIterator(this, 'value');\n          },\n\n          entries: function entries() {\n            requireMapSlot(this, 'entries');\n            return new MapIterator(this, 'key+value');\n          },\n\n          forEach: function forEach(callback) {\n            requireMapSlot(this, 'forEach');\n            var context = arguments.length > 1 ? arguments[1] : null;\n            var it = this.entries();\n            for (var entry = it.next(); !entry.done; entry = it.next()) {\n              if (context) {\n                _call(callback, context, entry.value[1], entry.value[0], this);\n              } else {\n                callback(entry.value[1], entry.value[0], this);\n              }\n            }\n          }\n        });\n        addIterator(Map$prototype, Map$prototype.entries);\n\n        return MapShim;\n      }()),\n\n      Set: (function () {\n        var isSet = function isSet(set) {\n          return set._es6set && typeof set._storage !== 'undefined';\n        };\n        var requireSetSlot = function requireSetSlot(set, method) {\n          if (!ES.TypeIsObject(set) || !isSet(set)) {\n            // https://github.com/paulmillr/es6-shim/issues/176\n            throw new TypeError('Set.prototype.' + method + ' called on incompatible receiver ' + ES.ToString(set));\n          }\n        };\n\n        // Creating a Map is expensive.  To speed up the common case of\n        // Sets containing only string or numeric keys, we use an object\n        // as backing storage and lazily create a full Map only when\n        // required.\n        var Set$prototype;\n        var SetShim = function Set() {\n          if (!(this instanceof Set)) {\n            throw new TypeError('Constructor Set requires \"new\"');\n          }\n          if (this && this._es6set) {\n            throw new TypeError('Bad construction');\n          }\n          var set = emulateES6construct(this, Set, Set$prototype, {\n            _es6set: true,\n            '[[SetData]]': null,\n            _storage: emptyObject()\n          });\n          if (!set._es6set) {\n            throw new TypeError('bad set');\n          }\n\n          // Optionally initialize Set from iterable\n          if (arguments.length > 0) {\n            addIterableToSet(Set, set, arguments[0]);\n          }\n          return set;\n        };\n        Set$prototype = SetShim.prototype;\n\n        var decodeKey = function (key) {\n          var k = key;\n          if (k === '^null') {\n            return null;\n          } else if (k === '^undefined') {\n            return void 0;\n          } else {\n            var first = k.charAt(0);\n            if (first === '$') {\n              return _strSlice(k, 1);\n            } else if (first === 'n') {\n              return +_strSlice(k, 1);\n            } else if (first === 'b') {\n              return k === 'btrue';\n            }\n          }\n          return +k;\n        };\n        // Switch from the object backing storage to a full Map.\n        var ensureMap = function ensureMap(set) {\n          if (!set['[[SetData]]']) {\n            var m = new collectionShims.Map();\n            set['[[SetData]]'] = m;\n            _forEach(keys(set._storage), function (key) {\n              var k = decodeKey(key);\n              m.set(k, k);\n            });\n            set['[[SetData]]'] = m;\n          }\n          set._storage = null; // free old backing storage\n        };\n\n        Value.getter(SetShim.prototype, 'size', function () {\n          requireSetSlot(this, 'size');\n          if (this._storage) {\n            return keys(this._storage).length;\n          }\n          ensureMap(this);\n          return this['[[SetData]]'].size;\n        });\n\n        defineProperties(SetShim.prototype, {\n          has: function has(key) {\n            requireSetSlot(this, 'has');\n            var fkey;\n            if (this._storage && (fkey = fastkey(key)) !== null) {\n              return !!this._storage[fkey];\n            }\n            ensureMap(this);\n            return this['[[SetData]]'].has(key);\n          },\n\n          add: function add(key) {\n            requireSetSlot(this, 'add');\n            var fkey;\n            if (this._storage && (fkey = fastkey(key)) !== null) {\n              this._storage[fkey] = true;\n              return this;\n            }\n            ensureMap(this);\n            this['[[SetData]]'].set(key, key);\n            return this;\n          },\n\n          'delete': function (key) {\n            requireSetSlot(this, 'delete');\n            var fkey;\n            if (this._storage && (fkey = fastkey(key)) !== null) {\n              var hasFKey = _hasOwnProperty(this._storage, fkey);\n              return (delete this._storage[fkey]) && hasFKey;\n            }\n            ensureMap(this);\n            return this['[[SetData]]']['delete'](key);\n          },\n\n          clear: function clear() {\n            requireSetSlot(this, 'clear');\n            if (this._storage) {\n              this._storage = emptyObject();\n            }\n            if (this['[[SetData]]']) {\n              this['[[SetData]]'].clear();\n            }\n          },\n\n          values: function values() {\n            requireSetSlot(this, 'values');\n            ensureMap(this);\n            return new SetIterator(this['[[SetData]]'].values());\n          },\n\n          entries: function entries() {\n            requireSetSlot(this, 'entries');\n            ensureMap(this);\n            return new SetIterator(this['[[SetData]]'].entries());\n          },\n\n          forEach: function forEach(callback) {\n            requireSetSlot(this, 'forEach');\n            var context = arguments.length > 1 ? arguments[1] : null;\n            var entireSet = this;\n            ensureMap(entireSet);\n            this['[[SetData]]'].forEach(function (value, key) {\n              if (context) {\n                _call(callback, context, key, key, entireSet);\n              } else {\n                callback(key, key, entireSet);\n              }\n            });\n          }\n        });\n        defineProperty(SetShim.prototype, 'keys', SetShim.prototype.values, true);\n        addIterator(SetShim.prototype, SetShim.prototype.values);\n\n        var SetIterator = function SetIterator(it) {\n          this.it = it;\n        };\n        SetIterator.prototype = {\n          isSetIterator: true,\n          next: function next() {\n            if (!this.isSetIterator) {\n              throw new TypeError('Not a SetIterator');\n            }\n            return this.it.next();\n          }\n        };\n        addIterator(SetIterator.prototype);\n\n        return SetShim;\n      }())\n    };\n\n    var isGoogleTranslate = globals.Set && !Set.prototype['delete'] && Set.prototype.remove && Set.prototype.items && Set.prototype.map && Array.isArray(new Set().keys);\n    if (isGoogleTranslate) {\n      // special-case force removal of wildly invalid Set implementation in Google Translate iframes\n      // see https://github.com/paulmillr/es6-shim/issues/438 / https://twitter.com/ljharb/status/849335573114363904\n      globals.Set = collectionShims.Set;\n    }\n    if (globals.Map || globals.Set) {\n      // Safari 8, for example, doesn't accept an iterable.\n      var mapAcceptsArguments = valueOrFalseIfThrows(function () { return new Map([[1, 2]]).get(1) === 2; });\n      if (!mapAcceptsArguments) {\n        globals.Map = function Map() {\n          if (!(this instanceof Map)) {\n            throw new TypeError('Constructor Map requires \"new\"');\n          }\n          var m = new OrigMap();\n          if (arguments.length > 0) {\n            addIterableToMap(Map, m, arguments[0]);\n          }\n          delete m.constructor;\n          Object.setPrototypeOf(m, globals.Map.prototype);\n          return m;\n        };\n        globals.Map.prototype = create(OrigMap.prototype);\n        defineProperty(globals.Map.prototype, 'constructor', globals.Map, true);\n        Value.preserveToString(globals.Map, OrigMap);\n      }\n      var testMap = new Map();\n      var mapUsesSameValueZero = (function () {\n        // Chrome 38-42, node 0.11/0.12, iojs 1/2 also have a bug when the Map has a size > 4\n        var m = new Map([[1, 0], [2, 0], [3, 0], [4, 0]]);\n        m.set(-0, m);\n        return m.get(0) === m && m.get(-0) === m && m.has(0) && m.has(-0);\n      }());\n      var mapSupportsChaining = testMap.set(1, 2) === testMap;\n      if (!mapUsesSameValueZero || !mapSupportsChaining) {\n        overrideNative(Map.prototype, 'set', function set(k, v) {\n          _call(origMapSet, this, k === 0 ? 0 : k, v);\n          return this;\n        });\n      }\n      if (!mapUsesSameValueZero) {\n        defineProperties(Map.prototype, {\n          get: function get(k) {\n            return _call(origMapGet, this, k === 0 ? 0 : k);\n          },\n          has: function has(k) {\n            return _call(origMapHas, this, k === 0 ? 0 : k);\n          }\n        }, true);\n        Value.preserveToString(Map.prototype.get, origMapGet);\n        Value.preserveToString(Map.prototype.has, origMapHas);\n      }\n      var testSet = new Set();\n      var setUsesSameValueZero = Set.prototype['delete'] && Set.prototype.add && Set.prototype.has && (function (s) {\n        s['delete'](0);\n        s.add(-0);\n        return !s.has(0);\n      }(testSet));\n      var setSupportsChaining = testSet.add(1) === testSet;\n      if (!setUsesSameValueZero || !setSupportsChaining) {\n        var origSetAdd = Set.prototype.add;\n        Set.prototype.add = function add(v) {\n          _call(origSetAdd, this, v === 0 ? 0 : v);\n          return this;\n        };\n        Value.preserveToString(Set.prototype.add, origSetAdd);\n      }\n      if (!setUsesSameValueZero) {\n        var origSetHas = Set.prototype.has;\n        Set.prototype.has = function has(v) {\n          return _call(origSetHas, this, v === 0 ? 0 : v);\n        };\n        Value.preserveToString(Set.prototype.has, origSetHas);\n        var origSetDel = Set.prototype['delete'];\n        Set.prototype['delete'] = function SetDelete(v) {\n          return _call(origSetDel, this, v === 0 ? 0 : v);\n        };\n        Value.preserveToString(Set.prototype['delete'], origSetDel);\n      }\n      var mapSupportsSubclassing = supportsSubclassing(globals.Map, function (M) {\n        var m = new M([]);\n        // Firefox 32 is ok with the instantiating the subclass but will\n        // throw when the map is used.\n        m.set(42, 42);\n        return m instanceof M;\n      });\n      // without Object.setPrototypeOf, subclassing is not possible\n      var mapFailsToSupportSubclassing = Object.setPrototypeOf && !mapSupportsSubclassing;\n      var mapRequiresNew = (function () {\n        try {\n          return !(globals.Map() instanceof globals.Map);\n        } catch (e) {\n          return e instanceof TypeError;\n        }\n      }());\n      if (globals.Map.length !== 0 || mapFailsToSupportSubclassing || !mapRequiresNew) {\n        globals.Map = function Map() {\n          if (!(this instanceof Map)) {\n            throw new TypeError('Constructor Map requires \"new\"');\n          }\n          var m = new OrigMap();\n          if (arguments.length > 0) {\n            addIterableToMap(Map, m, arguments[0]);\n          }\n          delete m.constructor;\n          Object.setPrototypeOf(m, Map.prototype);\n          return m;\n        };\n        globals.Map.prototype = OrigMap.prototype;\n        defineProperty(globals.Map.prototype, 'constructor', globals.Map, true);\n        Value.preserveToString(globals.Map, OrigMap);\n      }\n      var setSupportsSubclassing = supportsSubclassing(globals.Set, function (S) {\n        var s = new S([]);\n        s.add(42, 42);\n        return s instanceof S;\n      });\n      // without Object.setPrototypeOf, subclassing is not possible\n      var setFailsToSupportSubclassing = Object.setPrototypeOf && !setSupportsSubclassing;\n      var setRequiresNew = (function () {\n        try {\n          return !(globals.Set() instanceof globals.Set);\n        } catch (e) {\n          return e instanceof TypeError;\n        }\n      }());\n      if (globals.Set.length !== 0 || setFailsToSupportSubclassing || !setRequiresNew) {\n        var OrigSet = globals.Set;\n        globals.Set = function Set() {\n          if (!(this instanceof Set)) {\n            throw new TypeError('Constructor Set requires \"new\"');\n          }\n          var s = new OrigSet();\n          if (arguments.length > 0) {\n            addIterableToSet(Set, s, arguments[0]);\n          }\n          delete s.constructor;\n          Object.setPrototypeOf(s, Set.prototype);\n          return s;\n        };\n        globals.Set.prototype = OrigSet.prototype;\n        defineProperty(globals.Set.prototype, 'constructor', globals.Set, true);\n        Value.preserveToString(globals.Set, OrigSet);\n      }\n      var newMap = new globals.Map();\n      var mapIterationThrowsStopIterator = !valueOrFalseIfThrows(function () {\n        return newMap.keys().next().done;\n      });\n      /*\n        - In Firefox < 23, Map#size is a function.\n        - In all current Firefox, Set#entries/keys/values & Map#clear do not exist\n        - https://bugzilla.mozilla.org/show_bug.cgi?id=869996\n        - In Firefox 24, Map and Set do not implement forEach\n        - In Firefox 25 at least, Map and Set are callable without \"new\"\n      */\n      if (\n        typeof globals.Map.prototype.clear !== 'function' ||\n        new globals.Set().size !== 0 ||\n        newMap.size !== 0 ||\n        typeof globals.Map.prototype.keys !== 'function' ||\n        typeof globals.Set.prototype.keys !== 'function' ||\n        typeof globals.Map.prototype.forEach !== 'function' ||\n        typeof globals.Set.prototype.forEach !== 'function' ||\n        isCallableWithoutNew(globals.Map) ||\n        isCallableWithoutNew(globals.Set) ||\n        typeof newMap.keys().next !== 'function' || // Safari 8\n        mapIterationThrowsStopIterator || // Firefox 25\n        !mapSupportsSubclassing\n      ) {\n        defineProperties(globals, {\n          Map: collectionShims.Map,\n          Set: collectionShims.Set\n        }, true);\n      }\n\n      if (globals.Set.prototype.keys !== globals.Set.prototype.values) {\n        // Fixed in WebKit with https://bugs.webkit.org/show_bug.cgi?id=144190\n        defineProperty(globals.Set.prototype, 'keys', globals.Set.prototype.values, true);\n      }\n\n      // Shim incomplete iterator implementations.\n      addIterator(Object.getPrototypeOf((new globals.Map()).keys()));\n      addIterator(Object.getPrototypeOf((new globals.Set()).keys()));\n\n      if (functionsHaveNames && globals.Set.prototype.has.name !== 'has') {\n        // Microsoft Edge v0.11.10074.0 is missing a name on Set#has\n        var anonymousSetHas = globals.Set.prototype.has;\n        overrideNative(globals.Set.prototype, 'has', function has(key) {\n          return _call(anonymousSetHas, this, key);\n        });\n      }\n    }\n    defineProperties(globals, collectionShims);\n    addDefaultSpecies(globals.Map);\n    addDefaultSpecies(globals.Set);\n  }\n\n  var throwUnlessTargetIsObject = function throwUnlessTargetIsObject(target) {\n    if (!ES.TypeIsObject(target)) {\n      throw new TypeError('target must be an object');\n    }\n  };\n\n  // Some Reflect methods are basically the same as\n  // those on the Object global, except that a TypeError is thrown if\n  // target isn't an object. As well as returning a boolean indicating\n  // the success of the operation.\n  var ReflectShims = {\n    // Apply method in a functional form.\n    apply: function apply() {\n      return ES.Call(ES.Call, null, arguments);\n    },\n\n    // New operator in a functional form.\n    construct: function construct(constructor, args) {\n      if (!ES.IsConstructor(constructor)) {\n        throw new TypeError('First argument must be a constructor.');\n      }\n      var newTarget = arguments.length > 2 ? arguments[2] : constructor;\n      if (!ES.IsConstructor(newTarget)) {\n        throw new TypeError('new.target must be a constructor.');\n      }\n      return ES.Construct(constructor, args, newTarget, 'internal');\n    },\n\n    // When deleting a non-existent or configurable property,\n    // true is returned.\n    // When attempting to delete a non-configurable property,\n    // it will return false.\n    deleteProperty: function deleteProperty(target, key) {\n      throwUnlessTargetIsObject(target);\n      if (supportsDescriptors) {\n        var desc = Object.getOwnPropertyDescriptor(target, key);\n\n        if (desc && !desc.configurable) {\n          return false;\n        }\n      }\n\n      // Will return true.\n      return delete target[key];\n    },\n\n    has: function has(target, key) {\n      throwUnlessTargetIsObject(target);\n      return key in target;\n    }\n  };\n\n  if (Object.getOwnPropertyNames) {\n    Object.assign(ReflectShims, {\n      // Basically the result of calling the internal [[OwnPropertyKeys]].\n      // Concatenating propertyNames and propertySymbols should do the trick.\n      // This should continue to work together with a Symbol shim\n      // which overrides Object.getOwnPropertyNames and implements\n      // Object.getOwnPropertySymbols.\n      ownKeys: function ownKeys(target) {\n        throwUnlessTargetIsObject(target);\n        var keys = Object.getOwnPropertyNames(target);\n\n        if (ES.IsCallable(Object.getOwnPropertySymbols)) {\n          _pushApply(keys, Object.getOwnPropertySymbols(target));\n        }\n\n        return keys;\n      }\n    });\n  }\n\n  var callAndCatchException = function ConvertExceptionToBoolean(func) {\n    return !throwsError(func);\n  };\n\n  if (Object.preventExtensions) {\n    Object.assign(ReflectShims, {\n      isExtensible: function isExtensible(target) {\n        throwUnlessTargetIsObject(target);\n        return Object.isExtensible(target);\n      },\n      preventExtensions: function preventExtensions(target) {\n        throwUnlessTargetIsObject(target);\n        return callAndCatchException(function () {\n          return Object.preventExtensions(target);\n        });\n      }\n    });\n  }\n\n  if (supportsDescriptors) {\n    var internalGet = function get(target, key, receiver) {\n      var desc = Object.getOwnPropertyDescriptor(target, key);\n\n      if (!desc) {\n        var parent = Object.getPrototypeOf(target);\n\n        if (parent === null) {\n          return void 0;\n        }\n\n        return internalGet(parent, key, receiver);\n      }\n\n      if ('value' in desc) {\n        return desc.value;\n      }\n\n      if (desc.get) {\n        return ES.Call(desc.get, receiver);\n      }\n\n      return void 0;\n    };\n\n    var internalSet = function set(target, key, value, receiver) {\n      var desc = Object.getOwnPropertyDescriptor(target, key);\n\n      if (!desc) {\n        var parent = Object.getPrototypeOf(target);\n\n        if (parent !== null) {\n          return internalSet(parent, key, value, receiver);\n        }\n\n        desc = {\n          value: void 0,\n          writable: true,\n          enumerable: true,\n          configurable: true\n        };\n      }\n\n      if ('value' in desc) {\n        if (!desc.writable) {\n          return false;\n        }\n\n        if (!ES.TypeIsObject(receiver)) {\n          return false;\n        }\n\n        var existingDesc = Object.getOwnPropertyDescriptor(receiver, key);\n\n        if (existingDesc) {\n          return Reflect.defineProperty(receiver, key, {\n            value: value\n          });\n        } else {\n          return Reflect.defineProperty(receiver, key, {\n            value: value,\n            writable: true,\n            enumerable: true,\n            configurable: true\n          });\n        }\n      }\n\n      if (desc.set) {\n        _call(desc.set, receiver, value);\n        return true;\n      }\n\n      return false;\n    };\n\n    Object.assign(ReflectShims, {\n      defineProperty: function defineProperty(target, propertyKey, attributes) {\n        throwUnlessTargetIsObject(target);\n        return callAndCatchException(function () {\n          return Object.defineProperty(target, propertyKey, attributes);\n        });\n      },\n\n      getOwnPropertyDescriptor: function getOwnPropertyDescriptor(target, propertyKey) {\n        throwUnlessTargetIsObject(target);\n        return Object.getOwnPropertyDescriptor(target, propertyKey);\n      },\n\n      // Syntax in a functional form.\n      get: function get(target, key) {\n        throwUnlessTargetIsObject(target);\n        var receiver = arguments.length > 2 ? arguments[2] : target;\n\n        return internalGet(target, key, receiver);\n      },\n\n      set: function set(target, key, value) {\n        throwUnlessTargetIsObject(target);\n        var receiver = arguments.length > 3 ? arguments[3] : target;\n\n        return internalSet(target, key, value, receiver);\n      }\n    });\n  }\n\n  if (Object.getPrototypeOf) {\n    var objectDotGetPrototypeOf = Object.getPrototypeOf;\n    ReflectShims.getPrototypeOf = function getPrototypeOf(target) {\n      throwUnlessTargetIsObject(target);\n      return objectDotGetPrototypeOf(target);\n    };\n  }\n\n  if (Object.setPrototypeOf && ReflectShims.getPrototypeOf) {\n    var willCreateCircularPrototype = function (object, lastProto) {\n      var proto = lastProto;\n      while (proto) {\n        if (object === proto) {\n          return true;\n        }\n        proto = ReflectShims.getPrototypeOf(proto);\n      }\n      return false;\n    };\n\n    Object.assign(ReflectShims, {\n      // Sets the prototype of the given object.\n      // Returns true on success, otherwise false.\n      setPrototypeOf: function setPrototypeOf(object, proto) {\n        throwUnlessTargetIsObject(object);\n        if (proto !== null && !ES.TypeIsObject(proto)) {\n          throw new TypeError('proto must be an object or null');\n        }\n\n        // If they already are the same, we're done.\n        if (proto === Reflect.getPrototypeOf(object)) {\n          return true;\n        }\n\n        // Cannot alter prototype if object not extensible.\n        if (Reflect.isExtensible && !Reflect.isExtensible(object)) {\n          return false;\n        }\n\n        // Ensure that we do not create a circular prototype chain.\n        if (willCreateCircularPrototype(object, proto)) {\n          return false;\n        }\n\n        Object.setPrototypeOf(object, proto);\n\n        return true;\n      }\n    });\n  }\n  var defineOrOverrideReflectProperty = function (key, shim) {\n    if (!ES.IsCallable(globals.Reflect[key])) {\n      defineProperty(globals.Reflect, key, shim);\n    } else {\n      var acceptsPrimitives = valueOrFalseIfThrows(function () {\n        globals.Reflect[key](1);\n        globals.Reflect[key](NaN);\n        globals.Reflect[key](true);\n        return true;\n      });\n      if (acceptsPrimitives) {\n        overrideNative(globals.Reflect, key, shim);\n      }\n    }\n  };\n  Object.keys(ReflectShims).forEach(function (key) {\n    defineOrOverrideReflectProperty(key, ReflectShims[key]);\n  });\n  var originalReflectGetProto = globals.Reflect.getPrototypeOf;\n  if (functionsHaveNames && originalReflectGetProto && originalReflectGetProto.name !== 'getPrototypeOf') {\n    overrideNative(globals.Reflect, 'getPrototypeOf', function getPrototypeOf(target) {\n      return _call(originalReflectGetProto, globals.Reflect, target);\n    });\n  }\n  if (globals.Reflect.setPrototypeOf) {\n    if (valueOrFalseIfThrows(function () {\n      globals.Reflect.setPrototypeOf(1, {});\n      return true;\n    })) {\n      overrideNative(globals.Reflect, 'setPrototypeOf', ReflectShims.setPrototypeOf);\n    }\n  }\n  if (globals.Reflect.defineProperty) {\n    if (!valueOrFalseIfThrows(function () {\n      var basic = !globals.Reflect.defineProperty(1, 'test', { value: 1 });\n      // \"extensible\" fails on Edge 0.12\n      var extensible = typeof Object.preventExtensions !== 'function' || !globals.Reflect.defineProperty(Object.preventExtensions({}), 'test', {});\n      return basic && extensible;\n    })) {\n      overrideNative(globals.Reflect, 'defineProperty', ReflectShims.defineProperty);\n    }\n  }\n  if (globals.Reflect.construct) {\n    if (!valueOrFalseIfThrows(function () {\n      var F = function F() {};\n      return globals.Reflect.construct(function () {}, [], F) instanceof F;\n    })) {\n      overrideNative(globals.Reflect, 'construct', ReflectShims.construct);\n    }\n  }\n\n  if (String(new Date(NaN)) !== 'Invalid Date') {\n    var dateToString = Date.prototype.toString;\n    var shimmedDateToString = function toString() {\n      var valueOf = +this;\n      if (valueOf !== valueOf) {\n        return 'Invalid Date';\n      }\n      return ES.Call(dateToString, this);\n    };\n    overrideNative(Date.prototype, 'toString', shimmedDateToString);\n  }\n\n  // Annex B HTML methods\n  // http://www.ecma-international.org/ecma-262/6.0/#sec-additional-properties-of-the-string.prototype-object\n  var stringHTMLshims = {\n    anchor: function anchor(name) { return ES.CreateHTML(this, 'a', 'name', name); },\n    big: function big() { return ES.CreateHTML(this, 'big', '', ''); },\n    blink: function blink() { return ES.CreateHTML(this, 'blink', '', ''); },\n    bold: function bold() { return ES.CreateHTML(this, 'b', '', ''); },\n    fixed: function fixed() { return ES.CreateHTML(this, 'tt', '', ''); },\n    fontcolor: function fontcolor(color) { return ES.CreateHTML(this, 'font', 'color', color); },\n    fontsize: function fontsize(size) { return ES.CreateHTML(this, 'font', 'size', size); },\n    italics: function italics() { return ES.CreateHTML(this, 'i', '', ''); },\n    link: function link(url) { return ES.CreateHTML(this, 'a', 'href', url); },\n    small: function small() { return ES.CreateHTML(this, 'small', '', ''); },\n    strike: function strike() { return ES.CreateHTML(this, 'strike', '', ''); },\n    sub: function sub() { return ES.CreateHTML(this, 'sub', '', ''); },\n    sup: function sub() { return ES.CreateHTML(this, 'sup', '', ''); }\n  };\n  _forEach(Object.keys(stringHTMLshims), function (key) {\n    var method = String.prototype[key];\n    var shouldOverwrite = false;\n    if (ES.IsCallable(method)) {\n      var output = _call(method, '', ' \" ');\n      var quotesCount = _concat([], output.match(/\"/g)).length;\n      shouldOverwrite = output !== output.toLowerCase() || quotesCount > 2;\n    } else {\n      shouldOverwrite = true;\n    }\n    if (shouldOverwrite) {\n      overrideNative(String.prototype, key, stringHTMLshims[key]);\n    }\n  });\n\n  var JSONstringifiesSymbols = (function () {\n    // Microsoft Edge v0.12 stringifies Symbols incorrectly\n    if (!hasSymbols) { return false; } // Symbols are not supported\n    var stringify = typeof JSON === 'object' && typeof JSON.stringify === 'function' ? JSON.stringify : null;\n    if (!stringify) { return false; } // JSON.stringify is not supported\n    if (typeof stringify(Symbol()) !== 'undefined') { return true; } // Symbols should become `undefined`\n    if (stringify([Symbol()]) !== '[null]') { return true; } // Symbols in arrays should become `null`\n    var obj = { a: Symbol() };\n    obj[Symbol()] = true;\n    if (stringify(obj) !== '{}') { return true; } // Symbol-valued keys *and* Symbol-valued properties should be omitted\n    return false;\n  }());\n  var JSONstringifyAcceptsObjectSymbol = valueOrFalseIfThrows(function () {\n    // Chrome 45 throws on stringifying object symbols\n    if (!hasSymbols) { return true; } // Symbols are not supported\n    return JSON.stringify(Object(Symbol())) === '{}' && JSON.stringify([Object(Symbol())]) === '[{}]';\n  });\n  if (JSONstringifiesSymbols || !JSONstringifyAcceptsObjectSymbol) {\n    var origStringify = JSON.stringify;\n    overrideNative(JSON, 'stringify', function stringify(value) {\n      if (typeof value === 'symbol') { return; }\n      var replacer;\n      if (arguments.length > 1) {\n        replacer = arguments[1];\n      }\n      var args = [value];\n      if (!isArray(replacer)) {\n        var replaceFn = ES.IsCallable(replacer) ? replacer : null;\n        var wrappedReplacer = function (key, val) {\n          var parsedValue = replaceFn ? _call(replaceFn, this, key, val) : val;\n          if (typeof parsedValue !== 'symbol') {\n            if (Type.symbol(parsedValue)) {\n              return assignTo({})(parsedValue);\n            } else {\n              return parsedValue;\n            }\n          }\n        };\n        args.push(wrappedReplacer);\n      } else {\n        // create wrapped replacer that handles an array replacer?\n        args.push(replacer);\n      }\n      if (arguments.length > 2) {\n        args.push(arguments[2]);\n      }\n      return origStringify.apply(this, args);\n    });\n  }\n\n  return globals;\n}));\n", "/** @license React v16.6.1\n * react.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';var k=require(\"object-assign\"),n=\"function\"===typeof Symbol&&Symbol.for,p=n?Symbol.for(\"react.element\"):60103,q=n?Symbol.for(\"react.portal\"):60106,r=n?Symbol.for(\"react.fragment\"):60107,t=n?Symbol.for(\"react.strict_mode\"):60108,u=n?Symbol.for(\"react.profiler\"):60114,v=n?Symbol.for(\"react.provider\"):60109,w=n?Symbol.for(\"react.context\"):60110,x=n?Symbol.for(\"react.concurrent_mode\"):60111,y=n?Symbol.for(\"react.forward_ref\"):60112,z=n?Symbol.for(\"react.suspense\"):60113,A=n?Symbol.for(\"react.memo\"):\n60115,B=n?Symbol.for(\"react.lazy\"):60116,C=\"function\"===typeof Symbol&&Symbol.iterator;function aa(a,b,e,c,d,g,h,f){if(!a){a=void 0;if(void 0===b)a=Error(\"Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.\");else{var l=[e,c,d,g,h,f],m=0;a=Error(b.replace(/%s/g,function(){return l[m++]}));a.name=\"Invariant Violation\"}a.framesToPop=1;throw a;}}\nfunction D(a){for(var b=arguments.length-1,e=\"https://reactjs.org/docs/error-decoder.html?invariant=\"+a,c=0;c<b;c++)e+=\"&args[]=\"+encodeURIComponent(arguments[c+1]);aa(!1,\"Minified React error #\"+a+\"; visit %s for the full message or use the non-minified dev environment for full errors and additional helpful warnings. \",e)}var E={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},F={};\nfunction G(a,b,e){this.props=a;this.context=b;this.refs=F;this.updater=e||E}G.prototype.isReactComponent={};G.prototype.setState=function(a,b){\"object\"!==typeof a&&\"function\"!==typeof a&&null!=a?D(\"85\"):void 0;this.updater.enqueueSetState(this,a,b,\"setState\")};G.prototype.forceUpdate=function(a){this.updater.enqueueForceUpdate(this,a,\"forceUpdate\")};function H(){}H.prototype=G.prototype;function I(a,b,e){this.props=a;this.context=b;this.refs=F;this.updater=e||E}var J=I.prototype=new H;\nJ.constructor=I;k(J,G.prototype);J.isPureReactComponent=!0;var K={current:null,currentDispatcher:null},L=Object.prototype.hasOwnProperty,M={key:!0,ref:!0,__self:!0,__source:!0};\nfunction N(a,b,e){var c=void 0,d={},g=null,h=null;if(null!=b)for(c in void 0!==b.ref&&(h=b.ref),void 0!==b.key&&(g=\"\"+b.key),b)L.call(b,c)&&!M.hasOwnProperty(c)&&(d[c]=b[c]);var f=arguments.length-2;if(1===f)d.children=e;else if(1<f){for(var l=Array(f),m=0;m<f;m++)l[m]=arguments[m+2];d.children=l}if(a&&a.defaultProps)for(c in f=a.defaultProps,f)void 0===d[c]&&(d[c]=f[c]);return{$$typeof:p,type:a,key:g,ref:h,props:d,_owner:K.current}}\nfunction ba(a,b){return{$$typeof:p,type:a.type,key:b,ref:a.ref,props:a.props,_owner:a._owner}}function O(a){return\"object\"===typeof a&&null!==a&&a.$$typeof===p}function escape(a){var b={\"=\":\"=0\",\":\":\"=2\"};return\"$\"+(\"\"+a).replace(/[=:]/g,function(a){return b[a]})}var P=/\\/+/g,Q=[];function R(a,b,e,c){if(Q.length){var d=Q.pop();d.result=a;d.keyPrefix=b;d.func=e;d.context=c;d.count=0;return d}return{result:a,keyPrefix:b,func:e,context:c,count:0}}\nfunction S(a){a.result=null;a.keyPrefix=null;a.func=null;a.context=null;a.count=0;10>Q.length&&Q.push(a)}\nfunction T(a,b,e,c){var d=typeof a;if(\"undefined\"===d||\"boolean\"===d)a=null;var g=!1;if(null===a)g=!0;else switch(d){case \"string\":case \"number\":g=!0;break;case \"object\":switch(a.$$typeof){case p:case q:g=!0}}if(g)return e(c,a,\"\"===b?\".\"+U(a,0):b),1;g=0;b=\"\"===b?\".\":b+\":\";if(Array.isArray(a))for(var h=0;h<a.length;h++){d=a[h];var f=b+U(d,h);g+=T(d,f,e,c)}else if(null===a||\"object\"!==typeof a?f=null:(f=C&&a[C]||a[\"@@iterator\"],f=\"function\"===typeof f?f:null),\"function\"===typeof f)for(a=f.call(a),h=\n0;!(d=a.next()).done;)d=d.value,f=b+U(d,h++),g+=T(d,f,e,c);else\"object\"===d&&(e=\"\"+a,D(\"31\",\"[object Object]\"===e?\"object with keys {\"+Object.keys(a).join(\", \")+\"}\":e,\"\"));return g}function V(a,b,e){return null==a?0:T(a,\"\",b,e)}function U(a,b){return\"object\"===typeof a&&null!==a&&null!=a.key?escape(a.key):b.toString(36)}function ca(a,b){a.func.call(a.context,b,a.count++)}\nfunction da(a,b,e){var c=a.result,d=a.keyPrefix;a=a.func.call(a.context,b,a.count++);Array.isArray(a)?W(a,c,e,function(a){return a}):null!=a&&(O(a)&&(a=ba(a,d+(!a.key||b&&b.key===a.key?\"\":(\"\"+a.key).replace(P,\"$&/\")+\"/\")+e)),c.push(a))}function W(a,b,e,c,d){var g=\"\";null!=e&&(g=(\"\"+e).replace(P,\"$&/\")+\"/\");b=R(b,g,c,d);V(a,da,b);S(b)}\nvar X={Children:{map:function(a,b,e){if(null==a)return a;var c=[];W(a,c,null,b,e);return c},forEach:function(a,b,e){if(null==a)return a;b=R(null,null,b,e);V(a,ca,b);S(b)},count:function(a){return V(a,function(){return null},null)},toArray:function(a){var b=[];W(a,b,null,function(a){return a});return b},only:function(a){O(a)?void 0:D(\"143\");return a}},createRef:function(){return{current:null}},Component:G,PureComponent:I,createContext:function(a,b){void 0===b&&(b=null);a={$$typeof:w,_calculateChangedBits:b,\n_currentValue:a,_currentValue2:a,_threadCount:0,Provider:null,Consumer:null};a.Provider={$$typeof:v,_context:a};return a.Consumer=a},forwardRef:function(a){return{$$typeof:y,render:a}},lazy:function(a){return{$$typeof:B,_ctor:a,_status:-1,_result:null}},memo:function(a,b){return{$$typeof:A,type:a,compare:void 0===b?null:b}},Fragment:r,StrictMode:t,Suspense:z,createElement:N,cloneElement:function(a,b,e){null===a||void 0===a?D(\"267\",a):void 0;var c=void 0,d=k({},a.props),g=a.key,h=a.ref,f=a._owner;\nif(null!=b){void 0!==b.ref&&(h=b.ref,f=K.current);void 0!==b.key&&(g=\"\"+b.key);var l=void 0;a.type&&a.type.defaultProps&&(l=a.type.defaultProps);for(c in b)L.call(b,c)&&!M.hasOwnProperty(c)&&(d[c]=void 0===b[c]&&void 0!==l?l[c]:b[c])}c=arguments.length-2;if(1===c)d.children=e;else if(1<c){l=Array(c);for(var m=0;m<c;m++)l[m]=arguments[m+2];d.children=l}return{$$typeof:p,type:a.type,key:g,ref:h,props:d,_owner:f}},createFactory:function(a){var b=N.bind(null,a);b.type=a;return b},isValidElement:O,version:\"16.6.3\",\n__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:{ReactCurrentOwner:K,assign:k}};X.unstable_ConcurrentMode=x;X.unstable_Profiler=u;var Y={default:X},Z=Y&&X||Y;module.exports=Z.default||Z;\n", "/** @license React v16.6.1\n * react-dom.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n/*\n Modernizr 3.0.0pre (Custom Build) | MIT\n*/\n'use strict';var aa=require(\"react\"),n=require(\"object-assign\"),ba=require(\"scheduler\");function ca(a,b,c,d,e,f,g,h){if(!a){a=void 0;if(void 0===b)a=Error(\"Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.\");else{var k=[c,d,e,f,g,h],l=0;a=Error(b.replace(/%s/g,function(){return k[l++]}));a.name=\"Invariant Violation\"}a.framesToPop=1;throw a;}}\nfunction t(a){for(var b=arguments.length-1,c=\"https://reactjs.org/docs/error-decoder.html?invariant=\"+a,d=0;d<b;d++)c+=\"&args[]=\"+encodeURIComponent(arguments[d+1]);ca(!1,\"Minified React error #\"+a+\"; visit %s for the full message or use the non-minified dev environment for full errors and additional helpful warnings. \",c)}aa?void 0:t(\"227\");function da(a,b,c,d,e,f,g,h,k){var l=Array.prototype.slice.call(arguments,3);try{b.apply(c,l)}catch(m){this.onError(m)}}\nvar ea=!1,fa=null,ha=!1,ia=null,ja={onError:function(a){ea=!0;fa=a}};function ka(a,b,c,d,e,f,g,h,k){ea=!1;fa=null;da.apply(ja,arguments)}function la(a,b,c,d,e,f,g,h,k){ka.apply(this,arguments);if(ea){if(ea){var l=fa;ea=!1;fa=null}else t(\"198\"),l=void 0;ha||(ha=!0,ia=l)}}var ma=null,na={};\nfunction oa(){if(ma)for(var a in na){var b=na[a],c=ma.indexOf(a);-1<c?void 0:t(\"96\",a);if(!pa[c]){b.extractEvents?void 0:t(\"97\",a);pa[c]=b;c=b.eventTypes;for(var d in c){var e=void 0;var f=c[d],g=b,h=d;qa.hasOwnProperty(h)?t(\"99\",h):void 0;qa[h]=f;var k=f.phasedRegistrationNames;if(k){for(e in k)k.hasOwnProperty(e)&&ra(k[e],g,h);e=!0}else f.registrationName?(ra(f.registrationName,g,h),e=!0):e=!1;e?void 0:t(\"98\",d,a)}}}}\nfunction ra(a,b,c){sa[a]?t(\"100\",a):void 0;sa[a]=b;ta[a]=b.eventTypes[c].dependencies}var pa=[],qa={},sa={},ta={},ua=null,va=null,wa=null;function xa(a,b,c){var d=a.type||\"unknown-event\";a.currentTarget=wa(c);la(d,b,void 0,a);a.currentTarget=null}function ya(a,b){null==b?t(\"30\"):void 0;if(null==a)return b;if(Array.isArray(a)){if(Array.isArray(b))return a.push.apply(a,b),a;a.push(b);return a}return Array.isArray(b)?[a].concat(b):[a,b]}\nfunction za(a,b,c){Array.isArray(a)?a.forEach(b,c):a&&b.call(c,a)}var Aa=null;function Ba(a){if(a){var b=a._dispatchListeners,c=a._dispatchInstances;if(Array.isArray(b))for(var d=0;d<b.length&&!a.isPropagationStopped();d++)xa(a,b[d],c[d]);else b&&xa(a,b,c);a._dispatchListeners=null;a._dispatchInstances=null;a.isPersistent()||a.constructor.release(a)}}\nvar Ca={injectEventPluginOrder:function(a){ma?t(\"101\"):void 0;ma=Array.prototype.slice.call(a);oa()},injectEventPluginsByName:function(a){var b=!1,c;for(c in a)if(a.hasOwnProperty(c)){var d=a[c];na.hasOwnProperty(c)&&na[c]===d||(na[c]?t(\"102\",c):void 0,na[c]=d,b=!0)}b&&oa()}};\nfunction Da(a,b){var c=a.stateNode;if(!c)return null;var d=ua(c);if(!d)return null;c=d[b];a:switch(b){case \"onClick\":case \"onClickCapture\":case \"onDoubleClick\":case \"onDoubleClickCapture\":case \"onMouseDown\":case \"onMouseDownCapture\":case \"onMouseMove\":case \"onMouseMoveCapture\":case \"onMouseUp\":case \"onMouseUpCapture\":(d=!d.disabled)||(a=a.type,d=!(\"button\"===a||\"input\"===a||\"select\"===a||\"textarea\"===a));a=!d;break a;default:a=!1}if(a)return null;c&&\"function\"!==typeof c?t(\"231\",b,typeof c):void 0;\nreturn c}function Ea(a){null!==a&&(Aa=ya(Aa,a));a=Aa;Aa=null;if(a&&(za(a,Ba),Aa?t(\"95\"):void 0,ha))throw a=ia,ha=!1,ia=null,a;}var Fa=Math.random().toString(36).slice(2),Ga=\"__reactInternalInstance$\"+Fa,Ha=\"__reactEventHandlers$\"+Fa;function Ia(a){if(a[Ga])return a[Ga];for(;!a[Ga];)if(a.parentNode)a=a.parentNode;else return null;a=a[Ga];return 5===a.tag||6===a.tag?a:null}function Ja(a){a=a[Ga];return!a||5!==a.tag&&6!==a.tag?null:a}\nfunction Ka(a){if(5===a.tag||6===a.tag)return a.stateNode;t(\"33\")}function La(a){return a[Ha]||null}function Ma(a){do a=a.return;while(a&&5!==a.tag);return a?a:null}function Na(a,b,c){if(b=Da(a,c.dispatchConfig.phasedRegistrationNames[b]))c._dispatchListeners=ya(c._dispatchListeners,b),c._dispatchInstances=ya(c._dispatchInstances,a)}\nfunction Oa(a){if(a&&a.dispatchConfig.phasedRegistrationNames){for(var b=a._targetInst,c=[];b;)c.push(b),b=Ma(b);for(b=c.length;0<b--;)Na(c[b],\"captured\",a);for(b=0;b<c.length;b++)Na(c[b],\"bubbled\",a)}}function Pa(a,b,c){a&&c&&c.dispatchConfig.registrationName&&(b=Da(a,c.dispatchConfig.registrationName))&&(c._dispatchListeners=ya(c._dispatchListeners,b),c._dispatchInstances=ya(c._dispatchInstances,a))}function Qa(a){a&&a.dispatchConfig.registrationName&&Pa(a._targetInst,null,a)}\nfunction Ra(a){za(a,Oa)}var Sa=!(\"undefined\"===typeof window||!window.document||!window.document.createElement);function Ta(a,b){var c={};c[a.toLowerCase()]=b.toLowerCase();c[\"Webkit\"+a]=\"webkit\"+b;c[\"Moz\"+a]=\"moz\"+b;return c}var Ua={animationend:Ta(\"Animation\",\"AnimationEnd\"),animationiteration:Ta(\"Animation\",\"AnimationIteration\"),animationstart:Ta(\"Animation\",\"AnimationStart\"),transitionend:Ta(\"Transition\",\"TransitionEnd\")},Va={},Wa={};\nSa&&(Wa=document.createElement(\"div\").style,\"AnimationEvent\"in window||(delete Ua.animationend.animation,delete Ua.animationiteration.animation,delete Ua.animationstart.animation),\"TransitionEvent\"in window||delete Ua.transitionend.transition);function Xa(a){if(Va[a])return Va[a];if(!Ua[a])return a;var b=Ua[a],c;for(c in b)if(b.hasOwnProperty(c)&&c in Wa)return Va[a]=b[c];return a}\nvar Ya=Xa(\"animationend\"),Za=Xa(\"animationiteration\"),$a=Xa(\"animationstart\"),ab=Xa(\"transitionend\"),bb=\"abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange seeked seeking stalled suspend timeupdate volumechange waiting\".split(\" \"),cb=null,eb=null,fb=null;\nfunction gb(){if(fb)return fb;var a,b=eb,c=b.length,d,e=\"value\"in cb?cb.value:cb.textContent,f=e.length;for(a=0;a<c&&b[a]===e[a];a++);var g=c-a;for(d=1;d<=g&&b[c-d]===e[f-d];d++);return fb=e.slice(a,1<d?1-d:void 0)}function hb(){return!0}function ib(){return!1}\nfunction A(a,b,c,d){this.dispatchConfig=a;this._targetInst=b;this.nativeEvent=c;a=this.constructor.Interface;for(var e in a)a.hasOwnProperty(e)&&((b=a[e])?this[e]=b(c):\"target\"===e?this.target=d:this[e]=c[e]);this.isDefaultPrevented=(null!=c.defaultPrevented?c.defaultPrevented:!1===c.returnValue)?hb:ib;this.isPropagationStopped=ib;return this}\nn(A.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():\"unknown\"!==typeof a.returnValue&&(a.returnValue=!1),this.isDefaultPrevented=hb)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():\"unknown\"!==typeof a.cancelBubble&&(a.cancelBubble=!0),this.isPropagationStopped=hb)},persist:function(){this.isPersistent=hb},isPersistent:ib,destructor:function(){var a=this.constructor.Interface,\nb;for(b in a)this[b]=null;this.nativeEvent=this._targetInst=this.dispatchConfig=null;this.isPropagationStopped=this.isDefaultPrevented=ib;this._dispatchInstances=this._dispatchListeners=null}});A.Interface={type:null,target:null,currentTarget:function(){return null},eventPhase:null,bubbles:null,cancelable:null,timeStamp:function(a){return a.timeStamp||Date.now()},defaultPrevented:null,isTrusted:null};\nA.extend=function(a){function b(){}function c(){return d.apply(this,arguments)}var d=this;b.prototype=d.prototype;var e=new b;n(e,c.prototype);c.prototype=e;c.prototype.constructor=c;c.Interface=n({},d.Interface,a);c.extend=d.extend;jb(c);return c};jb(A);function kb(a,b,c,d){if(this.eventPool.length){var e=this.eventPool.pop();this.call(e,a,b,c,d);return e}return new this(a,b,c,d)}function lb(a){a instanceof this?void 0:t(\"279\");a.destructor();10>this.eventPool.length&&this.eventPool.push(a)}\nfunction jb(a){a.eventPool=[];a.getPooled=kb;a.release=lb}var mb=A.extend({data:null}),nb=A.extend({data:null}),ob=[9,13,27,32],pb=Sa&&\"CompositionEvent\"in window,qb=null;Sa&&\"documentMode\"in document&&(qb=document.documentMode);\nvar rb=Sa&&\"TextEvent\"in window&&!qb,sb=Sa&&(!pb||qb&&8<qb&&11>=qb),tb=String.fromCharCode(32),ub={beforeInput:{phasedRegistrationNames:{bubbled:\"onBeforeInput\",captured:\"onBeforeInputCapture\"},dependencies:[\"compositionend\",\"keypress\",\"textInput\",\"paste\"]},compositionEnd:{phasedRegistrationNames:{bubbled:\"onCompositionEnd\",captured:\"onCompositionEndCapture\"},dependencies:\"blur compositionend keydown keypress keyup mousedown\".split(\" \")},compositionStart:{phasedRegistrationNames:{bubbled:\"onCompositionStart\",\ncaptured:\"onCompositionStartCapture\"},dependencies:\"blur compositionstart keydown keypress keyup mousedown\".split(\" \")},compositionUpdate:{phasedRegistrationNames:{bubbled:\"onCompositionUpdate\",captured:\"onCompositionUpdateCapture\"},dependencies:\"blur compositionupdate keydown keypress keyup mousedown\".split(\" \")}},vb=!1;\nfunction wb(a,b){switch(a){case \"keyup\":return-1!==ob.indexOf(b.keyCode);case \"keydown\":return 229!==b.keyCode;case \"keypress\":case \"mousedown\":case \"blur\":return!0;default:return!1}}function xb(a){a=a.detail;return\"object\"===typeof a&&\"data\"in a?a.data:null}var yb=!1;function zb(a,b){switch(a){case \"compositionend\":return xb(b);case \"keypress\":if(32!==b.which)return null;vb=!0;return tb;case \"textInput\":return a=b.data,a===tb&&vb?null:a;default:return null}}\nfunction Ab(a,b){if(yb)return\"compositionend\"===a||!pb&&wb(a,b)?(a=gb(),fb=eb=cb=null,yb=!1,a):null;switch(a){case \"paste\":return null;case \"keypress\":if(!(b.ctrlKey||b.altKey||b.metaKey)||b.ctrlKey&&b.altKey){if(b.char&&1<b.char.length)return b.char;if(b.which)return String.fromCharCode(b.which)}return null;case \"compositionend\":return sb&&\"ko\"!==b.locale?null:b.data;default:return null}}\nvar Bb={eventTypes:ub,extractEvents:function(a,b,c,d){var e=void 0;var f=void 0;if(pb)b:{switch(a){case \"compositionstart\":e=ub.compositionStart;break b;case \"compositionend\":e=ub.compositionEnd;break b;case \"compositionupdate\":e=ub.compositionUpdate;break b}e=void 0}else yb?wb(a,c)&&(e=ub.compositionEnd):\"keydown\"===a&&229===c.keyCode&&(e=ub.compositionStart);e?(sb&&\"ko\"!==c.locale&&(yb||e!==ub.compositionStart?e===ub.compositionEnd&&yb&&(f=gb()):(cb=d,eb=\"value\"in cb?cb.value:cb.textContent,yb=\n!0)),e=mb.getPooled(e,b,c,d),f?e.data=f:(f=xb(c),null!==f&&(e.data=f)),Ra(e),f=e):f=null;(a=rb?zb(a,c):Ab(a,c))?(b=nb.getPooled(ub.beforeInput,b,c,d),b.data=a,Ra(b)):b=null;return null===f?b:null===b?f:[f,b]}},Cb=null,Db=null,Eb=null;function Hb(a){if(a=va(a)){\"function\"!==typeof Cb?t(\"280\"):void 0;var b=ua(a.stateNode);Cb(a.stateNode,a.type,b)}}function Ib(a){Db?Eb?Eb.push(a):Eb=[a]:Db=a}function Jb(){if(Db){var a=Db,b=Eb;Eb=Db=null;Hb(a);if(b)for(a=0;a<b.length;a++)Hb(b[a])}}\nfunction Kb(a,b){return a(b)}function Lb(a,b,c){return a(b,c)}function Mb(){}var Nb=!1;function Ob(a,b){if(Nb)return a(b);Nb=!0;try{return Kb(a,b)}finally{if(Nb=!1,null!==Db||null!==Eb)Mb(),Jb()}}var Pb={color:!0,date:!0,datetime:!0,\"datetime-local\":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Qb(a){var b=a&&a.nodeName&&a.nodeName.toLowerCase();return\"input\"===b?!!Pb[a.type]:\"textarea\"===b?!0:!1}\nfunction Rb(a){a=a.target||a.srcElement||window;a.correspondingUseElement&&(a=a.correspondingUseElement);return 3===a.nodeType?a.parentNode:a}function Sb(a){if(!Sa)return!1;a=\"on\"+a;var b=a in document;b||(b=document.createElement(\"div\"),b.setAttribute(a,\"return;\"),b=\"function\"===typeof b[a]);return b}function Tb(a){var b=a.type;return(a=a.nodeName)&&\"input\"===a.toLowerCase()&&(\"checkbox\"===b||\"radio\"===b)}\nfunction Ub(a){var b=Tb(a)?\"checked\":\"value\",c=Object.getOwnPropertyDescriptor(a.constructor.prototype,b),d=\"\"+a[b];if(!a.hasOwnProperty(b)&&\"undefined\"!==typeof c&&\"function\"===typeof c.get&&\"function\"===typeof c.set){var e=c.get,f=c.set;Object.defineProperty(a,b,{configurable:!0,get:function(){return e.call(this)},set:function(a){d=\"\"+a;f.call(this,a)}});Object.defineProperty(a,b,{enumerable:c.enumerable});return{getValue:function(){return d},setValue:function(a){d=\"\"+a},stopTracking:function(){a._valueTracker=\nnull;delete a[b]}}}}function Vb(a){a._valueTracker||(a._valueTracker=Ub(a))}function Wb(a){if(!a)return!1;var b=a._valueTracker;if(!b)return!0;var c=b.getValue();var d=\"\";a&&(d=Tb(a)?a.checked?\"true\":\"false\":a.value);a=d;return a!==c?(b.setValue(a),!0):!1}\nvar Xb=aa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Yb=/^(.*)[\\\\\\/]/,D=\"function\"===typeof Symbol&&Symbol.for,Zb=D?Symbol.for(\"react.element\"):60103,$b=D?Symbol.for(\"react.portal\"):60106,ac=D?Symbol.for(\"react.fragment\"):60107,bc=D?Symbol.for(\"react.strict_mode\"):60108,cc=D?Symbol.for(\"react.profiler\"):60114,dc=D?Symbol.for(\"react.provider\"):60109,ec=D?Symbol.for(\"react.context\"):60110,fc=D?Symbol.for(\"react.concurrent_mode\"):60111,gc=D?Symbol.for(\"react.forward_ref\"):60112,hc=D?Symbol.for(\"react.suspense\"):\n60113,ic=D?Symbol.for(\"react.memo\"):60115,jc=D?Symbol.for(\"react.lazy\"):60116,kc=\"function\"===typeof Symbol&&Symbol.iterator;function lc(a){if(null===a||\"object\"!==typeof a)return null;a=kc&&a[kc]||a[\"@@iterator\"];return\"function\"===typeof a?a:null}\nfunction mc(a){if(null==a)return null;if(\"function\"===typeof a)return a.displayName||a.name||null;if(\"string\"===typeof a)return a;switch(a){case fc:return\"ConcurrentMode\";case ac:return\"Fragment\";case $b:return\"Portal\";case cc:return\"Profiler\";case bc:return\"StrictMode\";case hc:return\"Suspense\"}if(\"object\"===typeof a)switch(a.$$typeof){case ec:return\"Context.Consumer\";case dc:return\"Context.Provider\";case gc:var b=a.render;b=b.displayName||b.name||\"\";return a.displayName||(\"\"!==b?\"ForwardRef(\"+b+\n\")\":\"ForwardRef\");case ic:return mc(a.type);case jc:if(a=1===a._status?a._result:null)return mc(a)}return null}function nc(a){var b=\"\";do{a:switch(a.tag){case 2:case 16:case 0:case 1:case 5:case 8:case 13:var c=a._debugOwner,d=a._debugSource,e=mc(a.type);var f=null;c&&(f=mc(c.type));c=e;e=\"\";d?e=\" (at \"+d.fileName.replace(Yb,\"\")+\":\"+d.lineNumber+\")\":f&&(e=\" (created by \"+f+\")\");f=\"\\n    in \"+(c||\"Unknown\")+e;break a;default:f=\"\"}b+=f;a=a.return}while(a);return b}\nvar oc=/^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$/,pc=Object.prototype.hasOwnProperty,qc={},rc={};\nfunction sc(a){if(pc.call(rc,a))return!0;if(pc.call(qc,a))return!1;if(oc.test(a))return rc[a]=!0;qc[a]=!0;return!1}function tc(a,b,c,d){if(null!==c&&0===c.type)return!1;switch(typeof b){case \"function\":case \"symbol\":return!0;case \"boolean\":if(d)return!1;if(null!==c)return!c.acceptsBooleans;a=a.toLowerCase().slice(0,5);return\"data-\"!==a&&\"aria-\"!==a;default:return!1}}\nfunction uc(a,b,c,d){if(null===b||\"undefined\"===typeof b||tc(a,b,c,d))return!0;if(d)return!1;if(null!==c)switch(c.type){case 3:return!b;case 4:return!1===b;case 5:return isNaN(b);case 6:return isNaN(b)||1>b}return!1}function E(a,b,c,d,e){this.acceptsBooleans=2===b||3===b||4===b;this.attributeName=d;this.attributeNamespace=e;this.mustUseProperty=c;this.propertyName=a;this.type=b}var F={};\n\"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style\".split(\" \").forEach(function(a){F[a]=new E(a,0,!1,a,null)});[[\"acceptCharset\",\"accept-charset\"],[\"className\",\"class\"],[\"htmlFor\",\"for\"],[\"httpEquiv\",\"http-equiv\"]].forEach(function(a){var b=a[0];F[b]=new E(b,1,!1,a[1],null)});[\"contentEditable\",\"draggable\",\"spellCheck\",\"value\"].forEach(function(a){F[a]=new E(a,2,!1,a.toLowerCase(),null)});\n[\"autoReverse\",\"externalResourcesRequired\",\"focusable\",\"preserveAlpha\"].forEach(function(a){F[a]=new E(a,2,!1,a,null)});\"allowFullScreen async autoFocus autoPlay controls default defer disabled formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope\".split(\" \").forEach(function(a){F[a]=new E(a,3,!1,a.toLowerCase(),null)});[\"checked\",\"multiple\",\"muted\",\"selected\"].forEach(function(a){F[a]=new E(a,3,!0,a,null)});\n[\"capture\",\"download\"].forEach(function(a){F[a]=new E(a,4,!1,a,null)});[\"cols\",\"rows\",\"size\",\"span\"].forEach(function(a){F[a]=new E(a,6,!1,a,null)});[\"rowSpan\",\"start\"].forEach(function(a){F[a]=new E(a,5,!1,a.toLowerCase(),null)});var vc=/[\\-:]([a-z])/g;function xc(a){return a[1].toUpperCase()}\n\"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height\".split(\" \").forEach(function(a){var b=a.replace(vc,\nxc);F[b]=new E(b,1,!1,a,null)});\"xlink:actuate xlink:arcrole xlink:href xlink:role xlink:show xlink:title xlink:type\".split(\" \").forEach(function(a){var b=a.replace(vc,xc);F[b]=new E(b,1,!1,a,\"http://www.w3.org/1999/xlink\")});[\"xml:base\",\"xml:lang\",\"xml:space\"].forEach(function(a){var b=a.replace(vc,xc);F[b]=new E(b,1,!1,a,\"http://www.w3.org/XML/1998/namespace\")});F.tabIndex=new E(\"tabIndex\",1,!1,\"tabindex\",null);\nfunction yc(a,b,c,d){var e=F.hasOwnProperty(b)?F[b]:null;var f=null!==e?0===e.type:d?!1:!(2<b.length)||\"o\"!==b[0]&&\"O\"!==b[0]||\"n\"!==b[1]&&\"N\"!==b[1]?!1:!0;f||(uc(b,c,e,d)&&(c=null),d||null===e?sc(b)&&(null===c?a.removeAttribute(b):a.setAttribute(b,\"\"+c)):e.mustUseProperty?a[e.propertyName]=null===c?3===e.type?!1:\"\":c:(b=e.attributeName,d=e.attributeNamespace,null===c?a.removeAttribute(b):(e=e.type,c=3===e||4===e&&!0===c?\"\":\"\"+c,d?a.setAttributeNS(d,b,c):a.setAttribute(b,c))))}\nfunction zc(a){switch(typeof a){case \"boolean\":case \"number\":case \"object\":case \"string\":case \"undefined\":return a;default:return\"\"}}function Ac(a,b){var c=b.checked;return n({},b,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=c?c:a._wrapperState.initialChecked})}\nfunction Bc(a,b){var c=null==b.defaultValue?\"\":b.defaultValue,d=null!=b.checked?b.checked:b.defaultChecked;c=zc(null!=b.value?b.value:c);a._wrapperState={initialChecked:d,initialValue:c,controlled:\"checkbox\"===b.type||\"radio\"===b.type?null!=b.checked:null!=b.value}}function Cc(a,b){b=b.checked;null!=b&&yc(a,\"checked\",b,!1)}\nfunction Dc(a,b){Cc(a,b);var c=zc(b.value),d=b.type;if(null!=c)if(\"number\"===d){if(0===c&&\"\"===a.value||a.value!=c)a.value=\"\"+c}else a.value!==\"\"+c&&(a.value=\"\"+c);else if(\"submit\"===d||\"reset\"===d){a.removeAttribute(\"value\");return}b.hasOwnProperty(\"value\")?Ec(a,b.type,c):b.hasOwnProperty(\"defaultValue\")&&Ec(a,b.type,zc(b.defaultValue));null==b.checked&&null!=b.defaultChecked&&(a.defaultChecked=!!b.defaultChecked)}\nfunction Fc(a,b,c){if(b.hasOwnProperty(\"value\")||b.hasOwnProperty(\"defaultValue\")){var d=b.type;if(!(\"submit\"!==d&&\"reset\"!==d||void 0!==b.value&&null!==b.value))return;b=\"\"+a._wrapperState.initialValue;c||b===a.value||(a.value=b);a.defaultValue=b}c=a.name;\"\"!==c&&(a.name=\"\");a.defaultChecked=!a.defaultChecked;a.defaultChecked=!!a._wrapperState.initialChecked;\"\"!==c&&(a.name=c)}\nfunction Ec(a,b,c){if(\"number\"!==b||a.ownerDocument.activeElement!==a)null==c?a.defaultValue=\"\"+a._wrapperState.initialValue:a.defaultValue!==\"\"+c&&(a.defaultValue=\"\"+c)}var Gc={change:{phasedRegistrationNames:{bubbled:\"onChange\",captured:\"onChangeCapture\"},dependencies:\"blur change click focus input keydown keyup selectionchange\".split(\" \")}};function Hc(a,b,c){a=A.getPooled(Gc.change,a,b,c);a.type=\"change\";Ib(c);Ra(a);return a}var Jc=null,Kc=null;function Lc(a){Ea(a)}\nfunction Mc(a){var b=Ka(a);if(Wb(b))return a}function Nc(a,b){if(\"change\"===a)return b}var Oc=!1;Sa&&(Oc=Sb(\"input\")&&(!document.documentMode||9<document.documentMode));function Pc(){Jc&&(Jc.detachEvent(\"onpropertychange\",Qc),Kc=Jc=null)}function Qc(a){\"value\"===a.propertyName&&Mc(Kc)&&(a=Hc(Kc,a,Rb(a)),Ob(Lc,a))}function Rc(a,b,c){\"focus\"===a?(Pc(),Jc=b,Kc=c,Jc.attachEvent(\"onpropertychange\",Qc)):\"blur\"===a&&Pc()}function Sc(a){if(\"selectionchange\"===a||\"keyup\"===a||\"keydown\"===a)return Mc(Kc)}\nfunction Tc(a,b){if(\"click\"===a)return Mc(b)}function Uc(a,b){if(\"input\"===a||\"change\"===a)return Mc(b)}\nvar Vc={eventTypes:Gc,_isInputEventSupported:Oc,extractEvents:function(a,b,c,d){var e=b?Ka(b):window,f=void 0,g=void 0,h=e.nodeName&&e.nodeName.toLowerCase();\"select\"===h||\"input\"===h&&\"file\"===e.type?f=Nc:Qb(e)?Oc?f=Uc:(f=Sc,g=Rc):(h=e.nodeName)&&\"input\"===h.toLowerCase()&&(\"checkbox\"===e.type||\"radio\"===e.type)&&(f=Tc);if(f&&(f=f(a,b)))return Hc(f,c,d);g&&g(a,e,b);\"blur\"===a&&(a=e._wrapperState)&&a.controlled&&\"number\"===e.type&&Ec(e,\"number\",e.value)}},Wc=A.extend({view:null,detail:null}),Xc={Alt:\"altKey\",\nControl:\"ctrlKey\",Meta:\"metaKey\",Shift:\"shiftKey\"};function Yc(a){var b=this.nativeEvent;return b.getModifierState?b.getModifierState(a):(a=Xc[a])?!!b[a]:!1}function Zc(){return Yc}\nvar $c=0,ad=0,bd=!1,cd=!1,dd=Wc.extend({screenX:null,screenY:null,clientX:null,clientY:null,pageX:null,pageY:null,ctrlKey:null,shiftKey:null,altKey:null,metaKey:null,getModifierState:Zc,button:null,buttons:null,relatedTarget:function(a){return a.relatedTarget||(a.fromElement===a.srcElement?a.toElement:a.fromElement)},movementX:function(a){if(\"movementX\"in a)return a.movementX;var b=$c;$c=a.screenX;return bd?\"mousemove\"===a.type?a.screenX-b:0:(bd=!0,0)},movementY:function(a){if(\"movementY\"in a)return a.movementY;\nvar b=ad;ad=a.screenY;return cd?\"mousemove\"===a.type?a.screenY-b:0:(cd=!0,0)}}),ed=dd.extend({pointerId:null,width:null,height:null,pressure:null,tangentialPressure:null,tiltX:null,tiltY:null,twist:null,pointerType:null,isPrimary:null}),fd={mouseEnter:{registrationName:\"onMouseEnter\",dependencies:[\"mouseout\",\"mouseover\"]},mouseLeave:{registrationName:\"onMouseLeave\",dependencies:[\"mouseout\",\"mouseover\"]},pointerEnter:{registrationName:\"onPointerEnter\",dependencies:[\"pointerout\",\"pointerover\"]},pointerLeave:{registrationName:\"onPointerLeave\",\ndependencies:[\"pointerout\",\"pointerover\"]}},gd={eventTypes:fd,extractEvents:function(a,b,c,d){var e=\"mouseover\"===a||\"pointerover\"===a,f=\"mouseout\"===a||\"pointerout\"===a;if(e&&(c.relatedTarget||c.fromElement)||!f&&!e)return null;e=d.window===d?d:(e=d.ownerDocument)?e.defaultView||e.parentWindow:window;f?(f=b,b=(b=c.relatedTarget||c.toElement)?Ia(b):null):f=null;if(f===b)return null;var g=void 0,h=void 0,k=void 0,l=void 0;if(\"mouseout\"===a||\"mouseover\"===a)g=dd,h=fd.mouseLeave,k=fd.mouseEnter,l=\"mouse\";\nelse if(\"pointerout\"===a||\"pointerover\"===a)g=ed,h=fd.pointerLeave,k=fd.pointerEnter,l=\"pointer\";var m=null==f?e:Ka(f);e=null==b?e:Ka(b);a=g.getPooled(h,f,c,d);a.type=l+\"leave\";a.target=m;a.relatedTarget=e;c=g.getPooled(k,b,c,d);c.type=l+\"enter\";c.target=e;c.relatedTarget=m;d=b;if(f&&d)a:{b=f;e=d;l=0;for(g=b;g;g=Ma(g))l++;g=0;for(k=e;k;k=Ma(k))g++;for(;0<l-g;)b=Ma(b),l--;for(;0<g-l;)e=Ma(e),g--;for(;l--;){if(b===e||b===e.alternate)break a;b=Ma(b);e=Ma(e)}b=null}else b=null;e=b;for(b=[];f&&f!==e;){l=\nf.alternate;if(null!==l&&l===e)break;b.push(f);f=Ma(f)}for(f=[];d&&d!==e;){l=d.alternate;if(null!==l&&l===e)break;f.push(d);d=Ma(d)}for(d=0;d<b.length;d++)Pa(b[d],\"bubbled\",a);for(d=f.length;0<d--;)Pa(f[d],\"captured\",c);return[a,c]}},hd=Object.prototype.hasOwnProperty;function id(a,b){return a===b?0!==a||0!==b||1/a===1/b:a!==a&&b!==b}\nfunction jd(a,b){if(id(a,b))return!0;if(\"object\"!==typeof a||null===a||\"object\"!==typeof b||null===b)return!1;var c=Object.keys(a),d=Object.keys(b);if(c.length!==d.length)return!1;for(d=0;d<c.length;d++)if(!hd.call(b,c[d])||!id(a[c[d]],b[c[d]]))return!1;return!0}function kd(a){var b=a;if(a.alternate)for(;b.return;)b=b.return;else{if(0!==(b.effectTag&2))return 1;for(;b.return;)if(b=b.return,0!==(b.effectTag&2))return 1}return 3===b.tag?2:3}function ld(a){2!==kd(a)?t(\"188\"):void 0}\nfunction md(a){var b=a.alternate;if(!b)return b=kd(a),3===b?t(\"188\"):void 0,1===b?null:a;for(var c=a,d=b;;){var e=c.return,f=e?e.alternate:null;if(!e||!f)break;if(e.child===f.child){for(var g=e.child;g;){if(g===c)return ld(e),a;if(g===d)return ld(e),b;g=g.sibling}t(\"188\")}if(c.return!==d.return)c=e,d=f;else{g=!1;for(var h=e.child;h;){if(h===c){g=!0;c=e;d=f;break}if(h===d){g=!0;d=e;c=f;break}h=h.sibling}if(!g){for(h=f.child;h;){if(h===c){g=!0;c=f;d=e;break}if(h===d){g=!0;d=f;c=e;break}h=h.sibling}g?\nvoid 0:t(\"189\")}}c.alternate!==d?t(\"190\"):void 0}3!==c.tag?t(\"188\"):void 0;return c.stateNode.current===c?a:b}function nd(a){a=md(a);if(!a)return null;for(var b=a;;){if(5===b.tag||6===b.tag)return b;if(b.child)b.child.return=b,b=b.child;else{if(b===a)break;for(;!b.sibling;){if(!b.return||b.return===a)return null;b=b.return}b.sibling.return=b.return;b=b.sibling}}return null}\nvar od=A.extend({animationName:null,elapsedTime:null,pseudoElement:null}),pd=A.extend({clipboardData:function(a){return\"clipboardData\"in a?a.clipboardData:window.clipboardData}}),qd=Wc.extend({relatedTarget:null});function rd(a){var b=a.keyCode;\"charCode\"in a?(a=a.charCode,0===a&&13===b&&(a=13)):a=b;10===a&&(a=13);return 32<=a||13===a?a:0}\nvar sd={Esc:\"Escape\",Spacebar:\" \",Left:\"ArrowLeft\",Up:\"ArrowUp\",Right:\"ArrowRight\",Down:\"ArrowDown\",Del:\"Delete\",Win:\"OS\",Menu:\"ContextMenu\",Apps:\"ContextMenu\",Scroll:\"ScrollLock\",MozPrintableKey:\"Unidentified\"},td={8:\"Backspace\",9:\"Tab\",12:\"Clear\",13:\"Enter\",16:\"Shift\",17:\"Control\",18:\"Alt\",19:\"Pause\",20:\"CapsLock\",27:\"Escape\",32:\" \",33:\"PageUp\",34:\"PageDown\",35:\"End\",36:\"Home\",37:\"ArrowLeft\",38:\"ArrowUp\",39:\"ArrowRight\",40:\"ArrowDown\",45:\"Insert\",46:\"Delete\",112:\"F1\",113:\"F2\",114:\"F3\",115:\"F4\",\n116:\"F5\",117:\"F6\",118:\"F7\",119:\"F8\",120:\"F9\",121:\"F10\",122:\"F11\",123:\"F12\",144:\"NumLock\",145:\"ScrollLock\",224:\"Meta\"},ud=Wc.extend({key:function(a){if(a.key){var b=sd[a.key]||a.key;if(\"Unidentified\"!==b)return b}return\"keypress\"===a.type?(a=rd(a),13===a?\"Enter\":String.fromCharCode(a)):\"keydown\"===a.type||\"keyup\"===a.type?td[a.keyCode]||\"Unidentified\":\"\"},location:null,ctrlKey:null,shiftKey:null,altKey:null,metaKey:null,repeat:null,locale:null,getModifierState:Zc,charCode:function(a){return\"keypress\"===\na.type?rd(a):0},keyCode:function(a){return\"keydown\"===a.type||\"keyup\"===a.type?a.keyCode:0},which:function(a){return\"keypress\"===a.type?rd(a):\"keydown\"===a.type||\"keyup\"===a.type?a.keyCode:0}}),vd=dd.extend({dataTransfer:null}),wd=Wc.extend({touches:null,targetTouches:null,changedTouches:null,altKey:null,metaKey:null,ctrlKey:null,shiftKey:null,getModifierState:Zc}),xd=A.extend({propertyName:null,elapsedTime:null,pseudoElement:null}),yd=dd.extend({deltaX:function(a){return\"deltaX\"in a?a.deltaX:\"wheelDeltaX\"in\na?-a.wheelDeltaX:0},deltaY:function(a){return\"deltaY\"in a?a.deltaY:\"wheelDeltaY\"in a?-a.wheelDeltaY:\"wheelDelta\"in a?-a.wheelDelta:0},deltaZ:null,deltaMode:null}),zd=[[\"abort\",\"abort\"],[Ya,\"animationEnd\"],[Za,\"animationIteration\"],[$a,\"animationStart\"],[\"canplay\",\"canPlay\"],[\"canplaythrough\",\"canPlayThrough\"],[\"drag\",\"drag\"],[\"dragenter\",\"dragEnter\"],[\"dragexit\",\"dragExit\"],[\"dragleave\",\"dragLeave\"],[\"dragover\",\"dragOver\"],[\"durationchange\",\"durationChange\"],[\"emptied\",\"emptied\"],[\"encrypted\",\"encrypted\"],\n[\"ended\",\"ended\"],[\"error\",\"error\"],[\"gotpointercapture\",\"gotPointerCapture\"],[\"load\",\"load\"],[\"loadeddata\",\"loadedData\"],[\"loadedmetadata\",\"loadedMetadata\"],[\"loadstart\",\"loadStart\"],[\"lostpointercapture\",\"lostPointerCapture\"],[\"mousemove\",\"mouseMove\"],[\"mouseout\",\"mouseOut\"],[\"mouseover\",\"mouseOver\"],[\"playing\",\"playing\"],[\"pointermove\",\"pointerMove\"],[\"pointerout\",\"pointerOut\"],[\"pointerover\",\"pointerOver\"],[\"progress\",\"progress\"],[\"scroll\",\"scroll\"],[\"seeking\",\"seeking\"],[\"stalled\",\"stalled\"],\n[\"suspend\",\"suspend\"],[\"timeupdate\",\"timeUpdate\"],[\"toggle\",\"toggle\"],[\"touchmove\",\"touchMove\"],[ab,\"transitionEnd\"],[\"waiting\",\"waiting\"],[\"wheel\",\"wheel\"]],Ad={},Bd={};function Cd(a,b){var c=a[0];a=a[1];var d=\"on\"+(a[0].toUpperCase()+a.slice(1));b={phasedRegistrationNames:{bubbled:d,captured:d+\"Capture\"},dependencies:[c],isInteractive:b};Ad[a]=b;Bd[c]=b}\n[[\"blur\",\"blur\"],[\"cancel\",\"cancel\"],[\"click\",\"click\"],[\"close\",\"close\"],[\"contextmenu\",\"contextMenu\"],[\"copy\",\"copy\"],[\"cut\",\"cut\"],[\"auxclick\",\"auxClick\"],[\"dblclick\",\"doubleClick\"],[\"dragend\",\"dragEnd\"],[\"dragstart\",\"dragStart\"],[\"drop\",\"drop\"],[\"focus\",\"focus\"],[\"input\",\"input\"],[\"invalid\",\"invalid\"],[\"keydown\",\"keyDown\"],[\"keypress\",\"keyPress\"],[\"keyup\",\"keyUp\"],[\"mousedown\",\"mouseDown\"],[\"mouseup\",\"mouseUp\"],[\"paste\",\"paste\"],[\"pause\",\"pause\"],[\"play\",\"play\"],[\"pointercancel\",\"pointerCancel\"],\n[\"pointerdown\",\"pointerDown\"],[\"pointerup\",\"pointerUp\"],[\"ratechange\",\"rateChange\"],[\"reset\",\"reset\"],[\"seeked\",\"seeked\"],[\"submit\",\"submit\"],[\"touchcancel\",\"touchCancel\"],[\"touchend\",\"touchEnd\"],[\"touchstart\",\"touchStart\"],[\"volumechange\",\"volumeChange\"]].forEach(function(a){Cd(a,!0)});zd.forEach(function(a){Cd(a,!1)});\nvar Dd={eventTypes:Ad,isInteractiveTopLevelEventType:function(a){a=Bd[a];return void 0!==a&&!0===a.isInteractive},extractEvents:function(a,b,c,d){var e=Bd[a];if(!e)return null;switch(a){case \"keypress\":if(0===rd(c))return null;case \"keydown\":case \"keyup\":a=ud;break;case \"blur\":case \"focus\":a=qd;break;case \"click\":if(2===c.button)return null;case \"auxclick\":case \"dblclick\":case \"mousedown\":case \"mousemove\":case \"mouseup\":case \"mouseout\":case \"mouseover\":case \"contextmenu\":a=dd;break;case \"drag\":case \"dragend\":case \"dragenter\":case \"dragexit\":case \"dragleave\":case \"dragover\":case \"dragstart\":case \"drop\":a=\nvd;break;case \"touchcancel\":case \"touchend\":case \"touchmove\":case \"touchstart\":a=wd;break;case Ya:case Za:case $a:a=od;break;case ab:a=xd;break;case \"scroll\":a=Wc;break;case \"wheel\":a=yd;break;case \"copy\":case \"cut\":case \"paste\":a=pd;break;case \"gotpointercapture\":case \"lostpointercapture\":case \"pointercancel\":case \"pointerdown\":case \"pointermove\":case \"pointerout\":case \"pointerover\":case \"pointerup\":a=ed;break;default:a=A}b=a.getPooled(e,b,c,d);Ra(b);return b}},Ed=Dd.isInteractiveTopLevelEventType,\nFd=[];function Gd(a){var b=a.targetInst,c=b;do{if(!c){a.ancestors.push(c);break}var d;for(d=c;d.return;)d=d.return;d=3!==d.tag?null:d.stateNode.containerInfo;if(!d)break;a.ancestors.push(c);c=Ia(d)}while(c);for(c=0;c<a.ancestors.length;c++){b=a.ancestors[c];var e=Rb(a.nativeEvent);d=a.topLevelType;for(var f=a.nativeEvent,g=null,h=0;h<pa.length;h++){var k=pa[h];k&&(k=k.extractEvents(d,b,f,e))&&(g=ya(g,k))}Ea(g)}}var Hd=!0;\nfunction G(a,b){if(!b)return null;var c=(Ed(a)?Id:Jd).bind(null,a);b.addEventListener(a,c,!1)}function Kd(a,b){if(!b)return null;var c=(Ed(a)?Id:Jd).bind(null,a);b.addEventListener(a,c,!0)}function Id(a,b){Lb(Jd,a,b)}\nfunction Jd(a,b){if(Hd){var c=Rb(b);c=Ia(c);null===c||\"number\"!==typeof c.tag||2===kd(c)||(c=null);if(Fd.length){var d=Fd.pop();d.topLevelType=a;d.nativeEvent=b;d.targetInst=c;a=d}else a={topLevelType:a,nativeEvent:b,targetInst:c,ancestors:[]};try{Ob(Gd,a)}finally{a.topLevelType=null,a.nativeEvent=null,a.targetInst=null,a.ancestors.length=0,10>Fd.length&&Fd.push(a)}}}var Ld={},Md=0,Nd=\"_reactListenersID\"+(\"\"+Math.random()).slice(2);\nfunction Od(a){Object.prototype.hasOwnProperty.call(a,Nd)||(a[Nd]=Md++,Ld[a[Nd]]={});return Ld[a[Nd]]}function Pd(a){a=a||(\"undefined\"!==typeof document?document:void 0);if(\"undefined\"===typeof a)return null;try{return a.activeElement||a.body}catch(b){return a.body}}function Qd(a){for(;a&&a.firstChild;)a=a.firstChild;return a}\nfunction Rd(a,b){var c=Qd(a);a=0;for(var d;c;){if(3===c.nodeType){d=a+c.textContent.length;if(a<=b&&d>=b)return{node:c,offset:b-a};a=d}a:{for(;c;){if(c.nextSibling){c=c.nextSibling;break a}c=c.parentNode}c=void 0}c=Qd(c)}}function Sd(a,b){return a&&b?a===b?!0:a&&3===a.nodeType?!1:b&&3===b.nodeType?Sd(a,b.parentNode):\"contains\"in a?a.contains(b):a.compareDocumentPosition?!!(a.compareDocumentPosition(b)&16):!1:!1}\nfunction Td(){for(var a=window,b=Pd();b instanceof a.HTMLIFrameElement;){try{a=b.contentDocument.defaultView}catch(c){break}b=Pd(a.document)}return b}function Ud(a){var b=a&&a.nodeName&&a.nodeName.toLowerCase();return b&&(\"input\"===b&&(\"text\"===a.type||\"search\"===a.type||\"tel\"===a.type||\"url\"===a.type||\"password\"===a.type)||\"textarea\"===b||\"true\"===a.contentEditable)}\nvar Vd=Sa&&\"documentMode\"in document&&11>=document.documentMode,Wd={select:{phasedRegistrationNames:{bubbled:\"onSelect\",captured:\"onSelectCapture\"},dependencies:\"blur contextmenu dragend focus keydown keyup mousedown mouseup selectionchange\".split(\" \")}},Xd=null,Yd=null,Zd=null,$d=!1;\nfunction ae(a,b){var c=b.window===b?b.document:9===b.nodeType?b:b.ownerDocument;if($d||null==Xd||Xd!==Pd(c))return null;c=Xd;\"selectionStart\"in c&&Ud(c)?c={start:c.selectionStart,end:c.selectionEnd}:(c=(c.ownerDocument&&c.ownerDocument.defaultView||window).getSelection(),c={anchorNode:c.anchorNode,anchorOffset:c.anchorOffset,focusNode:c.focusNode,focusOffset:c.focusOffset});return Zd&&jd(Zd,c)?null:(Zd=c,a=A.getPooled(Wd.select,Yd,a,b),a.type=\"select\",a.target=Xd,Ra(a),a)}\nvar be={eventTypes:Wd,extractEvents:function(a,b,c,d){var e=d.window===d?d.document:9===d.nodeType?d:d.ownerDocument,f;if(!(f=!e)){a:{e=Od(e);f=ta.onSelect;for(var g=0;g<f.length;g++){var h=f[g];if(!e.hasOwnProperty(h)||!e[h]){e=!1;break a}}e=!0}f=!e}if(f)return null;e=b?Ka(b):window;switch(a){case \"focus\":if(Qb(e)||\"true\"===e.contentEditable)Xd=e,Yd=b,Zd=null;break;case \"blur\":Zd=Yd=Xd=null;break;case \"mousedown\":$d=!0;break;case \"contextmenu\":case \"mouseup\":case \"dragend\":return $d=!1,ae(c,d);case \"selectionchange\":if(Vd)break;\ncase \"keydown\":case \"keyup\":return ae(c,d)}return null}};Ca.injectEventPluginOrder(\"ResponderEventPlugin SimpleEventPlugin EnterLeaveEventPlugin ChangeEventPlugin SelectEventPlugin BeforeInputEventPlugin\".split(\" \"));ua=La;va=Ja;wa=Ka;Ca.injectEventPluginsByName({SimpleEventPlugin:Dd,EnterLeaveEventPlugin:gd,ChangeEventPlugin:Vc,SelectEventPlugin:be,BeforeInputEventPlugin:Bb});function de(a){var b=\"\";aa.Children.forEach(a,function(a){null!=a&&(b+=a)});return b}\nfunction ee(a,b){a=n({children:void 0},b);if(b=de(b.children))a.children=b;return a}function fe(a,b,c,d){a=a.options;if(b){b={};for(var e=0;e<c.length;e++)b[\"$\"+c[e]]=!0;for(c=0;c<a.length;c++)e=b.hasOwnProperty(\"$\"+a[c].value),a[c].selected!==e&&(a[c].selected=e),e&&d&&(a[c].defaultSelected=!0)}else{c=\"\"+zc(c);b=null;for(e=0;e<a.length;e++){if(a[e].value===c){a[e].selected=!0;d&&(a[e].defaultSelected=!0);return}null!==b||a[e].disabled||(b=a[e])}null!==b&&(b.selected=!0)}}\nfunction ge(a,b){null!=b.dangerouslySetInnerHTML?t(\"91\"):void 0;return n({},b,{value:void 0,defaultValue:void 0,children:\"\"+a._wrapperState.initialValue})}function he(a,b){var c=b.value;null==c&&(c=b.defaultValue,b=b.children,null!=b&&(null!=c?t(\"92\"):void 0,Array.isArray(b)&&(1>=b.length?void 0:t(\"93\"),b=b[0]),c=b),null==c&&(c=\"\"));a._wrapperState={initialValue:zc(c)}}\nfunction ie(a,b){var c=zc(b.value),d=zc(b.defaultValue);null!=c&&(c=\"\"+c,c!==a.value&&(a.value=c),null==b.defaultValue&&a.defaultValue!==c&&(a.defaultValue=c));null!=d&&(a.defaultValue=\"\"+d)}function je(a){var b=a.textContent;b===a._wrapperState.initialValue&&(a.value=b)}var ke={html:\"http://www.w3.org/1999/xhtml\",mathml:\"http://www.w3.org/1998/Math/MathML\",svg:\"http://www.w3.org/2000/svg\"};\nfunction le(a){switch(a){case \"svg\":return\"http://www.w3.org/2000/svg\";case \"math\":return\"http://www.w3.org/1998/Math/MathML\";default:return\"http://www.w3.org/1999/xhtml\"}}function me(a,b){return null==a||\"http://www.w3.org/1999/xhtml\"===a?le(b):\"http://www.w3.org/2000/svg\"===a&&\"foreignObject\"===b?\"http://www.w3.org/1999/xhtml\":a}\nvar ne=void 0,oe=function(a){return\"undefined\"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(b,c,d,e){MSApp.execUnsafeLocalFunction(function(){return a(b,c,d,e)})}:a}(function(a,b){if(a.namespaceURI!==ke.svg||\"innerHTML\"in a)a.innerHTML=b;else{ne=ne||document.createElement(\"div\");ne.innerHTML=\"<svg>\"+b+\"</svg>\";for(b=ne.firstChild;a.firstChild;)a.removeChild(a.firstChild);for(;b.firstChild;)a.appendChild(b.firstChild)}});\nfunction pe(a,b){if(b){var c=a.firstChild;if(c&&c===a.lastChild&&3===c.nodeType){c.nodeValue=b;return}}a.textContent=b}\nvar qe={animationIterationCount:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,\nfloodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},re=[\"Webkit\",\"ms\",\"Moz\",\"O\"];Object.keys(qe).forEach(function(a){re.forEach(function(b){b=b+a.charAt(0).toUpperCase()+a.substring(1);qe[b]=qe[a]})});function se(a,b,c){return null==b||\"boolean\"===typeof b||\"\"===b?\"\":c||\"number\"!==typeof b||0===b||qe.hasOwnProperty(a)&&qe[a]?(\"\"+b).trim():b+\"px\"}\nfunction te(a,b){a=a.style;for(var c in b)if(b.hasOwnProperty(c)){var d=0===c.indexOf(\"--\"),e=se(c,b[c],d);\"float\"===c&&(c=\"cssFloat\");d?a.setProperty(c,e):a[c]=e}}var ue=n({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});\nfunction ve(a,b){b&&(ue[a]&&(null!=b.children||null!=b.dangerouslySetInnerHTML?t(\"137\",a,\"\"):void 0),null!=b.dangerouslySetInnerHTML&&(null!=b.children?t(\"60\"):void 0,\"object\"===typeof b.dangerouslySetInnerHTML&&\"__html\"in b.dangerouslySetInnerHTML?void 0:t(\"61\")),null!=b.style&&\"object\"!==typeof b.style?t(\"62\",\"\"):void 0)}\nfunction we(a,b){if(-1===a.indexOf(\"-\"))return\"string\"===typeof b.is;switch(a){case \"annotation-xml\":case \"color-profile\":case \"font-face\":case \"font-face-src\":case \"font-face-uri\":case \"font-face-format\":case \"font-face-name\":case \"missing-glyph\":return!1;default:return!0}}\nfunction xe(a,b){a=9===a.nodeType||11===a.nodeType?a:a.ownerDocument;var c=Od(a);b=ta[b];for(var d=0;d<b.length;d++){var e=b[d];if(!c.hasOwnProperty(e)||!c[e]){switch(e){case \"scroll\":Kd(\"scroll\",a);break;case \"focus\":case \"blur\":Kd(\"focus\",a);Kd(\"blur\",a);c.blur=!0;c.focus=!0;break;case \"cancel\":case \"close\":Sb(e)&&Kd(e,a);break;case \"invalid\":case \"submit\":case \"reset\":break;default:-1===bb.indexOf(e)&&G(e,a)}c[e]=!0}}}function ye(){}var ze=null,Ae=null;\nfunction Be(a,b){switch(a){case \"button\":case \"input\":case \"select\":case \"textarea\":return!!b.autoFocus}return!1}function Ce(a,b){return\"textarea\"===a||\"option\"===a||\"noscript\"===a||\"string\"===typeof b.children||\"number\"===typeof b.children||\"object\"===typeof b.dangerouslySetInnerHTML&&null!==b.dangerouslySetInnerHTML&&null!=b.dangerouslySetInnerHTML.__html}var De=\"function\"===typeof setTimeout?setTimeout:void 0,Ee=\"function\"===typeof clearTimeout?clearTimeout:void 0;\nfunction Fe(a){for(a=a.nextSibling;a&&1!==a.nodeType&&3!==a.nodeType;)a=a.nextSibling;return a}function Ge(a){for(a=a.firstChild;a&&1!==a.nodeType&&3!==a.nodeType;)a=a.nextSibling;return a}new Set;var He=[],Ie=-1;function H(a){0>Ie||(a.current=He[Ie],He[Ie]=null,Ie--)}function I(a,b){Ie++;He[Ie]=a.current;a.current=b}var Je={},J={current:Je},K={current:!1},Ke=Je;\nfunction Le(a,b){var c=a.type.contextTypes;if(!c)return Je;var d=a.stateNode;if(d&&d.__reactInternalMemoizedUnmaskedChildContext===b)return d.__reactInternalMemoizedMaskedChildContext;var e={},f;for(f in c)e[f]=b[f];d&&(a=a.stateNode,a.__reactInternalMemoizedUnmaskedChildContext=b,a.__reactInternalMemoizedMaskedChildContext=e);return e}function L(a){a=a.childContextTypes;return null!==a&&void 0!==a}function Me(a){H(K,a);H(J,a)}function Ne(a){H(K,a);H(J,a)}\nfunction Oe(a,b,c){J.current!==Je?t(\"168\"):void 0;I(J,b,a);I(K,c,a)}function Pe(a,b,c){var d=a.stateNode;a=b.childContextTypes;if(\"function\"!==typeof d.getChildContext)return c;d=d.getChildContext();for(var e in d)e in a?void 0:t(\"108\",mc(b)||\"Unknown\",e);return n({},c,d)}function Qe(a){var b=a.stateNode;b=b&&b.__reactInternalMemoizedMergedChildContext||Je;Ke=J.current;I(J,b,a);I(K,K.current,a);return!0}\nfunction Re(a,b,c){var d=a.stateNode;d?void 0:t(\"169\");c?(b=Pe(a,b,Ke),d.__reactInternalMemoizedMergedChildContext=b,H(K,a),H(J,a),I(J,b,a)):H(K,a);I(K,c,a)}var Se=null,Te=null;function Ue(a){return function(b){try{return a(b)}catch(c){}}}\nfunction Ve(a){if(\"undefined\"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__)return!1;var b=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(b.isDisabled||!b.supportsFiber)return!0;try{var c=b.inject(a);Se=Ue(function(a){return b.onCommitFiberRoot(c,a)});Te=Ue(function(a){return b.onCommitFiberUnmount(c,a)})}catch(d){}return!0}\nfunction We(a,b,c,d){this.tag=a;this.key=c;this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null;this.index=0;this.ref=null;this.pendingProps=b;this.firstContextDependency=this.memoizedState=this.updateQueue=this.memoizedProps=null;this.mode=d;this.effectTag=0;this.lastEffect=this.firstEffect=this.nextEffect=null;this.childExpirationTime=this.expirationTime=0;this.alternate=null}function M(a,b,c,d){return new We(a,b,c,d)}\nfunction Xe(a){a=a.prototype;return!(!a||!a.isReactComponent)}function Ye(a){if(\"function\"===typeof a)return Xe(a)?1:0;if(void 0!==a&&null!==a){a=a.$$typeof;if(a===gc)return 11;if(a===ic)return 14}return 2}\nfunction Ze(a,b){var c=a.alternate;null===c?(c=M(a.tag,b,a.key,a.mode),c.elementType=a.elementType,c.type=a.type,c.stateNode=a.stateNode,c.alternate=a,a.alternate=c):(c.pendingProps=b,c.effectTag=0,c.nextEffect=null,c.firstEffect=null,c.lastEffect=null);c.childExpirationTime=a.childExpirationTime;c.expirationTime=a.expirationTime;c.child=a.child;c.memoizedProps=a.memoizedProps;c.memoizedState=a.memoizedState;c.updateQueue=a.updateQueue;c.firstContextDependency=a.firstContextDependency;c.sibling=a.sibling;\nc.index=a.index;c.ref=a.ref;return c}\nfunction $e(a,b,c,d,e,f){var g=2;d=a;if(\"function\"===typeof a)Xe(a)&&(g=1);else if(\"string\"===typeof a)g=5;else a:switch(a){case ac:return af(c.children,e,f,b);case fc:return bf(c,e|3,f,b);case bc:return bf(c,e|2,f,b);case cc:return a=M(12,c,b,e|4),a.elementType=cc,a.type=cc,a.expirationTime=f,a;case hc:return a=M(13,c,b,e),a.elementType=hc,a.type=hc,a.expirationTime=f,a;default:if(\"object\"===typeof a&&null!==a)switch(a.$$typeof){case dc:g=10;break a;case ec:g=9;break a;case gc:g=11;break a;case ic:g=\n14;break a;case jc:g=16;d=null;break a}t(\"130\",null==a?a:typeof a,\"\")}b=M(g,c,b,e);b.elementType=a;b.type=d;b.expirationTime=f;return b}function af(a,b,c,d){a=M(7,a,d,b);a.expirationTime=c;return a}function bf(a,b,c,d){a=M(8,a,d,b);b=0===(b&1)?bc:fc;a.elementType=b;a.type=b;a.expirationTime=c;return a}function cf(a,b,c){a=M(6,a,null,b);a.expirationTime=c;return a}\nfunction df(a,b,c){b=M(4,null!==a.children?a.children:[],a.key,b);b.expirationTime=c;b.stateNode={containerInfo:a.containerInfo,pendingChildren:null,implementation:a.implementation};return b}function ef(a,b){a.didError=!1;var c=a.earliestPendingTime;0===c?a.earliestPendingTime=a.latestPendingTime=b:c<b?a.earliestPendingTime=b:a.latestPendingTime>b&&(a.latestPendingTime=b);ff(b,a)}\nfunction gf(a,b){a.didError=!1;var c=a.latestPingedTime;0!==c&&c>=b&&(a.latestPingedTime=0);c=a.earliestPendingTime;var d=a.latestPendingTime;c===b?a.earliestPendingTime=d===b?a.latestPendingTime=0:d:d===b&&(a.latestPendingTime=c);c=a.earliestSuspendedTime;d=a.latestSuspendedTime;0===c?a.earliestSuspendedTime=a.latestSuspendedTime=b:c<b?a.earliestSuspendedTime=b:d>b&&(a.latestSuspendedTime=b);ff(b,a)}\nfunction hf(a,b){var c=a.earliestPendingTime;a=a.earliestSuspendedTime;c>b&&(b=c);a>b&&(b=a);return b}function ff(a,b){var c=b.earliestSuspendedTime,d=b.latestSuspendedTime,e=b.earliestPendingTime,f=b.latestPingedTime;e=0!==e?e:f;0===e&&(0===a||d<a)&&(e=d);a=e;0!==a&&c>a&&(a=c);b.nextExpirationTimeToWorkOn=e;b.expirationTime=a}var jf=!1;\nfunction kf(a){return{baseState:a,firstUpdate:null,lastUpdate:null,firstCapturedUpdate:null,lastCapturedUpdate:null,firstEffect:null,lastEffect:null,firstCapturedEffect:null,lastCapturedEffect:null}}function lf(a){return{baseState:a.baseState,firstUpdate:a.firstUpdate,lastUpdate:a.lastUpdate,firstCapturedUpdate:null,lastCapturedUpdate:null,firstEffect:null,lastEffect:null,firstCapturedEffect:null,lastCapturedEffect:null}}\nfunction mf(a){return{expirationTime:a,tag:0,payload:null,callback:null,next:null,nextEffect:null}}function nf(a,b){null===a.lastUpdate?a.firstUpdate=a.lastUpdate=b:(a.lastUpdate.next=b,a.lastUpdate=b)}\nfunction of(a,b){var c=a.alternate;if(null===c){var d=a.updateQueue;var e=null;null===d&&(d=a.updateQueue=kf(a.memoizedState))}else d=a.updateQueue,e=c.updateQueue,null===d?null===e?(d=a.updateQueue=kf(a.memoizedState),e=c.updateQueue=kf(c.memoizedState)):d=a.updateQueue=lf(e):null===e&&(e=c.updateQueue=lf(d));null===e||d===e?nf(d,b):null===d.lastUpdate||null===e.lastUpdate?(nf(d,b),nf(e,b)):(nf(d,b),e.lastUpdate=b)}\nfunction pf(a,b){var c=a.updateQueue;c=null===c?a.updateQueue=kf(a.memoizedState):qf(a,c);null===c.lastCapturedUpdate?c.firstCapturedUpdate=c.lastCapturedUpdate=b:(c.lastCapturedUpdate.next=b,c.lastCapturedUpdate=b)}function qf(a,b){var c=a.alternate;null!==c&&b===c.updateQueue&&(b=a.updateQueue=lf(b));return b}\nfunction rf(a,b,c,d,e,f){switch(c.tag){case 1:return a=c.payload,\"function\"===typeof a?a.call(f,d,e):a;case 3:a.effectTag=a.effectTag&-2049|64;case 0:a=c.payload;e=\"function\"===typeof a?a.call(f,d,e):a;if(null===e||void 0===e)break;return n({},d,e);case 2:jf=!0}return d}\nfunction sf(a,b,c,d,e){jf=!1;b=qf(a,b);for(var f=b.baseState,g=null,h=0,k=b.firstUpdate,l=f;null!==k;){var m=k.expirationTime;m<e?(null===g&&(g=k,f=l),h<m&&(h=m)):(l=rf(a,b,k,l,c,d),null!==k.callback&&(a.effectTag|=32,k.nextEffect=null,null===b.lastEffect?b.firstEffect=b.lastEffect=k:(b.lastEffect.nextEffect=k,b.lastEffect=k)));k=k.next}m=null;for(k=b.firstCapturedUpdate;null!==k;){var q=k.expirationTime;q<e?(null===m&&(m=k,null===g&&(f=l)),h<q&&(h=q)):(l=rf(a,b,k,l,c,d),null!==k.callback&&(a.effectTag|=\n32,k.nextEffect=null,null===b.lastCapturedEffect?b.firstCapturedEffect=b.lastCapturedEffect=k:(b.lastCapturedEffect.nextEffect=k,b.lastCapturedEffect=k)));k=k.next}null===g&&(b.lastUpdate=null);null===m?b.lastCapturedUpdate=null:a.effectTag|=32;null===g&&null===m&&(f=l);b.baseState=f;b.firstUpdate=g;b.firstCapturedUpdate=m;a.expirationTime=h;a.memoizedState=l}\nfunction tf(a,b,c){null!==b.firstCapturedUpdate&&(null!==b.lastUpdate&&(b.lastUpdate.next=b.firstCapturedUpdate,b.lastUpdate=b.lastCapturedUpdate),b.firstCapturedUpdate=b.lastCapturedUpdate=null);uf(b.firstEffect,c);b.firstEffect=b.lastEffect=null;uf(b.firstCapturedEffect,c);b.firstCapturedEffect=b.lastCapturedEffect=null}function uf(a,b){for(;null!==a;){var c=a.callback;if(null!==c){a.callback=null;var d=b;\"function\"!==typeof c?t(\"191\",c):void 0;c.call(d)}a=a.nextEffect}}\nfunction vf(a,b){return{value:a,source:b,stack:nc(b)}}var wf={current:null},xf=null,yf=null,zf=null;function Af(a,b){var c=a.type._context;I(wf,c._currentValue,a);c._currentValue=b}function Bf(a){var b=wf.current;H(wf,a);a.type._context._currentValue=b}function Cf(a){xf=a;zf=yf=null;a.firstContextDependency=null}\nfunction Df(a,b){if(zf!==a&&!1!==b&&0!==b){if(\"number\"!==typeof b||1073741823===b)zf=a,b=1073741823;b={context:a,observedBits:b,next:null};null===yf?(null===xf?t(\"293\"):void 0,xf.firstContextDependency=yf=b):yf=yf.next=b}return a._currentValue}var Ef={},N={current:Ef},Ff={current:Ef},Gf={current:Ef};function Hf(a){a===Ef?t(\"174\"):void 0;return a}\nfunction If(a,b){I(Gf,b,a);I(Ff,a,a);I(N,Ef,a);var c=b.nodeType;switch(c){case 9:case 11:b=(b=b.documentElement)?b.namespaceURI:me(null,\"\");break;default:c=8===c?b.parentNode:b,b=c.namespaceURI||null,c=c.tagName,b=me(b,c)}H(N,a);I(N,b,a)}function Jf(a){H(N,a);H(Ff,a);H(Gf,a)}function Kf(a){Hf(Gf.current);var b=Hf(N.current);var c=me(b,a.type);b!==c&&(I(Ff,a,a),I(N,c,a))}function Lf(a){Ff.current===a&&(H(N,a),H(Ff,a))}\nfunction O(a,b){if(a&&a.defaultProps){b=n({},b);a=a.defaultProps;for(var c in a)void 0===b[c]&&(b[c]=a[c])}return b}function Mf(a){var b=a._result;switch(a._status){case 1:return b;case 2:throw b;case 0:throw b;default:throw a._status=0,b=a._ctor,b=b(),b.then(function(b){0===a._status&&(b=b.default,a._status=1,a._result=b)},function(b){0===a._status&&(a._status=2,a._result=b)}),a._result=b,b;}}var Nf=Xb.ReactCurrentOwner,Of=(new aa.Component).refs;\nfunction Pf(a,b,c,d){b=a.memoizedState;c=c(d,b);c=null===c||void 0===c?b:n({},b,c);a.memoizedState=c;d=a.updateQueue;null!==d&&0===a.expirationTime&&(d.baseState=c)}\nvar Uf={isMounted:function(a){return(a=a._reactInternalFiber)?2===kd(a):!1},enqueueSetState:function(a,b,c){a=a._reactInternalFiber;var d=Qf();d=Rf(d,a);var e=mf(d);e.payload=b;void 0!==c&&null!==c&&(e.callback=c);Sf();of(a,e);Tf(a,d)},enqueueReplaceState:function(a,b,c){a=a._reactInternalFiber;var d=Qf();d=Rf(d,a);var e=mf(d);e.tag=1;e.payload=b;void 0!==c&&null!==c&&(e.callback=c);Sf();of(a,e);Tf(a,d)},enqueueForceUpdate:function(a,b){a=a._reactInternalFiber;var c=Qf();c=Rf(c,a);var d=mf(c);d.tag=\n2;void 0!==b&&null!==b&&(d.callback=b);Sf();of(a,d);Tf(a,c)}};function Vf(a,b,c,d,e,f,g){a=a.stateNode;return\"function\"===typeof a.shouldComponentUpdate?a.shouldComponentUpdate(d,f,g):b.prototype&&b.prototype.isPureReactComponent?!jd(c,d)||!jd(e,f):!0}\nfunction Wf(a,b,c){var d=!1,e=Je;var f=b.contextType;\"object\"===typeof f&&null!==f?f=Nf.currentDispatcher.readContext(f):(e=L(b)?Ke:J.current,d=b.contextTypes,f=(d=null!==d&&void 0!==d)?Le(a,e):Je);b=new b(c,f);a.memoizedState=null!==b.state&&void 0!==b.state?b.state:null;b.updater=Uf;a.stateNode=b;b._reactInternalFiber=a;d&&(a=a.stateNode,a.__reactInternalMemoizedUnmaskedChildContext=e,a.__reactInternalMemoizedMaskedChildContext=f);return b}\nfunction Xf(a,b,c,d){a=b.state;\"function\"===typeof b.componentWillReceiveProps&&b.componentWillReceiveProps(c,d);\"function\"===typeof b.UNSAFE_componentWillReceiveProps&&b.UNSAFE_componentWillReceiveProps(c,d);b.state!==a&&Uf.enqueueReplaceState(b,b.state,null)}\nfunction Yf(a,b,c,d){var e=a.stateNode;e.props=c;e.state=a.memoizedState;e.refs=Of;var f=b.contextType;\"object\"===typeof f&&null!==f?e.context=Nf.currentDispatcher.readContext(f):(f=L(b)?Ke:J.current,e.context=Le(a,f));f=a.updateQueue;null!==f&&(sf(a,f,c,e,d),e.state=a.memoizedState);f=b.getDerivedStateFromProps;\"function\"===typeof f&&(Pf(a,b,f,c),e.state=a.memoizedState);\"function\"===typeof b.getDerivedStateFromProps||\"function\"===typeof e.getSnapshotBeforeUpdate||\"function\"!==typeof e.UNSAFE_componentWillMount&&\n\"function\"!==typeof e.componentWillMount||(b=e.state,\"function\"===typeof e.componentWillMount&&e.componentWillMount(),\"function\"===typeof e.UNSAFE_componentWillMount&&e.UNSAFE_componentWillMount(),b!==e.state&&Uf.enqueueReplaceState(e,e.state,null),f=a.updateQueue,null!==f&&(sf(a,f,c,e,d),e.state=a.memoizedState));\"function\"===typeof e.componentDidMount&&(a.effectTag|=4)}var Zf=Array.isArray;\nfunction $f(a,b,c){a=c.ref;if(null!==a&&\"function\"!==typeof a&&\"object\"!==typeof a){if(c._owner){c=c._owner;var d=void 0;c&&(1!==c.tag?t(\"289\"):void 0,d=c.stateNode);d?void 0:t(\"147\",a);var e=\"\"+a;if(null!==b&&null!==b.ref&&\"function\"===typeof b.ref&&b.ref._stringRef===e)return b.ref;b=function(a){var b=d.refs;b===Of&&(b=d.refs={});null===a?delete b[e]:b[e]=a};b._stringRef=e;return b}\"string\"!==typeof a?t(\"284\"):void 0;c._owner?void 0:t(\"290\",a)}return a}\nfunction ag(a,b){\"textarea\"!==a.type&&t(\"31\",\"[object Object]\"===Object.prototype.toString.call(b)?\"object with keys {\"+Object.keys(b).join(\", \")+\"}\":b,\"\")}\nfunction bg(a){function b(b,c){if(a){var d=b.lastEffect;null!==d?(d.nextEffect=c,b.lastEffect=c):b.firstEffect=b.lastEffect=c;c.nextEffect=null;c.effectTag=8}}function c(c,d){if(!a)return null;for(;null!==d;)b(c,d),d=d.sibling;return null}function d(a,b){for(a=new Map;null!==b;)null!==b.key?a.set(b.key,b):a.set(b.index,b),b=b.sibling;return a}function e(a,b,c){a=Ze(a,b,c);a.index=0;a.sibling=null;return a}function f(b,c,d){b.index=d;if(!a)return c;d=b.alternate;if(null!==d)return d=d.index,d<c?(b.effectTag=\n2,c):d;b.effectTag=2;return c}function g(b){a&&null===b.alternate&&(b.effectTag=2);return b}function h(a,b,c,d){if(null===b||6!==b.tag)return b=cf(c,a.mode,d),b.return=a,b;b=e(b,c,d);b.return=a;return b}function k(a,b,c,d){if(null!==b&&b.elementType===c.type)return d=e(b,c.props,d),d.ref=$f(a,b,c),d.return=a,d;d=$e(c.type,c.key,c.props,null,a.mode,d);d.ref=$f(a,b,c);d.return=a;return d}function l(a,b,c,d){if(null===b||4!==b.tag||b.stateNode.containerInfo!==c.containerInfo||b.stateNode.implementation!==\nc.implementation)return b=df(c,a.mode,d),b.return=a,b;b=e(b,c.children||[],d);b.return=a;return b}function m(a,b,c,d,g){if(null===b||7!==b.tag)return b=af(c,a.mode,d,g),b.return=a,b;b=e(b,c,d);b.return=a;return b}function q(a,b,c){if(\"string\"===typeof b||\"number\"===typeof b)return b=cf(\"\"+b,a.mode,c),b.return=a,b;if(\"object\"===typeof b&&null!==b){switch(b.$$typeof){case Zb:return c=$e(b.type,b.key,b.props,null,a.mode,c),c.ref=$f(a,null,b),c.return=a,c;case $b:return b=df(b,a.mode,c),b.return=a,b}if(Zf(b)||\nlc(b))return b=af(b,a.mode,c,null),b.return=a,b;ag(a,b)}return null}function x(a,b,c,d){var e=null!==b?b.key:null;if(\"string\"===typeof c||\"number\"===typeof c)return null!==e?null:h(a,b,\"\"+c,d);if(\"object\"===typeof c&&null!==c){switch(c.$$typeof){case Zb:return c.key===e?c.type===ac?m(a,b,c.props.children,d,e):k(a,b,c,d):null;case $b:return c.key===e?l(a,b,c,d):null}if(Zf(c)||lc(c))return null!==e?null:m(a,b,c,d,null);ag(a,c)}return null}function z(a,b,c,d,e){if(\"string\"===typeof d||\"number\"===typeof d)return a=\na.get(c)||null,h(b,a,\"\"+d,e);if(\"object\"===typeof d&&null!==d){switch(d.$$typeof){case Zb:return a=a.get(null===d.key?c:d.key)||null,d.type===ac?m(b,a,d.props.children,e,d.key):k(b,a,d,e);case $b:return a=a.get(null===d.key?c:d.key)||null,l(b,a,d,e)}if(Zf(d)||lc(d))return a=a.get(c)||null,m(b,a,d,e,null);ag(b,d)}return null}function B(e,g,h,k){for(var l=null,r=null,m=g,u=g=0,p=null;null!==m&&u<h.length;u++){m.index>u?(p=m,m=null):p=m.sibling;var v=x(e,m,h[u],k);if(null===v){null===m&&(m=p);break}a&&\nm&&null===v.alternate&&b(e,m);g=f(v,g,u);null===r?l=v:r.sibling=v;r=v;m=p}if(u===h.length)return c(e,m),l;if(null===m){for(;u<h.length;u++)if(m=q(e,h[u],k))g=f(m,g,u),null===r?l=m:r.sibling=m,r=m;return l}for(m=d(e,m);u<h.length;u++)if(p=z(m,e,u,h[u],k))a&&null!==p.alternate&&m.delete(null===p.key?u:p.key),g=f(p,g,u),null===r?l=p:r.sibling=p,r=p;a&&m.forEach(function(a){return b(e,a)});return l}function Q(e,g,h,k){var l=lc(h);\"function\"!==typeof l?t(\"150\"):void 0;h=l.call(h);null==h?t(\"151\"):void 0;\nfor(var m=l=null,r=g,u=g=0,p=null,v=h.next();null!==r&&!v.done;u++,v=h.next()){r.index>u?(p=r,r=null):p=r.sibling;var y=x(e,r,v.value,k);if(null===y){r||(r=p);break}a&&r&&null===y.alternate&&b(e,r);g=f(y,g,u);null===m?l=y:m.sibling=y;m=y;r=p}if(v.done)return c(e,r),l;if(null===r){for(;!v.done;u++,v=h.next())v=q(e,v.value,k),null!==v&&(g=f(v,g,u),null===m?l=v:m.sibling=v,m=v);return l}for(r=d(e,r);!v.done;u++,v=h.next())v=z(r,e,u,v.value,k),null!==v&&(a&&null!==v.alternate&&r.delete(null===v.key?u:\nv.key),g=f(v,g,u),null===m?l=v:m.sibling=v,m=v);a&&r.forEach(function(a){return b(e,a)});return l}return function(a,d,f,h){var k=\"object\"===typeof f&&null!==f&&f.type===ac&&null===f.key;k&&(f=f.props.children);var l=\"object\"===typeof f&&null!==f;if(l)switch(f.$$typeof){case Zb:a:{l=f.key;for(k=d;null!==k;){if(k.key===l)if(7===k.tag?f.type===ac:k.elementType===f.type){c(a,k.sibling);d=e(k,f.type===ac?f.props.children:f.props,h);d.ref=$f(a,k,f);d.return=a;a=d;break a}else{c(a,k);break}else b(a,k);k=\nk.sibling}f.type===ac?(d=af(f.props.children,a.mode,h,f.key),d.return=a,a=d):(h=$e(f.type,f.key,f.props,null,a.mode,h),h.ref=$f(a,d,f),h.return=a,a=h)}return g(a);case $b:a:{for(k=f.key;null!==d;){if(d.key===k)if(4===d.tag&&d.stateNode.containerInfo===f.containerInfo&&d.stateNode.implementation===f.implementation){c(a,d.sibling);d=e(d,f.children||[],h);d.return=a;a=d;break a}else{c(a,d);break}else b(a,d);d=d.sibling}d=df(f,a.mode,h);d.return=a;a=d}return g(a)}if(\"string\"===typeof f||\"number\"===typeof f)return f=\n\"\"+f,null!==d&&6===d.tag?(c(a,d.sibling),d=e(d,f,h),d.return=a,a=d):(c(a,d),d=cf(f,a.mode,h),d.return=a,a=d),g(a);if(Zf(f))return B(a,d,f,h);if(lc(f))return Q(a,d,f,h);l&&ag(a,f);if(\"undefined\"===typeof f&&!k)switch(a.tag){case 1:case 0:h=a.type,t(\"152\",h.displayName||h.name||\"Component\")}return c(a,d)}}var cg=bg(!0),dg=bg(!1),eg=null,fg=null,gg=!1;\nfunction hg(a,b){var c=M(5,null,null,0);c.elementType=\"DELETED\";c.type=\"DELETED\";c.stateNode=b;c.return=a;c.effectTag=8;null!==a.lastEffect?(a.lastEffect.nextEffect=c,a.lastEffect=c):a.firstEffect=a.lastEffect=c}function ig(a,b){switch(a.tag){case 5:var c=a.type;b=1!==b.nodeType||c.toLowerCase()!==b.nodeName.toLowerCase()?null:b;return null!==b?(a.stateNode=b,!0):!1;case 6:return b=\"\"===a.pendingProps||3!==b.nodeType?null:b,null!==b?(a.stateNode=b,!0):!1;default:return!1}}\nfunction jg(a){if(gg){var b=fg;if(b){var c=b;if(!ig(a,b)){b=Fe(c);if(!b||!ig(a,b)){a.effectTag|=2;gg=!1;eg=a;return}hg(eg,c)}eg=a;fg=Ge(b)}else a.effectTag|=2,gg=!1,eg=a}}function kg(a){for(a=a.return;null!==a&&5!==a.tag&&3!==a.tag;)a=a.return;eg=a}function lg(a){if(a!==eg)return!1;if(!gg)return kg(a),gg=!0,!1;var b=a.type;if(5!==a.tag||\"head\"!==b&&\"body\"!==b&&!Ce(b,a.memoizedProps))for(b=fg;b;)hg(a,b),b=Fe(b);kg(a);fg=eg?Fe(a.stateNode):null;return!0}function mg(){fg=eg=null;gg=!1}var ng=Xb.ReactCurrentOwner;\nfunction P(a,b,c,d){b.child=null===a?dg(b,null,c,d):cg(b,a.child,c,d)}function og(a,b,c,d,e){c=c.render;var f=b.ref;Cf(b,e);d=c(d,f);b.effectTag|=1;P(a,b,d,e);return b.child}\nfunction pg(a,b,c,d,e,f){if(null===a){var g=c.type;if(\"function\"===typeof g&&!Xe(g)&&void 0===g.defaultProps&&null===c.compare)return b.tag=15,b.type=g,qg(a,b,g,d,e,f);a=$e(c.type,null,d,null,b.mode,f);a.ref=b.ref;a.return=b;return b.child=a}g=a.child;if(e<f&&(e=g.memoizedProps,c=c.compare,c=null!==c?c:jd,c(e,d)&&a.ref===b.ref))return rg(a,b,f);b.effectTag|=1;a=Ze(g,d,f);a.ref=b.ref;a.return=b;return b.child=a}\nfunction qg(a,b,c,d,e,f){return null!==a&&e<f&&jd(a.memoizedProps,d)&&a.ref===b.ref?rg(a,b,f):sg(a,b,c,d,f)}function tg(a,b){var c=b.ref;if(null===a&&null!==c||null!==a&&a.ref!==c)b.effectTag|=128}function sg(a,b,c,d,e){var f=L(c)?Ke:J.current;f=Le(b,f);Cf(b,e);c=c(d,f);b.effectTag|=1;P(a,b,c,e);return b.child}\nfunction ug(a,b,c,d,e){if(L(c)){var f=!0;Qe(b)}else f=!1;Cf(b,e);if(null===b.stateNode)null!==a&&(a.alternate=null,b.alternate=null,b.effectTag|=2),Wf(b,c,d,e),Yf(b,c,d,e),d=!0;else if(null===a){var g=b.stateNode,h=b.memoizedProps;g.props=h;var k=g.context,l=c.contextType;\"object\"===typeof l&&null!==l?l=Nf.currentDispatcher.readContext(l):(l=L(c)?Ke:J.current,l=Le(b,l));var m=c.getDerivedStateFromProps,q=\"function\"===typeof m||\"function\"===typeof g.getSnapshotBeforeUpdate;q||\"function\"!==typeof g.UNSAFE_componentWillReceiveProps&&\n\"function\"!==typeof g.componentWillReceiveProps||(h!==d||k!==l)&&Xf(b,g,d,l);jf=!1;var x=b.memoizedState;k=g.state=x;var z=b.updateQueue;null!==z&&(sf(b,z,d,g,e),k=b.memoizedState);h!==d||x!==k||K.current||jf?(\"function\"===typeof m&&(Pf(b,c,m,d),k=b.memoizedState),(h=jf||Vf(b,c,h,d,x,k,l))?(q||\"function\"!==typeof g.UNSAFE_componentWillMount&&\"function\"!==typeof g.componentWillMount||(\"function\"===typeof g.componentWillMount&&g.componentWillMount(),\"function\"===typeof g.UNSAFE_componentWillMount&&\ng.UNSAFE_componentWillMount()),\"function\"===typeof g.componentDidMount&&(b.effectTag|=4)):(\"function\"===typeof g.componentDidMount&&(b.effectTag|=4),b.memoizedProps=d,b.memoizedState=k),g.props=d,g.state=k,g.context=l,d=h):(\"function\"===typeof g.componentDidMount&&(b.effectTag|=4),d=!1)}else g=b.stateNode,h=b.memoizedProps,g.props=b.type===b.elementType?h:O(b.type,h),k=g.context,l=c.contextType,\"object\"===typeof l&&null!==l?l=Nf.currentDispatcher.readContext(l):(l=L(c)?Ke:J.current,l=Le(b,l)),m=c.getDerivedStateFromProps,\n(q=\"function\"===typeof m||\"function\"===typeof g.getSnapshotBeforeUpdate)||\"function\"!==typeof g.UNSAFE_componentWillReceiveProps&&\"function\"!==typeof g.componentWillReceiveProps||(h!==d||k!==l)&&Xf(b,g,d,l),jf=!1,k=b.memoizedState,x=g.state=k,z=b.updateQueue,null!==z&&(sf(b,z,d,g,e),x=b.memoizedState),h!==d||k!==x||K.current||jf?(\"function\"===typeof m&&(Pf(b,c,m,d),x=b.memoizedState),(m=jf||Vf(b,c,h,d,k,x,l))?(q||\"function\"!==typeof g.UNSAFE_componentWillUpdate&&\"function\"!==typeof g.componentWillUpdate||\n(\"function\"===typeof g.componentWillUpdate&&g.componentWillUpdate(d,x,l),\"function\"===typeof g.UNSAFE_componentWillUpdate&&g.UNSAFE_componentWillUpdate(d,x,l)),\"function\"===typeof g.componentDidUpdate&&(b.effectTag|=4),\"function\"===typeof g.getSnapshotBeforeUpdate&&(b.effectTag|=256)):(\"function\"!==typeof g.componentDidUpdate||h===a.memoizedProps&&k===a.memoizedState||(b.effectTag|=4),\"function\"!==typeof g.getSnapshotBeforeUpdate||h===a.memoizedProps&&k===a.memoizedState||(b.effectTag|=256),b.memoizedProps=\nd,b.memoizedState=x),g.props=d,g.state=x,g.context=l,d=m):(\"function\"!==typeof g.componentDidUpdate||h===a.memoizedProps&&k===a.memoizedState||(b.effectTag|=4),\"function\"!==typeof g.getSnapshotBeforeUpdate||h===a.memoizedProps&&k===a.memoizedState||(b.effectTag|=256),d=!1);return vg(a,b,c,d,f,e)}\nfunction vg(a,b,c,d,e,f){tg(a,b);var g=0!==(b.effectTag&64);if(!d&&!g)return e&&Re(b,c,!1),rg(a,b,f);d=b.stateNode;ng.current=b;var h=g&&\"function\"!==typeof c.getDerivedStateFromError?null:d.render();b.effectTag|=1;null!==a&&g?(b.child=cg(b,a.child,null,f),b.child=cg(b,null,h,f)):P(a,b,h,f);b.memoizedState=d.state;e&&Re(b,c,!0);return b.child}function wg(a){var b=a.stateNode;b.pendingContext?Oe(a,b.pendingContext,b.pendingContext!==b.context):b.context&&Oe(a,b.context,!1);If(a,b.containerInfo)}\nfunction xg(a,b,c){var d=b.mode,e=b.pendingProps,f=b.memoizedState;if(0===(b.effectTag&64)){f=null;var g=!1}else f={timedOutAt:null!==f?f.timedOutAt:0},g=!0,b.effectTag&=-65;null===a?g?(g=e.fallback,e=af(null,d,0,null),0===(b.mode&1)&&(e.child=null!==b.memoizedState?b.child.child:b.child),d=af(g,d,c,null),e.sibling=d,c=e,c.return=d.return=b):c=d=dg(b,null,e.children,c):null!==a.memoizedState?(d=a.child,a=d.sibling,g?(c=e.fallback,e=Ze(d,d.pendingProps,0),0===(b.mode&1)&&(g=null!==b.memoizedState?\nb.child.child:b.child,g!==d.child&&(e.child=g)),d=e.sibling=Ze(a,c,a.expirationTime),c=e,e.childExpirationTime=0,c.return=d.return=b):c=d=cg(b,d.child,e.children,c)):(a=a.child,g?(g=e.fallback,e=af(null,d,0,null),e.child=a,0===(b.mode&1)&&(e.child=null!==b.memoizedState?b.child.child:b.child),d=e.sibling=af(g,d,c,null),d.effectTag|=2,c=e,e.childExpirationTime=0,c.return=d.return=b):d=c=cg(b,a,e.children,c));b.memoizedState=f;b.child=c;return d}\nfunction rg(a,b,c){null!==a&&(b.firstContextDependency=a.firstContextDependency);if(b.childExpirationTime<c)return null;null!==a&&b.child!==a.child?t(\"153\"):void 0;if(null!==b.child){a=b.child;c=Ze(a,a.pendingProps,a.expirationTime);b.child=c;for(c.return=b;null!==a.sibling;)a=a.sibling,c=c.sibling=Ze(a,a.pendingProps,a.expirationTime),c.return=b;c.sibling=null}return b.child}\nfunction yg(a,b,c){var d=b.expirationTime;if(null!==a&&a.memoizedProps===b.pendingProps&&!K.current&&d<c){switch(b.tag){case 3:wg(b);mg();break;case 5:Kf(b);break;case 1:L(b.type)&&Qe(b);break;case 4:If(b,b.stateNode.containerInfo);break;case 10:Af(b,b.memoizedProps.value);break;case 13:if(null!==b.memoizedState){d=b.child.childExpirationTime;if(0!==d&&d>=c)return xg(a,b,c);b=rg(a,b,c);return null!==b?b.sibling:null}}return rg(a,b,c)}b.expirationTime=0;switch(b.tag){case 2:d=b.elementType;null!==\na&&(a.alternate=null,b.alternate=null,b.effectTag|=2);a=b.pendingProps;var e=Le(b,J.current);Cf(b,c);e=d(a,e);b.effectTag|=1;if(\"object\"===typeof e&&null!==e&&\"function\"===typeof e.render&&void 0===e.$$typeof){b.tag=1;if(L(d)){var f=!0;Qe(b)}else f=!1;b.memoizedState=null!==e.state&&void 0!==e.state?e.state:null;var g=d.getDerivedStateFromProps;\"function\"===typeof g&&Pf(b,d,g,a);e.updater=Uf;b.stateNode=e;e._reactInternalFiber=b;Yf(b,d,a,c);b=vg(null,b,d,!0,f,c)}else b.tag=0,P(null,b,e,c),b=b.child;\nreturn b;case 16:e=b.elementType;null!==a&&(a.alternate=null,b.alternate=null,b.effectTag|=2);f=b.pendingProps;a=Mf(e);b.type=a;e=b.tag=Ye(a);f=O(a,f);g=void 0;switch(e){case 0:g=sg(null,b,a,f,c);break;case 1:g=ug(null,b,a,f,c);break;case 11:g=og(null,b,a,f,c);break;case 14:g=pg(null,b,a,O(a.type,f),d,c);break;default:t(\"283\",a)}return g;case 0:return d=b.type,e=b.pendingProps,e=b.elementType===d?e:O(d,e),sg(a,b,d,e,c);case 1:return d=b.type,e=b.pendingProps,e=b.elementType===d?e:O(d,e),ug(a,b,d,\ne,c);case 3:wg(b);d=b.updateQueue;null===d?t(\"282\"):void 0;e=b.memoizedState;e=null!==e?e.element:null;sf(b,d,b.pendingProps,null,c);d=b.memoizedState.element;if(d===e)mg(),b=rg(a,b,c);else{e=b.stateNode;if(e=(null===a||null===a.child)&&e.hydrate)fg=Ge(b.stateNode.containerInfo),eg=b,e=gg=!0;e?(b.effectTag|=2,b.child=dg(b,null,d,c)):(P(a,b,d,c),mg());b=b.child}return b;case 5:return Kf(b),null===a&&jg(b),d=b.type,e=b.pendingProps,f=null!==a?a.memoizedProps:null,g=e.children,Ce(d,e)?g=null:null!==\nf&&Ce(d,f)&&(b.effectTag|=16),tg(a,b),1!==c&&b.mode&1&&e.hidden?(b.expirationTime=1,b=null):(P(a,b,g,c),b=b.child),b;case 6:return null===a&&jg(b),null;case 13:return xg(a,b,c);case 4:return If(b,b.stateNode.containerInfo),d=b.pendingProps,null===a?b.child=cg(b,null,d,c):P(a,b,d,c),b.child;case 11:return d=b.type,e=b.pendingProps,e=b.elementType===d?e:O(d,e),og(a,b,d,e,c);case 7:return P(a,b,b.pendingProps,c),b.child;case 8:return P(a,b,b.pendingProps.children,c),b.child;case 12:return P(a,b,b.pendingProps.children,\nc),b.child;case 10:a:{d=b.type._context;e=b.pendingProps;g=b.memoizedProps;f=e.value;Af(b,f);if(null!==g){var h=g.value;f=h===f&&(0!==h||1/h===1/f)||h!==h&&f!==f?0:(\"function\"===typeof d._calculateChangedBits?d._calculateChangedBits(h,f):1073741823)|0;if(0===f){if(g.children===e.children&&!K.current){b=rg(a,b,c);break a}}else for(g=b.child,null!==g&&(g.return=b);null!==g;){h=g.firstContextDependency;if(null!==h){do{if(h.context===d&&0!==(h.observedBits&f)){if(1===g.tag){var k=mf(c);k.tag=2;of(g,k)}g.expirationTime<\nc&&(g.expirationTime=c);k=g.alternate;null!==k&&k.expirationTime<c&&(k.expirationTime=c);for(var l=g.return;null!==l;){k=l.alternate;if(l.childExpirationTime<c)l.childExpirationTime=c,null!==k&&k.childExpirationTime<c&&(k.childExpirationTime=c);else if(null!==k&&k.childExpirationTime<c)k.childExpirationTime=c;else break;l=l.return}}k=g.child;h=h.next}while(null!==h)}else k=10===g.tag?g.type===b.type?null:g.child:g.child;if(null!==k)k.return=g;else for(k=g;null!==k;){if(k===b){k=null;break}g=k.sibling;\nif(null!==g){g.return=k.return;k=g;break}k=k.return}g=k}}P(a,b,e.children,c);b=b.child}return b;case 9:return e=b.type,f=b.pendingProps,d=f.children,Cf(b,c),e=Df(e,f.unstable_observedBits),d=d(e),b.effectTag|=1,P(a,b,d,c),b.child;case 14:return e=b.type,f=O(e.type,b.pendingProps),pg(a,b,e,f,d,c);case 15:return qg(a,b,b.type,b.pendingProps,d,c);case 17:return d=b.type,e=b.pendingProps,e=b.elementType===d?e:O(d,e),null!==a&&(a.alternate=null,b.alternate=null,b.effectTag|=2),b.tag=1,L(d)?(a=!0,Qe(b)):\na=!1,Cf(b,c),Wf(b,d,e,c),Yf(b,d,e,c),vg(null,b,d,!0,a,c);default:t(\"156\")}}function zg(a){a.effectTag|=4}var Ag=void 0,Bg=void 0,Fg=void 0,Gg=void 0;Ag=function(a,b){for(var c=b.child;null!==c;){if(5===c.tag||6===c.tag)a.appendChild(c.stateNode);else if(4!==c.tag&&null!==c.child){c.child.return=c;c=c.child;continue}if(c===b)break;for(;null===c.sibling;){if(null===c.return||c.return===b)return;c=c.return}c.sibling.return=c.return;c=c.sibling}};Bg=function(){};\nFg=function(a,b,c,d,e){var f=a.memoizedProps;if(f!==d){var g=b.stateNode;Hf(N.current);a=null;switch(c){case \"input\":f=Ac(g,f);d=Ac(g,d);a=[];break;case \"option\":f=ee(g,f);d=ee(g,d);a=[];break;case \"select\":f=n({},f,{value:void 0});d=n({},d,{value:void 0});a=[];break;case \"textarea\":f=ge(g,f);d=ge(g,d);a=[];break;default:\"function\"!==typeof f.onClick&&\"function\"===typeof d.onClick&&(g.onclick=ye)}ve(c,d);g=c=void 0;var h=null;for(c in f)if(!d.hasOwnProperty(c)&&f.hasOwnProperty(c)&&null!=f[c])if(\"style\"===\nc){var k=f[c];for(g in k)k.hasOwnProperty(g)&&(h||(h={}),h[g]=\"\")}else\"dangerouslySetInnerHTML\"!==c&&\"children\"!==c&&\"suppressContentEditableWarning\"!==c&&\"suppressHydrationWarning\"!==c&&\"autoFocus\"!==c&&(sa.hasOwnProperty(c)?a||(a=[]):(a=a||[]).push(c,null));for(c in d){var l=d[c];k=null!=f?f[c]:void 0;if(d.hasOwnProperty(c)&&l!==k&&(null!=l||null!=k))if(\"style\"===c)if(k){for(g in k)!k.hasOwnProperty(g)||l&&l.hasOwnProperty(g)||(h||(h={}),h[g]=\"\");for(g in l)l.hasOwnProperty(g)&&k[g]!==l[g]&&(h||\n(h={}),h[g]=l[g])}else h||(a||(a=[]),a.push(c,h)),h=l;else\"dangerouslySetInnerHTML\"===c?(l=l?l.__html:void 0,k=k?k.__html:void 0,null!=l&&k!==l&&(a=a||[]).push(c,\"\"+l)):\"children\"===c?k===l||\"string\"!==typeof l&&\"number\"!==typeof l||(a=a||[]).push(c,\"\"+l):\"suppressContentEditableWarning\"!==c&&\"suppressHydrationWarning\"!==c&&(sa.hasOwnProperty(c)?(null!=l&&xe(e,c),a||k===l||(a=[])):(a=a||[]).push(c,l))}h&&(a=a||[]).push(\"style\",h);e=a;(b.updateQueue=e)&&zg(b)}};Gg=function(a,b,c,d){c!==d&&zg(b)};\nfunction Hg(a,b){var c=b.source,d=b.stack;null===d&&null!==c&&(d=nc(c));null!==c&&mc(c.type);b=b.value;null!==a&&1===a.tag&&mc(a.type);try{console.error(b)}catch(e){setTimeout(function(){throw e;})}}function Ig(a){var b=a.ref;if(null!==b)if(\"function\"===typeof b)try{b(null)}catch(c){Jg(a,c)}else b.current=null}\nfunction Kg(a){\"function\"===typeof Te&&Te(a);switch(a.tag){case 0:case 11:case 14:case 15:var b=a.updateQueue;if(null!==b&&(b=b.lastEffect,null!==b)){var c=b=b.next;do{var d=c.destroy;if(null!==d){var e=a;try{d()}catch(f){Jg(e,f)}}c=c.next}while(c!==b)}break;case 1:Ig(a);b=a.stateNode;if(\"function\"===typeof b.componentWillUnmount)try{b.props=a.memoizedProps,b.state=a.memoizedState,b.componentWillUnmount()}catch(f){Jg(a,f)}break;case 5:Ig(a);break;case 4:Lg(a)}}\nfunction Mg(a){return 5===a.tag||3===a.tag||4===a.tag}\nfunction Ng(a){a:{for(var b=a.return;null!==b;){if(Mg(b)){var c=b;break a}b=b.return}t(\"160\");c=void 0}var d=b=void 0;switch(c.tag){case 5:b=c.stateNode;d=!1;break;case 3:b=c.stateNode.containerInfo;d=!0;break;case 4:b=c.stateNode.containerInfo;d=!0;break;default:t(\"161\")}c.effectTag&16&&(pe(b,\"\"),c.effectTag&=-17);a:b:for(c=a;;){for(;null===c.sibling;){if(null===c.return||Mg(c.return)){c=null;break a}c=c.return}c.sibling.return=c.return;for(c=c.sibling;5!==c.tag&&6!==c.tag;){if(c.effectTag&2)continue b;\nif(null===c.child||4===c.tag)continue b;else c.child.return=c,c=c.child}if(!(c.effectTag&2)){c=c.stateNode;break a}}for(var e=a;;){if(5===e.tag||6===e.tag)if(c)if(d){var f=b,g=e.stateNode,h=c;8===f.nodeType?f.parentNode.insertBefore(g,h):f.insertBefore(g,h)}else b.insertBefore(e.stateNode,c);else d?(g=b,h=e.stateNode,8===g.nodeType?(f=g.parentNode,f.insertBefore(h,g)):(f=g,f.appendChild(h)),g=g._reactRootContainer,null!==g&&void 0!==g||null!==f.onclick||(f.onclick=ye)):b.appendChild(e.stateNode);\nelse if(4!==e.tag&&null!==e.child){e.child.return=e;e=e.child;continue}if(e===a)break;for(;null===e.sibling;){if(null===e.return||e.return===a)return;e=e.return}e.sibling.return=e.return;e=e.sibling}}\nfunction Lg(a){for(var b=a,c=!1,d=void 0,e=void 0;;){if(!c){c=b.return;a:for(;;){null===c?t(\"160\"):void 0;switch(c.tag){case 5:d=c.stateNode;e=!1;break a;case 3:d=c.stateNode.containerInfo;e=!0;break a;case 4:d=c.stateNode.containerInfo;e=!0;break a}c=c.return}c=!0}if(5===b.tag||6===b.tag){a:for(var f=b,g=f;;)if(Kg(g),null!==g.child&&4!==g.tag)g.child.return=g,g=g.child;else{if(g===f)break;for(;null===g.sibling;){if(null===g.return||g.return===f)break a;g=g.return}g.sibling.return=g.return;g=g.sibling}e?\n(f=d,g=b.stateNode,8===f.nodeType?f.parentNode.removeChild(g):f.removeChild(g)):d.removeChild(b.stateNode)}else if(4===b.tag?(d=b.stateNode.containerInfo,e=!0):Kg(b),null!==b.child){b.child.return=b;b=b.child;continue}if(b===a)break;for(;null===b.sibling;){if(null===b.return||b.return===a)return;b=b.return;4===b.tag&&(c=!1)}b.sibling.return=b.return;b=b.sibling}}\nfunction Og(a,b){switch(b.tag){case 0:case 11:case 14:case 15:break;case 1:break;case 5:var c=b.stateNode;if(null!=c){var d=b.memoizedProps,e=null!==a?a.memoizedProps:d;a=b.type;var f=b.updateQueue;b.updateQueue=null;if(null!==f){c[Ha]=d;\"input\"===a&&\"radio\"===d.type&&null!=d.name&&Cc(c,d);we(a,e);b=we(a,d);for(e=0;e<f.length;e+=2){var g=f[e],h=f[e+1];\"style\"===g?te(c,h):\"dangerouslySetInnerHTML\"===g?oe(c,h):\"children\"===g?pe(c,h):yc(c,g,h,b)}switch(a){case \"input\":Dc(c,d);break;case \"textarea\":ie(c,\nd);break;case \"select\":b=c._wrapperState.wasMultiple,c._wrapperState.wasMultiple=!!d.multiple,a=d.value,null!=a?fe(c,!!d.multiple,a,!1):b!==!!d.multiple&&(null!=d.defaultValue?fe(c,!!d.multiple,d.defaultValue,!0):fe(c,!!d.multiple,d.multiple?[]:\"\",!1))}}}break;case 6:null===b.stateNode?t(\"162\"):void 0;b.stateNode.nodeValue=b.memoizedProps;break;case 3:break;case 12:break;case 13:c=b.memoizedState;a=b;null===c?d=!1:(d=!0,a=b.child,0===c.timedOutAt&&(c.timedOutAt=Qf()));if(null!==a)a:for(b=c=a;;){if(5===\nb.tag)a=b.stateNode,d?a.style.display=\"none\":(a=b.stateNode,f=b.memoizedProps.style,f=void 0!==f&&null!==f&&f.hasOwnProperty(\"display\")?f.display:null,a.style.display=se(\"display\",f));else if(6===b.tag)b.stateNode.nodeValue=d?\"\":b.memoizedProps;else if(13===b.tag&&null!==b.memoizedState){a=b.child.sibling;a.return=b;b=a;continue}else if(null!==b.child){b.child.return=b;b=b.child;continue}if(b===c)break a;for(;null===b.sibling;){if(null===b.return||b.return===c)break a;b=b.return}b.sibling.return=\nb.return;b=b.sibling}break;case 17:break;default:t(\"163\")}}function Pg(a,b,c){c=mf(c);c.tag=3;c.payload={element:null};var d=b.value;c.callback=function(){Qg(d);Hg(a,b)};return c}\nfunction Rg(a,b,c){c=mf(c);c.tag=3;var d=a.type.getDerivedStateFromError;if(\"function\"===typeof d){var e=b.value;c.payload=function(){return d(e)}}var f=a.stateNode;null!==f&&\"function\"===typeof f.componentDidCatch&&(c.callback=function(){\"function\"!==typeof d&&(null===Sg?Sg=new Set([this]):Sg.add(this));var c=b.value,e=b.stack;Hg(a,b);this.componentDidCatch(c,{componentStack:null!==e?e:\"\"})});return c}\nfunction Tg(a){switch(a.tag){case 1:L(a.type)&&Me(a);var b=a.effectTag;return b&2048?(a.effectTag=b&-2049|64,a):null;case 3:return Jf(a),Ne(a),b=a.effectTag,0!==(b&64)?t(\"285\"):void 0,a.effectTag=b&-2049|64,a;case 5:return Lf(a),null;case 13:return b=a.effectTag,b&2048?(a.effectTag=b&-2049|64,a):null;case 4:return Jf(a),null;case 10:return Bf(a),null;default:return null}}\nvar Ug={readContext:Df},Vg=Xb.ReactCurrentOwner,Wg=1073741822,Xg=0,Yg=!1,R=null,S=null,T=0,Zg=-1,$g=!1,U=null,ah=!1,bh=null,ch=null,Sg=null;function dh(){if(null!==R)for(var a=R.return;null!==a;){var b=a;switch(b.tag){case 1:var c=b.type.childContextTypes;null!==c&&void 0!==c&&Me(b);break;case 3:Jf(b);Ne(b);break;case 5:Lf(b);break;case 4:Jf(b);break;case 10:Bf(b)}a=a.return}S=null;T=0;Zg=-1;$g=!1;R=null}function Sf(){null!==ch&&(ba.unstable_cancelCallback(bh),ch())}\nfunction eh(a){for(;;){var b=a.alternate,c=a.return,d=a.sibling;if(0===(a.effectTag&1024)){R=a;a:{var e=b;b=a;var f=T;var g=b.pendingProps;switch(b.tag){case 2:break;case 16:break;case 15:case 0:break;case 1:L(b.type)&&Me(b);break;case 3:Jf(b);Ne(b);g=b.stateNode;g.pendingContext&&(g.context=g.pendingContext,g.pendingContext=null);if(null===e||null===e.child)lg(b),b.effectTag&=-3;Bg(b);break;case 5:Lf(b);var h=Hf(Gf.current);f=b.type;if(null!==e&&null!=b.stateNode)Fg(e,b,f,g,h),e.ref!==b.ref&&(b.effectTag|=\n128);else if(g){var k=Hf(N.current);if(lg(b)){g=b;e=g.stateNode;var l=g.type,m=g.memoizedProps,q=h;e[Ga]=g;e[Ha]=m;f=void 0;h=l;switch(h){case \"iframe\":case \"object\":G(\"load\",e);break;case \"video\":case \"audio\":for(l=0;l<bb.length;l++)G(bb[l],e);break;case \"source\":G(\"error\",e);break;case \"img\":case \"image\":case \"link\":G(\"error\",e);G(\"load\",e);break;case \"form\":G(\"reset\",e);G(\"submit\",e);break;case \"details\":G(\"toggle\",e);break;case \"input\":Bc(e,m);G(\"invalid\",e);xe(q,\"onChange\");break;case \"select\":e._wrapperState=\n{wasMultiple:!!m.multiple};G(\"invalid\",e);xe(q,\"onChange\");break;case \"textarea\":he(e,m),G(\"invalid\",e),xe(q,\"onChange\")}ve(h,m);l=null;for(f in m)m.hasOwnProperty(f)&&(k=m[f],\"children\"===f?\"string\"===typeof k?e.textContent!==k&&(l=[\"children\",k]):\"number\"===typeof k&&e.textContent!==\"\"+k&&(l=[\"children\",\"\"+k]):sa.hasOwnProperty(f)&&null!=k&&xe(q,f));switch(h){case \"input\":Vb(e);Fc(e,m,!0);break;case \"textarea\":Vb(e);je(e,m);break;case \"select\":case \"option\":break;default:\"function\"===typeof m.onClick&&\n(e.onclick=ye)}f=l;g.updateQueue=f;g=null!==f?!0:!1;g&&zg(b)}else{m=b;e=f;q=g;l=9===h.nodeType?h:h.ownerDocument;k===ke.html&&(k=le(e));k===ke.html?\"script\"===e?(e=l.createElement(\"div\"),e.innerHTML=\"<script>\\x3c/script>\",l=e.removeChild(e.firstChild)):\"string\"===typeof q.is?l=l.createElement(e,{is:q.is}):(l=l.createElement(e),\"select\"===e&&q.multiple&&(l.multiple=!0)):l=l.createElementNS(k,e);e=l;e[Ga]=m;e[Ha]=g;Ag(e,b,!1,!1);q=e;l=f;m=g;var x=h,z=we(l,m);switch(l){case \"iframe\":case \"object\":G(\"load\",\nq);h=m;break;case \"video\":case \"audio\":for(h=0;h<bb.length;h++)G(bb[h],q);h=m;break;case \"source\":G(\"error\",q);h=m;break;case \"img\":case \"image\":case \"link\":G(\"error\",q);G(\"load\",q);h=m;break;case \"form\":G(\"reset\",q);G(\"submit\",q);h=m;break;case \"details\":G(\"toggle\",q);h=m;break;case \"input\":Bc(q,m);h=Ac(q,m);G(\"invalid\",q);xe(x,\"onChange\");break;case \"option\":h=ee(q,m);break;case \"select\":q._wrapperState={wasMultiple:!!m.multiple};h=n({},m,{value:void 0});G(\"invalid\",q);xe(x,\"onChange\");break;case \"textarea\":he(q,\nm);h=ge(q,m);G(\"invalid\",q);xe(x,\"onChange\");break;default:h=m}ve(l,h);k=void 0;var B=l,Q=q,v=h;for(k in v)if(v.hasOwnProperty(k)){var r=v[k];\"style\"===k?te(Q,r):\"dangerouslySetInnerHTML\"===k?(r=r?r.__html:void 0,null!=r&&oe(Q,r)):\"children\"===k?\"string\"===typeof r?(\"textarea\"!==B||\"\"!==r)&&pe(Q,r):\"number\"===typeof r&&pe(Q,\"\"+r):\"suppressContentEditableWarning\"!==k&&\"suppressHydrationWarning\"!==k&&\"autoFocus\"!==k&&(sa.hasOwnProperty(k)?null!=r&&xe(x,k):null!=r&&yc(Q,k,r,z))}switch(l){case \"input\":Vb(q);\nFc(q,m,!1);break;case \"textarea\":Vb(q);je(q,m);break;case \"option\":null!=m.value&&q.setAttribute(\"value\",\"\"+zc(m.value));break;case \"select\":h=q;h.multiple=!!m.multiple;q=m.value;null!=q?fe(h,!!m.multiple,q,!1):null!=m.defaultValue&&fe(h,!!m.multiple,m.defaultValue,!0);break;default:\"function\"===typeof h.onClick&&(q.onclick=ye)}(g=Be(f,g))&&zg(b);b.stateNode=e}null!==b.ref&&(b.effectTag|=128)}else null===b.stateNode?t(\"166\"):void 0;break;case 6:e&&null!=b.stateNode?Gg(e,b,e.memoizedProps,g):(\"string\"!==\ntypeof g&&(null===b.stateNode?t(\"166\"):void 0),e=Hf(Gf.current),Hf(N.current),lg(b)?(g=b,f=g.stateNode,e=g.memoizedProps,f[Ga]=g,(g=f.nodeValue!==e)&&zg(b)):(f=b,g=(9===e.nodeType?e:e.ownerDocument).createTextNode(g),g[Ga]=b,f.stateNode=g));break;case 11:break;case 13:g=b.memoizedState;if(0!==(b.effectTag&64)){b.expirationTime=f;R=b;break a}g=null!==g;f=null!==e&&null!==e.memoizedState;null!==e&&!g&&f&&(e=e.child.sibling,null!==e&&(h=b.firstEffect,null!==h?(b.firstEffect=e,e.nextEffect=h):(b.firstEffect=\nb.lastEffect=e,e.nextEffect=null),e.effectTag=8));if(g!==f||0===(b.effectTag&1)&&g)b.effectTag|=4;break;case 7:break;case 8:break;case 12:break;case 4:Jf(b);Bg(b);break;case 10:Bf(b);break;case 9:break;case 14:break;case 17:L(b.type)&&Me(b);break;default:t(\"156\")}R=null}b=a;if(1===T||1!==b.childExpirationTime){g=0;for(f=b.child;null!==f;)e=f.expirationTime,h=f.childExpirationTime,e>g&&(g=e),h>g&&(g=h),f=f.sibling;b.childExpirationTime=g}if(null!==R)return R;null!==c&&0===(c.effectTag&1024)&&(null===\nc.firstEffect&&(c.firstEffect=a.firstEffect),null!==a.lastEffect&&(null!==c.lastEffect&&(c.lastEffect.nextEffect=a.firstEffect),c.lastEffect=a.lastEffect),1<a.effectTag&&(null!==c.lastEffect?c.lastEffect.nextEffect=a:c.firstEffect=a,c.lastEffect=a))}else{a=Tg(a,T);if(null!==a)return a.effectTag&=1023,a;null!==c&&(c.firstEffect=c.lastEffect=null,c.effectTag|=1024)}if(null!==d)return d;if(null!==c)a=c;else break}return null}\nfunction fh(a){var b=yg(a.alternate,a,T);a.memoizedProps=a.pendingProps;null===b&&(b=eh(a));Vg.current=null;return b}\nfunction gh(a,b){Yg?t(\"243\"):void 0;Sf();Yg=!0;Vg.currentDispatcher=Ug;var c=a.nextExpirationTimeToWorkOn;if(c!==T||a!==S||null===R)dh(),S=a,T=c,R=Ze(S.current,null,T),a.pendingCommitExpirationTime=0;var d=!1;do{try{if(b)for(;null!==R&&!hh();)R=fh(R);else for(;null!==R;)R=fh(R)}catch(B){if(zf=yf=xf=null,null===R)d=!0,Qg(B);else{null===R?t(\"271\"):void 0;var e=R,f=e.return;if(null===f)d=!0,Qg(B);else{a:{var g=a,h=f,k=e,l=B;f=T;k.effectTag|=1024;k.firstEffect=k.lastEffect=null;if(null!==l&&\"object\"===\ntypeof l&&\"function\"===typeof l.then){var m=l;l=h;var q=-1,x=-1;do{if(13===l.tag){var z=l.alternate;if(null!==z&&(z=z.memoizedState,null!==z)){x=10*(1073741822-z.timedOutAt);break}z=l.pendingProps.maxDuration;if(\"number\"===typeof z)if(0>=z)q=0;else if(-1===q||z<q)q=z}l=l.return}while(null!==l);l=h;do{if(z=13===l.tag)z=void 0===l.memoizedProps.fallback?!1:null===l.memoizedState;if(z){h=ih.bind(null,g,l,k,0===(l.mode&1)?1073741823:f);m.then(h,h);if(0===(l.mode&1)){l.effectTag|=64;k.effectTag&=-1957;\n1===k.tag&&null===k.alternate&&(k.tag=17);k.expirationTime=f;break a}-1===q?g=1073741823:(-1===x&&(x=10*(1073741822-hf(g,f))-5E3),g=x+q);0<=g&&Zg<g&&(Zg=g);l.effectTag|=2048;l.expirationTime=f;break a}l=l.return}while(null!==l);l=Error((mc(k.type)||\"A React component\")+\" suspended while rendering, but no fallback UI was specified.\\n\\nAdd a <Suspense fallback=...> component higher in the tree to provide a loading indicator or placeholder to display.\"+nc(k))}$g=!0;l=vf(l,k);g=h;do{switch(g.tag){case 3:k=\nl;g.effectTag|=2048;g.expirationTime=f;f=Pg(g,k,f);pf(g,f);break a;case 1:if(k=l,h=g.type,m=g.stateNode,0===(g.effectTag&64)&&(\"function\"===typeof h.getDerivedStateFromError||null!==m&&\"function\"===typeof m.componentDidCatch&&(null===Sg||!Sg.has(m)))){g.effectTag|=2048;g.expirationTime=f;f=Rg(g,k,f);pf(g,f);break a}}g=g.return}while(null!==g)}R=eh(e);continue}}}break}while(1);Yg=!1;zf=yf=xf=Vg.currentDispatcher=null;if(d)S=null,a.finishedWork=null;else if(null!==R)a.finishedWork=null;else{d=a.current.alternate;\nnull===d?t(\"281\"):void 0;S=null;if($g){e=a.latestPendingTime;f=a.latestSuspendedTime;g=a.latestPingedTime;if(0!==e&&e<c||0!==f&&f<c||0!==g&&g<c){gf(a,c);jh(a,d,c,a.expirationTime,-1);return}if(!a.didError&&b){a.didError=!0;c=a.nextExpirationTimeToWorkOn=c;b=a.expirationTime=1073741823;jh(a,d,c,b,-1);return}}b&&-1!==Zg?(gf(a,c),b=10*(1073741822-hf(a,c)),b<Zg&&(Zg=b),b=10*(1073741822-Qf()),b=Zg-b,jh(a,d,c,a.expirationTime,0>b?0:b)):(a.pendingCommitExpirationTime=c,a.finishedWork=d)}}\nfunction Jg(a,b){for(var c=a.return;null!==c;){switch(c.tag){case 1:var d=c.stateNode;if(\"function\"===typeof c.type.getDerivedStateFromError||\"function\"===typeof d.componentDidCatch&&(null===Sg||!Sg.has(d))){a=vf(b,a);a=Rg(c,a,1073741823);of(c,a);Tf(c,1073741823);return}break;case 3:a=vf(b,a);a=Pg(c,a,1073741823);of(c,a);Tf(c,1073741823);return}c=c.return}3===a.tag&&(c=vf(b,a),c=Pg(a,c,1073741823),of(a,c),Tf(a,1073741823))}\nfunction Rf(a,b){0!==Xg?a=Xg:Yg?a=ah?1073741823:T:b.mode&1?(a=kh?1073741822-10*(((1073741822-a+15)/10|0)+1):1073741822-25*(((1073741822-a+500)/25|0)+1),null!==S&&a===T&&--a):a=1073741823;kh&&(0===lh||a<lh)&&(lh=a);return a}\nfunction ih(a,b,c,d){var e=a.earliestSuspendedTime;var f=a.latestSuspendedTime;if(0!==e&&d<=e&&d>=f){f=e=d;a.didError=!1;var g=a.latestPingedTime;if(0===g||g>f)a.latestPingedTime=f;ff(f,a)}else e=Qf(),e=Rf(e,b),ef(a,e);0!==(b.mode&1)&&a===S&&T===d&&(S=null);mh(b,e);0===(b.mode&1)&&(mh(c,e),1===c.tag&&null!==c.stateNode&&(b=mf(e),b.tag=2,of(c,b)));c=a.expirationTime;0!==c&&nh(a,c)}\nfunction mh(a,b){a.expirationTime<b&&(a.expirationTime=b);var c=a.alternate;null!==c&&c.expirationTime<b&&(c.expirationTime=b);var d=a.return,e=null;if(null===d&&3===a.tag)e=a.stateNode;else for(;null!==d;){c=d.alternate;d.childExpirationTime<b&&(d.childExpirationTime=b);null!==c&&c.childExpirationTime<b&&(c.childExpirationTime=b);if(null===d.return&&3===d.tag){e=d.stateNode;break}d=d.return}return e}\nfunction Tf(a,b){a=mh(a,b);null!==a&&(!Yg&&0!==T&&b>T&&dh(),ef(a,b),Yg&&!ah&&S===a||nh(a,a.expirationTime),oh>ph&&(oh=0,t(\"185\")))}function qh(a,b,c,d,e){var f=Xg;Xg=1073741823;try{return a(b,c,d,e)}finally{Xg=f}}var rh=null,V=null,sh=0,th=void 0,W=!1,uh=null,X=0,lh=0,vh=!1,wh=null,Z=!1,xh=!1,kh=!1,yh=null,zh=ba.unstable_now(),Ah=1073741822-(zh/10|0),Bh=Ah,ph=50,oh=0,Ch=null;function Dh(){Ah=1073741822-((ba.unstable_now()-zh)/10|0)}\nfunction Eh(a,b){if(0!==sh){if(b<sh)return;null!==th&&ba.unstable_cancelCallback(th)}sh=b;a=ba.unstable_now()-zh;th=ba.unstable_scheduleCallback(Fh,{timeout:10*(1073741822-b)-a})}function jh(a,b,c,d,e){a.expirationTime=d;0!==e||hh()?0<e&&(a.timeoutHandle=De(Gh.bind(null,a,b,c),e)):(a.pendingCommitExpirationTime=c,a.finishedWork=b)}function Gh(a,b,c){a.pendingCommitExpirationTime=c;a.finishedWork=b;Dh();Bh=Ah;Hh(a,c)}function Qf(){if(W)return Bh;Ih();if(0===X||1===X)Dh(),Bh=Ah;return Bh}\nfunction nh(a,b){null===a.nextScheduledRoot?(a.expirationTime=b,null===V?(rh=V=a,a.nextScheduledRoot=a):(V=V.nextScheduledRoot=a,V.nextScheduledRoot=rh)):b>a.expirationTime&&(a.expirationTime=b);W||(Z?xh&&(uh=a,X=1073741823,Jh(a,1073741823,!1)):1073741823===b?Kh(1073741823,!1):Eh(a,b))}\nfunction Ih(){var a=0,b=null;if(null!==V)for(var c=V,d=rh;null!==d;){var e=d.expirationTime;if(0===e){null===c||null===V?t(\"244\"):void 0;if(d===d.nextScheduledRoot){rh=V=d.nextScheduledRoot=null;break}else if(d===rh)rh=e=d.nextScheduledRoot,V.nextScheduledRoot=e,d.nextScheduledRoot=null;else if(d===V){V=c;V.nextScheduledRoot=rh;d.nextScheduledRoot=null;break}else c.nextScheduledRoot=d.nextScheduledRoot,d.nextScheduledRoot=null;d=c.nextScheduledRoot}else{e>a&&(a=e,b=d);if(d===V)break;if(1073741823===\na)break;c=d;d=d.nextScheduledRoot}}uh=b;X=a}var Lh=!1;function hh(){return Lh?!0:ba.unstable_shouldYield()?Lh=!0:!1}function Fh(){try{if(!hh()&&null!==rh){Dh();var a=rh;do{var b=a.expirationTime;0!==b&&Ah<=b&&(a.nextExpirationTimeToWorkOn=Ah);a=a.nextScheduledRoot}while(a!==rh)}Kh(0,!0)}finally{Lh=!1}}\nfunction Kh(a,b){Ih();if(b)for(Dh(),Bh=Ah;null!==uh&&0!==X&&a<=X&&!(Lh&&Ah>X);)Jh(uh,X,Ah>X),Ih(),Dh(),Bh=Ah;else for(;null!==uh&&0!==X&&a<=X;)Jh(uh,X,!1),Ih();b&&(sh=0,th=null);0!==X&&Eh(uh,X);oh=0;Ch=null;if(null!==yh)for(a=yh,yh=null,b=0;b<a.length;b++){var c=a[b];try{c._onComplete()}catch(d){vh||(vh=!0,wh=d)}}if(vh)throw a=wh,wh=null,vh=!1,a;}function Hh(a,b){W?t(\"253\"):void 0;uh=a;X=b;Jh(a,b,!1);Kh(1073741823,!1)}\nfunction Jh(a,b,c){W?t(\"245\"):void 0;W=!0;if(c){var d=a.finishedWork;null!==d?Mh(a,d,b):(a.finishedWork=null,d=a.timeoutHandle,-1!==d&&(a.timeoutHandle=-1,Ee(d)),gh(a,c),d=a.finishedWork,null!==d&&(hh()?a.finishedWork=d:Mh(a,d,b)))}else d=a.finishedWork,null!==d?Mh(a,d,b):(a.finishedWork=null,d=a.timeoutHandle,-1!==d&&(a.timeoutHandle=-1,Ee(d)),gh(a,c),d=a.finishedWork,null!==d&&Mh(a,d,b));W=!1}\nfunction Mh(a,b,c){var d=a.firstBatch;if(null!==d&&d._expirationTime>=c&&(null===yh?yh=[d]:yh.push(d),d._defer)){a.finishedWork=b;a.expirationTime=0;return}a.finishedWork=null;a===Ch?oh++:(Ch=a,oh=0);ah=Yg=!0;a.current===b?t(\"177\"):void 0;c=a.pendingCommitExpirationTime;0===c?t(\"261\"):void 0;a.pendingCommitExpirationTime=0;d=b.expirationTime;var e=b.childExpirationTime;d=e>d?e:d;a.didError=!1;0===d?(a.earliestPendingTime=0,a.latestPendingTime=0,a.earliestSuspendedTime=0,a.latestSuspendedTime=0,a.latestPingedTime=\n0):(e=a.latestPendingTime,0!==e&&(e>d?a.earliestPendingTime=a.latestPendingTime=0:a.earliestPendingTime>d&&(a.earliestPendingTime=a.latestPendingTime)),e=a.earliestSuspendedTime,0===e?ef(a,d):d<a.latestSuspendedTime?(a.earliestSuspendedTime=0,a.latestSuspendedTime=0,a.latestPingedTime=0,ef(a,d)):d>e&&ef(a,d));ff(0,a);Vg.current=null;1<b.effectTag?null!==b.lastEffect?(b.lastEffect.nextEffect=b,d=b.firstEffect):d=b:d=b.firstEffect;ze=Hd;e=Td();if(Ud(e)){if(\"selectionStart\"in e)var f={start:e.selectionStart,\nend:e.selectionEnd};else a:{f=(f=e.ownerDocument)&&f.defaultView||window;var g=f.getSelection&&f.getSelection();if(g&&0!==g.rangeCount){f=g.anchorNode;var h=g.anchorOffset,k=g.focusNode;g=g.focusOffset;try{f.nodeType,k.nodeType}catch(db){f=null;break a}var l=0,m=-1,q=-1,x=0,z=0,B=e,Q=null;b:for(;;){for(var v;;){B!==f||0!==h&&3!==B.nodeType||(m=l+h);B!==k||0!==g&&3!==B.nodeType||(q=l+g);3===B.nodeType&&(l+=B.nodeValue.length);if(null===(v=B.firstChild))break;Q=B;B=v}for(;;){if(B===e)break b;Q===f&&\n++x===h&&(m=l);Q===k&&++z===g&&(q=l);if(null!==(v=B.nextSibling))break;B=Q;Q=B.parentNode}B=v}f=-1===m||-1===q?null:{start:m,end:q}}else f=null}f=f||{start:0,end:0}}else f=null;Ae={focusedElem:e,selectionRange:f};Hd=!1;for(U=d;null!==U;){e=!1;f=void 0;try{for(;null!==U;){if(U.effectTag&256)a:{var r=U.alternate;h=U;switch(h.tag){case 0:case 11:case 15:break a;case 1:if(h.effectTag&256&&null!==r){var u=r.memoizedProps,y=r.memoizedState,Y=h.stateNode,Th=Y.getSnapshotBeforeUpdate(h.elementType===h.type?\nu:O(h.type,u),y);Y.__reactInternalSnapshotBeforeUpdate=Th}break a;case 3:case 5:case 6:case 4:case 17:break a;default:t(\"163\")}}U=U.nextEffect}}catch(db){e=!0,f=db}e&&(null===U?t(\"178\"):void 0,Jg(U,f),null!==U&&(U=U.nextEffect))}for(U=d;null!==U;){r=!1;u=void 0;try{for(;null!==U;){var w=U.effectTag;w&16&&pe(U.stateNode,\"\");if(w&128){var C=U.alternate;if(null!==C){var p=C.ref;null!==p&&(\"function\"===typeof p?p(null):p.current=null)}}switch(w&14){case 2:Ng(U);U.effectTag&=-3;break;case 6:Ng(U);U.effectTag&=\n-3;Og(U.alternate,U);break;case 4:Og(U.alternate,U);break;case 8:y=U,Lg(y),y.return=null,y.child=null,y.alternate&&(y.alternate.child=null,y.alternate.return=null)}U=U.nextEffect}}catch(db){r=!0,u=db}r&&(null===U?t(\"178\"):void 0,Jg(U,u),null!==U&&(U=U.nextEffect))}p=Ae;C=Td();w=p.focusedElem;u=p.selectionRange;if(C!==w&&w&&w.ownerDocument&&Sd(w.ownerDocument.documentElement,w)){null!==u&&Ud(w)&&(C=u.start,p=u.end,void 0===p&&(p=C),\"selectionStart\"in w?(w.selectionStart=C,w.selectionEnd=Math.min(p,\nw.value.length)):(p=(C=w.ownerDocument||document)&&C.defaultView||window,p.getSelection&&(p=p.getSelection(),y=w.textContent.length,r=Math.min(u.start,y),u=void 0===u.end?r:Math.min(u.end,y),!p.extend&&r>u&&(y=u,u=r,r=y),y=Rd(w,r),Y=Rd(w,u),y&&Y&&(1!==p.rangeCount||p.anchorNode!==y.node||p.anchorOffset!==y.offset||p.focusNode!==Y.node||p.focusOffset!==Y.offset)&&(C=C.createRange(),C.setStart(y.node,y.offset),p.removeAllRanges(),r>u?(p.addRange(C),p.extend(Y.node,Y.offset)):(C.setEnd(Y.node,Y.offset),\np.addRange(C))))));C=[];for(p=w;p=p.parentNode;)1===p.nodeType&&C.push({element:p,left:p.scrollLeft,top:p.scrollTop});\"function\"===typeof w.focus&&w.focus();for(w=0;w<C.length;w++)p=C[w],p.element.scrollLeft=p.left,p.element.scrollTop=p.top}Ae=null;Hd=!!ze;ze=null;a.current=b;for(U=d;null!==U;){d=!1;w=void 0;try{for(C=c;null!==U;){var Fb=U.effectTag;if(Fb&36){var Gb=U.alternate;p=U;r=C;switch(p.tag){case 0:case 11:case 15:break;case 1:var wc=p.stateNode;if(p.effectTag&4)if(null===Gb)wc.componentDidMount();\nelse{var ci=p.elementType===p.type?Gb.memoizedProps:O(p.type,Gb.memoizedProps);wc.componentDidUpdate(ci,Gb.memoizedState,wc.__reactInternalSnapshotBeforeUpdate)}var Cg=p.updateQueue;null!==Cg&&tf(p,Cg,wc,r);break;case 3:var Dg=p.updateQueue;if(null!==Dg){u=null;if(null!==p.child)switch(p.child.tag){case 5:u=p.child.stateNode;break;case 1:u=p.child.stateNode}tf(p,Dg,u,r)}break;case 5:var di=p.stateNode;null===Gb&&p.effectTag&4&&Be(p.type,p.memoizedProps)&&di.focus();break;case 6:break;case 4:break;\ncase 12:break;case 13:break;case 17:break;default:t(\"163\")}}if(Fb&128){var Ic=U.ref;if(null!==Ic){var Eg=U.stateNode;switch(U.tag){case 5:var ce=Eg;break;default:ce=Eg}\"function\"===typeof Ic?Ic(ce):Ic.current=ce}}U=U.nextEffect}}catch(db){d=!0,w=db}d&&(null===U?t(\"178\"):void 0,Jg(U,w),null!==U&&(U=U.nextEffect))}Yg=ah=!1;\"function\"===typeof Se&&Se(b.stateNode);Fb=b.expirationTime;b=b.childExpirationTime;b=b>Fb?b:Fb;0===b&&(Sg=null);a.expirationTime=b;a.finishedWork=null}\nfunction Qg(a){null===uh?t(\"246\"):void 0;uh.expirationTime=0;vh||(vh=!0,wh=a)}function Nh(a,b){var c=Z;Z=!0;try{return a(b)}finally{(Z=c)||W||Kh(1073741823,!1)}}function Oh(a,b){if(Z&&!xh){xh=!0;try{return a(b)}finally{xh=!1}}return a(b)}function Ph(a,b,c){if(kh)return a(b,c);Z||W||0===lh||(Kh(lh,!1),lh=0);var d=kh,e=Z;Z=kh=!0;try{return a(b,c)}finally{kh=d,(Z=e)||W||Kh(1073741823,!1)}}\nfunction Qh(a,b,c,d,e){var f=b.current;a:if(c){c=c._reactInternalFiber;b:{2===kd(c)&&1===c.tag?void 0:t(\"170\");var g=c;do{switch(g.tag){case 3:g=g.stateNode.context;break b;case 1:if(L(g.type)){g=g.stateNode.__reactInternalMemoizedMergedChildContext;break b}}g=g.return}while(null!==g);t(\"171\");g=void 0}if(1===c.tag){var h=c.type;if(L(h)){c=Pe(c,h,g);break a}}c=g}else c=Je;null===b.context?b.context=c:b.pendingContext=c;b=e;e=mf(d);e.payload={element:a};b=void 0===b?null:b;null!==b&&(e.callback=b);\nSf();of(f,e);Tf(f,d);return d}function Rh(a,b,c,d){var e=b.current,f=Qf();e=Rf(f,e);return Qh(a,b,c,e,d)}function Sh(a){a=a.current;if(!a.child)return null;switch(a.child.tag){case 5:return a.child.stateNode;default:return a.child.stateNode}}function Uh(a,b,c){var d=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:$b,key:null==d?null:\"\"+d,children:a,containerInfo:b,implementation:c}}\nCb=function(a,b,c){switch(b){case \"input\":Dc(a,c);b=c.name;if(\"radio\"===c.type&&null!=b){for(c=a;c.parentNode;)c=c.parentNode;c=c.querySelectorAll(\"input[name=\"+JSON.stringify(\"\"+b)+'][type=\"radio\"]');for(b=0;b<c.length;b++){var d=c[b];if(d!==a&&d.form===a.form){var e=La(d);e?void 0:t(\"90\");Wb(d);Dc(d,e)}}}break;case \"textarea\":ie(a,c);break;case \"select\":b=c.value,null!=b&&fe(a,!!c.multiple,b,!1)}};\nfunction Vh(a){var b=1073741822-25*(((1073741822-Qf()+500)/25|0)+1);b>=Wg&&(b=Wg-1);this._expirationTime=Wg=b;this._root=a;this._callbacks=this._next=null;this._hasChildren=this._didComplete=!1;this._children=null;this._defer=!0}Vh.prototype.render=function(a){this._defer?void 0:t(\"250\");this._hasChildren=!0;this._children=a;var b=this._root._internalRoot,c=this._expirationTime,d=new Wh;Qh(a,b,null,c,d._onCommit);return d};\nVh.prototype.then=function(a){if(this._didComplete)a();else{var b=this._callbacks;null===b&&(b=this._callbacks=[]);b.push(a)}};\nVh.prototype.commit=function(){var a=this._root._internalRoot,b=a.firstBatch;this._defer&&null!==b?void 0:t(\"251\");if(this._hasChildren){var c=this._expirationTime;if(b!==this){this._hasChildren&&(c=this._expirationTime=b._expirationTime,this.render(this._children));for(var d=null,e=b;e!==this;)d=e,e=e._next;null===d?t(\"251\"):void 0;d._next=e._next;this._next=b;a.firstBatch=this}this._defer=!1;Hh(a,c);b=this._next;this._next=null;b=a.firstBatch=b;null!==b&&b._hasChildren&&b.render(b._children)}else this._next=\nnull,this._defer=!1};Vh.prototype._onComplete=function(){if(!this._didComplete){this._didComplete=!0;var a=this._callbacks;if(null!==a)for(var b=0;b<a.length;b++)(0,a[b])()}};function Wh(){this._callbacks=null;this._didCommit=!1;this._onCommit=this._onCommit.bind(this)}Wh.prototype.then=function(a){if(this._didCommit)a();else{var b=this._callbacks;null===b&&(b=this._callbacks=[]);b.push(a)}};\nWh.prototype._onCommit=function(){if(!this._didCommit){this._didCommit=!0;var a=this._callbacks;if(null!==a)for(var b=0;b<a.length;b++){var c=a[b];\"function\"!==typeof c?t(\"191\",c):void 0;c()}}};\nfunction Xh(a,b,c){b=M(3,null,null,b?3:0);a={current:b,containerInfo:a,pendingChildren:null,earliestPendingTime:0,latestPendingTime:0,earliestSuspendedTime:0,latestSuspendedTime:0,latestPingedTime:0,didError:!1,pendingCommitExpirationTime:0,finishedWork:null,timeoutHandle:-1,context:null,pendingContext:null,hydrate:c,nextExpirationTimeToWorkOn:0,expirationTime:0,firstBatch:null,nextScheduledRoot:null};this._internalRoot=b.stateNode=a}\nXh.prototype.render=function(a,b){var c=this._internalRoot,d=new Wh;b=void 0===b?null:b;null!==b&&d.then(b);Rh(a,c,null,d._onCommit);return d};Xh.prototype.unmount=function(a){var b=this._internalRoot,c=new Wh;a=void 0===a?null:a;null!==a&&c.then(a);Rh(null,b,null,c._onCommit);return c};Xh.prototype.legacy_renderSubtreeIntoContainer=function(a,b,c){var d=this._internalRoot,e=new Wh;c=void 0===c?null:c;null!==c&&e.then(c);Rh(b,d,a,e._onCommit);return e};\nXh.prototype.createBatch=function(){var a=new Vh(this),b=a._expirationTime,c=this._internalRoot,d=c.firstBatch;if(null===d)c.firstBatch=a,a._next=null;else{for(c=null;null!==d&&d._expirationTime>=b;)c=d,d=d._next;a._next=d;null!==c&&(c._next=a)}return a};function Yh(a){return!(!a||1!==a.nodeType&&9!==a.nodeType&&11!==a.nodeType&&(8!==a.nodeType||\" react-mount-point-unstable \"!==a.nodeValue))}Kb=Nh;Lb=Ph;Mb=function(){W||0===lh||(Kh(lh,!1),lh=0)};\nfunction Zh(a,b){b||(b=a?9===a.nodeType?a.documentElement:a.firstChild:null,b=!(!b||1!==b.nodeType||!b.hasAttribute(\"data-reactroot\")));if(!b)for(var c;c=a.lastChild;)a.removeChild(c);return new Xh(a,!1,b)}\nfunction $h(a,b,c,d,e){Yh(c)?void 0:t(\"200\");var f=c._reactRootContainer;if(f){if(\"function\"===typeof e){var g=e;e=function(){var a=Sh(f._internalRoot);g.call(a)}}null!=a?f.legacy_renderSubtreeIntoContainer(a,b,e):f.render(b,e)}else{f=c._reactRootContainer=Zh(c,d);if(\"function\"===typeof e){var h=e;e=function(){var a=Sh(f._internalRoot);h.call(a)}}Oh(function(){null!=a?f.legacy_renderSubtreeIntoContainer(a,b,e):f.render(b,e)})}return Sh(f._internalRoot)}\nfunction ai(a,b){var c=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;Yh(b)?void 0:t(\"200\");return Uh(a,b,null,c)}\nvar bi={createPortal:ai,findDOMNode:function(a){if(null==a)return null;if(1===a.nodeType)return a;var b=a._reactInternalFiber;void 0===b&&(\"function\"===typeof a.render?t(\"188\"):t(\"268\",Object.keys(a)));a=nd(b);a=null===a?null:a.stateNode;return a},hydrate:function(a,b,c){return $h(null,a,b,!0,c)},render:function(a,b,c){return $h(null,a,b,!1,c)},unstable_renderSubtreeIntoContainer:function(a,b,c,d){null==a||void 0===a._reactInternalFiber?t(\"38\"):void 0;return $h(a,b,c,!1,d)},unmountComponentAtNode:function(a){Yh(a)?\nvoid 0:t(\"40\");return a._reactRootContainer?(Oh(function(){$h(null,null,a,!1,function(){a._reactRootContainer=null})}),!0):!1},unstable_createPortal:function(){return ai.apply(void 0,arguments)},unstable_batchedUpdates:Nh,unstable_interactiveUpdates:Ph,flushSync:function(a,b){W?t(\"187\"):void 0;var c=Z;Z=!0;try{return qh(a,b)}finally{Z=c,Kh(1073741823,!1)}},unstable_flushControlled:function(a){var b=Z;Z=!0;try{qh(a)}finally{(Z=b)||W||Kh(1073741823,!1)}},__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:{Events:[Ja,\nKa,La,Ca.injectEventPluginsByName,qa,Ra,function(a){za(a,Qa)},Ib,Jb,Jd,Ea]},unstable_createRoot:function(a,b){Yh(a)?void 0:t(\"299\",\"unstable_createRoot\");return new Xh(a,!0,null!=b&&!0===b.hydrate)}};(function(a){var b=a.findFiberByHostInstance;return Ve(n({},a,{findHostInstanceByFiber:function(a){a=nd(a);return null===a?null:a.stateNode},findFiberByHostInstance:function(a){return b?b(a):null}}))})({findFiberByHostInstance:Ia,bundleType:0,version:\"16.6.3\",rendererPackageName:\"react-dom\"});\nvar ei={default:bi},fi=ei&&bi||ei;module.exports=fi.default||fi;\n", "/** @license React v0.11.3\n * scheduler.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';Object.defineProperty(exports,\"__esModule\",{value:!0});var d=null,f=!1,h=3,k=-1,l=-1,m=!1,n=!1;function p(){if(!m){var a=d.expirationTime;n?q():n=!0;r(t,a)}}\nfunction u(){var a=d,b=d.next;if(d===b)d=null;else{var c=d.previous;d=c.next=b;b.previous=c}a.next=a.previous=null;c=a.callback;b=a.expirationTime;a=a.priorityLevel;var e=h,Q=l;h=a;l=b;try{var g=c()}finally{h=e,l=Q}if(\"function\"===typeof g)if(g={callback:g,priorityLevel:a,expirationTime:b,next:null,previous:null},null===d)d=g.next=g.previous=g;else{c=null;a=d;do{if(a.expirationTime>=b){c=a;break}a=a.next}while(a!==d);null===c?c=d:c===d&&(d=g,p());b=c.previous;b.next=c.previous=g;g.next=c;g.previous=\nb}}function v(){if(-1===k&&null!==d&&1===d.priorityLevel){m=!0;try{do u();while(null!==d&&1===d.priorityLevel)}finally{m=!1,null!==d?p():n=!1}}}function t(a){m=!0;var b=f;f=a;try{if(a)for(;null!==d;){var c=exports.unstable_now();if(d.expirationTime<=c){do u();while(null!==d&&d.expirationTime<=c)}else break}else if(null!==d){do u();while(null!==d&&!w())}}finally{m=!1,f=b,null!==d?p():n=!1,v()}}\nvar x=Date,y=\"function\"===typeof setTimeout?setTimeout:void 0,z=\"function\"===typeof clearTimeout?clearTimeout:void 0,A=\"function\"===typeof requestAnimationFrame?requestAnimationFrame:void 0,B=\"function\"===typeof cancelAnimationFrame?cancelAnimationFrame:void 0,C,D;function E(a){C=A(function(b){z(D);a(b)});D=y(function(){B(C);a(exports.unstable_now())},100)}\nif(\"object\"===typeof performance&&\"function\"===typeof performance.now){var F=performance;exports.unstable_now=function(){return F.now()}}else exports.unstable_now=function(){return x.now()};var r,q,w,G=null;\"undefined\"!==typeof window?G=window:\"undefined\"!==typeof global&&(G=global);\nif(G&&G._schedMock){var H=G._schedMock;r=H[0];q=H[1];w=H[2];exports.unstable_now=H[3]}else if(\"undefined\"===typeof window||\"function\"!==typeof MessageChannel){var I=null,J=function(a){if(null!==I)try{I(a)}finally{I=null}};r=function(a){null!==I?setTimeout(r,0,a):(I=a,setTimeout(J,0,!1))};q=function(){I=null};w=function(){return!1}}else{\"undefined\"!==typeof console&&(\"function\"!==typeof A&&console.error(\"This browser doesn't support requestAnimationFrame. Make sure that you load a polyfill in older browsers. https://fb.me/react-polyfills\"),\n\"function\"!==typeof B&&console.error(\"This browser doesn't support cancelAnimationFrame. Make sure that you load a polyfill in older browsers. https://fb.me/react-polyfills\"));var K=null,L=!1,M=-1,N=!1,O=!1,P=0,R=33,S=33;w=function(){return P<=exports.unstable_now()};var T=new MessageChannel,U=T.port2;T.port1.onmessage=function(){L=!1;var a=K,b=M;K=null;M=-1;var c=exports.unstable_now(),e=!1;if(0>=P-c)if(-1!==b&&b<=c)e=!0;else{N||(N=!0,E(V));K=a;M=b;return}if(null!==a){O=!0;try{a(e)}finally{O=!1}}};\nvar V=function(a){if(null!==K){E(V);var b=a-P+S;b<S&&R<S?(8>b&&(b=8),S=b<R?R:b):R=b;P=a+S;L||(L=!0,U.postMessage(void 0))}else N=!1};r=function(a,b){K=a;M=b;O||0>b?U.postMessage(void 0):N||(N=!0,E(V))};q=function(){K=null;L=!1;M=-1}}exports.unstable_ImmediatePriority=1;exports.unstable_UserBlockingPriority=2;exports.unstable_NormalPriority=3;exports.unstable_IdlePriority=5;exports.unstable_LowPriority=4;\nexports.unstable_runWithPriority=function(a,b){switch(a){case 1:case 2:case 3:case 4:case 5:break;default:a=3}var c=h,e=k;h=a;k=exports.unstable_now();try{return b()}finally{h=c,k=e,v()}};\nexports.unstable_scheduleCallback=function(a,b){var c=-1!==k?k:exports.unstable_now();if(\"object\"===typeof b&&null!==b&&\"number\"===typeof b.timeout)b=c+b.timeout;else switch(h){case 1:b=c+-1;break;case 2:b=c+250;break;case 5:b=c+1073741823;break;case 4:b=c+1E4;break;default:b=c+5E3}a={callback:a,priorityLevel:h,expirationTime:b,next:null,previous:null};if(null===d)d=a.next=a.previous=a,p();else{c=null;var e=d;do{if(e.expirationTime>b){c=e;break}e=e.next}while(e!==d);null===c?c=d:c===d&&(d=a,p());\nb=c.previous;b.next=c.previous=a;a.next=c;a.previous=b}return a};exports.unstable_cancelCallback=function(a){var b=a.next;if(null!==b){if(b===a)d=null;else{a===d&&(d=b);var c=a.previous;c.next=b;b.previous=c}a.next=a.previous=null}};exports.unstable_wrapCallback=function(a){var b=h;return function(){var c=h,e=k;h=b;k=exports.unstable_now();try{return a.apply(this,arguments)}finally{h=c,k=e,v()}}};exports.unstable_getCurrentPriorityLevel=function(){return h};\nexports.unstable_shouldYield=function(){return!f&&(null!==d&&d.expirationTime<l||w())};\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)\r\n            t[p[i]] = s[p[i]];\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n", "/*!\n * is-plain-object <https://github.com/jonschlinkert/is-plain-object>\n *\n * Copyright (c) 2014-2017, <PERSON>.\n * Released under the MIT License.\n */\n\n'use strict';\n\nvar isObject = require('isobject');\n\nfunction isObjectObject(o) {\n  return isObject(o) === true\n    && Object.prototype.toString.call(o) === '[object Object]';\n}\n\nmodule.exports = function isPlainObject(o) {\n  var ctor,prot;\n\n  if (isObjectObject(o) === false) return false;\n\n  // If has modified constructor\n  ctor = o.constructor;\n  if (typeof ctor !== 'function') return false;\n\n  // If has modified prototype\n  prot = ctor.prototype;\n  if (isObjectObject(prot) === false) return false;\n\n  // If constructor does not have an Object-specific method\n  if (prot.hasOwnProperty('isPrototypeOf') === false) {\n    return false;\n  }\n\n  // Most likely a plain Object\n  return true;\n};\n", "/*!\n * isobject <https://github.com/jonschlinkert/isobject>\n *\n * Copyright (c) 2014-2017, <PERSON>.\n * Released under the MIT License.\n */\n\n'use strict';\n\nmodule.exports = function isObject(val) {\n  return val != null && typeof val === 'object' && Array.isArray(val) === false;\n};\n", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.supportedValue = exports.supportedProperty = exports.prefix = undefined;\n\nvar _prefix = require('./prefix');\n\nvar _prefix2 = _interopRequireDefault(_prefix);\n\nvar _supportedProperty = require('./supported-property');\n\nvar _supportedProperty2 = _interopRequireDefault(_supportedProperty);\n\nvar _supportedValue = require('./supported-value');\n\nvar _supportedValue2 = _interopRequireDefault(_supportedValue);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }\n\nexports['default'] = {\n  prefix: _prefix2['default'],\n  supportedProperty: _supportedProperty2['default'],\n  supportedValue: _supportedValue2['default']\n}; /**\n    * CSS Vendor prefix detection and property feature testing.\n    *\n    * @copyright Oleg Slobodskoi 2015\n    * @website https://github.com/jsstyles/css-vendor\n    * @license MIT\n    */\n\nexports.prefix = _prefix2['default'];\nexports.supportedProperty = _supportedProperty2['default'];\nexports.supportedValue = _supportedValue2['default'];", "/** @license React v16.6.1\n * react-is.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';Object.defineProperty(exports,\"__esModule\",{value:!0});\nvar b=\"function\"===typeof Symbol&&Symbol.for,c=b?Symbol.for(\"react.element\"):60103,d=b?Symbol.for(\"react.portal\"):60106,e=b?Symbol.for(\"react.fragment\"):60107,f=b?Symbol.for(\"react.strict_mode\"):60108,g=b?Symbol.for(\"react.profiler\"):60114,h=b?Symbol.for(\"react.provider\"):60109,k=b?Symbol.for(\"react.context\"):60110,l=b?Symbol.for(\"react.async_mode\"):60111,m=b?Symbol.for(\"react.concurrent_mode\"):60111,n=b?Symbol.for(\"react.forward_ref\"):60112,p=b?Symbol.for(\"react.suspense\"):60113,r=b?Symbol.for(\"react.memo\"):\n60115,t=b?Symbol.for(\"react.lazy\"):60116;function u(a){if(\"object\"===typeof a&&null!==a){var q=a.$$typeof;switch(q){case c:switch(a=a.type,a){case l:case m:case e:case g:case f:return a;default:switch(a=a&&a.$$typeof,a){case k:case n:case h:return a;default:return q}}case d:return q}}}function v(a){return u(a)===m}exports.typeOf=u;exports.AsyncMode=l;exports.ConcurrentMode=m;exports.ContextConsumer=k;exports.ContextProvider=h;exports.Element=c;exports.ForwardRef=n;exports.Fragment=e;\nexports.Profiler=g;exports.Portal=d;exports.StrictMode=f;exports.isValidElementType=function(a){return\"string\"===typeof a||\"function\"===typeof a||a===e||a===m||a===g||a===f||a===p||\"object\"===typeof a&&null!==a&&(a.$$typeof===t||a.$$typeof===r||a.$$typeof===h||a.$$typeof===k||a.$$typeof===n)};exports.isAsyncMode=function(a){return v(a)||u(a)===l};exports.isConcurrentMode=v;exports.isContextConsumer=function(a){return u(a)===k};exports.isContextProvider=function(a){return u(a)===h};\nexports.isElement=function(a){return\"object\"===typeof a&&null!==a&&a.$$typeof===c};exports.isForwardRef=function(a){return u(a)===n};exports.isFragment=function(a){return u(a)===e};exports.isProfiler=function(a){return u(a)===g};exports.isPortal=function(a){return u(a)===d};exports.isStrictMode=function(a){return u(a)===f};\n", "/**!\n * @fileOverview Kickass library to create and place poppers near their reference elements.\n * @version 1.14.6\n * @license\n * Copyright (c) 2016 <PERSON> and contributors\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined';\n\nvar longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\nvar timeoutDuration = 0;\nfor (var i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n  if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n    timeoutDuration = 1;\n    break;\n  }\n}\n\nfunction microtaskDebounce(fn) {\n  var called = false;\n  return function () {\n    if (called) {\n      return;\n    }\n    called = true;\n    window.Promise.resolve().then(function () {\n      called = false;\n      fn();\n    });\n  };\n}\n\nfunction taskDebounce(fn) {\n  var scheduled = false;\n  return function () {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(function () {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\n\nvar supportsMicroTasks = isBrowser && window.Promise;\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nvar debounce = supportsMicroTasks ? microtaskDebounce : taskDebounce;\n\n/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {Any} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nfunction isFunction(functionToCheck) {\n  var getType = {};\n  return functionToCheck && getType.toString.call(functionToCheck) === '[object Function]';\n}\n\n/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nfunction getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  var window = element.ownerDocument.defaultView;\n  var css = window.getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n\n/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nfunction getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nfunction getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element) {\n    return document.body;\n  }\n\n  switch (element.nodeName) {\n    case 'HTML':\n    case 'BODY':\n      return element.ownerDocument.body;\n    case '#document':\n      return element.body;\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n\n  var _getStyleComputedProp = getStyleComputedProperty(element),\n      overflow = _getStyleComputedProp.overflow,\n      overflowX = _getStyleComputedProp.overflowX,\n      overflowY = _getStyleComputedProp.overflowY;\n\n  if (/(auto|scroll|overlay)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n\n  return getScrollParent(getParentNode(element));\n}\n\nvar isIE11 = isBrowser && !!(window.MSInputMethodContext && document.documentMode);\nvar isIE10 = isBrowser && /MSIE 10/.test(navigator.userAgent);\n\n/**\n * Determines if the browser is Internet Explorer\n * @method\n * @memberof Popper.Utils\n * @param {Number} version to check\n * @returns {Boolean} isIE\n */\nfunction isIE(version) {\n  if (version === 11) {\n    return isIE11;\n  }\n  if (version === 10) {\n    return isIE10;\n  }\n  return isIE11 || isIE10;\n}\n\n/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nfunction getOffsetParent(element) {\n  if (!element) {\n    return document.documentElement;\n  }\n\n  var noOffsetParent = isIE(10) ? document.body : null;\n\n  // NOTE: 1 DOM access here\n  var offsetParent = element.offsetParent || null;\n  // Skip hidden elements which don't have an offsetParent\n  while (offsetParent === noOffsetParent && element.nextElementSibling) {\n    offsetParent = (element = element.nextElementSibling).offsetParent;\n  }\n\n  var nodeName = offsetParent && offsetParent.nodeName;\n\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    return element ? element.ownerDocument.documentElement : document.documentElement;\n  }\n\n  // .offsetParent will return the closest TH, TD or TABLE in case\n  // no offsetParent is present, I hate this job...\n  if (['TH', 'TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 && getStyleComputedProperty(offsetParent, 'position') === 'static') {\n    return getOffsetParent(offsetParent);\n  }\n\n  return offsetParent;\n}\n\nfunction isOffsetContainer(element) {\n  var nodeName = element.nodeName;\n\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element;\n}\n\n/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nfunction getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n\n  return node;\n}\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nfunction findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  var order = element1.compareDocumentPosition(element2) & Node.DOCUMENT_POSITION_FOLLOWING;\n  var start = order ? element1 : element2;\n  var end = order ? element2 : element1;\n\n  // Get common ancestor container\n  var range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  var commonAncestorContainer = range.commonAncestorContainer;\n\n  // Both nodes are inside #document\n\n  if (element1 !== commonAncestorContainer && element2 !== commonAncestorContainer || start.contains(end)) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  var element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n\n/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {number} amount of scrolled pixels\n */\nfunction getScroll(element) {\n  var side = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'top';\n\n  var upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  var nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    var html = element.ownerDocument.documentElement;\n    var scrollingElement = element.ownerDocument.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n\n  return element[upperSide];\n}\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nfunction includeScroll(rect, element) {\n  var subtract = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n  var scrollTop = getScroll(element, 'top');\n  var scrollLeft = getScroll(element, 'left');\n  var modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n\n/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles\n * Result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {number} borders - The borders size of the given axis\n */\n\nfunction getBordersSize(styles, axis) {\n  var sideA = axis === 'x' ? 'Left' : 'Top';\n  var sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n\n  return parseFloat(styles['border' + sideA + 'Width'], 10) + parseFloat(styles['border' + sideB + 'Width'], 10);\n}\n\nfunction getSize(axis, body, html, computedStyle) {\n  return Math.max(body['offset' + axis], body['scroll' + axis], html['client' + axis], html['offset' + axis], html['scroll' + axis], isIE(10) ? parseInt(html['offset' + axis]) + parseInt(computedStyle['margin' + (axis === 'Height' ? 'Top' : 'Left')]) + parseInt(computedStyle['margin' + (axis === 'Height' ? 'Bottom' : 'Right')]) : 0);\n}\n\nfunction getWindowSizes(document) {\n  var body = document.body;\n  var html = document.documentElement;\n  var computedStyle = isIE(10) && getComputedStyle(html);\n\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle)\n  };\n}\n\nvar classCallCheck = function (instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n};\n\nvar createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\n\n\n\n\n\nvar defineProperty = function (obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n};\n\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n\n  return target;\n};\n\n/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nfunction getClientRect(offsets) {\n  return _extends({}, offsets, {\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height\n  });\n}\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nfunction getBoundingClientRect(element) {\n  var rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  try {\n    if (isIE(10)) {\n      rect = element.getBoundingClientRect();\n      var scrollTop = getScroll(element, 'top');\n      var scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    } else {\n      rect = element.getBoundingClientRect();\n    }\n  } catch (e) {}\n\n  var result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top\n  };\n\n  // subtract scrollbar size from sizes\n  var sizes = element.nodeName === 'HTML' ? getWindowSizes(element.ownerDocument) : {};\n  var width = sizes.width || element.clientWidth || result.right - result.left;\n  var height = sizes.height || element.clientHeight || result.bottom - result.top;\n\n  var horizScrollbar = element.offsetWidth - width;\n  var vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    var styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n\n  return getClientRect(result);\n}\n\nfunction getOffsetRectRelativeToArbitraryNode(children, parent) {\n  var fixedPosition = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n  var isIE10 = isIE(10);\n  var isHTML = parent.nodeName === 'HTML';\n  var childrenRect = getBoundingClientRect(children);\n  var parentRect = getBoundingClientRect(parent);\n  var scrollParent = getScrollParent(children);\n\n  var styles = getStyleComputedProperty(parent);\n  var borderTopWidth = parseFloat(styles.borderTopWidth, 10);\n  var borderLeftWidth = parseFloat(styles.borderLeftWidth, 10);\n\n  // In cases where the parent is fixed, we must ignore negative scroll in offset calc\n  if (fixedPosition && isHTML) {\n    parentRect.top = Math.max(parentRect.top, 0);\n    parentRect.left = Math.max(parentRect.left, 0);\n  }\n  var offsets = getClientRect({\n    top: childrenRect.top - parentRect.top - borderTopWidth,\n    left: childrenRect.left - parentRect.left - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (!isIE10 && isHTML) {\n    var marginTop = parseFloat(styles.marginTop, 10);\n    var marginLeft = parseFloat(styles.marginLeft, 10);\n\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n\n  if (isIE10 && !fixedPosition ? parent.contains(scrollParent) : parent === scrollParent && scrollParent.nodeName !== 'BODY') {\n    offsets = includeScroll(offsets, parent);\n  }\n\n  return offsets;\n}\n\nfunction getViewportOffsetRectRelativeToArtbitraryNode(element) {\n  var excludeScroll = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var html = element.ownerDocument.documentElement;\n  var relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  var width = Math.max(html.clientWidth, window.innerWidth || 0);\n  var height = Math.max(html.clientHeight, window.innerHeight || 0);\n\n  var scrollTop = !excludeScroll ? getScroll(html) : 0;\n  var scrollLeft = !excludeScroll ? getScroll(html, 'left') : 0;\n\n  var offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width: width,\n    height: height\n  };\n\n  return getClientRect(offset);\n}\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {Boolean} answer to \"isFixed?\"\n */\nfunction isFixed(element) {\n  var nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  return isFixed(getParentNode(element));\n}\n\n/**\n * Finds the first parent of an element that has a transformed property defined\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} first transformed parent or documentElement\n */\n\nfunction getFixedPositionOffsetParent(element) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element || !element.parentElement || isIE()) {\n    return document.documentElement;\n  }\n  var el = element.parentElement;\n  while (el && getStyleComputedProperty(el, 'transform') === 'none') {\n    el = el.parentElement;\n  }\n  return el || document.documentElement;\n}\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} popper\n * @param {HTMLElement} reference\n * @param {number} padding\n * @param {HTMLElement} boundariesElement - Element used to define the boundaries\n * @param {Boolean} fixedPosition - Is in fixed position mode\n * @returns {Object} Coordinates of the boundaries\n */\nfunction getBoundaries(popper, reference, padding, boundariesElement) {\n  var fixedPosition = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n\n  // NOTE: 1 DOM access here\n\n  var boundaries = { top: 0, left: 0 };\n  var offsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, reference);\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport') {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent, fixedPosition);\n  } else {\n    // Handle other cases based on DOM element used as boundaries\n    var boundariesNode = void 0;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(reference));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = popper.ownerDocument.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = popper.ownerDocument.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n\n    var offsets = getOffsetRectRelativeToArbitraryNode(boundariesNode, offsetParent, fixedPosition);\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      var _getWindowSizes = getWindowSizes(popper.ownerDocument),\n          height = _getWindowSizes.height,\n          width = _getWindowSizes.width;\n\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  padding = padding || 0;\n  var isPaddingNumber = typeof padding === 'number';\n  boundaries.left += isPaddingNumber ? padding : padding.left || 0;\n  boundaries.top += isPaddingNumber ? padding : padding.top || 0;\n  boundaries.right -= isPaddingNumber ? padding : padding.right || 0;\n  boundaries.bottom -= isPaddingNumber ? padding : padding.bottom || 0;\n\n  return boundaries;\n}\n\nfunction getArea(_ref) {\n  var width = _ref.width,\n      height = _ref.height;\n\n  return width * height;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeAutoPlacement(placement, refRect, popper, reference, boundariesElement) {\n  var padding = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 0;\n\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n\n  var boundaries = getBoundaries(popper, reference, padding, boundariesElement);\n\n  var rects = {\n    top: {\n      width: boundaries.width,\n      height: refRect.top - boundaries.top\n    },\n    right: {\n      width: boundaries.right - refRect.right,\n      height: boundaries.height\n    },\n    bottom: {\n      width: boundaries.width,\n      height: boundaries.bottom - refRect.bottom\n    },\n    left: {\n      width: refRect.left - boundaries.left,\n      height: boundaries.height\n    }\n  };\n\n  var sortedAreas = Object.keys(rects).map(function (key) {\n    return _extends({\n      key: key\n    }, rects[key], {\n      area: getArea(rects[key])\n    });\n  }).sort(function (a, b) {\n    return b.area - a.area;\n  });\n\n  var filteredAreas = sortedAreas.filter(function (_ref2) {\n    var width = _ref2.width,\n        height = _ref2.height;\n    return width >= popper.clientWidth && height >= popper.clientHeight;\n  });\n\n  var computedPlacement = filteredAreas.length > 0 ? filteredAreas[0].key : sortedAreas[0].key;\n\n  var variation = placement.split('-')[1];\n\n  return computedPlacement + (variation ? '-' + variation : '');\n}\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @param {Element} fixedPosition - is in fixed position mode\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nfunction getReferenceOffsets(state, popper, reference) {\n  var fixedPosition = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n\n  var commonOffsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, reference);\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent, fixedPosition);\n}\n\n/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nfunction getOuterSizes(element) {\n  var window = element.ownerDocument.defaultView;\n  var styles = window.getComputedStyle(element);\n  var x = parseFloat(styles.marginTop || 0) + parseFloat(styles.marginBottom || 0);\n  var y = parseFloat(styles.marginLeft || 0) + parseFloat(styles.marginRight || 0);\n  var result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x\n  };\n  return result;\n}\n\n/**\n * Get the opposite placement of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nfunction getOppositePlacement(placement) {\n  var hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nfunction getPopperOffsets(popper, referenceOffsets, placement) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  var popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  var popperOffsets = {\n    width: popperRect.width,\n    height: popperRect.height\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  var isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  var mainSide = isHoriz ? 'top' : 'left';\n  var secondarySide = isHoriz ? 'left' : 'top';\n  var measurement = isHoriz ? 'height' : 'width';\n  var secondaryMeasurement = !isHoriz ? 'height' : 'width';\n\n  popperOffsets[mainSide] = referenceOffsets[mainSide] + referenceOffsets[measurement] / 2 - popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] = referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] = referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n\n  return popperOffsets;\n}\n\n/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(function (cur) {\n      return cur[prop] === value;\n    });\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  var match = find(arr, function (obj) {\n    return obj[prop] === value;\n  });\n  return arr.indexOf(match);\n}\n\n/**\n * Loop trough the list of modifiers and run them in order,\n * each of them will then edit the data object.\n * @method\n * @memberof Popper.Utils\n * @param {dataObject} data\n * @param {Array} modifiers\n * @param {String} ends - Optional modifier name used as stopper\n * @returns {dataObject}\n */\nfunction runModifiers(modifiers, data, ends) {\n  var modifiersToRun = ends === undefined ? modifiers : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n\n  modifiersToRun.forEach(function (modifier) {\n    if (modifier['function']) {\n      // eslint-disable-line dot-notation\n      console.warn('`modifier.function` is deprecated, use `modifier.fn`!');\n    }\n    var fn = modifier['function'] || modifier.fn; // eslint-disable-line dot-notation\n    if (modifier.enabled && isFunction(fn)) {\n      // Add properties to offsets to make them a complete clientRect object\n      // we do this before each modifier to make sure the previous one doesn't\n      // mess with these values\n      data.offsets.popper = getClientRect(data.offsets.popper);\n      data.offsets.reference = getClientRect(data.offsets.reference);\n\n      data = fn(data, modifier);\n    }\n  });\n\n  return data;\n}\n\n/**\n * Updates the position of the popper, computing the new offsets and applying\n * the new style.<br />\n * Prefer `scheduleUpdate` over `update` because of performance reasons.\n * @method\n * @memberof Popper\n */\nfunction update() {\n  // if popper is destroyed, don't perform any further update\n  if (this.state.isDestroyed) {\n    return;\n  }\n\n  var data = {\n    instance: this,\n    styles: {},\n    arrowStyles: {},\n    attributes: {},\n    flipped: false,\n    offsets: {}\n  };\n\n  // compute reference element offsets\n  data.offsets.reference = getReferenceOffsets(this.state, this.popper, this.reference, this.options.positionFixed);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  data.placement = computeAutoPlacement(this.options.placement, data.offsets.reference, this.popper, this.reference, this.options.modifiers.flip.boundariesElement, this.options.modifiers.flip.padding);\n\n  // store the computed placement inside `originalPlacement`\n  data.originalPlacement = data.placement;\n\n  data.positionFixed = this.options.positionFixed;\n\n  // compute the popper offsets\n  data.offsets.popper = getPopperOffsets(this.popper, data.offsets.reference, data.placement);\n\n  data.offsets.popper.position = this.options.positionFixed ? 'fixed' : 'absolute';\n\n  // run the modifiers\n  data = runModifiers(this.modifiers, data);\n\n  // the first `update` will call `onCreate` callback\n  // the other ones will call `onUpdate` callback\n  if (!this.state.isCreated) {\n    this.state.isCreated = true;\n    this.options.onCreate(data);\n  } else {\n    this.options.onUpdate(data);\n  }\n}\n\n/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nfunction isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(function (_ref) {\n    var name = _ref.name,\n        enabled = _ref.enabled;\n    return enabled && name === modifierName;\n  });\n}\n\n/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase or PascalCase, depending on the vendor prefix)\n */\nfunction getSupportedPropertyName(property) {\n  var prefixes = [false, 'ms', 'Webkit', 'Moz', 'O'];\n  var upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n\n  for (var i = 0; i < prefixes.length; i++) {\n    var prefix = prefixes[i];\n    var toCheck = prefix ? '' + prefix + upperProp : property;\n    if (typeof document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n\n/**\n * Destroys the popper.\n * @method\n * @memberof Popper\n */\nfunction destroy() {\n  this.state.isDestroyed = true;\n\n  // touch DOM only if `applyStyle` modifier is enabled\n  if (isModifierEnabled(this.modifiers, 'applyStyle')) {\n    this.popper.removeAttribute('x-placement');\n    this.popper.style.position = '';\n    this.popper.style.top = '';\n    this.popper.style.left = '';\n    this.popper.style.right = '';\n    this.popper.style.bottom = '';\n    this.popper.style.willChange = '';\n    this.popper.style[getSupportedPropertyName('transform')] = '';\n  }\n\n  this.disableEventListeners();\n\n  // remove the popper if user explicity asked for the deletion on destroy\n  // do not use `remove` because IE11 doesn't support it\n  if (this.options.removeOnDestroy) {\n    this.popper.parentNode.removeChild(this.popper);\n  }\n  return this;\n}\n\n/**\n * Get the window associated with the element\n * @argument {Element} element\n * @returns {Window}\n */\nfunction getWindow(element) {\n  var ownerDocument = element.ownerDocument;\n  return ownerDocument ? ownerDocument.defaultView : window;\n}\n\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  var isBody = scrollParent.nodeName === 'BODY';\n  var target = isBody ? scrollParent.ownerDocument.defaultView : scrollParent;\n  target.addEventListener(event, callback, { passive: true });\n\n  if (!isBody) {\n    attachToScrollParents(getScrollParent(target.parentNode), event, callback, scrollParents);\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction setupEventListeners(reference, options, state, updateBound) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  getWindow(reference).addEventListener('resize', state.updateBound, { passive: true });\n\n  // Scroll event listener on scroll parents\n  var scrollElement = getScrollParent(reference);\n  attachToScrollParents(scrollElement, 'scroll', state.updateBound, state.scrollParents);\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n\n  return state;\n}\n\n/**\n * It will add resize/scroll events and start recalculating\n * position of the popper element when they are triggered.\n * @method\n * @memberof Popper\n */\nfunction enableEventListeners() {\n  if (!this.state.eventsEnabled) {\n    this.state = setupEventListeners(this.reference, this.options, this.state, this.scheduleUpdate);\n  }\n}\n\n/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  getWindow(reference).removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(function (target) {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n\n/**\n * It will remove resize/scroll events and won't recalculate popper position\n * when they are triggered. It also won't trigger `onUpdate` callback anymore,\n * unless you call `update` method manually.\n * @method\n * @memberof Popper\n */\nfunction disableEventListeners() {\n  if (this.state.eventsEnabled) {\n    cancelAnimationFrame(this.scheduleUpdate);\n    this.state = removeEventListeners(this.reference, this.state);\n  }\n}\n\n/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nfunction isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setStyles(element, styles) {\n  Object.keys(styles).forEach(function (prop) {\n    var unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !== -1 && isNumeric(styles[prop])) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n\n/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function (prop) {\n    var value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} data.styles - List of style properties - values to apply to popper element\n * @argument {Object} data.attributes - List of attribute properties - values to apply to popper element\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The same data object\n */\nfunction applyStyle(data) {\n  // any property present in `data.styles` will be applied to the popper,\n  // in this way we can make the 3rd party modifiers add custom styles to it\n  // Be aware, modifiers could override the properties defined in the previous\n  // lines of this modifier!\n  setStyles(data.instance.popper, data.styles);\n\n  // any property present in `data.attributes` will be applied to the popper,\n  // they will be set as HTML attributes of the element\n  setAttributes(data.instance.popper, data.attributes);\n\n  // if arrowElement is defined and arrowStyles has some properties\n  if (data.arrowElement && Object.keys(data.arrowStyles).length) {\n    setStyles(data.arrowElement, data.arrowStyles);\n  }\n\n  return data;\n}\n\n/**\n * Set the x-placement attribute before everything else because it could be used\n * to add margins to the popper margins needs to be calculated to get the\n * correct popper offsets.\n * @method\n * @memberof Popper.modifiers\n * @param {HTMLElement} reference - The reference element used to position the popper\n * @param {HTMLElement} popper - The HTML element used as popper\n * @param {Object} options - Popper.js options\n */\nfunction applyStyleOnLoad(reference, popper, options, modifierOptions, state) {\n  // compute reference element offsets\n  var referenceOffsets = getReferenceOffsets(state, popper, reference, options.positionFixed);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  var placement = computeAutoPlacement(options.placement, referenceOffsets, popper, reference, options.modifiers.flip.boundariesElement, options.modifiers.flip.padding);\n\n  popper.setAttribute('x-placement', placement);\n\n  // Apply `position` to popper before anything else because\n  // without the position applied we can't guarantee correct computations\n  setStyles(popper, { position: options.positionFixed ? 'fixed' : 'absolute' });\n\n  return options;\n}\n\n/**\n * @function\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Boolean} shouldRound - If the offsets should be rounded at all\n * @returns {Object} The popper's position offsets rounded\n *\n * The tale of pixel-perfect positioning. It's still not 100% perfect, but as\n * good as it can be within reason.\n * Discussion here: https://github.com/FezVrasta/popper.js/pull/715\n *\n * Low DPI screens cause a popper to be blurry if not using full pixels (Safari\n * as well on High DPI screens).\n *\n * Firefox prefers no rounding for positioning and does not have blurriness on\n * high DPI screens.\n *\n * Only horizontal placement and left/right values need to be considered.\n */\nfunction getRoundedOffsets(data, shouldRound) {\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n\n  var isVertical = ['left', 'right'].indexOf(data.placement) !== -1;\n  var isVariation = data.placement.indexOf('-') !== -1;\n  var sameWidthOddness = reference.width % 2 === popper.width % 2;\n  var bothOddWidth = reference.width % 2 === 1 && popper.width % 2 === 1;\n  var noRound = function noRound(v) {\n    return v;\n  };\n\n  var horizontalToInteger = !shouldRound ? noRound : isVertical || isVariation || sameWidthOddness ? Math.round : Math.floor;\n  var verticalToInteger = !shouldRound ? noRound : Math.round;\n\n  return {\n    left: horizontalToInteger(bothOddWidth && !isVariation && shouldRound ? popper.left - 1 : popper.left),\n    top: verticalToInteger(popper.top),\n    bottom: verticalToInteger(popper.bottom),\n    right: horizontalToInteger(popper.right)\n  };\n}\n\nvar isFirefox = isBrowser && /Firefox/i.test(navigator.userAgent);\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeStyle(data, options) {\n  var x = options.x,\n      y = options.y;\n  var popper = data.offsets.popper;\n\n  // Remove this legacy support in Popper.js v2\n\n  var legacyGpuAccelerationOption = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'applyStyle';\n  }).gpuAcceleration;\n  if (legacyGpuAccelerationOption !== undefined) {\n    console.warn('WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!');\n  }\n  var gpuAcceleration = legacyGpuAccelerationOption !== undefined ? legacyGpuAccelerationOption : options.gpuAcceleration;\n\n  var offsetParent = getOffsetParent(data.instance.popper);\n  var offsetParentRect = getBoundingClientRect(offsetParent);\n\n  // Styles\n  var styles = {\n    position: popper.position\n  };\n\n  var offsets = getRoundedOffsets(data, window.devicePixelRatio < 2 || !isFirefox);\n\n  var sideA = x === 'bottom' ? 'top' : 'bottom';\n  var sideB = y === 'right' ? 'left' : 'right';\n\n  // if gpuAcceleration is set to `true` and transform is supported,\n  //  we use `translate3d` to apply the position to the popper we\n  // automatically use the supported prefixed version if needed\n  var prefixedProperty = getSupportedPropertyName('transform');\n\n  // now, let's make a step back and look at this code closely (wtf?)\n  // If the content of the popper grows once it's been positioned, it\n  // may happen that the popper gets misplaced because of the new content\n  // overflowing its reference element\n  // To avoid this problem, we provide two options (x and y), which allow\n  // the consumer to define the offset origin.\n  // If we position a popper on top of a reference element, we can set\n  // `x` to `top` to make the popper grow towards its top instead of\n  // its bottom.\n  var left = void 0,\n      top = void 0;\n  if (sideA === 'bottom') {\n    // when offsetParent is <html> the positioning is relative to the bottom of the screen (excluding the scrollbar)\n    // and not the bottom of the html element\n    if (offsetParent.nodeName === 'HTML') {\n      top = -offsetParent.clientHeight + offsets.bottom;\n    } else {\n      top = -offsetParentRect.height + offsets.bottom;\n    }\n  } else {\n    top = offsets.top;\n  }\n  if (sideB === 'right') {\n    if (offsetParent.nodeName === 'HTML') {\n      left = -offsetParent.clientWidth + offsets.right;\n    } else {\n      left = -offsetParentRect.width + offsets.right;\n    }\n  } else {\n    left = offsets.left;\n  }\n  if (gpuAcceleration && prefixedProperty) {\n    styles[prefixedProperty] = 'translate3d(' + left + 'px, ' + top + 'px, 0)';\n    styles[sideA] = 0;\n    styles[sideB] = 0;\n    styles.willChange = 'transform';\n  } else {\n    // othwerise, we use the standard `top`, `left`, `bottom` and `right` properties\n    var invertTop = sideA === 'bottom' ? -1 : 1;\n    var invertLeft = sideB === 'right' ? -1 : 1;\n    styles[sideA] = top * invertTop;\n    styles[sideB] = left * invertLeft;\n    styles.willChange = sideA + ', ' + sideB;\n  }\n\n  // Attributes\n  var attributes = {\n    'x-placement': data.placement\n  };\n\n  // Update `data` attributes, styles and arrowStyles\n  data.attributes = _extends({}, attributes, data.attributes);\n  data.styles = _extends({}, styles, data.styles);\n  data.arrowStyles = _extends({}, data.offsets.arrow, data.arrowStyles);\n\n  return data;\n}\n\n/**\n * Helper used to know if the given modifier depends from another one.<br />\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nfunction isModifierRequired(modifiers, requestingName, requestedName) {\n  var requesting = find(modifiers, function (_ref) {\n    var name = _ref.name;\n    return name === requestingName;\n  });\n\n  var isRequired = !!requesting && modifiers.some(function (modifier) {\n    return modifier.name === requestedName && modifier.enabled && modifier.order < requesting.order;\n  });\n\n  if (!isRequired) {\n    var _requesting = '`' + requestingName + '`';\n    var requested = '`' + requestedName + '`';\n    console.warn(requested + ' modifier is required by ' + _requesting + ' modifier in order to work, be sure to include it before ' + _requesting + '!');\n  }\n  return isRequired;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction arrow(data, options) {\n  var _data$offsets$arrow;\n\n  // arrow depends on keepTogether in order to work\n  if (!isModifierRequired(data.instance.modifiers, 'arrow', 'keepTogether')) {\n    return data;\n  }\n\n  var arrowElement = options.element;\n\n  // if arrowElement is a string, suppose it's a CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = data.instance.popper.querySelector(arrowElement);\n\n    // if arrowElement is not found, don't run the modifier\n    if (!arrowElement) {\n      return data;\n    }\n  } else {\n    // if the arrowElement isn't a query selector we must check that the\n    // provided DOM node is child of its popper node\n    if (!data.instance.popper.contains(arrowElement)) {\n      console.warn('WARNING: `arrow.element` must be child of its popper element!');\n      return data;\n    }\n  }\n\n  var placement = data.placement.split('-')[0];\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var isVertical = ['left', 'right'].indexOf(placement) !== -1;\n\n  var len = isVertical ? 'height' : 'width';\n  var sideCapitalized = isVertical ? 'Top' : 'Left';\n  var side = sideCapitalized.toLowerCase();\n  var altSide = isVertical ? 'left' : 'top';\n  var opSide = isVertical ? 'bottom' : 'right';\n  var arrowElementSize = getOuterSizes(arrowElement)[len];\n\n  //\n  // extends keepTogether behavior making sure the popper and its\n  // reference have enough pixels in conjunction\n  //\n\n  // top/left side\n  if (reference[opSide] - arrowElementSize < popper[side]) {\n    data.offsets.popper[side] -= popper[side] - (reference[opSide] - arrowElementSize);\n  }\n  // bottom/right side\n  if (reference[side] + arrowElementSize > popper[opSide]) {\n    data.offsets.popper[side] += reference[side] + arrowElementSize - popper[opSide];\n  }\n  data.offsets.popper = getClientRect(data.offsets.popper);\n\n  // compute center of the popper\n  var center = reference[side] + reference[len] / 2 - arrowElementSize / 2;\n\n  // Compute the sideValue using the updated popper offsets\n  // take popper margin in account because we don't have this info available\n  var css = getStyleComputedProperty(data.instance.popper);\n  var popperMarginSide = parseFloat(css['margin' + sideCapitalized], 10);\n  var popperBorderSide = parseFloat(css['border' + sideCapitalized + 'Width'], 10);\n  var sideValue = center - data.offsets.popper[side] - popperMarginSide - popperBorderSide;\n\n  // prevent arrowElement from being placed not contiguously to its popper\n  sideValue = Math.max(Math.min(popper[len] - arrowElementSize, sideValue), 0);\n\n  data.arrowElement = arrowElement;\n  data.offsets.arrow = (_data$offsets$arrow = {}, defineProperty(_data$offsets$arrow, side, Math.round(sideValue)), defineProperty(_data$offsets$arrow, altSide, ''), _data$offsets$arrow);\n\n  return data;\n}\n\n/**\n * Get the opposite placement variation of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement variation\n * @returns {String} flipped placement variation\n */\nfunction getOppositeVariation(variation) {\n  if (variation === 'end') {\n    return 'start';\n  } else if (variation === 'start') {\n    return 'end';\n  }\n  return variation;\n}\n\n/**\n * List of accepted placements to use as values of the `placement` option.<br />\n * Valid placements are:\n * - `auto`\n * - `top`\n * - `right`\n * - `bottom`\n * - `left`\n *\n * Each placement can have a variation from this list:\n * - `-start`\n * - `-end`\n *\n * Variations are interpreted easily if you think of them as the left to right\n * written languages. Horizontally (`top` and `bottom`), `start` is left and `end`\n * is right.<br />\n * Vertically (`left` and `right`), `start` is top and `end` is bottom.\n *\n * Some valid examples are:\n * - `top-end` (on top of reference, right aligned)\n * - `right-start` (on right of reference, top aligned)\n * - `bottom` (on bottom, centered)\n * - `auto-end` (on the side with more space available, alignment depends by placement)\n *\n * @static\n * @type {Array}\n * @enum {String}\n * @readonly\n * @method placements\n * @memberof Popper\n */\nvar placements = ['auto-start', 'auto', 'auto-end', 'top-start', 'top', 'top-end', 'right-start', 'right', 'right-end', 'bottom-end', 'bottom', 'bottom-start', 'left-end', 'left', 'left-start'];\n\n// Get rid of `auto` `auto-start` and `auto-end`\nvar validPlacements = placements.slice(3);\n\n/**\n * Given an initial placement, returns all the subsequent placements\n * clockwise (or counter-clockwise).\n *\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement - A valid placement (it accepts variations)\n * @argument {Boolean} counter - Set to true to walk the placements counterclockwise\n * @returns {Array} placements including their variations\n */\nfunction clockwise(placement) {\n  var counter = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var index = validPlacements.indexOf(placement);\n  var arr = validPlacements.slice(index + 1).concat(validPlacements.slice(0, index));\n  return counter ? arr.reverse() : arr;\n}\n\nvar BEHAVIORS = {\n  FLIP: 'flip',\n  CLOCKWISE: 'clockwise',\n  COUNTERCLOCKWISE: 'counterclockwise'\n};\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction flip(data, options) {\n  // if `inner` modifier is enabled, we can't use the `flip` modifier\n  if (isModifierEnabled(data.instance.modifiers, 'inner')) {\n    return data;\n  }\n\n  if (data.flipped && data.placement === data.originalPlacement) {\n    // seems like flip is trying to loop, probably there's not enough space on any of the flippable sides\n    return data;\n  }\n\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, options.boundariesElement, data.positionFixed);\n\n  var placement = data.placement.split('-')[0];\n  var placementOpposite = getOppositePlacement(placement);\n  var variation = data.placement.split('-')[1] || '';\n\n  var flipOrder = [];\n\n  switch (options.behavior) {\n    case BEHAVIORS.FLIP:\n      flipOrder = [placement, placementOpposite];\n      break;\n    case BEHAVIORS.CLOCKWISE:\n      flipOrder = clockwise(placement);\n      break;\n    case BEHAVIORS.COUNTERCLOCKWISE:\n      flipOrder = clockwise(placement, true);\n      break;\n    default:\n      flipOrder = options.behavior;\n  }\n\n  flipOrder.forEach(function (step, index) {\n    if (placement !== step || flipOrder.length === index + 1) {\n      return data;\n    }\n\n    placement = data.placement.split('-')[0];\n    placementOpposite = getOppositePlacement(placement);\n\n    var popperOffsets = data.offsets.popper;\n    var refOffsets = data.offsets.reference;\n\n    // using floor because the reference offsets may contain decimals we are not going to consider here\n    var floor = Math.floor;\n    var overlapsRef = placement === 'left' && floor(popperOffsets.right) > floor(refOffsets.left) || placement === 'right' && floor(popperOffsets.left) < floor(refOffsets.right) || placement === 'top' && floor(popperOffsets.bottom) > floor(refOffsets.top) || placement === 'bottom' && floor(popperOffsets.top) < floor(refOffsets.bottom);\n\n    var overflowsLeft = floor(popperOffsets.left) < floor(boundaries.left);\n    var overflowsRight = floor(popperOffsets.right) > floor(boundaries.right);\n    var overflowsTop = floor(popperOffsets.top) < floor(boundaries.top);\n    var overflowsBottom = floor(popperOffsets.bottom) > floor(boundaries.bottom);\n\n    var overflowsBoundaries = placement === 'left' && overflowsLeft || placement === 'right' && overflowsRight || placement === 'top' && overflowsTop || placement === 'bottom' && overflowsBottom;\n\n    // flip the variation if required\n    var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n    var flippedVariation = !!options.flipVariations && (isVertical && variation === 'start' && overflowsLeft || isVertical && variation === 'end' && overflowsRight || !isVertical && variation === 'start' && overflowsTop || !isVertical && variation === 'end' && overflowsBottom);\n\n    if (overlapsRef || overflowsBoundaries || flippedVariation) {\n      // this boolean to detect any flip loop\n      data.flipped = true;\n\n      if (overlapsRef || overflowsBoundaries) {\n        placement = flipOrder[index + 1];\n      }\n\n      if (flippedVariation) {\n        variation = getOppositeVariation(variation);\n      }\n\n      data.placement = placement + (variation ? '-' + variation : '');\n\n      // this object contains `position`, we want to preserve it along with\n      // any additional property we may add in the future\n      data.offsets.popper = _extends({}, data.offsets.popper, getPopperOffsets(data.instance.popper, data.offsets.reference, data.placement));\n\n      data = runModifiers(data.instance.modifiers, data, 'flip');\n    }\n  });\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction keepTogether(data) {\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var placement = data.placement.split('-')[0];\n  var floor = Math.floor;\n  var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n  var side = isVertical ? 'right' : 'bottom';\n  var opSide = isVertical ? 'left' : 'top';\n  var measurement = isVertical ? 'width' : 'height';\n\n  if (popper[side] < floor(reference[opSide])) {\n    data.offsets.popper[opSide] = floor(reference[opSide]) - popper[measurement];\n  }\n  if (popper[opSide] > floor(reference[side])) {\n    data.offsets.popper[opSide] = floor(reference[side]);\n  }\n\n  return data;\n}\n\n/**\n * Converts a string containing value + unit into a px value number\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} str - Value + unit string\n * @argument {String} measurement - `height` or `width`\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @returns {Number|String}\n * Value in pixels, or original string if no values were extracted\n */\nfunction toValue(str, measurement, popperOffsets, referenceOffsets) {\n  // separate value from unit\n  var split = str.match(/((?:\\-|\\+)?\\d*\\.?\\d*)(.*)/);\n  var value = +split[1];\n  var unit = split[2];\n\n  // If it's not a number it's an operator, I guess\n  if (!value) {\n    return str;\n  }\n\n  if (unit.indexOf('%') === 0) {\n    var element = void 0;\n    switch (unit) {\n      case '%p':\n        element = popperOffsets;\n        break;\n      case '%':\n      case '%r':\n      default:\n        element = referenceOffsets;\n    }\n\n    var rect = getClientRect(element);\n    return rect[measurement] / 100 * value;\n  } else if (unit === 'vh' || unit === 'vw') {\n    // if is a vh or vw, we calculate the size based on the viewport\n    var size = void 0;\n    if (unit === 'vh') {\n      size = Math.max(document.documentElement.clientHeight, window.innerHeight || 0);\n    } else {\n      size = Math.max(document.documentElement.clientWidth, window.innerWidth || 0);\n    }\n    return size / 100 * value;\n  } else {\n    // if is an explicit pixel unit, we get rid of the unit and keep the value\n    // if is an implicit unit, it's px, and we return just the value\n    return value;\n  }\n}\n\n/**\n * Parse an `offset` string to extrapolate `x` and `y` numeric offsets.\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} offset\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @argument {String} basePlacement\n * @returns {Array} a two cells array with x and y offsets in numbers\n */\nfunction parseOffset(offset, popperOffsets, referenceOffsets, basePlacement) {\n  var offsets = [0, 0];\n\n  // Use height if placement is left or right and index is 0 otherwise use width\n  // in this way the first offset will use an axis and the second one\n  // will use the other one\n  var useHeight = ['right', 'left'].indexOf(basePlacement) !== -1;\n\n  // Split the offset string to obtain a list of values and operands\n  // The regex addresses values with the plus or minus sign in front (+10, -20, etc)\n  var fragments = offset.split(/(\\+|\\-)/).map(function (frag) {\n    return frag.trim();\n  });\n\n  // Detect if the offset string contains a pair of values or a single one\n  // they could be separated by comma or space\n  var divider = fragments.indexOf(find(fragments, function (frag) {\n    return frag.search(/,|\\s/) !== -1;\n  }));\n\n  if (fragments[divider] && fragments[divider].indexOf(',') === -1) {\n    console.warn('Offsets separated by white space(s) are deprecated, use a comma (,) instead.');\n  }\n\n  // If divider is found, we divide the list of values and operands to divide\n  // them by ofset X and Y.\n  var splitRegex = /\\s*,\\s*|\\s+/;\n  var ops = divider !== -1 ? [fragments.slice(0, divider).concat([fragments[divider].split(splitRegex)[0]]), [fragments[divider].split(splitRegex)[1]].concat(fragments.slice(divider + 1))] : [fragments];\n\n  // Convert the values with units to absolute pixels to allow our computations\n  ops = ops.map(function (op, index) {\n    // Most of the units rely on the orientation of the popper\n    var measurement = (index === 1 ? !useHeight : useHeight) ? 'height' : 'width';\n    var mergeWithPrevious = false;\n    return op\n    // This aggregates any `+` or `-` sign that aren't considered operators\n    // e.g.: 10 + +5 => [10, +, +5]\n    .reduce(function (a, b) {\n      if (a[a.length - 1] === '' && ['+', '-'].indexOf(b) !== -1) {\n        a[a.length - 1] = b;\n        mergeWithPrevious = true;\n        return a;\n      } else if (mergeWithPrevious) {\n        a[a.length - 1] += b;\n        mergeWithPrevious = false;\n        return a;\n      } else {\n        return a.concat(b);\n      }\n    }, [])\n    // Here we convert the string values into number values (in px)\n    .map(function (str) {\n      return toValue(str, measurement, popperOffsets, referenceOffsets);\n    });\n  });\n\n  // Loop trough the offsets arrays and execute the operations\n  ops.forEach(function (op, index) {\n    op.forEach(function (frag, index2) {\n      if (isNumeric(frag)) {\n        offsets[index] += frag * (op[index2 - 1] === '-' ? -1 : 1);\n      }\n    });\n  });\n  return offsets;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @argument {Number|String} options.offset=0\n * The offset value as described in the modifier description\n * @returns {Object} The data object, properly modified\n */\nfunction offset(data, _ref) {\n  var offset = _ref.offset;\n  var placement = data.placement,\n      _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var basePlacement = placement.split('-')[0];\n\n  var offsets = void 0;\n  if (isNumeric(+offset)) {\n    offsets = [+offset, 0];\n  } else {\n    offsets = parseOffset(offset, popper, reference, basePlacement);\n  }\n\n  if (basePlacement === 'left') {\n    popper.top += offsets[0];\n    popper.left -= offsets[1];\n  } else if (basePlacement === 'right') {\n    popper.top += offsets[0];\n    popper.left += offsets[1];\n  } else if (basePlacement === 'top') {\n    popper.left += offsets[0];\n    popper.top -= offsets[1];\n  } else if (basePlacement === 'bottom') {\n    popper.left += offsets[0];\n    popper.top += offsets[1];\n  }\n\n  data.popper = popper;\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction preventOverflow(data, options) {\n  var boundariesElement = options.boundariesElement || getOffsetParent(data.instance.popper);\n\n  // If offsetParent is the reference element, we really want to\n  // go one step up and use the next offsetParent as reference to\n  // avoid to make this modifier completely useless and look like broken\n  if (data.instance.reference === boundariesElement) {\n    boundariesElement = getOffsetParent(boundariesElement);\n  }\n\n  // NOTE: DOM access here\n  // resets the popper's position so that the document size can be calculated excluding\n  // the size of the popper element itself\n  var transformProp = getSupportedPropertyName('transform');\n  var popperStyles = data.instance.popper.style; // assignment to help minification\n  var top = popperStyles.top,\n      left = popperStyles.left,\n      transform = popperStyles[transformProp];\n\n  popperStyles.top = '';\n  popperStyles.left = '';\n  popperStyles[transformProp] = '';\n\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, boundariesElement, data.positionFixed);\n\n  // NOTE: DOM access here\n  // restores the original style properties after the offsets have been computed\n  popperStyles.top = top;\n  popperStyles.left = left;\n  popperStyles[transformProp] = transform;\n\n  options.boundaries = boundaries;\n\n  var order = options.priority;\n  var popper = data.offsets.popper;\n\n  var check = {\n    primary: function primary(placement) {\n      var value = popper[placement];\n      if (popper[placement] < boundaries[placement] && !options.escapeWithReference) {\n        value = Math.max(popper[placement], boundaries[placement]);\n      }\n      return defineProperty({}, placement, value);\n    },\n    secondary: function secondary(placement) {\n      var mainSide = placement === 'right' ? 'left' : 'top';\n      var value = popper[mainSide];\n      if (popper[placement] > boundaries[placement] && !options.escapeWithReference) {\n        value = Math.min(popper[mainSide], boundaries[placement] - (placement === 'right' ? popper.width : popper.height));\n      }\n      return defineProperty({}, mainSide, value);\n    }\n  };\n\n  order.forEach(function (placement) {\n    var side = ['left', 'top'].indexOf(placement) !== -1 ? 'primary' : 'secondary';\n    popper = _extends({}, popper, check[side](placement));\n  });\n\n  data.offsets.popper = popper;\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction shift(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var shiftvariation = placement.split('-')[1];\n\n  // if shift shiftvariation is specified, run the modifier\n  if (shiftvariation) {\n    var _data$offsets = data.offsets,\n        reference = _data$offsets.reference,\n        popper = _data$offsets.popper;\n\n    var isVertical = ['bottom', 'top'].indexOf(basePlacement) !== -1;\n    var side = isVertical ? 'left' : 'top';\n    var measurement = isVertical ? 'width' : 'height';\n\n    var shiftOffsets = {\n      start: defineProperty({}, side, reference[side]),\n      end: defineProperty({}, side, reference[side] + reference[measurement] - popper[measurement])\n    };\n\n    data.offsets.popper = _extends({}, popper, shiftOffsets[shiftvariation]);\n  }\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction hide(data) {\n  if (!isModifierRequired(data.instance.modifiers, 'hide', 'preventOverflow')) {\n    return data;\n  }\n\n  var refRect = data.offsets.reference;\n  var bound = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'preventOverflow';\n  }).boundaries;\n\n  if (refRect.bottom < bound.top || refRect.left > bound.right || refRect.top > bound.bottom || refRect.right < bound.left) {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === true) {\n      return data;\n    }\n\n    data.hide = true;\n    data.attributes['x-out-of-boundaries'] = '';\n  } else {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === false) {\n      return data;\n    }\n\n    data.hide = false;\n    data.attributes['x-out-of-boundaries'] = false;\n  }\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction inner(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var isHoriz = ['left', 'right'].indexOf(basePlacement) !== -1;\n\n  var subtractLength = ['top', 'left'].indexOf(basePlacement) === -1;\n\n  popper[isHoriz ? 'left' : 'top'] = reference[basePlacement] - (subtractLength ? popper[isHoriz ? 'width' : 'height'] : 0);\n\n  data.placement = getOppositePlacement(placement);\n  data.offsets.popper = getClientRect(popper);\n\n  return data;\n}\n\n/**\n * Modifier function, each modifier can have a function of this type assigned\n * to its `fn` property.<br />\n * These functions will be called on each update, this means that you must\n * make sure they are performant enough to avoid performance bottlenecks.\n *\n * @function ModifierFn\n * @argument {dataObject} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {dataObject} The data object, properly modified\n */\n\n/**\n * Modifiers are plugins used to alter the behavior of your poppers.<br />\n * Popper.js uses a set of 9 modifiers to provide all the basic functionalities\n * needed by the library.\n *\n * Usually you don't want to override the `order`, `fn` and `onLoad` props.\n * All the other properties are configurations that could be tweaked.\n * @namespace modifiers\n */\nvar modifiers = {\n  /**\n   * Modifier used to shift the popper on the start or end of its reference\n   * element.<br />\n   * It will read the variation of the `placement` property.<br />\n   * It can be one either `-end` or `-start`.\n   * @memberof modifiers\n   * @inner\n   */\n  shift: {\n    /** @prop {number} order=100 - Index used to define the order of execution */\n    order: 100,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: shift\n  },\n\n  /**\n   * The `offset` modifier can shift your popper on both its axis.\n   *\n   * It accepts the following units:\n   * - `px` or unit-less, interpreted as pixels\n   * - `%` or `%r`, percentage relative to the length of the reference element\n   * - `%p`, percentage relative to the length of the popper element\n   * - `vw`, CSS viewport width unit\n   * - `vh`, CSS viewport height unit\n   *\n   * For length is intended the main axis relative to the placement of the popper.<br />\n   * This means that if the placement is `top` or `bottom`, the length will be the\n   * `width`. In case of `left` or `right`, it will be the `height`.\n   *\n   * You can provide a single value (as `Number` or `String`), or a pair of values\n   * as `String` divided by a comma or one (or more) white spaces.<br />\n   * The latter is a deprecated method because it leads to confusion and will be\n   * removed in v2.<br />\n   * Additionally, it accepts additions and subtractions between different units.\n   * Note that multiplications and divisions aren't supported.\n   *\n   * Valid examples are:\n   * ```\n   * 10\n   * '10%'\n   * '10, 10'\n   * '10%, 10'\n   * '10 + 10%'\n   * '10 - 5vh + 3%'\n   * '-10px + 5vh, 5px - 6%'\n   * ```\n   * > **NB**: If you desire to apply offsets to your poppers in a way that may make them overlap\n   * > with their reference element, unfortunately, you will have to disable the `flip` modifier.\n   * > You can read more on this at this [issue](https://github.com/FezVrasta/popper.js/issues/373).\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  offset: {\n    /** @prop {number} order=200 - Index used to define the order of execution */\n    order: 200,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: offset,\n    /** @prop {Number|String} offset=0\n     * The offset value as described in the modifier description\n     */\n    offset: 0\n  },\n\n  /**\n   * Modifier used to prevent the popper from being positioned outside the boundary.\n   *\n   * A scenario exists where the reference itself is not within the boundaries.<br />\n   * We can say it has \"escaped the boundaries\" — or just \"escaped\".<br />\n   * In this case we need to decide whether the popper should either:\n   *\n   * - detach from the reference and remain \"trapped\" in the boundaries, or\n   * - if it should ignore the boundary and \"escape with its reference\"\n   *\n   * When `escapeWithReference` is set to`true` and reference is completely\n   * outside its boundaries, the popper will overflow (or completely leave)\n   * the boundaries in order to remain attached to the edge of the reference.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  preventOverflow: {\n    /** @prop {number} order=300 - Index used to define the order of execution */\n    order: 300,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: preventOverflow,\n    /**\n     * @prop {Array} [priority=['left','right','top','bottom']]\n     * Popper will try to prevent overflow following these priorities by default,\n     * then, it could overflow on the left and on top of the `boundariesElement`\n     */\n    priority: ['left', 'right', 'top', 'bottom'],\n    /**\n     * @prop {number} padding=5\n     * Amount of pixel used to define a minimum distance between the boundaries\n     * and the popper. This makes sure the popper always has a little padding\n     * between the edges of its container\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='scrollParent'\n     * Boundaries used by the modifier. Can be `scrollParent`, `window`,\n     * `viewport` or any DOM element.\n     */\n    boundariesElement: 'scrollParent'\n  },\n\n  /**\n   * Modifier used to make sure the reference and its popper stay near each other\n   * without leaving any gap between the two. Especially useful when the arrow is\n   * enabled and you want to ensure that it points to its reference element.\n   * It cares only about the first axis. You can still have poppers with margin\n   * between the popper and its reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  keepTogether: {\n    /** @prop {number} order=400 - Index used to define the order of execution */\n    order: 400,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: keepTogether\n  },\n\n  /**\n   * This modifier is used to move the `arrowElement` of the popper to make\n   * sure it is positioned between the reference element and its popper element.\n   * It will read the outer size of the `arrowElement` node to detect how many\n   * pixels of conjunction are needed.\n   *\n   * It has no effect if no `arrowElement` is provided.\n   * @memberof modifiers\n   * @inner\n   */\n  arrow: {\n    /** @prop {number} order=500 - Index used to define the order of execution */\n    order: 500,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: arrow,\n    /** @prop {String|HTMLElement} element='[x-arrow]' - Selector or node used as arrow */\n    element: '[x-arrow]'\n  },\n\n  /**\n   * Modifier used to flip the popper's placement when it starts to overlap its\n   * reference element.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   *\n   * **NOTE:** this modifier will interrupt the current update cycle and will\n   * restart it if it detects the need to flip the placement.\n   * @memberof modifiers\n   * @inner\n   */\n  flip: {\n    /** @prop {number} order=600 - Index used to define the order of execution */\n    order: 600,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: flip,\n    /**\n     * @prop {String|Array} behavior='flip'\n     * The behavior used to change the popper's placement. It can be one of\n     * `flip`, `clockwise`, `counterclockwise` or an array with a list of valid\n     * placements (with optional variations)\n     */\n    behavior: 'flip',\n    /**\n     * @prop {number} padding=5\n     * The popper will flip if it hits the edges of the `boundariesElement`\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='viewport'\n     * The element which will define the boundaries of the popper position.\n     * The popper will never be placed outside of the defined boundaries\n     * (except if `keepTogether` is enabled)\n     */\n    boundariesElement: 'viewport'\n  },\n\n  /**\n   * Modifier used to make the popper flow toward the inner of the reference element.\n   * By default, when this modifier is disabled, the popper will be placed outside\n   * the reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  inner: {\n    /** @prop {number} order=700 - Index used to define the order of execution */\n    order: 700,\n    /** @prop {Boolean} enabled=false - Whether the modifier is enabled or not */\n    enabled: false,\n    /** @prop {ModifierFn} */\n    fn: inner\n  },\n\n  /**\n   * Modifier used to hide the popper when its reference element is outside of the\n   * popper boundaries. It will set a `x-out-of-boundaries` attribute which can\n   * be used to hide with a CSS selector the popper when its reference is\n   * out of boundaries.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   * @memberof modifiers\n   * @inner\n   */\n  hide: {\n    /** @prop {number} order=800 - Index used to define the order of execution */\n    order: 800,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: hide\n  },\n\n  /**\n   * Computes the style that will be applied to the popper element to gets\n   * properly positioned.\n   *\n   * Note that this modifier will not touch the DOM, it just prepares the styles\n   * so that `applyStyle` modifier can apply it. This separation is useful\n   * in case you need to replace `applyStyle` with a custom implementation.\n   *\n   * This modifier has `850` as `order` value to maintain backward compatibility\n   * with previous versions of Popper.js. Expect the modifiers ordering method\n   * to change in future major versions of the library.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  computeStyle: {\n    /** @prop {number} order=850 - Index used to define the order of execution */\n    order: 850,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: computeStyle,\n    /**\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: true,\n    /**\n     * @prop {string} [x='bottom']\n     * Where to anchor the X axis (`bottom` or `top`). AKA X offset origin.\n     * Change this if your popper should grow in a direction different from `bottom`\n     */\n    x: 'bottom',\n    /**\n     * @prop {string} [x='left']\n     * Where to anchor the Y axis (`left` or `right`). AKA Y offset origin.\n     * Change this if your popper should grow in a direction different from `right`\n     */\n    y: 'right'\n  },\n\n  /**\n   * Applies the computed styles to the popper element.\n   *\n   * All the DOM manipulations are limited to this modifier. This is useful in case\n   * you want to integrate Popper.js inside a framework or view library and you\n   * want to delegate all the DOM manipulations to it.\n   *\n   * Note that if you disable this modifier, you must make sure the popper element\n   * has its position set to `absolute` before Popper.js can do its work!\n   *\n   * Just disable this modifier and define your own to achieve the desired effect.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  applyStyle: {\n    /** @prop {number} order=900 - Index used to define the order of execution */\n    order: 900,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: applyStyle,\n    /** @prop {Function} */\n    onLoad: applyStyleOnLoad,\n    /**\n     * @deprecated since version 1.10.0, the property moved to `computeStyle` modifier\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: undefined\n  }\n};\n\n/**\n * The `dataObject` is an object containing all the information used by Popper.js.\n * This object is passed to modifiers and to the `onCreate` and `onUpdate` callbacks.\n * @name dataObject\n * @property {Object} data.instance The Popper.js instance\n * @property {String} data.placement Placement applied to popper\n * @property {String} data.originalPlacement Placement originally defined on init\n * @property {Boolean} data.flipped True if popper has been flipped by flip modifier\n * @property {Boolean} data.hide True if the reference element is out of boundaries, useful to know when to hide the popper\n * @property {HTMLElement} data.arrowElement Node used as arrow by arrow modifier\n * @property {Object} data.styles Any CSS property defined here will be applied to the popper. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.arrowStyles Any CSS property defined here will be applied to the popper arrow. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.boundaries Offsets of the popper boundaries\n * @property {Object} data.offsets The measurements of popper, reference and arrow elements\n * @property {Object} data.offsets.popper `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.reference `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.arrow] `top` and `left` offsets, only one of them will be different from 0\n */\n\n/**\n * Default options provided to Popper.js constructor.<br />\n * These can be overridden using the `options` argument of Popper.js.<br />\n * To override an option, simply pass an object with the same\n * structure of the `options` object, as the 3rd argument. For example:\n * ```\n * new Popper(ref, pop, {\n *   modifiers: {\n *     preventOverflow: { enabled: false }\n *   }\n * })\n * ```\n * @type {Object}\n * @static\n * @memberof Popper\n */\nvar Defaults = {\n  /**\n   * Popper's placement.\n   * @prop {Popper.placements} placement='bottom'\n   */\n  placement: 'bottom',\n\n  /**\n   * Set this to true if you want popper to position it self in 'fixed' mode\n   * @prop {Boolean} positionFixed=false\n   */\n  positionFixed: false,\n\n  /**\n   * Whether events (resize, scroll) are initially enabled.\n   * @prop {Boolean} eventsEnabled=true\n   */\n  eventsEnabled: true,\n\n  /**\n   * Set to true if you want to automatically remove the popper when\n   * you call the `destroy` method.\n   * @prop {Boolean} removeOnDestroy=false\n   */\n  removeOnDestroy: false,\n\n  /**\n   * Callback called when the popper is created.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onCreate}\n   */\n  onCreate: function onCreate() {},\n\n  /**\n   * Callback called when the popper is updated. This callback is not called\n   * on the initialization/creation of the popper, but only on subsequent\n   * updates.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onUpdate}\n   */\n  onUpdate: function onUpdate() {},\n\n  /**\n   * List of modifiers used to modify the offsets before they are applied to the popper.\n   * They provide most of the functionalities of Popper.js.\n   * @prop {modifiers}\n   */\n  modifiers: modifiers\n};\n\n/**\n * @callback onCreate\n * @param {dataObject} data\n */\n\n/**\n * @callback onUpdate\n * @param {dataObject} data\n */\n\n// Utils\n// Methods\nvar Popper = function () {\n  /**\n   * Creates a new Popper.js instance.\n   * @class Popper\n   * @param {HTMLElement|referenceObject} reference - The reference element used to position the popper\n   * @param {HTMLElement} popper - The HTML element used as the popper\n   * @param {Object} options - Your custom options to override the ones defined in [Defaults](#defaults)\n   * @return {Object} instance - The generated Popper.js instance\n   */\n  function Popper(reference, popper) {\n    var _this = this;\n\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    classCallCheck(this, Popper);\n\n    this.scheduleUpdate = function () {\n      return requestAnimationFrame(_this.update);\n    };\n\n    // make update() debounced, so that it only runs at most once-per-tick\n    this.update = debounce(this.update.bind(this));\n\n    // with {} we create a new object with the options inside it\n    this.options = _extends({}, Popper.Defaults, options);\n\n    // init state\n    this.state = {\n      isDestroyed: false,\n      isCreated: false,\n      scrollParents: []\n    };\n\n    // get reference and popper elements (allow jQuery wrappers)\n    this.reference = reference && reference.jquery ? reference[0] : reference;\n    this.popper = popper && popper.jquery ? popper[0] : popper;\n\n    // Deep merge modifiers options\n    this.options.modifiers = {};\n    Object.keys(_extends({}, Popper.Defaults.modifiers, options.modifiers)).forEach(function (name) {\n      _this.options.modifiers[name] = _extends({}, Popper.Defaults.modifiers[name] || {}, options.modifiers ? options.modifiers[name] : {});\n    });\n\n    // Refactoring modifiers' list (Object => Array)\n    this.modifiers = Object.keys(this.options.modifiers).map(function (name) {\n      return _extends({\n        name: name\n      }, _this.options.modifiers[name]);\n    })\n    // sort the modifiers by order\n    .sort(function (a, b) {\n      return a.order - b.order;\n    });\n\n    // modifiers have the ability to execute arbitrary code when Popper.js get inited\n    // such code is executed in the same order of its modifier\n    // they could add new properties to their options configuration\n    // BE AWARE: don't add options to `options.modifiers.name` but to `modifierOptions`!\n    this.modifiers.forEach(function (modifierOptions) {\n      if (modifierOptions.enabled && isFunction(modifierOptions.onLoad)) {\n        modifierOptions.onLoad(_this.reference, _this.popper, _this.options, modifierOptions, _this.state);\n      }\n    });\n\n    // fire the first update to position the popper in the right place\n    this.update();\n\n    var eventsEnabled = this.options.eventsEnabled;\n    if (eventsEnabled) {\n      // setup event listeners, they will take care of update the position in specific situations\n      this.enableEventListeners();\n    }\n\n    this.state.eventsEnabled = eventsEnabled;\n  }\n\n  // We can't use class properties because they don't get listed in the\n  // class prototype and break stuff like Sinon stubs\n\n\n  createClass(Popper, [{\n    key: 'update',\n    value: function update$$1() {\n      return update.call(this);\n    }\n  }, {\n    key: 'destroy',\n    value: function destroy$$1() {\n      return destroy.call(this);\n    }\n  }, {\n    key: 'enableEventListeners',\n    value: function enableEventListeners$$1() {\n      return enableEventListeners.call(this);\n    }\n  }, {\n    key: 'disableEventListeners',\n    value: function disableEventListeners$$1() {\n      return disableEventListeners.call(this);\n    }\n\n    /**\n     * Schedules an update. It will run on the next UI update available.\n     * @method scheduleUpdate\n     * @memberof Popper\n     */\n\n\n    /**\n     * Collection of utilities useful when writing custom modifiers.\n     * Starting from version 1.7, this method is available only if you\n     * include `popper-utils.js` before `popper.js`.\n     *\n     * **DEPRECATION**: This way to access PopperUtils is deprecated\n     * and will be removed in v2! Use the PopperUtils module directly instead.\n     * Due to the high instability of the methods contained in Utils, we can't\n     * guarantee them to follow semver. Use them at your own risk!\n     * @static\n     * @private\n     * @type {Object}\n     * @deprecated since version 1.8\n     * @member Utils\n     * @memberof Popper\n     */\n\n  }]);\n  return Popper;\n}();\n\n/**\n * The `referenceObject` is an object that provides an interface compatible with Popper.js\n * and lets you use it as replacement of a real DOM node.<br />\n * You can use this method to position a popper relatively to a set of coordinates\n * in case you don't have a DOM node to use as reference.\n *\n * ```\n * new Popper(referenceObject, popperNode);\n * ```\n *\n * NB: This feature isn't supported in Internet Explorer 10.\n * @name referenceObject\n * @property {Function} data.getBoundingClientRect\n * A function that returns a set of coordinates compatible with the native `getBoundingClientRect` method.\n * @property {number} data.clientWidth\n * An ES6 getter that will return the width of the virtual reference element.\n * @property {number} data.clientHeight\n * An ES6 getter that will return the height of the virtual reference element.\n */\n\n\nPopper.Utils = (typeof window !== 'undefined' ? window : global).PopperUtils;\nPopper.placements = placements;\nPopper.Defaults = Defaults;\n\nexport default Popper;\n//# sourceMappingURL=popper.js.map\n", "/** @license React v16.6.1\n * react-is.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';Object.defineProperty(exports,\"__esModule\",{value:!0});\nvar b=\"function\"===typeof Symbol&&Symbol.for,c=b?Symbol.for(\"react.element\"):60103,d=b?Symbol.for(\"react.portal\"):60106,e=b?Symbol.for(\"react.fragment\"):60107,f=b?Symbol.for(\"react.strict_mode\"):60108,g=b?Symbol.for(\"react.profiler\"):60114,h=b?Symbol.for(\"react.provider\"):60109,k=b?Symbol.for(\"react.context\"):60110,l=b?Symbol.for(\"react.async_mode\"):60111,m=b?Symbol.for(\"react.concurrent_mode\"):60111,n=b?Symbol.for(\"react.forward_ref\"):60112,p=b?Symbol.for(\"react.suspense\"):60113,r=b?Symbol.for(\"react.memo\"):\n60115,t=b?Symbol.for(\"react.lazy\"):60116;function u(a){if(\"object\"===typeof a&&null!==a){var q=a.$$typeof;switch(q){case c:switch(a=a.type,a){case l:case m:case e:case g:case f:return a;default:switch(a=a&&a.$$typeof,a){case k:case n:case h:return a;default:return q}}case d:return q}}}function v(a){return u(a)===m}exports.typeOf=u;exports.AsyncMode=l;exports.ConcurrentMode=m;exports.ContextConsumer=k;exports.ContextProvider=h;exports.Element=c;exports.ForwardRef=n;exports.Fragment=e;\nexports.Profiler=g;exports.Portal=d;exports.StrictMode=f;exports.isValidElementType=function(a){return\"string\"===typeof a||\"function\"===typeof a||a===e||a===m||a===g||a===f||a===p||\"object\"===typeof a&&null!==a&&(a.$$typeof===t||a.$$typeof===r||a.$$typeof===h||a.$$typeof===k||a.$$typeof===n)};exports.isAsyncMode=function(a){return v(a)||u(a)===l};exports.isConcurrentMode=v;exports.isContextConsumer=function(a){return u(a)===k};exports.isContextProvider=function(a){return u(a)===h};\nexports.isElement=function(a){return\"object\"===typeof a&&null!==a&&a.$$typeof===c};exports.isForwardRef=function(a){return u(a)===n};exports.isFragment=function(a){return u(a)===e};exports.isProfiler=function(a){return u(a)===g};exports.isPortal=function(a){return u(a)===d};exports.isStrictMode=function(a){return u(a)===f};\n", "/*!\n * escape-html\n * Copyright(c) 2012-2013 <PERSON><PERSON>\n * Copyright(c) 2015 <PERSON>\n * Copyright(c) 2015 Tiancheng \"Timothy\" Gu\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module variables.\n * @private\n */\n\nvar matchHtmlRegExp = /[\"'&<>]/;\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = escapeHtml;\n\n/**\n * Escape special characters in the given string of html.\n *\n * @param  {string} string The string to escape for inserting into HTML\n * @return {string}\n * @public\n */\n\nfunction escapeHtml(string) {\n  var str = '' + string;\n  var match = matchHtmlRegExp.exec(str);\n\n  if (!match) {\n    return str;\n  }\n\n  var escape;\n  var html = '';\n  var index = 0;\n  var lastIndex = 0;\n\n  for (index = match.index; index < str.length; index++) {\n    switch (str.charCodeAt(index)) {\n      case 34: // \"\n        escape = '&quot;';\n        break;\n      case 38: // &\n        escape = '&amp;';\n        break;\n      case 39: // '\n        escape = '&#39;';\n        break;\n      case 60: // <\n        escape = '&lt;';\n        break;\n      case 62: // >\n        escape = '&gt;';\n        break;\n      default:\n        continue;\n    }\n\n    if (lastIndex !== index) {\n      html += str.substring(lastIndex, index);\n    }\n\n    lastIndex = index + 1;\n    html += escape;\n  }\n\n  return lastIndex !== index\n    ? html + str.substring(lastIndex, index)\n    : html;\n}\n"], "mappings": "AAGA;;;;;;;;;;;ACIA;;;;AC8IA;;;;;AC7IA;;;;;ACKA;;;;;ACAA;;;;;;;;;ACGA;;;;;;;;ACPA;;;;;;;;ACGA;;;;;;;;;ACHA;;;;;;;;;;;;;;;ACOA;;;;;;ACPA;;;;;;ACAA;;;;;;;;ACwBA;;;;;;;;ACxBA;;;;;;;;;;;;;;;;;;;;;;;;;ACeA;;;;;;;;ACfA;;;;;;;ACMA", "sourceRoot": ""}