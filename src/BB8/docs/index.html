<!doctype html><html><head><meta charset="utf-8"><title>Storybook</title><style>html, body {
    overflow: hidden;
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
  }</style><script>try {
    if (window.parent !== window) {
      window.__REACT_DEVTOOLS_GLOBAL_HOOK__ = window.parent.__REACT_DEVTOOLS_GLOBAL_HOOK__;
      window.__VUE_DEVTOOLS_GLOBAL_HOOK__ = window.parent.__VUE_DEVTOOLS_GLOBAL_HOOK__;
    }
  } catch (e) {
    console.warn('unable to connect to parent frame for connecting dev tools');
  }</script></head><body><div id="root"></div><script src="static/runtime~manager.1be4c9c775fb85324315.bundle.js"></script><script src="static/manager.f5b65738be5f2ee4a58b.bundle.js"></script></body></html>