import React from "react";
import { IDefaultProps } from "../BB8Shared";
import "../BB8Shared.scss";
import "./styles.scss";

export class BB8MainPage extends React.Component<IDefaultProps> {
    public static Padded: React.FunctionComponent = ({ children }: any) => (
        <section className="main-page padded">{ children }</section>
    );
    public static Full: React.FunctionComponent = ({ children }: any) => (
        <section className="main-page">{ children }</section>
    );

    public render() {
        const { children } = this.props;
        return <main className="main-page-wrapper">{ children }</main>;
    }
}

BB8MainPage.Padded.displayName = "BB8MainPage.Padded";
BB8MainPage.Full.displayName = "BB8MainPage.Full";

export default BB8MainPage;
