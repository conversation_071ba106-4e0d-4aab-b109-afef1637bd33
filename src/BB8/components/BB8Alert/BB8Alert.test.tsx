import { MuiThemeProvider } from '@material-ui/core/styles';
import { render } from '@testing-library/react';
import React from 'react';
import mainTheme from '../../theme/mainTheme';
import BB8Alert from './index';

describe('BB8Alert', () => {
  it('should render', () => {
    const component = render(
      <MuiThemeProvider theme={mainTheme}>
        <BB8Alert show={true} variant="error" message="test" />
      </MuiThemeProvider>
    )
    expect(component.container.firstChild).toBeTruthy();
  })
})
