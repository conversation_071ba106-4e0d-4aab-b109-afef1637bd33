@use "../../BB8Variables" as *;
@use "../../../vendor/airmiles/pattern-library/sass/base/colors" as *;

.bb8-search-wrapper {
  position: relative;
  min-height: 3em;
  width: auto;
  display: flex;
  flex-flow: row nowrap;
  flex: 0 0 auto;
  .bb8-search {
    background: color("grey-accent-light-4");
    color: color("black");
    flex: 1 1 100%;
    font-family: "Omnes";
    font-size: 16px;
    border-radius: $bb8-border-radius;
    border: 0;
    padding-left: 1em;
    padding-right: 1em;
    width: 100%;

    &:focus {
      outline: color("blue-accent") solid 3px;
      outline-offset: -3px;
    }
    &.dirty.invalid {
      background: color("error-light");
    }
    &.valid {
      background: color("grey-light-4");
    }
  }
  .bb8-search-icon {
    position: absolute;
    right: 0rem;
    visibility: hidden;
    pointer-events: none;
    &.show {
      visibility: visible;
      pointer-events: all;
    }
  }
}

.margin-20-top {
  margin-top: 20px !important;
}

.rem-1-font {
  font-size: 1rem !important;
}

.lookup-popper {
  z-index: 1300;
}
