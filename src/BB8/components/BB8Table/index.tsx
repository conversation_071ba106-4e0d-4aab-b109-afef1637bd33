import { Theme } from "@material-ui/core";
import createStyles from "@material-ui/core/styles/createStyles";
import withStyles from "@material-ui/core/styles/withStyles";

import Table from "@material-ui/core/Table";
import TableBody from "@material-ui/core/TableBody";
import TableCell, { TableCellProps } from "@material-ui/core/TableCell";
import TableHead from "@material-ui/core/TableHead";
import TableRow from "@material-ui/core/TableRow";
import { path } from "rambda";
import React from "react";

export interface IColProps {
    id: string | "colSpan";
    title: string | JSX.Element;
    style?: any;
    headerCellProps?: TableCellProps;
    tableCellProps?: TableCellProps;
    tableCellTemplate?: (props: ITemplateCellProps) => React.ReactElement<any>;
}

export interface ITemplateCellProps {
    id: string;
    data: any;
    row: any;
}

const CustomTableCell = withStyles((theme: Theme) =>
    createStyles({
        body: {
            backgroundColor: "#FFFFFF",
            color: "#231F20",
            fontFamily: "Omnes",
            fontSize: 14,
            fontWeight: 400,
            lineHeight: 4,
            opacity: 0.9,
            padding: "4px 16px 4px 16px"
        },
        head: {
            alignSelf: "flex-start",
            backgroundColor: "#F2F5F7",
            borderBottomColor: "#CCD9E0",
            borderBottomWidth: 2,
            color: theme.palette.common.black,
            fontFamily: "Omnes",
            fontSize: 14,
            fontWeight: 500,
            padding: "4px 16px 4px 16px",
            position: "sticky",
            top: 0,
            zIndex: 10
        }
    })
)(TableCell);

const styles = createStyles((theme: Theme) => ({
    root: {
        marginTop: theme.spacing(3),
        overflowX: "auto",
        width: "100%"
    },
    row: {
        "&:nth-of-type(odd)": {
            backgroundColor: "rgb(189, 211, 222)"
        }
    }
}));

const BB8Table = ({ tableId, cols, data, classes, noDataComponent }: {
    tableId?: string;
    cols: IColProps[];
    data: any[];
    classes: any;
    noDataComponent?: JSX.Element;
}) => {
    return (
        <Table id={ tableId } className={ classes.table }>
            <TableHead>
                <TableRow>
                    { cols.map(({ title, headerCellProps }, index: number) => (
                        <CustomTableCell key={ index } { ...headerCellProps }>
                            { title }
                        </CustomTableCell>
                    )) }
                </TableRow>
            </TableHead>
            <BB8TableBody
                cols={ cols }
                data={ data }
                classes={ classes }
                noDataComponent={ noDataComponent }
            />
        </Table>
    );
};

const BB8TableBody = ({ cols, data, classes, noDataComponent }: {
    cols: IColProps[];
    data: any[];
    classes: any;
    noDataComponent?: JSX.Element;
}) => {
    if (!data || !data.length) {
        return (
            <TableBody>
                <TableRow>
                    <CustomTableCell colSpan={ cols.length }>
                        { noDataComponent ? noDataComponent : "No data" }
                    </CustomTableCell>
                </TableRow>
            </TableBody>
        );
    }
    return (
        <TableBody>
            { data.map((row, i: number) => {
                return (
                    <TableRow hover={ true } key={ i } className={ classes.row }>
                        { cols
                            .filter(({ id }) => id !== "colSpan")
                            .map(
                                (
                                    { id, style, tableCellProps, tableCellTemplate },
                                    index: number
                                ) => {
                                    return (
                                        <CustomTableCell
                                            key={ index }
                                            style={ style }
                                            { ...tableCellProps }
                                        >
                                            { tableCellTemplate
                                                ? tableCellTemplate({ id, data: path(id, row), row })
                                                : path(id, row) }
                                        </CustomTableCell>
                                    );
                                }
                            ) }
                    </TableRow>
                );
            }) }
        </TableBody>
    );
};

export default withStyles(styles)(BB8Table);
