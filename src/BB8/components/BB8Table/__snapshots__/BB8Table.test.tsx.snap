// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`BB8Table is truthy 1`] = `
Object {
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <table
        class="MuiTable-root"
      >
        <thead
          class="MuiTableHead-root"
        >
          <tr
            class="MuiTableRow-root MuiTableRow-head"
          />
        </thead>
        <tbody
          class="MuiTableBody-root"
        >
          <tr
            class="MuiTableRow-root"
          >
            <td
              class="MuiTableCell-root MuiTableCell-body WithStyles(ForwardRef(TableCell))-body-3"
              colspan="0"
            >
              No data
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </body>,
  "container": <div>
    <table
      class="MuiTable-root"
    >
      <thead
        class="MuiTableHead-root"
      >
        <tr
          class="MuiTableRow-root MuiTableRow-head"
        />
      </thead>
      <tbody
        class="MuiTableBody-root"
      >
        <tr
          class="MuiTableRow-root"
        >
          <td
            class="MuiTableCell-root MuiTableCell-body WithStyles(ForwardRef(TableCell))-body-3"
            colspan="0"
          >
            No data
          </td>
        </tr>
      </tbody>
    </table>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
