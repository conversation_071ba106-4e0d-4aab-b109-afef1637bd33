import CircularProgress, { CircularProgressProps } from "@material-ui/core/CircularProgress";
import Portal from "@material-ui/core/Portal";
import React from "react";
import "./styles.scss";

export type IBB8SpinnerProps = { show: boolean } & CircularProgressProps;

export enum BB8SpinnerSize {
    Big = 200
}

export class BB8Spinner extends React.Component<IBB8SpinnerProps> {
    private container?: HTMLElement;
    constructor(props: any) {
        super(props);
    }

    public componentWillMount() {
        this.container = document.createElement("div");
        this.container.id = "spinner-root";
        document.body.appendChild(this.container);
    }

    public render() {
        this.setupContainer();
        const { show, ...props } = this.props;
        return show ? (
            <Portal container={this.container}>
                <CircularProgress {...props as CircularProgressProps} />
            </Portal>
        ) : (
            <></>
        );
    }

    public componentWillUnmount() {
        if (this.container) {
            document.body.removeChild(this.container);
        }
    }

    private setupContainer() {
        if (this.container && this.container instanceof HTMLElement) {
            this.props.show
                ? this.container.classList.add("shown")
                : this.container.classList.remove("shown");
        }
    }
}

export default BB8Spinner;