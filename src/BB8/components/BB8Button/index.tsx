import Button, { ButtonProps } from '@material-ui/core/Button';
import { MuiThemeProvider } from '@material-ui/core/styles';
import React from 'react';
import buttonTheme from '../../theme/buttonTheme';

export interface IBB8Button {
    sh?: string
}

export const BB8Button = (props: IBB8Button & ButtonProps) => {
    return (
        <MuiThemeProvider theme={ buttonTheme }>
            <Button id={ props.id } { ...props }>
                { props.children }
            </Button>
        </MuiThemeProvider>
    );
}

export default BB8Button;