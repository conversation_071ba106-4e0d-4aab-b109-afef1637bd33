import { FormHelperText, withStyles, WithStyles } from "@material-ui/core";
import { Field, FieldProps } from "formik";
import React from "react";
import { formFieldStyles, IBB8FieldProps } from "../BB8FormField";
import StyledBB8Lookup, { IBB8LookupProps } from "../BB8Lookup";

const BB8FormLookupField = (props: IBB8FieldProps & IBB8LookupProps & WithStyles) => {
  const {
    id,
    classes,
    defaultValue,
    disabled,
    itemToString,
    loading,
    onSelect,
    renderOption,
    name,
    inputValue,
    error,
    status,
    touched,
    placeholder,
    validate,
    value,
    options,
    shouldOpenOnFocus,
    onInputChange,
    ...formProps
  } = props;
  return (
    <Field name={props.name} validate={props.validate} {...formProps}>
      {({ field, form }: FieldProps) => {
        return (
          <>
            <FormHelperText
              className={props.classes.fieldLabel}
              error={
                !props.disabled &&
                (!!props.error ||
                  !!(form.touched[field.name] && form.errors[field.name]))
              }
            >
              {props.label}
            </FormHelperText>
            <StyledBB8Lookup
              id={id}
              defaultValue={defaultValue}
              value={value}
              disabled={disabled}
              itemToString={itemToString}
              loading={loading}
              renderOption={renderOption}
              name={name}
              inputValue={inputValue}
              onInputBlur={form.handleBlur}
              onInputChange={onInputChange}
              onSelect={(value: any) => {
                if (props.valuePath) {
                  form.setFieldValue(
                    field.name,
                    value
                      ? value[props.valuePath]
                      : props.defaultValue
                      ? (props.defaultValue as any)[props.valuePath]
                      : undefined
                  );
                } else {
                  form.setFieldValue(field.name, value || props.defaultValue);
                }
              }}
              options={options}
              shouldOpenOnFocus={shouldOpenOnFocus}
              {...field}
            />
            <FormHelperText
              classes={{ contained: props.classes.formTextHelper }}
              error={
                !props.disabled &&
                (!!props.error ||
                  !!(form.touched[field.name] && form.errors[field.name]))
              }
            >
              {form.errors[field.name] && !!form.touched[field.name]
                ? form.errors[field.name]
                : ""}
            </FormHelperText>
          </>
        );
      }}
    </Field>
  );
};

export default withStyles(formFieldStyles)(BB8FormLookupField);
