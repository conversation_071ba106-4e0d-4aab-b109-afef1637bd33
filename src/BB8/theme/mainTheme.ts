import createMuiTheme, { Theme } from '@material-ui/core/styles/createMuiTheme'

const mainTheme = createMuiTheme({
  palette: {
    alert: {
      error: '#D30E8B',
      info: '',
      success: '#F2F7D6',
      warning: '#FFF1D9'
    },
    background: {
      default: '#fff',
      paper: '#fff'
    },
    error: {
      dark: '#D30E8B',
      light: '#F9E7F1',
      main: '#D30E8B'
    },
    grey: '#DE7426',
    primary: {
      dark: '#0A6FB3',
      light: '#61C1EE',
      main: '#1790CC'
    },
    secondary: {
      dark: '#D1E9F5',
      main: '#ccc'
    },
    status: {
      error: '#D30E8B',
      info: '',
      success: '#008000',
      warning: '#FFF1D9'
    } as any
  } as any,
  shape: {
    borderRadius: 4
  },
  typography: {
    fontFamily: 'Omnes',
  } as any
} as Theme)

export default mainTheme
