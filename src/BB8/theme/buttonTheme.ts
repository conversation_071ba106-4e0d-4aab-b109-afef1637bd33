import createMuiTheme from "@material-ui/core/styles/createMuiTheme";

const buttonTheme = createMuiTheme({
  overrides: {
    MuiButton: {
      contained: {
        color: "#fff"
      },
      root: {
        "&:active": {
          backgroundColor: "#2a669f",
          borderColor: "#005cbf",
          boxShadow: "none"
        },
        "&:focus": {
          boxShadow: "0 0 0 0.1rem rgba(0,123,255,.5)"
        },
        "&:hover": {
          backgroundColor: "#4583b9"
        },
        borderRadius: "2px",
        fontFamily: "Omnes",
        textTransform: "capitalize"
      },
      text: {
        "&:hover": {
          backgroundColor: "transparent"
        }
      },
    }
  },

  palette: {
    primary: {
      main: "#2e6fae"
    },
    secondary: {
      main: "#ccc"
    }
  },
  typography: {
  },
});

export default buttonTheme;
