// tslint:disable:only-arrow-functions
import "@testing-library/jest-dom";
import "@testing-library/react";
import { queryByAttribute } from '@testing-library/react';
import { atob } from "abab";
// tslint:disable:object-literal-shorthand
// tslint:disable:object-literal-sort-keys
// tslint:disable:no-empty
import "core-js/es/map";
import "core-js/es/set";
import "raf/polyfill";

global.atob = atob;

window.matchMedia =
    window.matchMedia ||
    function () {
      return {
        matches: false,
        addListener: function () {
        },
        removeListener: function () {
        }
      };
    };

window.requestAnimationFrame =
    window.requestAnimationFrame ||
    function (callback) {
      setTimeout(callback, 0);
    };

const mockResponse = jest.fn();
Object.defineProperty(window, 'location', {
  value: {
    hash: {
      endsWith: mockResponse,
      includes: mockResponse,
    },
    replace: mockResponse,
  },
  writable: true,
});

HTMLCanvasElement.prototype.getContext = jest.fn();

export const getById = queryByAttribute.bind(null, 'id');