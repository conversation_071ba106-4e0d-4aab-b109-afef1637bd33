import { get } from "lodash";
import { IStaticFilterOption } from "./IStaticFilterOption";

export const OFFER_LIMITATION_TYPES: IStaticFilterOption[] = [
  {
    name: "No Limit",
    value: "noLimit"
  },
  {
    name: "One per Collector Number",
    value: "perCollector"
  },
  {
    name: "One per Collector Number per Transaction",
    value: "perCollectorPerTransaction"
  },
  {
    name: "One per Collector per Day",
    value: "perCollectorPerDay"
  },
  {
    name: "Custom",
    value: "custom"
  }
];

export function getOfferLimitationTypeName(value?: string) {
  return get(OFFER_LIMITATION_TYPES.find(pt => pt.value === value), "name");
}
