import { get } from "lodash";
import { IStaticFilterOption } from "./IStaticFilterOption";

export const OFFER_TYPES: IStaticFilterOption[] = [
  {
    name: "Buy",
    value: "buy"
  },
  {
    name: "Spend",
    value: "spend"
  },
  {
    name: "Base",
    value: "base"
  },
  {
    name: "AM Cash Earn",
    value: "amCashEarn"
  },
  {
    name: "AM Cash Discount",
    value: "amCashDiscount"
  },
  {
    name: "Custom",
    value: "custom"
  }
];

export function getOfferTypeName(value?: string) {
  return get(OFFER_TYPES.find(pt => pt.value === value), "name");
}
