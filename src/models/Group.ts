import { IPartner } from "./Partner";
import { IRbacUser } from "./User";
export interface IHavePartners {
  partners: IPartner[];
}

export interface IHaveName {
  name: string;
}

export interface IGroupPartnerMappings {
  createdBy: string;
  createdDate: string;
  groupId: number;
  partnerId: string;
}

export interface IGroup extends IHavePartners, IHaveName {
  id: string;
  coversAllPartners?: boolean;
  createdDate?: string;
  updatedDate?: string;
  users?: IRbacUser[];
  version?: number;
  partnerMappings?: IGroupPartnerMappings[];
}
