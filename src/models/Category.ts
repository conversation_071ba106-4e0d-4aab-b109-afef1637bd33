import { ILocalizedObject } from "../validation/validator";
import { get, find } from "lodash";

export interface IOfferCategory {
  id: string;
  translations: ILocalizedObject;
  childCategories?: IOfferCategory[];
}

export interface IOfferCategoryResponse {
  results: IOfferCategory[];
}

export interface IOffersCounts {
  draft: number;
  changesPending: number;
  staged: number;
  live: number;
  expired: number;
  disabled: number;
  total: number;
}