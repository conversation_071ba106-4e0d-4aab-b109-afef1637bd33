import { objectToQueryString, queryStringToObject } from "../shared/helpers";

export interface IBulkFilter {
  [key: string]: any;
  bulkId?: string;

  toQueryString?: () => string | undefined;
}

export class BulkRequestParams implements IBulkFilter {
  [key: string]: any;

  public static fromQueryString(qs: string): IBulkFilter {
    if (!qs) {
      return new BulkRequestParams();
    }
    const params = queryStringToObject(qs);
    return new BulkRequestParams(params);
  }

  public partnerId: string | undefined;

  constructor(params?: any) {
    if (params) {
      this.partnerId = params.partnerId;
    }
  }

  public toQueryString() {
    return objectToQueryString({
      partnerId: this.partnerId
    });
  }
}
