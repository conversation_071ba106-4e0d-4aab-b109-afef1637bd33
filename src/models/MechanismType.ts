import { IStaticFilterOption } from "./IStaticFilterOption";

export const MECHANISM_TYPES: IStaticFilterOption[] = [
  {
    name: "No Action",
    value: "noAction"
  },
  {
    name: "Barcode UPC",
    value: "barcodeUPC"
  },
  {
    name: "Barcode EAN",
    value: "barcodeEAN"
  },
  {
    name: "Barcode 39",
    value: "barcodeCODE39"
  },
  {
    name: "Barcode 128",
    value: "barcodeCODE128"
  },
  {
    name: "PLU",
    value: "plu"
  },
  {
    name: "Coupon Code",
    value: "couponCode"
  },
  {
    name: "But<PERSON>",
    value: "button"
  },
  {
    name: "Opt In",
    value: "optIn"
  },
  {
    name: "Scan Receipt",
    value: "scanReceipt"
  }
];
