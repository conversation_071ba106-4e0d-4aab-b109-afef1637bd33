import moment from "moment";
import { DATE_TIME_FORMAT } from "../features/offer-submission/utils";

export interface ITargetedOfferFilter {
  collectorNumber: string;
  date: string;
  partnerId: string;
}

export class TargetedOfferFilter implements ITargetedOfferFilter {
  public collectorNumber: string;
  public date: string;
  public partnerId: string;

  constructor({
    collectorNumber,
    date,
    partnerId
  }: {
    collectorNumber: string;
    date: string | Date;
    partnerId: string;
  }) {
    this.collectorNumber = collectorNumber;
    if (typeof date === "string") {
      this.date = date;
    } else {
      this.date = moment(date).format(DATE_TIME_FORMAT);
    }
    this.partnerId = partnerId;
  }

  public toQueryString() {
    return `collectorNumber=${this.collectorNumber}&date=${moment(
      this.date
    ).format("YYYY-MM-DD")}&partnerId=${this.partnerId}`;
  }
}
