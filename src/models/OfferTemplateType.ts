import { get } from "lodash";
import { IStaticFilterOption } from "./IStaticFilterOption";

export const OfferTemplateOptions = [
  'buy-product',
  'spend-storewide',
  'spend-category',
  'buy-fuel',
  'buy-perProduct',
  'spend-perProduct',
  'buy-perUnit',
  'base-perDollar',
  'base-frequency',
  'base-storewide',
  'amCashEarn-cashRedemption',
  'amCashDiscount-cashDiscount',
  'custom-custom'
];

export const featureFlagOfferTemplateOptions:string[] = [
  'buy-perProduct',
  'spend-perProduct',
  'buy-perUnit',
  'base-perDollar',
  'base-frequency',
  'base-storewide'
]

export const OFFER_TEMPLATE_GROUPS_DIVIDERS = ["buy-fuel","base-storewide", "amCashDiscount-cashDiscount"];

export const OFFER_TEMPLATE_TYPES: IStaticFilterOption[] = [
  {
    name: "Buy [quantity] [product(s)] ...*",
    value: "buy-product"
  },
  {
    name: "Spend $[amount]+ on almost anything ...*",
    value: "spend-storewide"
  },
  {
    name: "Spend $[amount]+ on [category] ...*",
    value: "spend-category"
  },
  {
    name: "Fuel up with [volume] L or more of [fuel grade(s)] ...*",
    value: "buy-fuel"
  },
  {
    name: "Get Bonus Miles | Use Cash Miles for $ towards your purchases*",
    value: "amCashEarn-cashRedemption"
  },
  {
    name: "Get Cash Miles off | Use fewer Cash Miles for $ towards your purchases*",
    value: "amCashDiscount-cashDiscount"
  },
  {
    name: "For every [product] purchased",
    value: "buy-perProduct"
  },
  {
    name: "For every $[amount] spent on [product]",
    value: "spend-perProduct"
  },
  {
    name: "For every X [quantity] of [product] purchased",
    value: "buy-perUnit"
  },
  {
    name: "For every $[amount] purchased, earn Miles",
    value: "base-perDollar"
  },
  {
    name: "For every [quantity] receipt(s) scanned, earn Miles",
    value: "base-frequency"
  },
  {
    name: "Spend at least $[amount], earn Miles",
    value: "base-storewide"
  },
  {
    name: "Custom Qualifier (Not Recommended)",
    value: "custom-custom"
  }
];
