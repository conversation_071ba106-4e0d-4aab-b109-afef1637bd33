import { IStaticFilterOption } from "./IStaticFilterOption";

export const REGIONS: IStaticFilterOption[] = [
  {
    name: "British Columbia",
    value: "BC"
  },
  {
    name: "Alberta",
    value: "AB"
  },
  {
    name: "Saskatchewan",
    value: "SK"
  },
  {
    name: "Manitoba",
    value: "MB"
  },
  {
    name: "Ontario",
    value: "ON"
  },
  {
    name: "Quebec",
    value: "QC"
  },
  {
    name: "Thunder Bay",
    value: "TB"
  },
  {
    name: "New Brunswick",
    value: "NB"
  },
  {
    name: "Nova Scotia",
    value: "NS"
  },
  {
    name: "Prince Edward Island",
    value: "PE"
  },
  {
    name: "Newfoundland and Labrador",
    value: "NL"
  },
  {
    name: "Northwest Territories",
    value: "NT"
  },
  {
    name: "Nunavut",
    value: "NU"
  },
  {
    name: "Yukon",
    value: "YT"
  }
];
