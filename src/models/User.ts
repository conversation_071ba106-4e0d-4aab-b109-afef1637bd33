import { IGroup, IHavePartners } from "./Group";
import { IPartner } from "./Partner";
import { IRole } from "./Role";

export interface IUser {
  name: string;
  email: string;
  permissions: string[];
  role: string;
  exp: number;
}

export interface IRbacUser extends IHavePartners {
  id: string;
  fullName: string;
  isDisabled: boolean;
  lastLoginTs: Date | string;
  createdTs: Date | string;
  lastUpdatedTs: Date | string;
  version: number;
  role: IRole;
  groups: IGroup[];
}
