import { IStaticFilterOption } from "./IStaticFilterOption";

export const QUALIFIER_TYPES: IStaticFilterOption[] = [
  {
    name: "Product",
    value: "product"
  },
  {
    name: "Fuel",
    value: "fuel"
  },
  {
    name: "Storewide",
    value: "storewide"
  },
  {
    name: "Category",
    value: "category"
  },
  {
    name: "Cash Redemption",
    value: "cashRedemption"
  },
  {
    name: "Cash Discount",
    value: "cashDiscount"
  },
  {
    name: "Custom",
    value: "custom"
  },
  {
    name: "Per Product",
    value: "perProduct"
  },
  {
    name: "Per Unit",
    value: "perUnit"
  },
  {
    name: "Per Dollar",
    value: "perDollar"
  },
  {
    name: "Frequency",
    value: "frequency"
  }  
];
