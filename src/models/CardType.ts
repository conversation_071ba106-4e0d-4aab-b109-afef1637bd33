import { IStaticFilterOption } from "./IStaticFilterOption";
import {get} from "lodash";

export const CARD_TYPES: IStaticFilterOption[] = [
    {
        name: "Non-BMO Mastercard",
        value: "NonBmoMastercard",
    },
    {
        name: "BMO Mastercard",
        value: "BmoMastercard",
    },
    {
        name: "BMO Debit",
        value: "BmoDebit",
    },
];

export function getCardTypeName(value?: string) {
return get(CARD_TYPES.find(pt => pt.value === value), "name");
}
