import { get } from "lodash";
import { QUALIFIER_TYPES } from "./QualifierType";
import { OFFER_TYPES } from "./OfferType";
import { AWARD_TYPES } from "./AwardType";
import { OFFER_TEMPLATE_TYPES } from "./OfferTemplateType";
import {PROGRAM_TYPES} from "./ProgramType";

export interface IStaticFilterOption {
  name: string;
  value: string;
}

export function getStaticFilterName(
  propertyName: "offerType" | "qualifier" | "awardType" | "offerTemplate" | "programType",
  value?: string
) {
  switch (propertyName) {
    case "qualifier":
      return get(QUALIFIER_TYPES.find(pt => pt.value === value), "name");
    case "offerType":
      return get(OFFER_TYPES.find(pt => pt.value === value), "name");
    case "awardType":
      return get(AWARD_TYPES.find(pt => pt.value === value), "name");
    case "offerTemplate":
      return get(OFFER_TEMPLATE_TYPES.find(pt => pt.value === value), "name");
    case "programType":
      return get(PROGRAM_TYPES.find(pt => pt.value === value), "name");
  }
  return;
}
