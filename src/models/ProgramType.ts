import { IStaticFilterOption } from "./IStaticFilterOption";
import {get} from "lodash";

export const PROGRAM_TYPES: IStaticFilterOption[] = [
    {
        name: `Traditional Core`,
        value: "traditionalcore"
    },
    {
        name:  `Card Linked Offers`,
        value: "cardlinked"
    },
    {
        name: `Airmilesshops`,
        value: "airmilesshops"
    },
    {
        name: `BMO Pre-approval`,
        value: "bmopreapp"
    },
    {
        name: `AM Receipts`,
        value: "amreceipts"
    }
];

export function getProgramTypeName(value?: string) {
    return get(PROGRAM_TYPES.find(pt => pt.value === value), "name");
}