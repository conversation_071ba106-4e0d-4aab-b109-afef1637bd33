import { get } from "lodash";

export interface IDisplayPriority {
  name: string;
  value: number;
}

export const DISPLAY_PRIORITIES = [
  {
    name: "1 - None (0)",
    value: 0
  },
  {
    name: "2 - Low Priority (250)",
    value: 250
  },
  {
    name: "3 - Moderate Priority (500)",
    value: 500
  },
  {
    name: "4 - High Priority (750)",
    value: 750
  },
  {
    name: "5 - Highest Priority (1000)",
    value: 1000
  }
];

export function getDisplayPriority(value?: number) {
  return get(DISPLAY_PRIORITIES.find(pt => pt.value === value), "name");
}
