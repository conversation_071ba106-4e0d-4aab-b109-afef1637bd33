{"$id": "PostOfferFormObject.json", "$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"id": {"type": "string", "format": "uuid", "maxLength": 36}, "duplicatedFrom": {"type": "string", "format": "uuid", "maxLength": 36}, "partnerOfferId": {"type": "string", "maxLength": 36}, "publishedBy": {"type": "string", "maxLength": 256}, "createdAt": {"type": "string", "format": "date-time", "maxLength": 256}, "createdBy": {"type": "string", "maxLength": 256}, "updatedBy": {"type": "string", "maxLength": 256}, "status": {"type": "string", "maxLength": 256}, "partnerId": {"section": "partner", "title": "Partner", "type": "string", "format": "uuid", "maxLength": 36}, "partnerName": {"section": "partner", "type": "string", "maxLength": 256}, "partnerBaseEarnRate": {"type": "number", "maximum": 999}, "baseCashRedemption": {"type": "number", "maximum": 999, "default": 95}, "displayDate": {"type": "string", "format": "date-time", "maxLength": 256}, "startDate": {"type": "string", "format": "date-time", "maxLength": 256}, "endDate": {"type": "string", "format": "date-time", "maxLength": 256}, "firstQualificationDate": {"type": "string", "format": "date-time", "maxLength": 256}, "lastQualificationDate": {"type": "string", "format": "date-time", "maxLength": 256}, "eligibilityDuration": {"type": "number", "maximum": 999, "minimum": 1}, "eventBasedOffer": {"type": "boolean", "default": false}, "eligibilityDurationUnit": {"type": "string", "enum": ["days", "hours", "minutes"]}, "programType": {"type": "string", "enum": ["traditionalcore", "cardlinked", "airmilesshops", "bmopreapp", "amreceipts"], "default": "traditionalcore"}, "cardType": {"type": "array", "items": {"type": "string", "enum": ["NonBmoMastercard", "BmoMastercard", "BmoDebit"]}}, "usageLimit": {"type": "number", "maximum": 999, "minimum": 1}, "retailerGroupId": {"type": "array", "items": {"type": "string", "format": "uuid"}}, "offerType": {"type": "string", "enum": ["buy", "spend", "base", "amCashEarn", "amCashDiscount", "custom"]}, "qualifier": {"type": "string", "enum": ["product", "fuel", "storewide", "category", "cashRedemption", "cashDiscount", "custom", "perProduct", "perUnit", "<PERSON><PERSON><PERSON><PERSON>", "frequency"]}, "awardType": {"type": "string", "enum": ["flatMiles", "multiplierMiles", "flatDiscount", "percentDiscount", "cashDiscount", "custom"]}, "awardShort": {"type": "object", "title": "Award Short", "properties": {"en-US": {"type": "string", "maxLength": 256}, "fr-CA": {"type": "string", "maxLength": 256}}, "additionalProperties": false, "required": ["en-US"]}, "qualifierShort": {"type": "object", "title": "Qualifier Short", "properties": {"en-US": {"type": "string", "maxLength": 256}, "fr-CA": {"type": "string", "maxLength": 256}}, "additionalProperties": false, "required": ["en-US"]}, "sponsorCode": {"type": "string"}, "issuanceCode": {"type": "string"}, "flagSponsorCode": {"type": "boolean"}, "partnerInternalAirmilesCalculated": {"type": "boolean"}, "campaignCode": {"type": "string", "minLength": 1, "maxLength": 50, "pattern": "^(?!\\s*$).+"}, "image": {"type": "object", "properties": {"en-US": {"type": "object", "properties": {"path": {"type": "string", "$comment": "TODO: need to set maxLength to be 2083, but causing issues with frontend due to blobUrl being used for validation which is greater than 2083 characters"}}, "required": ["path"], "additionalProperties": false}, "fr-CA": {"type": "object", "properties": {"path": {"type": "string", "$comment": "TODO: need to set maxLength to be 2083, but causing issues with frontend due to blobUrl being used for validation which is greater than 2083 characters"}}, "required": ["path"], "additionalProperties": false}}, "required": ["en-US"]}, "cashierInstruction": {"type": "object", "title": "Cashier Instruction", "properties": {"en-US": {"type": "string", "maxLength": 256}, "fr-CA": {"type": "string", "maxLength": 256}}, "required": ["en-US"], "additionalProperties": false}, "mechanisms": {"title": "Mechanisms", "type": "array", "items": {"$ref": "MechanismObject.json"}, "minItems": 1, "maxItems": 3, "additionalItems": false}, "tiers": {"type": "array", "items": {"$ref": "TierObject.json"}, "minItems": 1, "maxItems": 3, "additionalItems": false}, "displayPriority": {"type": "number", "$comment": "priority of offers within the partner", "minimum": 0, "maximum": 9999, "default": 0}, "regions": {"type": "array", "items": {"type": "string", "enum": ["AB", "BC", "MB", "NB", "NL", "NS", "NT", "NU", "ON", "PE", "QC", "SK", "YT", "TB", "ALL"], "uniqueItems": true}}, "offerLimitation": {"type": "string", "enum": ["noLimit", "perCollector", "perCollectorPerTransaction", "perCollectorPerDay", "custom"], "default": "noLimit"}, "offerLimitationText": {"type": "object", "title": "Offer Limitation Text", "properties": {"en-US": {"type": "string", "maxLength": 256}, "fr-CA": {"type": "string", "maxLength": 256}}, "required": ["en-US"], "additionalProperties": false}, "includedLocations": {"type": "array", "title": "Included locations", "items": {"type": "object", "title": "Included location", "properties": {"en-US": {"type": "string", "maxLength": 50}, "fr-CA": {"type": "string", "maxLength": 50}}, "required": ["en-US"], "additionalProperties": false}}, "excludedLocations": {"type": "array", "title": "Excluded locations", "items": {"type": "object", "title": "Excluded location", "properties": {"en-US": {"type": "string", "maxLength": 50}, "fr-CA": {"type": "string", "maxLength": 50}}, "required": ["en-US"], "additionalProperties": false}}, "includedBanners": {"type": "array", "title": "Included banners", "items": {"title": "Included banner", "type": "object", "properties": {"en-US": {"type": "string", "maxLength": 50}, "fr-CA": {"type": "string", "maxLength": 50}}, "required": ["en-US"], "additionalProperties": false}}, "excludedBanners": {"type": "array", "title": "Excluded banners", "items": {"type": "object", "title": "Excluded banner", "properties": {"en-US": {"type": "string", "maxLength": 50}, "fr-CA": {"type": "string", "maxLength": 50}}, "required": ["en-US"], "additionalProperties": false}}, "canBeCombined": {"type": "boolean", "default": true}, "combinationsText": {"type": "object", "title": "Combinations Text", "properties": {"en-US": {"type": "string", "maxLength": 256}, "fr-CA": {"type": "string", "maxLength": 256}}, "required": ["en-US"], "additionalProperties": false}, "ctaLabel": {"type": "object", "title": "CTA Label", "properties": {"en-US": {"type": "string", "maxLength": 256}, "fr-CA": {"type": "string", "maxLength": 256}}}, "ctaUrl": {"type": "object", "title": "CTA URL", "properties": {"en-US": {"type": "string", "format": "uri", "maxLength": 2083, "pattern": "^(https?):"}, "fr-CA": {"type": "string", "format": "uri", "maxLength": 2083, "pattern": "^(https?):"}}}, "exclusions": {"type": "object", "title": "Excluded Locations", "properties": {"en-US": {"type": "string", "maxLength": 500}, "fr-CA": {"type": "string", "maxLength": 500}}, "required": ["en-US"], "additionalProperties": false}, "availability": {"type": "array", "items": {"type": "string", "enum": ["inStore", "online"]}, "uniqueItems": true}, "partnerUrl": {"title": "Partner URL", "type": "object", "properties": {"en-US": {"type": "string", "format": "uri", "maxLength": 2083, "pattern": "^(https?):"}, "fr-CA": {"type": "string", "format": "uri", "maxLength": 2083, "pattern": "^(https?):"}}, "required": ["en-US"], "additionalProperties": false}, "daysToApply": {"type": "number", "minimum": 1, "maximum": 365}, "trademarkInfo": {"title": "Additional Trademark Information", "type": "object", "properties": {"en-US": {"type": "string", "maxLength": 300}, "fr-CA": {"type": "string", "maxLength": 300}}, "required": ["en-US"], "additionalProperties": false}, "description": {"title": "Add Description", "type": "object", "properties": {"en-US": {"type": "string", "maxLength": 256}, "fr-CA": {"type": "string", "maxLength": 256}}, "required": ["en-US"], "additionalProperties": false}, "tags": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "partnerLegalName": {"title": "Partner Legal Name", "type": "object", "properties": {"en-US": {"type": "string", "maxLength": 50}, "fr-CA": {"type": "string", "maxLength": 50}}, "required": ["en-US"], "additionalProperties": false}, "hasCustomLegal": {"type": "boolean", "default": false}, "active": {"type": "boolean", "default": true}, "massOffer": {"type": "boolean"}, "audience": {"type": "string", "enum": ["mass", "targeted", "event"]}, "offerCategory1": {"type": "string", "format": "uuid", "maxLength": 36}, "offerCategory2": {"type": "string", "format": "uuid", "maxLength": 36}, "offerCategory3": {"type": "string", "format": "uuid", "maxLength": 36}, "productName": {"type": "string", "maxLength": 256}, "productBrand": {"type": "string", "maxLength": 256}, "legalText": {"title": "Add Custom Legal Text", "type": "object", "$comment": "Generated by API - TODO: Check if this can be edited manually", "properties": {"en-US": {"type": "string", "maxLength": 20000}, "fr-CA": {"type": "string", "maxLength": 20000}}, "required": ["en-US"], "additionalProperties": false}, "programPriority": {"type": "number", "$comment": "priority of offers globally", "minimum": 0, "maximum": 1000, "default": 0}}, "additionalProperties": false, "dependencies": {"programType": {"oneOf": [{"properties": {"programType": {"enum": ["cardlinked"]}, "cardType": {"type": "array", "items": {"type": "string", "enum": ["NonBmoMastercard", "BmoMastercard", "BmoDebit"]}, "minItems": 1}}, "required": ["cardType"]}, {"properties": {"programType": {"not": {"enum": ["cardlinked"]}}, "cardType": {"type": ["null", "array"], "items": {"type": "string", "enum": ["NonBmoMastercard", "BmoMastercard", "BmoDebit"]}}}}]}, "awardType": {"allOf": [{"if": {"properties": {"awardType": {"enum": ["flatMiles"]}}}, "then": {"properties": {"tiers": {"items": {"properties": {"awardValue": {"type": "number", "minimum": 1, "maximum": 99999}}}}}}}, {"if": {"properties": {"awardType": {"enum": ["flatDiscount"]}}}, "then": {"properties": {"tiers": {"items": {"properties": {"awardValue": {"type": "number", "minimum": 0.01, "maximum": 99999.99}}}}}}}, {"if": {"properties": {"awardType": {"enum": ["multiplierMiles", "percentDiscount"]}}}, "then": {"properties": {"tiers": {"items": {"properties": {"awardValue": {"type": "number", "minimum": 1, "maximum": 99}}}}}}}]}, "qualifier": {"allOf": [{"if": {"properties": {"qualifier": {"enum": ["storewide", "category", "perUnit", "<PERSON><PERSON><PERSON><PERSON>", "frequency"]}}}, "then": {"properties": {"tiers": {"items": {"properties": {"qualifierValue": {"type": "number", "minimum": 0.01, "maximum": 99999.99}}}}}}}, {"if": {"properties": {"qualifier": {"enum": ["product", "fuel"]}}}, "then": {"properties": {"tiers": {"items": {"properties": {"qualifierValue": {"type": "number", "minimum": 1, "maximum": 99}}}}}}}, {"if": {"properties": {"qualifier": {"enum": ["fuel"]}}}, "then": {"properties": {"tiers": {"items": {"properties": {"qualifierFrequency": {"type": "number", "minimum": 1, "maximum": 10}}}}}}}, {"if": {"properties": {"qualifier": {"not": {"enum": ["fuel"]}}}}, "then": {"properties": {"tiers": {"items": {"properties": {"qualifierFrequency": {"type": "number"}}}}}}}]}, "offerType": {"allOf": [{"if": {"properties": {"offerType": {"enum": ["buy"]}}}, "then": {"properties": {"qualifier": {"enum": ["product", "fuel", "perProduct", "perUnit"]}, "awardType": {"enum": ["flatMiles", "multiplierMiles", "flatDiscount", "percentDiscount", "custom"]}}}}, {"if": {"properties": {"offerType": {"enum": ["buy"]}}}, "then": {"properties": {"qualifier": {"enum": ["product", "fuel", "perProduct", "perUnit"]}, "awardType": {"enum": ["flatMiles", "multiplierMiles", "flatDiscount", "percentDiscount", "custom"]}}}}, {"if": {"properties": {"offerType": {"enum": ["spend"]}}}, "then": {"properties": {"qualifier": {"enum": ["storewide", "category", "perProduct"]}, "awardType": {"enum": ["flatMiles", "multiplierMiles", "flatDiscount", "percentDiscount", "custom"]}}}}, {"if": {"properties": {"offerType": {"enum": ["base"]}}}, "then": {"properties": {"qualifier": {"enum": ["storewide", "<PERSON><PERSON><PERSON><PERSON>", "frequency"]}, "awardType": {"enum": ["flatMiles", "multiplierMiles", "flatDiscount", "percentDiscount", "custom"]}}}}, {"if": {"properties": {"offerType": {"enum": ["amCashEarn"]}}}, "then": {"properties": {"qualifier": {"enum": ["cashRedemption"]}, "awardType": {"enum": ["flatMiles"]}}}}, {"if": {"properties": {"offerType": {"enum": ["amCashDiscount"]}}}, "then": {"properties": {"qualifier": {"enum": ["cashDiscount"]}, "awardType": {"enum": ["cashDiscount"]}}}}, {"if": {"properties": {"offerType": {"enum": ["custom"]}}}, "then": {"properties": {"qualifier": {"enum": ["product", "fuel", "storewide", "category", "perProduct", "perUnit", "<PERSON><PERSON><PERSON><PERSON>", "frequency", "custom"]}, "awardType": {"enum": ["flatMiles", "multiplierMiles", "flatDiscount", "percentDiscount", "custom"]}}}}, {"if": {"properties": {"offerType": {"enum": ["spend"]}, "qualifier": {"enum": ["perProduct"]}}}, "then": {"properties": {"tiers": {"items": {"properties": {"qualifierValue": {"type": "number", "minimum": 1, "maximum": 99}}, "required": ["qualifierValue"]}}}}}, {"if": {"properties": {"awardType": {"enum": ["custom"]}, "tiers": {"minItems": 2}}}, "then": {"required": ["awardShort"]}}, {"if": {"properties": {"qualifier": {"enum": ["custom"]}, "tiers": {"minItems": 2}}}, "then": {"required": ["qualifierShort"]}}, {"if": {"properties": {"awardType": {"enum": ["custom"]}}}, "then": {"properties": {"tiers": {"items": {"required": ["awardLong"]}}}}}, {"if": {"properties": {"awardType": {"enum": ["multiplierMiles"]}}}, "then": {"required": ["partnerBaseEarnRate"]}}, {"if": {"properties": {"awardType": {"not": {"enum": ["custom"]}}}}, "then": {"properties": {"tiers": {"items": {"required": ["awardValue"]}}}}}, {"if": {"properties": {"offerLimitation": {"enum": ["custom"]}}}, "then": {"required": ["offerLimitationText"]}}, {"if": {"properties": {"availability": {"contains": {"const": "online"}}}, "required": ["availability"]}, "then": {"required": ["partnerUrl"]}}, {"if": {"properties": {"hasCustomLegal": {"const": true}}}, "then": {"required": ["legalText"]}}, {"if": {"properties": {"qualifier": {"not": {"enum": ["custom", "perProduct"]}}}}, "then": {"properties": {"tiers": {"items": {"required": ["qualifierValue"]}}}}}, {"if": {"properties": {"qualifier": {"not": {"enum": ["custom"]}}}}, "then": {"properties": {"tiers": {"items": {"required": ["qualifierFrequency"]}}}}}, {"if": {"properties": {"qualifier": {"enum": ["custom"]}}}, "then": {"properties": {"tiers": {"items": {"required": ["qualifierLong"]}}}}}, {"if": {"properties": {"qualifier": {"enum": ["category", "product", "fuel"]}}}, "then": {"properties": {"tiers": {"items": {"required": ["content"]}}}}}, {"if": {"properties": {"offerType": {"enum": ["amCashDiscount"]}}}, "then": {"properties": {"tiers": {"items": {"properties": {"awardValue": {"minimum": 1}}}}}}}, {"if": {"not": {"properties": {"qualifier": {"enum": ["storewide", "category", "cashRedemption", "cashDiscount"]}}}}, "then": {"required": ["image"]}}]}, "mechanisms": {"allOf": [{"if": {"properties": {"mechanisms": {"minItems": 2}}}, "then": {"properties": {"mechanisms": {"items": {"properties": {"mechanismType": {"enum": ["barcodeUPC", "barcodeCODE128", "plu", "couponCode", "button", "load+go", "optIn", "scanReceipt"]}}}}}}}, {"if": {"properties": {"mechanisms": {"items": {"properties": {"mechanismType": {"enum": ["optIn"]}}}}}}, "then": {"required": ["campaignCode"]}}]}, "tierContent": {"allOf": [{"if": {"properties": {"qualifier": {"enum": ["product"]}}}, "then": {"properties": {"tiers": {"items": {"properties": {"content": {"maxItems": 5}}}}}}}, {"if": {"properties": {"qualifier": {"enum": ["category", "fuel"]}}}, "then": {"properties": {"tiers": {"items": {"properties": {"content": {"maxItems": 3}}}}}}}]}, "massOffer": {"allOf": [{"if": {"properties": {"massOffer": {"const": false}}}, "then": {"required": ["campaignCode"]}}]}, "eventBasedOffer": {"allOf": [{"if": {"properties": {"eventBasedOffer": {"const": true}}}, "then": {"required": ["eligibilityDuration"]}}]}}, "required": ["partnerId", "partner<PERSON>ame", "offerType", "qualifier", "awardType", "displayDate", "startDate", "endDate", "mechanisms", "eventBasedOffer", "tiers", "regions", "massOffer"]}