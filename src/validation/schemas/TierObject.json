{"$id": "TierObject.json", "$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"awardValue": {"type": "number", "minimum": 0.01, "maximum": 99999.99}, "qualifierValue": {"type": "number", "minimum": 0.01, "maximum": 99999.99}, "qualifierFrequency": {"type": "number", "minimum": 1, "maximum": 10}, "content": {"type": "array", "items": {"type": "object", "properties": {"en-US": {"type": "string", "maxLength": 100}, "fr-CA": {"type": "string", "maxLength": 100}, "productSKU": {"type": "string", "maxLength": 18}, "skus": {"type": "array", "items": {"type": "string"}}, "upc": {"type": "string", "maxLength": 18}, "masterProduct": {"type": "boolean", "default": false}}, "required": ["en-US"]}}, "qualifierLong": {"type": "object", "title": "Qualifier Long", "$comment": "Generated by the API", "properties": {"en-US": {"type": "string", "maxLength": 256}, "fr-CA": {"type": "string", "maxLength": 256}}, "required": ["en-US"]}, "awardLong": {"type": "object", "title": "Award Long", "$comment": "Generated by the API", "properties": {"en-US": {"type": "string", "maxLength": 256}, "fr-CA": {"type": "string", "maxLength": 256}}, "required": ["en-US"]}}, "required": [], "additionalProperties": false}