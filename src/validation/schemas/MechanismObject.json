{"$id": "MechanismObject.json", "$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"mechanismType": {"type": "string", "enum": ["noAction", "barcodeUPC", "barcodeCODE128", "plu", "couponCode", "button", "load+go", "optIn", "scanReceipt"]}, "mechanismLabel": {"type": "object", "title": "Mechanism Label", "properties": {"en-US": {"type": "string", "maxLength": 24}, "fr-CA": {"type": "string", "maxLength": 24}}, "required": ["en-US"], "additionalProperties": false}, "mechanismTitle": {"type": "object", "title": "Mechanism Title", "properties": {"en-US": {"type": "string", "maxLength": 50}, "fr-CA": {"type": "string", "maxLength": 50}}, "additionalProperties": false}, "mechanismText": {"type": "object", "title": "Mechanism Text", "properties": {"en-US": {"type": "string", "maxLength": 50}, "fr-CA": {"type": "string", "maxLength": 50}}, "additionalProperties": false}, "mechanismValue": {"type": "object", "title": "Mechanism Value", "properties": {"en-US": {"type": "string", "maxLength": 2083}, "fr-CA": {"type": "string", "maxLength": 2083}}, "required": ["en-US"], "additionalProperties": false}}, "required": ["mechanismType"], "additionalProperties": false, "allOf": [{"if": {"properties": {"mechanismType": {"enum": ["barcodeUPC", "barcodeCODE128", "plu", "couponCode", "button"]}}}, "then": {"required": ["mechanismValue"]}}, {"if": {"properties": {"mechanismType": {"enum": ["button"]}}}, "then": {"required": ["mechanismLabel"], "properties": {"mechanismValue": {"properties": {"en-US": {"format": "uri", "pattern": "^(https?):", "maxLength": 2083}, "fr-CA": {"format": "uri", "pattern": "^(https?):", "maxLength": 2083}}}}}}, {"if": {"properties": {"mechanismType": {"enum": ["barcodeUPC"]}}}, "then": {"properties": {"mechanismValue": {"properties": {"en-US": {"maxLength": 12, "minLength": 11}, "fr-CA": {"maxLength": 12, "minLength": 11}}}}}}, {"if": {"properties": {"mechanismType": {"enum": ["barcodeCODE128"]}}}, "then": {"properties": {"mechanismValue": {"properties": {"en-US": {"maxLength": 20, "minLength": 5}, "fr-CA": {"maxLength": 20, "minLength": 5}}}}}}, {"if": {"properties": {"mechanismType": {"enum": ["plu"]}}}, "then": {"properties": {"mechanismValue": {"properties": {"en-US": {"maxLength": 5, "minLength": 4}, "fr-CA": {"maxLength": 5, "minLength": 4}}}}}}, {"if": {"properties": {"mechanismType": {"enum": ["couponCode"]}}}, "then": {"properties": {"mechanismValue": {"properties": {"en-US": {"maxLength": 25, "minLength": 1}, "fr-CA": {"maxLength": 25, "minLength": 1}}}}}}]}