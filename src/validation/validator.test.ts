import { ValidationSchemas, Validator } from "./validator";

describe("Validator", () => {
  let validator: Validator;
  beforeEach(() => {
    validator = new Validator();
  });

  it("Should return an empty array for valid offers", done => {
    expect(validator.validate(require("./samples/cashEarn.json"), ValidationSchemas.PostOfferFormObject).length).toBe(0);
    expect(validator.validate(require("./samples/cashDiscount.json"), ValidationSchemas.PostOfferFormObject).length).toBe(0);
    expect(validator.validate(require("./samples/customOffer.json"), ValidationSchemas.PostOfferFormObject).length).toBe(0);
    expect(validator.validate(require("./samples/fuelOffer.json"), ValidationSchemas.PostOfferFormObject).length).toBe(0);
    expect(validator.validate(require("./samples/multiProduct.json"), ValidationSchemas.PostOfferFormObject).length).toBe(0);
    expect(validator.validate(require("./samples/spendAndGet.json"), ValidationSchemas.PostOfferFormObject).length).toBe(0);
    done();
  });

  it("Should return an empty array for valid bulk Post offers", done => {
    expect(validator.validate(require("./samples/PostBulkCashDiscount.json"), ValidationSchemas.PostBulkOffersFormObject).length).toBe(0);
    done();
  });

  it("Should return an empty array for valid bulk Put offers", done => {
    expect(validator.validate(require("./samples/PutBulkCashDiscount.json"), ValidationSchemas.PutBulkOffersFormObject).length).toBe(0);
    done();
  });

  it("Should return an array of errors for an invalid PutOfferFormObject", done => {
    expect((validator.validate(require("./samples/cashEarn.json"), ValidationSchemas.PutOfferFormObject)).length).toBeGreaterThan(0);
    expect(validator.validate(require("./samples/Invalid_FuelOfferForm.json"), ValidationSchemas.PostOfferFormObject).length).toBeGreaterThan(0);
    done();
  });

  it("Should return an array of errors for an invalid PutBulkOfferFormObject", done => {
    expect((validator.validate(require("./samples/PostBulkCashDiscount.json"), ValidationSchemas.PutBulkOffersFormObject)).length).toBeGreaterThan(0);
    done();
  });

  it("Should return an array of errors for an invalid PostOfferFormObject", done => {
    expect((validator.validate({
      offerType: "",
      issuanceCode: "$$$"
    }, ValidationSchemas.PostOfferFormObject)).length).toBeGreaterThan(0);
    done();
  });

  it("Should return an array of errors for an invalid PutOfferFormObject", done => {
    expect((validator.validate(require("./samples/cashEarn.json"), ValidationSchemas.PutOfferFormObject)).length).toBe(1);
    done();
  });


});
