// types.d.ts
// example of including `react-router` actions in `RootAction`
import { RouterAction, LocationChangeAction } from "connected-react-router";
type ReactRouterAction = RouterAction | LocationChangeAction;
import { AdministrationActionType } from "./features/administration/actions";
import { IRole, IPermission } from "./models/Role";
import { ThunkAction, ThunkDispatch } from "redux-thunk";
import { IRootState } from "./store";
import { Action, ActionCreator } from "redux";
import { IRbacUser } from "./models/User";
import { IPartner } from "./models/Partner";

export type RootAction = ReactRouterAction | AdministrationActionType;
export interface IAdministrationState {
  groups: IGroup[];
  partners: IPartner[];
  roles: IRole[];
  permissions: IPermission[];
  users: IRbacUser[];
  groupUserMappings: any; //TODO: Test using type Array<{ userId: string; groupId: number; }>;
  userPartnersMapping: { [email: string]: string[] };
  userGroupsMapping: { [email: string]: string[] };
}

export type ThunkResult<R> = ActionCreator<
  ThunkAction<R, IRootState, undefined, Action>
>;
