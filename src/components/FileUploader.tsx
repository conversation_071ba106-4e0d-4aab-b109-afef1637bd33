import Uppy from "@uppy/core";
import { Dashboard } from "@uppy/react";
import { DashboardProps } from "@uppy/react/src/Dashboard";
import React, { createRef } from "react";
import "./FileUploader.scss";
import { SnackbarContext } from "../features/communication/AlertContainer";

export interface IFileUploaderProps extends Partial<DashboardProps> {
  onBeforeFileAdded?: (
    currentFile: Uppy.UppyFile,
    files: { [key: string]: Uppy.UppyFile }
  ) => boolean | Uppy.UppyFile | undefined;
  onFileAdded?: (files: Uppy.UppyFile[]) => void;
  onFileRemoved?: (files: Uppy.UppyFile[]) => void;
  onCancelAll?: () => void;
  uppyConfig?: Partial<Uppy.UppyOptions>;
  // Details of the new cropped image
  croppedImage?:
    | ({ fileDetails: { [key: string]: string }; blob: Blob })
    | null;
  // After we've successfully updated to the latest cropped image
  onUpdateToCroppedImage?: () => void;
  error?: boolean;
}

export default class FileUploader extends React.PureComponent<
  IFileUploaderProps
> {

  public static contextType = SnackbarContext;
  public uppy: Uppy.Uppy;
  public contextType: typeof SnackbarContext;

  public componentDidUpdate() {
    const { croppedImage, onUpdateToCroppedImage } = this.props;
    if (croppedImage) {
      this.uppy.reset();
      try {
        this.uppy.addFile({
          name: croppedImage.fileDetails.name, // file name
          type: croppedImage.fileDetails.type, // file type
          data: croppedImage.blob, // file blob
          source: "Local", // optional, determines the source of the file, for example, Instagram
          isRemote: false // optional, set to true if actual file is not in the browser, but on some remote server, for example, when using companion in combination with Instagram
        });
      } catch (error) {
        console.error(error);
        this.context.displayAlert({
          message: `${error}`,
          variant: "error"
        });
        if (this.props.onCancelAll) { this.props.onCancelAll(); }
      }
      if (onUpdateToCroppedImage) {
        onUpdateToCroppedImage();
      }
    }
  }

  public render() {
    const {
      onFileAdded,
      onFileRemoved,
      onCancelAll,
      uppyConfig,
      ...others
    } = this.props;
    return (
      <Dashboard
        uppy={this.uppy}
        proudlyDisplayPoweredByUppy={false}
        {...others}
      />
    );
  }

  public componentWillMount() {
    this.uppy = Uppy(this.getUppyRequiredConfig());
    // When Uppy finishes uploading a file
    this.uppy.on("file-added", this.onFileAdded);
    this.uppy.on("file-removed", this.onFileRemoved);
    this.uppy.on("cancel-all", this.onCancelAll);
  }

  public componentWillUnmount() {
    this.uppy.reset();
  }

  /**
   * Imperatively reset file upload component
   */
  public resetFiles = () => {
    if (this.uppy) {
      this.uppy.reset();
    }
  };

  private getUppyRequiredConfig: () => Partial<Uppy.UppyOptions> = () => ({
    onBeforeFileAdded: this.handleBeforeFileAdded,
    ...this.props.uppyConfig
  });
  /**
   * whenever a file is added to uppy's dashboard
   * @param file
   */
  private onFileAdded = (file: Uppy.UppyFile) => {
    if (this.props.onFileAdded) {
      this.props.onFileAdded((this.uppy as Uppy.Uppy).getFiles());
    }
  };
  /**
   * whenever a file is removed from uppy's dashboard
   * @param file
   */
  private onFileRemoved = (file: Uppy.UppyFile) => {
    if (this.props.onFileRemoved && this.props.croppedImage === null) {
      this.props.onFileRemoved((this.uppy as Uppy.Uppy).getFiles());
    }
  };
  /**
   * whenever a file is uploaded to uppy
   */
  private onCancelAll = () => {
    // We check for the croppedImage to let uppy know not to trigger this if we're just updating the file
    // because a new cropped image relaced the original one
    if (this.props.onCancelAll && this.props.croppedImage === null) {
      this.props.onCancelAll();
    }
  };

  private handleBeforeFileAdded = (
    currentFile: Uppy.UppyFile,
    files: { [key: string]: Uppy.UppyFile }
  ) => {
    if (this.props.onBeforeFileAdded) {
      return this.props.onBeforeFileAdded(currentFile, files);
    } else {
      return true;
    }
  };
}
