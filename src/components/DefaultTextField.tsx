import { Grid, TextField, Typography } from "@material-ui/core";
import { TextFieldProps } from "@material-ui/core/TextField";
import { debounce } from "lodash";
import React, {useEffect, useState} from "react";
import HelperTooltip from "./form/widgets/HelperTooltip";

export type DefaultTextFieldProps = TextFieldProps & {
  optional?: boolean;
  tooltip?: string;
  disabled?: boolean;
  shouldUpdateFromProps?: boolean;
};

export const DefaultTextField: React.FunctionComponent<DefaultTextFieldProps> = props => {
  const { value, optional, label, tooltip, shouldUpdateFromProps, onChange, disabled, ...otherProps } = props;

  const [valueState, setValueState] = useState(value);

  useEffect(() => {
    if (value && valueState !== value) {
      setValueState(value);
    }

    syncChange = debounce(syncChange, 500);
  }, []);

  useEffect(() => {
    if (value && shouldUpdateFromProps && valueState !== value) {
      setValueState(value);
    }
  }, [shouldUpdateFromProps, valueState]);

  let syncChange = (
      e: React.ChangeEvent<
          HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
      >
  ) => {
    if (onChange) {
      onChange(e);
    }
  };

  const handleChange = (
      e: React.ChangeEvent<
          HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
      >
  ) => {
    if (e.persist) {
      e.persist();
    }

    setValueState(e.target.value)
    syncChange(e);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.which === 13 && onChange) {
      onChange(e as any);
    }

    return true;
  };

  return (
      <>
        <Grid direction="row" container={true}>
          <Typography variant="subtitle1" color="initial">
            {label}{" "}
          </Typography>
          {tooltip && <HelperTooltip text={tooltip} />}
          {optional && (
              <Typography variant="subtitle1" color="primary">
                &nbsp;(Optional)
              </Typography>
          )}
        </Grid>
        <TextField
            {...otherProps}
            onChange={handleChange}
            onBlur={e => {
              if (onChange) {
                onChange(e);
              }
            }}
            onKeyDown={handleKeyDown}
            value={valueState ? valueState : value}
            variant="filled"
            color="default"
            fullWidth={true}
            InputProps={{
              disableUnderline: true
            }}
            disabled={disabled ? disabled : false}
        />
      </>
  );
}