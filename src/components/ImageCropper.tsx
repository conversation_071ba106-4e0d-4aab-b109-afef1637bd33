import { Grid } from "@material-ui/core";
import Uppy from "@uppy/core";
import classNames from "classnames";
import <PERSON>ropper from "cropperjs";
import React from "react";
import { BB8Button } from "../BB8";
import { getDiffOfObjects } from "../shared/helpers";
import { MAX_IMG_HEIGHT, MAX_IMG_WIDTH } from "../shared/services/image.service";
import "./ImageCropper.scss";

export interface IImageCropperProps {
  file: Uppy.UppyFile | null;
  onFileCropped: (
    origFileDetails: { [key: string]: string | number },
    file: Blob
  ) => void;
  canCropImage: boolean;
  onCropCancel?: () => void;
}

export interface IImageCropperState {
  imageHasLoaded: boolean;
}

export default class ImageCropper extends React.PureComponent<
  IImageCropperProps,
  IImageCropperState
> {
  private cropper: Cropper | null;
  private imageRef: HTMLImageElement | null;
  private fr: FileReader = new FileReader();

  private cropperConfig: Cropper.Options = {
    autoCrop: false,
    aspectRatio: 1,
    background: false,
    guides: false,
    viewMode: 2,
    dragMode: "move" as Cropper.DragMode,
    autoCropArea: 1
  };

  public constructor(props: IImageCropperProps) {
    super(props);
    this.state = {
      imageHasLoaded: false
    };
  }

  public componentWillMount() {
    // Triggers whenever fr.readAsDataURL completes
    this.fr.addEventListener(
      "loadend",
      this.onFileReaderLoadSuccess.bind(this),
      false
    );
  }

  public componentWillUnmount() {
    this.fr.removeEventListener("loadend", this.onFileReaderLoadSuccess);
    if (this.imageRef) {
      this.imageRef.removeEventListener("ready", this.onCropperReadyToCrop);
    }
    this.resetCropperAndImage();
  }

  public componentDidUpdate(prevProps: any, prevState: any) {
    const diffObj = getDiffOfObjects(prevProps, this.props);
    const { canCropImage } = this.props;
    if ("file" in diffObj) {
      const { file } = this.props;
      // Make sure to abort the file reader reading, so we dont run into errors that the file reader is currently reading
      this.fr.abort();
      if (file) {
        this.setState({
          imageHasLoaded: false
        });
        this.fr.readAsDataURL(file.data);
      } else {
        this.resetCropperAndImage();
      }
    }
    // Only enable the cropping options if they clicked the edit button
    if (canCropImage) {
      if (!this.cropper && this.imageRef) {
        // Make sure to reinitialize the listeners for new images
        this.imageRef.removeEventListener("ready", this.onCropperReadyToCrop);
        this.imageRef.addEventListener("ready", this.onCropperReadyToCrop);
        this.cropper = new Cropper(this.imageRef, this.cropperConfig);
      }
    }
  }

  public render() {
    const { file, canCropImage } = this.props;
    const { imageHasLoaded } = this.state;
    return (
      <Grid
        container={true}
        item={true}
        xs={12}
        direction="column"
        alignItems="center"
      >
        <Grid
          className="image-cropper__img-wrapper"
          container={true}
          item={true}
          xs={12}
          alignItems="center"
        >
          <img
            id="image"
            ref={ref => (this.imageRef = ref)}
            className={classNames("image-cropper__img")}
          />
        </Grid>

        {canCropImage ? (
          <Grid container={true} justify="space-around">
            <BB8Button
              color="primary"
              variant="contained"
              onClick={this.saveCroppedImage}
            >
              Apply Changes
            </BB8Button>
            <BB8Button
              color="default"
              variant="outlined"
              onClick={this.resetCropper}
            >
              Cancel
            </BB8Button>
          </Grid>
        ) : (
          this.props.children
        )}
      </Grid>
    );
  }

  private onFileReaderLoadSuccess = () => {
    if (this.imageRef != null && this.fr.result != null) {
      this.setState({
        imageHasLoaded: true
      });
      const result: string = this.fr.result as string;
      this.imageRef.src = result;
    }
  };

  /**
   * Make sure we only initialize the crop when the cropper has actually loaded, else the crop box wont show up if cropper isnt ready
   */
  private onCropperReadyToCrop = () => {
    if (this.cropper) {
      this.cropper.crop();
    }
  };

  // Update the file in uppy with the new cropped image, and update croppy to point to the new image
  private saveCroppedImage = () => {
    const { file } = this.props;
    if (this.cropper) {
      try {
        this.cropper
          .getCroppedCanvas({ height: MAX_IMG_HEIGHT, width: MAX_IMG_WIDTH })
          .toBlob((blob: Blob | null) => {
            if (!blob) {
              return;
            }
            let origFile: { [key: string]: any } = {};
            if (file) {
              origFile = {
                name: file.name,
                size: file.size,
                type: file.type
              };
            }
            this.props.onFileCropped(origFile, blob);

            // Force croppy to refresh for a new image
            this.resetCropperAndImage();
          });
      } catch (error) {
        console.error(error);
      }
    }
  };

  /**
   * Remove Cropper and remove the instance completely to make sure cropper reinitializes properly for a new image
   */
  private resetCropperAndImage = () => {
    if (this.imageRef) {
      this.imageRef.src = "";
    }
    this.resetCropper();
  };

  private resetCropper = () => {
    if (this.cropper) {
      this.cropper.destroy();
      this.cropper = null;
    }
    if (this.props.onCropCancel) {
      this.props.onCropCancel();
    }
  };
}
