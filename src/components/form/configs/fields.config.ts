import { $Keys } from "utility-types";
import offerSchema from "../../../validation/schemas/PostOfferFormObject.json";
import tierSchema from "../../../validation/schemas/TierObject.json";
import mechanismSchema from "../../../validation/schemas/MechanismObject.json";

export type FieldKeys =
  | $Keys<typeof mechanismSchema.properties>
  | $Keys<typeof tierSchema.properties>
  | $Keys<typeof offerSchema.properties>
  | "en-US"
  | "fr-CA"
  | "path"
  | "offerTemplate"
  | "eventBasedOfferLegal"
  | "cardType"
  | "retailerGroupId";

export const FIELDS_CONFIG: {
  [key in Partial<FieldKeys>]: { title: string; toolTip: string; }
} = {
  partnerId: {
    title: "Partner",
    toolTip: ""
  },
  programType: {
    title: "Program Type",
    toolTip: ""
  },
  offerTemplate: {
    title: "Offer Template",
    toolTip: ""
  },
  offerType: {
    title: "Offer Type",
    toolTip: ""
  },
  qualifier: {
    title: "Qualifier",
    toolTip: ""
  },
  awardType: {
    title: "Award Type",
    toolTip: ""
  },
  availability: {
    title: "Availability",
    toolTip:
      "Select corresponding box if the offer is available in-store, online or both."
  },
  cardType:{
    title: "Card Type",
    toolTip:
        "These are multi-selection boxes, please select the valid combination: " +
        "\n 1. Non-BMO Mastercard + BMO Mastercard + BMO Debit" +
        "\n 2. Non-BMO Mastercard + BMO Mastercard" +
        "\n 3. BMO Debit "
  },
  retailerGroupId:{
    title: "Retailer Group",
    toolTip: ""
  },
  partnerBaseEarnRate: {
    title: "Partner Base Earn Rate",
    toolTip: "Base Offer Spend Amount e.g. 1 Mile for $20"
  },
  baseCashRedemption: {
    title: "Base Cash Redemption",
    toolTip: "" // Adding empty tooltip to work around typescript
  },
  description: {
    title: "Additional Description",
    toolTip:
      "Select if additional description is needed for the offer detail view"
  },
  displayDate: {
    title: "Display Date",
    toolTip: "Date you want the offer thumbnail visible."
  },
  endDate: {
    title: "End Date",
    toolTip: "Offer expiry date/removed from channels e.g. mobile app."
  },
  firstQualificationDate: {
    title: "First Qualification Date",
    toolTip: "First segment's start date of the offer."
  },
  lastQualificationDate: {
    title: "Last Qualification Date",
    toolTip: "Last segment's start date of the offer."
  },
  eligibilityDuration: {
    title: "Collector’s Eligibility Duration (days)",
    toolTip: "How long a collector has to utilize the offer."
  },
  eventBasedOffer: {
    title: "",
    toolTip: ""
  },
  eligibilityDurationUnit: {
    title: "",
    toolTip: ""
  },
  eventBasedOfferLegal: {
    title: "",
    toolTip: "COLLECTOR_START_DATE and COLLECTOR_END_DATE is for preview purposes only. The actual dates will automatically be calculated and shown to collectors on App/Web as these are personalized."
  },
  usageLimit: {
    title: "",
    toolTip: "Used to identify how many times an offer can be used by a collector"
  },
  sponsorCode: {
    title: "Sponsor Code",
    toolTip: "Please select the option carefully. You cannot change the Sponsor Code and Issuance Code once the offer is published. If edits are required, please reach out to \<<EMAIL>\> and \<<EMAIL>\>"
  },
  issuanceCode: {
    title: "Issuance Code",
    toolTip: "Please select the option carefully. You cannot change the Sponsor Code and Issuance Code once the offer is published. If edits are required, please reach out to \<<EMAIL>\> and \<<EMAIL>\>"
  },
  flagSponsorCode: {
    title: "",
    toolTip: ""
  },
  startDate: {
    title: "Start Date",
    toolTip: "Offer valid date."
  },
  displayPriority: {
    title: "displayPriority",
    toolTip:
      'If you have an important offer, set it as "5. - Highest Priority (1000)" so it appears 1st on your Partner page (i.e. National Promo offer). Set all other offers as "1 - None (0)" to allow you to prioritize unexpected offers.'
  },
  mechanisms: {
    title: "Offer Mechanism",
    toolTip: "Please select the option carefully. You cannot change the Offer Mechanism once the offer is published. If edits are required, please reach out to \<<EMAIL>\> and \<<EMAIL>\>"
  },
  tags: {
    title: "Promo Tags",
    toolTip: "Select which Promotion the offer is part of."
  },
  image: {
    title: "Images",
    toolTip:
      "Minimum 320 pixels by 320 pixels; 720 pixels by 720 pixels; use re-sizer if required."
  },
  canBeCombined: {
    title: "Can be combined with other offers?",
    toolTip:
      "When this box is checked the terms and conditions indicate that your offer can be combined with other AIR MILES and/or Partner offers. When the box is NOT checked, the terms and conditions indicate that your offer can NOT be combined with other AIR MILES and/or Partner offers"
  },
  daysToApply: {
    title: "Days to Apply",
    toolTip:
      "Select if your offer issuance will be longer than typical 120-day fulfillment."
  },
  regions: {
    title: "Offer Availability",
    toolTip: ""
  },
  ctaLabel: {
    title: "CTA Label",
    toolTip: "This is an optional field. The CTA Label & URL will only show up in offer after you have entered value for both CTA Label & URL fields"
  },
  ctaUrl: {
    title: "CTA URL",
    toolTip: "This is an optional field. The CTA Label & URL will only show up in offer after you have entered value for both CTA Label & URL fields"
  },
  "en-US": {
    title: "English",
    toolTip: ""
  },
  "fr-CA": {
    title: "French",
    toolTip: ""
  },
  awardValue: {
    title: "Award Value",
    toolTip: ""
  },
  qualifierValue: {
    title: "Qualifier Value",
    toolTip: ""
  },
  qualifierFrequency: {
    title: "Qualifier Frequency",
    toolTip: "Receipts Only: Use to identify how many receipts are required to scan before earning miles (Only available for participation offers)"
  },
  awardShort: {
    title: "",
    toolTip: ""
  },
  awardLong: {
    title: "",
    toolTip: ""
  },
  cashierInstruction: {
    title: "",
    toolTip: ""
  },
  combinationsText: {
    title: "",
    toolTip: ""
  },
  content: {
    title: "",
    toolTip: ""
  },
  createdAt: {
    title: "",
    toolTip: ""
  },
  createdBy: {
    title: "",
    toolTip: ""
  },
  duplicatedFrom: {
    title: "",
    toolTip: ""
  },
  excludedBanners: {
    title: "",
    toolTip: ""
  },
  excludedLocations: {
    title: "",
    toolTip: ""
  },
  exclusions: {
    title: "",
    toolTip: ""
  },
  hasCustomLegal: {
    title: "",
    toolTip: ""
  },
  id: {
    title: "Offer Id",
    toolTip: ""
  },
  includedBanners: {
    title: "",
    toolTip: ""
  },
  includedLocations: {
    title: "",
    toolTip: ""
  },
  legalText: {
    title: "",
    toolTip: ""
  },
  offerLimitation: {
    title: "",
    toolTip: ""
  },
  offerLimitationText: {
    title: "",
    toolTip: ""
  },
  partnerLegalName: {
    title: "",
    toolTip: ""
  },
  partnerName: {
    title: "",
    toolTip: ""
  },
  partnerUrl: {
    title: "",
    toolTip: ""
  },
  publishedBy: {
    title: "",
    toolTip: ""
  },
  qualifierLong: {
    title: "",
    toolTip: ""
  },
  qualifierShort: {
    title: "",
    toolTip: ""
  },
  status: {
    title: "",
    toolTip: ""
  },
  tiers: {
    title: "Offer Content",
    toolTip: ""
  },
  trademarkInfo: {
    title: "",
    toolTip: ""
  },
  updatedBy: {
    title: "",
    toolTip: ""
  },
  mechanismLabel: {
    title: "",
    toolTip: ""
  },
  mechanismType: {
    title: "Mechanism Type",
    toolTip: ""
  },
  mechanismValue: {
    title: "Mechanism Value",
    toolTip: ""
  },
  path: {
    title: "Image",
    toolTip: ""
  },
  mechanismText: {
    title: "Mechanism Text",
    toolTip: ""
  },
  mechanismTitle: {
    title: "Mechanism Title",
    toolTip: ""
  },
  audience: {
    title: "Please Select Your Audience Type Carefully",
    toolTip:
      "You cannot change the Audience Type after publishing this offer. Please select the Audience Type carefully, or you'll have to start again!"
  },
  massOffer: {
    title: "Mass Offer",
    toolTip: ""
  },
  campaignCode: {
    title: "Campaign Code",
    toolTip:
      "This field should be composed of up to 50 alphanumeric characters, excluding special characters"
  },
  active: {
    title: "",
    toolTip: ""
  },
  offerCategory1: {
    title: "Category",
    toolTip: ""
  },
  offerCategory2: {
    title: "More Filters",
    toolTip: ""
  },
  offerCategory3: {
    title: "Specific Filter",
    toolTip: ""
  },
  productName: {
    title: "",
    toolTip: ""
  },
  productBrand: {
    title: "",
    toolTip: ""
  },
  programPriority: {
    title: "",
    toolTip: ""
  },
  partnerOfferId: {
    title: "",
    toolTip: ""
  },
  partnerInternalAirmilesCalculated: {
    title: "",
    toolTip: ""
  },
};

export const DYNAMIC_FIELDS_CONFIG = {
  qualifier: {
    title: (offerType: string, qualifier: string): string => {
      const titleMap = new Map<string, string>([
        //Product mappings
        [["buy", "product"].toString(), "Product"],
        [["buy", "perProduct"].toString(), "Product"],
        [["spend", "perProduct"].toString(), "Product"],
        [["buy", "perUnit"].toString(), "Product"],
        [["custom", "product"].toString(), "Product"],

        //Category mappings
        [["spend", "category"].toString(), "Category"],
        [["custom", "category"].toString(), "Category"],

        //Fuel
        [["buy", "fuel"].toString(), "Category"],
        [["custom", "fuel"].toString(), "Category"],
      ]);
      const key = [offerType, qualifier].toString();
      return titleMap.get(key) || "";
    },
  },
  awardLong: {
    title: (offerType: string, awardType: string) => {
      if (offerType === "custom" && awardType === "custom") {
        return "Custom Award";
      }
    }
  },
  qualifierLong: {
    title: (offerType: string, awardType: string, qualifier: string) => {
      if (
        offerType === "custom" &&
        awardType === "custom" &&
        qualifier === "custom"
      ) {
        return "Custom Qualifier";
      }
    }
  }
};

export const PARTNER_SECTION_FIELDS: string[] = ["partnerId", "partnerName"];
export const OFFER_TYPE_SECTION_FIELDS: string[] = [
  "programType",
  "offerType",
  "qualifier",
  "awardType",
  "availability",
  "audience",
  "campaignCode"
];
export const OFFER_CATEGORY_SECTION_FIELDS: string[] = [
  "offerCategory1",
  "offerCategory2"
];
export const OFFER_CONTENT_SECTION_FIELDS: string[] = [
  "baseCashRedemption",
  "partnerBaseEarnRate",
  "tiers",
  "tiers[].awardValue",
  "tiers[].qualifierValue",
  "tiers[].qualifierFrequency",
  "tiers[].content",
  "tiers[].content[].en-US",
  "tiers[].content[].fr-CA",
  "tiers[].content[].skus",  
  "tiers[].qualifierLong",
  "tiers[].qualifierLong.en-US",
  "tiers[].qualifierLong.fr-CA",
  "tiers[].awardLong",
  "tiers[].awardLong.en-US",
  "tiers[].awardLong.fr-CA",
  "description",
  "description.en-US",
  "description.fr-CA",
  "awardShort",
  "awardShort.en-US",
  "awardShort.fr-CA",
  "qualifierShort",
  "qualifierShort.en-US",
  "qualifierShort.fr-CA",
  "image",
  "image.en-US",
  "image.fr-CA",
  "image.en-US.path",
  "image.fr-CA.path"
];
export const OFFER_SETTINGS_SECTION_FIELDS: string[] = [
  "sponsorCode",
  "issuanceCode",
  "flagSponsorCode",
  "displayDate",
  "startDate",
  "endDate",
  "firstQualificationDate",
  "lastQualificationDate",
  "eligibilityDuration",
  "usageLimit",
  "displayPriority",
  "tags",
  "mechanisms",
  "mechanisms[].mechanismType",
  "mechanisms[].mechanismLabel",
  "mechanisms[].mechanismLabel.en-US",
  "mechanisms[].mechanismLabel.fr-CA",
  "mechanisms[].mechanismValue",
  "mechanisms[].mechanismValue.en-US",
  "mechanisms[].mechanismValue.fr-CA",
  "ctaLabel",
  "ctaLabel.en-US",
  "ctaLabel.fr-CA",
  "ctaUrl",
  "ctaUrl.en-US",
  "ctaUrl.fr-CA",
  "cashierInstruction",
  "cashierInstruction.en-US",
  "cashierInstruction.fr-CA",
  "regions",
  "excludedLocations[]",
  "excludedLocations[].en-US",
  "excludedLocations[].fr-CA",
  "includedLocations[]",
  "includedLocations[].en-US",
  "includedLocations[].fr-CA",
  "excludedBanners[]",
  "excludedBanners[].en-US",
  "excludedBanners[].fr-CA",
  "includedBanners[]",
  "includedBanners[].en-US",
  "includedBanners[].fr-CA",
  "partnerInternalAirmilesCalculated"
];
export const OFFER_TERMS_SECTION_FIELDS: string[] = [
  "combinationsText",
  "combinationsText.en-US",
  "combinationsText.fr-CA",
  "partnerUrl",
  "partnerUrl.en-US",
  "partnerUrl.fr-CA",
  "daysToApply",
  "exclusions",
  "exclusions.en-US",
  "exclusions.fr-CA",
  "offerLimitationText",
  "offerLimitationText.en-US",
  "offerLimitationText.fr-CA",
  "partnerLegalName",
  "partnerLegalName.en-US",
  "partnerLegalName.fr-CA",
  "trademarkInfo",
  "trademarkInfo.en-US",
  "trademarkInfo.fr-CA",
  "legalText",
  "legalText.en-US",
  "legalText.fr-CA"
];

/**
 * Note: If you want any errors to show up in the error summary table at the bottom, the field name
 * needs to be listed here below
 */
export const FIELDS_ORDER: string[] = [
  ...PARTNER_SECTION_FIELDS,
  ...OFFER_TYPE_SECTION_FIELDS,
  ...OFFER_CATEGORY_SECTION_FIELDS,
  ...OFFER_CONTENT_SECTION_FIELDS,
  ...OFFER_SETTINGS_SECTION_FIELDS,
  ...OFFER_TERMS_SECTION_FIELDS
];

export const SECTIONS_WITH_FIELDS = {
  partnerSectionFields: PARTNER_SECTION_FIELDS,
  offerTypeSectionFields: OFFER_TYPE_SECTION_FIELDS,
  offerCategorySectionFields: OFFER_CATEGORY_SECTION_FIELDS,
  offerContentSectionFields: OFFER_CONTENT_SECTION_FIELDS,
  offerSettingsSectionFields: OFFER_SETTINGS_SECTION_FIELDS,
  offerTermsSectionFields: OFFER_TERMS_SECTION_FIELDS
};