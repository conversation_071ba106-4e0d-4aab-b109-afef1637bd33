import {
  Checkbox,
  createSty<PERSON>,
  FormControlLabel,
  Grid,
  Typography,
  withStyles,
  WithStyles
} from "@material-ui/core";
import { CheckboxProps } from "@material-ui/core/Checkbox";
import { Field, FieldConfig, FieldProps } from "formik";
import React from "react";

class CustomCheckboxField extends React.PureComponent<
  CheckboxProps &
  FieldConfig & { label: string; optional?: boolean } & WithStyles
  > {
  public render() {
    const { name, classes, disabled } = this.props;
    return (
      <Field name={name}>
        {(fieldProps: FieldProps) => {
          const val = fieldProps.field.value || false;
          return (
            <FormControlLabel
              classes={{ root: classes.formLabel }}
              control={
                <Checkbox
                  classes={{ root: classes.checkBox }}
                  checked={val}
                  color="primary"
                  disabled={disabled}
                  onChange={() => fieldProps.form.setFieldValue(name, !val)}
                />
              }
              label={this.renderLabel()}
            />
          );
        }}
      </Field>
    );
  }

  private renderLabel = () => {
    const { label, optional } = this.props;
    return (
      <Grid direction="row" sm={true} container={true} item={true}>
        <Typography variant="subtitle1" color="initial">
          {label}{" "}
        </Typography>
        {optional && (
          <Typography variant="subtitle1" color="primary">
            &nbsp;(Optional)
          </Typography>
        )}
      </Grid>
    );
  };
}

const styles = createStyles(() => ({
  checkBox: {
    padding: "0.2em 0.5em"
  },
  formLabel: {
    paddingBottom: "1em"
  }
}));

export default withStyles(styles)(CustomCheckboxField);
