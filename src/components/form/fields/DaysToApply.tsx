import {
  Checkbox,
  createSty<PERSON>,
  FormControlLabel,
  FormLabel,
  Grid,
  withStyles
} from "@material-ui/core";
import React from "react";
import { FIELDS_CONFIG } from "../configs/fields.config";
import CustomTextWidget from "../widgets/CustomTextWidget";
import HelperTooltip from "../widgets/HelperTooltip";
import { FormikProps } from "formik";
import { IOfferFormModel } from "../../../validation/validator";

export interface IDaysToApplyState {
  hasDaysToApply: boolean;
}

export interface IDaysToApplyProps {
  min: number;
  max: number;
  formikProps: FormikProps<IOfferFormModel>;
}

class DaysToApply extends React.PureComponent<
  IDaysToApplyProps,
  IDaysToApplyState
> {
  constructor(props: IDaysToApplyProps) {
    super(props);
    const hasDaysToApply = this.props.formikProps.values.daysToApply
      ? true
      : false;
    this.state = { hasDaysToApply };
  }

  public render() {
    const { hasDaysToApply } = this.state;
    const { min, max, formikProps } = this.props;

    return (
      <>
        <Grid
          direction="row"
          item={true}
          container={true}
          alignItems={"center"}
        >
          <Grid item={true} xs={12}>
            <FormControlLabel
              key="hasDaysToApply"
              control={
                <Checkbox
                  style={{ padding: "0.2em 0.5em" }}
                  checked={hasDaysToApply}
                  color="primary"
                  onChange={e => {
                    if (!hasDaysToApply) {
                      // This is the only way I can figure to trigger a warning showing up without explcitly putting a number
                      formikProps.setFieldValue("daysToApply", null);
                    } else {
                      formikProps.setFieldValue("daysToApply", undefined);
                    }
                    this.setState({
                      hasDaysToApply: !hasDaysToApply
                    });
                  }}
                  value={hasDaysToApply}
                  name="hasDaysToApply"
                />
              }
              label="This offer has an unusually long fulfillment period."
            />
            <HelperTooltip text={FIELDS_CONFIG.daysToApply.toolTip} />
          </Grid>
          {hasDaysToApply && (
            <Grid item={true} xs={12}>
              <CustomTextWidget
                name="daysToApply"
                type="number"
                label={FIELDS_CONFIG.daysToApply.title}
                inputProps={{
                  min,
                  max,
                  step: 1,
                  pattern: "/^d{1,3}$/"
                }}
                placeholder="Range between 1 and 365"
                required={true}
                onChange={e => {
                  // We have to manually call onChange, because we have to make sure we're not setting undefined if the user doesn't type anything/clears the field
                  formikProps.setFieldValue(
                    "daysToApply",
                    Number(e.target.value)
                  );
                }}
              />
            </Grid>
          )}
        </Grid>
      </>
    );
  }
}

const styles = createStyles(() => ({}));

export default withStyles(styles)(DaysToApply);
