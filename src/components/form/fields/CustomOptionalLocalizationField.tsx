import { Fab, Grid, Typography } from "@material-ui/core";
import AddIcon from "@material-ui/icons/Add";
import { FormikProps } from "formik";
import { get, isEmpty, isUndefined } from "lodash";
import React from "react";
import OfferSubmissionFormPanel from "../../../features/offer-submission/components/OfferSubmissionFormPanel";
import { IOfferFormModel } from "../../../validation/validator";
import { FieldKeys, FIELDS_CONFIG } from "../configs/fields.config";
import HelperTooltip from "../widgets/HelperTooltip";
import CustomLocalizationTextField from "./CustomLocalizationTextField";
interface IOptionalLocalizationField {
  formikProps: FormikProps<IOfferFormModel>;
  fieldPath: any;
  label?: string;
}

class CustomOptionalLocalizationField extends React.Component<
  IOptionalLocalizationField
> {
  public render() {
    const { label, fieldPath, formikProps } = this.props;
    const hasValue = !isUndefined(get(formikProps.values, fieldPath));
    const title =
      label || FIELDS_CONFIG[fieldPath as FieldKeys].title || "Empty";
    const tooltip = FIELDS_CONFIG[fieldPath as FieldKeys].toolTip;
    return hasValue ? (
      <OfferSubmissionFormPanel title={title + " Translations"}>
        <CustomLocalizationTextField
          name={fieldPath}
          label={title}
          removeItem={this.toggleOptionalLocalizationField}
        />
      </OfferSubmissionFormPanel>
    ) : (
      <Grid
        direction="row"
        item={true}
        container={true}
        alignItems={"center"}
        onClick={this.toggleOptionalLocalizationField}
      >
        <Fab size="small" color="primary" aria-label="Add">
          <AddIcon />
        </Fab>
        <Typography variant="button" color="primary">
          &nbsp;&nbsp;&nbsp;&nbsp;Add {title}
        </Typography>
        {!isEmpty(tooltip) && <HelperTooltip text={tooltip} />}
        <Typography variant="subtitle1" color="primary">
          &nbsp;&nbsp;(Optional)
        </Typography>
      </Grid>
    );
  }

  private toggleOptionalLocalizationField = () => {
    const { formikProps, fieldPath } = this.props;
    const hasValue = !isUndefined(get(formikProps.values, fieldPath));
    formikProps.setFieldValue(
      fieldPath,
      !hasValue
        ? {
            "en-US": undefined,
            "fr-CA": undefined
          }
        : undefined,
      true
    );
  };
}

export default CustomOptionalLocalizationField;
