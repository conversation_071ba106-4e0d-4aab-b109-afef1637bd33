import {
  createStyles,
  Fab,
  Grid,
  Typography,
  WithStyles,
  withStyles
} from "@material-ui/core";
import { TextFieldProps } from "@material-ui/core/TextField";
import ClearIcon from "@material-ui/icons/Clear";
import { get } from "lodash";
import React from "react";
import CustomTextWidget from "../widgets/CustomTextWidget";
import MechanismObject from "./../../../validation/schemas/MechanismObject.json";
import PostOfferFormObject from "./../../../validation/schemas/PostOfferFormObject.json";
import TierObject from "./../../../validation/schemas/TierObject.json";
import { Field } from "formik";
import HelperTooltip from "../../../components/form/widgets/HelperTooltip";

const CustomLocalizationTextField: React.FunctionComponent<
  WithStyles &
    TextFieldProps & {
      index?: number;
      items?: any;
      removeItem?: (index: number) => void;
    }
> = props => {
  const { label, name, index, items, classes, removeItem, disabled} = props;
  const itemIndex = index ? index : 0;
  return (
    <>
      {itemIndex === 0 && (
        <Grid
          item={true}
          container={true}
          sm={12}
          alignItems="center"
          direction="row"
        >
          {items && items.length > 1 && (
            <Grid item={true} className={classes.counterColumn}>
              <Typography variant="body2">#</Typography>
            </Grid>
          )}
          <Grid
            direction="row"
            sm={true}
            className={classes.subHeaderHolder}
            container={true}
            item={true}
          >
            <Typography variant="subtitle1" color="initial">
              {label}{" "}
            </Typography>
            <Typography variant="subtitle1" color="secondary">
              &nbsp;(English)
            </Typography>
          </Grid>
          <Grid
            direction="row"
            sm={true}
            className={classes.subHeaderHolder}
            container={true}
            item={true}
          >
            <Typography variant="subtitle1" color="initial">
              {label}{" "}
            </Typography>
            <Typography variant="subtitle1" color="secondary">
              &nbsp; (French)
            </Typography>
            <Typography variant="subtitle1" color="primary">
              &nbsp;(Optional)
            </Typography>
          </Grid>
          {removeItem && (
            <Grid item={true} className={classes.removeButtonColumn} />
          )}
        </Grid>
      )}
      <Grid item={true} container={true} sm={12} direction="row">
        {items && items.length > 1 && (
          <Grid item={true} className={classes.counterColumn}>
            <Typography>{itemIndex + 1}</Typography>
          </Grid>
        )}
        <Grid item={true} sm={true} className={classes.fieldHolder}>
          <CustomTextWidget
            name={`${name}${
              index !== undefined ? "[" + itemIndex + "]" : ""
            }.en-US`}
            placeholder={getPlaceholderText(name, "en-US")}
            type="string"
            disabled={disabled}
          />
        </Grid>
        <Grid item={true} sm={true} className={classes.fieldHolder}>
          <CustomTextWidget
            name={`${name}${
              index !== undefined ? "[" + itemIndex + "]" : ""
            }.fr-CA`}
            placeholder={getPlaceholderText(name, "fr-CA")}
            type="string"
            disabled={disabled}
          />
        </Grid>
        {(label==="Product" || label==="Category") && (
            <Grid item={true} sm={12} className={classes.fieldHolder}>
              <Typography variant="subtitle1" color="initial">
                {"SKUs"}
                <HelperTooltip text={"this is an optional field for putting in single or multiple sku, if mulitple,please use ',' to separate the sku."} />
              </Typography>
              <CustomTextWidget
                  name={`${name}${index !== undefined ? "[" + itemIndex + "]" : ""}.skus`}
                  placeholder="e.g: 1000000 or 1000000,10000002,1000003"
                  type="string"
                  disabled={disabled}
                  isArray={true}
              />
            </Grid>
        )}
        {removeItem && (
          <Grid item={true} className={classes.removeButtonColumn}>
            <Fab
              size="small"
              color="primary"
              aria-label="Remove"
              // Is there a better way to do this?
              onClick={() => {
                return removeItem(itemIndex);
              }}
            >
              <ClearIcon />
            </Fab>
          </Grid>
        )}
      </Grid>
    </>
  );
};
const getPlaceholderText = (name: any, lang: string) => {
  const schemaObject = name.includes("mechanisms")
    ? MechanismObject
    : name.includes("tiers")
    ? TierObject
    : PostOfferFormObject;
  const path =
    name.includes("mechanisms") || name.includes("tiers")
      ? name.split(".")[1]
      : name;
  let maxLength = 0;

  const type = get(schemaObject, `properties[${path}].type`, undefined);
  maxLength = get(
    schemaObject,
    `properties[${path}]${
      type === "array" ? ".items" : ""
    }.properties.${lang}.maxLength`,
    undefined
  );

  return maxLength ? `Maximum ${maxLength} Characters` : "";
};

const styles = createStyles(() => ({
  counterColumn: {
    padding: "1em"
  },
  subHeaderHolder: {
    padding: "1em"
  },
  removeButtonColumn: {
    padding: "0.4em 0",
    width: "50px"
  },
  fieldHolder: {
    padding: "0 1em"
  }
}));

export default withStyles(styles)(CustomLocalizationTextField);
