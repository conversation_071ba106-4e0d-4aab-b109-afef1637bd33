import { Grid, Typography, FormHelperText } from "@material-ui/core";
import React from "react";
import { IStaticFilterOption } from "../../../models/IStaticFilterOption";
import { REGIONS } from "../../../models/Region";
import CustomCheckboxGroup from "../widgets/CustomCheckboxGroup";
import { Field, FieldProps } from "formik";
import classNames from "classnames";
import "./CustomRegionComponent.scss";
export default class CustomRegionComponent extends React.PureComponent {
  public render() {
    const westRegions = {
      title: "West",
      items: this.getCorrectRegions(["AB", "BC", "MB", "SK"])
    };
    const centralRegions = {
      title: "Central",
      items: this.getCorrectRegions(["ON", "QC", "TB"])
    };
    const atlanticRegions = {
      title: "Atlantic",
      items: this.getCorrectRegions(["NB", "NS", "NL", "PE"])
    };
    const northRegions = {
      title: "North",
      items: this.getCorrectRegions(["NT", "NU", "YT"])
    };

    const values = [westRegions, centralRegions, atlanticRegions, northRegions];
    return (
      <Field name="regions">
        {({ field, form }: FieldProps) => {
          const errors = form.errors[field.name];
          return (
            <div
              className={classNames("regions-wrapper", {
                "regions-wrapper__error": Boolean(errors)
              })}
            >
              <Typography
                variant="h5"
                component="h5"
                paragraph={true}
                gutterBottom={true}
                id="regions"
                color={Boolean(errors) ? "error" : "inherit"}
              >
                Offer Availability
              </Typography>
              <Grid container={true} justify="space-between">
                {values.map(value => (
                  <Grid item={true} xs="auto" key={value.title}>
                    <CustomCheckboxGroup
                      values={value.items}
                      isColumn={true}
                      title={value.title}
                      name="regions"
                    />
                  </Grid>
                ))}
              </Grid>
              <Grid item={true}>
                {Boolean(errors) && Array.isArray(errors) && (
                  <FormHelperText error={true}>
                    Please select at least one region
                  </FormHelperText>
                )}
              </Grid>
            </div>
          );
        }}
      </Field>
    );
  }
  private getCorrectRegions(listOfSpecificRegions: string[]) {
    return REGIONS.filter(
      (region: IStaticFilterOption) =>
        listOfSpecificRegions.indexOf(region.value) >= 0
    );
  }
}
