import {
  createStyles,
  Fab,
  Grid,
  Theme,
  Typography,
  WithStyles,
  withStyles
} from "@material-ui/core";
import { TextFieldProps } from "@material-ui/core/TextField";
import AddIcon from "@material-ui/icons/Add";
import { FieldArray } from "formik";
import React from "react";
import OfferSubmissionFormPanel from "../../../features/offer-submission/components/OfferSubmissionFormPanel";
import { generateRandomGUIDForCacheInvalidation } from "../../../shared/helpers";
import CustomLocalizationTextField from "./CustomLocalizationTextField";

export const CustomLocalizationFieldArray: React.FunctionComponent<
  WithStyles &
    TextFieldProps & {
      maxItems?: number;
      optional?: boolean;
    }
> = props => {
  const { label, name, classes, maxItems, optional } = props;
  const [key, setKey] = React.useState<string>(
    generateRandomGUIDForCacheInvalidation()
  );

  return (
    <FieldArray
      name={name as string}
      render={arrayHelpers => {
        const items = arrayHelpers.form.values[arrayHelpers.name]
          ? arrayHelpers.form.values[arrayHelpers.name]
          : getNestedFormValueItems(
              arrayHelpers.form.values,
              arrayHelpers.name
            );

        if (items && items.length > 0) {
          return (
            <OfferSubmissionFormPanel title={label + " Translations"}>
              {items.map((_: any, index: number) => {
                return (
                  <CustomLocalizationTextField
                    label={label}
                    name={name}
                    index={index}
                    key={key + index}
                    removeItem={
                      items.length > 1 || optional
                        ? (i: number) => {
                            setKey(generateRandomGUIDForCacheInvalidation());
                            arrayHelpers.remove(i);
                          }
                        : undefined
                    }
                    items={items}
                  />
                );
              })}
              {(maxItems ? items.length < maxItems : true) && (
                <Grid
                  direction="row"
                  container={true}
                  item={true}
                  justify="flex-end"
                  alignItems="center"
                  className={classes.addItemSection}
                >
                  <Fab
                    size="small"
                    color="primary"
                    aria-label="Add"
                    onClick={() => arrayHelpers.push({})}
                  >
                    <AddIcon />
                  </Fab>
                  <Typography
                    variant="button"
                    color="primary"
                    onClick={() => arrayHelpers.push({})}
                  >
                    &nbsp;&nbsp;Add {label}
                  </Typography>
                </Grid>
              )}
            </OfferSubmissionFormPanel>
          );
        } else {
          return (
            <Grid
              direction="row"
              item={true}
              container={true}
              alignItems={"center"}
              className={classes.addItemButtonHolder}
              onClick={() => arrayHelpers.push({})}
            >
              <Fab size="small" color="primary" aria-label="Add">
                <AddIcon />
              </Fab>
              <Typography variant="button" color="primary">
                &nbsp;&nbsp;&nbsp;&nbsp;Add {label}
              </Typography>
              {optional && (
                <Typography variant="subtitle2" color="primary">
                  &nbsp;(Optional)
                </Typography>
              )}
            </Grid>
          );
        }
      }}
    />
  );
};

// This function will handle for arrays in nested paths for up to one level deep
// Will convert tiers[0].content into items
const getNestedFormValueItems = (formValues: any, path: string) => {
  const pathProps = path
    .replace(".", "")
    .replace("[", ",")
    .replace("]", ",")
    .split(",");

  const base = pathProps[0];
  const index = pathProps[1];
  const field = pathProps[2];

  let items;
  if (
    formValues[base] &&
    formValues[base][index] &&
    formValues[base][index][field]
  ) {
    items = formValues[base][index][field];
  }
  return items;
};
const styles = createStyles((theme: Theme) => ({
  addItemButtonHolder: {
    marginBottom: "0.8em"
  },
  addItemSection: {
    backgroundColor: `${theme.palette.grey[300]}`,
    borderTop: "1px solid #1790CC",
    marginTop: ".8em",
    padding: "0.5em"
  },
  optionalParagraphHolder: {
    padding: "0.5em"
  }
}));

export default withStyles(styles)(CustomLocalizationFieldArray);
