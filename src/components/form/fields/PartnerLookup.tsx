import { MenuItem } from "@material-ui/core";
import { chain, find } from "lodash";
import React, { FunctionComponent, useState } from "react";
import { BB8Lookup, IRenderOptionProps } from "../../../BB8";
import { IPartner } from "../../../models/Partner";

export const PartnerLookup: FunctionComponent<{
  name: string;
  value?: string;
  placeholder: string;
  partners: IPartner[];
  disabled?: boolean;
  onSelect: (selectedPartner?: IPartner) => void;
}> = props => {
  const { value, partners, placeholder, onSelect, disabled } = props;
  const renderPartnerOption: React.FunctionComponent<
    IRenderOptionProps<IPartner>
  > = renderOptionProps => {
    const isHighlighted =
      renderOptionProps.highlightedIndex === renderOptionProps.index;
    return (
      <MenuItem
        {...renderOptionProps.itemProps}
        key={renderOptionProps.option.id}
        selected={isHighlighted}
      >
        {renderOptionProps.option.name}
      </MenuItem>
    );
  };

  function getPartnerLookupOptions(
    partners2: IPartner[] | undefined,
    searchString2: string
  ) {
    return chain(partners2)
      .filter(partner =>
        partner.name.toLowerCase().includes(searchString2.trim().toLowerCase())
      )
      .sortBy(["name"], ["asc"])
      .value();
  }

  function getPartnerNameFromId(partnerId: any) {
    const found = find(partners, partner => partner.id === partnerId);
    return found ? found.name : "";
  }

  function getPartner(partner: IPartner | string): string {
    if (typeof partner === "string") {
      return partner;
    } else {
      return partner ? partner.name : "";
    }
  }
  const [searchString, setPartnerSearch] = useState<string>("");
  const partnersOptions = getPartnerLookupOptions(partners, searchString);

  return (
    <BB8Lookup
      name={name}
      options={partnersOptions}
      renderOption={renderPartnerOption}
      onSelect={(selectedPartner: IPartner) => onSelect(selectedPartner)}
      onInputChange={e => {
        if (e.target.value.length === 0) {
          onSelect(); // select no partner
        }
        setPartnerSearch(e.target.value);
      }}
      onInputBlur={() => setPartnerSearch("")}
      itemToString={getPartner}
      value={getPartnerNameFromId(value)}
      shouldOpenOnFocus={true}
      placeholder={placeholder || "Type Partner Name"}
      className="partner-lookup"
      disabled={disabled ? disabled : false}
      classes={ {} }
    />
  );
};
