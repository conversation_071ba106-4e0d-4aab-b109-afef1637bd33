import {
  Checkbox,
  createSty<PERSON>,
  FormControlLabel,
  FormLabel,
  Grid,
  withSty<PERSON>,
  WithStyles
} from "@material-ui/core";
import { FormikProps } from "formik";
import React from "react";
import OfferSubmissionFormPanel from "../../../features/offer-submission/components/OfferSubmissionFormPanel";
import { IOfferFormModel } from "../../../validation/validator";
import { FIELDS_CONFIG } from "../configs/fields.config";
import HelperTooltip from "../widgets/HelperTooltip";
import CustomLocalizationTextField from "./CustomLocalizationTextField";

export interface IOfferCombinationsProps {
  formikProps: FormikProps<Partial<IOfferFormModel>>;
}

class OfferCombinations extends React.PureComponent<
  WithStyles & IOfferCombinationsProps
> {
  public render() {
    const { formikProps } = this.props;
    const canBeCombined = formikProps.values.canBeCombined;
    const { classes } = this.props;

    return (
      <>
        <Grid
          direction="row"
          item={true}
          container={true}
          alignItems={"center"}
        >
          <Grid item={true} xs={12}>
            <FormControlLabel
              key="canBeCombined"
              control={
                <Checkbox
                  className={classes.checkboxPadding}
                  checked={canBeCombined}
                  color="primary"
                  onChange={e =>
                    formikProps.setFieldValue("canBeCombined", !canBeCombined)
                  }
                  value={canBeCombined}
                  name="canBeCombined"
                />
              }
              label="Can be combined with other offers?"
            />
            <HelperTooltip text={FIELDS_CONFIG["canBeCombined"].toolTip} />
          </Grid>

          <OfferSubmissionFormPanel
            title={
              canBeCombined
                ? "Offer Combinations Allowed Translations"
                : "Offer Combinations NOT Allowed Translations"
            }
            tooltipText={`Indicate which offer(s) your offer can 
              ${canBeCombined ? "" : "NOT"} be combined with.`}
          >
            <CustomLocalizationTextField
              name="combinationsText"
              label={
                canBeCombined
                  ? "Offer Combinations Allowed"
                  : "Offer Combinations NOT Allowed"
              }
            />
          </OfferSubmissionFormPanel>
        </Grid>
      </>
    );
  }
}

const styles = createStyles(() => ({
  checkboxPadding: { padding: "0.2em 0.5em" }
}));

export default withStyles(styles)(OfferCombinations);
