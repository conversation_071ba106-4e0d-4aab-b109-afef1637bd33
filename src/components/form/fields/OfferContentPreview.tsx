import { Grid, Typography } from "@material-ui/core";
import React from "react";
import { ILocalizedObject } from "../../../validation/validator";
import { get, endsWith } from "lodash";
import "./OfferContentPreview.scss";

interface IOfferContentPreviewProps {
  awardText: ILocalizedObject;
  qualifierText: ILocalizedObject;
  previewTitle: string;
  showBorder?: boolean;
}

const OfferContentPreview: React.FunctionComponent<
  IOfferContentPreviewProps
> = props => {
  const { awardText, qualifierText, previewTitle, showBorder } = props;

  function renderContentPreviewSection(locale: "en-US" | "fr-CA") {
    return (
      <Grid style={{ padding: "0.4em" }} item={true} xs={12} sm={6} md={6}>
        <Grid direction="row" container={true}>
          <Typography variant="subtitle1" color="initial">
            {previewTitle}
          </Typography>
          <Typography variant="subtitle1" color="secondary">
            &nbsp;({locale === "en-US" ? "English" : "French"})
          </Typography>
        </Grid>
        <div className="offer-content-preview-locale-section">
          <div className="offer-content-preview-awardText">
            {awardText ? awardText[locale] : ""}
          </div>
          <div className="offer-content-preview-qualifierText">
            {qualifierText ? qualifierText[locale] : ""}
          </div>
        </div>
      </Grid>
    );
  }

  return (
    <Grid
      container={true}
      item={true}
      xs="auto"
      className={
        showBorder
          ? `offer-content-preview-block-borders`
          : `offer-content-preview-block`
      }
    >
      {renderContentPreviewSection("en-US")}
      {renderContentPreviewSection("fr-CA")}
    </Grid>
  );
};

export default OfferContentPreview;
