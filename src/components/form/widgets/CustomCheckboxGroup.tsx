import {
  Checkbox,
  FormControlLabel,
  FormGroup,
  FormLabel,
  Grid,
  Typography
} from "@material-ui/core";
import classNames from "classnames";
import { FieldArray, FieldArrayRenderProps } from "formik";
import React from "react";
import "./CustomCheckboxGroup.scss";
import HelperTooltip from "./HelperTooltip";

export interface ICustomCheckboxGroupWidgetProps {
  values: { name: string; value: string | number }[];
  isColumn?: boolean;
  title?: string;
  tooltipText?: string;
  name: string;
  onChange?: (
    event: React.ChangeEvent<HTMLInputElement>,
    checked: boolean
  ) => void;
}

export default class CustomCheckboxGroup extends React.PureComponent<
  ICustomCheckboxGroupWidgetProps
> {
  public render() {
    const { name } = this.props;
    return <FieldArray name={name} render={this.renderFieldArray} />;
  }

  private renderFieldArray = (arrayHelpers: FieldArrayRenderProps) => {
    const { values, isColumn, title, tooltipText, name, onChange } = this.props;
    const formGroupClassNames = classNames("form-group-wrapper", {
      column: isColumn
    });

    return (
      <>
        <Grid item={true} xs={12} container={true} alignItems="center">
          <Typography variant="subtitle1" color="initial">
            {title}
          </Typography>

          {tooltipText && <HelperTooltip text={tooltipText} />}
        </Grid>
        <FormGroup className={formGroupClassNames}>
          {values.map(value => (
            <FormControlLabel
              key={value.value}
              control={
                <Checkbox
                  style={{ padding: "0.2em 0.5em" }}
                  checked={
                    arrayHelpers.form.values[name]
                      ? arrayHelpers.form.values[name].includes(value.value)
                      : false
                  }
                  color="primary"
                  onChange={this.handleChange(arrayHelpers, onChange)}
                  value={value.value}
                  name={name}
                />
              }
              label={value.name}
            />
          ))}
        </FormGroup>
      </>
    );
  };

  private handleChange = (
    arrayHelpers: FieldArrayRenderProps,
    onChange?: (
      event: React.ChangeEvent<HTMLInputElement>,
      checked: boolean
    ) => void
  ) => (e: React.ChangeEvent<HTMLInputElement>, checked: boolean) => {
    if (e.target.checked) {
      arrayHelpers.push(e.target.value);
    } else {
      const vals = arrayHelpers.form.values[arrayHelpers.name];
      const idx = vals.indexOf(e.target.value);
      arrayHelpers.remove(idx);
      if (vals.length <= 1) {
        arrayHelpers.form.setFieldValue(arrayHelpers.name, undefined);
      }
    }
    if (onChange) {
      onChange(e, checked);
    }
  };
}
