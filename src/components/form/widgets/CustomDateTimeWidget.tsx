import { createStyles, FormHelperText, Grid, Typography, WithStyles, withStyles } from "@material-ui/core";
import classNames from "classnames";
import { Field, FieldProps } from "formik";
import { InlineDatePicker, InlineDateTimePicker } from "material-ui-pickers";
import moment from "moment";
import caLocale from "moment/locale/en-ca";
import React from "react";
import { DATE_FULL_YEAR_TIME_SECONDS_FORMAT, DATE_TIME_FORMAT } from "../../../features/offer-submission/utils";
import { FieldKeys, FIELDS_CONFIG } from "../configs/fields.config";
import HelperTooltip from "./HelperTooltip";
import "./CustomDateTimeWidget.scss";

const styles = createStyles(() => ({
    humanDate: {
        backgroundColor: "#f1f2f2",
        border: "10px solid #f1f2f2",
        textAlign: "center",
        width: "100%"
    },
    datePicker: {
        width: "100%",
        margin: "0 0 0.5rem 0"
    },
    datePickerError: {
        outline: "#D30E8B solid 3px",
        borderColor: "#f7e0ed",
        outlineOffset: "-3px",
        backgroundColor: "#f7e0ed"
    },
    datePickerHolder: {
        padding: "0 0 1rem 0"
    },
    toolTipHolder: {
        padding: "2px 0 0 0"
    }
}));

export interface IDateTimePickerProps extends WithStyles {
    timePicker?: boolean;
    name: string;
}

/**
 * As by default, we can't select time, when a user selects a date,
 * we want to default to the end of day (11:59 pm) if user is selecting
 * "End Date" of the offer, otherwise, round down to start of the day (12:00 AM)
 * @param val the value of the time that's being set
 * @param fieldName the field name that's being modified
 */
function roundToTheCorrectHour(val: Date, fieldName?: string) {
    const desiredDate = new Date(val);
    if (fieldName === "endDate" || fieldName === "lastQualificationDate") {
        desiredDate.setUTCHours(23, 59, 59, 999);
    } else {
        desiredDate.setUTCHours(0, 0, 0, 0);
    }
    return desiredDate.toISOString();
}

const CustomDateTimeWidget: React.FunctionComponent<IDateTimePickerProps> = ({
                                                                                 classes,
                                                                                 name,
                                                                                 timePicker = false
                                                                             }) => {
    moment.locale('en-ca', [caLocale]);

    const fieldConfig = FIELDS_CONFIG[name as FieldKeys];
    const DateComponent: React.FunctionComponent<any> = timePicker
        ? InlineDateTimePicker
        : InlineDatePicker;
    const format = timePicker
        ? DATE_FULL_YEAR_TIME_SECONDS_FORMAT
        : DATE_TIME_FORMAT;

    return (
        <Grid
            container={true}
            item={true}
            direction="row"
            spacing={1}
            className={classes.datePickerHolder}
            xs={12}
        >
            <Grid item={true} spacing={0} container={true} xs="auto" direction="row">
                <Grid item={true}>
                    <Typography variant="subtitle1" color="initial">
                        {fieldConfig.title}
                    </Typography>
                </Grid>
                <Grid item={true} className={classes.toolTipHolder}>
                    <HelperTooltip text={fieldConfig.toolTip}/>
                </Grid>
            </Grid>
            <Grid container={true} item={true} xs={12}>
                <Field name={name}>
                    {(fieldProps: FieldProps) => {
                        const errors: any[] | undefined = fieldProps.form.errors[
                            fieldProps.field.name
                            ] as any;

                        if (!fieldProps.field.value) {
                            fieldProps.form.setFieldValue(
                                name,
                                roundToTheCorrectHour(new Date(), name),
                                true
                            );
                        }

                        return (
                            <>
                                <Grid item={true} xs={12}>
                                    <DateComponent
                                        className={classNames(classes.datePicker, {
                                            [classes.datePickerError]: Boolean(errors)
                                        })}
                                        InputAdornmentProps={{
                                            className: classNames({
                                                [classes.datePickerError]: Boolean(errors)
                                            })
                                        }}
                                        // TextFieldComponent={DefaultTextField}
                                        clearable={true}
                                        keyboard={true}
                                        value={moment.utc(fieldProps.field.value)}
                                        variant="outlined"
                                        onChange={(val: Date) => {
                                            fieldProps.form.setFieldValue(
                                                name,
                                                roundToTheCorrectHour(val, name),
                                                true
                                            );
                                        }}
                                        format={format}
                                    />
                                </Grid>
                                <Grid item={true} xs={12}>
                                    <div className={classes.humanDate}>
                                        <span className="date-format-text">
                                          {moment.utc(fieldProps.field.value).format("LLLL")}
                                        </span>
                                    </div>
                                </Grid>
                                {(errors || []).map((error: any) => (
                                    <FormHelperText error={true}>{`${
                                        error.message
                                    }`}</FormHelperText>
                                ))}
                            </>
                        );
                    }}
                </Field>
            </Grid>
        </Grid>
    );
};

export const CustomStyledDateTimeWidget = withStyles(styles)(
    CustomDateTimeWidget
);
