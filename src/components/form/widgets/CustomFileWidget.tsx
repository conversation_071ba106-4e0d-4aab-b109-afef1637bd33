import { Form<PERSON><PERSON>perText, Grid, WithStyles, withStyles } from "@material-ui/core";
import Uppy, { UppyOptions } from "@uppy/core";
import classNames from "classnames";
import { set } from "lodash";
import React from "react";
import { BB8Button } from "../../../BB8";
import { buildErrorMessage } from "../../../features/offer-submission/pages/ErrorSummary";
import { getDiffOfObjects } from "../../../shared/helpers";
import imageService, { MIN_IMG_HEIGHT, MIN_IMG_WIDTH } from "../../../shared/services/image.service";
import { IValidationErrors } from "../../../validation/validator";
import FileUploader from "../../FileUploader";
import ImageCropper from "../../ImageCropper";
import "./CustomFileWidget.scss";

export interface ICustomFileWidgetState {
  // The local file they're modifying so that they can see all their changes for each cropping
  fileToUpload: Uppy.UppyFile<{
    isValid?: boolean;
    error?: IValidationErrors;
  }> | null;
  croppedFile: { fileDetails: { [key: string]: string }; blob: Blob } | null;
  canCropImage: boolean;
}

/**
 * This is used for image.en-US.path or image.fr-CA.path
 * We stringify this object and pass it as the URL so that we push relevant data up to the form that is needed for file uploading
 * including Mime Type and the file name
 */
export interface IUploadImageData {
  blobData: string;
  name: string;
  mimeType: string;
  valid: boolean;
  error?: IValidationErrors;
}

interface ICustomFileWidgetProps extends WithStyles {
  name: string;
  value: string;
  onChange: (value?: string) => void;
  onError?: (error: IValidationErrors[]) => void;
  error?: IValidationErrors[];
}

export class CustomFileWidget extends React.PureComponent<
  ICustomFileWidgetProps,
  ICustomFileWidgetState
> {
  private uppyConfig: Partial<UppyOptions> = {
    restrictions: {
      maxNumberOfFiles: 1,
      allowedFileTypes: [
        "image/jpg",
        "image/jpeg",
        "image/png",
        "image/gif",
        ".jpg",
        ".jpeg",
        ".png",
        ".gif"
      ],
      maxFileSize: 10000000,
      minNumberOfFiles: 1
    },
    meta: {},
    autoProceed: false,
    allowMultipleUploads: false
  };

  constructor(props: ICustomFileWidgetProps) {
    super(props);
    this.state = {
      fileToUpload: null,
      croppedFile: null,
      canCropImage: false
    };
  }

  public componentDidMount() {
    const { value } = this.props;
    if (value) {
      this.getExistingImageFromOffer(value);
    }
  }

  public componentDidUpdate(prevProps: any, prevState: any) {
    const diffObj = getDiffOfObjects(prevProps, this.props);

    if (diffObj.value) {
      const { value } = this.props;
      /**
       * FIXME: this is a dumb solution, but we need default images for
       * certain offer types and qualififers, so in those cases we'll
       * get just a URL instead of the IUploadImageData that we normally work with
       * If we can't JSON.parse the value, we know it must not be an object,
       * so we should go and fetch the image URL manually
       */

      if (!value) {
        return;
      }
      try {
        JSON.parse(value);
      } catch (error) {
        this.getExistingImageFromOffer(value);
      }
    }
  }

  public render() {
    const { name, error } = this.props;
    const { fileToUpload: updatedFile, croppedFile, canCropImage } = this.state;
    return (
      <Grid container={true} item={true} xs={12} spacing={2}>
        <Grid
          item={true}
          md={12}
          lg={6}
          className={classNames("fileuploader__wrapper", {
            fileuploader__wrapper_error: Boolean(this.props.error)
          })}
        >
          <FileUploader
            onFileAdded={this.onFileAdded}
            onFileRemoved={this.onFileRemoved}
            onCancelAll={this.onFileRemoved}
            uppyConfig={this.uppyConfig}
            croppedImage={croppedFile}
            onUpdateToCroppedImage={() => this.setState({ croppedFile: null })}
            width={250}
            height={182}
            inline={true}
            note="Choose a Partner Image"
            showProgressDetails={true}
            hideUploadButton={true}
          />
          <FormHelperText error={Boolean(error)}>
            {Array.isArray(error) &&
              error.map((e, i) => {
                if (e.keyword !== "required") {
                  return <span key={i}>{buildErrorMessage(name)(e)}</span>;
                }
              })}
          </FormHelperText>
        </Grid>
        <Grid
          item={true}
          md={12}
          lg={6}
          alignItems="center"
          justify="center"
          container={true}
        >
          <ImageCropper
            file={updatedFile}
            onFileCropped={this.onFileCropped}
            canCropImage={canCropImage}
            onCropCancel={this.handleCropCancel}
          >
            {updatedFile != null && !canCropImage && (
              <Grid>
                <BB8Button
                  color="primary"
                  variant="contained"
                  onClick={this.toggleEdit}
                >
                  Edit
                </BB8Button>
              </Grid>
            )}
          </ImageCropper>
        </Grid>
      </Grid>
    );
  }

  /**
   * If the image already exists from the offer, we have to manually
   * fetch the image from the source, and transform it to a blob
   * so that we can add it to croppy and uppy
   */
  private getExistingImageFromOffer = (url: string) => {
    const xhr = new XMLHttpRequest();
    xhr.open("GET", url, true);
    xhr.responseType = "blob";
    const setState = (blob: Blob) => {
      this.setState({
        croppedFile: {
          fileDetails: {
            name: url,
            type: blob.type
          },
          blob,
          fileToUpload: blob
        }
      });
    };

    try {
      const stringify = this.stringifyImageAndTransformationData;

      xhr.onload = function(e: Event) {
        if (this.status !== 200) {
          return;
        }
        setState(this.response);
        stringify();
      };
      xhr.send();
    } catch (error) {
      console.error(error);
      throw error;
    }
  };

  /**
   * When a new file is added to uppy
   */
  private onFileAdded = async (files: Uppy.UppyFile[]) => {
    // validate before doing something
    const blob = await files[0].data;
    const isValid = await imageService.isValid(blob);
    const error: Partial<IValidationErrors> = {};
    if (!isValid) {
      // const { onError } = this.props;
      // if (onError) {
      error.data = blob;
      error.dataPath = this.props.name;
      error.keyword = "size";
      error.message =
        "Image is too small, please upload an image larger than " +
        MIN_IMG_HEIGHT +
        "px x " +
        MIN_IMG_WIDTH +
        "px";
      error.params = {};
      error.parentSchema = "";
      error.schema = "";
      error.schemaPath = "";
      // };
      // onError([error]);
      // }
    }
    const newState = {
      fileToUpload: files[0]
    };
    if (!isValid) {
      set(newState, "fileToUpload.meta.isValid", false);
      set(newState, "fileToUpload.meta.error", error);
    }
    this.setState(newState, () => {
      this.stringifyImageAndTransformationData();
    });
    return true;
  };

  /**
   * stringify the blob data  for the API to handle later
   */
  private stringifyImageAndTransformationData = () => {
    const { fileToUpload } = this.state;
    const { onChange } = this.props;
    const getMimeType = this.getMimeTypeFromFile;

    if (fileToUpload) {
      const fr = new FileReader();
      fr.onloadend = async () => {
        const mimeType = await getMimeType(fileToUpload.data);
        const imageData: IUploadImageData = {
          blobData: fr.result,
          name: Date.now() + "-" + fileToUpload.name,
          mimeType,
          valid: fileToUpload.meta.isValid,
          error: fileToUpload.meta.error
        } as IUploadImageData;
        const stringifedData = JSON.stringify(imageData);
        onChange(stringifedData);
      };
      fr.readAsDataURL(fileToUpload.data);
    }
  };

  /**
   * File is removed from uppy
   */
  private onFileRemoved = () => {
    const { onChange } = this.props;

    onChange(undefined);
    this.setState({
      fileToUpload: null,
      croppedFile: null
    });
  };

  private onFileCropped = (
    origFileDetails: { [key: string]: any },
    file: Blob
  ) => {
    this.setState({
      croppedFile: {
        fileDetails: origFileDetails,
        blob: file
      },
      canCropImage: false
    });
  };

  /**
   * Use this to get a more secure mime type for the upload file. Since the mime type could be spoofed if a person changes the extension
   * we check for specific values to try get a proper guess MIME type
   * https://stackoverflow.com/questions/18299806/how-to-check-file-mime-type-with-javascript-before-upload/29672957#29672957
   * @param blob
   */
  private getMimeTypeFromFile = (blob: Blob) => {
    return new Promise((resolve, reject) => {
      try {
        const fileReader = new FileReader();
        fileReader.onloadend = (e: any) => {
          const arr: Uint8Array = new Uint8Array(e.target.result).subarray(
            0,
            4
          );
          let header: string = "";
          for (let i = 0; i < arr.length; i++) {
            header += arr[i].toString(16);
          }
          let type: string = "";
          switch (header) {
            case "89504e47":
              type = "image/png";
              break;
            case "47494638":
              type = "image/gif";
              break;
            case "ffd8ffe0":
            case "ffd8ffe1":
            case "ffd8ffe2":
            case "ffd8ffe3":
            case "ffd8ffe8":
              type = "image/jpeg";
              break;
            default:
              console.error(
                "unknown file type, falling back to provided: " + blob.type
              );
              type = blob.type; // Or you can use the blob.type as fallback
              break;
          }
          resolve(type);
        };
        fileReader.readAsArrayBuffer(blob);
      } catch (error) {
        reject(error);
      }
    });
  };

  private toggleEdit = () => {
    this.setState(currentState => ({
      canCropImage: !currentState.canCropImage
    }));
  };
  private handleCropCancel = () => {
    this.setState({
      canCropImage: false
    });
  };
}

export default withStyles(() => ({
  root: {
    height: "100px",
    overflow: "hidden"
  }
}))(CustomFileWidget);
