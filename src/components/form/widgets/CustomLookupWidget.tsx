import { createStyles, FormHelperText, Grid, MenuItem, Typography, withStyles, WithStyles } from "@material-ui/core";
import { SelectProps } from "@material-ui/core/Select";
import { Field, FieldProps as FormikFieldProps, FormikProps } from "formik";
import { chain, find, isEmpty } from "lodash";
import React, { FunctionComponent, useEffect, useState } from "react";
import { BB8Lookup, IRenderOptionProps } from "../../../BB8";
import { FEATURE_FLAG, featureFlagCompare } from "../../../features/offer-submission/components/FeatureFlag";
import { buildErrorMessage } from "../../../features/offer-submission/pages/ErrorSummary";
import { IIssuance } from "../../../models/Issuance";
import { ISponsor } from "../../../models/Sponsor";
import { IOfferFormModel, IValidationErrors } from "../../../validation/validator";
import { <PERSON>Keys, FIELDS_CONFIG } from "../configs/fields.config";
import HelperTooltip from "./HelperTooltip";

const styles = createStyles(() => ({
  toolTipHolder: {
    padding: "2px 0 0 0"
  }
}));

const LookupWidget: FunctionComponent<
  SelectProps &
    WithStyles & {
      name: string;
      options: any[];
      hasTooltip?: boolean;
      optional?: boolean;
      disabled?: boolean;
      canSelectEmptyValue?: boolean;
      onChange?: (e: React.ChangeEvent<HTMLSelectElement>) => void;
      onSelect: (setFieldValue: any, selectedOption?: any) => void;
      placeholder: string;
      formikProps?: FormikProps<IOfferFormModel>;
    }
> = props => {
  const {
    options,
    name,
    hasTooltip,
    optional,
    disabled,
    canSelectEmptyValue,
    classes,
    onChange,
    onSelect,
    placeholder,
    formikProps,
    ...otherProps
  } = props;
  const fieldConfig = FIELDS_CONFIG[name as FieldKeys];

  const renderOption: React.FunctionComponent<
    IRenderOptionProps<ISponsor | IIssuance>
  > = renderOptionProps => {
    const isHighlighted =
      renderOptionProps.highlightedIndex === renderOptionProps.index;

    const optionProps = getOptionValue(renderOptionProps.option);

    return (
      <MenuItem
        {...renderOptionProps.itemProps}
        key={optionProps.key}
        selected={isHighlighted}
      >
        {optionProps.display}
      </MenuItem>
    );
  };

  function getOptionValue(option: ISponsor | IIssuance) {
    const optionValue = option ? isSponsorType(option)
    ? {
        key: option.issuerCode,
        display: `${option.issuerCode} | ${option.issuerName}`
    }
    : {
        key: option.offerCode,
        display: `${option.offerCode} | ${option.offerDescription}`
    }
    : {key: '', display: ''}
    return optionValue;
  }

  function getItemToString(itemToString: ISponsor | IIssuance | string): string {
    if (typeof itemToString === "string") {
      return itemToString;
    } else {
      const optionValue = getOptionValue(itemToString)
      return optionValue ? optionValue.display : "";
    }
  }

  // returns the option display in the format "code | description"
  function getOptionDisplayFromId(optionId: any) {
    const mappedOptions = options.map(option => getOptionValue(option));
    const found = find(mappedOptions, mappedValue => mappedValue.key === optionId);
    return found ? found.display : optionId;
  }

  // returns the full selected option object
  function getSelectedOptionFromId(optionId: any) {
    let selectedOption;
    options.forEach(
        (option) => {
          if (isSponsorType(option)) {
            if (option.issuerCode === optionId) {
              selectedOption = option;
            }
          } else {
            if (option.offerCode === optionId) {
              selectedOption = option;
            }
          }
        }
    );

    return selectedOption;
  }

  let toolTip: any;
  let optionalField: any;
  if (hasTooltip) {
    toolTip = <HelperTooltip text={fieldConfig.toolTip} />;
  }
  if (optional) {
    optionalField = (
      <Typography variant="subtitle1" color="primary">
        &nbsp;(Optional)
      </Typography>
    );
  }

  function isSponsorType(option: any): option is ISponsor {
    return 'issuerName' in option;
  }

  function getLookupOptions(
    optionsArray: ISponsor[] | IIssuance[] | undefined,
    lookupSearchString: string
  ) {
    return chain(optionsArray)
      .filter((option: ISponsor | IIssuance) => {
        const optionKey = getOptionValue(option).display;
        return optionKey.toLowerCase().includes(lookupSearchString.trim().toLowerCase())
      })
      .sortBy(["asc"])
      .value();
  }

  const featureFlagSponsorCode = featureFlagCompare(FEATURE_FLAG.OFFER_ISSUER_DROPDOWN, "true");

  const [searchString, setSearchString] = useState<string>("");
  const lookupOptions = getLookupOptions(options, searchString);

  const [optionValue, setOptionValue] = useState("");
  const [optionDisplay, setOptionDisplay] = useState("");
  const [fieldValueFunc, setFieldValueFunc] = useState<Function>((value: any) => {
    if (formikProps) {
      formikProps.setFieldValue(name, value);
    }
  });

  useEffect(() => {
    if (featureFlagSponsorCode) {
      if (optionValue && options) {
        setOptionDisplay(getOptionDisplayFromId(optionValue));
      }
    }
  }, [optionValue, options]);

  useEffect(() => {
    if (featureFlagSponsorCode) {
      if (formikProps && optionValue && optionValue.length > 0) {
        const selectedOption = getSelectedOptionFromId(optionValue);

        if (selectedOption) {
          onSelect(
              fieldValueFunc,
              selectedOption
          );
        }
      }
    }
  }, [optionDisplay]);

  return (
    <Field name={name}>
      {(fieldProps: FormikFieldProps) => {
        const touched: any = fieldProps.form.touched[fieldProps.field.name];
        const errors: any = fieldProps.form.errors[fieldProps.field.name];

        if (featureFlagSponsorCode) {
          if (fieldProps && fieldProps.field && fieldProps.field.value && optionValue === "") {
            setOptionValue(fieldProps.field.value);
            setFieldValueFunc(() => fieldProps.form.setFieldValue);
          }
        }

        return (
          <>
            <Grid container={true} direction="row">
              <Grid item={true}>
                <Typography
                  variant="subtitle1"
                  color="initial"
                  gutterBottom={true}
                >
                  {otherProps.title}
                </Typography>
              </Grid>
              <Grid item={true}> {optionalField}</Grid>
              <Grid item={true} className={classes.toolTipHolder}>
                {toolTip}
              </Grid>
            </Grid>
            <BB8Lookup
              name={name}
              options={lookupOptions}
              renderOption={renderOption}
              onSelect={
                (selectedOption?: ISponsor | IIssuance) => {
                  // If selectedOption contains offerCode attribute, it means is an instance of IIssuance
                  if (selectedOption && "offerCode" in selectedOption) {
                    setOptionValue(selectedOption.offerCode);
                    setFieldValueFunc(() => fieldProps.form.setFieldValue);
                  } else if(selectedOption) {
                    setOptionValue(selectedOption.issuerCode);
                    setFieldValueFunc(() => fieldProps.form.setFieldValue);
                  }
                }
              }
              onInputChange={e => {
                if (e.target.value.length === 0) {
                  onSelect(fieldProps.form.setFieldValue); // select no option - clear the field
                }
                setSearchString(e.target.value);
              }}
              onInputBlur={() => setSearchString("")}
              itemToString={getItemToString}
              value={featureFlagSponsorCode ? optionDisplay : getOptionDisplayFromId(fieldProps.field.value)}
              shouldOpenOnFocus={true}
              placeholder={placeholder}
              className="lookupWidget"
              disabled={disabled ? disabled : false}
              error={Boolean(errors)}
              classes={ {} }
            />
            {!isEmpty(errors) && (
              <FormHelperText error={Boolean(errors)}>
                {errors.map((error: IValidationErrors, index: number) => {
                  return (
                    <span key={index}>{buildErrorMessage(name)(error)}</span>
                  );
                })}
              </FormHelperText>
            )}
          </>
        );
      }}
    </Field>
  );
};

const CustomLookupWidget = React.memo(withStyles(styles)(LookupWidget));
export default CustomLookupWidget;
