import {
  createStyles,
  FilledInput,
  FormHelperText,
  Grid,
  MenuItem,
  Select,
  Typography,
  withStyles,
  WithStyles
} from "@material-ui/core";
import { SelectProps } from "@material-ui/core/Select";
import { Field, FieldProps as FormikFieldProps } from "formik";
import { get, isEmpty } from "lodash";
import React, { FunctionComponent, ReactNode } from "react";
import { getStaticFilterName } from "../../../models/IStaticFilterOption";
import { IValidationErrors } from "../../../validation/validator";
import { FIELDS_CONFIG, FieldKeys } from "../configs/fields.config";
import HelperTooltip from "./HelperTooltip";
import { buildErrorMessage } from "../../../features/offer-submission/pages/ErrorSummary";

const styles = createStyles(() => ({
  toolTipHolder: {
    padding: "2px 0 0 0"
  }
}));

const SelectWidget: FunctionComponent<
  SelectProps &
    WithStyles & {
      name: string;
      options: any[];
      hasTooltip?: boolean;
      optional?: boolean;
      canSelectEmptyValue?: boolean;
      onChange?: (e: React.ChangeEvent<HTMLSelectElement>) => void;
    }
> = props => {
  const {
    options,
    name,
    hasTooltip,
    optional,
    canSelectEmptyValue,
    classes,
    onChange,
    ...otherProps
  } = props;
  const fieldConfig = FIELDS_CONFIG[name as FieldKeys];

  let toolTip: any;
  let optionalField: any;
  if (hasTooltip) {
    toolTip = <HelperTooltip text={fieldConfig.toolTip} />;
  }
  if (optional) {
    optionalField = (
      <Typography variant="subtitle1" color="primary">
        &nbsp;(Optional)
      </Typography>
    );
  }

  return (
    <Field name={name}>
      {(fieldProps: FormikFieldProps) => {
        const touched: any = fieldProps.form.touched[fieldProps.field.name];
        const errors: any = fieldProps.form.errors[fieldProps.field.name];

        return (
          <>
            <Grid container={true} direction="row">
              <Grid item={true}>
                <Typography
                  variant="subtitle1"
                  color="initial"
                  gutterBottom={true}
                >
                  {otherProps.title}
                </Typography>
              </Grid>
              <Grid item={true}> {optionalField}</Grid>
              <Grid item={true} className={classes.toolTipHolder}>
                {toolTip}
              </Grid>
            </Grid>
            <Select
              id={fieldProps.field.name}
              {...fieldProps.field}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
                fieldProps.field.onChange(e);
                // fieldProps.form.setTouched({ [e.target.name]: true });
                if (onChange) {
                  onChange(e);
                }
              }}
              onBlur={(e: React.FocusEvent<HTMLDivElement>) => {
                // fieldProps.field.onChange(e);
              }}
              {...otherProps as SelectProps}
              fullWidth={true}
              displayEmpty={true}
              error={Boolean(errors)}
              input={
                <FilledInput
                  disableUnderline={true}
                  fullWidth={true}
                  color="default"
                />
              }
            >
              <MenuItem disabled={!canSelectEmptyValue} value={undefined}>
                <em>{`- Select ${otherProps.title} -`}</em>
              </MenuItem>
              {options.map(
                ({
                  label,
                  value,
                  disabled,
                  hasDivider
                }: {
                  value: any;
                  label: string;
                  disabled?: boolean;
                  hasDivider?: boolean;
                }) => (
                  <MenuItem key={value} value={value} disabled={disabled} divider={hasDivider}>
                    {label}
                  </MenuItem>
                )
              )}
            </Select>
            {!isEmpty(errors) && (
              <FormHelperText error={Boolean(errors)}>
                {errors.map((error: IValidationErrors, index: number) => {
                  if (error.keyword !== "required") {
                    return (
                      <span key={index}>{buildErrorMessage(name)(error)}</span>
                    );
                  }
                })}
              </FormHelperText>
            )}
          </>
        );
      }}
    </Field>
  );
};

export const CustomSelectWidget = React.memo(withStyles(styles)(SelectWidget));
