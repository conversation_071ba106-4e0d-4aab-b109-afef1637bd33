import {FormControl, FormHelperText, Grid, Theme, Typography, WithStyles, withStyles} from "@material-ui/core";
import React, {ReactNode} from "react";
import {connect} from "react-redux";
import {IRootState, withAuthStateToProps} from "../../../store";
import {FormikProps} from "formik";
import {IOfferFormModel, ProgramType} from "../../../validation/validator";
import withAuthorization from "../../../features/auth/components/Authorization";
import CustomCheckboxGroup from "./CustomCheckboxGroup";
import {FEATURE_FLAG, featureFlagCompare} from "../../../features/offer-submission/components/FeatureFlag";
import {FIELDS_CONFIG} from "../configs/fields.config";
import HelperTooltip from "./HelperTooltip";
import { CARD_TYPES } from "../../../models/CardType";

interface IOfferCardTypeProps extends WithStyles {
    formikProps: FormikProps<IOfferFormModel>;
}

const styles = (theme: Theme) => ({
    checkBox: {
        padding: "0.2em 0.5em"
    },
    formLabel: {
        paddingBottom: "1em"
    }
});

const CustomCardTypeWidget: React.FunctionComponent<IOfferCardTypeProps> = ({formikProps}) => {
    const rendercheckBoxes = () => {
        const checkBoxes: ReactNode[] = [];

        if(formikProps.values.programType==='cardlinked'){
                checkBoxes.push(
                    <CustomCheckboxGroup
                        values={CARD_TYPES}
                        isColumn={true}
                        name="cardType"
                    />
                );
        }
        else{
            checkBoxes.push("");
        }
        return checkBoxes;
    }

    // SEND FALSE when we dont want the card type
    // SEND TRUE when we want the card type
    const featureFlagCardType = featureFlagCompare(FEATURE_FLAG.CARD_TYPE, "true");
    let cardTypeForm;
    if ( formikProps.values.programType === ProgramType.CardLinkedOffers && featureFlagCardType ) {
        cardTypeForm =
            <Grid container={true} direction="column">
                <Grid item={true} container={true} direction="row">
                    <Typography
                        variant="h5"
                        component="h5"
                        style={{marginBottom: '3px'}}
                        paragraph={true}
                        gutterBottom={true}
                    >
                        Card Types:
                    </Typography>
                    <HelperTooltip text={FIELDS_CONFIG.cardType.toolTip}/>
                </Grid>
                <Grid item={true} container={true}>
                    <FormControl fullWidth={false} required={true}>
                        {rendercheckBoxes()}
                    </FormControl>
                </Grid>
        </Grid>
    }
    else{
        cardTypeForm = <FormControl>{}</FormControl>
    }
    return cardTypeForm;
}
const mapStateToProps = (state: IRootState) => ({
    ...withAuthStateToProps(state)
});

const styledCustomCardTypeWidget = withStyles(styles)(CustomCardTypeWidget);

export default connect(mapStateToProps)(
    withStyles(styles)(
        withAuthorization(styledCustomCardTypeWidget, ["CREATE_OFFER"])
    )
);
