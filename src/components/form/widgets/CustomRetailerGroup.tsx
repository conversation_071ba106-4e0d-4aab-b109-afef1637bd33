import React from "react";
import {Field, FieldProps} from "formik";
import classNames from "classnames";
import {FormControl, FormHelperText, Grid, InputLabel, Select, Typography} from "@material-ui/core";
import Input from "@material-ui/core/Input";
import MenuItem from "@material-ui/core/MenuItem";
import Checkbox from "@material-ui/core/Checkbox";
import ListItemText from "@material-ui/core/ListItemText";
import {ProgramType} from "../../../validation/validator";
import {FEATURE_FLAG, featureFlagCompare} from "../../../features/offer-submission/components/FeatureFlag";


const RetailerGroup = [
    {
        name: "Dollarama",
        value: "dd4d2074-4fcb-4854-9548-839e31803042"
    },
    {
        name: "LobLaws",
        value: "f207007b-d4a2-497b-878e-937be061b4c5"
    }
];
export default class CustomRetailerGroup extends React.PureComponent {
     public render(): JSX.Element |null {
        return (
            <Field name="retailerGroupId">
                {({ field, form }: FieldProps) => {
                    const errors = form.errors[field.name];
                    const hasError = Boolean(errors);
                    const featureFlagRetailerGroup = featureFlagCompare(FEATURE_FLAG.RETAILER_GROUP, "true");

                    if(form.values.programType !== ProgramType.AMReceipts || !featureFlagRetailerGroup){
                        return null;
                    }
                    return (
                        <div
                            className={classNames("retailer-wrapper", {
                                "retailer-wrapper__error": hasError
                            })}
                        >
                            <FormControl fullWidth error={hasError} variant={"outlined"} margin={'dense'}>
                                <Grid item={true} container={true} direction="row">
                                    <Typography
                                        variant="h4"
                                        color="default"
                                        gutterBottom={true}
                                    >
                                        Retailer Group
                                    </Typography>
                                </Grid>
                                {hasError && (
                                    <FormHelperText>Select atleast one retailer</FormHelperText>
                                )}
                                <InputLabel htmlFor="select-multiple-checkbox" color="default"></InputLabel>
                                <Select
                                    labelId="retailer-group-label"
                                    multiple
                                    value={field.value || []}
                                    disableUnderline={true}
                                    onChange={(event) => {
                                        form.setFieldValue(field.name, event.target.value);
                                    }}
                                    input={<Input id="select-multiple-checkbox" color="default"/>}
                                    renderValue={(selected) =>
                                        (selected as string[]).map((val) => RetailerGroup.find(
                                            (group) => group.value === val)?.name || val).join(', ')}
                                >
                                    {RetailerGroup.map(retailer => (
                                        <MenuItem key={retailer.name} value={retailer.value}>
                                            <Checkbox checked={field.value?.includes(retailer.value)}/>
                                            <ListItemText primary={retailer.name}/>
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </div>
                    );
                }
                }
            </Field>
        );
     }
}
