import { FormHelperText } from "@material-ui/core";
import { Field, FieldConfig, FieldProps } from "formik";
import { isEmpty } from "lodash";
import React, { FunctionComponent, useCallback } from "react";
import { buildErrorMessage } from "../../../features/offer-submission/pages/ErrorSummary";
import {
  DefaultTextField,
  DefaultTextFieldProps
} from "../../DefaultTextField";
export const CustomTextWidget: FunctionComponent<
  DefaultTextFieldProps & FieldConfig & { isArray? : boolean}
> = props => {
  const { type, name, onChange, disabled, isArray = false, ...otherProps } = props;

  const handleOnChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
    , fieldProps: FieldProps<any>) => {
    if (onChange) {
      onChange(e);
      return
    }

    const val: any = e.target.value

    if (isArray) {
      if (!val || !val.trim()) {
        // Empty input becomes empty array
        fieldProps.form.setFieldValue(name, []);
      } else {
        // Split by comma, trim whitespace, and filter empty items
        const arrayValue = val
          .split(',')
          .map(item => item.trim())
          .filter(item => item !== '');
        
        fieldProps.form.setFieldValue(name, arrayValue);
      }
    } else {
      // Original behavior for non-array fields
      fieldProps.form.setFieldValue(
        name,
        val ? (type === "number" ? Number(val) : val) : undefined
      );
    }
  }, [isArray, name, onChange, type])

  const formatValueForDisplay = useCallback((value: any): any => {
    if (isArray && Array.isArray(value)) {
      return value.join(',');
    }
    return value;
  }, [isArray]);

  return (
    <Field type={type} name={name}>
      {(fieldProps: FieldProps) => {
        const touched = fieldProps.form.touched[fieldProps.field.name];
        const errors = fieldProps.form.errors[fieldProps.field.name];
        const displayValue = formatValueForDisplay(fieldProps.field.value);

        return (
          <>
            <DefaultTextField
              id={fieldProps.field.name}
              {...fieldProps.field}
              {...otherProps}
              value={displayValue}
              onChange={e => {
                handleOnChange(e, fieldProps)
              }}
              type={type}
              fullWidth={true}
              disabled={disabled ? disabled : false}
              error={Boolean(errors)}
            />
            <FormHelperText error={Boolean(errors)}>
              {isEmpty(errors) && !touched ? null : Array.isArray(errors) ? (
                errors.map((error, index) => {
                  if (error.keyword !== "required") {
                    return <p key={index}>{buildErrorMessage(name)(error)}</p>;
                  }
                })
              ) : (
                <span>{`${errors}`}</span>
              )}
            </FormHelperText>
          </>
        );
      }}
    </Field>
  );
};

export default CustomTextWidget;
