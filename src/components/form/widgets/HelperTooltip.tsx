import {
  createStyles,
  SvgIcon,
  Theme,
  Tooltip,
  WithStyles,
  withStyles
} from "@material-ui/core";
import classNames from "classnames";
import React, { useState } from "react";

function arrowGenerator(color: string) {
  return {
    '&[x-placement*="bottom"] $arrow': {
      top: 0,
      left: 0,
      marginTop: "-0.95em",
      width: "3em",
      height: "1em",
      "&::before": {
        borderWidth: "0 1em 1em 1em",
        borderColor: `transparent transparent ${color} transparent`
      }
    },
    '&[x-placement*="top"] $arrow': {
      bottom: 0,
      left: 0,
      marginBottom: "-0.95em",
      width: "3em",
      height: "1em",
      "&::before": {
        borderWidth: "1em 1em 0 1em",
        borderColor: `${color} transparent transparent transparent`
      }
    },
    '&[x-placement*="right"] $arrow': {
      left: 0,
      marginLeft: "-0.95em",
      height: "3em",
      width: "1em",
      "&::before": {
        borderWidth: "1em 1em 1em 0",
        borderColor: `transparent ${color} transparent transparent`
      }
    },
    '&[x-placement*="left"] $arrow': {
      right: 0,
      marginRight: "-0.95em",
      height: "3em",
      width: "1em",
      "&::before": {
        borderWidth: "1em 0 1em 1em",
        borderColor: `transparent transparent transparent ${color}`
      }
    }
  };
}

const toolTipStyles = createStyles((theme: Theme) => ({
  questionMark: {
    marginLeft: "10px",
    verticalAlign: "middle"
  },
  arrowPopper: arrowGenerator("#dadde9"),
  arrow: {
    position: "absolute",
    fontSize: 6,
    width: "3em",
    height: "3em",
    "&::before": {
      content: '""',
      margin: "auto",
      display: "block",
      width: 0,
      height: 0,
      borderStyle: "solid"
    }
  },
  tooltipBox: {
    backgroundColor: "#dadde9",
    color: "rgba(0, 0, 0, 0.87)",
    maxWidth: 320,
    fontSize: theme.typography.pxToRem(16),
    border: "1px solid #dadde9",
    "& b": {
      fontWeight: theme.typography.fontWeightMedium
    }
  }
}));

export interface ITooltipProps extends WithStyles {
  text: string;
  hasInvertedColours?: boolean;
}

const HelperTooltip: React.FunctionComponent<ITooltipProps> = ({
  text,
  classes,
  hasInvertedColours
}) => {
  const [arrowRef, setArrowRef] = useState<any>(null);

  return (
    <Tooltip
      placement="top-start"
      title={
        <React.Fragment>
          <span style={{ whiteSpace: 'pre-line', fontWeight: 'normal' }}>{text}</span>
          <span className={classes.arrow} ref={setArrowRef} />
        </React.Fragment>
      }
      classes={{
        popper: classes.arrowPopper,
        tooltip: classes.tooltipBox
      }}
      PopperProps={{
        popperOptions: {
          modifiers: {
            arrow: {
              enabled: Boolean(arrowRef),
              element: arrowRef
            }
          }
        }
      }}
    >
      <SvgIcon className={classes.questionMark}>
        <path fill="none" d="M0 0h24v24H0z" />
        <path
          fill={hasInvertedColours ? "white" : "#3A84B2"}
          d="M11 18h2v-2h-2v2zm1-16C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-2.21 0-4 1.79-4 4h2c0-1.1.9-2 2-2s2 .9 2 2c0 2-3 1.75-3 5h2c0-2.25 3-2.5 3-5 0-2.21-1.79-4-4-4z"
        />
      </SvgIcon>
    </Tooltip>
  );
};

export default withStyles(toolTipStyles)(HelperTooltip);