import {
    FormControl,
    FormControlLabel,
    Grid,
    Radio,
    RadioGroup,
    Theme,
    Typography,
    withStyles,
    WithStyles
} from "@material-ui/core";
import {FormikProps} from "formik";
import React, {ReactNode, useEffect, useState} from "react";
import withAuthorization from "../../../features/auth/components/Authorization";
import {Audience, AudienceType, IOfferFormModel, OfferStatus, ProgramType} from "../../../validation/validator";
import {FIELDS_CONFIG} from "../configs/fields.config";
import HelperTooltip from "./HelperTooltip";
import {connect} from "react-redux";
import {IRootState, withAuthStateToProps} from "../../../store";
import {FEATURE_FLAG, featureFlagCompare} from "../../../features/offer-submission/components/FeatureFlag";

interface IOfferAudienceProps extends WithStyles {
    formikProps: FormikProps<IOfferFormModel>;
}

enum AudienceRadioGroup {
    Mass = "mass",
    Targeted = "targeted",
    Event = "event"
}

interface IAudienceRadioButtons {
    label: string;
    value: AudienceRadioGroup;
}

const BASE_LEVEL_RADIO_BUTTONS: Array<IAudienceRadioButtons> = [
    {
        label: "Mass (Offer is available to all Collectors in a Partner's given region, or the entire Program)",
        value: AudienceRadioGroup.Mass
    },
    {
        label: "Targeted (Offer is available to a subset or group of Collectors selected using various criteria that makes them relevant to receive this specific offer)",
        value: AudienceRadioGroup.Targeted
    },
    {
        label: "Event-based targeted",
        value: AudienceRadioGroup.Event
    }
];

const styles = (theme: Theme) => ({
    group: {
        justifyContent: "space-between"
    },
    radioButtonRoot: {},
    checked: {}
});

const MAGENTA = '#D30E8B';
const GREY = '#5e5e5e';

const CustomAudienceWidget: React.FunctionComponent<IOfferAudienceProps> = ({classes, formikProps}) => {
    const [selectedAudienceOption, setSelectedAudienceOption] = useState("");
    const [audienceChecked, setAudienceChecked] = useState(false);

    useEffect(() => {
        if (formikProps.values.id) {
            if (formikProps.values.massOffer && formikProps.values.massOffer === true) {
                setSelectedAudienceOption(Audience.Mass);
            } else {
                if (formikProps.values.eventBasedOffer && formikProps.values.eventBasedOffer === true) {
                    setSelectedAudienceOption(Audience.Event);
                } else {
                    setSelectedAudienceOption(Audience.Targeted);
                }
            }
            setAudienceChecked(true);
        }
    }, [formikProps.values.id]);

    const renderRadioButtons = () => {
        const radioButtons: ReactNode[] = [];
        const radioButtonTitles = BASE_LEVEL_RADIO_BUTTONS;

        const radioButtonFilter = (item: IAudienceRadioButtons) => {
            if(formikProps.values.programType === "amreceipts"){
                return item.value === AudienceRadioGroup.Mass;
            }

            // SEND FALSE when we dont want the radio button
            // SEND TRUE when we want the radio button
            if(item.value === AudienceRadioGroup.Event){
                return featureFlagCompare(FEATURE_FLAG.EVENT_BASED_OFFER, "true")
            } else {
                return true
            }
        }

        radioButtonTitles.filter((item) => radioButtonFilter(item)).forEach(config => {
            radioButtons.push(
                <FormControlLabel
                    key={config.value}
                    value={config.value}
                    control={
                        <Radio
                            color="secondary"
                            classes={{
                                root: classes.radioButtonRoot,
                                checked: classes.checked
                            }}
                            style={{color: audienceChecked ? GREY : MAGENTA}}
                            disabled={formikProps.values.status === OfferStatus.Published.toUpperCase() || formikProps.values.status === OfferStatus.Updated.toUpperCase()}
                        />
                    }
                    label={config.label}
                />
            );
        });
        return radioButtons;
    };

    const onRadioButtonChange = (event: React.ChangeEvent<any>) => {
        formikProps.setFieldValue(`audience`, false);
        formikProps.setFieldValue(`massOffer`, false);

        if (event.target.value === AudienceRadioGroup.Mass) {
            formikProps.setFieldValue(`audience`, Audience.Mass);
            formikProps.setFieldValue(`massOffer`, AudienceType.Mass);
            formikProps.setFieldValue(`eventBasedOffer`, false);
        } else if (event.target.value === AudienceRadioGroup.Targeted) {
            formikProps.setFieldValue(`audience`, Audience.Targeted);
            formikProps.setFieldValue(`massOffer`, AudienceType.Targeted);
            formikProps.setFieldValue(`eventBasedOffer`, false);
        } else if (event.target.value === AudienceRadioGroup.Event) {
            formikProps.setFieldValue(`audience`, Audience.Event);
            formikProps.setFieldValue(`massOffer`, AudienceType.Event);
            formikProps.setFieldValue(`eventBasedOffer`, true);
        }

        setSelectedAudienceOption(event.target.value);
        setAudienceChecked(true);
    };

    return (
        <Grid container={true} direction="column">
            <Grid item={true} container={true} direction="row">
                <Typography
                    variant="h5"
                    component="h5"
                    color={audienceChecked ? 'default' : 'error'}
                    paragraph={true}
                    gutterBottom={true}
                >
                    {audienceChecked ? 'Audience Type' : FIELDS_CONFIG.audience.title}{" "}
                </Typography>
                <HelperTooltip text={FIELDS_CONFIG.audience.toolTip}/>
            </Grid>
            <Grid item={true} container={true}>
                <FormControl fullWidth={false}>
                    <RadioGroup
                        name="AudienceRadioGroup"
                        classes={{root: classes.group}}
                        row={true}
                        value={selectedAudienceOption}
                        onChange={onRadioButtonChange}
                        style={{color: audienceChecked ? GREY : MAGENTA, flexDirection: "column"}}
                    >
                        {renderRadioButtons()}
                    </RadioGroup>
                </FormControl>
            </Grid>

        </Grid>
    );
}

const mapStateToProps = (state: IRootState) => ({
    ...withAuthStateToProps(state)
});

const styledCustomAudienceWidget = withStyles(styles)(CustomAudienceWidget);

export default connect(mapStateToProps)(
    withStyles(styles)(
        withAuthorization(styledCustomAudienceWidget, ["CREATE_OFFER"])
    )
);
