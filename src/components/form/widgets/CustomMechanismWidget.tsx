import {
  FormControl,
  FormControlLabel,
  Grid,
  Radio,
  RadioGroup,
  Theme,
  Typography,
  withStyles,
  WithStyles
} from "@material-ui/core";
import {FormikProps} from "formik";
import {get} from "lodash";
import React, { ReactNode, useEffect, useState, useRef } from "react";
import CustomLocalizationTextField from "../../../components/form/fields/CustomLocalizationTextField";
import CustomTextWidget from "../../../components/form/widgets/CustomTextWidget";
import OfferSubmissionFormPanel from "../../../features/offer-submission/components/OfferSubmissionFormPanel";
import withAuthorization from "../../../features/auth/components/Authorization";
import {
  IMechanismObject,
  IOfferFormModel,
  MechanismType
} from "../../../validation/validator";
import {FIELDS_CONFIG} from "../configs/fields.config";
import {CustomSelectWidget} from "./CustomSelectWidget";
import HelperTooltip from "./HelperTooltip";
import {connect} from "react-redux";
import { withAuthStateToProps, IRootState } from "../../../store";
import {
  FEATURE_FLAG,
  FeatureFlag
} from "../../../features/offer-submission/components/FeatureFlag";
import {IPartner} from "../../../models/Partner";

interface IOfferMechanismProps extends WithStyles {
  formikProps: FormikProps<IOfferFormModel>;
  mechanismIndex: number;
  partners?: IPartner[];
  disabled?: boolean;
}

enum MechanismGroup {
  NoAction = "noAction",
  Barcode = "barcode",
  CashierCode = "cashierCode",
  Button = "button",
  OptIn = "optIn"
}

const FIRST_LEVEL_RADIO_BUTTONS: Array<{
  label: string;
  value: MechanismGroup;
}> = [
  {
    label: "Button",
    value: MechanismGroup.Button
  },
  {
    label: "Barcode",
    value: MechanismGroup.Barcode
  },
  {
    label: "Cashier Code",
    value: MechanismGroup.CashierCode
  },
  {
    label: "Opt In",
    value: MechanismGroup.OptIn
  },
  {
    label: "No Action",
    value: MechanismGroup.NoAction
  }
];

const MULTI_LEVEL_RADIO_BUTTONS: Array<{
  label: string;
  value: MechanismGroup;
}> = [
  {
    label: "Button",
    value: MechanismGroup.Button
  },
  {
    label: "Barcode",
    value: MechanismGroup.Barcode
  },
  {
    label: "Cashier Code",
    value: MechanismGroup.CashierCode
  }
];

const MULTI_LEVEL_CARD_LINKED_RADIO_BUTTONS: Array<{
  label: string;
  value: MechanismGroup;
}> = [
  {
    label: "Opt In",
    value: MechanismGroup.OptIn
  },
  {
    label: "Use your linked card",
    value: MechanismGroup.NoAction
  }
];

const BAR_CODE_OPTIONS = [
  {
    label: "UPC",
    value: MechanismType.BarcodeUPC
  },
  {
    label: "Code 128",
    value: MechanismType.Barcode128
  }
];
const CASHIER_CODE_OPTIONS = [
  {
    label: "PLU",
    value: MechanismType.PLU
  },
  {
    label: "Coupon Code",
    value: MechanismType.CouponCode
  }
];

const BMOPREAPP_RADIO_BUTTONS: Array<{
  label: string;
  value: MechanismGroup;
}> = [
  {
    label: "Opt In",
    value: MechanismGroup.OptIn,
  },
];

const AMRECEIPTS_RADIO_BUTTONS: Array<{
  label: string;
  value: MechanismType;
}> = [
  {
    label: "Scan Receipt",
    value: MechanismType.ScanReceipt,
  },
];

type radioOption =
  | {label: string; value: MechanismGroup}
  | {label: string; value: MechanismType};

const styles = (theme: Theme) => ({
  group: {
    justifyContent: "flex-start"
  },
  radioButtonRoot: {
    color: "grey",
    "&$checked": {
      color: theme.palette.primary.main
    }
  },
  checked: {}
});

const CustomMechanismWidget: React.FunctionComponent<IOfferMechanismProps> = (props) => {
  const [selectedMechanismOption, setSelectedMechanismOption] = useState(MechanismGroup.NoAction);

  const previousMechanismRef = useRef<string | null>(null);

  const { disabled } = props;

  const getMechanismType = (props: any) => {
    const { formikProps, mechanismIndex } = props;
    // If the formikProps.values contain no mechanism type, it might be a barcode or PLU code - it doesn't necessarily mean that it is no action.
    return get(
        formikProps.values,
        `mechanisms[${mechanismIndex}].mechanismType`
    );
  };

  const updateSelectedMechanismOption = () => {
      const formMechanismType = getMechanismType(props) || MechanismType.NoAction;
          if (
              formMechanismType.includes(MechanismType.PLU) ||
              formMechanismType.includes(MechanismType.CouponCode)
          ) {
            setSelectedMechanismOption(MechanismGroup.CashierCode)
          } else if (formMechanismType.includes(MechanismGroup.Barcode)) {
            setSelectedMechanismOption(MechanismGroup.Barcode)
          } else if (formMechanismType.includes(MechanismGroup.Button)) {
            setSelectedMechanismOption(MechanismGroup.Button)
          } else if (formMechanismType.includes(MechanismGroup.OptIn)) {
            setSelectedMechanismOption(MechanismGroup.OptIn)
          } else {
            setSelectedMechanismOption(formMechanismType)
          }
  };

  function isMechanismType(option: radioOption): option is {label: string; value: MechanismType}{
    return Object.values(MechanismType).includes(option.value as MechanismType);
  }

  function isMechanismGroup(option: radioOption): option is {label: string; value: MechanismGroup}{
    return Object.values(MechanismGroup).includes(option.value as MechanismGroup);
  }

  const renderRadioButtons = () => {
    const { classes, mechanismIndex,formikProps } = props;
    const radioButtons: ReactNode[] = [];
    const radioButtonTitles:radioOption[] =
        formikProps.values.programType === "bmopreapp"? BMOPREAPP_RADIO_BUTTONS
          : formikProps.values.programType === "amreceipts"? AMRECEIPTS_RADIO_BUTTONS
          : (formikProps.values.programType === "cardlinked"? MULTI_LEVEL_CARD_LINKED_RADIO_BUTTONS
              : (mechanismIndex >= 1
                  ? MULTI_LEVEL_RADIO_BUTTONS
                  : FIRST_LEVEL_RADIO_BUTTONS));

    const optInEnabled = Boolean(process.env.REACT_APP_OPT_IN_MECHANISM_TYPE);
    const amReceiptsEnabled = Boolean(process.env.REACT_APP_AM_RECEIPTS);

    radioButtonTitles.forEach((config) => {
      if (
          (isMechanismGroup(config) && config.value !== MechanismGroup.OptIn) ||
          (isMechanismGroup(config) && config.value === MechanismGroup.OptIn && optInEnabled) ||
          (isMechanismType(config) && config.value === MechanismType.ScanReceipt && amReceiptsEnabled)
      ) {
        radioButtons.push(
            <FormControlLabel
                key={config.value}
                value={config.value}
                disabled={disabled ? disabled : false}
                control={
                  <Radio
                      color="secondary"
                      classes={{
                        root: classes.radioButtonRoot,
                        checked: classes.checked,
                      }}
                  />
                }
                label={config.label}
            />
        );
      }
    });
    return radioButtons;
  };

  // When the radio button changes, reset the mechanism object in the form to be empty
  const onRadioButtonChange = (event: React.ChangeEvent<any>) => {
    const { formikProps, mechanismIndex } = props;
    // set the current mechanismType
    if (event.target.value === MechanismGroup.NoAction) {
      // If the first mechanism has the value no action, then reset the mechanism array to have only one mechanism as no action
      formikProps.setFieldValue(`mechanisms`, [
        {
          mechanismType: MechanismType.NoAction,
        },
      ]);
    } else if (event.target.value === MechanismGroup.OptIn) {
      formikProps.setFieldValue(`mechanisms`, [
        {
          mechanismType: MechanismType.OptIn,
        },
      ]);
    } else if (event.target.value === MechanismGroup.Button) {
      formikProps.setFieldValue(`mechanisms[${mechanismIndex}]`, {
        mechanismType: MechanismType.Button,
        mechanismLabel: { "en-US": undefined, "fr-CA": undefined },
        mechanismValue: { "en-US": undefined, "fr-CA": undefined },
      });
    } else {
      formikProps.setFieldValue(`mechanisms[${mechanismIndex}]`, {});
    }

    setSelectedMechanismOption(event.target.value)
  };

  const onSelectWidgetChange = (
      event: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const { formikProps, mechanismIndex } = props;
    const newMechanismType: MechanismGroup = event.target
        .value as MechanismGroup;
    const storedMechanismValue = formikProps.values.mechanisms[mechanismIndex]
        .mechanismValue || { "en-US": undefined };

    if (
        newMechanismType !== MechanismGroup.NoAction &&
        newMechanismType !== MechanismGroup.OptIn &&
        newMechanismType !== MechanismGroup.Button
    ) {
      formikProps.setFieldValue(`mechanisms[${mechanismIndex}]`, {
        mechanismType: newMechanismType,
        mechanismValue: storedMechanismValue,
      });
    }
  };

  useEffect(() => {
   updateSelectedMechanismOption();
  }, []);

  useEffect(() => {
    const propMechanism = getMechanismType(props);
    const previousMechanism = previousMechanismRef.current;
    //When program type is selected optIn is assigned by default
    if (
        formikProps.values.programType === "bmopreapp" &&
        propMechanism !== MechanismGroup.OptIn.toString()
        )
    {
            formikProps.setFieldValue("mechanisms", [
                { mechanismType: MechanismGroup.OptIn }
            ]);
            setSelectedMechanismOption(MechanismGroup.OptIn);
    }

    if(formikProps.values.programType === "amreceipts" && propMechanism !== MechanismType.ScanReceipt.toString())
    {
      formikProps.setFieldValue("mechanisms", [
        { mechanismType: MechanismType.ScanReceipt }
      ]);
      setSelectedMechanismOption(MechanismType.ScanReceipt);
    }

    if (previousMechanism && propMechanism && propMechanism !== previousMechanism) {
          updateSelectedMechanismOption();
    }

    previousMechanismRef.current = propMechanism;
  }, [props, getMechanismType, updateSelectedMechanismOption]);

  const { classes, formikProps, mechanismIndex } = props;
  const formData = formikProps.values;

  const listOfBarcodeTypes = [
    MechanismType.Barcode128,
    MechanismType.BarcodeUPC,
  ];

  const generatedMechanism: IMechanismObject = get(
      formData,
      `mechanisms[${mechanismIndex}]`,
      {
        mechanismType: MechanismType.NoAction,
      }
  );

  return (
      <Grid container={true} direction="column">
        <Grid item={true} container={true} direction="row">
          <Typography
              variant="h5"
              component="h5"
              paragraph={true}
              gutterBottom={true}
          >
            {FIELDS_CONFIG.mechanisms.title}{" "}
            {mechanismIndex >= 1 ? mechanismIndex + 1 : ""}
          </Typography>
          <HelperTooltip text={FIELDS_CONFIG.mechanisms.toolTip} />
        </Grid>
        <Grid item={true} container={true}>
          <FormControl fullWidth={true}>
            <RadioGroup
                name="mechanismGroups"
                classes={{ root: classes.group }}
                row={true}
                value={selectedMechanismOption}
                onChange={onRadioButtonChange}
            >
              {renderRadioButtons()}
            </RadioGroup>
          </FormControl>
        </Grid>
        {(selectedMechanismOption === MechanismGroup.Barcode ||
            selectedMechanismOption === MechanismGroup.CashierCode) && (
            <Grid item={true}>
              <CustomSelectWidget
                  type={`mechanisms[${mechanismIndex}].mechanismType`}
                  name={`mechanisms[${mechanismIndex}].mechanismType`}
                  onChange={onSelectWidgetChange}
                  title={
                    selectedMechanismOption === MechanismGroup.Barcode
                        ? "Barcode Type"
                        : "Cashier Code Type"
                  }
                  options={
                    selectedMechanismOption === MechanismGroup.Barcode
                        ? BAR_CODE_OPTIONS
                        : CASHIER_CODE_OPTIONS
                  }
              />
            </Grid>
        )}
        {selectedMechanismOption === MechanismGroup.Button && (
            <Grid item={true}>
              <OfferSubmissionFormPanel title="Button Text Translations">
                <CustomLocalizationTextField
                    name={`mechanisms[${mechanismIndex}].mechanismLabel`}
                    label="Button Text"
                />
              </OfferSubmissionFormPanel>
            </Grid>
        )}
        {(selectedMechanismOption === MechanismGroup.Button ||
            (selectedMechanismOption === MechanismGroup.CashierCode &&
                generatedMechanism.mechanismType === MechanismType.CouponCode)) && (
            <Grid item={true}>
              <OfferSubmissionFormPanel
                  title={
                    selectedMechanismOption === MechanismGroup.Button
                        ? "Button URL Translations"
                        : "Coupon Code Translations"
                  }
                  tooltipText={
                    selectedMechanismOption === MechanismGroup.Button
                        ? "Website URL - must start with https:// or http:// e.g. http://www.airmiles.ca"
                        : "The coupon code value that will be displayed"
                  }
              >
                <CustomLocalizationTextField
                    name={`mechanisms[${mechanismIndex}].mechanismValue`}
                    label={
                      selectedMechanismOption === MechanismGroup.Button
                          ? "Button URL"
                          : "Coupon Code"
                    }
                />
              </OfferSubmissionFormPanel>
            </Grid>
        )}
        {((selectedMechanismOption === MechanismGroup.Barcode &&
                listOfBarcodeTypes.includes(generatedMechanism.mechanismType)) ||
            (selectedMechanismOption === MechanismGroup.CashierCode &&
                generatedMechanism.mechanismType === MechanismType.PLU)) && (
            <>
              <FeatureFlag featureName={FEATURE_FLAG.MECHANISM_TITLE_TEXT}>
                <OfferSubmissionFormPanel title={"Mechanism Title Translations"}>
                  <CustomLocalizationTextField
                      name={`mechanisms[${mechanismIndex}].mechanismTitle`}
                      label="Mechanism Title"
                  />
                </OfferSubmissionFormPanel>{" "}
              </FeatureFlag>
              <Grid item={true}>
                <CustomTextWidget
                    name={`mechanisms[${mechanismIndex}].mechanismValue.en-US`}
                    label={
                      selectedMechanismOption === MechanismGroup.Barcode
                          ? "Barcode Value"
                          : "PLU Code"
                    }
                />
              </Grid>
              <FeatureFlag featureName={FEATURE_FLAG.MECHANISM_TITLE_TEXT}>
                <OfferSubmissionFormPanel title={"Mechanism Text Translations"}>
                  <CustomLocalizationTextField
                      name={`mechanisms[${mechanismIndex}].mechanismText`}
                      label="Mechanism Text"
                  />
                </OfferSubmissionFormPanel>
              </FeatureFlag>
            </>
        )}
      </Grid>
  );
}

const mapStateToProps = (state: IRootState) => ({
  ...withAuthStateToProps(state)
});

const styledCustomMechanismWidget = withStyles(styles)(CustomMechanismWidget);

export default connect(mapStateToProps)(
  withStyles(styles)(
    withAuthorization(styledCustomMechanismWidget, ["CREATE_OFFER"])
  )
);
