# offer-submission-web

> **React + Redux application for submitting and managing promotional offers in the AMRP ecosystem.**

This project serves as the frontend for managing and submitting marketing offers. It integrates with authentication, offer APIs, and user management services. It is built using [React 18](https://reactjs.org/), [Redux](https://redux.js.org/), and [Material-UI](https://mui.com/) and follows modern DevOps practices with Jenkins pipelines and AWS deployment.

---

##  Tech Stack

- **Frontend**: React 18, Redux, React Router, Material-UI
- **State Management**: Redux, Redux Thunk
- **Styling**: SASS, Material-UI Theme
- **Build Tool**: Create React App
- **Authentication**: Ping Identity via SSO
- **CI/CD**: Jenkins, Docker, AWS ECS, S3, CloudFormation
- **Testing**: React Testing Library, Jest
- **PWA**: Service worker and offline support

---

## Local Development

### 1. Prerequisites

- Node.js 18.x
- Yarn
- [NVM](https://github.com/nvm-sh/nvm) for node version management

### 2. Start Locally

Minimal steps to start the app:

```bash
yarn install
yarn build-css
yarn start
```

This will:
- Install dependencies
- Compile SCSS stylesheets
- Start the React development server with `REACT_APP_ENV=local`

App runs at: [http://localhost:3000](http://localhost:3000)

For full setup (with backend APIs), you can run:

```bash
./localstart.sh
```

This script:
- Starts `offer-submission-api` and `post-security-manager` locally
- Waits for 30s
- Then launches the web app

---

### 🔐 Authentication Proxy (Dev Only)

Local development requires authenticated API requests. This is handled by a companion project:

**[offer-submission-web-tool](https://github.com/your-org/offer-submission-web-tool)**  
Runs on: `http://localhost:8181`

#### Usage:
1. Clone and start the tool:
   ```bash
   yarn start
   ```

2. Go to your dev portal (e.g., `https://dev-postui.toolbox.loyalty.com`)  
   Open DevTools → Network tab → Copy the `Request Headers` headers. (from Accept to the end)

3. Use Postman or curl to send a request to:

   ```
   POST http://localhost:8181/auth
   ```

   Include your auth request headers (e.g., `Authorization`, `Cookie`, etc).

Once authenticated, all requests from `offer-submission-web` will be proxied with your token.

---

### 3. Environment Configuration

Available in `.env.*` and `public/config/permission.json`. Key environment variables:

```env
REACT_APP_OFFER_SUBMISSION_API=/api
REACT_APP_PING_LOGIN_URL=https://fsps.loyalty.com/idp/startSSO.ping?...
REACT_APP_CASHIER_INSTRUCTIONS=true
...
```

---

## Build

```bash
# Build for local
yarn build

# Build for specific environment
yarn build:dev
yarn build:int
yarn build:uat
yarn build:prod
```

---

## Testing

```bash
# Local tests
yarn test

# CI test mode
yarn test-ci
```

Testing framework: Jest + React Testing Library  
Custom test setup in: `setupTests.js`

---

## Docker

```Dockerfile
FROM node:18 AS development
WORKDIR /app
COPY package.json yarn.lock ./
RUN yarn install
COPY . .
RUN yarn build-css
EXPOSE 3000
CMD ["yarn", "start"]
```

---

## CI/CD

CI/CD is managed by Jenkins pipelines:

- **`Jenkinsfile-ci`** – CI + version tagging + snapshot release
- **`Jenkinsfile-cd`** – Deployment to dev → INT → UAT
- **`Jenkinsfile-ci-hotfix`** – Fast hotfix pipeline
- **Artifacts**: built and pushed to S3
- **Deployment**: ECS via CloudFormation (see `cfn/`)

Environment tags defined in:
```json
"cfn/dev.tags.json"
```

---

## Useful Scripts

```bash
yarn build-css          # Compile all .scss files
yarn analyze            # Analyze bundle size
yarn tslint-check       # Validate TSLint + Prettier compatibility
```

---

##  Permissions

Permissions and features are toggled via `.env.*` and `public/config/permission.json`. Features include:

- Conditional UI rendering based on user roles
- Toggle issuer dropdowns, offer template types, mechanism types, etc.

---

## References

- [React Docs](https://reactjs.org/)
- [Redux Docs](https://redux.js.org/)
- [Material-UI Docs](https://mui.com/)
- [Create React App](https://create-react-app.dev/)
- [Jenkins Shared Lib](https://github.com/AirMilesLoyaltyInc/jenkins-shared-lib-v2)
