/**
 * Loops through each sample json files in ./src/validation/samples and generates respective csv files
 * This can be used for PM-253 https://jira.loyalty.com/browse/PM-253
 */
const dir = "../src/validation/samples";

const fs = require("fs");
const path = require("path");
const fg = require("fast-glob");
const Papa = require("papaparse");
const _ = require("lodash");

function assignValue(key, value, fields, data) {
  if (value instanceof Object) {
    if (Array.isArray(value)) {
      for (let i = 0; i < value.length; i++) {
        const v = value[i];
        if (v instanceof Object) {
          console.trace(`Parsing ${key} as a Collection`, key, value);
          return assignValue(`${key}.[${i}]`, v, fields, data);
        } else {
          console.trace(`Parsing ${key} as an Array of values`, key, value);
          fields.push(key);
          data.push(value.join("|"));
          return;
        }
      }
    } else if (value instanceof Object) {
      console.trace(`Parsing ${key} as an Object`, key, value);

      const pairs = _.toPairs(value);
      for (let j = 0; j < pairs.length; j++) {
        const [innerKey, innerValue] = pairs[j];
        assignValue(`${key}.${innerKey}`, innerValue, fields, data);
      }
    } else {
      console.trace(`Parsing ${key} as a value`, key, value);

      fields.push(key);
      data.push(value);
      return;
    }
  } else {
    console.trace(`Parsing ${key} as a value`, key, value);
    fields.push(key);
    data.push(value);
  }
}

function generateParseObject(sampleObject) {
  const pairs = _.toPairs(sampleObject);
  const fields = [],
    data = [];
  for (let [key, value] of pairs) {
    assignValue(key, value, fields, data);
  }
  return {
    fields,
    data
  };
}

function generateCSVfromJSON(entry) {
  console.trace(`Generating file for `, entry);
  const extension = path.extname(entry);
  const file = path.basename(entry, extension);
  const sampleArray = generateParseObject(require(entry));
  const unparseJSON = Papa.unparse(sampleArray);
  fs.writeFile(path.join(__dirname, dir, file + ".csv"), unparseJSON, err => {
    if (err) {
      console.error(err);
    }
  });
}

function main() {
  console.info(`Parsing files at ${dir}`);
  console.time("Generated in");
  const entries = fg.sync([path.join(__dirname, dir, "*.json")]);
  entries.forEach(generateCSVfromJSON);
  console.timeEnd("Generated in");
  console.info(`Parsed ${entries.length} files`);
}

main();
