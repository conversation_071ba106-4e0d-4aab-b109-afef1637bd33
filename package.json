{"name": "offer-submission-web", "version": "0.1.526-SNAPSHOT", "private": true, "engines": {"node": "18.x"}, "dependencies": {"@babel/core": "^7.0.0-0", "@babel/plugin-proposal-private-property-in-object": "7.14.5", "@babel/plugin-syntax-flow": "^7.14.5", "@babel/plugin-transform-react-jsx": "^7.14.9", "@date-io/moment": "^1.3.13", "@material-ui/core": "4.1.0", "@material-ui/icons": "^3.0.2", "@reach/component-component": "^0.1.3", "@uppy/core": "^1.2.0", "@uppy/dashboard": "^1.2.0", "@uppy/drag-drop": "^1.2.0", "@uppy/file-input": "^1.2.0", "@uppy/progress-bar": "^1.2.0", "@uppy/react": "^1.2.0", "ajv": "^6.9.1", "browser-cookies": "^1.2.0", "classnames": "^2.2.6", "clipboard-copy": "^3.0.0", "connected-react-router": "^6.2.1", "cropperjs": "^1.4.3", "d3": "^5.9.2", "downshift": "^3.4.8", "file-type": "^10.7.1", "formik": "^1.5.8", "history": "^4.7.2", "html2canvas": "^1.0.0-alpha.12", "jsbarcode": "^3.11.0", "json-schema": "^0.4.0", "jspdf": "^2.3.1", "jss-plugin-camel-case": "^10.10.0", "jszip": "^3.1.5", "jwt-decode": "^2.2.0", "lodash": "^4.17.13", "material-ui-pickers": "^2.2.4", "module-alias": "^2.2.3", "moment": "^2.29.4", "moment-timezone": "^0.5.46", "papaparse": "^4.6.3", "path": "^0.12.7", "prop-types": "^15.6.2", "rambda": "^2.3.0", "react": "^18.0.0", "react-dates": "^20.1.0", "react-dom": "^18.3.0", "react-redux": "^8.0.4", "react-router": "^5.0.0", "react-router-dom": "^5.0.0", "react-slick": "^0.23.2", "react-with-direction": "^1.3.0", "redux": "^4.2.1", "redux-thunk": "^2.4.1", "reselect": "^4.0.0", "sass": "^1.80.4", "sass-loader": "^13.1.0", "typesafe-actions": "^3.0.0", "utility-types": "^3.2.1", "uuid": "^11.0.3"}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "16.2.0", "@types/classnames": "^2.2.6", "@types/d3": "^5.7.1", "@types/file-type": "^10.6.0", "@types/html2canvas": "^0.0.35", "@types/jest": "^23.3.12", "@types/json-schema": "^7.0.3", "@types/jspdf": "^1.2.2", "@types/jszip": "^3.1.5", "@types/lodash": "4.14.118", "@types/moment-timezone": "^0.5.12", "@types/node": "16.18.122", "@types/papaparse": "^4.5.7", "@types/react": "18.3.18", "@types/react-datepicker": "^2.9.4", "@types/react-dates": "^17.1.5", "@types/react-dom": "18.3.5", "@types/react-redux": "^7.1.34", "@types/react-router": "^5.1.20", "@types/react-router-dom": "^5.3.3", "@types/redux-mock-store": "^1.0.0", "@types/uuid": "^3.4.10", "abab": "^2.0.6", "cross-env": "^5.2.0", "dotenv": "6.1.0", "dotenv-expand": "4.2.0", "env-cmd": "^9.0.1", "fast-glob": "^2.2.6", "husky": "^1.3.1", "lint-staged": "^8.1.0", "react-scripts": "5.0.1", "redux-mock-store": "^1.5.3", "releaseme": "^0.1.14", "source-map-explorer": "^1.6.0", "tslint": "^5.11.0", "tslint-config-prettier": "^1.15.0", "tslint-react": "^3.6.0", "typescript": "3.2.2"}, "resolutions": {"@types/file-type/file-type": "17.1.6", "autoprefixer": "10.4.5"}, "scripts": {"test:debug": "react-scripts --inspect-brk test --runInBand --no-cache", "analyze": "source-map-explorer build/static/js/main.*", "start": "cross-env REACT_APP_ENV=local react-scripts start --silent", "test": "cross-env REACT_APP_ENV=local react-scripts test", "test-ci": "cross-env CI=true react-scripts test --env=jsdom --silent", "eject": "react-scripts eject", "tslint-check": "tslint-config-prettier-check ./tslint.json", "build": "sh -ac '. ./.env.${REACT_APP_ENV}; react-scripts build'", "build-css": "sass --quiet src:dist/styles", "build:dev": "cross-env REACT_APP_ENV=development yarn run build", "build:int": "cross-env REACT_APP_ENV=int env-cmd -f .env.int react-scripts build", "build:uat": "cross-env REACT_APP_ENV=uat env-cmd -f .env.uat react-scripts build", "build:prod": "cross-env REACT_APP_ENV=production yarn run build"}, "husky": {"hooks": {"pre-push": "cross-env REACT_APP_ENV=local yarn test-ci && yarn tslint-check && lint-staged"}}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --write", "git add"]}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"], "releaseme": {"steps": ["setReleaseVersion", "commitReleaseVersion", "tagRelease", "setNextVersion", "commitNextVersion", "pushChanges"]}}