import groovy.json.JsonOutput
/**
 * Hosts the common utility functions for j<PERSON>kins pipelines
 */

/**
 * Get the CFN stack name
 * @param  env [environment name]
 * @return     [CFN stack name wrt environment name]
 */
String getCFNStackName(String env) {
  return "${env}-change-me"
}

/**
 * get the name of the repository
 * @return [Name of the repository]
 */
String getRepoName() {
    return scm.getUserRemoteConfigs()[0].getUrl().tokenize('/').last().split("\\.")[0]
}

/**
 * Deploy application in ECS
 * @param env     [env name]
 * @param version [version number]
 */
void ecsDeploy(String env, String version) {
  def stackName = getCFNStackName(env)
  ansiColor('xterm') {
    sh """
      #!/bin/bash
      # configure system-wide environment variables and aliases needed for nvm and npm
      source /etc/profile
      export AWS_DEFAULT_REGION=us-east-1
      ecs-service deploy ${stackName} ${version} \
        cfn/templates/service.json cfn/${env}.params.json \
        -e cfn/${env}.env \
        -t cfn/${env}.tags.json \
        -r us-east-1
    """
  }
}

/**
 * Read json file and return as String required by aws cli
 * @param  filename          [Name of the file]
 * @param  ['ParameterKey'   [Key ]
 * @param  'ParameterValue'] [Value]
 * @return                   [String of key values pairs]
 */
def paramsFromFile(String filename, keyPair = ['ParameterKey', 'ParameterValue']) {
  assert keyPair.size() == 2

  def paramsJson = readJSON(file: filename)

  paramsJson.collect { item ->
    keyPair.collect { key ->
      item.get(key)
    }.join('=')
  }.join(' ')

}

void deployResourceStack(String env) {
  echo "Deploying Resource Stack ${env}"
  def stackName = "${getCFNStackName(env)}-resources"
  def parameterOverrides = paramsFromFile("cfn/resourcesCFN.${env}.params.json")
  def args = "deploy --stack-name ${stackName} --region us-east-1 --template-file cfn/templates/resourcesCFN.yaml --parameter-overrides ${parameterOverrides} --capabilities CAPABILITY_IAM"
  sh "./jenkins/aws-cloudformation ${args}"
}

void BuildAndDeployEnv(String envName) {
  env.DEPLOY_ENV = envName
  sh '''
    # configure system-wide environment variables and aliases needed for nvm and npm
    source /etc/profile
    nvm install 18.20 >/dev/null
    nvm use 18.20 >/dev/null
    node --version
    npm install -g yarn
    yarn install
    yarn run build:${DEPLOY_ENV}
    aws s3 cp build/ s3://${DEPLOY_ENV}-postui.toolbox.loyalty.com/ --recursive
  '''
}

return this
