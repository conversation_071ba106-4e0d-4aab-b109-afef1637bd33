@Library('jenkins-shared-lib-v2') _
import groovy.json.JsonOutput


def blue= "#42b3f4"
def good="#3dd62f"
def danger="#f45641"
def warning="#ffd344"


config = null
jenkinsUtils = null

/**
 * Checks if the current commit is from jenkins
 * @return true when the current commit is from jenkins
 */
Boolean isJenkinsCommit() {
  def commitEmail = sh(script: "git log -1 --pretty=format:'%ae'", returnStdout: true)?.trim()
  return (commitEmail == "${env.GIT_COMMITTER_EMAIL}")
}

/**
 * Check if the current branch is master
 * @return true when the current branch is master
 */
Boolean isMaster() {
  def branchFullName = "${env.GIT_BRANCH}"
  def branchList = branchFullName.tokenize('/')
  def branchName = branchList.get(branchList.size()-1)
  return branchName == 'master'
}

/**
 * Authenticate session for pushing into ECR
 */
void authECR() {
  println "Authenticate to push docker image"
  ansiColor('xterm') {
    sh '''
      #!/bin/bash
      # configure system-wide environment variables and aliases needed for nvm and npm
      source /etc/profile
      export AWS_DEFAULT_REGION=us-east-1
      # Use the cross account role to access ECR
      eval $(assume-role --role-arn="$ECR_ROLE")
      # Login to private container repository
      eval $(aws ecr get-login)
    '''
  }
}

pipeline {
  // agent {
  //   node {
  //     label 'ubuntu-22'
  //   }
  // }
  agent {
        docker {
            image '************.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22'
        }
    }
  stages {
    stage("Repository Information") {
      steps {
        println "Repository Information"
        script {
          config = {}
          jenkinsUtils = load "jenkins/JenkinsUtils.groovy"
          env.SKIP_SNAPSHOT_RELEASE = (!isJenkinsCommit() && isMaster() && true)
          def props = readJSON file: 'package.json'
          config.versionSnapshot = props.version
          def (versionNumber, snapshot) = config.versionSnapshot.tokenize('-')
          config.version = versionNumber
          env.VERSION_NUMBER = versionNumber
          config.repoName = jenkinsUtils.getRepoName()
        }
      }
    }
    stage("Build") {
      steps {
        ansiColor('xterm') {
          sh '''
            #!/bin/bash
            # configure system-wide environment variables and aliases needed for nvm and npm
            source /etc/profile
            nvm install 18.20 >/dev/null
            nvm use 18.20 >/dev/null
            node --version
            npm install -g yarn
            yarn install
            yarn run build:dev
          '''
        }
      }
      post {
        always {
          script {
              gitUtils.reportPRStatus()
          }
        }
        success {
          echo 'Build Successful on dev.'
        }
        failure {
          echo "failed to build on dev."
        }
        aborted {
          echo "job aborted. Did not build on dev."
        }
      }
    }
    stage("Test") {
      steps {
        ansiColor('xterm') {
          sh '''
            #!/bin/bash
            # configure system-wide environment variables and aliases needed for nvm and npm
            source /etc/profile
            nvm use 18.20 >/dev/null
            REACT_APP_ENV=local yarn test-ci
          '''
        }
      }
    }
    stage("Checkmarx Scan") {
      when { environment name: 'SKIP_SNAPSHOT_RELEASE', value: 'true' }
      steps {
        step([$class: 'CxScanBuilder', comment: '', credentialsId: '', excludeFolders: '', excludeOpenSourceFolders: '',
            exclusionsSetting: 'job', failBuildOnNewResults: "${!isMaster()}",
            filterPattern: '''!**/_cvs/**/*, !**/.svn/**/*,   !**/.hg/**/*,   !**/.git/**/*,  !**/.bzr/**/*, !**/bin/**/*, !**/node_modules/**/*,
            !**/build/**/*, !**/target/**/*, !**/.gradle/**/*, !**/obj/**/*,  !**/backup/**/*, !**/.idea/**/*, !**/*.DS_Store, !**/*.ipr, !**/*.iws,
            !**/*.bak,     !**/*.tmp,       !**/*.aac,      !**/*.aif,      !**/*.iff,     !**/*.m3u, !**/*.mid, !**/*.mp3,
            !**/*.mpa,     !**/*.ra,        !**/*.wav,      !**/*.wma,      !**/*.3g2,     !**/*.3gp, !**/*.asf, !**/*.asx,
            !**/*.avi,     !**/*.flv,       !**/*.mov,      !**/*.mp4,      !**/*.mpg,     !**/*.rm,  !**/*.swf, !**/*.vob,
            !**/*.wmv,     !**/*.bmp,       !**/*.gif,      !**/*.jpg,      !**/*.png,     !**/*.psd, !**/*.tif, !**/*.swf,
            !**/*.jar,     !**/*.zip,       !**/*.rar,      !**/*.exe,      !**/*.dll,     !**/*.pdb, !**/*.7z,  !**/*.gz,
            !**/*.tar.gz,  !**/*.tar,       !**/*.gz,       !**/*.ahtm,     !**/*.ahtml,   !**/*.fhtml, !**/*.hdm,
            !**/*.hdml,    !**/*.hsql,      !**/*.ht,       !**/*.hta,      !**/*.htc,     !**/*.htd, !**/*.war, !**/*.ear,
            !**/*.htmls,   !**/*.ihtml,     !**/*.mht,      !**/*.mhtm,     !**/*.mhtml,   !**/*.ssi, !**/*.stm,
            !**/*.stml,    !**/*.ttml,      !**/*.txn,      !**/*.xhtm,     !**/*.xhtml,   !**/*.class, !**/*.iml,
            !Checkmarx/Reports/*.*''', fullScanCycle: 1, fullScansScheduled: true, groupId: '19431a46-4d94-4130-9a78-b5e2833244c6',
            includeOpenSourceFolders: '', incremental: false, osaArchiveIncludePatterns: '*.zip, *.war, *.ear, *.tgz',
            osaInstallBeforeScan: false, password: '{AQAAABAAAAAQZhCBOvS+ym3pI038ChU4e73PzZVrLLHSeP9Ej1IH8JU=}',
            preset: '36', projectName: "${config.repoName}", sastEnabled: true,
            serverUrl: 'https://checkmarx.loyalty.com', sourceEncoding: '1', username: ''])
        }
      }
    stage("Snapshot Release") {
      when { environment name: 'SKIP_SNAPSHOT_RELEASE', value: 'true' }
      steps {
        println "Snapshot Release"
        git (
          url: "**************:AirMilesLoyaltyInc/offer-submission-web.git",
          branch: "master",
          credentialsId: "jenkins-ssh-key"
        )
        sshagent (credentials: ['jenkins-ssh-key'], ignoreMissing: false) {
      		ansiColor('xterm') {
            sh '''
              mkdir -p ~/.ssh
              ssh-keyscan -t rsa github.com >> ~/.ssh/known_hosts
              git config user.name "AMNext-Jenkins"
              git config user.email "<EMAIL>"

              #!/bin/bash
              # configure system-wide environment variables and aliases needed for nvm and npm
              source /etc/profile
              nvm use 18.20 >/dev/null
              npm install -g releaseme
              releaseme
            '''
          }
      	}
      }
      post {
        success {
          echo 'success! Lets start up the deployment job.'
          script {
            build job: 'Deployment/offer-submission-web', parameters: [[$class: 'StringParameterValue', name: 'BUILD_VERSION', value: "${config.version}"]], wait: false
          }
        }
        failure {
          echo "failed to release"
        }
        aborted {
          echo "job aborted. Did not release"
        }
      }
    }
  }
}
