@Library('jenkins-shared-lib-v2') _
import groovy.json.JsonOutput


def blue= "#42b3f4"
def good="#3dd62f"
def danger="#f45641"
def warning="#ffd344"

def currentDateTime = new Date().format('yyyyMMddHHmmss')
def hotfixVersion = "hf-"+ currentDateTime

// config = null
jenkinsUtils = null


pipeline {
  agent {
        docker {
            image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22'
        }
    }
  stages {
    stage("Repository Information") {
      steps {
        println "Repository Information"
        script {
          // config = {}
          jenkinsUtils = load "jenkins/JenkinsUtils.groovy"
          // def props = readJSON file: 'package.json'
          // config.versionSnapshot = props.version
          // def (versionNumber, snapshot) = config.versionSnapshot.tokenize('-')
          // config.version = versionNumber
          // env.VERSION_NUMBER = versionNumber
          // config.repoName = jenkinsUtils.getRepoName()
        }
      }
    }
    stage("Build") {
      steps {
        ansiColor('xterm') {
          sh '''
            #!/bin/bash
            # configure system-wide environment variables and aliases needed for nvm and npm
            source /etc/profile
            nvm install 18.20 >/dev/null
            nvm use 18.20 >/dev/null
            node --version
            npm install -g yarn
            yarn install
            yarn run build:dev
          '''
        }
      }
      post {
        always {
          script {
              gitUtils.reportPRStatus()
          }
        }
        success {
          echo 'Build Successful on dev.'
        }
        failure {
          echo "failed to build on dev."
        }
        aborted {
          echo "job aborted. Did not build on dev."
        }
      }
    }
    stage("Test") {
      steps {
        ansiColor('xterm') {
          sh '''
            #!/bin/bash
            # configure system-wide environment variables and aliases needed for nvm and npm
            source /etc/profile
            nvm use 18.20 >/dev/null
            REACT_APP_ENV=local yarn test-ci
          '''
        }
      }
    }
    stage("Hotfix Release") {
      steps {
        println "Hotfix Release"
        // git (
        //   url: "**************:AirMilesLoyaltyInc/offer-submission-web.git",
        //   branch: "${params.BRANCH_TO_BUILD}",
        //   credentialsId: "jenkins-ssh-key"
        // )
        script {
          gitUtils.pushTag(hotfixVersion)
        }
      }
      post {
        success {
          echo 'success! Lets start up the deployment job.'
        }
        failure {
          echo "failed to release"
        }
        aborted {
          echo "job aborted. Did not release"
        }
      }
    }
  }
}
