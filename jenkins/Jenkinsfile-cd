// Jenkins deployment pipeline
import groovy.json.JsonOutput

def blue= "#42b3f4"
def good="#3dd62f"
def danger="#f45641"
def warning="#ffd344"

jenkinsUtils = null

pipeline {
  agent none

  stages {
    stage('Prep variables.') {
      agent {
        docker {
            image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22'
        }
    }
      steps {
        script {
          jenkinsUtils = load "jenkins/JenkinsUtils.groovy"
        }
      }
    }
    stage('Deploying to Dev.') {
      agent {
        docker {
            image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22'
        }
    }
      steps {
        println "Deploying to Dev."
        script {
          jenkinsUtils = load "jenkins/JenkinsUtils.groovy"
        }
        withCredentials([[$class: 'AmazonWebServicesCredentialsBinding', credentialsId: "dev-amrpwl-aws-deployer"]]) {
          script {          
            jenkinsUtils.BuildAndDeployEnv('dev')    
          }
        }
      }
      post {
        success {
          echo 'success! Deployed to dev.'
          //build job: 'offers-engine-ui-integration-test', wait: false
          //build job: 'postman-newman-bulk-upload-tests', wait: false
        }
        failure {
          echo "failed to deploy to dev."
        }
        aborted {
          echo "job aborted. Did not deploy to dev."
        }

      }
    }
    stage('Deploy to INT?') {
      agent none
      steps {
        input(message: "Do you want to deploy version ${params.BUILD_VERSION} to INT?")
      }
      post {
        success {
          echo 'Attempting to deploy to int'
        }
        aborted {
          echo "job aborted. Aborting attempt to deploy to int"
        }
      }
    }
    stage('Deploying to INT') {
      agent {
        docker {
            image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22'
        }
    }
      steps {
        println "Deploying to INT"
        withCredentials([[$class: 'AmazonWebServicesCredentialsBinding', credentialsId: "nonprod-amrpwl-aws-deployer"]]) {
          script { 
            jenkinsUtils.BuildAndDeployEnv('int')    
          }
        }
      }
      post {
        success {
          echo 'success! Deployed to INT.'
        }
        failure {
          echo "failed to deploy to INT."
        }
        aborted {
          echo "job aborted. Did not deploy to INT."
        }

      }
    }
    stage('Deploy to UAT?') {
      agent none
      steps {
        input(message: "Do you want to deploy version ${params.BUILD_VERSION} to UAT?")
      }
      post {
        success {
          echo 'Attempting to deploy to uat'
        }
        aborted {
          echo "job aborted. Aborting attempt to deploy to uat"
        }
      }
    }
    stage('Deploying to UAT') {
      agent {
        docker {
            image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22'
        }
    }
      steps {
        println "Deploying to UAT"
        withCredentials([[$class: 'AmazonWebServicesCredentialsBinding', credentialsId: "uat-amrpwl-aws-deployer"]]) {
          script { 
            jenkinsUtils.BuildAndDeployEnv('uat')    
          }
        }
      }
      post {
        success {
          echo 'success! Deployed to UAT.'
        }
        failure {
          echo "failed to deploy to UAT."
        }
        aborted {
          echo "job aborted. Did not deploy to UAT."
        }

      }
    }
  }
}
