// Jenkins prod deployment pipeline
import groovy.json.JsonOutput

def blue= "#42b3f4"
def good="#3dd62f"
def danger="#f45641"
def warning="#ffd344"

jenkinsUtils = null

pipeline {
  agent none

  stages {
    stage('Prep variables.') {
      agent {
        docker {
            image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22'
        }
    }
      steps {
        script {
          jenkinsUtils = load "jenkins/JenkinsUtils.groovy"
        }
      }
    }

    stage('Deploying to Production') {
      agent {
        docker {
            image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22'
        }
    }
      steps {
        println "Deploying to Production"
        withCredentials([[$class: 'AmazonWebServicesCredentialsBinding', credentialsId: "prod-amrpwl-aws-deployer"]]) {
          script { 
            jenkinsUtils.BuildAndDeployEnv('prod')
          }
        }
      }
      post {
        success {
          echo 'success! Deployed to Production.'
        }
        failure {
          echo "failed to deploy to Production."
        }
        aborted {
          echo "job aborted. Did not deploy to Production."
        }
      }
    }
  }
}
